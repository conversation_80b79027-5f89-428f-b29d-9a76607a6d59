### High-Level Idea for Extending VMS with Account, Contact, Opportunity, RFQ, and Quote Features

To extend your Vendor Management System (VMS) into a more CRM-like platform (inspired by Salesforce), we'll introduce **Account** (representing client companies or organizations), **Contact** (individuals associated with accounts, e.g., decision-makers), and **Opportunity** (potential deals or sales pipelines) objects. This will allow users to manage client-side relationships alongside vendor management, creating a bidirectional procurement ecosystem.

The core new workflow:
- **Opportunities as Entry Point**: Users create Opportunities linked to an Account and one or more Contacts (e.g., a sales deal for procuring services from vendors).
- **RFQ on Opportunity**: From an Opportunity, users generate an RFQ (Request for Quote) specifying items/services needed. This RFQ is sent to selected vendors (leveraging the existing Vendor Management and RFQ modules).
- **Vendor Submissions (Bids)**: Vendors submit bids via public forms (as in existing RFQ module), including per-item pricing, terms, etc.
- **Quote Generation from Submissions**: Users review submissions and create a client-facing **Quote** linked to the Account/Contact. The Quote can be composed by:
  - Selecting all items from a single submission (e.g., best overall vendor).
  - Mixing items from multiple submissions (e.g., lowest price per item across vendors).
  - Adding margins, taxes, or custom adjustments.
- **Integration with Existing Modules**: 
  - Link Quotes to Invoicing & Payments for seamless billing upon approval.
  - Use AI suggestions (from existing RFQ module) for optimal item selections.
  - Track everything via audits, workflows (e.g., approval chains for Quotes), and performance metrics (e.g., update Opportunity status based on Quote acceptance).
  - Maintain multi-tenant isolation: All new objects scoped to the tenant's dedicated DB.

**Benefits**:
- Transforms VMS into a full procurement CRM: Manage vendors (supply-side) and clients (demand-side) in one app.
- Enables end-to-end deal flow: Opportunity → RFQ → Submissions → Quote → Invoice.
- Market-leading: AI-optimized quotes, multi-bid aggregation, and integrations (e.g., Salesforce API sync if needed).
- Business Impact: Reduces quote creation time by 40-50%, improves margins via competitive selections, and tracks win rates for Opportunities.

**High-Level Tech Changes**:
- **Database**: Add new tables for Account, Contact, Opportunity, Quote; extend RFQ and RFQSubmission with links to Opportunity.
- **Backend**: New APIs for CRUD on new objects; enhance RFQ endpoints for Opportunity linking and Quote generation logic (e.g., aggregation algorithms).
- **Frontend**: New pages/routes (e.g., `/opportunities/:id/rfq`, `/opportunities/:id/create-quote`); components for bid comparison and item selection.
- **Integrations**: Reuse existing (e.g., email for RFQ invites, AI for suggestions); add optional Salesforce sync via webhooks.
- **Timeline**: 6-8 weeks, building on existing RFQ (3.10) and Invoicing (3.5) modules.

This extension keeps the app lightweight while adding CRM value. Now, let's deep dive into the detailed plan.

### Detailed Implementation Plan for Account, Contact, Opportunity, RFQ, and Quote Extension

#### Introduction to the Extension Module

This module extends the VMS to incorporate CRM elements (Account, Contact, Opportunity) and enhances the RFQ process to support client quoting. It integrates with existing modules:
- **Vendor Management**: Select vendors for RFQ invites based on Opportunity requirements.
- **RFQ Management** (from 3.10): RFQ creation now tied to Opportunities; submissions used for Quote generation.
- **Invoicing & Payments**: Auto-generate invoices from approved Quotes.
- **Performance & Compliance**: Update Opportunity win/loss based on Quote outcomes; apply vendor scores in selections.
- **Workflows & Automation**: Approval flows for Quotes; notifications for Contacts.
- **Reporting & Analytics**: Dashboards for Opportunity pipelines, quote win rates.
- **Authentication & RBAC**: Managers create Opportunities/RFQs/Quotes; Viewers read-only.

Key enhancements:
- **Public/Private Access**: Contacts can approve Quotes via shareable links (similar to RFQ submissions).
- **AI Optimization**: Extend RFQ AI to suggest Quote compositions (e.g., "Mix Vendor A for Item 1 (low cost) and Vendor B for Item 2 (high quality)").
- **Multi-Currency/Language**: Inherit from user preferences or Account settings.
- **Scenario Handling**: Edge cases like partial selections, quote revisions, opportunity closures.

Implementation stack remains consistent:
- **Frontend**: React.js with new components (e.g., OpportunityPipeline, QuoteBuilder with drag-select for items).
- **Backend**: Node.js/Express with Prisma extensions; algorithms for quote aggregation.
- **Database**: PostgreSQL extensions (new tables, FKs); no breaking changes.
- **Tools**: Reuse Stripe for quote payments; add Chart.js for pipeline visuals.

#### Business Use Cases

Detailed use cases, including actors, preconditions, steps, postconditions, benefits, and risks mitigated.

1. **Creating and Managing Accounts and Contacts**
   - **Actors**: Managers (create/edit), Viewers (read).
   - **Preconditions**: Authenticated user; optional link to existing Opportunity.
   - **Steps**:
     1. Navigate to `/accounts/create`.
     2. Enter Account details (name, industry, address); add Contacts (name, email, role).
     3. System validates and creates records, linking Contacts to Account.
     4. For edits: `/accounts/:id/edit` or `/contacts/:id/edit`.
   - **Postconditions**: Account/Contact records in DB; audit logged.
   - **Business Benefits**: Centralizes client data, enabling targeted RFQs (e.g., filter Opportunities by Account industry for vendor matching).
   - **Risks Mitigated**: Data duplicates via unique emails; incomplete profiles via validation.
   - **Metrics**: Account creation rate; average Contacts per Account (>2 for engagement).

2. **Creating an Opportunity Linked to Account/Contact**
   - **Actors**: Managers.
   - **Preconditions**: Account exists.
   - **Steps**:
     1. From `/opportunities/create?accountId=:id`.
     2. Define Opportunity: Name, stage (e.g., Prospecting), expected value, items needed (JSON array for RFQ prep).
     3. Assign Contacts (e.g., primary decision-maker).
     4. Save; system sets status to 'Open'.
   - **Postconditions**: Opportunity linked to Account/Contacts; pipeline updated.
   - **Business Benefits**: Tracks sales pipelines, integrating procurement (e.g., Opportunity for "IT Upgrade" leads to RFQ for hardware vendors).
   - **Risks Mitigated**: Lost deals via stage tracking; misassignment via RBAC.
   - **Metrics**: Opportunity win rate >30%; average deal value.

3. **Generating RFQ from Opportunity**
   - **Actors**: Managers.
   - **Preconditions**: Opportunity with items defined.
   - **Steps**:
     1. From `/opportunities/:id/create-rfq`.
     2. Auto-populate RFQ with Opportunity items; customize form/recipients (vendors filtered by category/score).
     3. Send invitations; link RFQ back to Opportunity.
   - **Postconditions**: RFQ created; vendors notified; Opportunity status to 'Sourcing'.
   - **Business Benefits**: Ties client needs (Opportunity) to vendor sourcing, automating procurement for deals.
   - **Risks Mitigated**: Mismatched items via auto-populate; low responses via vendor filtering.
   - **Metrics**: RFQ-to-Opportunity ratio; response rate >60%.

4. **Reviewing Submissions and Creating Quote with Item Selections**
   - **Actors**: Managers.
   - **Preconditions**: RFQ submissions received.
   - **Steps**:
     1. View `/rfqs/:id/submissions` (linked from Opportunity).
     2. Compare bids (table/charts); use AI for suggestions.
     3. Select "Create Quote": Choose items (all from one submission or mix, e.g., checkbox per item/vendor).
     4. System aggregates: Calculate totals, add margins/taxes; link to Account/Contact.
     5. Generate PDF; send to Contact for approval.
   - **Postconditions**: Quote created; linked to Opportunity/Submissions; notifications sent.
   - **Business Benefits**: Flexible quoting maximizes value (e.g., hybrid selections save 15-20% costs); bridges sourcing to sales.
   - **Risks Mitigated**: Suboptimal choices via AI; errors in aggregation via validation.
   - **Metrics**: Quote acceptance rate >70%; time from submission to quote <1 day.

5. **Quote Approval and Transition to Invoice**
   - **Actors**: Contacts (approve via link), Managers (monitor).
   - **Preconditions**: Quote generated.
   - **Steps**:
     1. Contact receives link (`/public/quote/:token/approve`); reviews and approves (e-signature optional).
     2. System updates Opportunity to 'Won'; auto-creates Invoice from Quote data.
   - **Postconditions**: Invoice in Invoicing module; Opportunity closed.
   - **Business Benefits**: Closes the loop from deal to revenue.

#### Detailed Implementation Plan

Phased plan, 6-8 weeks, 2-3 devs, Agile sprints. Builds on RFQ (3.10) and Invoicing (3.5).

##### Phase 1: Requirements Gathering and Design (Weeks 1-2)
- **Activities**:
  - Refine use cases: E.g., define item selection logic (algorithm for mixing: minimize cost while >threshold score).
  - Database Schema Extensions (Prisma Notation; minor to existing):
    ```
    model Account {
      id           Int       @id @default(autoincrement())
      name         String
      industry     String?
      address      Json?
      custom_fields Json?
      created_at   DateTime  @default(now())
      updated_at   DateTime  @updatedAt

      contacts     Contact[]
      opportunities Opportunity[]
    }

    model Contact {
      id          Int       @id @default(autoincrement())
      account_id  Int
      name        String
      email       String
      role        String?   // e.g., 'Decision Maker'
      phone       String?
      created_at  DateTime  @default(now())
      updated_at  DateTime  @updatedAt

      account     Account   @relation(fields: [account_id], references: [id], onDelete: Cascade)
      opportunities Opportunity[] // Many-to-Many if needed
    }

    model Opportunity {
      id          Int       @id @default(autoincrement())
      account_id  Int
      name        String
      stage       String    // e.g., 'Prospecting', 'Sourcing', 'Quoted', 'Won'
      expected_value Decimal?
      items       Json?     // [{"name": "Laptop", "qty": 10, "specs": "..."}]
      status      StatusEnum @default(OPEN)
      created_at  DateTime  @default(now())
      updated_at  DateTime  @updatedAt

      account     Account   @relation(fields: [account_id], references: [id], onDelete: Cascade)
      rfqs        RFQ[]     // One-to-Many
      quotes      Quote[]
    }

    model Quote {
      id            Int       @id @default(autoincrement())
      opportunity_id Int
      account_id    Int
      contact_id    Int?
      selections    Json      // [{"item": "Laptop", "from_submission_id": 123, "price": 1000, "vendor_id": 456}]
      total_amount  Decimal
      currency      String    @default("USD")
      status        StatusEnum @default(DRAFT)
      pdf_url       String?   // S3 link
      created_at    DateTime  @default(now())
      approved_at   DateTime?

      opportunity   Opportunity @relation(fields: [opportunity_id], references: [id], onDelete: Cascade)
      account       Account     @relation(fields: [account_id], references: [id])
      contact       Contact?    @relation(fields: [contact_id], references: [id])
      invoice       Invoice?    // Link post-approval
    }

    // Extend RFQ (from 3.10)
    model RFQ {
      // ... existing
      opportunity_id Int?
      opportunity    Opportunity? @relation(fields: [opportunity_id], references: [id])
    }
    ```
    - Indexing: On opportunity_id, stage for pipelines.
    - Migration: `prisma migrate dev --name crm_extension`; no downtime.
  - API Design:
    - POST `/api/accounts`: Create with contacts.
    - POST `/api/opportunities`: Link to account.
    - POST `/api/opportunities/:id/rfq`: Generate RFQ.
    - POST `/api/rfqs/:id/create-quote`: Body {selections: [...] }; aggregate logic.
    - PUT `/public/quote/:token/approve`: Public endpoint.
  - UI Wireframes: Pipeline kanban for Opportunities; quote builder with multi-select grid.
- **Deliverables**: Updated schema, API specs (Swagger), wireframes.

##### Phase 2: Backend Development (Weeks 2-4)
- **Activities**:
  - Models/Controllers: CRUD for new objects.
  - RFQ Extension: Add opportunity_id to creation; validate items match.
  - Quote Logic: Algorithm to aggregate (e.g., loop selections, sum prices, apply margins).
    ```ts
    // controllers/quoteController.ts
    const createQuote = async (req) => {
      const { rfqId, selections } = req.body; // selections: [{itemId, submissionId, quantity}]
      let total = 0;
      const aggregatedItems = selections.map(sel => {
        const sub = await prisma.rFQSubmission.findUnique({ where: { id: sel.submissionId } });
        const item = sub.bids.find(b => b.itemId === sel.itemId);
        total += item.price * sel.quantity;
        return { ...item, fromVendor: sub.vendorId };
      });
      // Create quote with aggregatedItems JSON
    };
    ```
  - AI Extension: Enhance suggestions for multi-submission mixes.
  - Integrations: Webhooks for quote approvals → invoice creation.
- **Deliverables**: APIs, tests (85% coverage).

##### Phase 3: Frontend Development (Weeks 4-6)
- **Activities**:
  - Components: AccountForm, OpportunityPipeline (kanban with Recharts), QuoteSelector (grid with checkboxes for items/vendors).
  - Routing: `/accounts/*`, `/opportunities/*`, `/opportunities/:id/rfq`, `/opportunities/:id/quote`.
  - Features: Drag-select for items; real-time totals calculation.
- **Deliverables**: UI pages, API integrations.

##### Phase 4: Integration, Testing, and Security Audit (Weeks 6-7)
- **Activities**:
  - Integrate: Link to existing RFQ/Invoicing; test full flow.
  - Testing: E2E (Cypress: Opportunity → RFQ → Quote → Approve → Invoice).
  - Security: Token-based public quote approval; encrypt contact emails.
- **Deliverables**: Test reports.

##### Phase 5: Deployment and Monitoring (Week 8)
- **Activities**:
  - Deploy: Update EC2 scripts; monitor Opportunity metrics.
  - Docs: User guide for CRM features.
- **Deliverables**: Live extension.

#### Risks and Mitigations
- **Risk**: Complex selection logic errors. **Mitigation**: Unit tests for aggregation; fallback to manual entry.
- **Risk**: Data overload in quotes. **Mitigation**: Pagination in submission views; limits (e.g., max 50 items).
- **Risk**: Integration with Salesforce (if added). **Mitigation**: Optional webhook; test in sandbox.
- **Budget**: ~$8K (dev time); **Success Criteria**: Full flow coverage; user feedback NPS >8.