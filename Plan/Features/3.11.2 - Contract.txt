### Introduction to Contact Implementation Module

The Contact implementation module builds on the Account module, adding individual-level client management to the Vendor Management System (VMS), closely mirroring Salesforce's Contact object for seamless integration. In Salesforce (as of July 29, 2025), the Contact object represents people associated with Accounts (e.g., employees or decision-makers) and includes standard fields like FirstName, LastName (Name is computed as "FirstName LastName"), Salutation (picklist: Mr., Ms., Dr., etc.), Title (job title), Department, Phone, MobilePhone, HomePhone, OtherPhone, Fax, Email (unique per org), MailingAddress and OtherAddress (composite with Street, City, State, PostalCode, Country, Latitude, Longitude), AccountId (required lookup to Account), OwnerId, ReportsToId (self-lookup for reporting hierarchies), AssistantName, AssistantPhone, Birthdate (date), LeadSource (picklist: Web, Phone Inquiry, etc.), Description, Languages__c (multi-picklist), Level__c (picklist: Primary, Secondary), DoNotCall/HasOptedOutOfEmail (boolean for compliance), and system fields like CreatedById, LastModifiedById, IsDeleted. Person Contacts (B2C) add personal details, but we'll focus on business Contacts to align with VMS's B2B procurement context.

In VMS, Contacts will link to Accounts (one-to-many), serving as points of contact for Opportunities, RFQs, Quotes (e.g., approvers/notifiees), and Invoices. The schema will align with Salesforce: Similar field names/types (e.g., first_name/last_name as VARCHAR, salutation as ENUM, addresses as JSONB), computed name via view or getter, hierarchical support via reports_to_id, and integration_id for Salesforce sync. This enables bi-directional integration (e.g., sync VMS Contacts to Salesforce for unified CRM, pull updates to reflect changes in VMS workflows).

Key principles:
- **Salesforce Alignment**: Fields like first_name, last_name, salutation, title, department, multiple phones/emails, composite addresses, account_id (required), lead_source (ENUM), etc., to ease mapping.
- **VMS-Specific Additions**: custom_fields (JSONB), status (ENUM for active/inactive), preferred_language (ENUM), integration_id.
- **Integration Focus**: Use Salesforce REST API (/sobjects/Contact) for sync; handle hierarchies and lookups (e.g., link to VMS Account's Salesforce ID).
- **Multi-Tenant Isolation**: Per-tenant DBs; RLS for access (e.g., only view Contacts linked to user's Accounts).
- **Features**: CRUD, hierarchy views (org charts), search/filter by role/email, auto-notify in workflows (e.g., Quote approvals).

Implementation leverages existing stack:
- **Frontend**: React.js with forms for multi-phone/address, org chart components (e.g., react-organizational-chart).
- **Backend**: Node.js/Express with Prisma; jsforce for Salesforce.
- **Database**: PostgreSQL extensions; JSONB for addresses.
- **Tools**: Reuse Nodemailer/Twilio for Contact notifications; AWS S3 for profile attachments.

This module enhances VMS as a Salesforce-integrated platform, enabling personalized client interactions (e.g., email Quotes to specific Contacts).

### Business Use Cases

This module manages individual client contacts, integrating with Accounts/Opportunities. Use cases include actors, preconditions, steps, postconditions, benefits, and risks mitigated.

1. **Creating a New Contact Linked to Account (Matching Salesforce Structure)**
   - **Actors**: Managers (create), Admins (configure).
   - **Preconditions**: Account exists; authenticated user.
   - **Steps**:
     1. Navigate to `/contacts/create?accountId=:id` (pre-fills Account).
     2. Fill form: First Name, Last Name (required; system computes full name), Salutation (picklist: Mr., Ms., Dr.), Title (e.g., CEO), Department, Phone, Mobile Phone, Home Phone, Other Phone, Fax, Email (required, unique), Mailing/Other Address (structured: street, city, state, postalCode, country, lat/lng), Reports To (lookup to another Contact), Assistant Name/Phone, Birthdate, Lead Source (picklist: Web, Partner), Description, Languages (multi-select), Level (picklist: Primary, Secondary), Do Not Call/Opted Out of Email (booleans for compliance).
     3. Add custom fields (e.g., preferred_contact_method).
     4. Submit; validate (e.g., email format, account_id required), create record, sync to Salesforce (POST /sobjects/Contact with AccountId mapping).
   - **Postconditions**: Contact in DB with status 'Active'; linked to Account/Reports To; audit logged; Salesforce ID in integration_id.
   - **Business Benefits**: Standardizes contact data like Salesforce, enabling targeted communications (e.g., send RFQ updates to department heads). Supports compliance with opt-outs.
   - **Risks Mitigated**: Invalid emails via regex; missing links via required FKs.
   - **Metrics for Success**: Creation time <3 min; sync rate >95%.

2. **Editing a Contact and Managing Hierarchies**
   - **Actors**: Managers (edit), Viewers (read).
   - **Preconditions**: Contact exists; RBAC access.
   - **Steps**:
     1. Access `/contacts/:id/edit`.
     2. Update fields (e.g., change Title, add Reports To for org structure).
     3. System updates hierarchies (e.g., validate no cycles); sync to Salesforce (PATCH /sobjects/Contact/:id).
     4. Log diffs (old/new values).
   - **Postconditions**: Contact updated; hierarchy refreshed; notifications if affects Quotes (e.g., reassign approver).
   - **Business Benefits**: Mirrors Salesforce reporting lines for enterprises (e.g., escalate Quotes to manager if primary Contact unavailable).
   - **Risks Mitigated**: Hierarchy cycles via DB checks; data loss via versioning.
   - **Metrics**: Edit frequency; average hierarchy depth.

3. **Searching and Filtering Contacts**
   - **Actors**: All authenticated users.
   - **Preconditions**: Contacts exist.
   - **Steps**:
     1. Go to `/contacts/list?accountId=:id` (scope to Account).
     2. Filter: Department (dropdown), Level (slider), fuzzy search on Name/Email, Lead Source.
     3. Query DB (e.g., ILIKE for last_name, JSONB for address.country).
     4. Export CSV.
   - **Postconditions**: Paginated results; shareable URL (e.g., `/contacts/list?department=Sales&level=Primary`).
   - **Business Benefits**: Quick selection for Opportunity assignments, aligning with Salesforce's related lists.
   - **Risks Mitigated**: Slow searches via indexes; privacy via scoped queries.
   - **Metrics**: Response time <1s; usage analytics.

4. **Integrating and Syncing with Salesforce**
   - **Actors**: Admins (setup), System (sync).
   - **Preconditions**: Salesforce creds in integrations.
   - **Steps**:
     1. On create/edit, map fields (e.g., VMS first_name to FirstName, mailing_address JSONB to MailingStreet/City/etc.) and sync.
     2. Pull updates: Cron/SOQL query (SELECT Id, FirstName, ... FROM Contact WHERE LastModifiedDate > :lastSync).
     3. Resolve conflicts (e.g., merge descriptions).
   - **Postconditions**: Synced data; logs in audits.
   - **Business Benefits**: Unified contacts across systems; auto-update VMS with Salesforce changes (e.g., new email).

5. **Using Contact in Opportunities/RFQs/Quotes**
   - **Actors**: Managers.
   - **Preconditions**: Contact linked to Account.
   - **Steps**:
     1. In Opportunity creation, select Contacts (e.g., primary approver).
     2. Quote generation: Auto-send to Contact email; approval link personalized.
   - **Postconditions**: Notifications sent; workflows trigger (e.g., escalate if no response).
   - **Business Benefits**: Personalized procurement (e.g., address Quotes to "Dr. Jane Doe, CEO").

### Detailed Implementation Plan

Phased plan, 4-6 weeks, 2-3 devs, Agile. Builds on Account module.

#### Phase 1: Requirements Gathering and Design (Weeks 1-2)
- **Activities**:
  - Refine: Picklists for salutation (Mr., Ms., etc.), lead_source (Web, etc.), level (Primary, etc.) matching Salesforce.
  - Database Schema (Prisma; extends Account/CRM):
    ```
    model Contact {
      id                     Int       @id @default(autoincrement())
      account_id             Int       // Required, matches AccountId
      first_name             String?
      last_name              String?   // Required for business contacts
      salutation             String?   // Picklist: Mr., Ms., Dr., Prof., etc.
      title                  String?   // e.g., 'CEO'
      department             String?
      phone                  String?   // Phone
      mobile_phone           String?   // MobilePhone
      home_phone             String?   // HomePhone
      other_phone            String?   // OtherPhone
      fax                    String?   // Fax
      email                  String?   // Email (unique)
      mailing_address        Json?     // {"street": "", "city": "", "state": "", "postalCode": "", "country": "", "latitude": Float?, "longitude": Float?}
      other_address          Json?     // Similar
      reports_to_id          Int?      // Self-FK for hierarchy
      assistant_name         String?   // AssistantName
      assistant_phone        String?   // AssistantPhone
      birthdate              DateTime? @db.Date  // Birthdate
      lead_source            String?   // Picklist: Web, Phone Inquiry, Partner
      description            Text?     // Description
      languages              String?   // Multi-picklist (comma-separated or JSON array)
      level                  String?   // Picklist: Primary, Secondary, Tertiary
      do_not_call            Boolean   @default(false)
      has_opted_out_of_email Boolean   @default(false)
      owner_id               Int?      // FK User
      integration_id         String?   // Salesforce ID
      custom_fields          Json?     // VMS extension
      status                 StatusEnum @default(ACTIVE)
      created_at             DateTime  @default(now())
      updated_at             DateTime  @updatedAt
      created_by_id          Int?
      last_modified_by_id    Int?
      is_deleted             Boolean   @default(false)

      account                Account   @relation(fields: [account_id], references: [id], onDelete: Cascade)
      reports_to             Contact?  @relation("ContactHierarchy", fields: [reports_to_id], references: [id])
      reports                Contact[] @relation("ContactHierarchy")
      owner                  User?     @relation("ContactOwner", fields: [owner_id], references: [id])
      created_by             User?     @relation("CreatedBy", fields: [created_by_id], references: [id])
      last_modified_by       User?     @relation("LastModifiedBy", fields: [last_modified_by_id], references: [id])
      opportunities          Opportunity[] // Links
    }
    ```
    - Computed Name: View or getter (CONCAT(first_name, ' ', last_name)).
    - Indexing: GIN on mailing_address, BTREE on last_name, email, department.
    - Triggers: Log changes; enforce account_id not null.
  - API Design:
    - POST `/api/contacts`: Body matches schema; sync to Salesforce.
    - GET `/api/contacts/:id`: Include hierarchy.
    - PUT `/api/contacts/:id`: Update/sync.
    - GET `/api/contacts/search`: Params {department, level, email}.
    - POST `/api/integrations/salesforce/sync-contact`.
  - UI Wireframes: Contact form with multi-phone tabs; org chart for reports_to.
  - Integration: Map VMS fields to Salesforce (e.g., mailing_address.street to MailingStreet); handle lookups (AccountId = Account.integration_id).
- **Deliverables**: Schema, API docs, wireframes, mapping doc.

#### Phase 2: Backend Development (Weeks 2-3)
- **Activities**:
  - Controllers: CRUD with validation (e.g., email unique, birthdate < current).
  - Hierarchy: Recursive queries for org structures.
  - Salesforce Sync:
    ```ts
    // services/salesforceService.ts (extend from Account)
    export const syncContactToSF = async (contact) => {
      const sfContact = {
        FirstName: contact.first_name,
        LastName: contact.last_name,
        AccountId: (await prisma.account.findUnique({ where: { id: contact.account_id } })).integration_id,
        MailingStreet: contact.mailing_address?.street,
        // Map others
      };
      const result = await conn.sobject('Contact').create(sfContact);
      // Update integration_id
    };
    ```
    - Pull: SOQL for updates; handle deletions (set is_deleted).
  - Scenarios: Opt-out checks in notifications; language-based emails.
- **Deliverables**: APIs, tests (85%).

#### Phase 3: Frontend Development (Weeks 3-4)
- **Activities**:
  - Components: ContactForm (picklists, address fields); OrgChart (for hierarchies).
  - Routing: `/contacts/create`, `/contacts/:id/*` (edit, view).
  - Features: Email validation; auto-compute name.
  - Integration: Sync button.
- **Deliverables**: UI, integrations.

#### Phase 4: Integration, Testing, and Security Audit (Weeks 4-5)
- **Activities**:
  - Integrate: Link to Opportunities (select Contacts as approvers).
  - Testing: E2E (create Contact → sync → update in Salesforce → pull).
  - Security: Encrypt emails if sensitive; RBAC for views.
- **Deliverables**: Reports.

#### Phase 5: Deployment and Monitoring (Weeks 5-6)
- **Activities**:
  - Deploy: Update scripts; cron for syncs.
  - Monitoring: Alerts on sync fails.
  - Docs: Salesforce mapping guide.
- **Deliverables**: Live, training.

#### Risks and Mitigations
- **Risk**: Field mismatches in sync. **Mitigation**: Mapping tests; handle optional fields.
- **Risk**: Email uniqueness conflicts. **Mitigation**: Tenant-scoped uniques.
- **Risk**: Hierarchy complexity. **Mitigation**: Depth limits; visualization tools.
- **Risk**: Compliance (opt-outs). **Mitigation**: Enforce in workflows.
- **Budget**: ~$6K; **Success Criteria**: Full match; sync <5s; zero leaks.