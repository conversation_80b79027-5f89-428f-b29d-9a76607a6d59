### Introduction to Account Implementation Module

The Account implementation module extends the Vendor Management System (VMS) to include client-side account management, mirroring Salesforce's Account object where feasible to facilitate seamless integration. In Salesforce (as of July 29, 2025), the Account object represents organizations or businesses (for business accounts) and includes standard fields like Name (required), Type (picklist for customer/prospect), Industry (picklist), AnnualRevenue (currency), NumberOfEmployees (number), Ownership (picklist), Phone, Website, Description, composite addresses (BillingAddress and ShippingAddress with subfields like Street, City, State, PostalCode, Country, Latitude, Longitude), ParentId (for hierarchy), AccountNumber, Site, TickerSymbol, Rating (picklist: Hot/Warm/Cold), and system fields like OwnerId, CreatedById, LastModifiedById, IsDeleted. Person Account fields (e.g., FirstName, LastName) are excluded here, focusing on business accounts to align with VMS's procurement focus.

In VMS, Accounts will represent client organizations (demand-side entities), linking to Contacts, Opportunities, RFQs, Quotes, and Invoices. The schema will closely match Salesforce for integration ease: Use similar field names/types (e.g., name as VARCHAR, annual_revenue as DECIMAL, industry as ENUM or VARCHAR with picklist values), composite addresses as JSONB (to mimic Salesforce's Address type without custom types), and hierarchical support via parent_account_id. This enables bidirectional sync with Salesforce (e.g., push VMS Accounts to Salesforce as leads/customers, pull updates via API).

Key principles:
- **Salesforce Alignment**: Fields like name (required), type, industry, annual_revenue, billing_address (JSONB mirroring composite), etc., to simplify mapping in integrations.
- **VMS-Specific Additions**: custom_fields (JSONB for extensibility), status (ENUM for active/inactive), integration_id (for Salesforce record ID).
- **Integration Focus**: Use Salesforce REST API (e.g., /sobjects/Account endpoint) for CRUD sync; webhooks for real-time updates; OAuth for auth.
- **Multi-Tenant Isolation**: Per-tenant DBs; RLS for shared access.
- **Features**: CRUD, hierarchy views, search/filter, links to Opportunities/Quotes.

Implementation leverages existing stack:
- **Frontend**: React.js with forms for address composites, tree views for hierarchies.
- **Backend**: Node.js/Express with Prisma; Salesforce SDK (jsforce or nforce) for integration.
- **Database**: PostgreSQL extensions; JSONB for addresses/certifications.
- **Tools**: AWS S3 for attachments; Nodemailer for notifications.

This module positions VMS as a Salesforce-integrated CRM extension, reducing data silos and enabling workflows like auto-syncing Accounts to Salesforce upon creation.

### Business Use Cases

This module supports client relationship management, integrating with Opportunities/RFQs/Quotes. Use cases include actors, preconditions, steps, postconditions, benefits, and risks mitigated.

1. **Creating a New Account (Matching Salesforce Structure)**
   - **Actors**: Managers (create), Admins (configure defaults).
   - **Preconditions**: Authenticated user; optional parent Account for hierarchy.
   - **Steps**:
     1. Navigate to `/accounts/create?parentId=:id` (pre-fills hierarchy).
     2. Fill form: Name (required), Type (picklist: Prospect, Customer), Industry (picklist: Banking, IT), Annual Revenue (currency), Number of Employees (number), Ownership (picklist: Public/Private), Phone, Website, Description, Billing/Shipping Address (structured inputs mapping to JSONB: street, city, state, postalCode, country, lat/lng), Account Number, Site, Ticker Symbol, Rating.
     3. Add custom fields if needed (e.g., preferred_currency).
     4. Submit; system validates (e.g., name unique per tenant), creates record, and syncs to Salesforce if integrated (POST to /sobjects/Account).
   - **Postconditions**: Account in DB with status 'Active'; linked to parent if specified; audit logged; Salesforce ID stored in integration_id.
   - **Business Benefits**: Standardizes client data like Salesforce, enabling quick onboarding for Opportunities (e.g., auto-populate addresses in Quotes). Supports global ops with multi-currency/address formats.
   - **Risks Mitigated**: Invalid data via picklist/validation; duplicates via unique constraints.
   - **Metrics for Success**: Creation time <5 min; sync success rate >95%.

2. **Editing an Account and Handling Hierarchy**
   - **Actors**: Managers (edit), Viewers (read).
   - **Preconditions**: Account exists; user has RBAC access.
   - **Steps**:
     1. Access `/accounts/:id/edit`.
     2. Update fields (e.g., change Rating from 'Warm' to 'Hot', add parent_account_id for subsidiaries).
     3. System recalculates hierarchies (e.g., update child counts); syncs changes to Salesforce (PATCH /sobjects/Account/:id).
     4. Log changes with old/new values.
   - **Postconditions**: Account updated; child/parent links refreshed; notifications if hierarchy affects Opportunities.
   - **Business Benefits**: Mirrors Salesforce hierarchies for enterprise clients (e.g., parent company with subsidiaries), aiding segmented RFQs.
   - **Risks Mitigated**: Data inconsistency via transactions; unauthorized edits via RBAC.
   - **Metrics**: Edit frequency; hierarchy depth average.

3. **Searching and Filtering Accounts**
   - **Actors**: All authenticated users.
   - **Preconditions**: Accounts exist.
   - **Steps**:
     1. Go to `/accounts/list`.
     2. Apply filters: Industry (dropdown), Rating (slider), Annual Revenue (range), fuzzy search on Name/Address.
     3. System queries DB (e.g., ILIKE for name, JSONB ops for address.country).
     4. Export to CSV/PDF.
   - **Postconditions**: Paginated results; URL params for sharing (e.g., `/accounts/list?industry=IT&revenueMin=1000000`).
   - **Business Benefits**: Quick client selection for Opportunities, aligning with Salesforce's list views for efficiency.
   - **Risks Mitigated**: Slow queries via indexing; overwhelming results via pagination.
   - **Metrics**: Search response <1s; filter usage analytics.

4. **Integrating and Syncing with Salesforce**
   - **Actors**: Admins (configure), System (auto-sync).
   - **Preconditions**: Salesforce API credentials in integrations table.
   - **Steps**:
     1. On create/edit, trigger webhook/job to sync (e.g., map VMS name to Salesforce Name, address JSONB to BillingStreet/City/etc.).
     2. Pull from Salesforce: Scheduled cron or webhook listens for updates, mapping back to VMS.
     3. Handle conflicts (e.g., last-modified wins).
   - **Postconditions**: Bi-directional sync; integration logs in audits.
   - **Business Benefits**: Avoids dual entry; leverages Salesforce for advanced CRM while VMS handles procurement.
   - **Risks Mitigated**: Data drift via timestamps; auth failures via retries/OAuth refresh.

5. **Linking Account to Opportunities/RFQs/Quotes**
   - **Actors**: Managers.
   - **Preconditions**: Account exists.
   - **Steps**:
     1. When creating Opportunity, select Account (auto-fills address/contacts).
     2. RFQ/Quote inherits Account data (e.g., shipping_address for delivery).
   - **Postconditions**: Linked records; workflows trigger (e.g., notify Account owner on Quote approval).
   - **Business Benefits**: End-to-end traceability from client Account to vendor Quote.

### Detailed Implementation Plan

Phased plan, assuming 2-3 developers, Agile sprints. Total: 4-6 weeks, building on CRM extension outline.

#### Phase 1: Requirements Gathering and Design (Weeks 1-2)
- **Activities**:
  - Gather specifics: E.g., picklist values for type/industry (match Salesforce: Type - Prospect, Customer-Direct; Industry - Agriculture, Banking, etc.).
  - Database Schema (Prisma; extends CRM extension):
    ```
    model Account {
      id                  Int       @id @default(autoincrement())
      name                String    // Matches Salesforce Name (required)
      account_number      String?   // AccountNumber
      type                String?   // Picklist: Prospect, Customer - Direct, etc. (use ENUM or VARCHAR)
      industry            String?   // Picklist: Banking, IT, etc.
      annual_revenue      Decimal?  // Currency
      number_of_employees Int?      // Number
      ownership           String?   // Picklist: Public, Private
      phone               String?   // Phone
      fax                 String?   // Fax
      website             String?   // URL
      ticker_symbol       String?   // TickerSymbol
      site                String?   // Site
      rating              String?   // Picklist: Hot, Warm, Cold
      description         Text?     // Text Area
      billing_address     Json?     // {"street": "", "city": "", "state": "", "postalCode": "", "country": "", "latitude": Float?, "longitude": Float?}
      shipping_address    Json?     // Similar composite
      parent_account_id   Int?      // Self-referential FK for hierarchy
      owner_id            Int?      // FK to User (matches OwnerId)
      integration_id      String?   // Salesforce Record ID for sync
      custom_fields       Json?     // VMS extension
      status              StatusEnum @default(ACTIVE)
      created_at          DateTime  @default(now())
      updated_at          DateTime  @updatedAt
      created_by_id       Int?      // FK User
      last_modified_by_id Int?      // FK User
      is_deleted          Boolean   @default(false)  // Soft delete like IsDeleted

      parent              Account?  @relation("AccountHierarchy", fields: [parent_account_id], references: [id])
      children            Account[] @relation("AccountHierarchy")
      owner               User?     @relation("AccountOwner", fields: [owner_id], references: [id])
      created_by          User?     @relation("CreatedBy", fields: [created_by_id], references: [id])
      last_modified_by    User?     @relation("LastModifiedBy", fields: [last_modified_by_id], references: [id])
      contacts            Contact[]
      opportunities       Opportunity[]
    }
    ```
    - Enums for type/industry/rating to match Salesforce picklists.
    - Indexing: GIN on billing_address, BTREE on name, industry, annual_revenue.
    - Triggers: Auto-log hierarchy changes; soft delete instead of hard.
  - API Design:
    - POST `/api/accounts`: Body matches schema; returns ID; trigger Salesforce sync.
    - GET `/api/accounts/:id`: Include hierarchy (recursive query).
    - PUT `/api/accounts/:id`: Update with diff logging; sync to Salesforce.
    - GET `/api/accounts/search`: Query params {industry, ratingMin, revenueRange}.
    - POST `/api/integrations/salesforce/sync-account`: Manual sync endpoint.
  - UI Wireframes: Account form with address sub-forms; tree view for hierarchies; integration setup page.
  - Integration Design: Use jsforce library; OAuth setup in .env; cron for pulls (every 15min); webhooks for pushes.
- **Deliverables**: Schema diagrams, API docs, wireframes, integration flows.

#### Phase 2: Backend Development (Weeks 2-3)
- **Activities**:
  - Controllers: Account CRUD with validation (Zod/Joi matching Salesforce constraints, e.g., name required, revenue >=0).
  - Hierarchy Logic: Recursive CTE queries for tree views (e.g., get all children).
  - Salesforce Integration:
    ```ts
    // services/salesforceService.ts
    import jsforce from 'jsforce';
    const conn = new jsforce.Connection({ loginUrl: process.env.SF_LOGIN_URL });
    await conn.login(process.env.SF_USERNAME, process.env.SF_PASSWORD + process.env.SF_TOKEN);

    export const syncAccountToSF = async (account) => {
      const sfAccount = {
        Name: account.name,
        Type: account.type,
        Industry: account.industry,
        AnnualRevenue: account.annual_revenue,
        BillingStreet: account.billing_address?.street,
        // Map other fields
      };
      const result = await conn.sobject('Account').create(sfAccount);
      await prisma.account.update({ where: { id: account.id }, data: { integration_id: result.id } });
    };
    ```
    - Handle updates (PATCH), deletes (soft in both), and pulls (query SOQL: SELECT Id, Name, ... FROM Account WHERE LastModifiedDate > :lastSync).
    - Error Handling: Retry on API limits (Salesforce has 100K calls/day); log failures.
  - Scenarios: Address parsing (validate lat/lng if provided); picklist enforcement.
- **Deliverables**: APIs, unit tests (Jest, 85% coverage), integration sandbox tests.

#### Phase 3: Frontend Development (Weeks 3-4)
- **Activities**:
  - Components: AccountForm (with picklists, address inputs); HierarchyTree (recursive component); SearchFilter (dropdowns/sliders).
  - Routing: `/accounts/create`, `/accounts/:id/*` (view, edit, hierarchy).
  - State: Redux for account list; Context for form state.
  - Features: Address autocomplete (optional Google Maps API); real-time validation.
  - Integration UI: Button to "Sync with Salesforce" on account page.
- **Deliverables**: UI, API calls with Axios.

#### Phase 4: Integration, Testing, and Security Audit (Weeks 4-5)
- **Activities**:
  - Integrate: Link to Opportunities (e.g., select Account in Opportunity form).
  - Testing: Unit (form validation); E2E (Cypress: create Account → sync → edit in Salesforce → pull back).
  - Security: Encrypt credentials; RBAC for sync (Admins only); GDPR consent for address data.
  - Performance: Optimize hierarchy queries (limit depth to 5).
- **Deliverables**: Reports, fixes.

#### Phase 5: Deployment and Monitoring (Weeks 5-6)
- **Activities**:
  - Deploy: Update CI/CD; add cron jobs for sync.
  - Monitoring: CloudWatch for sync failures; alerts on API errors.
  - Docs: Guide for Salesforce setup/mapping.
- **Deliverables**: Live module, training.

#### Risks and Mitigations
- **Risk**: Schema mismatches during sync. **Mitigation**: Custom mapping layer; test with Salesforce sandbox.
- **Risk**: API rate limits. **Mitigation**: Batch syncs; exponential backoff.
- **Risk**: Hierarchy loops. **Mitigation**: DB constraints (no self-parent); validation.
- **Risk**: Data privacy (addresses). **Mitigation**: Encrypt JSONB fields; consent flags.
- **Budget**: ~$6K; **Success Criteria**: 100% field coverage match; sync latency <5s; zero failed integrations in tests.