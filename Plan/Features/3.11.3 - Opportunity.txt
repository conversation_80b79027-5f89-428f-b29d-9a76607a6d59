### Introduction to Opportunity Implementation Module

The Opportunity implementation module completes the CRM extension in the Vendor Management System (VMS) by adding deal pipeline management, closely mirroring Salesforce's Opportunity object for seamless integration. In Salesforce (as of July 29, 2025), the Opportunity object tracks potential sales or deals and includes standard fields like Name (required text), AccountId (lookup to Account), Amount (currency), CloseDate (required date), StageName (required picklist: Prospecting, Qualification, Needs Analysis, Value Proposition, Id. Decision Makers, Perception Analysis, Proposal/Price Quote, Negotiation/Review, Closed Won, Closed Lost), Probability (percent, auto-calculated from Stage), Type (picklist: Existing Customer - Upgrade, New Customer, etc.), LeadSource (picklist: Web, Phone Inquiry, Partner Referral, etc.), IsPrivate (boolean to restrict visibility), Description (text area), OwnerId (lookup to User), ForecastCategory (picklist: Omitted, Pipeline, Best Case, Commit, Closed), HasOpportunityLineItem (boolean indicating products), NextStep (text), IsClosed (boolean, system), IsWon (boolean, system), CampaignId (lookup), and system fields like CreatedById, LastModifiedById, IsDeleted. Recent updates (per 2025 docs) include enhanced AI-driven Probability adjustments in Einstein features and expanded picklist values for Type (e.g., adding "Renewal - Upsell").

In VMS, Opportunities will represent potential procurement deals linked to Accounts and Contacts, serving as the bridge to RFQs, Quotes, and Invoices (e.g., RFQ items derived from Opportunity needs). The schema will align with Salesforce: Similar field names/types (e.g., name as VARCHAR, amount as DECIMAL, stage_name as ENUM with matching values), opportunity_line_items as JSONB (to mimic products without separate table initially), and integration_id for Salesforce sync. This enables bi-directional integration (e.g., sync VMS Opportunities to Salesforce for sales tracking, pull updates to reflect stage changes in VMS workflows).

Key principles:
- **Salesforce Alignment**: Fields like name, account_id, amount, close_date, stage_name (ENUM matching picklist), probability, type, lead_source, is_private, description, owner_id, forecast_category, etc., to simplify mapping.
- **VMS-Specific Additions**: items (JSONB for RFQ prep), status (ENUM extending IsClosed/IsWon), custom_fields, integration_id.
- **Integration Focus**: Use Salesforce REST API (/sobjects/Opportunity) for sync; handle lookups (e.g., AccountId to Account's Salesforce ID) and line items.
- **Multi-Tenant Isolation**: Per-tenant DBs; RLS for access (e.g., view only owned Opportunities).
- **Features**: CRUD, pipeline views (kanban), stage-based workflows, auto-probability calc, links to RFQs/Quotes.

Implementation leverages existing stack:
- **Frontend**: React.js with kanban boards (react-beautiful-dnd), forms for line items.
- **Backend**: Node.js/Express with Prisma; jsforce for Salesforce.
- **Database**: PostgreSQL extensions; JSONB for items/addresses.
- **Tools**: Recharts for pipeline analytics; cron for syncs.

This module transforms VMS into a full procurement CRM, with Opportunities driving revenue from client deals.

### Business Use Cases

This module manages deal pipelines, integrating with Accounts/Contacts/RFQs. Use cases include actors, preconditions, steps, postconditions, benefits, and risks mitigated.

1. **Creating a New Opportunity Linked to Account/Contact (Matching Salesforce Structure)**
   - **Actors**: Managers (create), Admins (configure).
   - **Preconditions**: Account exists; optional Contacts.
   - **Steps**:
     1. Navigate to `/opportunities/create?accountId=:id`.
     2. Fill form: Name (required), Account (lookup, required), Amount (currency), Close Date (required), Stage (picklist: Prospecting, Closed Won, etc.), Probability (percent, auto from stage), Type (picklist: New Customer, etc.), Lead Source (picklist: Web, etc.), Is Private (boolean), Description, Forecast Category (picklist: Pipeline, etc.), Next Step, Items/Products (JSON array: [{"name": "Service", "quantity": 1, "price": 1000}] mimicking line items).
     3. Assign Owner (default current user), link Contacts (e.g., approvers).
     4. Submit; validate (e.g., close_date future), create record, sync to Salesforce (POST /sobjects/Opportunity with AccountId mapping).
   - **Postconditions**: Opportunity in DB with status 'Open'; linked to Account/Contacts; audit logged; Salesforce ID in integration_id.
   - **Business Benefits**: Standardizes deal tracking like Salesforce, enabling RFQ generation from Opportunity items for procurement deals.
   - **Risks Mitigated**: Invalid stages via picklist; missing links via required FKs.
   - **Metrics for Success**: Creation time <4 min; sync rate >95%.

2. **Editing an Opportunity and Advancing Stages**
   - **Actors**: Managers (edit), Viewers (read).
   - **Preconditions**: Opportunity exists; RBAC access.
   - **Steps**:
     1. Access `/opportunities/:id/edit`.
     2. Update fields (e.g., advance Stage from 'Proposal' to 'Closed Won', auto-update Probability).
     3. System triggers workflows (e.g., if 'Closed Won', generate Quote/Invoice); sync to Salesforce (PATCH /sobjects/Opportunity/:id).
     4. Log changes.
   - **Postconditions**: Opportunity updated; probability/forecast recalculated; notifications if stage affects RFQs (e.g., cancel if 'Closed Lost').
   - **Business Benefits**: Mirrors Salesforce stage progression for forecasting, integrating with VMS Quotes (e.g., auto-close Opportunity on Quote approval).
   - **Risks Mitigated**: Inaccurate forecasts via auto-calc; unauthorized advances via RBAC.
   - **Metrics**: Stage progression rate; win rate >30%.

3. **Searching and Filtering Opportunities (Pipeline View)**
   - **Actors**: All authenticated users.
   - **Preconditions**: Opportunities exist.
   - **Steps**:
     1. Go to `/opportunities/list` or `/opportunities/pipeline`.
     2. Filter: Stage (dropdown), Type (multi-select), Amount Range, Close Date (date picker), fuzzy search on Name/Description.
     3. View as kanban (grouped by Stage) or list; aggregate totals (e.g., pipeline value).
     4. Export reports.
   - **Postconditions**: Filtered results; shareable URL (e.g., `/opportunities/list?stage=Proposal&amountMin=10000`).
   - **Business Benefits**: Visual pipeline like Salesforce, aiding prioritization (e.g., focus on high-amount Opportunities for RFQs).
   - **Risks Mitigated**: Outdated views via real-time queries; overload via pagination/aggregates.
   - **Metrics**: Response <1s; pipeline value tracked.

4. **Integrating and Syncing with Salesforce**
   - **Actors**: Admins (setup), System (sync).
   - **Preconditions**: Salesforce creds.
   - **Steps**:
     1. On create/edit, map fields (e.g., VMS amount to Amount, items to OpportunityLineItems if expanded) and sync.
     2. Pull: Cron/SOQL (SELECT Id, Name, ... FROM Opportunity WHERE LastModifiedDate > :lastSync).
     3. Handle conflicts (e.g., stage priority from Salesforce).
   - **Postconditions**: Synced data; logs.
   - **Business Benefits**: Unified pipelines; leverage Salesforce forecasting in VMS.

5. **Generating RFQ/Quote from Opportunity**
   - **Actors**: Managers.
   - **Preconditions**: Opportunity with items.
   - **Steps**:
     1. From `/opportunities/:id/create-rfq`, auto-populate from items.
     2. Post-submissions, create Quote selecting items; update Opportunity stage.
   - **Postconditions**: RFQ/Quote linked; Opportunity progressed.
   - **Business Benefits**: Seamless from deal to procurement.

### Detailed Implementation Plan

Phased plan, 4-6 weeks, 2-3 devs, Agile. Builds on Account/Contact.

#### Phase 1: Requirements Gathering and Design (Weeks 1-2)
- **Activities**:
  - Refine: Picklists for stage_name (Prospecting, etc.), type, lead_source, forecast_category matching Salesforce.
  - Database Schema (Prisma; extends previous):
    ```
    model Opportunity {
      id                     Int       @id @default(autoincrement())
      name                   String    // Required
      account_id             Int       // Required, FK Account
      amount                 Decimal?  // Currency
      close_date             DateTime? @db.Date // Required
      stage_name             String?   // Picklist: Prospecting, Qualification, etc.
      probability            Float?    // Percent, 0-100
      type                   String?   // Picklist: New Customer, etc.
      lead_source            String?   // Picklist: Web, etc.
      is_private             Boolean   @default(false)
      description            Text?
      forecast_category      String?   // Picklist: Pipeline, Best Case, etc.
      next_step              String?
      is_closed              Boolean   @default(false) // System
      is_won                 Boolean   @default(false) // System
      owner_id               Int?      // FK User
      campaign_id            String?   // Optional lookup
      has_opportunity_line_item Boolean @default(false) // If items present
      items                  Json?     // [{"name": "", "quantity": Int, "price": Decimal, "description": ""}]
      custom_fields          Json?
      integration_id         String?   // Salesforce ID
      status                 StatusEnum @default(OPEN) // VMS extension
      created_at             DateTime  @default(now())
      updated_at             DateTime  @updatedAt
      created_by_id          Int?
      last_modified_by_id    Int?
      is_deleted             Boolean   @default(false)

      account                Account   @relation(fields: [account_id], references: [id], onDelete: Cascade)
      owner                  User?     @relation("OpportunityOwner", fields: [owner_id], references: [id])
      created_by             User?     @relation("CreatedBy", fields: [created_by_id], references: [id])
      last_modified_by       User?     @relation("LastModifiedBy", fields: [last_modified_by_id], references: [id])
      contacts               Contact[] // Many-to-Many via junction if needed
      rfqs                   RFQ[]
      quotes                 Quote[]
    }
    ```
    - Enums for stage_name, etc.
    - Indexing: BTREE on close_date, stage_name, amount.
    - Triggers: Auto-update probability/is_closed based on stage; log changes.
  - API Design:
    - POST `/api/opportunities`: Body matches; sync.
    - GET `/api/opportunities/:id`: Include items/hierarchy.
    - PUT `/api/opportunities/:id`: Update/sync.
    - GET `/api/opportunities/pipeline`: Aggregates by stage.
    - POST `/api/integrations/salesforce/sync-opportunity`.
  - UI Wireframes: Opportunity form with picklists; kanban pipeline; item table.
  - Integration: Map to Salesforce (e.g., items to separate OpportunityLineItem creates if expanded).
- **Deliverables**: Schema, API docs, wireframes, mapping.

#### Phase 2: Backend Development (Weeks 2-3)
- **Activities**:
  - Controllers: CRUD with validation (e.g., close_date required, probability auto-calc: e.g., 'Closed Won' = 100%).
  - Pipeline Logic: Aggregates (SUM(amount) GROUP BY stage_name).
  - Salesforce Sync:
    ```ts
    // services/salesforceService.ts
    export const syncOpportunityToSF = async (opp) => {
      const sfOpp = {
        Name: opp.name,
        AccountId: (await prisma.account.findUnique({ where: { id: opp.account_id } })).integration_id,
        Amount: opp.amount,
        CloseDate: opp.close_date,
        StageName: opp.stage_name,
        // Map others; for items, create OpportunityLineItems if needed
      };
      const result = await conn.sobject('Opportunity').create(sfOpp);
      // Update integration_id
    };
    ```
    - Handle pulls, line items (JSONB to separate syncs).
  - Scenarios: Stage triggers (e.g., 'Proposal' → notify Contacts).
- **Deliverables**: APIs, tests (85%).

#### Phase 4: Integration, Testing, and Security Audit (Weeks 4-5)
- **Activities**:
  - Integrate: Link to RFQ (auto from items); update on Quote approval.
  - Testing: E2E (create Opportunity → sync → advance stage in Salesforce → pull).
  - Security: IsPrivate enforcement; encrypt descriptions if sensitive.
- **Deliverables**: Reports.

#### Phase 5: Deployment and Monitoring (Weeks 5-6)
- **Activities**:
  - Deploy: CI/CD updates; cron for syncs.
  - Monitoring: Alerts on overdue close_dates.
  - Docs: Pipeline guide.
- **Deliverables**: Live, training.

#### Risks and Mitigations
- **Risk**: Picklist mismatches. **Mitigation**: Dynamic fetch from Salesforce metadata API.
- **Risk**: Amount inconsistencies (currencies). **Mitigation**: Normalize to base currency.
- **Risk**: Line item complexity. **Mitigation**: Start with JSONB; expand to table later.
- **Risk**: Sync delays. **Mitigation**: Async queues (BullMQ).
- **Budget**: ~$6K; **Success Criteria**: Full field match; pipeline accuracy; zero failed syncs.