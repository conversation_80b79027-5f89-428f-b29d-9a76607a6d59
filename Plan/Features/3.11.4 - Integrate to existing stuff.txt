### Introduction to Integrated CRM Schema and Functionalities
This document provides a unified, integrated database schema and functionality overview for the Vendor Management System (VMS), incorporating the new CRM extensions (Account, Contact, Opportunity, Quote) with the existing core modules (User Authentication, Vendor Management, Contract Management, Performance & Compliance, Invoicing & Payments, Reporting & Analytics, Workflows & Automation, Admin Tools, and additional features). The integration ensures seamless data flow: e.g., Opportunities (client deals) trigger RFQs (vendor sourcing), which feed into Quotes (client proposals), linking back to Invoices and Contracts.

The schema is designed for PostgreSQL 17.x (July 29, 2025), using relational principles with JSONB for flexibility. Key integrations:

CRM to Procurement Flow: Opportunity items → RFQ creation → Submissions → Quote selections → Invoice generation.
Existing Schema Alignment: No breaking changes; adds FKs (e.g., rfq.opportunity_id) and extends tables (e.g., quote links to invoice).
Salesforce Sync: integration_id fields for bi-directional API sync (via jsforce; map VMS fields to Salesforce equivalents).
Functionality Enhancements: Workflows trigger on Opportunity stages (e.g., 'Proposal' → auto-RFQ); AI suggestions in Quotes use Vendor KPIs; Audits log CRM changes; RBAC scopes views (e.g., Owners see private Opportunities).
Compliance & Performance: Soft deletes (is_deleted); RLS policies; indexes for pipelines/searches; materialized views for aggregates (e.g., opportunity win rates by account industry).
If not already integrated (per prior docs):

RFQ now optional links to Opportunity (for item pre-fill).
Quote links to Submissions (for selections), Opportunity (source), Account/Contact (target), and Invoice (post-approval).
Existing Invoices/Contracts can reference Quotes/Opportunities retroactively via migrations.
Analytics: New views for CRM metrics (e.g., deal velocity from Opportunity to Quote).
Implementation notes: Use Prisma for ORM; run prisma migrate dev --name crm_integration to apply. Total migration impact: Additive only.

Integrated Database Schema (Prisma Notation)
Full schema combining existing (from Database Plan.txt) with new CRM elements. Changes: Added FKs, extended enums, new models (Account, Contact, Opportunity, Quote), and relations.

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

// Extended Enums (Integrating CRM)
enum RoleEnum {
  ADMIN
  MANAGER
  VIEWER
}

enum StatusEnum {
  ACTIVE
  INACTIVE
  DRAFT
  APPROVED
  SIGNED
  TERMINATED
  PAID
  PARTIALLY_PAID
  DISPUTED
  RESOLVED
  OPEN
  CLOSED
  BLACKLISTED
  PROSPECTING  // New for Opportunity stages
  QUALIFICATION
  NEEDS_ANALYSIS
  VALUE_PROPOSITION
  DECISION_MAKERS
  PERCEPTION_ANALYSIS
  PROPOSAL_PRICE_QUOTE
  NEGOTIATION_REVIEW
  CLOSED_WON
  CLOSED_LOST
}

enum ComplianceTypeEnum {
  GDPR
  ISO_27001
  SOX
  HIPAA
}

enum WorkflowStepTypeEnum {
  APPROVAL
  NOTIFICATION
  INTEGRATION
  BRANCH
  ESCALATION
}

enum NotificationTypeEnum {
  EMAIL
  SMS
}

enum GatewayEnum {
  STRIPE
  PAYPAL
}

enum KpiTypeEnum {
  DELIVERY
  QUALITY
  COST
}

enum AiInsightTypeEnum {
  RISK_PREDICTION
  VENDOR_RECOMMENDATION
}

// New CRM Enums (Salesforce-Aligned)
enum SalutationEnum {
  MR
  MS
  MRS
  DR
  PROF
}

enum LeadSourceEnum {
  WEB
  PHONE_INQUIRY
  PARTNER_REFERRAL
  PURCHASED_LIST
  OTHER
}

enum OpportunityTypeEnum {
  NEW_CUSTOMER
  EXISTING_CUSTOMER_UPGRADE
  EXISTING_CUSTOMER_REPLACEMENT
  EXISTING_CUSTOMER_DOWNGRADE
}

enum ForecastCategoryEnum {
  OMITTED
  PIPELINE
  BEST_CASE
  COMMIT
  CLOSED
}

enum ContactLevelEnum {
  PRIMARY
  SECONDARY
  TERTIARY
}

enum OwnershipEnum {
  PUBLIC
  PRIVATE
  SUBSIDIARY
  OTHER
}

enum RatingEnum {
  HOT
  WARM
  COLD
}

// Existing Models (With Integrations)
model User {
  id            Int       @id @default(autoincrement())
  email         String    @unique
  password      String    // Hashed
  role          RoleEnum  @default(VIEWER)
  is_verified   Boolean   @default(false)
  twofa_secret  String?
  preferences   Json?     // {lang: 'en', currency: 'USD'}
  consent_given Boolean   @default(false)
  created_at    DateTime  @default(now()) @db.Timestamptz()
  updated_at    DateTime  @updatedAt @db.Timestamptz()
  tenant_id     Int?      // Multi-tenant

  sessions             Session[]
  vendor_roles         UserVendorRole[]
  audits               Audit[]           @relation("changed_by")
  config_logs          ConfigLog[]       @relation("changed_by")
  documents            Document[]        @relation("uploaded_by")
  notifications        Notification[]    @relation("sent_to")
  workflow_instances   WorkflowInstance[] @relation("assignee")
  comments             Comment[]         @relation("author")
  backups              Backup[]          @relation("initiated_by")
  accounts_owned       Account[]         @relation("AccountOwner")  // New: Owned Accounts
  contacts_owned       Contact[]         @relation("ContactOwner")  // New
  opportunities_owned  Opportunity[]     @relation("OpportunityOwner")  // New
}

model Vendor {
  // ... Existing fields (name, contact_email, etc.)
  // No direct CRM changes; indirect via RFQs from Opportunities
}

model Contract {
  // ... Existing
  // Optional: Link to Quote if derived (quote_id Int?)
}

model Invoice {
  // ... Existing
  quote_id Int?  // New: Link to Quote for post-approval
  quote    Quote? @relation(fields: [quote_id], references: [id])
}

model RFQ {
  // ... Existing (from 3.10)
  opportunity_id Int?  // New: Link to Opportunity
  opportunity    Opportunity? @relation(fields: [opportunity_id], references: [id], onDelete: SetNull)
}

model RFQSubmission {
  // ... Existing
  // Used in Quote selections via JSON
}

// New CRM Models (Integrated)
model Account {
  id                  Int       @id @default(autoincrement())
  name                String    // Salesforce: Name (required)
  account_number      String?   // AccountNumber
  type                String?   // Picklist (use VARCHAR or ENUM)
  industry            String?   // Picklist
  annual_revenue      Decimal?  // AnnualRevenue
  number_of_employees Int?      // NumberOfEmployees
  ownership           OwnershipEnum?  // Ownership
  phone               String?   // Phone
  fax                 String?   // Fax
  website             String?   // Website
  ticker_symbol       String?   // TickerSymbol
  site                String?   // Site
  rating              RatingEnum?  // Rating
  description         Text?     // Description
  billing_address     Json?     // Composite address
  shipping_address    Json?     // Composite
  parent_account_id   Int?      // ParentId (hierarchy)
  owner_id            Int?      // OwnerId (FK User)
  integration_id      String?   // Salesforce ID
  custom_fields       Json?
  status              StatusEnum @default(ACTIVE)
  created_at          DateTime  @default(now())
  updated_at          DateTime  @updatedAt
  created_by_id       Int?
  last_modified_by_id Int?
  is_deleted          Boolean   @default(false)

  parent              Account?  @relation("AccountHierarchy", fields: [parent_account_id], references: [id])
  children            Account[] @relation("AccountHierarchy")
  owner               User?     @relation("AccountOwner", fields: [owner_id], references: [id])
  created_by          User?     @relation("CreatedBy", fields: [created_by_id], references: [id])
  last_modified_by    User?     @relation("LastModifiedBy", fields: [last_modified_by_id], references: [id])
  contacts            Contact[]
  opportunities       Opportunity[]
  quotes              Quote[]   // New relation
}

model Contact {
  id                     Int       @id @default(autoincrement())
  account_id             Int       // AccountId (required)
  first_name             String?
  last_name              String?   // Required
  salutation             SalutationEnum?
  title                  String?
  department             String?
  phone                  String?
  mobile_phone           String?
  home_phone             String?
  other_phone            String?
  fax                    String?
  email                  String?   // Unique
  mailing_address        Json?
  other_address          Json?
  reports_to_id          Int?      // ReportsToId
  assistant_name         String?
  assistant_phone        String?
  birthdate              DateTime? @db.Date
  lead_source            LeadSourceEnum?
  description            Text?
  languages              String?   // Comma-separated
  level                  ContactLevelEnum?
  do_not_call            Boolean   @default(false)
  has_opted_out_of_email Boolean   @default(false)
  owner_id               Int?
  integration_id         String?
  custom_fields          Json?
  status                 StatusEnum @default(ACTIVE)
  created_at             DateTime  @default(now())
  updated_at             DateTime  @updatedAt
  created_by_id          Int?
  last_modified_by_id    Int?
  is_deleted             Boolean   @default(false)

  account                Account   @relation(fields: [account_id], references: [id], onDelete: Cascade)
  reports_to             Contact?  @relation("ContactHierarchy", fields: [reports_to_id], references: [id])
  reports                Contact[] @relation("ContactHierarchy")
  owner                  User?     @relation("ContactOwner", fields: [owner_id], references: [id])
  created_by             User?     @relation("CreatedBy", fields: [created_by_id], references: [id])
  last_modified_by       User?     @relation("LastModifiedBy", fields: [last_modified_by_id], references: [id])
  opportunities          Opportunity[]  // M2M via junction if needed
  quotes                 Quote[]   // New: Approvers/notifiees
}

model Opportunity {
  id                        Int       @id @default(autoincrement())
  name                      String    // Required
  account_id                Int       // Required
  contact_id                Int?      // Primary Contact
  amount                    Decimal?  // Amount
  close_date                DateTime? @db.Date // Required
  stage_name                String?   // StageName picklist
  probability               Float?    // Probability (0-100)
  type                      OpportunityTypeEnum?
  lead_source               LeadSourceEnum?
  is_private                Boolean   @default(false)
  description               Text?
  forecast_category         ForecastCategoryEnum?
  next_step                 String?
  is_closed                 Boolean   @default(false)
  is_won                    Boolean   @default(false)
  owner_id                  Int?
  has_opportunity_line_item Boolean   @default(false)
  items                     Json?     // Line items for RFQ
  custom_fields             Json?
  integration_id            String?
  status                    StatusEnum @default(OPEN)
  created_at                DateTime  @default(now())
  updated_at                DateTime  @updatedAt
  created_by_id             Int?
  last_modified_by_id       Int?
  is_deleted                Boolean   @default(false)

  account                   Account   @relation(fields: [account_id], references: [id], onDelete: Cascade)
  contact                   Contact?  @relation(fields: [contact_id], references: [id], onDelete: SetNull)
  owner                     User?     @relation("OpportunityOwner", fields: [owner_id], references: [id])
  created_by                User?     @relation("CreatedBy", fields: [created_by_id], references: [id])
  last_modified_by          User?     @relation("LastModifiedBy", fields: [last_modified_by_id], references: [id])
  rfqs                      RFQ[]
  quotes                    Quote[]
}

model Quote {
  id                Int       @id @default(autoincrement())
  opportunity_id    Int       // Required
  account_id        Int       // From Opportunity
  contact_id        Int?      // Approver
  selections        Json      // Aggregated from submissions: [{"item": "", "submission_id": Int, "vendor_id": Int, "price": Decimal}]
  total_amount      Decimal
  currency          String    @default("USD")
  status            StatusEnum @default(DRAFT)
  pdf_url           String?   // S3 PDF
  margins           Decimal?  // Added %
  taxes             Decimal?  // Added %
  approved_at       DateTime?
  integration_id    String?   // Salesforce (if synced as Quote object)
  custom_fields     Json?
  created_at        DateTime  @default(now())
  updated_at        DateTime  @updatedAt
  is_deleted        Boolean   @default(false)

  opportunity       Opportunity @relation(fields: [opportunity_id], references: [id], onDelete: Cascade)
  account           Account     @relation(fields: [account_id], references: [id])
  contact           Contact?    @relation(fields: [contact_id], references: [id])
  invoice           Invoice[]   // One-to-Many if multiple
}

// Remaining Existing Models (Unchanged unless noted)
model Kpi {
  // ... Existing; can extend for CRM (e.g., client_satisfaction KPI)
}

model Workflow {
  // ... Existing; new triggers for Opportunity stages (e.g., 'Closed Won' → Invoice workflow)
}

// ... All other existing models (Audits, Notifications, etc.) remain; audits extended to log CRM actions via triggers.
SQL DDL for Key Integrations (Executable)
For direct DB application; focus on new/changed parts.

-- New Tables (Account, Contact, Opportunity, Quote)
CREATE TABLE accounts (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  -- ... All fields as in Prisma
  -- Constraints: CHECK (name != ''), etc.
);

CREATE TABLE contacts (
  id SERIAL PRIMARY KEY,
  account_id INTEGER NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  -- ... Fields
);

CREATE TABLE opportunities (
  id SERIAL PRIMARY KEY,
  account_id INTEGER NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  contact_id INTEGER REFERENCES contacts(id) ON DELETE SET NULL,
  -- ... Fields
);

CREATE TABLE quotes (
  id SERIAL PRIMARY KEY,
  opportunity_id INTEGER NOT NULL REFERENCES opportunities(id) ON DELETE CASCADE,
  account_id INTEGER NOT NULL REFERENCES accounts(id),
  contact_id INTEGER REFERENCES contacts(id),
  -- ... Fields
);

-- Extensions to Existing Tables
ALTER TABLE rfqs ADD COLUMN opportunity_id INTEGER REFERENCES opportunities(id) ON DELETE SET NULL;
ALTER TABLE invoices ADD COLUMN quote_id INTEGER REFERENCES quotes(id) ON DELETE SET NULL;

-- Indexes for Integrations
CREATE INDEX idx_rfqs_opportunity_id ON rfqs (opportunity_id);
CREATE INDEX idx_invoices_quote_id ON invoices (quote_id);
CREATE INDEX idx_opportunities_stage_name ON opportunities (stage_name);
CREATE INDEX idx_quotes_status ON quotes (status);

-- Triggers for Integrations (e.g., Auto-Update Opportunity on Quote Approval)
CREATE FUNCTION update_opportunity_on_quote_approval() RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'APPROVED' THEN
    UPDATE opportunities SET stage_name = 'CLOSED_WON', is_won = TRUE, is_closed = TRUE
    WHERE id = NEW.opportunity_id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER quote_approval_trigger AFTER UPDATE ON quotes
FOR EACH ROW WHEN (OLD.status != 'APPROVED' AND NEW.status = 'APPROVED')
EXECUTE PROCEDURE update_opportunity_on_quote_approval();

-- Materialized View for Integrated Analytics (e.g., Opportunity to Quote Conversion)
CREATE MATERIALIZED VIEW matvw_opportunity_metrics AS
SELECT o.id AS opportunity_id,
       a.name AS account_name,
       COUNT(q.id) AS quote_count,
       SUM(q.total_amount) AS total_quoted,
       AVG(o.probability) AS avg_probability,
       CASE WHEN o.is_won THEN 'Won' ELSE 'Lost' END AS outcome
FROM opportunities o
JOIN accounts a ON o.account_id = a.id
LEFT JOIN quotes q ON o.id = q.opportunity_id
GROUP BY o.id, a.name;
-- Refresh: REFRESH MATERIALIZED VIEW matvw_opportunity_metrics;

-- RLS Policies for CRM Security
ALTER TABLE opportunities ENABLE ROW LEVEL SECURITY;
CREATE POLICY opportunity_rbac ON opportunities
FOR ALL
USING (owner_id = current_setting('app.current_user_id')::INTEGER OR is_private = FALSE);  // Owners or public

-- Similar for other CRM tables
Integrated Functionalities Overview
CRM-Procurement Flow:

Opportunity Creation: Auto-links to Account/Contacts; items JSON pre-fills RFQ form.
RFQ from Opportunity: API endpoint /api/opportunities/:id/create-rfq copies items, sets rfq.opportunity_id.
Quote from Submissions: /api/rfqs/:id/create-quote aggregates selections into quote.selections JSON; sets quote.opportunity_id, auto-calcs total_amount (sum prices + margins/taxes); links to Account/Contact from Opportunity.
Approval to Invoice: On quote approval (public endpoint), trigger workflow to create Invoice (copy quote items/amount); set invoice.quote_id.
Stage Updates: Opportunity stage changes trigger notifications to Contacts/Vendors; e.g., 'Closed Lost' cancels linked RFQs.
Salesforce Sync Integration:

All CRM models (Account, Contact, Opportunity, Quote) have integration_id; sync functions map fields (e.g., Opportunity.items to Salesforce OpportunityLineItems via separate creates).
Backend Service: Extend salesforceService.ts with batch syncs; handle dependencies (sync Account first for AccountId).
Webhooks: Salesforce outbound messages to VMS endpoint /api/webhooks/salesforce/update for pulls.
Workflows & Automation:

New Triggers: Opportunity stage changes (e.g., 'Proposal' → approval workflow with Contact assignee).
Escalations: If Quote not approved by close_date, escalate to Account owner.
Integrations: Sync to Salesforce on workflow completion.
Reporting & Analytics:

Extended Views: Include CRM in spend_analysis (e.g., total_spend by account_industry).
AI Insights: Predict Opportunity win probability using Vendor KPIs from linked RFQs.
Dashboards: Pipeline by stage; conversion rates (Opportunities → Quotes → Won).
RBAC & Security:

Viewers read Opportunities if not private; Managers edit/create.
RLS: Scope to tenant/owner.
Audits: Log all CRM actions (triggers extended to new tables).
Frontend Integrations:

Routes: /opportunities/:id/rfq pre-fills from items; /opportunities/:id/quote shows submission selector.
Components: PipelineKanban (drag stages, auto-sync); QuoteBuilder (multi-select items from submissions).
Backend API Enhancements:

Extended Endpoints: Add opportunity_id to RFQ create; quote create validates selections against submissions.
Algorithms: Quote total calc: selections.reduce((sum, sel) => sum + (sel.price * sel.quantity * (1 + margins/100) * (1 + taxes/100)), 0).
Deployment & Migration:

Run Prisma migration; seed sample data (e.g., Account → Contact → Opportunity → RFQ).
Backfill: Script to link existing RFQs to dummy Opportunities if needed.
Monitoring: CloudWatch for sync errors, conversion metrics.
This integration ensures a cohesive system: CRM drives procurement, with full traceability and Salesforce compatibility. Estimated effort: 2-4 weeks for migration/tests. If gaps remain (e.g., expand line items to separate table), iterate in next phases.