### Introduction to User Authentication & Authorization Module

The User Authentication & Authorization module is a foundational component of the Vendor Management System (VMS). It ensures secure access to the application, protects sensitive vendor data (e.g., contracts, invoices, performance metrics), and enforces role-based permissions to prevent unauthorized actions. In a multi-tenant setup where each user/organization has a dedicated EC2 instance and PostgreSQL database, this module guarantees data isolation while allowing seamless collaboration through URL sharing among authenticated users within the same tenant.

This module handles user identity verification (authentication) and permission checks (authorization), incorporating features like email/password login, Single Sign-On (SSO), Role-Based Access Control (RBAC), and additional security scenarios. By implementing robust authentication, the VMS reduces risks such as data breaches, complies with standards like GDPR and ISO 27001, and enhances user experience through features like 2FA and session management.

The implementation will use:
- **Frontend**: React.js with React Router for URL handling and libraries like Axios for API calls.
- **Backend**: Node.js/Express.js with JWT (JSON Web Tokens) for session management, Passport.js for authentication strategies, and Sequelize (ORM) for database interactions.
- **Database**: PostgreSQL to store user data, roles, and sessions.
- **Security Tools**: bcrypt for password hashing, crypto for token generation, and libraries like helmet for Express security headers.

### Business Use Cases

This module addresses key business needs in vendor management, where multiple stakeholders (e.g., procurement teams, finance, legal) interact with sensitive data. Below are detailed use cases, including actors, preconditions, steps, postconditions, benefits, and potential risks mitigated.

1. **User Registration and Onboarding (Email/Password or SSO)**
   - **Actors**: New users (e.g., procurement managers, vendors' representatives).
   - **Preconditions**: User has access to the VMS instance URL; no existing account.
   - **Steps**:
     1. User navigates to `/register` page.
     2. Provides email, password, and optional details (e.g., name, organization role).
     3. Alternatively, selects SSO via Google/OAuth for seamless signup.
     4. System validates input, hashes password (if applicable), creates user record in DB, assigns default role (e.g., Viewer), and sends verification email.
     5. User verifies email to activate account.
   - **Postconditions**: User account is created and activated; initial session token issued.
   - **Business Benefits**: Streamlines onboarding for distributed teams in vendor management, reducing manual admin intervention. SSO integrates with enterprise tools (e.g., Google Workspace), improving adoption in large organizations. Enables quick setup for new tenants on dedicated EC2 instances.
   - **Risks Mitigated**: Prevents fake accounts via email verification; ensures compliance with data privacy by storing minimal PII.
   - **Metrics for Success**: 95% registration completion rate; average onboarding time < 2 minutes.

2. **User Login and Session Management**
   - **Actors**: Existing users.
   - **Preconditions**: User has valid credentials; account is active.
   - **Steps**:
     1. User navigates to `/login`.
     2. Enters email/password or uses SSO.
     3. System authenticates credentials, generates JWT, and redirects to dashboard (e.g., `/dashboard`).
     4. Session timeouts after inactivity (e.g., 30 minutes), requiring re-login.
   - **Postconditions**: User is logged in with a valid token stored in localStorage or cookies; access to role-permitted features.
   - **Business Benefits**: Secure entry point for daily operations like viewing vendor contracts or approving invoices. In a VMS, this ensures only authorized personnel can access real-time vendor performance data, preventing leaks to competitors.
   - **Risks Mitigated**: Brute-force attacks via rate-limiting; session hijacking via secure token storage.
   - **Metrics for Success**: Login success rate > 99%; average session duration tracked for UX improvements.

3. **Role-Based Access Control (RBAC) Enforcement**
   - **Actors**: Admins, Managers, Viewers.
   - **Preconditions**: User is authenticated.
   - **Steps**:
     1. Admin (full access) can create/edit users, assign roles, and manage all vendor data.
     2. Manager (approve contracts) can view/edit vendor profiles, approve/reject contracts, but not delete users.
     3. Viewer (read-only) can browse shared URLs (e.g., `/vendors/123`) but cannot modify data.
     4. On page load or API call, system checks JWT claims for role and permissions.
   - **Postconditions**: Actions are allowed/denied based on role; audit logs recorded.
   - **Business Benefits**: In vendor management, RBAC segregates duties—e.g., finance managers approve payments without accessing legal contracts. This supports scalability for enterprises with hierarchical teams, ensuring compliance in regulated industries like finance or healthcare.
   - **Risks Mitigated**: Privilege escalation via fine-grained permissions; insider threats by limiting access.
   - **Metrics for Success**: Zero unauthorized access incidents; role assignment efficiency measured by admin feedback.

4. **URL Sharing Among Authenticated Users**
   - **Actors**: Authenticated users within the same tenant.
   - **Preconditions**: User is logged in; target resource (e.g., vendor profile) exists.
   - **Steps**:
     1. User copies a deep link (e.g., `/contracts/456/details`).
     2. Shares via email or chat with another authenticated user.
     3. Recipient clicks link; system redirects to login if not authenticated, then checks token for access.
     4. If authorized (based on RBAC), page loads with shared content.
   - **Postconditions**: Recipient views the exact resource without re-navigation.
   - **Business Benefits**: Facilitates collaboration in VMS workflows, e.g., a manager shares a contract URL with legal for review, speeding up approvals. Enhances productivity in remote teams by enabling quick references to vendor data.
   - **Risks Mitigated**: Unauthorized external access via token validation; data exposure limited to tenant boundaries.
   - **Metrics for Success**: Share usage analytics; reduction in email attachments for vendor docs.

5. **Password Reset and Recovery**
   - **Actors**: Users who forgot passwords.
   - **Preconditions**: User has a registered email.
   - **Steps**:
     1. User requests reset via `/forgot-password`.
     2. System sends a time-limited reset token via email.
     3. User clicks link, sets new password.
   - **Postconditions**: Password updated; old sessions invalidated.
   - **Business Benefits**: Minimizes downtime for users handling urgent vendor issues, like invoice disputes.
   - **Risks Mitigated**: Phishing via secure, expiring tokens.

6. **Two-Factor Authentication (2FA)**
   - **Actors**: All users (optional or enforced).
   - **Preconditions**: User enables 2FA in settings.
   - **Steps**:
     1. During login, after credentials, system prompts for OTP (via app like Google Authenticator).
     2. Validates TOTP (Time-based One-Time Password).
   - **Postconditions**: Enhanced security for session.
   - **Business Benefits**: Protects high-value VMS data from credential stuffing attacks.

7. **Session Timeouts and Management**
   - **Actors**: Active users.
   - **Preconditions**: User is logged in.
   - **Steps**:
     1. Inactivity timer triggers logout.
     2. User re-authenticates to resume.
   - **Postconditions**: Session expired; data protected.
   - **Business Benefits**: Prevents unauthorized access in shared environments.

### Detailed Implementation Plan

The plan is phased for iterative development, with timelines assuming a small team (2-3 developers). Total estimated time: 4-6 weeks. Use Agile methodology with weekly sprints.

#### Phase 1: Requirements Gathering and Design (Week 1)
- **Activities**:
  - Refine use cases with stakeholders (e.g., via interviews: "How should RBAC handle vendor approval workflows?").
  - Design database schema:
    - Table: `users` (id: SERIAL PK, email: VARCHAR UNIQUE, password: VARCHAR (hashed), role: ENUM('admin', 'manager', 'viewer'), is_verified: BOOLEAN, 2fa_secret: VARCHAR).
    - Table: `sessions` (id: SERIAL PK, user_id: FK, token: TEXT, expires_at: TIMESTAMP).
    - Table: `roles_permissions` (role: ENUM PK, permission: VARCHAR, e.g., 'vendors:read', 'contracts:approve').
  - API Design:
    - POST `/api/auth/register`: Body {email, password}; returns JWT.
    - POST `/api/auth/login`: Body {email, password}; returns JWT.
    - GET `/api/auth/verify`: Token in header; verifies and returns user data.
    - POST `/api/auth/forgot-password`: Body {email}; sends reset email.
    - POST `/api/auth/reset-password`: Body {token, newPassword}.
    - POST `/api/auth/2fa/setup`: Generates secret; returns QR code.
    - POST `/api/auth/2fa/verify`: Body {token}; validates OTP.
  - UI Wireframes: Sketch login/register pages, role assignment dashboard (using Figma).
  - Security Design: Use JWT with HS256 algorithm; store tokens securely (HttpOnly cookies for SSR, localStorage for SPA). Implement CSRF protection.
- **Deliverables**: Requirements doc, ER diagram, API specs (Swagger), wireframes.
- **Tools**: Draw.io for diagrams, Postman for API mocking.

#### Phase 2: Backend Development (Weeks 2-3)
- **Activities**:
  - Setup Express server with middleware: cors, helmet, rate-limiter (express-rate-limit).
  - Implement Authentication:
    - Use Passport.js for local strategy (email/password) and OAuth2 (passport-google-oauth20).
    - Hash passwords with bcrypt (salt rounds=12).
    - Generate JWT with jsonwebtoken (expiry: 1h for access, 7d for refresh).
  - RBAC: Use a middleware function to check JWT claims (e.g., req.user.role) against required permissions.
  - URL Sharing: No special backend; rely on frontend routing and auth guards.
  - Scenarios:
    - Password Reset: Generate UUID token, store in DB with expiry (1h), email via Nodemailer.
    - 2FA: Use speakeasy for TOTP generation/validation.
    - Session Timeouts: JWT expiry + frontend idle timer (using react-idle-timer).
  - Integrations: Google OAuth setup (client ID/secret in .env).
  - Error Handling: Custom errors (e.g., 401 Unauthorized) with logging (winston).
- **Deliverables**: Functional backend APIs, unit tests (Jest: 80% coverage).

#### Phase 3: Frontend Development (Weeks 3-4)
- **Activities**:
  - Setup React app with React Router: ProtectedRoute component wraps pages, checks token via useAuth hook.
  - Pages:
    - Login/Register: Forms with validation (Formik + Yup), SSO button.
    - Dashboard: Role-based UI (e.g., hide 'Approve' button for Viewers).
  - State Management: Use Redux or Context API for auth state (user, token).
  - URL Sharing: React Router handles deep links; on load, authenticate via API.
  - Scenarios:
    - 2FA: Display QR on setup; input field on login.
    - Timeouts: Idle timer logs out, redirects to /login.
  - UI Library: Material-UI for forms/buttons; responsive design.
- **Deliverables**: Responsive UI, integration tests (React Testing Library).

#### Phase 4: Integration, Testing, and Security Audit (Week 5)
- **Activities**:
  - Integrate FE/BE: Axios interceptors add JWT to headers.
  - Testing:
    - Unit: Cover auth flows.
    - E2E: Cypress scripts (e.g., login -> share URL -> access check).
    - Security: OWASP scans, penetration testing (e.g., simulate brute-force).
  - Performance: Ensure login < 500ms latency.
- **Deliverables**: Test reports, bug fixes.

#### Phase 5: Deployment and Monitoring (Week 6)
- **Activities**:
  - Deploy to EC2: Use PM2 for Node, Nginx for React static files.
  - Tenant Isolation: Each EC2 provisions its own DB; auth scoped to tenant.
  - Monitoring: AWS CloudWatch for logs; alerts on failed logins.
  - Documentation: User guide for 2FA setup.
- **Deliverables**: Live module, CI/CD pipeline (GitHub Actions).

#### Risks and Mitigations
- **Risk**: OAuth misconfiguration. **Mitigation**: Test in staging.
- **Risk**: Token leakage. **Mitigation**: Use secure cookies, HTTPS.
- **Success Criteria**: 100% coverage of use cases; zero critical vulnerabilities.