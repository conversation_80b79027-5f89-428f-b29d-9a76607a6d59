### Introduction to Invoicing & Payments Module

The Invoicing & Payments module is a critical component of the Vendor Management System (VMS), responsible for generating invoices based on contracts, processing payments securely, and handling various payment scenarios. This module integrates tightly with Contract Management (e.g., pulling milestone data for invoice triggers) and Vendor Management (e.g., linking invoices to vendor profiles), as well as Performance & Compliance (e.g., applying penalties to invoices). In the multi-tenant setup with dedicated EC2 instances and PostgreSQL databases per user/organization, it ensures isolated financial data, compliance with payment regulations (e.g., PCI DSS), and accurate tracking to prevent errors or fraud.

By incorporating automated invoice generation with approvals, integrations with Stripe and PayPal for payment processing, and scenario handling like partial payments or currency conversions, this module streamlines financial workflows, reduces payment delays, and supports global operations. It positions the VMS as market-leading by enabling features such as real-time status tracking and dispute resolution tied to invoices, potentially improving cash flow management and reducing disputes by 25-35% through automation.

Implementation will use:
- **Frontend**: React.js with form components for invoice previews, payment gateways (e.g., Stripe Elements), and status dashboards.
- **Backend**: Node.js/Express.js with Sequelize for DB, Stripe SDK (npm: stripe) and PayPal SDK (npm: @paypal/checkout-server-sdk) for integrations, and currency conversion APIs (e.g., openexchangerates via a lightweight library).
- **Database**: PostgreSQL with tables for invoices, payments, disputes; numeric types for precise financial calculations.
- **Additional Tools**: AWS S3 for storing invoice PDFs; email notifications for approvals/status updates.

### Business Use Cases

This module facilitates financial transactions in vendor management, from billing to reconciliation. Use cases are detailed below, including actors, preconditions, steps, postconditions, benefits, and risks mitigated.

1. **Invoice Generation from Contracts with Approvals**
   - **Actors**: Managers (generate/approve), Automated system (trigger).
   - **Preconditions**: Active contract with billable items (e.g., completed milestones); vendor linked.
   - **Steps**:
     1. User navigates to `/invoices/generate?contractId=:id` or system auto-triggers based on contract events (e.g., milestone completion).
     2. System populates invoice: Pulls data like amounts, items from contract; calculates totals, taxes.
     3. Draft saved; routes for approval workflow (e.g., email to approver).
     4. Approver reviews at `/invoices/:id/approve`, adds notes, approves/rejects.
   - **Postconditions**: Invoice status set to 'Approved'; PDF generated and stored; notifications sent to vendor.
   - **Business Benefits**: Automates billing for recurring vendor services, ensuring accuracy (e.g., matching contract terms to avoid over/under-billing). In procurement, this ties directly to performance, applying discounts/penalties seamlessly.
   - **Risks Mitigated**: Manual errors via contract-linked generation; unauthorized invoices via approvals.
   - **Metrics for Success**: Invoice accuracy >98%; approval cycle <48 hours.

2. **Payment Processing with Stripe/PayPal Integration and Status Tracking**
   - **Actors**: Managers (initiate), Vendors (pay via link), System (track).
   - **Preconditions**: Approved invoice; payment gateway configured (Stripe/PayPal API keys in .env).
   - **Steps**:
     1. From invoice page `/invoices/:id/pay`, user selects gateway (Stripe or PayPal).
     2. System creates payment intent/session via SDK, redirects to gateway UI for card/bank details.
     3. Upon completion, webhook updates status (e.g., 'Paid', 'Failed').
     4. Dashboard `/payments/track` shows real-time statuses, history.
   - **Postconditions**: Payment recorded; invoice marked 'Paid'; receipts emailed; audit log updated.
   - **Business Benefits**: Secure, fast payments for vendor settlements, supporting multiple methods (e.g., credit card via Stripe, bank transfer via PayPal). Tracks statuses to enable quick follow-ups, improving vendor relations and cash flow.
   - **Risks Mitigated**: Fraud via gateway security; payment failures via retries/webhooks.
   - **Metrics for Success**: Payment success rate >95%; status update latency <5s.

3. **Partial Payments**
   - **Actors**: Managers (apply), Vendors (initiate partial).
   - **Preconditions**: Invoice outstanding; partial amount agreed (e.g., per contract).
   - **Steps**:
     1. During payment, select 'Partial' option with amount.
     2. System processes via gateway, updates remaining balance.
     3. Tracks partial history in invoice details.
   - **Postconditions**: Invoice status 'Partially Paid'; balance adjusted; notifications for remainder.
   - **Business Benefits**: Flexible for large contracts (e.g., phased projects), allowing incremental settlements without halting work.

4. **Invoice Disputes**
   - **Actors**: Vendors/Managers (raise/resolve).
   - **Preconditions**: Invoice issued; dispute identified (e.g., incorrect amount).
   - **Steps**:
     1. Vendor raises dispute via shared link or Manager at `/invoices/:id/dispute`.
     2. Logs details: Reason, evidence uploads.
     3. Workflow notifies approver; resolution updates invoice (e.g., credit note).
   - **Postconditions**: Dispute resolved; invoice adjusted/reissued; linked to performance module for penalties.
   - **Business Benefits**: Quick resolution prevents escalation, maintaining vendor trust.

5. **Currency Conversions**
   - **Actors**: System (auto-convert), Managers (review).
   - **Preconditions**: Multi-currency contract/invoice (e.g., vendor in EUR, org in USD).
   - **Steps**:
     1. During generation/payment, system fetches exchange rates (e.g., via cached API call).
     2. Converts amounts, displays both currencies.
     3. User confirms; processes in preferred currency via gateway.
   - **Postconditions**: Converted amounts recorded; rates logged for audits.
   - **Business Benefits**: Supports international vendors, avoiding exchange losses.

### Detailed Implementation Plan

Phased plan, assuming 2-3 developers and Agile. Total time: 8-10 weeks, building on prior modules.

#### Phase 1: Requirements Gathering and Design (Weeks 1-2)
- **Activities**:
  - Refine use cases: E.g., define approval workflows, currency sources (e.g., Open Exchange Rates API).
  - Database Schema:
    - Table: `invoices` (id: SERIAL PK, contract_id: FK, vendor_id: FK, amount: DECIMAL(15,2), currency: VARCHAR, status: ENUM('draft', 'approved', 'paid'), items: JSONB, generated_at: TIMESTAMP).
    - Table: `payments` (id: SERIAL PK, invoice_id: FK, amount: DECIMAL, gateway: ENUM('stripe', 'paypal'), status: ENUM('pending', 'completed', 'failed'), transaction_id: VARCHAR, paid_at: TIMESTAMP).
    - Table: `disputes` (id: SERIAL PK, invoice_id: FK, reason: TEXT, status: ENUM, resolved_at: TIMESTAMP).
    - Extend for partials: `partial_payments` (sub-table linked to payments).
    - Indexing: On status, dates for queries.
  - API Design:
    - POST `/api/invoices/generate`: Body {contractId}; returns ID.
    - PUT `/api/invoices/:id/approve`: Updates status.
    - POST `/api/payments/process`: Body {invoiceId, gateway, amount}; initiates.
    - GET `/api/payments/status/:id`: Tracks.
    - POST `/api/invoices/:id/dispute`: Body {reason}.
    - GET `/api/currency/convert`: Query {from, to, amount}; for conversions.
  - UI Wireframes: Invoice form/preview, payment modal with gateway options, dispute form.
  - Integrations: Setup Stripe/PayPal sandbox accounts; select currency API.
- **Deliverables**: Schema, API specs, wireframes.

#### Phase 2: Backend Development (Weeks 3-5)
- **Activities**:
  - Controllers: Invoice generation pulling from contracts; approval workflows with queues.
  - Payments: SDK integrations for Stripe (create intents) and PayPal (orders); webhook endpoints for updates.
  - Scenarios:
    - Partials: Handle incremental updates to balance.
    - Disputes: Link to resolutions, adjust amounts.
    - Conversions: Integrate rate fetching (cache daily rates in DB).
  - Security: PCI compliance via gateways; encrypt transaction data.
  - Error Handling: Handle gateway errors, retries.
- **Deliverables**: APIs, tests (Jest, 85% coverage).

#### Phase 3: Frontend Development (Weeks 5-7)
- **Activities**:
  - Components: InvoiceGenerator (form with contract data), PaymentModal (Stripe Elements/PayPal buttons), StatusTracker (timeline).
  - Routing: `/invoices/generate`, `/invoices/:id/*` (approve, pay, dispute).
  - Features: Currency selector with live conversion; partial payment inputs.
  - RBAC: Restrict approvals to Managers.
- **Deliverables**: UI, API integration.

#### Phase 4: Integration, Testing, and Security Audit (Weeks 7-9)
- **Activities**:
  - Integrate: Link to contracts/vendors; test webhooks in sandbox.
  - Testing: E2E (Cypress: generate -> approve -> pay partially -> dispute).
  - Security: PCI scans; audit financial flows.
  - Performance: Handle high-volume invoices.
- **Deliverables**: Reports.

#### Phase 5: Deployment and Monitoring (Week 10)
- **Activities**:
  - Deploy: EC2 updates for webhooks; secure API keys.
  - Monitoring: CloudWatch for payment failures; transaction logs.
  - Docs: Guide for gateway setup.
- **Deliverables**: Live module.

#### Risks and Mitigations
- **Risk**: Gateway integration failures. **Mitigation**: Sandbox testing, fallback options.
- **Risk**: Currency rate inaccuracies. **Mitigation**: Use reliable API, log rates.
- **Budget**: ~$10K (incl. gateway fees).
- **Success Criteria**: Full coverage; secure, accurate transactions.