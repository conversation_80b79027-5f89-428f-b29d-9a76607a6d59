### Introduction to Vendor Management Module

The Vendor Management module is a core component of the Vendor Management System (VMS), enabling organizations to efficiently onboard, track, and manage vendors throughout their lifecycle. This module handles vendor data from initial registration to ongoing performance evaluation, ensuring compliance, risk mitigation, and operational efficiency. In a multi-tenant architecture with dedicated EC2 instances and PostgreSQL databases per user/organization, it maintains strict data isolation while supporting features like document uploads and audit trails for traceability.

This module incorporates onboarding workflows, detailed profiles, advanced search/filtering, and scenario handling to address real-world complexities such as duplicate vendors or regulatory audits. By automating approvals and providing insightful views, it positions the VMS as a market leader, reducing manual effort in procurement processes and enabling data-driven decisions (e.g., selecting high-performing vendors based on scores).

Implementation will leverage:
- **Frontend**: React.js with components for forms (e.g., React Hook Form), data tables (e.g., React Table), and file uploads (e.g., Dropzone).
- **Backend**: Node.js/Express.js with Sequelize for DB operations, Multer for file handling, and workflow engines like BullMQ for auto-approvals.
- **Database**: PostgreSQL with tables for vendors, documents, histories, and audits; extensions like pg_trgm for fuzzy search.
- **Additional Tools**: AWS S3 for secure document storage (integrated with EC2), Nodemailer for workflow notifications.

### Business Use Cases

This module supports critical business processes in vendor management, such as sourcing reliable suppliers, monitoring performance, and ensuring compliance. Use cases are detailed below, including actors, preconditions, steps, postconditions, benefits, and risks mitigated.

1. **Vendor Onboarding with Auto-Approval Workflows**
   - **Actors**: Procurement admins, new vendors (self-onboarding option).
   - **Preconditions**: User is authenticated (e.g., as Manager or Admin); vendor not already in system.
   - **Steps**:
     1. User navigates to `/vendors/onboard` page.
     2. Fills form with details: name, contact info (email, phone, address), certifications (e.g., ISO, upload proofs), category (e.g., IT services), location.
     3. Submits form; system validates data (e.g., email format, required fields).
     4. Triggers auto-approval workflow: If vendor meets criteria (e.g., valid certification), auto-approves; else, routes to approver via email notification.
     5. Approver reviews and approves/rejects from a dedicated workflow page.
   - **Postconditions**: Vendor record created in DB with status 'Active'; notifications sent; audit log entry added.
   - **Business Benefits**: Accelerates vendor integration for supply chain operations, reducing onboarding time from days to hours. Auto-workflows minimize bottlenecks in high-volume environments like manufacturing, where quick supplier addition is crucial for production continuity.
   - **Risks Mitigated**: Incomplete data via form validation; non-compliant vendors via certification checks.
   - **Metrics for Success**: Onboarding completion rate > 90%; average approval time < 24 hours.

2. **Viewing and Updating Vendor Profiles**
   - **Actors**: Authenticated users (based on RBAC: Viewers read-only, Managers edit).
   - **Preconditions**: Vendor exists in DB; user has appropriate role.
   - **Steps**:
     1. User accesses profile via URL (e.g., `/vendors/:id/profile`).
     2. System loads detailed view: Basic info, history (e.g., contract timelines), ratings (e.g., average score from KPIs), uploaded documents.
     3. For edits: Manager updates fields, uploads new documents (e.g., contracts via file input).
     4. System saves changes, recalculates ratings if needed, and logs updates.
   - **Postconditions**: Profile updated; documents stored securely (e.g., in S3 with DB reference); history appended.
   - **Business Benefits**: Provides a centralized hub for vendor intel, aiding decisions like contract renewals. In scenarios like vendor performance reviews, history and ratings help identify top performers, optimizing spend (e.g., 20% cost savings via better negotiations).
   - **Risks Mitigated**: Data loss via versioned history; unauthorized changes via RBAC.
   - **Metrics for Success**: Page load time < 2s; user satisfaction via feedback (NPS > 8).

3. **Searching and Filtering Vendors**
   - **Actors**: All authenticated users.
   - **Preconditions**: Vendors exist in system.
   - **Steps**:
     1. User navigates to `/vendors/list`.
     2. Applies filters: Category (dropdown), location (search by city/country), performance score (slider, e.g., >80).
     3. System queries DB (e.g., using SQL WHERE clauses) and displays results in a paginated table.
     4. User sorts by columns (e.g., score descending) or exports to CSV.
   - **Postconditions**: Filtered list displayed; URL params preserve filters for sharing (e.g., `/vendors/list?category=IT&scoreMin=80`).
   - **Business Benefits**: Enables quick vendor selection for RFPs (Requests for Proposals), filtering by location for local sourcing compliance or by score for risk-averse choices. In global operations, this supports diversity goals (e.g., filter by certified minority-owned vendors).
   - **Risks Mitigated**: Overwhelming data via efficient indexing; inaccurate results via fuzzy matching.
   - **Metrics for Success**: Search response time < 1s; filter usage analytics.

4. **Vendor Merging for Duplicates**
   - **Actors**: Admins.
   - **Preconditions**: Duplicate vendors identified (e.g., via search showing similar names).
   - **Steps**:
     1. Admin selects duplicates from list view.
     2. System presents merge preview: Combine fields (e.g., keep latest contact), merge histories/documents.
     3. Admin confirms; system merges records, redirects references (e.g., contracts to surviving vendor ID), and deactivates duplicates.
   - **Postconditions**: Single unified vendor record; audit trail logs merge details.
   - **Business Benefits**: Cleans data for accurate reporting, preventing errors like double payments. In large enterprises, this avoids fragmented vendor views, improving analytics accuracy.

5. **Vendor Deactivation**
   - **Actors**: Managers or Admins.
   - **Preconditions**: Vendor exists; no active contracts (optional check).
   - **Steps**:
     1. From profile, select 'Deactivate'.
     2. Provide reason (e.g., poor performance); system updates status to 'Inactive', archives data.
   - **Postconditions**: Vendor hidden from active lists; access restricted; notifications sent.
   - **Business Benefits**: Manages vendor churn, e.g., blacklisting underperformers to mitigate supply risks.

6. **Audit Trails for Vendor Changes**
   - **Actors**: Admins (view audits).
   - **Preconditions**: Changes have occurred.
   - **Steps**:
     1. Access `/vendors/:id/audit` or global audit log.
     2. View chronological logs: Who changed what, when (e.g., "UserX updated rating from 75 to 85 on 2025-07-26").
   - **Postconditions**: Logs displayed/exported.
   - **Business Benefits**: Supports compliance audits (e.g., SOX), tracing issues like unauthorized edits.

### Detailed Implementation Plan

Phased plan for development, assuming 2-3 developers and Agile sprints. Total estimated time: 6-8 weeks, building on auth module.

#### Phase 1: Requirements Gathering and Design (Weeks 1-2)
- **Activities**:
  - Gather specifics: E.g., mandatory onboarding fields, workflow rules (if certification valid > 1 year, auto-approve).
  - Database Schema:
    - Table: `vendors` (id: SERIAL PK, name: VARCHAR, contact_email: VARCHAR, phone: VARCHAR, address: JSONB (for structured location), category: ENUM, certifications: JSONB (array of {type, expiry}), status: ENUM('active', 'inactive'), performance_score: FLOAT, created_at: TIMESTAMP).
    - Table: `vendor_histories` (id: SERIAL PK, vendor_id: FK, change_type: ENUM, old_value: JSONB, new_value: JSONB, changed_by: FK users.id, timestamp: TIMESTAMP).
    - Table: `documents` (id: SERIAL PK, vendor_id: FK, file_name: VARCHAR, s3_url: VARCHAR, uploaded_by: FK, timestamp: TIMESTAMP).
    - Table: `audit_logs` (id: SERIAL PK, entity_type: 'vendor', entity_id: INT, action: VARCHAR, user_id: FK, details: JSONB, timestamp: TIMESTAMP).
    - Indexing: On name (GIN for trigram search), category, score, location (if using PostGIS).
  - API Design:
    - POST `/api/vendors/onboard`: Body {name, contact..., files}; returns vendor ID.
    - GET `/api/vendors/:id`: Returns profile data.
    - PUT `/api/vendors/:id`: Update body; handles uploads.
    - GET `/api/vendors/search`: Query params {category, location, scoreMin}; paginated results.
    - POST `/api/vendors/merge`: Body {primaryId, duplicateIds}; performs merge.
    - PUT `/api/vendors/:id/deactivate`: Body {reason}.
    - GET `/api/vendors/:id/audit`: Returns logs.
  - UI Wireframes: Onboarding form, profile tabs (details/history/documents), search interface with filters.
  - Workflow: Use BullMQ for queues (e.g., approval emails).
- **Deliverables**: Schema diagrams, API docs (Swagger), wireframes, workflow flows.

#### Phase 2: Backend Development (Weeks 2-4)
- **Activities**:
  - Implement Controllers: Vendor creation with validation (Joi), file uploads to S3 (Multer + aws-sdk).
  - Workflows: Queue jobs for auto-approval (e.g., check certification expiry > current date).
  - Search: Sequelize queries with WHERE, ORDER BY; fuzzy search via ILIKE or pg_trgm.
  - Scenarios:
    - Merging: Transactional DB ops to merge data, update FKs.
    - Deactivation: Soft delete (update status).
    - Audits: Middleware logs all CRUD ops.
  - Integrations: S3 for docs; email for workflows.
  - Error Handling: 404 for non-existent vendors, 403 for unauthorized edits.
- **Deliverables**: APIs, unit tests (Jest, 85% coverage).

#### Phase 3: Frontend Development (Weeks 4-6)
- **Activities**:
  - Components: VendorForm (onboarding/edit), ProfileView (tabs with history timeline, rating stars), DataTable (for search with filters).
  - Routing: `/vendors/onboard`, `/vendors/:id/*` (sub-routes for profile sections).
  - State: Use Redux for vendor list; local state for forms.
  - Features: File uploads with progress; real-time search debounce.
  - RBAC: Hide edit buttons for Viewers.
- **Deliverables**: UI pages, integration with APIs.

#### Phase 4: Integration, Testing, and Security Audit (Weeks 6-7)
- **Activities**:
  - Integrate: API calls in components; handle file uploads.
  - Testing: E2E (Cypress: onboard -> search -> merge).
  - Security: Sanitize inputs; encrypt sensitive docs.
  - Performance: Optimize queries for large vendor lists.
- **Deliverables**: Test reports.

#### Phase 5: Deployment and Monitoring (Week 8)
- **Activities**:
  - Deploy: Update EC2 scripts for new endpoints.
  - Monitoring: CloudWatch for API usage; alerts on failed uploads.
  - Docs: User guide for onboarding.
- **Deliverables**: Live module, pipeline updates.

#### Risks and Mitigations
- **Risk**: Data duplication during merge. **Mitigation**: Use DB transactions.
- **Risk**: Large file uploads. **Mitigation**: Size limits (10MB), chunking.
- **Success Criteria**: Full use case coverage; scalable to 10K vendors per tenant.