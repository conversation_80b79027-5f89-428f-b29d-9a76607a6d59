### 3. Architecture & Tech Decisions: Comprehensive Plan and Rationale

We'll cover each aspect with justifications, pros/cons analyses, step-by-step implementation guides, code snippets, potential pitfalls, best practices (as of July 2025, incorporating recent trends like React 19's compiler optimizations and Prisma 5's enhanced type safety), and scalability considerations. The architecture prioritizes modularity, security, and performance in a multi-tenant (per-user isolated) setup, while noting cost optimizations.

The overall system is a full-stack, event-driven SPA with a microservices-inspired backend (initially monolithic for simplicity, evolvable to microservices). We'll use TypeScript end-to-end for type safety, ESLint/Prettier for code consistency, and <PERSON>sky for git hooks. Total estimated dev time for this phase: 4-6 weeks (post-module builds), with iterative refinement via Agile.

#### 3.1 Frontend: Single Page Application (SPA) with React Router for Navigation
**Rationale**: React remains the dominant SPA framework in 2025 (per State of JS surveys), offering component reusability and a vast ecosystem. React Router v7 (latest as of 2025) enables URL-accessible pages for sharing, aligning with requirements. SPA minimizes server loads by handling client-side rendering/routing.

**Tech Stack Decisions**:
- **React Version**: 19.x (released 2024, with built-in compiler for faster builds and automatic memoization).
- **Build Tool**: Vite (faster than Create React App; supports HMR and ES modules natively).
- **Navigation**: React Router v7 – Data APIs for loader/error handling, ensuring deep links (e.g., `/vendors/:id?tab=contracts`) are shareable and auth-protected.
  - Pros: Seamless client-side transitions, prefetching for performance.
  - Cons: SEO challenges (mitigated by SSR if needed later via Next.js migration).
- **Alternatives Considered**: Next.js for SSR/SSG (rejected for pure SPA simplicity; can migrate if SEO becomes critical).

**State Management**: Hybrid approach – Redux Toolkit (RTK) for global/complex states (e.g., auth, vendor lists with RTK Query for API caching), Context API for simpler/local states (e.g., form data).
  - **Why Hybrid?**: Redux excels in debugging (DevTools) and middleware (e.g., thunk for async), but Context avoids boilerplate for lightweight needs. Use Zustand as alternative if Redux feels heavy.
  - Pros: Scalable, predictable state flow.
  - Cons: Overkill for small apps (mitigated by RTK's slices).

**Styling**: Tailwind CSS v4 (preferred over Material-UI for flexibility and smaller bundle size; MUI v6 as fallback if pre-built components are prioritized).
  - **Why Tailwind?**: Utility-first for rapid prototyping, themeable (e.g., dark mode via plugins), integrates with PostCSS. Bundle size ~10KB minified.
  - Pros: Customizable, no context switching from CSS files; responsive utilities align with mobile needs.
  - Cons: Verbose classes (mitigated by editor plugins like Tailwind IntelliSense).
  - Alternatives: Material-UI for opinionated components (use if UI consistency > customization).

**Implementation Plan**:
1. **Setup (1-2 days)**: `npx create-vite@latest frontend --template react-ts`. Install deps: `yarn add react-router-dom @reduxjs/toolkit react-redux tailwindcss postcss autoprefixer`. Init Tailwind: `npx tailwindcss init -p`.
2. **Routing Structure (2-3 days)**: Define routes in `src/routes/index.tsx`. Use `createBrowserRouter` for data loaders.
   ```tsx
   // src/routes/index.tsx
   import { createBrowserRouter, RouterProvider } from 'react-router-dom';
   import { useAuth } from '../hooks/useAuth'; // Custom auth hook

   const router = createBrowserRouter([
     {
       path: '/',
       element: <Layout />, // Shared layout with nav
       children: [
         { path: 'dashboard', element: <Dashboard />, loader: dashboardLoader }, // Prefetch data
         { path: 'vendors/:id', element: <VendorProfile />, loader: vendorLoader }, // URL shareable
       ],
       errorElement: <ErrorPage />,
     },
     { path: '/login', element: <Login /> },
   ]);

   // ProtectedRoute wrapper
   function App() {
     const { isAuthenticated } = useAuth();
     return isAuthenticated ? <RouterProvider router={router} /> : <Navigate to="/login" />;
   }
   ```
   - Pitfall: Route guards – Implement via `useEffect` or custom `ProtectedRoute` to check JWT.
3. **State Management (2 days)**: Setup Redux store in `src/store/index.ts`.
   ```tsx
   // src/store/index.ts
   import { configureStore } from '@reduxjs/toolkit';
   import authReducer from './slices/authSlice';
   import vendorsReducer from './slices/vendorsSlice';

   export const store = configureStore({
     reducer: { auth: authReducer, vendors: vendorsReducer },
     middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(logger), // For dev
   });
   ```
   - For Context: Simple `AuthContext` for login state.
4. **Styling Integration (1 day)**: Configure `tailwind.config.js` for custom themes (e.g., extend colors: { primary: '#007bff' }).
5. **Best Practices**: Lazy loading with `React.lazy` for code splitting; error boundaries; accessibility (ARIA via Tailwind plugins).
6. **Pitfalls & Mitigations**: Hydration errors – Use `Suspense` for async components. Bundle bloat – Analyze with Vite's rollup-plugin-visualizer.
7. **Scalability**: Supports 10K+ components; migrate to Remix if full-stack routing needed.

#### 3.2 Backend: RESTful APIs (or GraphQL for Flexibility)
**Rationale**: Start with REST for simplicity and familiarity; GraphQL as optional evolution for over-fetching reduction in complex queries (e.g., nested vendor-contracts). Node.js/Express (v4.19) for lightweight, async I/O.

**Tech Stack Decisions**:
- **Node Version**: 22.x LTS (2025 stable, with improved ESM support).
- **Framework**: Express.js – Minimalist, middleware-rich.
- **API Style**: RESTful initially (/api/v1/resources); GraphQL via Apollo Server if query complexity grows (pros: flexible schemas; cons: steeper learning, caching challenges).
- **ORM**: Prisma v5.x (preferred over Sequelize for declarative schema, type-safe queries, and migrations).
  - **Why Prisma?**: Auto-generates TypeScript types from schema; supports raw SQL for perf tweaks. Migrations via `prisma migrate dev`.
  - Pros: Modern, intuitive; built-in connection pooling.
  - Cons: Less mature for complex joins (mitigated by raw queries).
  - Alternatives: Sequelize if raw SQL preference (but Prisma's DX wins).

**Implementation Plan**:
1. **Setup (1 day)**: `yarn init -y; yarn add express prisma @prisma/client typescript ts-node`. Init Prisma: `npx prisma init`.
2. **API Structure (3-4 days)**: Versioned routes in `src/routes/v1/index.ts`.
   ```ts
   // src/app.ts
   import express from 'express';
   import cors from 'cors';
   import rateLimit from 'express-rate-limit';
   import v1Router from './routes/v1';

   const app = express();
   app.use(cors({ origin: process.env.FRONTEND_URL })); // React integration
   app.use(rateLimit({ windowMs: 15 * 60 * 1000, max: 100 })); // Per-IP limit
   app.use('/api/v1', v1Router);
   app.use((err, req, res, next) => { res.status(500).json({ error: err.message }); }); // Global error

   // Example endpoint
   // src/routes/v1/vendors.ts
   import { Router } from 'express';
   const router = Router();
   router.get('/:id', async (req, res) => {
     const vendor = await prisma.vendor.findUnique({ where: { id: +req.params.id } });
     if (!vendor) return res.status(404).json({ error: 'Not found' });
     res.json(vendor);
   });
   ```
3. **GraphQL Option (If Adopted, 2 days)**: Install `apollo-server-express`; define schema in SDL.
4. **Best Practices**: Middleware for auth (JWT verify), logging (winston), validation (Joi/Zod).
5. **Pitfalls & Mitigations**: N+1 queries – Prisma's `include` for eager loading. Async errors – Use express-async-errors.
6. **Scalability**: PM2 for clustering; horizontal scale via load balancers.

#### 3.3 Database Schema (Detailed)
**Rationale**: PostgreSQL 17.x (2025 latest, with improved JSONB perf) for ACID compliance and extensions. Per-tenant DBs via separate schemas or instances for isolation.

**High-Level Schema Overview** (Use Prisma schema.prisma for definition):
- **Core Tables** (Detailed Fields, Types, Constraints):
  | Table       | Fields (Type, Constraints) | Purpose |
  |-------------|----------------------------|---------|
  | Users      | id (SERIAL PK), email (VARCHAR UNIQUE NOT NULL), password (VARCHAR NOT NULL - hashed), role (ENUM('admin','manager','viewer') NOT NULL), is_verified (BOOLEAN DEFAULT false), 2fa_secret (VARCHAR), created_at (TIMESTAMP DEFAULT NOW()) | Auth and RBAC. |
  | Vendors    | id (SERIAL PK), name (VARCHAR NOT NULL), contact_email (VARCHAR), address (JSONB), category (ENUM), certifications (JSONB), performance_score (FLOAT DEFAULT 0), status (ENUM('active','inactive') DEFAULT 'active'), created_at (TIMESTAMP) | Vendor data. |
  | Contracts  | id (SERIAL PK), vendor_id (INT FK Vendors ON DELETE CASCADE), title (VARCHAR), status (ENUM), parties (JSONB), clauses (JSONB), start_date (DATE), end_date (DATE), docusign_id (VARCHAR) | Contract lifecycle. |
  | Invoices   | id (SERIAL PK), contract_id (INT FK Contracts), amount (DECIMAL(15,2) NOT NULL), currency (VARCHAR DEFAULT 'USD'), status (ENUM), items (JSONB) | Billing. |
  | Payments   | id (SERIAL PK), invoice_id (INT FK Invoices), amount (DECIMAL(15,2)), gateway (ENUM), transaction_id (VARCHAR), status (ENUM) | Transactions. |
  | Audits     | id (SERIAL PK), entity_type (ENUM NOT NULL), entity_id (INT NOT NULL), action (VARCHAR), user_id (INT FK Users), details (JSONB), timestamp (TIMESTAMP DEFAULT NOW()) | Change tracking. |
  | Attachments| id (SERIAL PK), entity_type (ENUM), entity_id (INT), file_name (VARCHAR), s3_url (VARCHAR NOT NULL), uploaded_by (INT FK Users) | Files (docs, certs). |

- **Relations**:
  - One-to-Many: Vendors → Contracts (vendor_id FK); Contracts → Invoices.
  - Many-to-Many: Users ↔ Vendors via junction table `user_vendor_roles` (user_id, vendor_id, role: ENUM).
  - Self-Referential: Contracts → Amendments (amendment_id FK Contracts).

- **Indexing**:
  - Composite: Vendors (name, category) for searches; Contracts (vendor_id, status, end_date) for renewals.
  - GIN on JSONB fields (e.g., certifications) for fast queries: `CREATE INDEX idx_vendors_certifications ON vendors USING GIN(certifications);`.
  - Full-text: `CREATE INDEX idx_vendors_name ON vendors USING GIN(to_tsvector('english', name));`.

- **Security**: Use pgCrypto for encryption: `CREATE EXTENSION IF NOT EXISTS pgcrypto;`. Encrypt fields like passwords: `pgp_sym_encrypt('data', 'key')`.

**Implementation Plan**:
1. **Schema Definition (1 day)**: Write `prisma/schema.prisma` with models, relations.
   ```prisma
   model User {
     id          Int      @id @default(autoincrement())
     email       String   @unique
     password    String
     role        Role     @default(VIEWER)
     // ... other fields
     vendors     UserVendorRole[]
   }

   enum Role { ADMIN MANAGER VIEWER }
   ```
2. **Migrations (Ongoing)**: `npx prisma migrate dev --name init`. Seed data: `prisma/seed.ts`.
3. **Pitfalls**: Schema drift – Use Prisma's introspect for existing DBs. Data growth – Partition tables by tenant if scaling.
4. **Scalability**: Row-level security (RLS) policies for future multi-tenant sharing: `ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;`.

#### 3.4 API Design
**Rationale**: Versioned to avoid breaking changes; REST for CRUD simplicity.

- **Endpoints**: /api/v1/users (POST register), /api/v1/vendors/:id (GET/PUT), etc.
- **Rate-Limiting**: express-rate-limit (100 req/15min).
- **CORS**: Configured for React origin only.

**Implementation**: As in backend code snippet. Use OpenAPI/Swagger for docs.

#### 3.5 Security
- **HTTPS**: Enforce via Nginx reverse proxy on EC2; Let's Encrypt certs.
- **Validation**: Zod for schemas; prevent SQLi via ORM params.
- **Isolation**: Separate DB per tenant (e.g., AWS RDS instances).
- **GDPR**: Consent flags in users; deletion endpoints (soft delete + purge cron).

**Plan**: Audit with OWASP ZAP; implement via middleware.

#### 3.6 Performance
- **Caching**: Redis (v7.x) per EC2 for queries (e.g., vendor lists); ioredis client.
- **Latency**: Dedicated EC2 (t3.medium initially) per user; CloudFront CDN for static assets.
- **Monitoring**: AWS CloudWatch (logs, metrics); Prometheus exporter for custom stats.

**Plan**: Setup Redis: `yarn add redis`. Cache example: `redis.get('key', async () => await prisma.query())`.

#### 3.7 Testing
- **Unit**: Jest (v29) – 80% coverage; mock Prisma with @prisma/client mocks.
- **Integration**: Cypress (v13) – E2E flows (e.g., login → create vendor).
- **Load**: JMeter – Simulate 100 concurrent users; target <200ms response.

**Plan**: `yarn add --dev jest ts-jest cypress`. CI integration.

#### 3.8 Deployment Pipeline
- **CI/CD**: GitHub Actions – Build/test on push; deploy to EC2 via SSH.
- **Containerization**: Docker – Multi-stage builds for FE/BE.
  ```dockerfile
  # Dockerfile for backend
  FROM node:22-alpine
  WORKDIR /app
  COPY . .
  RUN yarn install --production
  CMD ["node", "dist/app.js"]
  ```
- **Kubernetes**: Optional for orchestration if users >50; start with ECS.
- **Per-User Setup**: AWS CDK script to provision EC2/RDS per signup.
- **Costs**: EC2 t3.medium ~$30/month/user; optimize with autoscaling (e.g., Lambda for low-traffic) or multi-tenant (RLS in single DB to cut costs 70%).

**Plan**: Workflow YAML for Actions; monitor costs via AWS Budgets.

This architecture ensures a robust, future-proof VMS. Next steps: Prototype core endpoints, then integrate FE/BE.