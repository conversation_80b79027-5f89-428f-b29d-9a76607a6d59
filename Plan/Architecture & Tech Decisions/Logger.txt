### Introduction to Comprehensive Logging System

The logging system for the Vendor Management System (VMS) will be a "god-level" implementation, capturing every conceivable event with granular detail, immutability, and traceability. This encompasses **audit logs** for records (e.g., data changes like vendor updates, contract approvals, invoice payments) and **configuration logs** for system settings (e.g., custom fields additions, theme changes, role assignments). The goal is to achieve full auditability for compliance (e.g., GDPR, SOX), debugging, performance analysis, and security forensics, while minimizing overhead in a multi-tenant environment with dedicated EC2 instances and PostgreSQL databases.

Key principles (based on 2025 best practices from sources like Better Stack, Last9, and DEV Community):
- **Structured Logging**: Use JSON format for machine-readable logs, including timestamps, user IDs, IP addresses, event types, before/after states, and contexts.
- **Log Levels**: DEBUG (dev-only), INFO (normal ops), WARN (potential issues), ERROR (failures), AUDIT (immutable changes).
- **Immutability**: Audit logs stored in write-once tables with no delete/update permissions.
- **Coverage**: Every event – API calls, DB operations, workflows, integrations, errors, auth attempts, config changes.
- **Performance**: Asynchronous logging to avoid blocking; use fast libraries like Pino for high-throughput.
- **Retention & Compliance**: Rotate logs (e.g., 90 days for operational, 7 years for audits); anonymize PII for GDPR.
- **Monitoring**: Integrate with AWS CloudWatch for alerts (e.g., on ERROR spikes).

This system differentiates between:
- **Record Logs (Audits)**: Focus on data integrity (e.g., who changed a vendor's score?).
- **Config Logs**: Track system tweaks (e.g., new custom field added).

Rationale: As per 2025 trends (e.g., Pino's dominance for speed, Winston's flexibility), we'll hybridize libraries. Sources emphasize middleware for Express, DB triggers for Postgres, and structured logs for observability.

### Tech Decisions

#### Logging Libraries
- **Primary: Pino (v9.x as of 2025)**: Fastest Node.js logger (benchmarks show 5-10x faster than Winston due to async flushing and low-overhead JSON serialization). Ideal for high-volume operational/event logs.
  - Pros: Minimal CPU impact; built-in redaction for sensitive data (e.g., PII in audits); child loggers for context; transports for DB/files.
  - Cons: Less customizable out-of-box (mitigated by plugins like pino-pretty for dev).
  - Why over Winston? 2025 articles (e.g., Last9, Better Stack) rank Pino #1 for production; Winston (v3.x) as fallback for complex transports if needed.
- **Secondary: Winston (v3.14+)**: For audit-specific logging where customization (e.g., DB transports) is key.
  - Used for configs/audits due to easy integration with Postgres via winston-postgres.
- **HTTP Logging: Morgan (v1.10+)**: Middleware for API request/response logging (e.g., endpoints hit, response times).
- **Alternatives Considered**: Bunyan (structured but slower), Log4js (feature-rich but heavy), Signale (dev-only pretty logs). Pino wins for 2025 perf focus.

#### Storage
- **Audit/Config Logs**: PostgreSQL table (immutable via triggers/policies). Use `audits` table (extended from high-level schema) for records; new `config_logs` for settings.
- **Operational/Error Logs**: Files (rotated daily via Pino transports) + AWS CloudWatch Logs for centralized querying/alerts.
- **Extensions**: Postgres pgaudit for DB-level audits (e.g., trigger on INSERT/UPDATE); Row-Level Security (RLS) to restrict log access to Admins.

#### Integration Points
- **Middleware**: Express global middleware for API events.
- **Prisma Hooks**: Pre/post hooks for DB ops (e.g., log before save).
- **WebSockets/Events**: Log Socket.io emissions (e.g., real-time updates).
- **Security**: Redact sensitive fields (e.g., passwords) using Pino's redaction; encrypt logs with pgCrypto if stored in DB.

#### Performance & Scalability
- Async: Pino's non-blocking; queue logs with BullMQ if spikes occur.
- Volume: Aim for <1ms per log; shard logs per tenant.
- Costs: CloudWatch ~$0.50/GB ingested; optimize with sampling for DEBUG.

### Detailed Implementation Plan

The plan is phased for iterative rollout, assuming integration post-core modules. Total time: 4-6 weeks (2 devs). Use Agile with bi-weekly reviews. All code in TypeScript; test coverage 90%+.

#### Phase 1: Requirements & Setup (Week 1)
- **Activities**:
  - Define Log Taxonomy: Categorize events (e.g., RECORD_CREATE, CONFIG_UPDATE, AUTH_LOGIN_FAIL).
  - Extend DB Schema:
    - `audits` Table (for records): Add fields like `old_value (JSONB)`, `new_value (JSONB)`, `ip_address (VARCHAR)`, `session_id (VARCHAR)`.
    - `config_logs` Table: Similar structure but for settings (e.g., `setting_key (VARCHAR)`, `changed_by (INT FK users)`).
    - Triggers: Postgres function to prevent updates: `CREATE TRIGGER prevent_audit_update BEFORE UPDATE ON audits FOR EACH ROW EXECUTE PROCEDURE raise_exception();`.
    - pgaudit Setup: `CREATE EXTENSION pgaudit; SET pgaudit.log = 'all';` for DB-level logging.
  - Install Libraries: `yarn add pino pino-pretty winston morgan twilio (for SMS alerts on critical errors)`.
  - Configure Pino: Global instance with levels, redaction (e.g., ['password', '2fa_secret']).
    ```ts
    // src/logger.ts
    import pino from 'pino';
    const logger = pino({
      level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
      redact: ['req.headers.authorization', 'password'], // Sensitive redaction
      transport: { target: 'pino-pretty', options: { colorize: true } }, // Dev pretty
      base: { pid: process.pid, hostname: require('os').hostname() },
    });
    export default logger;
    ```
  - Winston for Audits: Custom transport to Postgres.
    ```ts
    // src/transports/postgresAudit.ts
    import { Transport } from 'winston-transport';
    import { PrismaClient } from '@prisma/client';
    const prisma = new PrismaClient();

    class PostgresAuditTransport extends Transport {
      log(info, callback) {
        prisma.audit.create({ data: { ...info } }).then(() => callback());
      }
    }
    ```
- **Deliverables**: Schema migrations (`prisma migrate dev --name logging`), logger module, event taxonomy doc.
- **Best Practices**: Align with OWASP logging cheat sheet: Include context (user/agent), timestamps in UTC, correlation IDs for tracing (e.g., via cls-rtracer).

#### Phase 2: Core Logging Implementation (Weeks 2-3)
- **Activities**:
  - **Middleware for Events**: Global Express middleware to log every request/response.
    ```ts
    // src/middleware/loggerMiddleware.ts
    import logger from '../logger';
    import { v4 as uuidv4 } from 'uuid';

    export const loggerMiddleware = (req, res, next) => {
      const correlationId = uuidv4(); // Trace ID
      req.correlationId = correlationId;
      logger.info({ event: 'API_REQUEST', method: req.method, url: req.url, userId: req.user?.id, ip: req.ip, correlationId });
      
      res.on('finish', () => {
        logger.info({ event: 'API_RESPONSE', status: res.statusCode, duration: Date.now() - req.startTime, correlationId });
      });
      next();
    };
    // In app.ts: app.use(loggerMiddleware);
    ```
  - **Audit Logging for Records**: Prisma middleware to log changes.
    ```ts
    // src/prisma/middleware/audit.ts
    const auditMiddleware = async (params, next) => {
      const result = await next(params);
      if (['create', 'update', 'delete'].includes(params.action)) {
        logger.child({ module: 'audit' }).info({
          event: `RECORD_${params.action.toUpperCase()}`,
          model: params.model,
          args: params.args, // Before/after via diff
          userId: global.currentUser?.id, // From auth middleware
        });
        // Insert to audits table via Winston transport
      }
      return result;
    };
    prisma.$use(auditMiddleware);
    ```
  - **Config Logging**: Hook into admin endpoints (e.g., settings update).
    ```ts
    // src/controllers/admin/settingsController.ts
    import logger from '../../logger';
    export const updateSetting = async (req, res) => {
      const { key, value } = req.body;
      // Update DB
      logger.info({ event: 'CONFIG_UPDATE', key, oldValue: old, newValue: value, userId: req.user.id });
      // Insert to config_logs
    };
    ```
  - **Error Logging**: Global handler.
    ```ts
    app.use((err, req, res, next) => {
      logger.error({ event: 'ERROR', message: err.message, stack: err.stack, correlationId: req.correlationId });
      res.status(500).json({ error: 'Internal Server Error' });
    });
    ```
  - **Real-Time/Workflow Events**: In Socket.io, log emissions.
    ```ts
    // src/socket.ts
    io.on('connection', (socket) => {
      socket.on('comment', (data) => {
        logger.info({ event: 'COLLABORATION_COMMENT', vendorId: data.vendorId, userId: socket.userId });
        // Broadcast
      });
    });
    ```
  - **Notifications on Critical Logs**: Use Twilio for SMS on ERROR (e.g., if breaches detected).
- **Deliverables**: Middleware integrated, sample logs tested (e.g., via Postman).
- **Pitfalls & Mitigations**: Log overload – Sample DEBUG (10%); rotate files with pino-file. Sensitive data – Always redact.

#### Phase 3: Advanced Features & Integration (Weeks 3-4)
- **Activities**:
  - **DB Triggers for Integrity**: Postgres triggers for low-level audits.
    ```sql
    CREATE FUNCTION log_audit() RETURNS TRIGGER AS $$
    BEGIN
      INSERT INTO audits (entity_type, entity_id, action, details, timestamp)
      VALUES (TG_RELNAME, OLD.id, TG_OP, row_to_json(NEW)::jsonb - row_to_json(OLD)::jsonb, NOW());
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    CREATE TRIGGER vendor_audit AFTER UPDATE ON vendors FOR EACH ROW EXECUTE PROCEDURE log_audit();
    ```
  - **Module-Specific Logging**: 
    - Auth: Log login attempts, 2FA fails.
    - Vendor: Log onboarding, merging (with diffs).
    - Contracts: Log e-sign initiations, disputes.
    - Invoicing: Log payments, partials (with amounts).
    - Analytics: Log AI predictions for auditing biases.
    - Workflows: Log step executions, branches, escalations.
    - Admin: Log user invites, backups.
  - **Multi-Tenant Isolation**: Prefix logs with tenant_id (from EC2 metadata or DB schema).
  - **Real-Time Monitoring**: Pipe Pino to CloudWatch via AWS SDK transport.
    ```ts
    const cloudwatchTransport = new WinstonCloudWatch({ logGroupName: 'VMS-Logs', logStreamName: 'audit' });
    ```
  - **Retention Policy**: Cron job to archive old logs to S3 (>90 days); delete after 7 years for audits.
- **Deliverables**: Full integration across modules; dashboard for log queries (e.g., `/admin/logs` with filters).
- **Best Practices**: From Severalnines/DEV Community: Use correlation IDs for tracing distributed events; log at boundaries (entry/exit); avoid logging secrets.

#### Phase 4: Testing, Security Audit, & Optimization (Weeks 5-6)
- **Activities**:
  - **Testing**: Unit (Jest: mock logger calls); E2E (Cypress: simulate events, assert logs in DB).
    ```ts
    // tests/logger.test.ts
    test('logs API request', () => {
      const logSpy = jest.spyOn(logger, 'info');
      // Simulate req
      expect(logSpy).toHaveBeenCalledWith(expect.objectContaining({ event: 'API_REQUEST' }));
    });
    ```
  - **Security Audit**: Scan for log injection (escape inputs); ensure RLS on logs table (e.g., `CREATE POLICY admin_only ON audits FOR SELECT USING (current_user = 'admin');`).
  - **Optimization**: Benchmark with Artillery (load test logging overhead <5% CPU); enable Pino's extreme mode for prod.
  - **Compliance Check**: GDPR: Add consent for logging user actions; export logs on request.
- **Deliverables**: Test reports (90% coverage); audit doc; performance benchmarks.
- **Metrics for Success**: Log ingestion rate >99%; query time <500ms; zero sensitive leaks.

#### Phase 5: Deployment & Maintenance (Ongoing)
- **Activities**:
  - Deploy: Update CI/CD to include log configs; enable CloudWatch alarms (e.g., >10 ERROR/min).
  - Monitoring: Dashboards in Grafana (integrated with Prometheus for metrics like log volume).
  - Maintenance: Weekly reviews of log patterns; auto-purge cron.
- **Deliverables**: Live logging; user guide for querying logs.
- **Risks & Mitigations**: Log spam – Dynamic levels via env vars. Costs – Compress logs (snappy lib if needed).

This plan ensures exhaustive, secure logging, making the VMS audit-ready and resilient. If scaling issues arise, consider migrating audits to a dedicated log DB like TimescaleDB.