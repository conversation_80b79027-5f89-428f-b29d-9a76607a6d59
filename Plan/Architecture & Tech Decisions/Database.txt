erDiagram
    %% Core Entities and Relationships

    %% Authentication & Authorization
    USERS {
        int id PK
        string email UK
        string password
        enum role
        boolean is_verified
        string twofa_secret
        json preferences
        boolean consent_given
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    SESSIONS {
        int id PK
        int user_id FK
        string token
        timestamp expires_at
        timestamp created_at
    }
    ROLE_PERMISSIONS {
        enum role PK
        string[] permissions
    }
    USER_VENDOR_ROLES {
        int user_id PK, FK
        int vendor_id PK, FK
        enum role
        timestamp assigned_at
    }

    %% Vendor Management
    VENDORS {
        int id PK
        string name
        string contact_email
        string contact_phone
        json address
        string category
        json certifications
        float performance_score
        enum status
        json custom_fields
        timestamp created_at
        timestamp updated_at
        timestamp deactivated_at
        text blacklisted_reason
    }
    VENDOR_HISTORIES {
        int id PK
        int vendor_id FK
        string change_type
        json old_value
        json new_value
        int changed_by FK
        timestamp timestamp
    }
    DOCUMENTS {
        int id PK
        string entity_type
        int entity_id FK
        string file_name
        string s3_url
        int uploaded_by FK
        timestamp timestamp
    }

    %% Contract Management
    CONTRACTS {
        int id PK
        int vendor_id FK
        string title
        enum status
        json parties
        json clauses
        json milestones
        date start_date
        date end_date
        string docusign_envelope_id
        json amendments
        timestamp created_at
        timestamp updated_at
    }
    TEMPLATES {
        int id PK
        string name
        string content
        timestamp created_at
    }
    MILESTONES {
        int id PK
        int contract_id FK
        string description
        date due_date
        enum status
        timestamp created_at
    }
    DISPUTES {
        int id PK
        string entity_type
        int entity_id FK
        text description
        json evidence
        enum status
        int resolved_by FK
        text resolution
        timestamp created_at
        timestamp resolved_at
    }

    %% Performance & Compliance
    KPIS {
        int id PK
        int vendor_id FK
        enum type
        float score
        date period
        json details
        timestamp created_at
    }
    RISK_ASSESSMENTS {
        int id PK
        int vendor_id FK
        enum compliance_type
        float risk_score
        json issues
        boolean resolved
        timestamp assessed_at
    }
    ALERTS {
        int id PK
        enum type
        string entity_type
        int entity_id FK
        text message
        enum status
        json notified_users
        timestamp created_at
        timestamp resolved_at
    }

    %% Invoicing & Payments
    INVOICES {
        int id PK
        int contract_id FK
        int vendor_id FK
        decimal amount
        enum currency
        enum status
        json items
        decimal taxes
        decimal penalties
        date due_date
        int approved_by FK
        timestamp created_at
        timestamp updated_at
    }
    PAYMENTS {
        int id PK
        int invoice_id FK
        decimal amount
        enum currency
        enum gateway
        string transaction_id UK
        enum status
        json partial_amounts
        decimal conversion_rate
        timestamp paid_at
        timestamp created_at
    }

    %% Reporting & Analytics
    REPORTS {
        int id PK
        string name
        json config
        int generated_by FK
        timestamp last_generated_at
        timestamp created_at
    }
    AI_INSIGHTS {
        int id PK
        enum type
        int vendor_id FK
        json insight_data
        float confidence
        timestamp generated_at
    }

    %% Workflows & Automation
    WORKFLOWS {
        int id PK
        string name
        json steps
        json triggers
        timestamp created_at
    }
    WORKFLOW_INSTANCES {
        int id PK
        int workflow_id FK
        string entity_type
        int entity_id FK
        enum status
        int current_step
        json context
        timestamp started_at
        timestamp completed_at
    }
    INTEGRATIONS {
        int id PK
        enum type
        json credentials
        string webhook_url
        boolean active
        timestamp created_at
    }
    NOTIFICATIONS {
        int id PK
        enum type
        string recipient
        text content
        string related_entity_type
        int related_entity_id FK
        timestamp sent_at
        enum status
    }

    %% Admin Tools
    SYSTEM_SETTINGS {
        int id PK
        string key UK
        json value
        int updated_by FK
        timestamp updated_at
    }
    BACKUPS {
        int id PK
        string file_name
        string s3_url
        string hash
        bigint size_bytes
        int created_by FK
        timestamp created_at
    }

    %% Additional Features
    COMMENTS {
        int id PK
        enum entity_type
        int entity_id FK
        int user_id FK
        text text
        int parent_comment_id FK
        timestamp created_at
        timestamp updated_at
    }

    %% Logging
    AUDITS {
        int id PK
        string entity_type
        int entity_id FK
        enum action
        int user_id FK
        json old_value
        json new_value
        json details
        timestamp timestamp
    }
    CONFIG_LOGS {
        int id PK
        string setting_key
        enum action
        int user_id FK
        json old_value
        json new_value
        json details
        timestamp timestamp
    }

    %% Relationships

    USERS ||--o{ SESSIONS : "has"
    USERS ||--o{ USER_VENDOR_ROLES : "assigned_to"
    USERS ||--o{ VENDOR_HISTORIES : "changed_by"
    USERS ||--o{ DOCUMENTS : "uploaded_by"
    USERS ||--o{ DISPUTES : "resolved_by"
    USERS ||--o{ INVOICES : "approved_by"
    USERS ||--o{ REPORTS : "generated_by"
    USERS ||--o{ WORKFLOW_INSTANCES : "assigned_to"
    USERS ||--o{ SYSTEM_SETTINGS : "updated_by"
    USERS ||--o{ BACKUPS : "created_by"
    USERS ||--o{ COMMENTS : "author"
    USERS ||--o{ AUDITS : "user"
    USERS ||--o{ CONFIG_LOGS : "user"

    VENDORS ||--o{ USER_VENDOR_ROLES : "assigned_users"
    VENDORS ||--o{ VENDOR_HISTORIES : "history_for"
    VENDORS ||--o{ DOCUMENTS : "documents_for"
    VENDORS ||--o{ CONTRACTS : "contracts_for"
    VENDORS ||--o{ KPIS : "kpis_for"
    VENDORS ||--o{ RISK_ASSESSMENTS : "assessments_for"
    VENDORS ||--o{ AI_INSIGHTS : "insights_for"
    VENDORS ||--o{ COMMENTS : "comments_on"
    VENDORS ||--o{ INVOICES : "invoices_for"

    CONTRACTS ||--o{ MILESTONES : "milestones_for"
    CONTRACTS ||--o{ DISPUTES : "disputes_for"
    CONTRACTS ||--o{ INVOICES : "invoices_for"
    CONTRACTS ||--|{ CONTRACTS : "amends"
    %% Self-referential for amendments

    INVOICES ||--o{ PAYMENTS : "payments_for"
    INVOICES ||--o{ DISPUTES : "disputes_for"

    WORKFLOWS ||--o{ WORKFLOW_INSTANCES : "instances_of"

    WORKFLOW_INSTANCES ||--o{ NOTIFICATIONS : "notifications_for"

    COMMENTS ||--o| COMMENTS : "replies_to"
    %% Self-referential threading