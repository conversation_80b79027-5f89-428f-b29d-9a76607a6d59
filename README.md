# VendorMS - Modern Vendor Management System

A comprehensive, enterprise-grade Vendor Management System built with React, TypeScript, Node.js, and PostgreSQL. Features beautiful neumorphism design, role-based access control, real-time collaboration, AI-powered insights, and advanced RFQ management capabilities.

## 🚀 Features

### Core Functionality

- **Vendor Management**: Complete vendor lifecycle from onboarding to performance tracking with audit trails
- **Account & Contact Management**: Hierarchical account structures with organizational charts
- **RFQ System**: Advanced Request for Quote management with partial selection support
- **Quote Generation**: Intelligent quote builder with commission structures and client approval workflows
- **Contract Management**: Create, track, and manage vendor contracts with milestones and DocuSign integration
- **Invoice Processing**: Generate, approve, and process vendor invoices with RFQ tracking
- **Opportunity Management**: Sales pipeline with kanban boards and forecasting
- **Performance Analytics**: Real-time dashboards and AI-powered insights
- **Risk Assessment**: Automated compliance monitoring and risk scoring
- **Workflow Automation**: Custom workflow builder and execution

### Advanced Features

- **AI-Powered Recommendations**: Multi-provider AI support (OpenAI, Anthropic, Google Gemini) for vendor selection
- **Real-time Collaboration**: Comments system with threading, mentions, and typing indicators
- **Public Submission Portal**: Token-based vendor submission system for RFQs
- **Advanced Analytics**: Bid comparison, submission analytics, and performance metrics
- **Audit Logging**: Comprehensive audit trails for compliance and monitoring
- **File Management**: Secure file uploads with attachment support
- **Bulk Operations**: Mass updates and exports across all modules

### Design & UX

- **Neumorphism UI**: Modern soft UI design with subtle shadows and gradients
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Dark/Light Mode**: Seamless theme switching with system preference detection
- **Micro-animations**: Delightful interactions with Framer Motion
- **Accessibility**: WCAG 2.2 compliant with full keyboard navigation

### Technical Features

- **Role-Based Access Control (RBAC)**: Admin, Manager, and Viewer roles with granular permissions
- **Real-time Updates**: Socket.io integration for live collaboration and notifications
- **Multi-language Support**: i18next with EN/ES/FR translations
- **Multi-currency**: Support for USD/EUR/GBP with automatic conversion
- **State Management**: Redux Toolkit for global state management
- **Form Validation**: React Hook Form with Yup schemas and custom validation
- **API Security**: JWT authentication, input validation, and XSS protection
- **Database**: PostgreSQL with optimized queries and migrations

## 🛠 Tech Stack

### Frontend

- **React 18** - Modern UI library with concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Lightning-fast build tool
- **Tailwind CSS** - Utility-first styling with custom design system
- **Framer Motion** - Smooth animations and transitions

### State Management

- **Redux Toolkit** - Predictable state management
- **React Redux** - React bindings for Redux
- **Context API** - Local component state

### UI Components

- **Shadcn/ui** - Customizable component library
- **Radix UI** - Accessible primitive components
- **Lucide React** - Beautiful icon library
- **Recharts** - Responsive chart library

### Forms & Validation

- **React Hook Form** - Performant forms with minimal re-renders
- **Yup** - Schema-based validation
- **@hookform/resolvers** - Integration layer

### Networking & Data

- **Axios** - HTTP client for API calls
- **TanStack Query** - Server state management
- **Socket.io Client** - Real-time communication

### Internationalization

- **i18next** - Internationalization framework
- **react-i18next** - React integration for i18n

### Development

- **ESLint** - Code linting and quality
- **TypeScript ESLint** - TypeScript-specific rules
- **Vite PWA** - Progressive Web App features

### Backend Stack

- **Node.js** - JavaScript runtime environment
- **Express.js** - Web application framework
- **PostgreSQL** - Primary database with advanced features
- **Socket.io** - Real-time bidirectional communication
- **JWT** - JSON Web Tokens for authentication
- **Bcrypt** - Password hashing and security
- **Multer** - File upload handling
- **Helmet** - Security middleware
- **Morgan** - HTTP request logging
- **Compression** - Response compression middleware

### AI & Machine Learning

- **OpenAI API** - GPT models for recommendations
- **Anthropic Claude** - Advanced AI reasoning
- **Google Gemini** - Multi-modal AI capabilities
- **Custom AI Service Layer** - Provider abstraction and fallback logic

### Database & Storage

- **PostgreSQL** - Primary relational database
- **Database Migrations** - Schema version control
- **File Storage** - Local and cloud storage support
- **Audit Logging** - Comprehensive activity tracking

### Security & Validation

- **Joi** - Server-side data validation
- **Express Validator** - Request validation middleware
- **CORS** - Cross-origin resource sharing
- **Rate Limiting** - API protection
- **Input Sanitization** - XSS prevention

## 🏗 Project Structure

```
├── src/                    # Frontend React application
│   ├── components/         # Reusable UI components
│   │   ├── ui/            # Shadcn UI components
│   │   ├── Layout/        # Layout components (Sidebar, Header)
│   │   ├── accounts/      # Account management components
│   │   ├── contacts/      # Contact management components
│   │   ├── rfq/           # RFQ system components
│   │   ├── admin/         # Admin-specific components
│   │   ├── auth/          # Authentication components
│   │   ├── ai/            # AI configuration components
│   │   ├── analytics/     # Analytics and reporting components
│   │   ├── collaboration/ # Real-time collaboration features
│   │   ├── Comments/      # Comments system
│   │   ├── dashboard/     # Dashboard widgets
│   │   ├── opportunities/ # Opportunity management
│   │   ├── vendors/       # Vendor management components
│   │   └── workflows/     # Workflow builder components
│   ├── pages/             # Route-level page components
│   │   ├── admin/         # Admin pages
│   │   └── opportunities/ # Opportunity management pages
│   ├── hooks/             # Custom React hooks
│   ├── services/          # API service layer and external integrations
│   │   ├── api/           # API client functions
│   │   ├── ai/            # AI service adapters
│   │   ├── encryption/    # Security services
│   │   └── storage/       # Storage utilities
│   ├── store/             # Redux store configuration
│   │   └── slices/        # Feature-specific Redux slices
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions
│   └── lib/               # Shared libraries and configurations
├── backend/               # Node.js/Express API server
│   ├── controllers/       # Route handlers and business logic
│   ├── models/            # Database models and schemas
│   ├── routes/            # Express route definitions
│   ├── services/          # Business logic services
│   ├── middleware/        # Express middleware
│   ├── validation/        # Input validation schemas
│   ├── config/            # Configuration files
│   ├── scripts/           # Database and utility scripts
│   ├── uploads/           # File upload storage
│   └── data/              # Default data and templates
├── database/              # Database schemas and migrations
│   ├── schema.sql         # Complete database schema
│   ├── migrations/        # Database migration files
│   └── README.md          # Database setup instructions
├── docs/                  # Project documentation
├── Plan/                  # Project planning documents
├── public/                # Static frontend assets
└── jarvis/                # AI assistant specifications
```

## 🚦 Getting Started

### Prerequisites

- Node.js 18+ and npm/yarn
- PostgreSQL 12+ database
- Modern browser with ES2020+ support

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd vendorms
   ```

2. **Install frontend dependencies**

   ```bash
   npm install
   ```

3. **Install backend dependencies**

   ```bash
   cd backend
   npm install
   cd ..
   ```

4. **Database Setup**

   ```bash
   # Create PostgreSQL database
   createdb vendorms_dev

   # Run database schema
   psql vendorms_dev < database/schema.sql

   # Set up environment variables
   cp backend/.env.example backend/.env
   # Edit backend/.env with your database credentials
   ```

5. **Start the application**

   ```bash
   # Terminal 1: Start backend server (port 3001)
   cd backend
   npm run dev

   # Terminal 2: Start frontend server (port 8080)
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:8080`

### Demo Credentials

The application includes mock authentication with the following demo accounts:

- **Admin**: `<EMAIL>` / `password`
  - Full system access including user management and system settings
- **Manager**: `<EMAIL>` / `password`
  - Can edit vendors, create contracts, approve invoices
- **Viewer**: `<EMAIL>` / `password`
  - Read-only access to view data and reports

## 📱 Features Overview

### Dashboard

- Real-time metrics and KPIs
- Recent activity feed
- Quick action buttons
- Alert notifications
- Performance indicators

### Vendor Management

- Vendor onboarding workflow
- Profile management with custom fields
- Performance scoring and tracking
- Document management
- Certification tracking

### Account & Contact Management

- Hierarchical account structures
- Organizational chart visualization
- Contact relationship mapping
- Bulk import/export capabilities
- Advanced search and filtering

### RFQ System

- Advanced RFQ creation with custom forms
- Partial selection support for vendors
- Public submission portal with token-based access
- Real-time bid comparison and analytics
- AI-powered vendor recommendations

### Quote Generation

- Intelligent quote builder from RFQ submissions
- Commission structure management
- Client approval workflows with e-signatures
- PDF generation with custom templates
- Quote tracking and interaction analytics

### Contract Management

- Contract lifecycle management
- Milestone tracking
- DocuSign integration (mock)
- Amendment history
- Renewal alerts

### Invoice Processing

- Invoice generation from contracts and RFQs
- Multi-step approval workflow
- Payment processing (Stripe/PayPal mock)
- Tax and penalty calculations
- Dispute management

### Opportunity Management

- Sales pipeline with kanban boards
- Forecasting and probability tracking
- Account and contact integration
- Stage-based workflow automation
- Performance analytics

### Analytics & Reporting

- Interactive dashboards
- Custom report builder
- Export to PDF/Excel
- AI-powered insights
- Performance trends

### Administration

- User management with role assignment
- System configuration
- Backup and restore
- Audit logs
- Custom field definitions

## 🎨 Design System

The application uses a modern neumorphism design system with:

- **Soft Shadows**: Subtle elevation with inner/outer shadows
- **Rounded Corners**: Consistent border radius throughout
- **Smooth Transitions**: 300ms cubic-bezier animations
- **Color Harmony**: HSL-based color palette with semantic tokens
- **Typography**: Inter font family with proper hierarchy
- **Spacing**: 8px grid system for consistent layouts

### Color Palette

- **Primary**: Blue tones for actions and emphasis
- **Success**: Green for positive states
- **Warning**: Amber for caution states
- **Destructive**: Red for dangerous actions
- **Muted**: Gray tones for secondary content

## 🔒 Security Features

- **JWT Authentication**: Secure token-based auth
- **Role-Based Permissions**: Granular access control
- **Input Validation**: Client and server-side validation
- **XSS Protection**: Sanitized user inputs
- **CSRF Protection**: Cross-site request forgery prevention

## 🌐 Internationalization

Supports multiple languages with:

- English (en)
- Spanish (es)
- French (fr)

Translation keys are organized by feature modules for easy maintenance.

## 📱 PWA Features

- **Offline Support**: Cache critical pages and assets
- **Install Prompt**: Native app-like installation
- **Push Notifications**: Real-time alerts (when backend available)
- **Background Sync**: Queue actions when offline

## 🏗 Backend Architecture

### API Structure

The backend follows a RESTful API design with the following endpoints:

- **Authentication**: `/api/auth` - Login, registration, password reset
- **Users**: `/api/users` - User management and profiles
- **Vendors**: `/api/vendors` - Vendor CRUD operations
- **Accounts**: `/api/accounts` - Account hierarchy management
- **Contacts**: `/api/contacts` - Contact management with org charts
- **RFQs**: `/api/rfqs` - Request for Quote system
- **Quotes**: `/api/quotes` - Quote generation and management
- **Contracts**: `/api/contracts` - Contract lifecycle management
- **Invoices**: `/api/invoices` - Invoice processing and tracking
- **Opportunities**: `/api/opportunities` - Sales pipeline management
- **Comments**: `/api/comments` - Real-time collaboration system
- **AI Config**: `/api/ai` - AI provider configuration
- **Public Submissions**: `/api/public/rfq` - Token-based vendor submissions

### Database Schema

- **PostgreSQL** with comprehensive relational design
- **Audit trails** for all major entities
- **Hierarchical structures** for accounts and contacts
- **JSONB fields** for flexible metadata storage
- **Full-text search** capabilities
- **Optimized indexes** for performance

### Real-time Features

- **Socket.io** integration for live updates
- **Typing indicators** in comments
- **Presence tracking** for collaborative editing
- **Real-time notifications** for RFQ updates
- **Live bid comparison** during RFQ evaluation

### AI Integration

- **Multi-provider support**: OpenAI, Anthropic, Google Gemini
- **Vendor recommendation engine** with scoring algorithms
- **Risk assessment** based on historical data
- **Fallback mechanisms** for AI service failures
- **Usage tracking** and quota management

## 🧪 Testing Strategy

- **Unit Tests**: Jest with React Testing Library
- **Integration Tests**: API and component integration
- **E2E Tests**: Playwright for user journey testing
- **Accessibility Tests**: axe-core integration
- **Backend Testing**: Comprehensive API test suites
- **Database Testing**: Migration and schema validation

## 📈 Performance Optimizations

- **Code Splitting**: Route-based chunks
- **Lazy Loading**: Dynamic imports for heavy components
- **Image Optimization**: WebP format with fallbacks
- **Bundle Analysis**: Webpack Bundle Analyzer integration
- **Caching Strategy**: Service worker with cache-first approach

## 🚀 Deployment

### Vercel (Recommended)

```bash
npm run build
npx vercel --prod
```

### Netlify

```bash
npm run build
# Deploy dist/ folder to Netlify
```

### Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 8080
CMD ["npm", "run", "preview"]
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For questions and support:

- 📧 Email: <EMAIL>
- 💬 Discord: [VendorMS Community](https://discord.gg/vendorms)
- 📖 Documentation: [docs.vendorms.com](https://docs.vendorms.com)

## 🗺 Roadmap

### Phase 1 (Current)

- ✅ Core vendor management
- ✅ Authentication and RBAC
- ✅ Dashboard and analytics
- ✅ Modern UI with neumorphism

### Phase 2 (Q2 2025)

- 🔄 Real-time collaboration
- 🔄 Advanced reporting
- 🔄 Mobile app (React Native)
- 🔄 API integrations

### Phase 3 (Q3 2025)

- 📅 AI-powered recommendations
- 📅 Blockchain verification
- 📅 Advanced workflow automation
- 📅 Multi-tenant architecture

---

Built with ❤️ using modern web technologies and best practices.
