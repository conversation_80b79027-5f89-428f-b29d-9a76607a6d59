# Contract Management Setup & Testing Guide

## Issues Fixed ✅

### 1. Token Mismatch Fixed
- **Problem**: API client was looking for `auth_token` but auth system uses `vms_token`
- **Solution**: Updated `src/services/api.ts` to use correct token name

### 2. Authentication Bypass for Development
- **Problem**: Contract API calls were failing due to missing authentication
- **Solution**: Added development bypass in `backend/middleware/auth.js`

### 3. API Response Handling
- **Problem**: Contract slice wasn't handling API responses properly
- **Solution**: Added proper error handling and response structure validation

## Backend Setup

### 1. Install Dependencies
```bash
cd backend
npm install
```

### 2. Database Setup
Run the contract templates seeding script:
```bash
psql -d vendorms -f backend/scripts/seed-contract-templates.sql
```

### 3. Environment Variables
Make sure your `.env` file has:
```bash
NODE_ENV=development
JWT_SECRET=your_jwt_secret_here
```

### 4. Start Backend
```bash
cd backend
npm run dev
```

## Testing the Contract API

### 1. Test Health Check
```bash
curl http://localhost:3001/health
```

### 2. Test Contract Templates
```bash
curl http://localhost:3001/api/contracts/templates
```

### 3. Test Contract List
```bash
curl http://localhost:3001/api/contracts
```

### 4. Test Contract Creation
```bash
curl -X POST http://localhost:3001/api/contracts \
  -H "Content-Type: application/json" \
  -d '{
    "vendor_id": 1,
    "title": "Test Contract",
    "parties": {"vendor": "Test Vendor", "client": "Test Client"},
    "clauses": {"payment_terms": "30 days", "deliverables": "Test deliverables"},
    "start_date": "2024-01-01",
    "end_date": "2024-12-31"
  }'
```

## Frontend Testing

### 1. Check Browser Console
- Open browser dev tools
- Navigate to contracts list page
- Look for console messages:
  - ✅ "API Response: {data: [...], pagination: {...}}" = API working
  - ⚠️ "Contract API call failed, using mock data" = API not working

### 2. Check Network Tab
- Open Network tab in dev tools
- Navigate to contracts page
- Look for API calls to `/api/contracts`
- Check response status and data

## Expected Behavior

### When API is Working:
- Contract list shows real data from database
- Console shows "API Response: ..." messages
- Network tab shows successful API calls
- No "using mock data" warnings

### When API is Not Working:
- Contract list shows mock data
- Console shows "Contract API call failed, using mock data"
- Network tab shows failed API calls or no calls at all

## Troubleshooting

### 1. Backend Not Running
```bash
cd backend
npm run dev
```

### 2. Database Connection Issues
- Check PostgreSQL is running
- Verify database credentials in `.env`
- Run database setup scripts

### 3. Port Conflicts
- Backend should run on port 3001
- Frontend should run on port 8080
- Check no other services are using these ports

### 4. CORS Issues
- Backend CORS is configured for `http://localhost:8080`
- Make sure frontend is running on correct port

## Next Steps

1. **Remove Development Bypass**: Once authentication is fully implemented, remove the development bypass in `auth.js`
2. **Add Real Authentication**: Implement proper JWT token generation and validation
3. **Complete DocuSign Integration**: Add real DocuSign API credentials and implement e-signature functionality
4. **Add File Upload**: Implement document upload and storage functionality

## API Endpoints Available

- `GET /api/contracts` - List contracts with filtering
- `GET /api/contracts/templates` - Get contract templates
- `GET /api/contracts/:id` - Get specific contract
- `POST /api/contracts` - Create new contract
- `PUT /api/contracts/:id` - Update contract
- `DELETE /api/contracts/:id` - Delete contract
- `POST /api/contracts/:id/amendments` - Create amendment
- `POST /api/contracts/:id/esign` - Initiate e-signature (placeholder)
- `PUT /api/contracts/milestones/:id` - Update milestone