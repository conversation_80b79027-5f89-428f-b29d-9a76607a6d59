# RFQ Detail Page Enhancements - Technical Documentation

## Overview

This document provides technical details for the RFQ Detail Page enhancements, including database schema changes, API endpoints, component architecture, and deployment considerations.

## Database Schema Changes

### New Tables

#### `quote_selections`
Tracks detailed quote selections for advanced quote generation.

```sql
CREATE TABLE quote_selections (
    id SERIAL PRIMARY KEY,
    quote_id INTEGER NOT NULL REFERENCES client_quotes(id) ON DELETE CASCADE,
    submission_id INTEGER NOT NULL REFERENCES rfq_submissions(id) ON DELETE CASCADE,
    bid_item_id INTEGER REFERENCES bid_items(id) ON DELETE CASCADE,
    vendor_id INTEGER NOT NULL REFERENCES vendors(id),
    rfq_item_id VARCHAR(50),
    item_name VARCHAR(255),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(15, 2) NOT NULL CHECK (unit_price >= 0),
    total_price DECIMAL(15, 2) NOT NULL CHECK (total_price >= 0),
    commission_rate DECIMAL(5, 2) DEFAULT 0.00 CHECK (commission_rate >= 0),
    commission_amount DECIMAL(15, 2) DEFAULT 0.00 CHECK (commission_amount >= 0),
    final_price DECIMAL(15, 2) NOT NULL CHECK (final_price >= 0),
    is_whole_submission BOOLEAN DEFAULT false,
    selection_rationale TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### `invitation_tracking`
Enhanced engagement tracking for RFQ invitations.

```sql
CREATE TABLE invitation_tracking (
    id SERIAL PRIMARY KEY,
    invitation_id INTEGER NOT NULL REFERENCES rfq_invitations(id) ON DELETE CASCADE,
    viewed_at TIMESTAMP WITH TIME ZONE,
    reminded_at TIMESTAMP WITH TIME ZONE,
    reminder_count INTEGER DEFAULT 0,
    engagement_data JSONB DEFAULT '{}'::JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(invitation_id)
);
```

### Enhanced Existing Tables

#### `rfqs` Table Additions
```sql
ALTER TABLE rfqs ADD COLUMN allow_partial_selection BOOLEAN DEFAULT false;
ALTER TABLE rfqs ADD COLUMN partial_selection_config JSONB DEFAULT '{
    "enabled": false,
    "requireVendorConfirmation": true,
    "confirmationMessage": "Do you allow individual item purchases at the quoted rates?",
    "instructions": "Please confirm if you allow partial selection of items from your submission.",
    "defaultAllowed": false
}'::JSONB;
```

#### `rfq_submissions` Table Additions
```sql
ALTER TABLE rfq_submissions ADD COLUMN allows_partial_selection BOOLEAN DEFAULT false;
ALTER TABLE rfq_submissions ADD COLUMN partial_selection_notes TEXT;
ALTER TABLE rfq_submissions ADD COLUMN viewed_at TIMESTAMP WITH TIME ZONE;
```

#### `client_quotes` Table Additions
```sql
ALTER TABLE client_quotes ADD COLUMN account_id INTEGER;
ALTER TABLE client_quotes ADD COLUMN opportunity_id INTEGER;
ALTER TABLE client_quotes ADD COLUMN account_name VARCHAR(255);
ALTER TABLE client_quotes ADD COLUMN opportunity_name VARCHAR(255);
ALTER TABLE client_quotes ADD COLUMN global_commission_rate DECIMAL(5, 2) DEFAULT 0.00;
ALTER TABLE client_quotes ADD COLUMN item_commission_overrides JSONB DEFAULT '{}'::JSONB;
ALTER TABLE client_quotes ADD COLUMN total_cost DECIMAL(15, 2) DEFAULT 0.00;
ALTER TABLE client_quotes ADD COLUMN total_margin DECIMAL(15, 2) DEFAULT 0.00;
```

### New Indexes
```sql
-- Performance optimization indexes
CREATE INDEX idx_rfqs_partial_selection ON rfqs(allow_partial_selection);
CREATE INDEX idx_submissions_partial_selection ON rfq_submissions(allows_partial_selection);
CREATE INDEX idx_submissions_viewed_at ON rfq_submissions(viewed_at);
CREATE INDEX idx_quote_selections_quote ON quote_selections(quote_id);
CREATE INDEX idx_quote_selections_submission ON quote_selections(submission_id);
CREATE INDEX idx_quotes_account_opportunity ON client_quotes(account_id, opportunity_id);
CREATE INDEX idx_invitation_tracking_invitation ON invitation_tracking(invitation_id);
```

## API Endpoints

### Enhanced RFQ Management
```typescript
// Enhanced RFQ endpoints
PUT    /api/rfqs/:id                    // Update RFQ with enhanced fields
GET    /api/rfqs/:id/items/all          // Get all items for RFQ
POST   /api/rfqs/:id/items              // Add items to RFQ
PUT    /api/rfqs/:id/items/:itemId      // Update specific item
DELETE /api/rfqs/:id/items/:itemId      // Remove item from RFQ
```

### Enhanced Invitation Management
```typescript
// Enhanced invitation endpoints
POST   /api/rfqs/:id/invitations/resend/:invitationId  // Resend invitation
POST   /api/rfqs/:id/invitations/remind/:invitationId  // Send reminder
POST   /api/rfqs/:id/invitations/add-vendors           // Add new vendors
POST   /api/rfqs/invitations/track-view                // Track invitation viewing
GET    /api/rfqs/:id/invitations/metrics               // Get engagement metrics
```

### Advanced Quote Generation
```typescript
// Advanced quote endpoints
POST   /api/rfqs/:id/quotes/advanced                   // Generate advanced quote
POST   /api/quotes/:id/validate-selections             // Validate partial selections
GET    /api/quotes/:id/pricing-breakdown               // Get detailed pricing
PUT    /api/quotes/:id/link-account-opportunity        // Link to account/opportunity
```

### Account Integration
```typescript
// Account integration endpoints
GET    /api/accounts/search                            // Search accounts
GET    /api/accounts/:id/opportunities                 // Get account opportunities
GET    /api/opportunities/:id/details                  // Get opportunity details
POST   /api/quotes/validate-account-opportunity        // Validate account/opp link
```

## Component Architecture

### Frontend Components

#### `EnhancedItemsDisplay`
**Location:** `src/components/rfq/EnhancedItemsDisplay.tsx`

**Purpose:** Enhanced display of RFQ items with "Show All" functionality

**Key Features:**
- Configurable item display limit
- Modal view for all items
- Search and filtering capabilities
- Responsive design

**Props:**
```typescript
interface EnhancedItemsDisplayProps {
  items: RFQItem[];
  currency?: string;
  maxDisplayItems?: number;
  showExpandButton?: boolean;
  onShowAllItems?: () => void;
  compact?: boolean;
}
```

#### `AdvancedQuoteBuilder`
**Location:** `src/components/rfq/AdvancedQuoteBuilder.tsx`

**Purpose:** Multi-step quote generation with partial selection support

**Key Features:**
- 3-step wizard interface
- Partial selection validation
- Real-time pricing calculations
- Account/opportunity integration
- Commission management

**Props:**
```typescript
interface AdvancedQuoteBuilderProps {
  rfqId: number;
  isOpen: boolean;
  onClose: () => void;
  onQuoteGenerated: (quote: any) => void;
}
```

#### `VendorSelectionDialog`
**Location:** `src/components/rfq/VendorSelectionDialog.tsx`

**Purpose:** Advanced vendor selection with search and filtering

**Key Features:**
- Real-time vendor search
- Performance-based filtering
- Bulk vendor selection
- Engagement metrics display

#### `RFQEdit`
**Location:** `src/pages/RFQEdit.tsx`

**Purpose:** Comprehensive RFQ editing interface

**Key Features:**
- Full RFQ field editing
- Item management (CRUD operations)
- Partial selection configuration
- Change impact warnings

### Backend Services

#### Enhanced RFQ Service
```typescript
class EnhancedRFQService extends RFQService {
  async updateRFQ(rfqId: number, updates: RFQUpdateData, userId: number): Promise<RFQ>;
  async getAllItems(rfqId: number): Promise<RFQItem[]>;
  async updatePartialSelectionConfig(rfqId: number, config: PartialSelectionConfig): Promise<void>;
  async getDetailedAuditHistory(rfqId: number, filters?: AuditFilters): Promise<AuditEntry[]>;
}
```

#### Advanced Quote Generation Service
```typescript
class AdvancedQuoteGenerationService {
  async generateAdvancedQuote(
    rfqId: number,
    selections: QuoteSelectionItem[],
    config: QuoteConfiguration,
    userId: number
  ): Promise<ClientQuote>;
  
  async validatePartialSelections(selections: QuoteSelectionItem[]): Promise<ValidationResult>;
  async calculateQuotePricing(selections: QuoteSelectionItem[], globalCommissionRate: number, itemOverrides: Record<string, number>): Promise<QuotePricing>;
}
```

## Data Models

### Enhanced Interfaces

#### `PartialSelectionConfig`
```typescript
interface PartialSelectionConfig {
  enabled: boolean;
  requireVendorConfirmation: boolean;
  confirmationMessage: string;
  instructions: string;
  defaultAllowed: boolean;
}
```

#### `QuoteSelectionItem`
```typescript
interface QuoteSelectionItem {
  submissionId: number;
  bidItemId?: number; // null for whole submission
  vendorId: number;
  vendorName: string;
  rfqItemId?: string;
  itemName?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  allowsPartialSelection: boolean;
  commissionRate?: number; // override global rate
  isWholeSubmission: boolean;
  selectionRationale?: string;
}
```

#### `QuotePricing`
```typescript
interface QuotePricing {
  subtotal: number;
  totalCommission: number;
  totalMargin: number;
  finalAmount: number;
  itemBreakdown: QuoteItemPricing[];
}
```

## Database Functions and Triggers

### Commission Calculation Function
```sql
CREATE OR REPLACE FUNCTION calculate_selection_commission()
RETURNS TRIGGER AS $
DECLARE
    global_rate DECIMAL(5, 2);
    override_rate DECIMAL(5, 2);
    final_rate DECIMAL(5, 2);
BEGIN
    -- Get global commission rate from quote
    SELECT global_commission_rate INTO global_rate 
    FROM client_quotes 
    WHERE id = NEW.quote_id;
    
    -- Check for item-specific override
    SELECT (item_commission_overrides->>NEW.rfq_item_id)::DECIMAL INTO override_rate
    FROM client_quotes 
    WHERE id = NEW.quote_id 
    AND item_commission_overrides ? NEW.rfq_item_id;
    
    -- Use override rate if available, otherwise use global rate
    final_rate := COALESCE(override_rate, global_rate, 0);
    
    -- Calculate commission
    NEW.commission_rate := final_rate;
    NEW.commission_amount := NEW.total_price * (final_rate / 100);
    NEW.final_price := NEW.total_price + NEW.commission_amount;
    
    RETURN NEW;
END;
$ LANGUAGE plpgsql;
```

### Invitation View Tracking Function
```sql
CREATE OR REPLACE FUNCTION track_invitation_view()
RETURNS TRIGGER AS $
BEGIN
    -- Update viewed_at timestamp if not already set
    IF OLD.viewed_at IS NULL AND NEW.viewed_at IS NOT NULL THEN
        -- Insert or update invitation tracking
        INSERT INTO invitation_tracking (invitation_id, viewed_at, engagement_data)
        VALUES (NEW.id, NEW.viewed_at, '{"viewCount": 1}'::JSONB)
        ON CONFLICT (invitation_id) 
        DO UPDATE SET 
            viewed_at = COALESCE(invitation_tracking.viewed_at, NEW.viewed_at),
            engagement_data = jsonb_set(
                invitation_tracking.engagement_data, 
                '{viewCount}', 
                ((invitation_tracking.engagement_data->>'viewCount')::INTEGER + 1)::TEXT::JSONB
            ),
            updated_at = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$ LANGUAGE plpgsql;
```

## Performance Considerations

### Database Optimization
1. **Indexing Strategy:** Comprehensive indexes on frequently queried fields
2. **Query Optimization:** Efficient joins and pagination for large datasets
3. **Caching:** Redis caching for frequently accessed data

### Frontend Optimization
1. **Code Splitting:** Lazy loading of advanced components
2. **Memoization:** React.memo and useMemo for expensive calculations
3. **Virtual Scrolling:** For large item lists in modals

### API Optimization
1. **Pagination:** All list endpoints support pagination
2. **Field Selection:** Optional field selection to reduce payload size
3. **Bulk Operations:** Batch processing for multiple operations

## Security Considerations

### Data Protection
1. **Input Validation:** Comprehensive validation on all inputs
2. **SQL Injection Prevention:** Parameterized queries and ORM usage
3. **Access Control:** Role-based permissions for all operations

### Audit Trail
1. **Comprehensive Logging:** All changes tracked with user attribution
2. **Immutable Records:** Audit entries cannot be modified
3. **Data Integrity:** Hash verification for critical changes

## Testing Strategy

### Unit Tests
- Component testing with React Testing Library
- Service layer testing with Jest
- Database function testing

### Integration Tests
- End-to-end workflow testing
- API endpoint testing
- Database integration testing

### Performance Tests
- Load testing for large datasets
- Concurrent user testing
- Database performance testing

## Deployment Considerations

### Database Migration
1. Run migration script: `database/migrations/003_add_partial_selection_to_rfqs.sql`
2. Verify indexes are created
3. Test triggers and functions

### Application Deployment
1. Deploy backend API changes
2. Deploy frontend components
3. Update environment configurations
4. Run smoke tests

### Rollback Plan
1. Database rollback scripts available
2. Feature flags for gradual rollout
3. Monitoring and alerting setup

## Monitoring and Maintenance

### Key Metrics
- Quote generation success rate
- Partial selection usage
- API response times
- Database query performance

### Alerts
- Failed quote generations
- Database performance degradation
- High error rates on new endpoints

### Maintenance Tasks
- Regular index maintenance
- Audit log archiving
- Performance monitoring
- Security updates

## Future Enhancements

### Planned Features
1. Advanced analytics dashboard
2. Machine learning recommendations
3. Mobile app support
4. External system integrations

### Technical Debt
1. Legacy code refactoring
2. Performance optimization
3. Test coverage improvements
4. Documentation updates

---

*Last updated: February 2025*
*Version: 1.0*