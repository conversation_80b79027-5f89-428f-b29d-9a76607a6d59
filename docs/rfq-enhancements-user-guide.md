# RFQ Detail Page Enhancements - User Guide

## Overview

The RFQ Detail Page has been enhanced with powerful new features to improve your procurement workflow. This guide covers all the new functionality and how to use it effectively.

## New Features

### 1. Enhanced Items Display

**What's New:**
- Items section now shows up to 3 items by default
- "Show All Items" button appears when there are more than 3 items
- Detailed modal view with search and filtering capabilities

**How to Use:**
1. Navigate to any RFQ detail page
2. In the Overview tab, look for the Items section
3. If there are more than 3 items, click "Show All Items" to see the complete list
4. In the modal, you can:
   - Search items by name or description
   - Filter by category
   - View detailed specifications and attachments
   - See estimated values and quantities

### 2. Complete RFQ Editing

**What's New:**
- Full RFQ editing capabilities after creation
- Item management (add, edit, remove items)
- Partial selection configuration
- Warning system for RFQs with active submissions

**How to Use:**
1. From the RFQ detail page, click the "Edit RFQ" button in the actions menu
2. Modify any RFQ details:
   - Basic information (title, description, due date)
   - Add, edit, or remove items
   - Configure partial selection options
   - Update terms and conditions
3. Click "Save Changes" to apply your modifications
4. If there are active submissions, you'll see a warning about potential impacts

### 3. Advanced Invitation Management

**What's New:**
- Resend invitations to vendors
- Send custom reminder messages
- Add new vendors to existing RFQs
- Enhanced engagement tracking

**How to Use:**

#### Resending Invitations:
1. Go to the Invitations tab
2. Find the vendor invitation you want to resend
3. Click the actions menu (⋯) next to the vendor
4. Select "Resend Invitation"

#### Sending Reminders:
1. In the Invitations tab, click the actions menu for any vendor
2. Select "Send Reminder"
3. The system will send a reminder email to the vendor

#### Adding New Vendors:
1. In the Invitations tab, click "Add Vendors"
2. Search and select vendors from the dialog
3. Use filters to find vendors by category or performance score
4. Click "Add X Vendors" to send invitations

### 4. Advanced Quote Generation

**What's New:**
- Partial selection support (select individual items from different vendors)
- Commission management with global and item-level rates
- Account and opportunity integration
- Real-time pricing calculations

**How to Use:**

#### Generating Advanced Quotes:
1. Go to the Quotes tab
2. Click "Advanced Quote" (instead of "Basic Quote")
3. Follow the 3-step wizard:

**Step 1: Select Items**
- View all vendor submissions
- Select entire submissions or individual items
- Look for "Partial OK" badges to identify vendors that allow partial selection
- See real-time selection summary

**Step 2: Configure Quote**
- Enter client information
- Search and select account/opportunity (optional)
- Set global commission rate
- Override commission rates for specific items
- Add terms and conditions
- View real-time pricing preview

**Step 3: Review & Generate**
- Review all selections and pricing
- Confirm quote details
- Click "Generate Quote" to create the final quote

#### Understanding Partial Selection:
- Vendors with "Partial OK" badges allow individual item purchases
- Vendors with "Whole Only" badges require you to select their entire submission
- You can mix and match items from different vendors in a single quote

### 5. Partial Selection Configuration

**What's New:**
- Configure RFQs to allow vendors to opt-in for partial selection
- Customizable vendor confirmation messages
- Flexible configuration options

**How to Use:**

#### When Creating RFQs:
1. In the RFQ creation form, look for the "Partial Selection Configuration" step
2. Toggle "Allow Partial Selection" to enable the feature
3. Configure options:
   - Require vendor confirmation
   - Customize the confirmation message
   - Set default behavior
   - Add instructions for vendors

#### When Editing RFQs:
1. In the RFQ edit form, scroll to the "Partial Selection Configuration" section
2. Enable or disable the feature
3. Modify configuration as needed

### 6. Account and Opportunity Integration

**What's New:**
- Link quotes to specific accounts and opportunities
- Searchable account selection
- Filtered opportunity selection based on account
- Enhanced quote tracking and reporting

**How to Use:**
1. When generating an advanced quote, use the account/opportunity selector
2. Search for accounts by name
3. Select an account to see related opportunities
4. Choose an opportunity to link to the quote
5. This information will be included in the quote and used for tracking

## Tips and Best Practices

### For Procurement Managers:

1. **Use Partial Selection Strategically:**
   - Enable partial selection when you want flexibility in vendor selection
   - Consider vendor capabilities when deciding on partial selection
   - Use custom messages to clarify your requirements

2. **Leverage Advanced Quote Generation:**
   - Compare items across vendors before making selections
   - Use commission overrides for strategic pricing
   - Link quotes to opportunities for better pipeline tracking

3. **Manage Invitations Effectively:**
   - Send reminders to non-responsive vendors
   - Add high-performing vendors to existing RFQs when needed
   - Monitor engagement metrics to improve vendor relationships

### For Vendors (Public Submission Form):

1. **Understand Partial Selection:**
   - Read the partial selection question carefully
   - Consider your minimum order quantities and pricing structure
   - Provide clear notes about any conditions for partial orders

2. **Provide Complete Information:**
   - Fill out all required fields
   - Upload supporting documents
   - Include detailed specifications and alternatives

## Troubleshooting

### Common Issues:

**Q: I can't see the "Show All Items" button**
A: This button only appears when there are more than 3 items in the RFQ.

**Q: Why can't I select individual items from a vendor?**
A: The vendor has not opted-in for partial selection. Look for the "Whole Only" badge.

**Q: I'm getting a warning when editing an RFQ**
A: This appears when the RFQ has active vendor submissions. Changes may affect existing bids.

**Q: The account search isn't showing results**
A: Make sure you're typing at least 2 characters and that you have access to the accounts.

**Q: Commission calculations seem incorrect**
A: Check both global commission rates and any item-level overrides you've set.

### Getting Help:

If you encounter issues or need assistance:
1. Check this user guide for common solutions
2. Contact your system administrator
3. Submit a support ticket through the help desk

## Feature Availability

These enhancements are available to users with the following permissions:
- **Managers and Admins**: Full access to all features
- **Viewers**: Can view enhanced displays but cannot edit or create

## What's Next

Future enhancements may include:
- Automated vendor recommendations based on performance
- Advanced analytics and reporting
- Integration with external procurement systems
- Mobile app support for vendor submissions

---

*Last updated: February 2025*
*Version: 1.0*