import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Provider } from 'react-redux';
import { store } from './store';

// Components
import { ProtectedRoute } from './components/ProtectedRoute';
import { AuthRedirect } from './components/auth/AuthRedirect';
import { AuthProvider } from './components/AuthProvider';
import { AuthGuard } from './components/AuthGuard';
import { AppLayout } from './components/Layout/AppLayout';

// Pages
import { Login } from './pages/Login';
import { Register } from './pages/Register';
import { ForgotPassword } from './pages/ForgotPassword';
import { ResetPassword } from './pages/ResetPassword';
import { VerifyEmail } from './pages/VerifyEmail';
import { VerificationPending } from './pages/VerificationPending';
import { Dashboard } from './pages/Dashboard';
import { VendorsList } from './pages/VendorsList';
import { VendorCreate } from './pages/VendorCreate';
import { VendorView } from './pages/VendorView';
import { VendorEdit } from './pages/VendorEdit';
import { AccountsList } from './pages/AccountsList';
import { AccountCreate } from './pages/AccountCreate';
import { AccountEdit } from './pages/AccountEdit';
import { AccountView } from './pages/AccountView';
import { ContactsList } from './pages/ContactsList';
import { ContactCreate } from './pages/ContactCreate';
import { ContactEdit } from './pages/ContactEdit';
import { ContactView } from './pages/ContactView';
import { OpportunityManagement } from './pages/opportunities/OpportunityManagement';
import OpportunityCreate from './pages/opportunities/OpportunityCreate';
import OpportunityView from './pages/opportunities/OpportunityView';
import NotFound from "./pages/NotFound";
import ContractsList from './pages/ContractsList';
import ContractCreate from './pages/ContractCreate';
import ContractView from './pages/ContractView';
import ContractEdit from './pages/ContractEdit';
import AnalyticsDashboard from './pages/AnalyticsDashboard';
import CustomReports from './pages/CustomReports';
import AIInsights from './pages/AIInsights';
import Settings from './pages/Settings';
import InvoicesList from './pages/InvoicesList';
import InvoiceCreate from './pages/InvoiceCreate';
import InvoiceView from './pages/InvoiceView';
import InvoiceEdit from './pages/InvoiceEdit';
import InvoiceSettings from './pages/InvoiceSettings';

import { AdminUsers } from './pages/admin/AdminUsers';
import { AdminSettings } from './pages/admin/AdminSettings';
import { AISettings } from './pages/admin/AISettings';
import { ProfileSettings } from './pages/ProfileSettings';
import WorkflowsList from './pages/WorkflowsList';
import WorkflowCreate from './pages/WorkflowCreate';
import { ProtectedAdminRoute } from './components/ProtectedAdminRoute';
import CollaborationDemo from './pages/CollaborationDemo';
import PublicRFQSubmission from './pages/PublicRFQSubmission';
import RFQCreate from './pages/RFQCreate';
import RFQDetail from './pages/RFQDetail';
import RFQEdit from './pages/RFQEdit';
import RFQList from './pages/RFQList';

const queryClient = new QueryClient();

const App = () => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AuthProvider>
            <AuthGuard>
              <Routes>
                {/* Public Routes */}
                <Route path="/login" element={
                  <AuthRedirect>
                    <Login />
                  </AuthRedirect>
                } />
                <Route path="/register" element={
                  <AuthRedirect>
                    <Register />
                  </AuthRedirect>
                } />
                <Route path="/forgot-password" element={
                  <AuthRedirect>
                    <ForgotPassword />
                  </AuthRedirect>
                } />
                <Route path="/reset-password" element={<ResetPassword />} />
                <Route path="/verify-email" element={<VerifyEmail />} />
                <Route path="/verification-pending" element={
                  <AuthRedirect>
                    <VerificationPending />
                  </AuthRedirect>
                } />
                
                {/* Public RFQ Submission Route */}
                <Route path="/rfq/submit/:token" element={<PublicRFQSubmission />} />
                
                {/* Protected Routes */}
                <Route path="/" element={<AppLayout />}>
              <Route index element={<Dashboard />} />
                  <Route path="dashboard" element={<Dashboard />} />
              
              {/* Account Routes */}
              <Route path="accounts">
                <Route path="list" element={<AccountsList />} />
                <Route path="create" element={
                  <ProtectedRoute requiredRole="manager">
                    <AccountCreate />
                  </ProtectedRoute>
                } />
                <Route path=":id" element={<AccountView />} />
                <Route path=":id/edit" element={
                  <ProtectedRoute requiredRole="manager">
                    <AccountEdit />
                  </ProtectedRoute>
                } />
                <Route index element={<Navigate to="/accounts/list" replace />} />
              </Route>
              
              {/* Contact Routes */}
              <Route path="contacts">
                <Route path="list" element={<ContactsList />} />
                <Route path="create" element={
                  <ProtectedRoute requiredRole="manager">
                    <ContactCreate />
                  </ProtectedRoute>
                } />
                <Route path=":id" element={<ContactView />} />
                <Route path=":id/edit" element={
                  <ProtectedRoute requiredRole="manager">
                    <ContactEdit />
                  </ProtectedRoute>
                } />
                <Route index element={<Navigate to="/contacts/list" replace />} />
              </Route>
              
              {/* Opportunity Routes */}
              <Route path="opportunities">
                <Route path="list" element={<OpportunityManagement />} />
                <Route path="create" element={<OpportunityCreate />} />
                <Route path=":id" element={<OpportunityView />} />
                <Route index element={<Navigate to="/opportunities/list" replace />} />
              </Route>
              
              {/* Vendor Routes */}
              <Route path="vendors">
                <Route path="list" element={<VendorsList />} />
                <Route path="create" element={
                  <ProtectedRoute requiredRole="manager">
                    <VendorCreate />
                  </ProtectedRoute>
                } />
                <Route path=":id" element={<VendorView />} />
                <Route path=":id/edit" element={
                  <ProtectedRoute requiredRole="manager">
                    <VendorEdit />
                  </ProtectedRoute>
                } />
                <Route index element={<Navigate to="/vendors/list" replace />} />
              </Route>
              
              {/* Contract Routes */}
              <Route path="contracts">
                <Route path="list" element={<ContractsList />} />
                <Route path="create" element={
                  <ProtectedRoute requiredRole="manager">
                    <ContractCreate />
                  </ProtectedRoute>
                } />
                <Route path=":id" element={<ContractView />} />
                <Route path=":id/edit" element={
                  <ProtectedRoute requiredRole="manager">
                    <ContractEdit />
                  </ProtectedRoute>
                } />
                <Route index element={<Navigate to="/contracts/list" replace />} />
              </Route>
              
              {/* Invoice Routes */}
              <Route path="invoices">
                <Route path="list" element={<InvoicesList />} />
                <Route path="create" element={
                  <ProtectedRoute requiredRole="manager">
                    <InvoiceCreate />
                  </ProtectedRoute>
                } />
                <Route path="generate" element={<Navigate to="/invoices/create" replace />} />
                <Route path="settings" element={
                  <ProtectedRoute requiredRole="admin">
                    <InvoiceSettings />
                  </ProtectedRoute>
                } />
                <Route path=":id" element={<InvoiceView />} />
                <Route path=":id/edit" element={
                  <ProtectedRoute requiredRole="manager">
                    <InvoiceEdit />
                  </ProtectedRoute>
                } />
                <Route index element={<Navigate to="/invoices/list" replace />} />
              </Route>
              
              {/* RFQ Routes */}
              <Route path="rfqs">
                <Route path="list" element={<RFQList />} />
                <Route path="create" element={
                  <ProtectedRoute requiredRole="manager">
                    <RFQCreate />
                  </ProtectedRoute>
                } />
                <Route path=":id" element={<RFQDetail />} />
                <Route path=":id/edit" element={
                  <ProtectedRoute requiredRole="manager">
                    <RFQEdit />
                  </ProtectedRoute>
                } />
                <Route index element={<Navigate to="/rfqs/list" replace />} />
              </Route>
              
              {/* Performance Routes */}
              <Route path="performance">
                <Route path="scorecards" element={
                  <div className="p-8 text-center">Performance Scorecards (Coming Soon)</div>
                } />
                <Route path="risks" element={
                  <div className="p-8 text-center">Risk Assessment (Coming Soon)</div>
                } />
                <Route index element={<Navigate to="/performance/scorecards" replace />} />
              </Route>
              
              {/* Analytics Routes */}
              <Route path="analytics">
                {/* <Route path="dashboard" element={
                  <div className="p-8 text-center">Analytics Dashboard (Coming Soon)</div>
                } /> */}
                <Route path="dashboard" element={
                  <ProtectedRoute requiredRole="manager">
                    <AnalyticsDashboard />
                  </ProtectedRoute>
                } />
                <Route path="reports" element={
                  <ProtectedRoute requiredRole="manager">
                    <CustomReports />
                  </ProtectedRoute>
                } />
                <Route path="ai-insights" element={
                  <ProtectedRoute requiredRole="manager">
                    <AIInsights />
                  </ProtectedRoute>
                } />
                
                <Route index element={<Navigate to="/analytics/dashboard" replace />} />
              </Route>
              
              {/* Workflow Routes */}
              <Route path="workflows">
                <Route path="list" element={
                  <ProtectedRoute requiredRole="manager">
                    <WorkflowsList />
                  </ProtectedRoute>
                } />
                <Route path="create" element={
                  <ProtectedRoute requiredRole="admin">
                    <WorkflowCreate />
                  </ProtectedRoute>
                } />
                {/* <Route index element={<Navigate to="/workflows/list" replace />} /> */}
                <Route index element={<Navigate to="/workflows/list" replace />} />
              </Route>
              
              {/* Admin Routes */}
              <Route path="admin">
                <Route path="users" element={
                  <ProtectedAdminRoute>
                    <AdminUsers />
                  </ProtectedAdminRoute>
                } />
                <Route path="settings" element={
                  <ProtectedAdminRoute>
                    <AdminSettings />
                  </ProtectedAdminRoute>
                } />
                <Route path="ai-settings" element={
                  <ProtectedAdminRoute>
                    <AISettings />
                  </ProtectedAdminRoute>
                } />
                <Route path="backup" element={
                  <ProtectedAdminRoute>
                    <div className="p-8 text-center">Backup Management (Coming Soon)</div>
                  </ProtectedAdminRoute>
                } />
                <Route index element={<Navigate to="/admin/users" replace />} />
              </Route>
              
              {/* Settings Routes */}
              <Route path="settings" element={<Settings />} />
              
              {/* Profile Routes */}
              <Route path="profile">
                <Route path="settings" element={<ProfileSettings />} />
                <Route index element={<Navigate to="/profile/settings" replace />} />
              </Route>
              
              {/* Demo Routes */}
              <Route path="demo">
                <Route path="collaboration" element={<CollaborationDemo />} />
                <Route index element={<Navigate to="/demo/collaboration" replace />} />
              </Route>
                </Route>
                
                {/* Fallback Routes */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </AuthGuard>
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  </Provider>
);

export default App;
