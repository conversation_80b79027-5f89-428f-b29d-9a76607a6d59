export interface PasswordStrength {
  score: number;
  level: 'weak' | 'fair' | 'good' | 'strong';
  feedback: string[];
}

export const validatePasswordStrength = (password: string): PasswordStrength => {
  const feedback: string[] = [];
  let score = 0;

  // Length check
  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Password must be at least 8 characters long');
  }

  // Uppercase check
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add at least one uppercase letter');
  }

  // Lowercase check
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add at least one lowercase letter');
  }

  // Number check
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add at least one number');
  }

  // Special character check (bonus)
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 0.5;
  }

  // Determine level
  let level: PasswordStrength['level'];
  if (score <= 1) level = 'weak';
  else if (score <= 2) level = 'fair';
  else if (score <= 3) level = 'good';
  else level = 'strong';

  return {
    score: Math.min(score, 4),
    level,
    feedback,
  };
};

export const getPasswordStrengthColor = (level: PasswordStrength['level']): string => {
  switch (level) {
    case 'weak':
      return 'text-destructive';
    case 'fair':
      return 'text-warning';
    case 'good':
      return 'text-info';
    case 'strong':
      return 'text-success';
    default:
      return 'text-muted-foreground';
  }
};

export const getPasswordStrengthBgColor = (level: PasswordStrength['level']): string => {
  switch (level) {
    case 'weak':
      return 'bg-destructive';
    case 'fair':
      return 'bg-warning';
    case 'good':
      return 'bg-info';
    case 'strong':
      return 'bg-success';
    default:
      return 'bg-muted';
  }
};