// Validation schemas for admin and profile forms

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  min?: number;
  max?: number;
  custom?: (value: any) => string | null;
  message?: string;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// User invitation validation schema
export const userInvitationSchema: ValidationSchema = {
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'Please enter a valid email address',
  },
  role: {
    required: true,
    custom: (value) => {
      if (!['admin', 'manager', 'viewer'].includes(value)) {
        return 'Please select a valid role';
      }
      return null;
    },
  },
};

// User creation validation schema
export const userCreationSchema: ValidationSchema = {
  firstName: {
    required: true,
    minLength: 2,
    maxLength: 50,
    message: 'First name must be between 2 and 50 characters',
  },
  lastName: {
    required: true,
    minLength: 2,
    maxLength: 50,
    message: 'Last name must be between 2 and 50 characters',
  },
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'Please enter a valid email address',
  },
  password: {
    required: true,
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character',
  },
  confirmPassword: {
    required: true,
    message: 'Please confirm your password',
  },
  role: {
    required: true,
    custom: (value) => {
      if (!['admin', 'manager', 'viewer'].includes(value)) {
        return 'Please select a valid role';
      }
      return null;
    },
  },
};

// Password reset validation schema
export const passwordResetSchema: ValidationSchema = {
  password: {
    required: true,
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character',
  },
  confirmPassword: {
    required: true,
    message: 'Please confirm your password',
  },
};

// Password change validation schema
export const passwordChangeSchema: ValidationSchema = {
  currentPassword: {
    required: true,
    message: 'Current password is required',
  },
  newPassword: {
    required: true,
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character',
  },
  confirmPassword: {
    required: true,
    custom: (value, formData) => {
      if (value !== formData?.newPassword) {
        return 'Passwords must match';
      }
      return null;
    },
  },
};

// Profile update validation schema
export const profileUpdateSchema: ValidationSchema = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    message: 'Name must be between 2 and 100 characters',
  },
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'Please enter a valid email address',
  },
  phone: {
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    message: 'Please enter a valid phone number',
  },
  organization: {
    maxLength: 200,
    message: 'Organization name cannot exceed 200 characters',
  },
};

// System settings validation schema
export const systemSettingsSchema: ValidationSchema = {
  organizationName: {
    required: true,
    minLength: 2,
    maxLength: 100,
    message: 'Organization name must be between 2 and 100 characters',
  },
  contactEmail: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'Please enter a valid email address',
  },
  contactPhone: {
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    message: 'Please enter a valid phone number',
  },
  sessionTimeout: {
    required: true,
    min: 5,
    max: 480,
    message: 'Session timeout must be between 5 and 480 minutes',
  },
  maxLoginAttempts: {
    required: true,
    min: 3,
    max: 10,
    message: 'Max login attempts must be between 3 and 10',
  },
  'passwordPolicy.minLength': {
    required: true,
    min: 6,
    max: 32,
    message: 'Password minimum length must be between 6 and 32 characters',
  },
  'passwordPolicy.expiryDays': {
    required: true,
    min: 30,
    max: 365,
    message: 'Password expiry must be between 30 and 365 days',
  },
  'passwordPolicy.preventReuse': {
    required: true,
    min: 0,
    max: 24,
    message: 'Password reuse prevention must be between 0 and 24',
  },
};

// Custom field validation schema
export const customFieldSchema: ValidationSchema = {
  fieldName: {
    required: true,
    pattern: /^[a-z][a-z0-9_]*$/,
    minLength: 2,
    maxLength: 50,
    message: 'Field name must start with a letter, contain only lowercase letters, numbers, and underscores',
  },
  fieldLabel: {
    required: true,
    minLength: 2,
    maxLength: 100,
    message: 'Field label must be between 2 and 100 characters',
  },
  entityType: {
    required: true,
    custom: (value) => {
      if (!['vendor', 'contract', 'invoice'].includes(value)) {
        return 'Please select a valid entity type';
      }
      return null;
    },
  },
  fieldType: {
    required: true,
    custom: (value) => {
      if (!['text', 'number', 'date', 'select', 'boolean', 'textarea'].includes(value)) {
        return 'Please select a valid field type';
      }
      return null;
    },
  },
  helpText: {
    maxLength: 500,
    message: 'Help text cannot exceed 500 characters',
  },
};

// Validation function
export const validateForm = (data: Record<string, any>, schema: ValidationSchema): ValidationResult => {
  const errors: ValidationError[] = [];

  for (const [fieldPath, rule] of Object.entries(schema)) {
    const value = getNestedValue(data, fieldPath);
    const error = validateField(fieldPath, value, rule, data);
    if (error) {
      errors.push(error);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Validate individual field
export const validateField = (
  fieldPath: string,
  value: any,
  rule: ValidationRule,
  formData?: Record<string, any>
): ValidationError | null => {
  const fieldName = fieldPath.split('.').pop() || fieldPath;

  // Required validation
  if (rule.required && (value === undefined || value === null || value === '')) {
    return {
      field: fieldPath,
      message: rule.message || `${fieldName} is required`,
    };
  }

  // Skip other validations if value is empty and not required
  if (!rule.required && (value === undefined || value === null || value === '')) {
    return null;
  }

  // String validations
  if (typeof value === 'string') {
    if (rule.minLength && value.length < rule.minLength) {
      return {
        field: fieldPath,
        message: rule.message || `${fieldName} must be at least ${rule.minLength} characters`,
      };
    }

    if (rule.maxLength && value.length > rule.maxLength) {
      return {
        field: fieldPath,
        message: rule.message || `${fieldName} cannot exceed ${rule.maxLength} characters`,
      };
    }

    if (rule.pattern && !rule.pattern.test(value)) {
      return {
        field: fieldPath,
        message: rule.message || `${fieldName} format is invalid`,
      };
    }
  }

  // Number validations
  if (typeof value === 'number') {
    if (rule.min !== undefined && value < rule.min) {
      return {
        field: fieldPath,
        message: rule.message || `${fieldName} must be at least ${rule.min}`,
      };
    }

    if (rule.max !== undefined && value > rule.max) {
      return {
        field: fieldPath,
        message: rule.message || `${fieldName} cannot exceed ${rule.max}`,
      };
    }
  }

  // Custom validation
  if (rule.custom) {
    const customError = rule.custom(value, formData);
    if (customError) {
      return {
        field: fieldPath,
        message: customError,
      };
    }
  }

  return null;
};

// Helper function to get nested object values
const getNestedValue = (obj: Record<string, any>, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

// Real-time validation hook
export const useFormValidation = (schema: ValidationSchema) => {
  const [errors, setErrors] = React.useState<Record<string, string>>({});

  const validateField = (fieldPath: string, value: any, formData?: Record<string, any>) => {
    const rule = schema[fieldPath];
    if (!rule) return;

    const error = validateField(fieldPath, value, rule, formData);
    setErrors(prev => ({
      ...prev,
      [fieldPath]: error?.message || '',
    }));
  };

  const validateForm = (data: Record<string, any>) => {
    const result = validateForm(data, schema);
    const errorMap: Record<string, string> = {};
    
    result.errors.forEach(error => {
      errorMap[error.field] = error.message;
    });
    
    setErrors(errorMap);
    return result.isValid;
  };

  const clearErrors = () => {
    setErrors({});
  };

  const clearFieldError = (fieldPath: string) => {
    setErrors(prev => ({
      ...prev,
      [fieldPath]: '',
    }));
  };

  return {
    errors,
    validateField,
    validateForm,
    clearErrors,
    clearFieldError,
  };
};

// Password strength checker
export const checkPasswordStrength = (password: string): {
  score: number;
  feedback: string[];
  isStrong: boolean;
} => {
  const feedback: string[] = [];
  let score = 0;

  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Use at least 8 characters');
  }

  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add lowercase letters');
  }

  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add uppercase letters');
  }

  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add numbers');
  }

  if (/[@$!%*?&]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add special characters (@$!%*?&)');
  }

  if (password.length >= 12) {
    score += 1;
  }

  return {
    score,
    feedback,
    isStrong: score >= 4,
  };
};

// Email validation helper
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Phone validation helper
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone);
};

// RFQ validation schema
export const rfqValidationSchema: ValidationSchema = {
  title: {
    required: true,
    minLength: 3,
    maxLength: 255,
    message: 'RFQ title must be between 3 and 255 characters',
  },
  description: {
    required: true,
    minLength: 10,
    maxLength: 5000,
    message: 'Description must be between 10 and 5000 characters',
  },
  dueDate: {
    required: true,
    custom: (value) => {
      if (!value || new Date(value) <= new Date()) {
        return 'Due date must be in the future';
      }
      return null;
    },
  },
  items: {
    required: true,
    custom: (value) => {
      if (!Array.isArray(value) || value.length === 0) {
        return 'At least one item is required';
      }
      
      for (let i = 0; i < value.length; i++) {
        const item = value[i];
        if (!item.name || item.name.trim() === '') {
          return `Item ${i + 1} name is required`;
        }
        if (!item.quantity || item.quantity < 1) {
          return `Item ${i + 1} quantity must be at least 1`;
        }
        if (!item.category || item.category.trim() === '') {
          return `Item ${i + 1} category is required`;
        }
      }
      return null;
    },
  },
  selectedVendors: {
    required: true,
    custom: (value) => {
      if (!Array.isArray(value) || value.length === 0) {
        return 'At least one vendor must be selected';
      }
      if (value.length > 50) {
        return 'Cannot select more than 50 vendors';
      }
      return null;
    },
  },
  formConfig: {
    required: true,
    custom: (value) => {
      if (!Array.isArray(value) || value.length === 0) {
        return 'At least one form field is required';
      }
      
      for (let i = 0; i < value.length; i++) {
        const field = value[i];
        if (!field.label || field.label.trim() === '') {
          return `Form field ${i + 1} label is required`;
        }
        if (!field.type) {
          return `Form field ${i + 1} type is required`;
        }
        if (field.type === 'select' && (!field.options || field.options.length === 0)) {
          return `Form field ${i + 1} must have at least one option`;
        }
      }
      return null;
    },
  },
  terms: {
    maxLength: 2000,
    message: 'Terms and conditions cannot exceed 2000 characters',
  },
};

// File validation helpers
export const validateImageFile = (file: File): { isValid: boolean; error?: string } => {
  const maxSize = 2 * 1024 * 1024; // 2MB
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Please upload a valid image file (JPEG, PNG, GIF, or WebP)',
    };
  }

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'Image size must be less than 2MB',
    };
  }

  return { isValid: true };
};