// Secure file upload utilities

export interface FileUploadOptions {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  allowedExtensions?: string[];
  maxFiles?: number;
}

export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  file?: File;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
  fileName?: string;
}

// Default configurations for different file types
export const FILE_CONFIGS = {
  avatar: {
    maxSize: 2 * 1024 * 1024, // 2MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    maxFiles: 1,
  },
  organizationLogo: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/svg+xml'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.svg'],
    maxFiles: 1,
  },
  document: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
    ],
    allowedExtensions: ['.pdf', '.doc', '.docx', '.txt'],
    maxFiles: 5,
  },
} as const;

// Validate file against security rules
export const validateFile = (
  file: File,
  options: FileUploadOptions = {}
): FileValidationResult => {
  const errors: string[] = [];
  
  const {
    maxSize = 5 * 1024 * 1024, // 5MB default
    allowedTypes = [],
    allowedExtensions = [],
  } = options;

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size must be less than ${formatFileSize(maxSize)}`);
  }

  // Check file type
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }

  // Check file extension
  if (allowedExtensions.length > 0) {
    const fileExtension = getFileExtension(file.name);
    if (!allowedExtensions.includes(fileExtension)) {
      errors.push(`File extension ${fileExtension} is not allowed`);
    }
  }

  // Check for potentially dangerous file names
  if (isDangerousFileName(file.name)) {
    errors.push('File name contains potentially dangerous characters');
  }

  // Additional security checks
  if (file.name.length > 255) {
    errors.push('File name is too long');
  }

  if (file.size === 0) {
    errors.push('File is empty');
  }

  return {
    isValid: errors.length === 0,
    errors,
    file: errors.length === 0 ? file : undefined,
  };
};

// Validate multiple files
export const validateFiles = (
  files: FileList | File[],
  options: FileUploadOptions = {}
): FileValidationResult[] => {
  const fileArray = Array.from(files);
  const { maxFiles = 10 } = options;

  if (fileArray.length > maxFiles) {
    return [{
      isValid: false,
      errors: [`Maximum ${maxFiles} files allowed`],
    }];
  }

  return fileArray.map(file => validateFile(file, options));
};

// Secure file upload function
export const uploadFile = async (
  file: File,
  uploadType: keyof typeof FILE_CONFIGS,
  onProgress?: (progress: number) => void
): Promise<UploadResult> => {
  const config = FILE_CONFIGS[uploadType];
  const validation = validateFile(file, config);

  if (!validation.isValid) {
    return {
      success: false,
      error: validation.errors.join(', '),
    };
  }

  try {
    // Sanitize file name
    const sanitizedFileName = sanitizeFileName(file.name);
    
    // Create form data
    const formData = new FormData();
    formData.append('file', file, sanitizedFileName);
    formData.append('type', uploadType);
    formData.append('timestamp', Date.now().toString());

    // In a real application, you would upload to your server
    // For demo purposes, we'll simulate the upload and return a data URL
    return await simulateUpload(file, onProgress);

    // Real implementation would look like:
    // const response = await fetch('/api/upload', {
    //   method: 'POST',
    //   body: formData,
    //   headers: {
    //     'Authorization': `Bearer ${getAuthToken()}`,
    //   },
    // });
    
    // if (!response.ok) {
    //   throw new Error(`Upload failed: ${response.statusText}`);
    // }
    
    // const result = await response.json();
    // return {
    //   success: true,
    //   url: result.url,
    //   fileName: sanitizedFileName,
    // };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed',
    };
  }
};

// Simulate file upload for demo
const simulateUpload = (
  file: File,
  onProgress?: (progress: number) => void
): Promise<UploadResult> => {
  return new Promise((resolve) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      onProgress?.(progress);
      
      if (progress >= 100) {
        clearInterval(interval);
        
        // Create data URL for demo
        const reader = new FileReader();
        reader.onload = (e) => {
          resolve({
            success: true,
            url: e.target?.result as string,
            fileName: file.name,
          });
        };
        reader.readAsDataURL(file);
      }
    }, 100);
  });
};

// Utility functions
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const getFileExtension = (fileName: string): string => {
  return fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
};

export const sanitizeFileName = (fileName: string): string => {
  // Remove or replace dangerous characters
  return fileName
    .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
    .substring(0, 100); // Limit length
};

export const isDangerousFileName = (fileName: string): boolean => {
  const dangerousPatterns = [
    /\.\./,           // Directory traversal
    /[<>:"|?*]/,      // Windows reserved characters
    /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i, // Windows reserved names
    /^\./,            // Hidden files
    /\.(exe|bat|cmd|scr|pif|com|vbs|js|jar|app)$/i, // Executable extensions
  ];
  
  return dangerousPatterns.some(pattern => pattern.test(fileName));
};

// File preview utilities
export const createFilePreview = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith('image/')) {
      reject(new Error('File is not an image'));
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target?.result as string);
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
};

// Compress image before upload
export const compressImage = (
  file: File,
  maxWidth: number = 800,
  maxHeight: number = 600,
  quality: number = 0.8
): Promise<File> => {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith('image/')) {
      resolve(file);
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

// React hook for file upload
export const useFileUpload = (uploadType: keyof typeof FILE_CONFIGS) => {
  const [uploading, setUploading] = React.useState(false);
  const [progress, setProgress] = React.useState(0);
  const [error, setError] = React.useState<string | null>(null);

  const upload = async (file: File): Promise<UploadResult> => {
    setUploading(true);
    setProgress(0);
    setError(null);

    try {
      const result = await uploadFile(file, uploadType, setProgress);
      
      if (!result.success) {
        setError(result.error || 'Upload failed');
      }
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  const reset = () => {
    setUploading(false);
    setProgress(0);
    setError(null);
  };

  return {
    upload,
    uploading,
    progress,
    error,
    reset,
  };
};