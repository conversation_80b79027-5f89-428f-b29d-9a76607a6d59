// Audit logging utilities for tracking admin actions

export interface AuditLogEntry {
  id: string;
  userId: string;
  userName: string;
  action: string;
  entityType: string;
  entityId: string;
  changes: Record<string, { old: any; new: any }>;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export type AuditAction = 
  | 'USER_CREATED'
  | 'USER_UPDATED' 
  | 'USER_DELETED'
  | 'USER_ROLE_CHANGED'
  | 'USER_STATUS_CHANGED'
  | 'USER_INVITED'
  | 'USER_LOGIN'
  | 'USER_LOGOUT'
  | 'PASSWORD_CHANGED'
  | 'PROFILE_UPDATED'
  | 'SYSTEM_SETTINGS_UPDATED'
  | 'CUSTOM_FIELD_CREATED'
  | 'CUSTOM_FIELD_UPDATED'
  | 'CUSTOM_FIELD_DELETED'
  | 'BULK_USER_UPDATE'
  | 'SECURITY_SETTINGS_CHANGED'
  | 'NOTIFICATION_SETTINGS_CHANGED';

class AuditLogger {
  private logs: AuditLogEntry[] = [];
  private maxLogs = 1000; // Keep last 1000 logs in memory

  // Get client information
  private getClientInfo() {
    return {
      ipAddress: '127.0.0.1', // In real app, get from request
      userAgent: navigator.userAgent,
      sessionId: sessionStorage.getItem('session_id') || 'unknown',
    };
  }

  // Log an audit event
  log(
    action: AuditAction,
    entityType: string,
    entityId: string,
    changes: Record<string, { old: any; new: any }> = {},
    metadata: Record<string, any> = {}
  ) {
    // Get current user from localStorage or session
    const currentUser = this.getCurrentUser();
    if (!currentUser) {
      console.warn('Audit log attempted without authenticated user');
      return;
    }

    const clientInfo = this.getClientInfo();
    
    const logEntry: AuditLogEntry = {
      id: this.generateId(),
      userId: currentUser.id,
      userName: currentUser.name,
      action,
      entityType,
      entityId,
      changes,
      timestamp: new Date().toISOString(),
      ...clientInfo,
      metadata,
    };

    // Add to in-memory logs
    this.logs.unshift(logEntry);
    
    // Keep only the most recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // In production, send to backend
    this.sendToBackend(logEntry);

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Audit Log:', logEntry);
    }
  }

  // Specific logging methods for common actions
  logUserInvitation(email: string, role: string, invitedBy: string) {
    this.log('USER_INVITED', 'user', email, {
      email: { old: null, new: email },
      role: { old: null, new: role },
      invitedBy: { old: null, new: invitedBy },
    });
  }

  logUserRoleChange(userId: string, oldRole: string, newRole: string) {
    this.log('USER_ROLE_CHANGED', 'user', userId, {
      role: { old: oldRole, new: newRole },
    });
  }

  logUserStatusChange(userId: string, oldStatus: string, newStatus: string) {
    this.log('USER_STATUS_CHANGED', 'user', userId, {
      status: { old: oldStatus, new: newStatus },
    });
  }

  logProfileUpdate(userId: string, changes: Record<string, { old: any; new: any }>) {
    this.log('PROFILE_UPDATED', 'user', userId, changes);
  }

  logPasswordChange(userId: string) {
    this.log('PASSWORD_CHANGED', 'user', userId, {
      password: { old: '[REDACTED]', new: '[REDACTED]' },
    });
  }

  logSystemSettingsUpdate(changes: Record<string, { old: any; new: any }>) {
    this.log('SYSTEM_SETTINGS_UPDATED', 'settings', 'system', changes);
  }

  logCustomFieldCreated(fieldId: string, fieldData: any) {
    this.log('CUSTOM_FIELD_CREATED', 'custom_field', fieldId, {
      field: { old: null, new: fieldData },
    });
  }

  logCustomFieldUpdated(fieldId: string, changes: Record<string, { old: any; new: any }>) {
    this.log('CUSTOM_FIELD_UPDATED', 'custom_field', fieldId, changes);
  }

  logCustomFieldDeleted(fieldId: string, fieldData: any) {
    this.log('CUSTOM_FIELD_DELETED', 'custom_field', fieldId, {
      field: { old: fieldData, new: null },
    });
  }

  logBulkUserUpdate(userIds: string[], action: string, changes: any) {
    this.log('BULK_USER_UPDATE', 'user', 'bulk', {
      action: { old: null, new: action },
      userIds: { old: null, new: userIds },
      changes: { old: null, new: changes },
    }, {
      affectedUsers: userIds.length,
    });
  }

  // Get logs with filtering
  getLogs(filters: {
    startDate?: string;
    endDate?: string;
    userId?: string;
    action?: AuditAction;
    entityType?: string;
    limit?: number;
  } = {}) {
    let filteredLogs = [...this.logs];

    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startDate);
    }

    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endDate);
    }

    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }

    if (filters.action) {
      filteredLogs = filteredLogs.filter(log => log.action === filters.action);
    }

    if (filters.entityType) {
      filteredLogs = filteredLogs.filter(log => log.entityType === filters.entityType);
    }

    if (filters.limit) {
      filteredLogs = filteredLogs.slice(0, filters.limit);
    }

    return filteredLogs;
  }

  // Export logs for compliance
  exportLogs(format: 'json' | 'csv' = 'json', filters: any = {}) {
    const logs = this.getLogs(filters);
    
    if (format === 'csv') {
      return this.convertToCSV(logs);
    }
    
    return JSON.stringify(logs, null, 2);
  }

  // Clear logs (admin only)
  clearLogs() {
    const currentUser = this.getCurrentUser();
    if (currentUser?.role !== 'admin') {
      throw new Error('Only admins can clear audit logs');
    }
    
    this.log('AUDIT_LOGS_CLEARED', 'system', 'audit', {
      clearedCount: { old: this.logs.length, new: 0 },
    });
    
    this.logs = [];
  }

  // Private helper methods
  private getCurrentUser() {
    try {
      const token = localStorage.getItem('vms_token');
      if (!token) return null;
      
      // In real app, decode JWT token or get from Redux store
      // For now, return mock user based on token
      return {
        id: '1',
        name: 'System Administrator',
        role: 'admin' as const,
      };
    } catch {
      return null;
    }
  }

  private generateId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private async sendToBackend(logEntry: AuditLogEntry) {
    try {
      // In production, send to your audit logging service
      // await fetch('/api/audit-logs', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(logEntry),
      // });
      
      // For now, just store in localStorage for demo
      const existingLogs = JSON.parse(localStorage.getItem('audit_logs') || '[]');
      existingLogs.unshift(logEntry);
      
      // Keep only last 100 logs in localStorage
      const trimmedLogs = existingLogs.slice(0, 100);
      localStorage.setItem('audit_logs', JSON.stringify(trimmedLogs));
    } catch (error) {
      console.error('Failed to send audit log to backend:', error);
    }
  }

  private convertToCSV(logs: AuditLogEntry[]): string {
    if (logs.length === 0) return '';

    const headers = [
      'Timestamp',
      'User',
      'Action',
      'Entity Type',
      'Entity ID',
      'Changes',
      'IP Address',
      'User Agent',
    ];

    const rows = logs.map(log => [
      log.timestamp,
      log.userName,
      log.action,
      log.entityType,
      log.entityId,
      JSON.stringify(log.changes),
      log.ipAddress,
      log.userAgent,
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    return csvContent;
  }
}

// Create singleton instance
export const auditLogger = new AuditLogger();

// React hook for using audit logger
export const useAuditLogger = () => {
  return {
    log: auditLogger.log.bind(auditLogger),
    logUserInvitation: auditLogger.logUserInvitation.bind(auditLogger),
    logUserRoleChange: auditLogger.logUserRoleChange.bind(auditLogger),
    logUserStatusChange: auditLogger.logUserStatusChange.bind(auditLogger),
    logProfileUpdate: auditLogger.logProfileUpdate.bind(auditLogger),
    logPasswordChange: auditLogger.logPasswordChange.bind(auditLogger),
    logSystemSettingsUpdate: auditLogger.logSystemSettingsUpdate.bind(auditLogger),
    logCustomFieldCreated: auditLogger.logCustomFieldCreated.bind(auditLogger),
    logCustomFieldUpdated: auditLogger.logCustomFieldUpdated.bind(auditLogger),
    logCustomFieldDeleted: auditLogger.logCustomFieldDeleted.bind(auditLogger),
    logBulkUserUpdate: auditLogger.logBulkUserUpdate.bind(auditLogger),
    getLogs: auditLogger.getLogs.bind(auditLogger),
    exportLogs: auditLogger.exportLogs.bind(auditLogger),
  };
};