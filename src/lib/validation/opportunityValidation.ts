import * as yup from 'yup';
import {
  OpportunityStage,
  OpportunityType,
  OpportunityLeadSource,
  ForecastCategory,
  OpportunityStatus,
  OPPORTUNITY_STAGES,
  OPPORTUNITY_TYPES,
  OPPORTUNITY_LEAD_SOURCES,
  FORECAST_CATEGORIES,
  OPPORTUNITY_STATUSES,
  CONTACT_ROLES
} from '../../types/opportunity';

// Contact role schema
const contactRoleSchema = yup.object({
  contact_id: yup.number().required('Contact is required'),
  role: yup.string().oneOf(CONTACT_ROLES, 'Invalid contact role').required('Role is required'),
  is_primary: yup.boolean().default(false)
});

// Line item schema
const lineItemSchema = yup.object({
  product_name: yup.string().required('Product name is required').max(255, 'Product name must be less than 255 characters'),
  description: yup.string().max(1000, 'Description must be less than 1000 characters'),
  quantity: yup.number().positive('Quantity must be positive').required('Quantity is required'),
  unit_price: yup.number().min(0, 'Unit price must be non-negative').required('Unit price is required'),
  total_price: yup.number().min(0, 'Total price must be non-negative').required('Total price is required'),
  discount_percent: yup.number().min(0, 'Discount must be non-negative').max(100, 'Discount cannot exceed 100%')
});

// Create opportunity schema
export const opportunityCreateSchema = yup.object({
  account_id: yup.number().required('Account is required'),
  name: yup.string().required('Opportunity name is required').max(255, 'Name must be less than 255 characters'),
  stage: yup.string().oneOf(OPPORTUNITY_STAGES, 'Invalid opportunity stage').required('Stage is required'),
  amount: yup.number().min(0, 'Amount must be non-negative'),
  probability: yup.number().min(0, 'Probability must be between 0 and 100').max(100, 'Probability must be between 0 and 100'),
  close_date: yup.date().min(new Date(), 'Close date must be in the future'),
  type: yup.string().oneOf(OPPORTUNITY_TYPES, 'Invalid opportunity type'),
  lead_source: yup.string().oneOf(OPPORTUNITY_LEAD_SOURCES, 'Invalid lead source'),
  next_step: yup.string().max(500, 'Next step must be less than 500 characters'),
  description: yup.string().max(2000, 'Description must be less than 2000 characters'),
  forecast_category: yup.string().oneOf(FORECAST_CATEGORIES, 'Invalid forecast category'),
  campaign_id: yup.number(),
  owner_id: yup.number(),
  contact_roles: yup.array().of(contactRoleSchema),
  line_items: yup.array().of(lineItemSchema),
  custom_fields: yup.object()
});

// Update opportunity schema (all fields optional except id)
export const opportunityUpdateSchema = yup.object({
  id: yup.number(),
  account_id: yup.number(),
  name: yup.string().max(255, 'Name must be less than 255 characters'),
  stage: yup.string().oneOf(OPPORTUNITY_STAGES, 'Invalid opportunity stage'),
  amount: yup.number().min(0, 'Amount must be non-negative'),
  probability: yup.number().min(0, 'Probability must be between 0 and 100').max(100, 'Probability must be between 0 and 100'),
  close_date: yup.date(),
  type: yup.string().oneOf(OPPORTUNITY_TYPES, 'Invalid opportunity type'),
  lead_source: yup.string().oneOf(OPPORTUNITY_LEAD_SOURCES, 'Invalid lead source'),
  next_step: yup.string().max(500, 'Next step must be less than 500 characters'),
  description: yup.string().max(2000, 'Description must be less than 2000 characters'),
  forecast_category: yup.string().oneOf(FORECAST_CATEGORIES, 'Invalid forecast category'),
  campaign_id: yup.number(),
  owner_id: yup.number(),
  contact_roles: yup.array().of(contactRoleSchema),
  line_items: yup.array().of(lineItemSchema),
  custom_fields: yup.object()
});

// Search filters schema
export const opportunitySearchFiltersSchema = yup.object({
  search: yup.string(),
  account_id: yup.number(),
  stage: yup.array().of(yup.string().oneOf(OPPORTUNITY_STAGES)),
  type: yup.array().of(yup.string().oneOf(OPPORTUNITY_TYPES)),
  lead_source: yup.array().of(yup.string().oneOf(OPPORTUNITY_LEAD_SOURCES)),
  forecast_category: yup.array().of(yup.string().oneOf(FORECAST_CATEGORIES)),
  status: yup.array().of(yup.string().oneOf(OPPORTUNITY_STATUSES)),
  owner_id: yup.number(),
  amount_min: yup.number().min(0),
  amount_max: yup.number().min(0),
  probability_min: yup.number().min(0).max(100),
  probability_max: yup.number().min(0).max(100),
  close_date_after: yup.date(),
  close_date_before: yup.date(),
  created_after: yup.date(),
  created_before: yup.date()
});

// Stage advance schema
export const stageAdvanceSchema = yup.object({
  opportunity_id: yup.number().required('Opportunity ID is required'),
  new_stage: yup.string().oneOf(OPPORTUNITY_STAGES, 'Invalid opportunity stage').required('New stage is required'),
  notes: yup.string().max(1000, 'Notes must be less than 1000 characters')
});

// Custom validation functions
export const validateOpportunityAmount = (amount?: number, lineItems?: { total_price?: number }[]) => {
  if (!amount && (!lineItems || lineItems.length === 0)) {
    return 'Either amount or line items must be provided';
  }
  
  if (amount && lineItems && lineItems.length > 0) {
    const totalLineItems = lineItems.reduce((sum, item) => sum + (item.total_price || 0), 0);
    if (Math.abs(amount - totalLineItems) > 0.01) {
      return 'Amount must match the sum of line items';
    }
  }
  
  return null;
};

export const validateCloseDate = (closeDate?: Date, stage?: OpportunityStage) => {
  if (!closeDate) return null;
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  if (stage === OpportunityStage.CLOSED_WON || stage === OpportunityStage.CLOSED_LOST) {
    if (closeDate > today) {
      return 'Close date for closed opportunities cannot be in the future';
    }
  } else {
    if (closeDate < today) {
      return 'Close date for open opportunities should not be in the past';
    }
  }
  
  return null;
};

export const validateProbability = (probability?: number, stage?: OpportunityStage) => {
  if (probability === undefined) return null;
  
  const stageToMinProbability: Record<OpportunityStage, number> = {
    [OpportunityStage.PROSPECTING]: 0,
    [OpportunityStage.QUALIFICATION]: 10,
    [OpportunityStage.NEEDS_ANALYSIS]: 20,
    [OpportunityStage.VALUE_PROPOSITION]: 30,
    [OpportunityStage.ID_DECISION_MAKERS]: 40,
    [OpportunityStage.PERCEPTION_ANALYSIS]: 50,
    [OpportunityStage.PROPOSAL_PRICE_QUOTE]: 60,
    [OpportunityStage.NEGOTIATION_REVIEW]: 80,
    [OpportunityStage.CLOSED_WON]: 100,
    [OpportunityStage.CLOSED_LOST]: 0
  };
  
  if (stage && stageToMinProbability[stage] !== undefined) {
    const minProbability = stageToMinProbability[stage];
    if (stage === OpportunityStage.CLOSED_WON && probability !== 100) {
      return 'Closed Won opportunities must have 100% probability';
    }
    if (stage === OpportunityStage.CLOSED_LOST && probability !== 0) {
      return 'Closed Lost opportunities must have 0% probability';
    }
    if (stage !== OpportunityStage.CLOSED_WON && stage !== OpportunityStage.CLOSED_LOST && probability < minProbability) {
      return `Probability for ${stage} stage should be at least ${minProbability}%`;
    }
  }
  
  return null;
};

export const validateStageProgression = (currentStage?: OpportunityStage, newStage?: OpportunityStage) => {
  if (!currentStage || !newStage || currentStage === newStage) return null;
  
  const stageOrder = [
    OpportunityStage.PROSPECTING,
    OpportunityStage.QUALIFICATION,
    OpportunityStage.NEEDS_ANALYSIS,
    OpportunityStage.VALUE_PROPOSITION,
    OpportunityStage.ID_DECISION_MAKERS,
    OpportunityStage.PERCEPTION_ANALYSIS,
    OpportunityStage.PROPOSAL_PRICE_QUOTE,
    OpportunityStage.NEGOTIATION_REVIEW,
    OpportunityStage.CLOSED_WON
  ];
  
  const currentIndex = stageOrder.indexOf(currentStage);
  const newIndex = stageOrder.indexOf(newStage);
  
  // Allow moving to Closed Lost from any stage
  if (newStage === OpportunityStage.CLOSED_LOST) return null;
  
  // Don't allow moving backwards (except to Closed Lost)
  if (newIndex < currentIndex) {
    return 'Cannot move opportunity backwards in the sales process';
  }
  
  // Don't allow skipping more than 2 stages
  if (newIndex - currentIndex > 2) {
    return 'Cannot skip more than 2 stages in progression';
  }
  
  return null;
};

// Helper function to get default probability for stage
export const getDefaultProbabilityForStage = (stage: OpportunityStage): number => {
  const stageToProbability: Record<OpportunityStage, number> = {
    [OpportunityStage.PROSPECTING]: 10,
    [OpportunityStage.QUALIFICATION]: 20,
    [OpportunityStage.NEEDS_ANALYSIS]: 30,
    [OpportunityStage.VALUE_PROPOSITION]: 40,
    [OpportunityStage.ID_DECISION_MAKERS]: 50,
    [OpportunityStage.PERCEPTION_ANALYSIS]: 60,
    [OpportunityStage.PROPOSAL_PRICE_QUOTE]: 70,
    [OpportunityStage.NEGOTIATION_REVIEW]: 90,
    [OpportunityStage.CLOSED_WON]: 100,
    [OpportunityStage.CLOSED_LOST]: 0
  };
  
  return stageToProbability[stage] || 10;
};

// Helper function to get default forecast category for stage
export const getDefaultForecastCategoryForStage = (stage: OpportunityStage): ForecastCategory => {
  const stageToForecast: Record<OpportunityStage, ForecastCategory> = {
    [OpportunityStage.PROSPECTING]: ForecastCategory.PIPELINE,
    [OpportunityStage.QUALIFICATION]: ForecastCategory.PIPELINE,
    [OpportunityStage.NEEDS_ANALYSIS]: ForecastCategory.PIPELINE,
    [OpportunityStage.VALUE_PROPOSITION]: ForecastCategory.PIPELINE,
    [OpportunityStage.ID_DECISION_MAKERS]: ForecastCategory.BEST_CASE,
    [OpportunityStage.PERCEPTION_ANALYSIS]: ForecastCategory.BEST_CASE,
    [OpportunityStage.PROPOSAL_PRICE_QUOTE]: ForecastCategory.COMMIT,
    [OpportunityStage.NEGOTIATION_REVIEW]: ForecastCategory.COMMIT,
    [OpportunityStage.CLOSED_WON]: ForecastCategory.CLOSED,
    [OpportunityStage.CLOSED_LOST]: ForecastCategory.OMITTED
  };
  
  return stageToForecast[stage] || ForecastCategory.PIPELINE;
};