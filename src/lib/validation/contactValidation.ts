// Contact Validation Schemas
// Yup validation schemas for Contact entities

import * as yup from 'yup';
import {
  ContactSalutation,
  ContactLeadSource,
  ContactLevel,
  ContactStatus,
  CONTACT_SALUTATIONS,
  CONTACT_LEAD_SOURCES,
  CONTACT_LEVELS,
  CONTACT_STATUSES
} from '@/types/contact';

// Address validation schema
export const addressSchema = yup.object().shape({
  street: yup.string().max(255, 'Street must be less than 255 characters').nullable(),
  city: yup.string().max(100, 'City must be less than 100 characters').nullable(),
  state: yup.string().max(100, 'State must be less than 100 characters').nullable(),
  postalCode: yup.string().max(20, 'Postal code must be less than 20 characters').nullable(),
  country: yup.string().max(100, 'Country must be less than 100 characters').nullable(),
  latitude: yup.number().min(-90).max(90).nullable(),
  longitude: yup.number().min(-180).max(180).nullable()
});

// Phone number validation helper
const phoneValidation = yup.string()
  .max(50, 'Phone number must be less than 50 characters')
  .matches(
    /^[+]?[1-9][\d\s\-()]{0,20}$/,
    'Invalid phone number format'
  )
  .nullable();

// Email validation helper
const emailValidation = yup.string()
  .email('Invalid email format')
  .max(255, 'Email must be less than 255 characters');

// Contact creation validation schema
export const contactCreateSchema = yup.object().shape({
  // Required fields
  account_id: yup.number()
    .required('Account is required')
    .positive('Account ID must be positive')
    .integer('Account ID must be an integer'),
  
  last_name: yup.string()
    .required('Last name is required')
    .trim()
    .min(1, 'Last name cannot be empty')
    .max(255, 'Last name must be less than 255 characters'),
  
  email: emailValidation.required('Email is required'),
  
  // Optional personal information
  first_name: yup.string()
    .max(255, 'First name must be less than 255 characters')
    .nullable(),
  
  salutation: yup.string()
    .oneOf([...CONTACT_SALUTATIONS, null], 'Invalid salutation')
    .nullable(),
  
  title: yup.string()
    .max(255, 'Title must be less than 255 characters')
    .nullable(),
  
  department: yup.string()
    .max(255, 'Department must be less than 255 characters')
    .nullable(),
  
  // Communication channels
  phone: phoneValidation,
  mobile_phone: phoneValidation,
  home_phone: phoneValidation,
  other_phone: phoneValidation,
  fax: phoneValidation,
  
  // Address information
  mailing_address: addressSchema.nullable(),
  other_address: addressSchema.nullable(),
  
  // Hierarchy and relationships
  reports_to_id: yup.number()
    .positive('Reports To ID must be positive')
    .integer('Reports To ID must be an integer')
    .nullable()
    .test('no-self-reference', 'Contact cannot report to themselves', function(value) {
      // This would need to be enhanced with actual contact ID in update scenarios
      return true; // Basic validation, enhanced in backend
    }),
  
  // Assistant information
  assistant_name: yup.string()
    .max(255, 'Assistant name must be less than 255 characters')
    .nullable(),
  
  assistant_phone: phoneValidation,
  
  // Additional personal details
  birthdate: yup.date()
    .max(new Date(), 'Birthdate cannot be in the future')
    .nullable(),
  
  // Lead and qualification information
  lead_source: yup.string()
    .oneOf([...CONTACT_LEAD_SOURCES, null], 'Invalid lead source')
    .nullable(),
  
  level: yup.string()
    .oneOf([...CONTACT_LEVELS, null], 'Invalid contact level')
    .nullable(),
  
  description: yup.string()
    .max(5000, 'Description must be less than 5000 characters')
    .nullable(),
  
  // Language and communication preferences
  languages: yup.string()
    .max(500, 'Languages must be less than 500 characters')
    .nullable(),
  
  do_not_call: yup.boolean().default(false),
  has_opted_out_of_email: yup.boolean().default(false),
  
  // Ownership and assignment
  owner_id: yup.number()
    .positive('Owner ID must be positive')
    .integer('Owner ID must be an integer')
    .nullable(),
  
  // Custom fields
  custom_fields: yup.object().nullable()
});

// Contact update validation schema (all fields optional except ID constraints)
export const contactUpdateSchema = yup.object().shape({
  id: yup.number()
    .positive('Contact ID must be positive')
    .integer('Contact ID must be an integer')
    .nullable(),
  
  // All other fields are optional for updates
  account_id: yup.number()
    .positive('Account ID must be positive')
    .integer('Account ID must be an integer')
    .nullable(),
  
  last_name: yup.string()
    .trim()
    .min(1, 'Last name cannot be empty')
    .max(255, 'Last name must be less than 255 characters')
    .nullable(),
  
  email: emailValidation.nullable(),
  
  first_name: yup.string()
    .max(255, 'First name must be less than 255 characters')
    .nullable(),
  
  salutation: yup.string()
    .oneOf([...CONTACT_SALUTATIONS, null], 'Invalid salutation')
    .nullable(),
  
  title: yup.string()
    .max(255, 'Title must be less than 255 characters')
    .nullable(),
  
  department: yup.string()
    .max(255, 'Department must be less than 255 characters')
    .nullable(),
  
  phone: phoneValidation,
  mobile_phone: phoneValidation,
  home_phone: phoneValidation,
  other_phone: phoneValidation,
  fax: phoneValidation,
  
  mailing_address: addressSchema.nullable(),
  other_address: addressSchema.nullable(),
  
  reports_to_id: yup.number()
    .positive('Reports To ID must be positive')
    .integer('Reports To ID must be an integer')
    .nullable(),
  
  assistant_name: yup.string()
    .max(255, 'Assistant name must be less than 255 characters')
    .nullable(),
  
  assistant_phone: phoneValidation,
  
  birthdate: yup.date()
    .max(new Date(), 'Birthdate cannot be in the future')
    .nullable(),
  
  lead_source: yup.string()
    .oneOf([...CONTACT_LEAD_SOURCES, null], 'Invalid lead source')
    .nullable(),
  
  level: yup.string()
    .oneOf([...CONTACT_LEVELS, null], 'Invalid contact level')
    .nullable(),
  
  description: yup.string()
    .max(5000, 'Description must be less than 5000 characters')
    .nullable(),
  
  languages: yup.string()
    .max(500, 'Languages must be less than 500 characters')
    .nullable(),
  
  do_not_call: yup.boolean().nullable(),
  has_opted_out_of_email: yup.boolean().nullable(),
  
  owner_id: yup.number()
    .positive('Owner ID must be positive')
    .integer('Owner ID must be an integer')
    .nullable(),
  
  custom_fields: yup.object().nullable()
});

// Contact search filters validation schema
export const contactSearchSchema = yup.object().shape({
  search: yup.string().max(255, 'Search term must be less than 255 characters').nullable(),
  account_id: yup.number().positive().integer().nullable(),
  department: yup.array().of(yup.string()).nullable(),
  level: yup.array().of(yup.string().oneOf(CONTACT_LEVELS)).nullable(),
  lead_source: yup.array().of(yup.string().oneOf(CONTACT_LEAD_SOURCES)).nullable(),
  status: yup.array().of(yup.string().oneOf(CONTACT_STATUSES)).nullable(),
  owner_id: yup.number().positive().integer().nullable(),
  reports_to_id: yup.number().positive().integer().nullable(),
  has_direct_reports: yup.boolean().nullable(),
  created_after: yup.date().nullable(),
  created_before: yup.date().nullable(),
  page: yup.number().positive().integer().default(1),
  limit: yup.number().positive().integer().max(100).default(10)
});

// Bulk contact operations validation
export const bulkContactCreateSchema = yup.object().shape({
  contacts: yup.array()
    .of(contactCreateSchema)
    .min(1, 'At least one contact is required')
    .max(100, 'Cannot create more than 100 contacts at once')
    .required('Contacts array is required')
});

export const bulkContactUpdateSchema = yup.object().shape({
  contacts: yup.array()
    .of(contactUpdateSchema.concat(yup.object().shape({
      id: yup.number().required('Contact ID is required for updates').positive().integer()
    })))
    .min(1, 'At least one contact is required')
    .max(100, 'Cannot update more than 100 contacts at once')
    .required('Contacts array is required')
});

export const bulkContactDeleteSchema = yup.object().shape({
  contact_ids: yup.array()
    .of(yup.number().positive().integer())
    .min(1, 'At least one contact ID is required')
    .max(100, 'Cannot delete more than 100 contacts at once')
    .required('Contact IDs array is required')
});

// Hierarchy validation schema
export const hierarchyUpdateSchema = yup.object().shape({
  contact_id: yup.number()
    .required('Contact ID is required')
    .positive('Contact ID must be positive')
    .integer('Contact ID must be an integer'),
  
  new_reports_to_id: yup.number()
    .positive('Reports To ID must be positive')
    .integer('Reports To ID must be an integer')
    .nullable()
    .test('no-self-reference', 'Contact cannot report to themselves', function(value) {
      const { contact_id } = this.parent;
      return !value || value !== contact_id;
    })
});

// Communication preferences validation
export const communicationPreferencesSchema = yup.object().shape({
  do_not_call: yup.boolean().required('Do not call preference is required'),
  has_opted_out_of_email: yup.boolean().required('Email opt-out preference is required'),
  preferred_language: yup.string().max(10, 'Language code must be less than 10 characters').nullable(),
  preferred_contact_method: yup.string()
    .oneOf(['email', 'phone', 'mobile'], 'Invalid contact method')
    .nullable(),
  custom_preferences: yup.object().nullable()
});

// Export validation functions
export const validateContactCreate = async (data: unknown) => {
  try {
    const validatedData = await contactCreateSchema.validate(data, { abortEarly: false });
    return { isValid: true, data: validatedData, errors: [] };
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      return {
        isValid: false,
        data: null,
        errors: error.inner.map(err => ({
          field: err.path || 'unknown',
          message: err.message,
          code: err.type || 'validation_error'
        }))
      };
    }
    throw error;
  }
};

export const validateContactUpdate = async (data: unknown) => {
  try {
    const validatedData = await contactUpdateSchema.validate(data, { abortEarly: false });
    return { isValid: true, data: validatedData, errors: [] };
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      return {
        isValid: false,
        data: null,
        errors: error.inner.map(err => ({
          field: err.path || 'unknown',
          message: err.message,
          code: err.type || 'validation_error'
        }))
      };
    }
    throw error;
  }
};

export const validateContactSearch = async (data: unknown) => {
  try {
    const validatedData = await contactSearchSchema.validate(data, { abortEarly: false });
    return { isValid: true, data: validatedData, errors: [] };
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      return {
        isValid: false,
        data: null,
        errors: error.inner.map(err => ({
          field: err.path || 'unknown',
          message: err.message,
          code: err.type || 'validation_error'
        }))
      };
    }
    throw error;
  }
};

// Custom validation helpers
export const validateEmailUniqueness = async (email: string, contactId?: number): Promise<boolean> => {
  // This would need to be implemented with actual database check
  // For now, return true as placeholder
  return true;
};

export const validateHierarchyCycle = async (contactId: number, reportsToId: number): Promise<boolean> => {
  // This would need to be implemented with actual hierarchy checking
  // For now, return basic validation
  return contactId !== reportsToId;
};

export const validateAccountExists = async (accountId: number): Promise<boolean> => {
  // This would need to be implemented with actual database check
  // For now, return true as placeholder
  return true;
};

export const validateUserExists = async (userId: number): Promise<boolean> => {
  // This would need to be implemented with actual database check
  // For now, return true as placeholder
  return true;
};