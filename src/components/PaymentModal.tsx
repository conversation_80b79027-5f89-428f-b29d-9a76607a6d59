import * as React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CreditCard, 
  DollarSign, 
  X, 
  CheckCircle, 
  AlertTriangle,
  Loader2
} from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { cn } from '@/lib/utils';

export interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoiceAmount: number;
  currency: string;
  invoiceNumber: string;
  onPayment: (paymentData: PaymentData) => Promise<void>;
  className?: string;
}

export interface PaymentData {
  amount: number;
  gateway: 'stripe' | 'paypal' | 'bank_transfer';
  paymentMethod?: string;
  isPartial?: boolean;
}

const gatewayOptions = [
  {
    id: 'stripe',
    name: 'Stripe',
    icon: CreditCard,
    description: 'Credit/Debit Card',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
  },
  {
    id: 'paypal',
    name: 'PayPal',
    icon: DollarSign,
    description: 'PayPal Account',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  },
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    icon: DollarSign,
    description: 'Direct Bank Transfer',
    color: 'bg-green-100 text-green-800 border-green-200',
  },
];

export function PaymentModal({ 
  isOpen, 
  onClose, 
  invoiceAmount, 
  currency, 
  invoiceNumber,
  onPayment,
  className 
}: PaymentModalProps) {
  const [selectedGateway, setSelectedGateway] = React.useState<'stripe' | 'paypal' | 'bank_transfer'>('stripe');
  const [paymentAmount, setPaymentAmount] = React.useState(invoiceAmount);
  const [isPartialPayment, setIsPartialPayment] = React.useState(false);
  const [isProcessing, setIsProcessing] = React.useState(false);
  const [paymentStep, setPaymentStep] = React.useState<'select' | 'process' | 'success' | 'error'>('select');
  const [errorMessage, setErrorMessage] = React.useState('');

  // Reset state when modal opens
  React.useEffect(() => {
    if (isOpen) {
      setPaymentAmount(invoiceAmount);
      setIsPartialPayment(false);
      setPaymentStep('select');
      setErrorMessage('');
      setIsProcessing(false);
    }
  }, [isOpen, invoiceAmount]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(amount);
  };

  const handlePayment = async () => {
    if (paymentAmount <= 0 || paymentAmount > invoiceAmount) {
      setErrorMessage('Invalid payment amount');
      return;
    }

    setIsProcessing(true);
    setPaymentStep('process');
    setErrorMessage('');

    try {
      await onPayment({
        amount: paymentAmount,
        gateway: selectedGateway,
        isPartial: isPartialPayment,
      });
      
      setPaymentStep('success');
      
      // Auto-close after success
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      setPaymentStep('error');
      setErrorMessage(error instanceof Error ? error.message : 'Payment failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const selectedGatewayData = gatewayOptions.find(g => g.id === selectedGateway);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className={cn('w-full max-w-md', className)}
        >
          <Card className="card-neumorphic">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="w-5 h-5" />
                Process Payment
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                disabled={isProcessing}
              >
                <X className="w-4 h-4" />
              </Button>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* Invoice Info */}
              <div className="p-4 bg-muted/50 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-muted-foreground">Invoice:</span>
                  <span className="font-medium">{invoiceNumber}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Total Amount:</span>
                  <span className="font-bold text-lg">{formatCurrency(invoiceAmount)}</span>
                </div>
              </div>

              {paymentStep === 'select' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-4"
                >
                  {/* Payment Amount */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Label htmlFor="paymentAmount">Payment Amount</Label>
                      <button
                        type="button"
                        onClick={() => {
                          setIsPartialPayment(!isPartialPayment);
                          if (!isPartialPayment) {
                            setPaymentAmount(invoiceAmount / 2); // Default to half
                          } else {
                            setPaymentAmount(invoiceAmount);
                          }
                        }}
                        className="text-sm text-primary hover:underline"
                      >
                        {isPartialPayment ? 'Pay Full Amount' : 'Partial Payment'}
                      </button>
                    </div>
                    <Input
                      id="paymentAmount"
                      type="number"
                      value={paymentAmount}
                      onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                      min={0}
                      max={invoiceAmount}
                      step={0.01}
                    />
                    {isPartialPayment && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Remaining: {formatCurrency(invoiceAmount - paymentAmount)}
                      </p>
                    )}
                  </div>

                  {/* Gateway Selection */}
                  <div>
                    <Label className="mb-3 block">Payment Method</Label>
                    <div className="grid gap-3">
                      {gatewayOptions.map((gateway) => {
                        const Icon = gateway.icon;
                        const isSelected = selectedGateway === gateway.id;
                        
                        return (
                          <button
                            key={gateway.id}
                            onClick={() => setSelectedGateway(gateway.id as any)}
                            className={`p-4 rounded-lg border-2 transition-all text-left ${
                              isSelected 
                                ? 'border-primary bg-primary/5' 
                                : 'border-border hover:border-primary/50'
                            }`}
                          >
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg ${gateway.color}`}>
                                <Icon className="w-5 h-5" />
                              </div>
                              <div>
                                <div className="font-medium">{gateway.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {gateway.description}
                                </div>
                              </div>
                              {isSelected && (
                                <CheckCircle className="w-5 h-5 text-primary ml-auto" />
                              )}
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Error Message */}
                  {errorMessage && (
                    <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg">
                      <AlertTriangle className="w-4 h-4 text-red-600" />
                      <span className="text-sm text-red-600">{errorMessage}</span>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-4">
                    <Button
                      onClick={handlePayment}
                      disabled={paymentAmount <= 0 || paymentAmount > invoiceAmount}
                      className="flex-1"
                    >
                      Pay {formatCurrency(paymentAmount)}
                    </Button>
                    <Button variant="outline" onClick={onClose}>
                      Cancel
                    </Button>
                  </div>
                </motion.div>
              )}

              {paymentStep === 'process' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-8"
                >
                  <Loader2 className="w-12 h-12 animate-spin text-primary mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Processing Payment</h3>
                  <p className="text-muted-foreground">
                    Please wait while we process your payment via {selectedGatewayData?.name}...
                  </p>
                  <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                    <div className="text-sm text-muted-foreground">Amount:</div>
                    <div className="font-bold">{formatCurrency(paymentAmount)}</div>
                  </div>
                </motion.div>
              )}

              {paymentStep === 'success' && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center py-8"
                >
                  <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-green-600 mb-2">Payment Successful!</h3>
                  <p className="text-muted-foreground mb-4">
                    Your payment of {formatCurrency(paymentAmount)} has been processed successfully.
                  </p>
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    Transaction completed via {selectedGatewayData?.name}
                  </Badge>
                </motion.div>
              )}

              {paymentStep === 'error' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-8"
                >
                  <AlertTriangle className="w-16 h-16 text-red-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-red-600 mb-2">Payment Failed</h3>
                  <p className="text-muted-foreground mb-4">{errorMessage}</p>
                  <div className="flex gap-2 justify-center">
                    <Button onClick={() => setPaymentStep('select')}>
                      Try Again
                    </Button>
                    <Button variant="outline" onClick={onClose}>
                      Close
                    </Button>
                  </div>
                </motion.div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}