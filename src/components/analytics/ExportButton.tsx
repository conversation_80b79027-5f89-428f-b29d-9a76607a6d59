import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { Download, FileText, File, FileSpreadsheet, Loader2 } from 'lucide-react';

export interface ExportButtonProps {
  data: any[];
  filename: string;
  format?: 'pdf' | 'csv' | 'excel';
  loading?: boolean;
  onExport: (format: string) => Promise<void>;
  disabled?: boolean;
  className?: string;
}

const ExportButton: React.FC<ExportButtonProps> = ({
  data,
  filename,
  format,
  loading = false,
  onExport,
  disabled = false,
  className = '',
}) => {
  const [exportProgress, setExportProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();

  const handleExport = async (exportFormat: string) => {
    if (!data || data.length === 0) {
      toast({
        title: 'Export Error',
        description: 'No data available to export',
        variant: 'destructive',
      });
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    // Simulate progress for better UX
    const progressInterval = setInterval(() => {
      setExportProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return prev;
        }
        return prev + 10;
      });
    }, 200);

    try {
      await onExport(exportFormat);
      setExportProgress(100);
      
      setTimeout(() => {
        setIsExporting(false);
        setExportProgress(0);
        toast({
          title: 'Export Successful',
          description: `${filename}.${exportFormat} has been downloaded`,
        });
      }, 500);
    } catch (error) {
      clearInterval(progressInterval);
      setIsExporting(false);
      setExportProgress(0);
      toast({
        title: 'Export Failed',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      });
    }
  };

  const getFormatIcon = (formatType: string) => {
    switch (formatType) {
      case 'pdf':
        return <FileText className="h-4 w-4 mr-2" />;
      case 'csv':
        return <File className="h-4 w-4 mr-2" />;
      case 'excel':
        return <FileSpreadsheet className="h-4 w-4 mr-2" />;
      default:
        return <Download className="h-4 w-4 mr-2" />;
    }
  };

  const formats = [
    { value: 'csv', label: 'CSV', icon: File },
    { value: 'pdf', label: 'PDF', icon: FileText },
    { value: 'excel', label: 'Excel', icon: FileSpreadsheet },
  ];

  if (format) {
    // Single format button
    return (
      <div className={`space-y-2 ${className}`}>
        <Button
          variant="outline"
          onClick={() => handleExport(format)}
          disabled={disabled || loading || isExporting}
          className="w-full"
        >
          {isExporting ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            getFormatIcon(format)
          )}
          {isExporting ? 'Exporting...' : `Export ${format.toUpperCase()}`}
        </Button>
        {isExporting && (
          <div className="space-y-1">
            <Progress value={exportProgress} className="h-2" />
            <p className="text-xs text-center text-muted-foreground">
              {exportProgress}% complete
            </p>
          </div>
        )}
      </div>
    );
  }

  // Multi-format dropdown
  return (
    <div className={`space-y-2 ${className}`}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            disabled={disabled || loading || isExporting}
            className="w-full"
          >
            {isExporting ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            {isExporting ? 'Exporting...' : 'Export Data'}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {formats.map((fmt) => (
            <DropdownMenuItem
              key={fmt.value}
              onClick={() => handleExport(fmt.value)}
              disabled={isExporting}
            >
              <fmt.icon className="h-4 w-4 mr-2" />
              Export as {fmt.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
      {isExporting && (
        <div className="space-y-1">
          <Progress value={exportProgress} className="h-2" />
          <p className="text-xs text-center text-muted-foreground">
            {exportProgress}% complete
          </p>
        </div>
      )}
    </div>
  );
};

export default ExportButton;