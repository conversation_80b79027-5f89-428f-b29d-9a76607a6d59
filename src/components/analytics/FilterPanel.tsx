import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { X, Calendar as CalendarIcon, RotateCcw } from 'lucide-react';
import { format } from 'date-fns';

export interface FilterConfig {
  key: string;
  label: string;
  type: 'select' | 'multiselect' | 'daterange' | 'number' | 'text';
  options?: Array<{ value: string; label: string; }>;
  placeholder?: string;
}

export interface FilterPanelProps {
  filters: Record<string, string | string[] | { start: string | null; end: string | null }>;
  onFilterChange: (key: string, value: string | string[] | { start: string | null; end: string | null }) => void;
  onReset: () => void;
  availableFilters: FilterConfig[];
  className?: string;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  onFilterChange,
  onReset,
  availableFilters,
  className = '',
}) => {
  const renderFilter = (filter: FilterConfig) => {
    const value = filters[filter.key];

    switch (filter.type) {
      case 'text':
        return (
          <div key={filter.key} className="space-y-2">
            <Label htmlFor={filter.key}>{filter.label}</Label>
            <Input
              id={filter.key}
              type="text"
              placeholder={filter.placeholder}
              value={value || ''}
              onChange={(e) => onFilterChange(filter.key, e.target.value)}
            />
          </div>
        );

      case 'number':
        return (
          <div key={filter.key} className="space-y-2">
            <Label htmlFor={filter.key}>{filter.label}</Label>
            <Input
              id={filter.key}
              type="number"
              placeholder={filter.placeholder}
              value={value || ''}
              onChange={(e) => onFilterChange(filter.key, parseFloat(e.target.value) || 0)}
            />
          </div>
        );

      case 'select':
        return (
          <div key={filter.key} className="space-y-2">
            <Label>{filter.label}</Label>
            <Select
              value={value || ''}
              onValueChange={(val) => onFilterChange(filter.key, val)}
            >
              <SelectTrigger>
                <SelectValue placeholder={filter.placeholder || `Select ${filter.label.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                {filter.options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'multiselect': {
        const selectedValues = (value as string[]) || [];
        return (
          <div key={filter.key} className="space-y-2">
            <Label>{filter.label}</Label>
            <div className="space-y-2">
              <Select
                onValueChange={(val) => {
                  if (!selectedValues.includes(val)) {
                    onFilterChange(filter.key, [...selectedValues, val]);
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder={filter.placeholder || `Select ${filter.label.toLowerCase()}`} />
                </SelectTrigger>
                <SelectContent>
                  {filter.options?.filter(option => !selectedValues.includes(option.value)).map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedValues.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {selectedValues.map((val: string) => {
                    const option = filter.options?.find(opt => opt.value === val);
                    return (
                      <Badge key={val} variant="secondary" className="text-xs">
                        {option?.label || val}
                        <button
                          onClick={() => 
                            onFilterChange(filter.key, selectedValues.filter((v: string) => v !== val))
                          }
                          className="ml-1 hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        );
      }

      case 'daterange': {
        const dateRange = (value as { start: string | null; end: string | null }) || { start: null, end: null };
        const startDate = dateRange.start ? new Date(dateRange.start) : null;
        const endDate = dateRange.end ? new Date(dateRange.end) : null;
        return (
          <div key={filter.key} className="space-y-2">
            <Label>{filter.label}</Label>
            <div style={{display:"flex",justifyContent:"center", gap:"1rem", flexDirection:"column"}}>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, 'PPP') : 'Start date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => 
                      onFilterChange(filter.key, { ...dateRange, start: date ? date.toISOString() : null })
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, 'PPP') : 'End date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => 
                      onFilterChange(filter.key, { ...dateRange, end: date ? date.toISOString() : null })
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        );
      }

      default:
        return null;
    }
  };

  const hasActiveFilters = Object.values(filters).some(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== null && v !== undefined && v !== '');
    }
    return value !== null && value !== undefined && value !== '';
  });

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-base font-medium">Filters</CardTitle>
        {hasActiveFilters && (
          <Button variant="ghost" size="sm" onClick={onReset}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {availableFilters.map(renderFilter)}
      </CardContent>
    </Card>
  );
};

export default FilterPanel;