import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { User } from '@/store/slices/authSlice';
import { Users, Shield, ShieldOff, Trash2, Mail } from 'lucide-react';

interface BulkUserActionsProps {
  users: User[];
  selectedUsers: string[];
  onSelectionChange: (userIds: string[]) => void;
  onBulkRoleChange: (userIds: string[], newRole: 'admin' | 'manager' | 'viewer') => Promise<void>;
  onBulkStatusChange: (userIds: string[], newStatus: 'active' | 'inactive') => Promise<void>;
  onBulkInviteResend: (userIds: string[]) => Promise<void>;
  currentUser: User | null;
}

interface BulkActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  action: 'role' | 'status' | 'invite' | null;
  selectedCount: number;
  onConfirm: (value?: any) => void;
  isLoading: boolean;
  progress: number;
}

const BulkActionModal: React.FC<BulkActionModalProps> = ({
  isOpen,
  onClose,
  action,
  selectedCount,
  onConfirm,
  isLoading,
  progress
}) => {
  const [selectedRole, setSelectedRole] = useState<'admin' | 'manager' | 'viewer'>('viewer');
  const [selectedStatus, setSelectedStatus] = useState<'active' | 'inactive'>('active');

  const getActionTitle = () => {
    switch (action) {
      case 'role': return 'Change User Roles';
      case 'status': return 'Change User Status';
      case 'invite': return 'Resend Invitations';
      default: return 'Bulk Action';
    }
  };

  const getActionDescription = () => {
    switch (action) {
      case 'role': return `Change the role for ${selectedCount} selected users. This will immediately affect their permissions.`;
      case 'status': return `Change the status for ${selectedCount} selected users. This will affect their access to the system.`;
      case 'invite': return `Resend invitation emails to ${selectedCount} selected users with pending status.`;
      default: return '';
    }
  };

  const handleConfirm = () => {
    switch (action) {
      case 'role':
        onConfirm(selectedRole);
        break;
      case 'status':
        onConfirm(selectedStatus);
        break;
      case 'invite':
        onConfirm();
        break;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{getActionTitle()}</DialogTitle>
          <DialogDescription>{getActionDescription()}</DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          {action === 'role' && (
            <div>
              <label className="text-sm font-medium">New Role</label>
              <Select value={selectedRole} onValueChange={(value: 'admin' | 'manager' | 'viewer') => setSelectedRole(value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="viewer">Viewer</SelectItem>
                  <SelectItem value="manager">Manager</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          
          {action === 'status' && (
            <div>
              <label className="text-sm font-medium">New Status</label>
              <Select value={selectedStatus} onValueChange={(value: 'active' | 'inactive') => setSelectedStatus(value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          
          {isLoading && (
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-2">
                <span>Processing...</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={isLoading}>
            {isLoading ? 'Processing...' : 'Confirm'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export const BulkUserActions: React.FC<BulkUserActionsProps> = ({
  users,
  selectedUsers,
  onSelectionChange,
  onBulkRoleChange,
  onBulkStatusChange,
  onBulkInviteResend,
  currentUser
}) => {
  const [bulkAction, setBulkAction] = useState<'role' | 'status' | 'invite' | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);

  const selectableUsers = users.filter(user => user.id !== currentUser?.id);
  const allSelected = selectableUsers.length > 0 && selectedUsers.length === selectableUsers.length;
  const someSelected = selectedUsers.length > 0 && selectedUsers.length < selectableUsers.length;

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(selectableUsers.map(user => user.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleUserSelect = (userId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedUsers, userId]);
    } else {
      onSelectionChange(selectedUsers.filter(id => id !== userId));
    }
  };

  const handleBulkAction = async (action: 'role' | 'status' | 'invite', value?: any) => {
    setIsLoading(true);
    setProgress(0);
    
    try {
      const batchSize = 5;
      const batches = [];
      
      for (let i = 0; i < selectedUsers.length; i += batchSize) {
        batches.push(selectedUsers.slice(i, i + batchSize));
      }
      
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        
        switch (action) {
          case 'role':
            await onBulkRoleChange(batch, value);
            break;
          case 'status':
            await onBulkStatusChange(batch, value);
            break;
          case 'invite':
            await onBulkInviteResend(batch);
            break;
        }
        
        setProgress(((i + 1) / batches.length) * 100);
        
        // Small delay between batches to prevent overwhelming the server
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      onSelectionChange([]);
    } finally {
      setIsLoading(false);
      setProgress(0);
      setBulkAction(null);
    }
  };

  const pendingUsers = users.filter(user => 
    selectedUsers.includes(user.id) && user.status === 'pending'
  );

  if (selectedUsers.length === 0) {
    return (
      <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
        <Checkbox
          checked={allSelected}
          onCheckedChange={handleSelectAll}
          className="data-[state=indeterminate]:bg-primary data-[state=indeterminate]:text-primary-foreground"
          ref={(el) => {
            if (el) el.indeterminate = someSelected;
          }}
        />
        <span className="text-sm text-gray-600">
          Select users to perform bulk actions
        </span>
      </div>
    );
  }

  return (
    <>
      <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-center space-x-4">
          <Checkbox
            checked={allSelected}
            onCheckedChange={handleSelectAll}
            className="data-[state=indeterminate]:bg-primary data-[state=indeterminate]:text-primary-foreground"
            ref={(el) => {
              if (el) el.indeterminate = someSelected;
            }}
          />
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-900">
              {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''} selected
            </span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setBulkAction('role')}
            disabled={isLoading}
          >
            <Shield className="mr-2 h-4 w-4" />
            Change Role
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setBulkAction('status')}
            disabled={isLoading}
          >
            <ShieldOff className="mr-2 h-4 w-4" />
            Change Status
          </Button>
          
          {pendingUsers.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setBulkAction('invite')}
              disabled={isLoading}
            >
              <Mail className="mr-2 h-4 w-4" />
              Resend Invites
              <Badge variant="secondary" className="ml-2">
                {pendingUsers.length}
              </Badge>
            </Button>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onSelectionChange([])}
            disabled={isLoading}
          >
            Clear
          </Button>
        </div>
      </div>

      <BulkActionModal
        isOpen={bulkAction !== null}
        onClose={() => setBulkAction(null)}
        action={bulkAction}
        selectedCount={selectedUsers.length}
        onConfirm={(value) => handleBulkAction(bulkAction!, value)}
        isLoading={isLoading}
        progress={progress}
      />
    </>
  );
};