import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { validateForm, userInvitationSchema } from '@/lib/validation';
import { FormError } from '@/components/ErrorBoundary';
import { LoadingButton } from '@/components/LoadingStates';

interface InviteUserModalProps {
  onInvite: (email: string, role: 'admin' | 'manager' | 'viewer') => void;
  isLoading: boolean;
}

export const InviteUserModal: React.FC<InviteUserModalProps> = ({ onInvite, isLoading }) => {
  const [formData, setFormData] = useState({
    email: '',
    role: 'viewer' as 'admin' | 'manager' | 'viewer',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleInvite = () => {
    const validation = validateForm(formData, userInvitationSchema);
    
    if (!validation.isValid) {
      const errorMap: Record<string, string> = {};
      validation.errors.forEach(error => {
        errorMap[error.field] = error.message;
      });
      setErrors(errorMap);
      return;
    }
    
    onInvite(formData.email, formData.role);
    
    // Reset form on success
    setFormData({ email: '', role: 'viewer' });
    setErrors({});
  };

  return (
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Invite New User</DialogTitle>
        <DialogDescription>
          Enter the email and assign a role. The user will receive an invitation to join.
        </DialogDescription>
      </DialogHeader>
      <div className="grid gap-4 py-4">
        <div>
          <Label htmlFor="email">Email Address</Label>
          <Input 
            id="email"
            placeholder="<EMAIL>" 
            value={formData.email} 
            onChange={(e) => handleInputChange('email', e.target.value)}
            className={errors.email ? 'border-red-500' : ''}
          />
          <FormError error={errors.email} />
        </div>
        <div>
          <Label htmlFor="role">Role</Label>
          <Select 
            value={formData.role} 
            onValueChange={(value: 'admin' | 'manager' | 'viewer') => handleInputChange('role', value)}
          >
            <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="viewer">
                <div>
                  <div className="font-medium">Viewer</div>
                  <div className="text-sm text-gray-500">Read-only access to all data</div>
                </div>
              </SelectItem>
              <SelectItem value="manager">
                <div>
                  <div className="font-medium">Manager</div>
                  <div className="text-sm text-gray-500">Can edit vendors, contracts, and approve invoices</div>
                </div>
              </SelectItem>
              <SelectItem value="admin">
                <div>
                  <div className="font-medium">Admin</div>
                  <div className="text-sm text-gray-500">Full system access including user management</div>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          <FormError error={errors.role} />
        </div>
      </div>
      <DialogFooter>
        <LoadingButton
          onClick={handleInvite}
          isLoading={isLoading}
          loadingText="Sending..."
          className="neumorphic-button-primary"
        >
          Send Invitation
        </LoadingButton>
      </DialogFooter>
    </DialogContent>
  );
};