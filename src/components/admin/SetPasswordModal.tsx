import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { validateForm, passwordResetSchema } from '@/lib/validation';
import { FormError } from '@/components/ErrorBoundary';
import { LoadingButton } from '@/components/LoadingStates';
import { Eye, EyeOff, Key } from 'lucide-react';
import { User } from '@/store/slices/authSlice';

interface SetPasswordModalProps {
  user: User;
  onSetPassword: (userId: string, newPassword: string) => void;
  isLoading: boolean;
}

export const SetPasswordModal: React.FC<SetPasswordModalProps> = ({ 
  user, 
  onSetPassword, 
  isLoading 
}) => {
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSetPassword = () => {
    const validation = validateForm(formData, passwordResetSchema);
    
    if (!validation.isValid) {
      const errorMap: Record<string, string> = {};
      validation.errors.forEach(error => {
        errorMap[error.field] = error.message;
      });
      setErrors(errorMap);
      return;
    }

    // Check if passwords match
    if (formData.password !== formData.confirmPassword) {
      setErrors({ confirmPassword: 'Passwords do not match' });
      return;
    }
    
    onSetPassword(user.id, formData.password);
    
    // Reset form on success
    setFormData({
      password: '',
      confirmPassword: '',
    });
    setErrors({});
  };

  return (
    <DialogContent className="max-w-md">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          <Key className="w-5 h-5" />
          Set Password for {user.name}
        </DialogTitle>
        <DialogDescription>
          Set a new password for <strong>{user.email}</strong>. The user will be able to log in with this password immediately.
        </DialogDescription>
      </DialogHeader>
      <div className="grid gap-4 py-4">
        <div>
          <Label htmlFor="password">New Password</Label>
          <div className="relative">
            <Input 
              id="password"
              type={showPassword ? 'text' : 'password'}
              placeholder="Enter new password" 
              value={formData.password} 
              onChange={(e) => handleInputChange('password', e.target.value)}
              className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          <FormError error={errors.password} />
          <p className="text-xs text-gray-500 mt-1">
            Password must be at least 8 characters with uppercase, lowercase, number, and special character.
          </p>
        </div>

        <div>
          <Label htmlFor="confirmPassword">Confirm New Password</Label>
          <div className="relative">
            <Input 
              id="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder="Confirm new password" 
              value={formData.confirmPassword} 
              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
              className={errors.confirmPassword ? 'border-red-500 pr-10' : 'pr-10'}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          <FormError error={errors.confirmPassword} />
        </div>
      </div>
      <DialogFooter>
        <LoadingButton
          onClick={handleSetPassword}
          isLoading={isLoading}
          loadingText="Setting Password..."
          className="neumorphic-button-primary"
        >
          Set Password
        </LoadingButton>
      </DialogFooter>
    </DialogContent>
  );
};