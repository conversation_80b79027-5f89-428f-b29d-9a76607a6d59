import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { MoreHorizontal, Edit, Shield, ShieldOff, Key } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { User } from '@/store/slices/authSlice';
import { SetPasswordModal } from './SetPasswordModal';

interface UserManagementTableProps {
  users: User[];
  onRoleChange: (userId: string, newRole: 'admin' | 'manager' | 'viewer') => void;
  onStatusChange: (userId: string, newStatus: 'active' | 'inactive') => void;
  onSetPassword: (userId: string, newPassword: string) => void;
  currentUser: User | null;
  isPasswordLoading?: boolean;
}

interface RoleChangeModalProps {
  user: User;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (newRole: 'admin' | 'manager' | 'viewer') => void;
}

const RoleChangeModal: React.FC<RoleChangeModalProps> = ({ user, isOpen, onClose, onConfirm }) => {
  const [newRole, setNewRole] = useState<'admin' | 'manager' | 'viewer'>(user.role);

  const handleConfirm = () => {
    onConfirm(newRole);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Change User Role</DialogTitle>
          <DialogDescription>
            Change the role for {user.name} ({user.email}). This will immediately affect their permissions.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Select value={newRole} onValueChange={(value: 'admin' | 'manager' | 'viewer') => setNewRole(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="viewer">
                <div>
                  <div className="font-medium">Viewer</div>
                  <div className="text-sm text-gray-500">Read-only access</div>
                </div>
              </SelectItem>
              <SelectItem value="manager">
                <div>
                  <div className="font-medium">Manager</div>
                  <div className="text-sm text-gray-500">Can edit and approve</div>
                </div>
              </SelectItem>
              <SelectItem value="admin">
                <div>
                  <div className="font-medium">Admin</div>
                  <div className="text-sm text-gray-500">Full system access</div>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleConfirm} disabled={newRole === user.role}>
            Update Role
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export const UserManagementTable: React.FC<UserManagementTableProps> = ({ 
  users, 
  onRoleChange, 
  onStatusChange, 
  onSetPassword,
  currentUser,
  isPasswordLoading = false
}) => {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [passwordUser, setPasswordUser] = useState<User | null>(null);

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active': return 'default';
      case 'inactive': return 'secondary';
      case 'pending': return 'outline';
      default: return 'default';
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800 border-red-200';
      case 'manager': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'viewer': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatLastLogin = (lastLogin: string) => {
    if (!lastLogin) return 'Never';
    const date = new Date(lastLogin);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInHours < 168) {
      return `${Math.floor(diffInHours / 24)}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const handleRoleChange = (user: User) => {
    setSelectedUser(user);
    setIsRoleModalOpen(true);
  };

  const handleRoleConfirm = (newRole: 'admin' | 'manager' | 'viewer') => {
    if (selectedUser) {
      onRoleChange(selectedUser.id, newRole);
    }
  };

  const handleStatusToggle = (user: User) => {
    const newStatus = user.status === 'active' ? 'inactive' : 'active';
    onStatusChange(user.id, newStatus);
  };

  const handleSetPassword = (user: User) => {
    setPasswordUser(user);
    setIsPasswordModalOpen(true);
  };

  const handlePasswordConfirm = (userId: string, newPassword: string) => {
    onSetPassword(userId, newPassword);
    setIsPasswordModalOpen(false);
    setPasswordUser(null);
  };

  return (
    <>
      <div className="rounded-lg border bg-white shadow-sm">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Last Login</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={user.profilePicture} />
                      <AvatarFallback className="text-sm">
                        {user.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge className={getRoleBadgeColor(user.role)}>
                    {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusVariant(user.status)}>
                    {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                  </Badge>
                </TableCell>
                <TableCell className="text-sm text-gray-600">
                  {formatLastLogin(user.lastLogin)}
                </TableCell>
                <TableCell className="text-sm text-gray-600">
                  {new Date(user.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {currentUser?.id !== user.id ? (
                        <>
                          <DropdownMenuItem onClick={() => handleRoleChange(user)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Change Role
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleStatusToggle(user)}>
                            {user.status === 'active' ? (
                              <>
                                <ShieldOff className="mr-2 h-4 w-4" />
                                Deactivate User
                              </>
                            ) : (
                              <>
                                <Shield className="mr-2 h-4 w-4" />
                                Activate User
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleSetPassword(user)}>
                            <Key className="mr-2 h-4 w-4" />
                            Set Password
                          </DropdownMenuItem>
                        </>
                      ) : (
                        <DropdownMenuItem disabled>
                          Cannot modify own account
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {selectedUser && (
        <RoleChangeModal
          user={selectedUser}
          isOpen={isRoleModalOpen}
          onClose={() => {
            setIsRoleModalOpen(false);
            setSelectedUser(null);
          }}
          onConfirm={handleRoleConfirm}
        />
      )}

      {passwordUser && (
        <Dialog open={isPasswordModalOpen} onOpenChange={setIsPasswordModalOpen}>
          <SetPasswordModal
            user={passwordUser}
            onSetPassword={handlePasswordConfirm}
            isLoading={isPasswordLoading}
          />
        </Dialog>
      )}
    </>
  );
};