import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { validateForm, userCreationSchema } from '@/lib/validation';
import { FormError } from '@/components/ErrorBoundary';
import { LoadingButton } from '@/components/LoadingStates';
import { Eye, EyeOff } from 'lucide-react';

interface CreateUserModalProps {
  onCreateUser: (userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    role: 'admin' | 'manager' | 'viewer';
  }) => void;
  isLoading: boolean;
}

export const CreateUserModal: React.FC<CreateUserModalProps> = ({ onCreateUser, isLoading }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'viewer' as 'admin' | 'manager' | 'viewer',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleCreateUser = () => {
    const validation = validateForm(formData, userCreationSchema);
    
    if (!validation.isValid) {
      const errorMap: Record<string, string> = {};
      validation.errors.forEach(error => {
        errorMap[error.field] = error.message;
      });
      setErrors(errorMap);
      return;
    }

    // Check if passwords match
    if (formData.password !== formData.confirmPassword) {
      setErrors({ confirmPassword: 'Passwords do not match' });
      return;
    }
    
    onCreateUser({
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email,
      password: formData.password,
      role: formData.role,
    });
    
    // Reset form on success
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: 'viewer',
    });
    setErrors({});
  };

  return (
    <DialogContent className="max-w-md">
      <DialogHeader>
        <DialogTitle>Create New User</DialogTitle>
        <DialogDescription>
          Create a new user account with direct access credentials.
        </DialogDescription>
      </DialogHeader>
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="firstName">First Name</Label>
            <Input 
              id="firstName"
              placeholder="John" 
              value={formData.firstName} 
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              className={errors.firstName ? 'border-red-500' : ''}
            />
            <FormError error={errors.firstName} />
          </div>
          <div>
            <Label htmlFor="lastName">Last Name</Label>
            <Input 
              id="lastName"
              placeholder="Doe" 
              value={formData.lastName} 
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              className={errors.lastName ? 'border-red-500' : ''}
            />
            <FormError error={errors.lastName} />
          </div>
        </div>
        
        <div>
          <Label htmlFor="email">Email Address</Label>
          <Input 
            id="email"
            type="email"
            placeholder="<EMAIL>" 
            value={formData.email} 
            onChange={(e) => handleInputChange('email', e.target.value)}
            className={errors.email ? 'border-red-500' : ''}
          />
          <FormError error={errors.email} />
        </div>

        <div>
          <Label htmlFor="password">Password</Label>
          <div className="relative">
            <Input 
              id="password"
              type={showPassword ? 'text' : 'password'}
              placeholder="Enter password" 
              value={formData.password} 
              onChange={(e) => handleInputChange('password', e.target.value)}
              className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          <FormError error={errors.password} />
        </div>

        <div>
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <div className="relative">
            <Input 
              id="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder="Confirm password" 
              value={formData.confirmPassword} 
              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
              className={errors.confirmPassword ? 'border-red-500 pr-10' : 'pr-10'}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          <FormError error={errors.confirmPassword} />
        </div>
        
        <div>
          <Label htmlFor="role">Role</Label>
          <Select 
            value={formData.role} 
            onValueChange={(value: 'admin' | 'manager' | 'viewer') => handleInputChange('role', value)}
          >
            <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="viewer">
                <div>
                  <div className="font-medium">Viewer</div>
                  <div className="text-sm text-gray-500">Read-only access to all data</div>
                </div>
              </SelectItem>
              <SelectItem value="manager">
                <div>
                  <div className="font-medium">Manager</div>
                  <div className="text-sm text-gray-500">Can edit vendors, contracts, and approve invoices</div>
                </div>
              </SelectItem>
              <SelectItem value="admin">
                <div>
                  <div className="font-medium">Admin</div>
                  <div className="text-sm text-gray-500">Full system access including user management</div>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          <FormError error={errors.role} />
        </div>
      </div>
      <DialogFooter>
        <LoadingButton
          onClick={handleCreateUser}
          isLoading={isLoading}
          loadingText="Creating..."
          className="neumorphic-button-primary"
        >
          Create User
        </LoadingButton>
      </DialogFooter>
    </DialogContent>
  );
};