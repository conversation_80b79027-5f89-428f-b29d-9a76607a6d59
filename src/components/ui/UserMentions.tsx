import React, { useState, useEffect, useRef } from 'react';
import { User } from '../../services/api/users';
import { usersApi } from '../../services/api/users';

interface UserMentionsProps {
  onMention: (user: User) => void;
  trigger?: string;
  className?: string;
}

interface MentionSuggestion {
  user: User;
  highlighted: boolean;
}

const UserMentions: React.FC<UserMentionsProps> = ({
  onMention,
  trigger = '@',
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<MentionSuggestion[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Search users when query changes
  useEffect(() => {
    const searchUsers = async () => {
      if (!searchQuery.trim()) {
        setSuggestions([]);
        setIsOpen(false);
        return;
      }

      setLoading(true);
      try {
        const response = await usersApi.searchUsers(searchQuery);
        const users = response.data || [];
        setSuggestions(users.map(user => ({ user, highlighted: false })));
        setIsOpen(users.length > 0);
        setSelectedIndex(0);
      } catch (error) {
        console.error('Failed to search users:', error);
        setSuggestions([]);
        setIsOpen(false);
      } finally {
        setLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchUsers, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        event.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
      case 'Tab':
        event.preventDefault();
        if (suggestions[selectedIndex]) {
          handleSelectUser(suggestions[selectedIndex].user);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        break;
    }
  };

  // Handle user selection
  const handleSelectUser = (user: User) => {
    onMention(user);
    setIsOpen(false);
    setSearchQuery('');
    setSuggestions([]);
  };

  // Handle input change
  const handleInputChange = (value: string) => {
    if (value.startsWith(trigger)) {
      setSearchQuery(value.slice(1)); // Remove trigger character
    } else {
      setSearchQuery('');
      setIsOpen(false);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* This component is meant to be used with a text input */}
      {/* The parent component should handle the input and call handleInputChange */}
      
      {isOpen && (
        <div 
          ref={dropdownRef}
          className="absolute z-50 w-64 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto"
        >
          {loading ? (
            <div className="p-3 text-center text-gray-500">
              <div className="animate-spin inline-block w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full"></div>
              <span className="ml-2">Searching...</span>
            </div>
          ) : suggestions.length > 0 ? (
            <ul className="py-1">
              {suggestions.map((suggestion, index) => (
                <li
                  key={suggestion.user.id}
                  className={`px-3 py-2 cursor-pointer flex items-center space-x-2 ${
                    index === selectedIndex 
                      ? 'bg-blue-50 text-blue-700' 
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleSelectUser(suggestion.user)}
                >
                  {suggestion.user.avatar ? (
                    <img 
                      src={suggestion.user.avatar} 
                      alt={`${suggestion.user.firstName} ${suggestion.user.lastName}`}
                      className="w-6 h-6 rounded-full"
                    />
                  ) : (
                    <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-600">
                      {suggestion.user.firstName.charAt(0)}{suggestion.user.lastName.charAt(0)}
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">
                      {suggestion.user.firstName} {suggestion.user.lastName}
                    </div>
                    <div className="text-xs text-gray-500 truncate">
                      {suggestion.user.email}
                    </div>
                  </div>
                  <div className="text-xs text-gray-400 capitalize">
                    {suggestion.user.role}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="p-3 text-center text-gray-500 text-sm">
              No users found
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Hook for using mentions in text inputs
export const useMentions = () => {
  const [mentionTrigger, setMentionTrigger] = useState<string | null>(null);
  const [mentionQuery, setMentionQuery] = useState('');

  const handleTextChange = (text: string, cursorPosition: number) => {
    // Find @ symbol before cursor
    const textBeforeCursor = text.slice(0, cursorPosition);
    const lastAtIndex = textBeforeCursor.lastIndexOf('@');
    
    if (lastAtIndex !== -1) {
      const textAfterAt = textBeforeCursor.slice(lastAtIndex + 1);
      // Check if there's no space after @
      if (!textAfterAt.includes(' ')) {
        setMentionTrigger('@');
        setMentionQuery(textAfterAt);
        return;
      }
    }
    
    setMentionTrigger(null);
    setMentionQuery('');
  };

  const insertMention = (user: User, text: string, cursorPosition: number) => {
    const textBeforeCursor = text.slice(0, cursorPosition);
    const lastAtIndex = textBeforeCursor.lastIndexOf('@');
    
    if (lastAtIndex !== -1) {
      const beforeMention = text.slice(0, lastAtIndex);
      const afterCursor = text.slice(cursorPosition);
      const mention = `@${user.firstName} ${user.lastName}`;
      
      return {
        newText: beforeMention + mention + ' ' + afterCursor,
        newCursorPosition: beforeMention.length + mention.length + 1
      };
    }
    
    return { newText: text, newCursorPosition: cursorPosition };
  };

  return {
    mentionTrigger,
    mentionQuery,
    handleTextChange,
    insertMention
  };
};

export default UserMentions;