import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { cn } from '../../lib/utils';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  autoFocus?: boolean;
  maxLength?: number;
  className?: string;
  disabled?: boolean;
}

export interface RichTextEditorRef {
  focus: () => void;
  blur: () => void;
  getContent: () => string;
  setContent: (content: string) => void;
}

const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>((
  { 
    value, 
    onChange, 
    placeholder = 'Start typing...', 
    autoFocus = false, 
    maxLength, 
    className = '', 
    disabled = false 
  }, 
  ref
) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useImperativeHandle(ref, () => ({
    focus: () => textareaRef.current?.focus(),
    blur: () => textareaRef.current?.blur(),
    getContent: () => value,
    setContent: (content: string) => onChange(content)
  }));

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (maxLength && newValue.length > maxLength) {
      return;
    }
    onChange(newValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Handle Ctrl+Enter for submission
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      // Trigger form submission
      const form = e.currentTarget.closest('form');
      if (form) {
        form.requestSubmit();
      }
    }
  };

  return (
    <div className={cn('rich-text-editor', className)}>
      <textarea
        ref={textareaRef}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        autoFocus={autoFocus}
        disabled={disabled}
        className={cn(
          'w-full min-h-[120px] p-3 border border-gray-300 rounded-md',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
          'resize-y transition-colors',
          'placeholder:text-gray-400',
          disabled && 'bg-gray-50 cursor-not-allowed',
          className
        )}
        style={{ fontFamily: 'inherit' }}
      />
      
      {maxLength && (
        <div className="flex justify-end mt-1">
          <span className={cn(
            'text-xs',
            value.length > maxLength * 0.9 ? 'text-red-500' : 'text-gray-500'
          )}>
            {value.length}/{maxLength}
          </span>
        </div>
      )}
    </div>
  );
});

RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor;