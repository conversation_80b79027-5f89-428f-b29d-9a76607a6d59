import React from 'react';
import { AlertTriangle, X } from 'lucide-react';
import { cn } from '../../lib/utils';

interface ErrorMessageProps {
  message: string;
  onDismiss?: () => void;
  className?: string;
  variant?: 'error' | 'warning' | 'info';
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({ 
  message, 
  onDismiss, 
  className = '', 
  variant = 'error' 
}) => {
  const variantClasses = {
    error: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800'
  };

  return (
    <div className={cn(
      'flex items-center justify-between p-3 border rounded-md',
      variantClasses[variant],
      className
    )}>
      <div className="flex items-center space-x-2">
        <AlertTriangle className="w-4 h-4 flex-shrink-0" />
        <span className="text-sm">{message}</span>
      </div>
      
      {onDismiss && (
        <button
          onClick={onDismiss}
          className="ml-2 p-1 hover:bg-black/10 rounded transition-colors"
          aria-label="Dismiss error"
        >
          <X className="w-4 h-4" />
        </button>
      )}
    </div>
  );
};

export default ErrorMessage;