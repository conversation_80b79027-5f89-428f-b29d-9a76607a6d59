import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

interface AuthGuardProps {
  children: React.ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Only redirect if not loading and not authenticated
    if (!isLoading && !isAuthenticated) {
      // Check if we're already on an auth page
      const authPages = ['/login', '/register', '/forgot-password', '/reset-password', '/verify-email', '/verification-pending'];
      const isAuthPage = authPages.some(page => location.pathname.startsWith(page));
      const isPublicPage = location.pathname.startsWith('/rfq/submit/');
      
      if (!isAuthPage && !isPublicPage) {
        navigate('/login', { 
          state: { from: location },
          replace: true 
        });
      }
    }
  }, [isAuthenticated, isLoading, navigate, location]);

  return <>{children}</>;
};