import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Clock,
  Users,
  DollarSign,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Calendar,
  Award,
  AlertTriangle,
  RefreshCw,
  Download,
  Filter
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { RFQApiService } from '@/services/api/rfq';
import { RFQ, RFQAnalytics, RFQStatusStatistics } from '@/types/rfq';
import { formatCurrency } from '@/types/rfq';
import { useToast } from '@/hooks/use-toast';

interface RFQPerformanceMetricsProps {
  rfqId?: number; // If provided, show metrics for specific RFQ
  timeRange?: '7d' | '30d' | '90d' | '1y' | 'all';
  showExport?: boolean;
}

interface PerformanceMetrics {
  totalRFQs: number;
  activeRFQs: number;
  completedRFQs: number;
  cancelledRFQs: number;
  averageResponseRate: number;
  averageResponseTime: number; // in hours
  totalInvitationsSent: number;
  totalSubmissionsReceived: number;
  averageBidAmount: number;
  costSavings: number;
  vendorParticipation: number;
  onTimeCompletionRate: number;
  statusDistribution: RFQStatusStatistics[];
  monthlyTrends: {
    month: string;
    rfqsCreated: number;
    rfqsCompleted: number;
    averageResponseRate: number;
  }[];
  topPerformingVendors: {
    vendorId: number;
    vendorName: string;
    submissionCount: number;
    winRate: number;
    averageResponseTime: number;
  }[];
  categoryBreakdown: {
    category: string;
    count: number;
    averageValue: number;
  }[];
}

export const RFQPerformanceMetrics: React.FC<RFQPerformanceMetricsProps> = ({
  rfqId,
  timeRange = '30d',
  showExport = true
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [rfqAnalytics, setRFQAnalytics] = useState<RFQAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [activeTab, setActiveTab] = useState('overview');
  const { toast } = useToast();

  const loadMetrics = async () => {
    try {
      setLoading(true);
      setError(null);

      if (rfqId) {
        // Load specific RFQ analytics
        const response = await RFQApiService.getRFQAnalytics(rfqId);
        setRFQAnalytics(response.data);
      } else {
        // Load overall performance metrics
        // This would be a new API endpoint for dashboard metrics
        const mockMetrics: PerformanceMetrics = {
          totalRFQs: 156,
          activeRFQs: 23,
          completedRFQs: 98,
          cancelledRFQs: 35,
          averageResponseRate: 78.5,
          averageResponseTime: 48.2,
          totalInvitationsSent: 1247,
          totalSubmissionsReceived: 978,
          averageBidAmount: 15750,
          costSavings: 125000,
          vendorParticipation: 89,
          onTimeCompletionRate: 92.3,
          statusDistribution: [
            { status: 'draft', count: 12 },
            { status: 'sent', count: 8 },
            { status: 'in_progress', count: 15 },
            { status: 'closed', count: 98 },
            { status: 'cancelled', count: 23 }
          ],
          monthlyTrends: [
            { month: 'Jan', rfqsCreated: 18, rfqsCompleted: 15, averageResponseRate: 75.2 },
            { month: 'Feb', rfqsCreated: 22, rfqsCompleted: 19, averageResponseRate: 78.1 },
            { month: 'Mar', rfqsCreated: 25, rfqsCompleted: 21, averageResponseRate: 82.3 },
            { month: 'Apr', rfqsCreated: 19, rfqsCompleted: 18, averageResponseRate: 79.8 },
            { month: 'May', rfqsCreated: 28, rfqsCompleted: 24, averageResponseRate: 85.1 },
            { month: 'Jun', rfqsCreated: 31, rfqsCompleted: 27, averageResponseRate: 88.2 }
          ],
          topPerformingVendors: [
            { vendorId: 1, vendorName: 'TechCorp Solutions', submissionCount: 45, winRate: 67.2, averageResponseTime: 24.5 },
            { vendorId: 2, vendorName: 'Global Supplies Inc', submissionCount: 38, winRate: 58.9, averageResponseTime: 36.2 },
            { vendorId: 3, vendorName: 'Innovation Partners', submissionCount: 42, winRate: 71.4, averageResponseTime: 18.7 }
          ],
          categoryBreakdown: [
            { category: 'IT Services', count: 45, averageValue: 25000 },
            { category: 'Office Supplies', count: 32, averageValue: 5500 },
            { category: 'Consulting', count: 28, averageValue: 45000 },
            { category: 'Equipment', count: 51, averageValue: 18500 }
          ]
        };
        setMetrics(mockMetrics);
      }
    } catch (err) {
      setError('Failed to load performance metrics');
      toast({
        title: "Error",
        description: "Failed to load performance metrics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMetrics();
  }, [rfqId, selectedTimeRange]);

  const exportMetrics = async () => {
    try {
      // This would call an export API endpoint
      toast({
        title: "Export Started",
        description: "Performance metrics export will be available shortly",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export performance metrics",
        variant: "destructive",
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'closed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) {
      return <TrendingUp className="w-4 h-4 text-green-500" />;
    } else if (current < previous) {
      return <TrendingDown className="w-4 h-4 text-red-500" />;
    }
    return <Activity className="w-4 h-4 text-gray-500" />;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-full"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <AlertTriangle className="w-12 h-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Failed to Load Metrics</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={loadMetrics}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Render specific RFQ analytics
  if (rfqId && rfqAnalytics) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">RFQ Performance Analytics</h3>
            <p className="text-muted-foreground">Detailed metrics for this RFQ</p>
          </div>
          {showExport && (
            <Button variant="outline" onClick={exportMetrics}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Response Rate</p>
                  <p className="text-2xl font-bold">{rfqAnalytics.response_rate}%</p>
                </div>
                <Target className="w-8 h-8 text-blue-500" />
              </div>
              <Progress value={rfqAnalytics.response_rate} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Invitations</p>
                  <p className="text-2xl font-bold">{rfqAnalytics.total_invitations}</p>
                </div>
                <Users className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Submissions</p>
                  <p className="text-2xl font-bold">{rfqAnalytics.total_submissions}</p>
                </div>
                <BarChart3 className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg Response Time</p>
                  <p className="text-2xl font-bold">
                    {rfqAnalytics.average_response_time_hours ? 
                      `${Math.round(rfqAnalytics.average_response_time_hours)}h` : 
                      'N/A'
                    }
                  </p>
                </div>
                <Clock className="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {(rfqAnalytics.lowest_bid_amount || rfqAnalytics.highest_bid_amount) && (
          <Card>
            <CardHeader>
              <CardTitle>Bid Analysis</CardTitle>
              <CardDescription>Price range and statistics for received bids</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {rfqAnalytics.lowest_bid_amount && (
                  <div className="text-center">
                    <p className="text-sm font-medium text-muted-foreground">Lowest Bid</p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(rfqAnalytics.lowest_bid_amount, 'USD')}
                    </p>
                  </div>
                )}
                
                {rfqAnalytics.average_bid_amount && (
                  <div className="text-center">
                    <p className="text-sm font-medium text-muted-foreground">Average Bid</p>
                    <p className="text-2xl font-bold">
                      {formatCurrency(rfqAnalytics.average_bid_amount, 'USD')}
                    </p>
                  </div>
                )}
                
                {rfqAnalytics.highest_bid_amount && (
                  <div className="text-center">
                    <p className="text-sm font-medium text-muted-foreground">Highest Bid</p>
                    <p className="text-2xl font-bold text-red-600">
                      {formatCurrency(rfqAnalytics.highest_bid_amount, 'USD')}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  // Render overall dashboard metrics
  if (!metrics) return null;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">RFQ Performance Dashboard</h3>
          <p className="text-muted-foreground">Overall performance metrics and analytics</p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
          {showExport && (
            <Button variant="outline" onClick={exportMetrics}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="vendors">Vendors</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total RFQs</p>
                    <p className="text-2xl font-bold">{metrics.totalRFQs}</p>
                    <p className="text-xs text-muted-foreground">
                      {metrics.activeRFQs} active
                    </p>
                  </div>
                  <BarChart3 className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Response Rate</p>
                    <p className="text-2xl font-bold">{metrics.averageResponseRate}%</p>
                    <div className="flex items-center gap-1 mt-1">
                      {getTrendIcon(metrics.averageResponseRate, 75)}
                      <span className="text-xs text-muted-foreground">vs last period</span>
                    </div>
                  </div>
                  <Target className="w-8 h-8 text-green-500" />
                </div>
                <Progress value={metrics.averageResponseRate} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Avg Response Time</p>
                    <p className="text-2xl font-bold">{Math.round(metrics.averageResponseTime)}h</p>
                    <p className="text-xs text-muted-foreground">
                      {Math.round(metrics.averageResponseTime / 24)} days
                    </p>
                  </div>
                  <Clock className="w-8 h-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Cost Savings</p>
                    <p className="text-2xl font-bold">{formatCurrency(metrics.costSavings, 'USD')}</p>
                    <p className="text-xs text-green-600">
                      +12% vs last period
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Status Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>RFQ Status Distribution</CardTitle>
              <CardDescription>Current status breakdown of all RFQs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {metrics.statusDistribution.map((status) => (
                  <div key={status.status} className="text-center">
                    <Badge className={getStatusColor(status.status)} variant="secondary">
                      {status.status.replace('_', ' ').toUpperCase()}
                    </Badge>
                    <p className="text-2xl font-bold mt-2">{status.count}</p>
                    <p className="text-sm text-muted-foreground">
                      {Math.round((status.count / metrics.totalRFQs) * 100)}%
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Completion Metrics</CardTitle>
                <CardDescription>RFQ completion and timing statistics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">On-time Completion Rate</span>
                  <span className="text-sm font-bold">{metrics.onTimeCompletionRate}%</span>
                </div>
                <Progress value={metrics.onTimeCompletionRate} />
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Vendor Participation</span>
                  <span className="text-sm font-bold">{metrics.vendorParticipation}%</span>
                </div>
                <Progress value={metrics.vendorParticipation} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Volume Metrics</CardTitle>
                <CardDescription>Invitation and submission volumes</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Invitations</p>
                    <p className="text-2xl font-bold">{metrics.totalInvitationsSent}</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-500" />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Submissions</p>
                    <p className="text-2xl font-bold">{metrics.totalSubmissionsReceived}</p>
                  </div>
                  <BarChart3 className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="vendors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Vendors</CardTitle>
              <CardDescription>Vendors with highest participation and win rates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.topPerformingVendors.map((vendor, index) => (
                  <div key={vendor.vendorId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                        <span className="text-sm font-bold">{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium">{vendor.vendorName}</p>
                        <p className="text-sm text-muted-foreground">
                          {vendor.submissionCount} submissions
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{vendor.winRate}% win rate</p>
                      <p className="text-sm text-muted-foreground">
                        {Math.round(vendor.averageResponseTime)}h avg response
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Category Breakdown</CardTitle>
              <CardDescription>RFQ distribution by category and average values</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.categoryBreakdown.map((category) => (
                  <div key={category.category} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">{category.category}</p>
                      <p className="text-sm text-muted-foreground">
                        {category.count} RFQs
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(category.averageValue, 'USD')}</p>
                      <p className="text-sm text-muted-foreground">average value</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RFQPerformanceMetrics;