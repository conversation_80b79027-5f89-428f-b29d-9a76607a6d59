import React, { useState } from 'react';
import { 
  Trash2, 
  Send, 
  Archive, 
  Download,
  CheckSquare,
  Square,
  MoreHorizontal
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface RFQBulkActionsProps {
  selectedCount: number;
  totalCount: number;
  isAllSelected: boolean;
  onSelectAll: (selected: boolean) => void;
  onBulkDelete: () => void;
  onBulkStatusUpdate: (status: string, reason?: string) => void;
  onExport: (format: 'csv' | 'excel') => void;
  loading?: boolean;
}

export const RFQBulkActions: React.FC<RFQBulkActionsProps> = ({
  selectedCount,
  totalCount,
  isAllSelected,
  onSelectAll,
  onBulkDelete,
  onBulkStatusUpdate,
  onExport,
  loading = false
}) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [statusAction, setStatusAction] = useState<{ status: string; label: string } | null>(null);
  const [statusReason, setStatusReason] = useState('');

  const handleStatusUpdate = (status: string, label: string) => {
    setStatusAction({ status, label });
    setStatusReason('');
    setShowStatusDialog(true);
  };

  const confirmStatusUpdate = () => {
    if (statusAction) {
      onBulkStatusUpdate(statusAction.status, statusReason.trim() || undefined);
      setShowStatusDialog(false);
      setStatusAction(null);
      setStatusReason('');
    }
  };

  const confirmDelete = () => {
    onBulkDelete();
    setShowDeleteDialog(false);
  };

  if (selectedCount === 0) {
    return (
      <div className="flex items-center justify-between py-2">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onSelectAll(!isAllSelected)}
            className="flex items-center gap-2"
            disabled={loading || totalCount === 0}
          >
            {isAllSelected ? (
              <CheckSquare className="h-4 w-4" />
            ) : (
              <Square className="h-4 w-4" />
            )}
            Select All ({totalCount})
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onExport('csv')}
            disabled={loading || totalCount === 0}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export CSV
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onExport('excel')}
            disabled={loading || totalCount === 0}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export Excel
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex items-center justify-between py-2 bg-primary/5 px-4 rounded-lg border">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onSelectAll(false)}
            className="flex items-center gap-2"
            disabled={loading}
          >
            <CheckSquare className="h-4 w-4" />
            Clear Selection
          </Button>
          
          <Badge variant="secondary" className="font-medium">
            {selectedCount} selected
          </Badge>
        </div>

        <div className="flex items-center gap-2">
          {/* Quick Actions */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleStatusUpdate('cancelled', 'Cancel')}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <Archive className="h-4 w-4" />
            Cancel
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowDeleteDialog(true)}
            disabled={loading}
            className="flex items-center gap-2 text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
            Delete
          </Button>

          {/* More Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" disabled={loading}>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => handleStatusUpdate('sent', 'Send Invitations')}>
                <Send className="mr-2 h-4 w-4" />
                Send Invitations
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => handleStatusUpdate('closed', 'Close')}>
                <Archive className="mr-2 h-4 w-4" />
                Close RFQs
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem onClick={() => onExport('csv')}>
                <Download className="mr-2 h-4 w-4" />
                Export Selected (CSV)
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => onExport('excel')}>
                <Download className="mr-2 h-4 w-4" />
                Export Selected (Excel)
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete RFQs</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedCount} RFQ{selectedCount !== 1 ? 's' : ''}? 
              This action cannot be undone and will permanently remove the selected RFQs and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete {selectedCount} RFQ{selectedCount !== 1 ? 's' : ''}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Status Update Dialog */}
      <AlertDialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {statusAction?.label} RFQs
            </AlertDialogTitle>
            <AlertDialogDescription>
              You are about to {statusAction?.label.toLowerCase()} {selectedCount} RFQ{selectedCount !== 1 ? 's' : ''}. 
              This will change their status to "{statusAction?.status}".
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="space-y-2">
            <Label htmlFor="reason">Reason (optional)</Label>
            <Textarea
              id="reason"
              placeholder="Enter a reason for this status change..."
              value={statusReason}
              onChange={(e) => setStatusReason(e.target.value)}
              rows={3}
            />
          </div>
          
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setStatusReason('')}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmStatusUpdate}>
              {statusAction?.label} {selectedCount} RFQ{selectedCount !== 1 ? 's' : ''}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default RFQBulkActions;