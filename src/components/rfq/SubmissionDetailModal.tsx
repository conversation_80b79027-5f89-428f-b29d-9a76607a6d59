import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Clock,
  DollarSign,
  FileText,
  Package,
  Calendar,
  User,
  Star,
  Download
} from 'lucide-react';
import { RFQSubmission } from '@/types/rfq';
import { formatCurrency } from '@/types/rfq';

interface SubmissionDetailModalProps {
  submission: RFQSubmission | null;
  isOpen: boolean;
  onClose: () => void;
}

const SubmissionDetailModal: React.FC<SubmissionDetailModalProps> = ({
  submission,
  isOpen,
  onClose
}) => {
  if (!submission) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Avatar className="w-10 h-10">
              <AvatarFallback className="text-sm">
                {getInitials(submission.vendor_name)}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="text-xl font-semibold">{submission.vendor_name}</div>
              <div className="text-sm text-muted-foreground">
                Submission #{submission.id}
              </div>
            </div>
          </DialogTitle>
          <DialogDescription>
            Detailed view of vendor submission and bid items
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-green-600" />
                  <div className="text-sm font-medium">Total Amount</div>
                </div>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(submission.total_amount, submission.currency)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-blue-600" />
                  <div className="text-sm font-medium">Delivery</div>
                </div>
                <div className="text-2xl font-bold text-blue-600">
                  {submission.delivery_days ? `${submission.delivery_days} days` : 'Not specified'}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-yellow-600" />
                  <div className="text-sm font-medium">Performance</div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-2xl font-bold text-yellow-600">
                    {(submission.vendor_performance_score || 0).toFixed(1)}
                  </div>
                  <Progress 
                    value={submission.vendor_performance_score || 0} 
                    className="w-16 h-2" 
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-purple-600" />
                  <div className="text-sm font-medium">Submitted</div>
                </div>
                <div className="text-sm font-bold text-purple-600">
                  {formatDate(submission.submitted_at)}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Vendor Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Vendor Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Vendor Name</label>
                  <div className="text-sm">{submission.vendor_name}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Email</label>
                  <div className="text-sm">{submission.vendor_email}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Category</label>
                  <div className="text-sm">
                    <Badge variant="secondary">{submission.vendor_category}</Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Performance Score</label>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">
                      {(submission.vendor_performance_score || 0).toFixed(1)}/100
                    </span>
                    <Progress 
                      value={submission.vendor_performance_score || 0} 
                      className="w-20 h-2" 
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Bid Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                Bid Items ({submission.bid_items.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Total Price</TableHead>
                    <TableHead>Delivery</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {submission.bid_items.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <div className="font-medium">{item.item_name}</div>
                        {item.specifications && Object.keys(item.specifications).length > 0 && (
                          <div className="text-xs text-muted-foreground mt-1">
                            {Object.entries(item.specifications).map(([key, value]) => (
                              <span key={key} className="mr-2">
                                {key}: {String(value)}
                              </span>
                            ))}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {formatCurrency(item.unit_price, submission.currency)}
                      </TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(item.total_price, submission.currency)}
                      </TableCell>
                      <TableCell>
                        {item.delivery_days ? `${item.delivery_days} days` : 'Standard'}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-muted-foreground">
                          {item.notes || 'No notes'}
                        </div>
                        {item.alternatives && item.alternatives.length > 0 && (
                          <Badge variant="outline" className="mt-1 text-xs">
                            {item.alternatives.length} alternative(s)
                          </Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Terms and Notes */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Payment Terms</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground">
                  {submission.payment_terms || 'Standard payment terms'}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Validity Period</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground">
                  {submission.validity_period} days
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Notes */}
          {submission.additional_notes && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Additional Notes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground whitespace-pre-wrap">
                  {submission.additional_notes}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Attachments */}
          {submission.attachments && submission.attachments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="w-5 h-5" />
                  Attachments ({submission.attachments.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {submission.attachments.map((attachment, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        <div>
                          <div className="text-sm font-medium">{attachment.originalName}</div>
                          <div className="text-xs text-muted-foreground">
                            {(attachment.fileSize / 1024).toFixed(1)} KB
                          </div>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {attachment.mimeType}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SubmissionDetailModal;