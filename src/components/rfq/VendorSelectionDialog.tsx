import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Search,
  Users,
  Plus,
  Check,
  Star,
  Building,
  Mail,
  Phone,
  MapPin,
  Filter
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import VendorApiService from '@/services/vendorApi';
import { Vendor as VendorType } from '@/store/slices/vendorsSlice';

// Update the Vendor interface to match the backend structure
interface Vendor {
  id: number;
  name: string;
  contact_email: string;
  contact_phone?: string;
  category: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  performance_score: number;
  status: 'active' | 'inactive' | 'blacklisted';
  created_at: string;
  certifications?: string[];
  custom_fields?: Record<string, any>;
}

interface VendorSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onVendorsSelected: (vendorIds: number[]) => void;
  excludeVendorIds?: number[]; // Vendors already invited
  title?: string;
  description?: string;
}

export const VendorSelectionDialog: React.FC<VendorSelectionDialogProps> = ({
  isOpen,
  onClose,
  onVendorsSelected,
  excludeVendorIds = [],
  title = "Add Vendors to RFQ",
  description = "Select vendors to invite to this RFQ"
}) => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [filteredVendors, setFilteredVendors] = useState<Vendor[]>([]);
  const [selectedVendorIds, setSelectedVendorIds] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [performanceFilter, setPerformanceFilter] = useState<string>('all');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Mock data - replace with actual API call
  useEffect(() => {
    if (isOpen) {
      loadVendors();
    }
  }, [isOpen]);

  const loadVendors = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Replace mock API call with actual vendor API
      const response = await VendorApiService.getVendors(1, 100, { status: 'active' });
      
      if (response.success && response.data) {
        // Transform the vendor data to match the expected format
        const transformedVendors: Vendor[] = response.data.map((vendor: VendorType) => ({
          id: vendor.id,
          name: vendor.name,
          email: vendor.contact_email,
          phone: vendor.contact_phone,
          category: vendor.category,
          location: vendor.address ? `${vendor.address.city}, ${vendor.address.state}` : 'N/A',
          performance_score: vendor.performance_score || 0,
          status: vendor.status,
          created_at: vendor.created_at,
          contact_person: vendor.custom_fields?.contact_person || 'N/A',
          website: vendor.custom_fields?.website
        }));

        // Filter out already invited vendors
        const availableVendors = transformedVendors.filter(
          vendor => !excludeVendorIds.includes(vendor.id)
        );
        
        setVendors(availableVendors);
        setFilteredVendors(availableVendors);
      } else {
        throw new Error(response.error || 'Failed to load vendors');
      }
    } catch (err) {
      setError('Failed to load vendors');
      console.error('Error loading vendors:', err);
    } finally {
      setLoading(false);
    }
  };

  // Filter vendors based on search and filters
  useEffect(() => {
    let filtered = vendors;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(vendor =>
        vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        vendor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        vendor.contact_person?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(vendor => vendor.category === categoryFilter);
    }

    // Performance filter
    if (performanceFilter !== 'all') {
      switch (performanceFilter) {
        case 'excellent':
          filtered = filtered.filter(vendor => vendor.performance_score >= 90);
          break;
        case 'good':
          filtered = filtered.filter(vendor => vendor.performance_score >= 80 && vendor.performance_score < 90);
          break;
        case 'average':
          filtered = filtered.filter(vendor => vendor.performance_score >= 70 && vendor.performance_score < 80);
          break;
        case 'below_average':
          filtered = filtered.filter(vendor => vendor.performance_score < 70);
          break;
      }
    }

    setFilteredVendors(filtered);
  }, [vendors, searchTerm, categoryFilter, performanceFilter]);

  const handleVendorToggle = (vendorId: number) => {
    setSelectedVendorIds(prev => {
      if (prev.includes(vendorId)) {
        return prev.filter(id => id !== vendorId);
      } else {
        return [...prev, vendorId];
      }
    });
  };

  const handleSelectAll = () => {
    if (selectedVendorIds.length === filteredVendors.length) {
      setSelectedVendorIds([]);
    } else {
      setSelectedVendorIds(filteredVendors.map(vendor => vendor.id));
    }
  };

  const handleConfirm = () => {
    onVendorsSelected(selectedVendorIds);
    onClose();
    setSelectedVendorIds([]);
    setSearchTerm('');
    setCategoryFilter('all');
    setPerformanceFilter('all');
  };

  const handleCancel = () => {
    onClose();
    setSelectedVendorIds([]);
    setSearchTerm('');
    setCategoryFilter('all');
    setPerformanceFilter('all');
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceBadge = (score: number) => {
    if (score >= 90) return { label: 'Excellent', className: 'bg-green-100 text-green-800' };
    if (score >= 80) return { label: 'Good', className: 'bg-blue-100 text-blue-800' };
    if (score >= 70) return { label: 'Average', className: 'bg-yellow-100 text-yellow-800' };
    return { label: 'Below Average', className: 'bg-red-100 text-red-800' };
  };

  const categories = Array.from(new Set(vendors.map(vendor => vendor.category)));

  return (
    <Dialog open={isOpen} onOpenChange={handleCancel}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            {title}
          </DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search and Filters */}
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search vendors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={performanceFilter} onValueChange={setPerformanceFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Performance" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Performance</SelectItem>
                <SelectItem value="excellent">Excellent (90+)</SelectItem>
                <SelectItem value="good">Good (80-89)</SelectItem>
                <SelectItem value="average">Average (70-79)</SelectItem>
                <SelectItem value="below_average">Below Average (&lt;70)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Selection Summary */}
          <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={selectedVendorIds.length === filteredVendors.length && filteredVendors.length > 0}
                  onCheckedChange={handleSelectAll}
                />
                <Label htmlFor="select-all" className="text-sm">
                  Select All ({filteredVendors.length})
                </Label>
              </div>
              
              <div className="text-sm text-muted-foreground">
                {selectedVendorIds.length} selected
              </div>
            </div>

            <div className="text-sm text-muted-foreground">
              {filteredVendors.length} of {vendors.length} vendors shown
            </div>
          </div>

          {/* Vendors List */}
          <ScrollArea className="h-96">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-3">Loading vendors...</span>
              </div>
            ) : error ? (
              <div className="text-center py-12 text-destructive">
                <p>{error}</p>
                <Button variant="outline" onClick={loadVendors} className="mt-4">
                  Try Again
                </Button>
              </div>
            ) : filteredVendors.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No vendors match your search criteria</p>
              </div>
            ) : (
              <div className="space-y-2 pr-4">
                {filteredVendors.map((vendor, index) => {
                  const isSelected = selectedVendorIds.includes(vendor.id);
                  const performanceBadge = getPerformanceBadge(vendor.performance_score);

                  return (
                    <motion.div
                      key={vendor.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        isSelected 
                          ? 'border-primary bg-primary/5' 
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => handleVendorToggle(vendor.id)}
                    >
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleVendorToggle(vendor.id)}
                          className="mt-1"
                        />

                        <Avatar className="w-10 h-10">
                          <AvatarFallback className="text-sm">
                            {getInitials(vendor.name)}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="font-medium truncate">{vendor.name}</h4>
                              <div className="flex items-center gap-2 mt-1">
                                <Badge variant="outline" className="text-xs">
                                  {vendor.category}
                                </Badge>
                                <Badge className={`text-xs ${performanceBadge.className}`}>
                                  <Star className="w-3 h-3 mr-1" />
                                  {performanceBadge.label}
                                </Badge>
                              </div>
                            </div>

                            <div className="text-right">
                              <div className={`text-sm font-medium ${getPerformanceColor(vendor.performance_score)}`}>
                                {vendor.performance_score.toFixed(1)}
                              </div>
                              <Progress 
                                value={vendor.performance_score} 
                                className="w-16 h-2 mt-1" 
                              />
                            </div>
                          </div>

                          <div className="mt-2 space-y-1 text-sm text-muted-foreground">
                            {vendor.contact_person && (
                              <div className="flex items-center gap-1">
                                <Users className="w-3 h-3" />
                                <span>{vendor.contact_person}</span>
                              </div>
                            )}
                            <div className="flex items-center gap-1">
                              <Mail className="w-3 h-3" />
                              <span className="truncate">{vendor.email}</span>
                            </div>
                            {vendor.phone && (
                              <div className="flex items-center gap-1">
                                <Phone className="w-3 h-3" />
                                <span>{vendor.phone}</span>
                              </div>
                            )}
                            {vendor.location && (
                              <div className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                <span>{vendor.location}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}
          </ScrollArea>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button 
            onClick={handleConfirm}
            disabled={selectedVendorIds.length === 0}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add {selectedVendorIds.length} Vendor{selectedVendorIds.length !== 1 ? 's' : ''}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default VendorSelectionDialog;