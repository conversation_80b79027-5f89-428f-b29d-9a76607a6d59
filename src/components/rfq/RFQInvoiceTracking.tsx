import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  FileText,
  DollarSign,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Eye,
  Download,
  ExternalLink,
  TrendingUp,
  Users,
  CreditCard
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { InvoiceApiService } from '@/services/api/invoices';
import { toast } from '@/components/ui/use-toast';

interface RFQInvoiceTrackingProps {
  rfqId: number;
  rfqTitle: string;
}

interface InvoiceStatus {
  invoice_id: number;
  vendor_id: number;
  vendor_name: string;
  amount: number;
  currency: string;
  invoice_status: 'draft' | 'approved' | 'paid' | 'partially_paid' | 'disputed' | 'overdue';
  due_date: string;
  invoice_created_at: string;
  quote_id: number;
  quote_title: string;
  quote_status: string;
  quote_approved_at: string;
  total_paid: number;
  payment_count: number;
  last_payment_date?: string;
  outstanding_amount: number;
}

interface RFQInvoiceAnalytics {
  total_rfqs_with_invoices: number;
  total_rfq_invoices: number;
  unique_vendors_invoiced: number;
  total_invoice_value: number;
  average_invoice_amount: number;
  paid_invoices: number;
  overdue_invoices: number;
  total_paid_amount: number;
  avg_payment_days?: number;
  total_closed_rfqs: number;
  rfq_to_invoice_conversion_rate: number;
}

export const RFQInvoiceTracking: React.FC<RFQInvoiceTrackingProps> = ({ rfqId, rfqTitle }) => {
  const [invoiceStatus, setInvoiceStatus] = useState<InvoiceStatus[]>([]);
  const [analytics, setAnalytics] = useState<RFQInvoiceAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInvoiceData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const [statusResponse, analyticsResponse] = await Promise.all([
          InvoiceApiService.getRFQInvoiceStatus(rfqId),
          InvoiceApiService.getRFQInvoiceAnalytics({ rfqId })
        ]);

        if (statusResponse.success) {
          setInvoiceStatus(statusResponse.data);
        }

        if (analyticsResponse.success) {
          setAnalytics(analyticsResponse.data);
        }
      } catch (err) {
        console.error('Error fetching invoice data:', err);
        setError('Failed to load invoice data');
        toast({
          title: 'Error',
          description: 'Failed to load invoice data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoiceData();
  }, [rfqId]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'approved':
        return <Eye className="w-4 h-4 text-blue-500" />;
      case 'overdue':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'disputed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'partially_paid':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'approved':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'disputed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'partially_paid':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatCurrency = (amount: number | undefined | null, currency: string = 'USD') => {
    if (amount === undefined || amount === null || isNaN(amount)) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
      }).format(0);
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const calculatePaymentProgress = (totalPaid: number, totalAmount: number) => {
    return totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0;
  };

  const handleUpdateStatus = async (invoiceId: number, newStatus: string) => {
    try {
      const response = await InvoiceApiService.updateStatusWithRFQTracking(
        invoiceId,
        newStatus,
        'Status updated from RFQ tracking interface'
      );

      if (response.success) {
        // Refresh the data
        const statusResponse = await InvoiceApiService.getRFQInvoiceStatus(rfqId);
        if (statusResponse.success) {
          setInvoiceStatus(statusResponse.data);
        }

        toast({
          title: 'Success',
          description: 'Invoice status updated successfully',
        });
      }
    } catch (err) {
      console.error('Error updating invoice status:', err);
      toast({
        title: 'Error',
        description: 'Failed to update invoice status',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-muted rounded w-1/4 mb-2"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <AlertTriangle className="w-12 h-12 text-destructive mx-auto mb-4" />
          <p className="text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Invoice Tracking</h3>
          <p className="text-sm text-muted-foreground">
            Track invoices generated from {rfqTitle}
          </p>
        </div>
        {invoiceStatus.length > 0 && (
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        )}
      </div>

      <Tabs defaultValue="status" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="status">Invoice Status</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="status" className="space-y-4">
          {invoiceStatus.length > 0 ? (
            <div className="space-y-4">
              {invoiceStatus.map((invoice, index) => (
                <motion.div
                  key={invoice.invoice_id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-primary/10 rounded-lg">
                            <FileText className="w-5 h-5 text-primary" />
                          </div>
                          <div>
                            <h4 className="font-semibold">Invoice #{invoice.invoice_id}</h4>
                            <p className="text-sm text-muted-foreground">
                              {invoice.vendor_name}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getStatusColor(invoice.invoice_status)}>
                            {getStatusIcon(invoice.invoice_status)}
                            <span className="ml-1 capitalize">{invoice.invoice_status}</span>
                          </Badge>
                          <Button variant="ghost" size="sm">
                            <ExternalLink className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <div className="text-sm text-muted-foreground">Amount</div>
                          <div className="font-semibold">
                            {formatCurrency(invoice.amount, invoice.currency)}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm text-muted-foreground">Due Date</div>
                          <div className="font-medium">{formatDate(invoice.due_date)}</div>
                        </div>
                        <div>
                          <div className="text-sm text-muted-foreground">Paid</div>
                          <div className="font-semibold text-green-600">
                            {formatCurrency(invoice.total_paid, invoice.currency)}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm text-muted-foreground">Outstanding</div>
                          <div className="font-semibold text-red-600">
                            {formatCurrency(invoice.outstanding_amount, invoice.currency)}
                          </div>
                        </div>
                      </div>

                      {invoice.total_paid > 0 && (
                        <div className="mb-4">
                          <div className="flex justify-between text-sm mb-2">
                            <span className="text-muted-foreground">Payment Progress</span>
                            <span className="font-medium">
                              {calculatePaymentProgress(invoice.total_paid, invoice.amount).toFixed(1)}%
                            </span>
                          </div>
                          <Progress 
                            value={calculatePaymentProgress(invoice.total_paid, invoice.amount)} 
                            className="h-2" 
                          />
                        </div>
                      )}

                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <div>
                          Created: {formatDate(invoice.invoice_created_at)}
                        </div>
                        <div className="flex items-center gap-4">
                          {invoice.payment_count > 0 && (
                            <span>{invoice.payment_count} payment(s)</span>
                          )}
                          {invoice.last_payment_date && (
                            <span>Last payment: {formatDate(invoice.last_payment_date)}</span>
                          )}
                        </div>
                      </div>

                      {invoice.quote_approved_at && (
                        <div className="mt-3 pt-3 border-t border-border">
                          <div className="text-sm text-muted-foreground">
                            Generated from quote "{invoice.quote_title}" approved on{' '}
                            {formatDate(invoice.quote_approved_at)}
                          </div>
                        </div>
                      )}

                      <div className="mt-4 flex gap-2">
                        {invoice.invoice_status === 'draft' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleUpdateStatus(invoice.invoice_id, 'approved')}
                          >
                            Approve
                          </Button>
                        )}
                        {invoice.invoice_status === 'approved' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleUpdateStatus(invoice.invoice_id, 'paid')}
                          >
                            Mark as Paid
                          </Button>
                        )}
                        <Button size="sm" variant="ghost">
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Invoices Generated</h3>
                <p className="text-muted-foreground">
                  No invoices have been generated from this RFQ yet.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {analytics && (
            <>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-primary">{analytics.total_rfq_invoices}</div>
                    <div className="text-sm text-muted-foreground">Total Invoices</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{analytics.paid_invoices}</div>
                    <div className="text-sm text-muted-foreground">Paid</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-red-600">{analytics.overdue_invoices}</div>
                    <div className="text-sm text-muted-foreground">Overdue</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{analytics.unique_vendors_invoiced}</div>
                    <div className="text-sm text-muted-foreground">Vendors</div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="w-5 h-5" />
                      Financial Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Invoice Value</span>
                      <span className="font-semibold">
                        {formatCurrency(analytics.total_invoice_value)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Paid</span>
                      <span className="font-semibold text-green-600">
                        {formatCurrency(analytics.total_paid_amount)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Average Invoice</span>
                      <span className="font-semibold">
                        {formatCurrency(analytics.average_invoice_amount)}
                      </span>
                    </div>
                    {analytics.avg_payment_days && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Avg Payment Time</span>
                        <span className="font-semibold">
                          {analytics.avg_payment_days.toFixed(1)} days
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="w-5 h-5" />
                      Conversion Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-muted-foreground">RFQ to Invoice Rate</span>
                        <span className="font-medium">{analytics.rfq_to_invoice_conversion_rate}%</span>
                      </div>
                      <Progress value={analytics.rfq_to_invoice_conversion_rate} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-muted-foreground">Payment Rate</span>
                        <span className="font-medium">
                          {analytics.total_rfq_invoices > 0 
                            ? ((analytics.paid_invoices / analytics.total_rfq_invoices) * 100).toFixed(1)
                            : 0}%
                        </span>
                      </div>
                      <Progress 
                        value={analytics.total_rfq_invoices > 0 
                          ? (analytics.paid_invoices / analytics.total_rfq_invoices) * 100
                          : 0} 
                        className="h-2" 
                      />
                    </div>
                    <div className="pt-2 border-t border-border">
                      <div className="text-sm text-muted-foreground">
                        {analytics.total_rfqs_with_invoices} of {analytics.total_closed_rfqs} closed RFQs generated invoices
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RFQInvoiceTracking;