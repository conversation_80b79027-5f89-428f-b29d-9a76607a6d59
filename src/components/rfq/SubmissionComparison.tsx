import React, { useState, useMemo } from 'react';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Clock,
  Star,
  Download,
  Filter,
  ArrowUpDown,
  Trophy,
  Medal,
  Award
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { RFQSubmission } from '@/types/rfq';
import { formatCurrency } from '@/types/rfq';

interface SubmissionComparisonProps {
  submissions: RFQSubmission[];
  currency: string;
  onViewSubmission?: (submission: RFQSubmission) => void;
  onSelectSubmission?: (submission: RFQSubmission) => void;
  canEdit?: boolean;
}

type SortField = 'price' | 'delivery' | 'performance' | 'submission_date';
type SortOrder = 'asc' | 'desc';

interface ComparisonMetrics {
  bestPrice: number;
  fastestDelivery: number;
  highestRating: number;
  averagePrice: number;
  averageDelivery: number;
  averageRating: number;
}

export const SubmissionComparison: React.FC<SubmissionComparisonProps> = ({
  submissions,
  currency,
  onViewSubmission,
  onSelectSubmission,
  canEdit = false
}) => {
  const [sortField, setSortField] = useState<SortField>('price');
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc');
  const [priceFilter, setPriceFilter] = useState<string>('all');
  const [deliveryFilter, setDeliveryFilter] = useState<string>('all');
  const [ratingFilter, setRatingFilter] = useState<string>('all');

  // Calculate comparison metrics
  const metrics = useMemo((): ComparisonMetrics => {
    if (submissions.length === 0) {
      return {
        bestPrice: 0,
        fastestDelivery: 0,
        highestRating: 0,
        averagePrice: 0,
        averageDelivery: 0,
        averageRating: 0
      };
    }

    const prices = submissions.map(s => s.total_amount);
    const deliveries = submissions.map(s => s.delivery_days || 0);
    const ratings = submissions.map(s => s.vendor_performance_score || 0);

    return {
      bestPrice: Math.min(...prices),
      fastestDelivery: Math.min(...deliveries.filter(d => d > 0)),
      highestRating: Math.max(...ratings),
      averagePrice: prices.reduce((a, b) => a + b, 0) / prices.length,
      averageDelivery: deliveries.reduce((a, b) => a + b, 0) / deliveries.length,
      averageRating: ratings.reduce((a, b) => a + b, 0) / ratings.length
    };
  }, [submissions]);

  // Filter and sort submissions
  const filteredAndSortedSubmissions = useMemo(() => {
    let filtered = [...submissions];

    // Apply filters
    if (priceFilter !== 'all') {
      const threshold = metrics.averagePrice;
      filtered = filtered.filter(s => {
        if (priceFilter === 'below_avg') return s.total_amount < threshold;
        if (priceFilter === 'above_avg') return s.total_amount > threshold;
        if (priceFilter === 'best') return s.total_amount === metrics.bestPrice;
        return true;
      });
    }

    if (deliveryFilter !== 'all') {
      const threshold = metrics.averageDelivery;
      filtered = filtered.filter(s => {
        const delivery = s.delivery_days || 0;
        if (deliveryFilter === 'fast') return delivery <= threshold && delivery > 0;
        if (deliveryFilter === 'slow') return delivery > threshold;
        if (deliveryFilter === 'fastest') return delivery === metrics.fastestDelivery;
        return true;
      });
    }

    if (ratingFilter !== 'all') {
      const threshold = metrics.averageRating;
      filtered = filtered.filter(s => {
        const rating = s.vendor_performance_score || 0;
        if (ratingFilter === 'high') return rating >= threshold;
        if (ratingFilter === 'low') return rating < threshold;
        if (ratingFilter === 'highest') return rating === metrics.highestRating;
        return true;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: number, bValue: number;

      switch (sortField) {
        case 'price':
          aValue = a.total_amount;
          bValue = b.total_amount;
          break;
        case 'delivery':
          aValue = a.delivery_days || 999;
          bValue = b.delivery_days || 999;
          break;
        case 'performance':
          aValue = a.vendor_performance_score || 0;
          bValue = b.vendor_performance_score || 0;
          break;
        case 'submission_date':
          aValue = new Date(a.submitted_at).getTime();
          bValue = new Date(b.submitted_at).getTime();
          break;
        default:
          return 0;
      }

      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });

    return filtered;
  }, [submissions, sortField, sortOrder, priceFilter, deliveryFilter, ratingFilter, metrics]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Trophy className="w-4 h-4 text-yellow-500" />;
      case 2: return <Medal className="w-4 h-4 text-gray-400" />;
      case 3: return <Award className="w-4 h-4 text-amber-600" />;
      default: return <span className="w-4 h-4 flex items-center justify-center text-xs font-medium text-muted-foreground">{rank}</span>;
    }
  };

  const isHighlight = (submission: RFQSubmission, type: 'price' | 'delivery' | 'rating') => {
    switch (type) {
      case 'price':
        return submission.total_amount === metrics.bestPrice;
      case 'delivery':
        return submission.delivery_days === metrics.fastestDelivery;
      case 'rating':
        return submission.vendor_performance_score === metrics.highestRating;
      default:
        return false;
    }
  };

  const exportData = () => {
    const csvData = filteredAndSortedSubmissions.map((submission, index) => ({
      Rank: index + 1,
      Vendor: submission.vendor_name,
      'Total Amount': submission.total_amount,
      Currency: submission.currency,
      'Delivery Days': submission.delivery_days || 'N/A',
      'Performance Score': submission.vendor_performance_score || 'N/A',
      'Submission Date': new Date(submission.submitted_at).toLocaleDateString(),
      'Payment Terms': submission.payment_terms,
      'Validity Period': `${submission.validity_period} days`
    }));

    const csv = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'rfq-submission-comparison.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (submissions.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <DollarSign className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Submissions to Compare</h3>
          <p className="text-muted-foreground">
            Once vendors submit their bids, you'll be able to compare them here.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Best Price</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(metrics.bestPrice, currency)}
                </p>
              </div>
              <TrendingDown className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Fastest Delivery</p>
                <p className="text-2xl font-bold text-blue-600">
                  {metrics.fastestDelivery} days
                </p>
              </div>
              <Clock className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Highest Rating</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {metrics.highestRating.toFixed(1)}
                </p>
              </div>
              <Star className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Detailed Comparison</CardTitle>
              <CardDescription>
                Compare all submissions side-by-side with advanced filtering
              </CardDescription>
            </div>
            <Button onClick={exportData} variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filters:</span>
            </div>
            
            <Select value={priceFilter} onValueChange={setPriceFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Price Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Prices</SelectItem>
                <SelectItem value="best">Best Price</SelectItem>
                <SelectItem value="below_avg">Below Average</SelectItem>
                <SelectItem value="above_avg">Above Average</SelectItem>
              </SelectContent>
            </Select>

            <Select value={deliveryFilter} onValueChange={setDeliveryFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Delivery Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Delivery</SelectItem>
                <SelectItem value="fastest">Fastest</SelectItem>
                <SelectItem value="fast">Fast (&le; Avg)</SelectItem>
                <SelectItem value="slow">Slow (&gt; Avg)</SelectItem>
              </SelectContent>
            </Select>

            <Select value={ratingFilter} onValueChange={setRatingFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Rating Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Ratings</SelectItem>
                <SelectItem value="highest">Highest Rated</SelectItem>
                <SelectItem value="high">High (&ge; Avg)</SelectItem>
                <SelectItem value="low">Low (&lt; Avg)</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setPriceFilter('all');
                setDeliveryFilter('all');
                setRatingFilter('all');
              }}
            >
              Clear Filters
            </Button>
          </div>

          {/* Comparison Table */}
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">Rank</TableHead>
                  <TableHead>Vendor</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50" 
                    onClick={() => handleSort('price')}
                  >
                    <div className="flex items-center gap-1">
                      Total Price
                      <ArrowUpDown className="w-4 h-4" />
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50" 
                    onClick={() => handleSort('delivery')}
                  >
                    <div className="flex items-center gap-1">
                      Delivery
                      <ArrowUpDown className="w-4 h-4" />
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50" 
                    onClick={() => handleSort('performance')}
                  >
                    <div className="flex items-center gap-1">
                      Performance
                      <ArrowUpDown className="w-4 h-4" />
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50" 
                    onClick={() => handleSort('submission_date')}
                  >
                    <div className="flex items-center gap-1">
                      Submitted
                      <ArrowUpDown className="w-4 h-4" />
                    </div>
                  </TableHead>
                  <TableHead>Payment Terms</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAndSortedSubmissions.map((submission, index) => (
                  <TableRow key={submission.id} className="hover:bg-muted/50">
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getRankIcon(index + 1)}
                        <span className="text-sm font-medium">#{index + 1}</span>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="w-8 h-8">
                          <AvatarFallback className="text-xs">
                            {getInitials(submission.vendor_name)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{submission.vendor_name}</div>
                          <div className="text-sm text-muted-foreground">
                            {submission.vendor_category}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className={`font-medium ${
                          isHighlight(submission, 'price') ? 'text-green-600' : ''
                        }`}>
                          {formatCurrency(submission.total_amount, submission.currency)}
                        </span>
                        {isHighlight(submission, 'price') && (
                          <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                            Best
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className={`font-medium ${
                          isHighlight(submission, 'delivery') ? 'text-blue-600' : ''
                        }`}>
                          {submission.delivery_days ? `${submission.delivery_days} days` : 'N/A'}
                        </span>
                        {isHighlight(submission, 'delivery') && (
                          <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700">
                            Fastest
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          <span className={`text-sm font-medium ${
                            isHighlight(submission, 'rating') ? 'text-yellow-600' : ''
                          }`}>
                            {(submission.vendor_performance_score || 0).toFixed(1)}
                          </span>
                          <Star className="w-3 h-3 fill-current text-yellow-400" />
                        </div>
                        {isHighlight(submission, 'rating') && (
                          <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-700">
                            Top
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <span className="text-sm">
                        {new Date(submission.submitted_at).toLocaleDateString()}
                      </span>
                    </TableCell>
                    
                    <TableCell>
                      <span className="text-sm">{submission.payment_terms}</span>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => onViewSubmission?.(submission)}
                        >
                          View
                        </Button>
                        {canEdit && (
                          <Button 
                            variant="default" 
                            size="sm"
                            onClick={() => onSelectSubmission?.(submission)}
                          >
                            Select
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredAndSortedSubmissions.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                No submissions match the current filters.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SubmissionComparison;