import React, { useState, useEffect } from 'react';
import { useForm, Resolver } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { motion } from 'framer-motion';
import * as yup from 'yup';
import {
  FileText,
  Upload,
  Calendar,
  DollarSign,
  Truck,
  AlertCircle,
  CheckCircle,
  Loader2,
  Download,
  X,
  Building,
  Mail,
  Phone,
  Clock,
  Info,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import FileUpload from '@/components/ui/FileUpload';

export interface PublicRFQData {
  rfq_id: number;
  title: string;
  description: string;
  items: RFQItem[];
  due_date: string;
  form_config: FormFieldConfig[];
  terms: string;
  vendor_name: string;
  vendor_email: string;
  invitation_status: string;
  has_existing_submission: boolean;
  existing_submission_id?: number;
  allow_partial_selection: boolean;
  partial_selection_config: {
    enabled: boolean;
    requireVendorConfirmation: boolean;
    confirmationMessage: string;
    instructions: string;
    defaultAllowed: boolean;
  };
}

export interface RFQItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  specifications: Record<string, unknown>;
  category: string;
  estimatedPrice?: number;
}

export interface FormFieldConfig {
  id: string;
  type: 'text' | 'number' | 'date' | 'file' | 'select' | 'textarea';
  label: string;
  required: boolean;
  options?: string[];
  validation?: unknown[];
  itemSpecific: boolean;
}

export interface BidItem {
  rfq_item_id: string;
  item_name: string;
  unit_price: number;
  quantity: number;
  delivery_days?: number;
  specifications?: Record<string, unknown>;
  alternatives?: AlternativeOption[];
  notes?: string;
}

export interface AlternativeOption {
  description: string;
  unitPrice: number;
  totalPrice: number;
  notes: string;
}

type CurrencyType = 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CNY';

export interface BidSubmissionData {
  rfq_id: number;
  vendor_id: number;
  invitation_token: string;
  bidItems: BidItem[];
  bidData: {
    generalTerms: Record<string, unknown>;
    deliverySchedule: Record<string, unknown>;
    paymentTerms: string;
    validityPeriod: number;
    additionalNotes: string;
  };
  attachments: File[];
  currency: CurrencyType;
  delivery_days?: number;
  payment_terms: string;
  validity_period: number;
  additional_notes: string;
  allows_partial_selection?: boolean;
  partial_selection_notes?: string;
}

interface ExistingBidItem {
  rfq_item_id: string;
  item_name: string;
  unit_price: string | number;
  quantity: string | number;
  delivery_days?: string | number | null;
  specifications?: Record<string, unknown>;
  alternatives?: unknown[];
  notes?: string;
}

interface ExistingBidData {
  generalTerms?: Record<string, unknown>;
  deliverySchedule?: Record<string, unknown>;
  paymentTerms?: string;
  validityPeriod?: number;
  additionalNotes?: string;
}

interface ExistingSubmission {
  bid_items?: ExistingBidItem[];
  bid_data?: ExistingBidData;
  currency?: string;
  delivery_days?: number;
  payment_terms?: string;
  validity_period?: number;
  additional_notes?: string;
  allows_partial_selection?: boolean;
  partial_selection_notes?: string;
}

interface PublicSubmissionFormProps {
  rfq: PublicRFQData;
  token: string;
  onSubmit: (bidData: BidSubmissionData) => void;
  isLoading: boolean;
  existingSubmission?: ExistingSubmission;
}

// Validation schema for bid submission
const createBidValidationSchema = (rfq: PublicRFQData) => {
  // Create validation for item-specific custom fields
  const itemSpecificFieldsSchema: Record<string, yup.Schema> = {};
  // Skip core bid item fields that are already validated at the bid item level
  const coreFields = ['unit_price', 'delivery_days', 'notes'];
  rfq.form_config?.filter(field => field.itemSpecific && !coreFields.includes(field.id)).forEach(field => {
    if (field.required) {
      switch (field.type) {
        case 'text':
        case 'textarea':
        case 'select':
          itemSpecificFieldsSchema[field.id] = yup.string().required(`${field.label} is required`);
          break;
        case 'number':
          itemSpecificFieldsSchema[field.id] = yup.number().required(`${field.label} is required`);
          break;
        case 'date':
          itemSpecificFieldsSchema[field.id] = yup.string().required(`${field.label} is required`);
          break;
        case 'file':
          itemSpecificFieldsSchema[field.id] = yup.mixed().required(`${field.label} is required`);
          break;
      }
    } else {
      switch (field.type) {
        case 'text':
        case 'textarea':
        case 'select':
          itemSpecificFieldsSchema[field.id] = yup.string().nullable();
          break;
        case 'number':
          itemSpecificFieldsSchema[field.id] = yup.number().nullable();
          break;
        case 'date':
          itemSpecificFieldsSchema[field.id] = yup.string().nullable();
          break;
        case 'file':
          itemSpecificFieldsSchema[field.id] = yup.mixed().nullable();
          break;
      }
    }
  });

  const bidItemSchema = yup.object({
    rfq_item_id: yup.string().required('Item ID is required'),
    item_name: yup.string().required('Item name is required'),
    unit_price: yup
      .number()
      .required('Unit price is required')
      .min(0, 'Unit price must be positive'),
    quantity: yup
      .number()
      .required('Quantity is required')
      .min(1, 'Quantity must be at least 1')
      .integer('Quantity must be a whole number'),
    delivery_days: yup
      .number()
      .required('Delivery Time (Days) is required')
      .min(1, 'Delivery days must be at least 1')
      .integer('Delivery days must be a whole number'),
    specifications: yup.object(itemSpecificFieldsSchema).default({}),
    alternatives: yup.array().of(
      yup.object({
        description: yup.string().required('Alternative description is required'),
        unitPrice: yup.number().required('Alternative unit price is required').min(0),
        totalPrice: yup.number().required('Alternative total price is required').min(0),
        notes: yup.string().default('')
      })
    ).default([]),
    notes: yup.string().max(1000, 'Notes must be less than 1000 characters').default('')
  });

  return yup.object({
    bidItems: yup
      .array()
      .of(bidItemSchema)
      .min(rfq.items.length, `All ${rfq.items.length} items must be quoted`)
      .max(rfq.items.length, `Cannot quote more than ${rfq.items.length} items`),
    
    bidData: yup.object({
      generalTerms: yup.object((() => {
        const generalFieldsSchema: Record<string, yup.Schema> = {};
        rfq.form_config?.filter(field => !field.itemSpecific).forEach(field => {
          if (field.required) {
            switch (field.type) {
              case 'text':
              case 'textarea':
              case 'select':
                generalFieldsSchema[field.id] = yup.string().required(`${field.label} is required`);
                break;
              case 'number':
                generalFieldsSchema[field.id] = yup.number().required(`${field.label} is required`);
                break;
              case 'date':
                generalFieldsSchema[field.id] = yup.string().required(`${field.label} is required`);
                break;
              case 'file':
                generalFieldsSchema[field.id] = yup.mixed().required(`${field.label} is required`);
                break;
            }
          } else {
            switch (field.type) {
              case 'text':
              case 'textarea':
              case 'select':
                generalFieldsSchema[field.id] = yup.string().nullable();
                break;
              case 'number':
                generalFieldsSchema[field.id] = yup.number().nullable();
                break;
              case 'date':
                generalFieldsSchema[field.id] = yup.string().nullable();
                break;
              case 'file':
                generalFieldsSchema[field.id] = yup.mixed().nullable();
                break;
            }
          }
        });
        return generalFieldsSchema;
      })()).default({}),
      deliverySchedule: yup.object().default({}),
      paymentTerms: yup.string().max(1000, 'Payment terms must be less than 1000 characters').default(''),
      validityPeriod: yup
        .number()
        .min(1, 'Validity period must be at least 1 day')
        .max(365, 'Validity period cannot exceed 365 days')
        .default(30),
      additionalNotes: yup.string().max(5000, 'Additional notes must be less than 5000 characters').default('')
    }).default({}),
    
    currency: yup
      .string()
      .oneOf(['USD', 'EUR', 'GBP', 'JPY', 'CNY'], 'Invalid currency')
      .default('USD'),
    
    delivery_days: yup
      .number()
      .min(0, 'Delivery days must be positive')
      .integer('Delivery days must be a whole number')
      .nullable(),
    
    payment_terms: yup.string().max(1000, 'Payment terms must be less than 1000 characters').default(''),
    
    validity_period: yup
      .number()
      .min(1, 'Validity period must be at least 1 day')
      .max(365, 'Validity period cannot exceed 365 days')
      .default(30),
    
    additional_notes: yup.string().max(5000, 'Additional notes must be less than 5000 characters').default('')
  });
};

export const PublicSubmissionForm: React.FC<PublicSubmissionFormProps> = ({
  rfq,
  token,
  onSubmit,
  isLoading,
  existingSubmission
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [timeRemaining, setTimeRemaining] = useState<string>('');

  const validationSchema = createBidValidationSchema(rfq);
  
  const form = useForm<BidSubmissionData>({
    resolver: yupResolver(validationSchema) as unknown as Resolver<BidSubmissionData>,
    defaultValues: {
      rfq_id: rfq.rfq_id,
      vendor_id: 0, // Will be set from token validation
      invitation_token: token,
      bidItems: rfq.items.map(item => ({
        rfq_item_id: item.id,
        item_name: item.name,
        unit_price: 0,
        quantity: item.quantity,
        delivery_days: 0,
        specifications: {},
        alternatives: [],
        notes: ''
      })),
      bidData: {
        generalTerms: {},
        deliverySchedule: {},
        paymentTerms: '',
        validityPeriod: 30,
        additionalNotes: ''
      },
      attachments: [],
      currency: 'USD',
      delivery_days: 0,
      payment_terms: '',
      validity_period: 30,
      additional_notes: '',
      allows_partial_selection: rfq.partial_selection_config?.defaultAllowed || false,
      partial_selection_notes: ''
    },
  });

  // Calculate time remaining until deadline
  useEffect(() => {
    const updateTimeRemaining = () => {
      const now = new Date();
      const deadline = new Date(rfq.due_date);
      const diff = deadline.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeRemaining('Deadline passed');
        return;
      }

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

      if (days > 0) {
        setTimeRemaining(`${days} day${days > 1 ? 's' : ''}, ${hours} hour${hours > 1 ? 's' : ''}`);
      } else if (hours > 0) {
        setTimeRemaining(`${hours} hour${hours > 1 ? 's' : ''}, ${minutes} minute${minutes > 1 ? 's' : ''}`);
      } else {
        setTimeRemaining(`${minutes} minute${minutes > 1 ? 's' : ''}`);
      }
    };

    updateTimeRemaining();
    const interval = setInterval(updateTimeRemaining, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [rfq.due_date]);

  // Load existing submission data if available
  useEffect(() => {
    if (existingSubmission) {
      const submission = existingSubmission;
      form.reset({
        ...form.getValues(),
        bidItems: submission.bid_items ? submission.bid_items.map((item: ExistingBidItem) => ({
          rfq_item_id: item.rfq_item_id || '',
          item_name: item.item_name || '',
          unit_price: typeof item.unit_price === 'string' ? parseFloat(item.unit_price) : (item.unit_price || 0),
          quantity: typeof item.quantity === 'string' ? parseInt(item.quantity) : (item.quantity || 1),
          delivery_days: item.delivery_days ? (typeof item.delivery_days === 'string' ? parseInt(item.delivery_days) : item.delivery_days) : 0,
          specifications: item.specifications || {},
          alternatives: item.alternatives || [],
          notes: item.notes || ''
        })) : form.getValues().bidItems,
        bidData: submission.bid_data || form.getValues().bidData,
        currency: (submission.currency as CurrencyType) || 'USD',
        delivery_days: submission.delivery_days || null,
        payment_terms: submission.payment_terms || '',
        validity_period: submission.validity_period || 30,
        additional_notes: submission.additional_notes || '',
        allows_partial_selection: submission.allows_partial_selection || false,
        partial_selection_notes: submission.partial_selection_notes || ''
      });
    }
  }, [existingSubmission, form]);

  const handleFileUpload = (files: File[]) => {
    setUploadedFiles(prev => [...prev, ...files]);
    
    // Simulate upload progress
    files.forEach(file => {
      setUploadProgress(prev => ({ ...prev, [file.name]: 0 }));
      
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[file.name] || 0;
          if (currentProgress >= 100) {
            clearInterval(interval);
            return prev;
          }
          return { ...prev, [file.name]: currentProgress + 10 };
        });
      }, 100);
    });
  };

  const removeFile = (fileName: string) => {
    setUploadedFiles(prev => prev.filter(file => file.name !== fileName));
    setUploadProgress(prev => {
      const newProgress = { ...prev };
      delete newProgress[fileName];
      return newProgress;
    });
  };

  const calculateItemTotal = (unitPrice: number, quantity: number) => {
    return unitPrice * quantity;
  };

  const calculateGrandTotal = () => {
    const bidItems = form.watch('bidItems');
    return bidItems.reduce((total, item) => {
      return total + calculateItemTotal(item.unit_price, item.quantity);
    }, 0);
  };

  const handleSubmit = async (data: BidSubmissionData) => {
    console.log('handleSubmit called with data:', data);
    try {
      const submissionData = {
        ...data,
        attachments: uploadedFiles
      };
      console.log('Calling onSubmit with:', submissionData);
      await onSubmit(submissionData);
    } catch (error) {
      console.error('Submission error:', error);
    }
  };

  const isDeadlinePassed = new Date() > new Date(rfq.due_date);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            RFQ Submission Portal
          </h1>
          <p className="text-gray-600">
            Submit your bid for: <span className="font-semibold">{rfq.title}</span>
          </p>
        </motion.div>

        {/* RFQ Information Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="card-neumorphic mb-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="w-5 h-5 text-primary" />
                <span>RFQ Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Building className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm">
                    <strong>Vendor:</strong> {rfq.vendor_name}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm">
                    <strong>Email:</strong> {rfq.vendor_email}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm">
                    <strong>Deadline:</strong> {new Date(rfq.due_date).toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm">
                    <strong>Time Remaining:</strong> 
                    <Badge 
                      variant={timeRemaining === 'Deadline passed' ? 'destructive' : 'secondary'}
                      className="ml-1"
                    >
                      {timeRemaining}
                    </Badge>
                  </span>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-2">Description</h4>
                <p className="text-sm text-muted-foreground">{rfq.description}</p>
              </div>

              {rfq.terms && (
                <div>
                  <h4 className="font-medium mb-2">Terms & Conditions</h4>
                  <p className="text-sm text-muted-foreground">{rfq.terms}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Deadline Warning */}
        {isDeadlinePassed && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <Alert className="mb-6 border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                The submission deadline has passed. You can no longer submit or modify your bid.
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* Existing Submission Notice */}
        {rfq.has_existing_submission && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <Alert className="mb-6 border-blue-200 bg-blue-50">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                You have already submitted a bid for this RFQ. You can modify your submission until the deadline.
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* Submission Form */}
        {!isDeadlinePassed && (
          <Form {...form}>
            <form onSubmit={(e) => {
              console.log('Form onSubmit triggered', e);
              return form.handleSubmit(handleSubmit)(e);
            }} className="space-y-6">
              {/* Items to Quote */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="card-neumorphic">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <DollarSign className="w-5 h-5 text-primary" />
                      <span>Items to Quote</span>
                    </CardTitle>
                    <CardDescription>
                      Provide pricing and details for each item
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {rfq.items.map((item, index) => (
                      <div key={item.id} className="border rounded-lg p-4 space-y-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium text-lg">{item.name}</h4>
                            <p className="text-sm text-muted-foreground">{item.description}</p>
                            <p className="text-sm">
                              <strong>Quantity:</strong> {item.quantity}
                              {item.category && (
                                <span className="ml-4">
                                  <strong>Category:</strong> {item.category}
                                </span>
                              )}
                            </p>
                          </div>
                          <Badge variant="outline">Item {index + 1}</Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name={`bidItems.${index}.unit_price`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Unit Price <span className="text-destructive">*</span></FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    placeholder="0.00"
                                    className="input-neumorphic"
                                    {...field}
                                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`bidItems.${index}.delivery_days`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Delivery Days <span className="text-destructive">*</span></FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    placeholder="e.g., 7"
                                    className="input-neumorphic"
                                    {...field}
                                    onChange={(e) => {
                                       const value = e.target.value;
                                       field.onChange(value === '' ? 0 : parseInt(value) || 0);
                                     }}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Days to deliver this item
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="flex items-end">
                            <div className="w-full">
                              <FormLabel>Total Price</FormLabel>
                              <div className="input-neumorphic bg-muted flex items-center justify-center h-10">
                                <span className="font-medium">
                                  ${calculateItemTotal(
                                    form.watch(`bidItems.${index}.unit_price`) || 0,
                                    item.quantity
                                  ).toFixed(2)}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <FormField
                          control={form.control}
                          name={`bidItems.${index}.notes`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Notes</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Any additional notes or specifications for this item..."
                                  className="input-neumorphic"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Item-Specific Custom Fields - Only show truly custom fields, not duplicates */}
                        {rfq.form_config && rfq.form_config.filter(field => 
                          field.itemSpecific && 
                          !['Unit Price', 'Delivery Time (Days)', 'Additional Notes'].includes(field.label)
                        ).length > 0 && (
                          <div className="col-span-full">
                            <h5 className="font-medium text-foreground mb-3 border-t pt-4">Additional Item Information</h5>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {rfq.form_config.filter(field => 
                                field.itemSpecific && 
                                !['Unit Price', 'Delivery Time (Days)', 'Additional Notes'].includes(field.label)
                              ).map((field) => (
                                <FormField
                                  key={`${item.id}-${field.id}`}
                                  control={form.control}
                                  name={`bidItems.${index}.specifications.${field.id}`}
                                  render={({ field: formField }) => (
                                    <FormItem>
                                      <FormLabel>
                                        {field.label} {field.required && <span className="text-destructive">*</span>}
                                      </FormLabel>
                                      <FormControl>
                                        {(() => {
                                          switch (field.type) {
                                            case 'text':
                                              return (
                                                <Input
                                                  placeholder={`Enter ${field.label.toLowerCase()}`}
                                                  className="input-neumorphic"
                                                  {...formField}
                                                  value={formField.value as string || ''}
                                                />
                                              );
                                            case 'number':
                                              return (
                                                <Input
                                                  type="number"
                                                  placeholder="0"
                                                  className="input-neumorphic"
                                                  {...formField}
                                                  value={formField.value as string || ''}
                                                  onChange={(e) => formField.onChange(parseFloat(e.target.value) || 0)}
                                                />
                                              );
                                            case 'date':
                                              return (
                                                <Input
                                                  type="date"
                                                  className="input-neumorphic"
                                                  {...formField}
                                                  value={formField.value as string || ''}
                                                />
                                              );
                                            case 'textarea':
                                              return (
                                                <Textarea
                                                  placeholder={`Enter ${field.label.toLowerCase()}`}
                                                  className="input-neumorphic"
                                                  {...formField}
                                                  value={formField.value as string || ''}
                                                />
                                              );
                                            case 'select':
                                              return field.options ? (
                                                <select
                                                  className="input-neumorphic w-full px-3 py-2 border border-input bg-background text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                                                  {...formField}
                                                  value={formField.value as string || ''}
                                                >
                                                  <option value="">Select {field.label.toLowerCase()}</option>
                                                  {field.options.map((option, index) => (
                                                    <option key={index} value={option}>
                                                      {option}
                                                    </option>
                                                  ))}
                                                </select>
                                              ) : null;
                                            case 'file':
                                              return (
                                                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 text-center">
                                                  <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                                                  <p className="text-sm text-muted-foreground">Upload {field.label.toLowerCase()}</p>
                                                  <Input
                                                    type="file"
                                                    className="mt-2"
                                                    onChange={(e) => formField.onChange(e.target.files?.[0] || null)}
                                                    onBlur={formField.onBlur}
                                                    name={formField.name}
                                                    ref={formField.ref}
                                                  />
                                                </div>
                                              );
                                            default:
                                              return null;
                                          }
                                        })()} 
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}

                    {/* Grand Total */}
                    <div className="border-t pt-4">
                      <div className="flex justify-between items-center text-lg font-semibold">
                        <span>Grand Total:</span>
                        <span className="text-primary">
                          ${calculateGrandTotal().toFixed(2)} {form.watch('currency')}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Additional Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="card-neumorphic">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Truck className="w-5 h-5 text-primary" />
                      <span>Additional Information</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="currency"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Currency</FormLabel>
                            <FormControl>
                              <select
                                className="input-neumorphic w-full"
                                {...field}
                              >
                                <option value="USD">USD ($)</option>
                                <option value="EUR">EUR (€)</option>
                                <option value="GBP">GBP (£)</option>
                                <option value="JPY">JPY (¥)</option>
                                <option value="CNY">CNY (¥)</option>
                              </select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="validity_period"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Quote Validity (Days)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                max="365"
                                className="input-neumorphic"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 30)}
                              />
                            </FormControl>
                            <FormDescription>
                              How long is this quote valid?
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="payment_terms"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Payment Terms</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="e.g., Net 30, 2/10 Net 30, Payment on delivery..."
                              className="input-neumorphic"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Specify your payment terms and conditions
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="additional_notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Additional Notes</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Any additional information, terms, or conditions..."
                              className="input-neumorphic min-h-[100px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* General Custom Fields */}
                    {rfq.form_config.filter(field => !field.itemSpecific).length > 0 && (
                      <div>
                        <Separator className="my-6" />
                        <h4 className="font-medium text-foreground mb-4">Custom Information</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {rfq.form_config.filter(field => !field.itemSpecific).map((field) => (
                            <FormField
                              key={field.id}
                              control={form.control}
                              name={`bidData.generalTerms.${field.id}`}
                              render={({ field: formField }) => (
                                <FormItem>
                                  <FormLabel>
                                    {field.label} {field.required && <span className="text-destructive">*</span>}
                                  </FormLabel>
                                  <FormControl>
                                    {(() => {
                                      switch (field.type) {
                                        case 'text':
                                          return (
                                            <Input
                                              placeholder={`Enter ${field.label.toLowerCase()}`}
                                              className="input-neumorphic"
                                              {...formField}
                                              value={formField.value as string || ''}
                                            />
                                          );
                                        case 'number':
                                          return (
                                            <Input
                                              type="number"
                                              placeholder="0"
                                              className="input-neumorphic"
                                              {...formField}
                                              value={formField.value as string || ''}
                                              onChange={(e) => formField.onChange(parseFloat(e.target.value) || 0)}
                                            />
                                          );
                                        case 'date':
                                          return (
                                            <Input
                                              type="date"
                                              className="input-neumorphic"
                                              {...formField}
                                              value={formField.value as string || ''}
                                            />
                                          );
                                        case 'textarea':
                                          return (
                                            <Textarea
                                              placeholder={`Enter ${field.label.toLowerCase()}`}
                                              className="input-neumorphic"
                                              {...formField}
                                              value={formField.value as string || ''}
                                            />
                                          );
                                        case 'select':
                                          return field.options ? (
                                            <select
                                              className="input-neumorphic w-full px-3 py-2 border border-input bg-background text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                                              {...formField}
                                              value={formField.value as string || ''}
                                            >
                                              <option value="">Select {field.label.toLowerCase()}</option>
                                              {field.options.map((option, index) => (
                                                <option key={index} value={option}>
                                                  {option}
                                                </option>
                                              ))}
                                            </select>
                                          ) : null;
                                        case 'file':
                                          return (
                                            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 text-center">
                                              <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                                              <p className="text-sm text-muted-foreground">Upload {field.label.toLowerCase()}</p>
                                              <Input
                                                type="file"
                                                className="mt-2"
                                                onChange={(e) => formField.onChange(e.target.files?.[0] || null)}
                                                onBlur={formField.onBlur}
                                                name={formField.name}
                                                ref={formField.ref}
                                              />
                                            </div>
                                          );
                                        default:
                                          return null;
                                      }
                                    })()}
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>



              {/* Partial Selection Question */}
              {rfq.allow_partial_selection && rfq.partial_selection_config && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <Card className="card-neumorphic border-primary/20">
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-primary" />
                        <span>Partial Selection Option</span>
                      </CardTitle>
                      <CardDescription>
                        {rfq.partial_selection_config.instructions}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {rfq.partial_selection_config.requireVendorConfirmation ? (
                        <FormField
                          control={form.control}
                          name="allows_partial_selection"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-base font-medium">
                                {rfq.partial_selection_config.confirmationMessage}
                              </FormLabel>
                              <FormControl>
                                <div className="flex items-center space-x-4">
                                  <label className="flex items-center space-x-2 cursor-pointer">
                                    <input
                                      type="radio"
                                      value="true"
                                      checked={field.value === true}
                                      onChange={() => field.onChange(true)}
                                      className="w-4 h-4 text-primary"
                                    />
                                    <span>Yes, I allow individual item purchases</span>
                                  </label>
                                  <label className="flex items-center space-x-2 cursor-pointer">
                                    <input
                                      type="radio"
                                      value="false"
                                      checked={field.value === false}
                                      onChange={() => field.onChange(false)}
                                      className="w-4 h-4 text-primary"
                                    />
                                    <span>No, only complete submission</span>
                                  </label>
                                </div>
                              </FormControl>
                              <FormDescription>
                                This allows the buyer to select individual items from your quote at the rates you've provided.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      ) : (
                        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                          <div className="flex items-center space-x-2 mb-2">
                            <CheckCircle className="w-5 h-5 text-green-600" />
                            <span className="font-medium text-green-800">Partial Selection Enabled</span>
                          </div>
                          <p className="text-sm text-green-700">
                            This RFQ allows partial selection of items. The buyer can select individual items from your submission at the rates you provide.
                          </p>
                        </div>
                      )}

                      <FormField
                        control={form.control}
                        name="partial_selection_notes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Additional Terms for Partial Selection (Optional)</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Any specific terms, minimum quantities, or conditions for partial selection..."
                                className="input-neumorphic"
                                rows={3}
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Specify any conditions or minimum requirements for individual item purchases.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* File Attachments */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Card className="card-neumorphic">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Upload className="w-5 h-5 text-primary" />
                      <span>Supporting Documents</span>
                    </CardTitle>
                    <CardDescription>
                      Upload any supporting documents, specifications, or certifications
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <FileUpload
                      onFilesSelected={handleFileUpload}
                      maxFiles={10}
                      maxFileSize={10 * 1024 * 1024} // 10MB
                      acceptedTypes={[
                        'application/pdf',
                        'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'application/vnd.ms-excel',
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'image/*',
                        'text/plain',
                        'text/csv'
                      ]}
                      multiple={true}
                      disabled={isLoading}
                    />

                    {/* Uploaded Files List */}
                    {uploadedFiles.length > 0 && (
                      <div className="mt-4 space-y-2">
                        <h4 className="font-medium text-sm">Uploaded Files:</h4>
                        {uploadedFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-muted rounded-lg"
                          >
                            <div className="flex items-center space-x-2">
                              <FileText className="w-4 h-4 text-muted-foreground" />
                              <span className="text-sm">{file.name}</span>
                              <span className="text-xs text-muted-foreground">
                                ({(file.size / 1024 / 1024).toFixed(2)} MB)
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              {uploadProgress[file.name] !== undefined && uploadProgress[file.name] < 100 && (
                                <div className="w-16 bg-gray-200 rounded-full h-2">
                                  <div
                                    className="bg-primary h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${uploadProgress[file.name]}%` }}
                                  />
                                </div>
                              )}
                              {uploadProgress[file.name] === 100 && (
                                <CheckCircle className="w-4 h-4 text-green-500" />
                              )}
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFile(file.name)}
                                disabled={isLoading}
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>

              {/* Submit Button */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="flex justify-center pt-6"
              >
                <Button
                  type="submit"
                  disabled={isLoading || isDeadlinePassed}
                  className="btn-neumorphic gradient-primary text-primary-foreground px-12 py-3 text-lg"
                  onClick={async (e) => {
                    console.log('Submit button clicked', e);
                    console.log('RFQ form_config:', rfq.form_config);
                    console.log('Form state:', form.formState);
                    console.log('Form errors:', form.formState.errors);
                    console.log('Form values:', form.getValues());
                    console.log('Form is valid:', form.formState.isValid);
                    console.log('Detailed errors:', JSON.stringify(form.formState.errors, null, 2));
                    
                    // Manually trigger validation to see all errors
                    const isValid = await form.trigger();
                    console.log('Manual validation result:', isValid);
                    console.log('Errors after manual validation:', JSON.stringify(form.formState.errors, null, 2));
                    
                    if (!isValid) {
                      console.log('VALIDATION FAILED - This is why handleSubmit is not called');
                      console.log('First error field:', Object.keys(form.formState.errors)[0]);
                    }
                  }}
                >
                  {isLoading && <Loader2 className="w-5 h-5 mr-2 animate-spin" />}
                  {rfq.has_existing_submission ? 'Update Submission' : 'Submit Bid'}
                </Button>
              </motion.div>
            </form>
          </Form>
        )}

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7 }}
          className="text-center mt-12 text-sm text-muted-foreground"
        >
          <p>
            Need help? Contact support at{' '}
            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              <EMAIL>
            </a>
          </p>
        </motion.div>
      </div>
    </div>
  );
};