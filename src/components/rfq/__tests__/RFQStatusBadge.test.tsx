import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import RFQStatusBadge from '../RFQStatusBadge';
import { RFQStatus } from '@/types/rfq';

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

describe('RFQStatusBadge', () => {
  const mockOnStatusChange = vi.fn();

  beforeEach(() => {
    mockOnStatusChange.mockClear();
  });

  it('renders status badge with correct label', () => {
    render(
      <RFQStatusBadge 
        status="draft" 
        showIcon={true}
      />
    );

    expect(screen.getByText('Draft')).toBeInTheDocument();
  });

  it('shows dropdown when transitions are allowed', () => {
    render(
      <RFQStatusBadge 
        status="draft" 
        allowTransitions={true}
        rfqId={1}
        onStatusChange={mockOnStatusChange}
      />
    );

    const badge = screen.getByRole('button');
    expect(badge).toBeInTheDocument();
  });

  it('does not show dropdown when transitions are disabled', () => {
    render(
      <RFQStatusBadge 
        status="draft" 
        allowTransitions={false}
      />
    );

    const badge = screen.getByText('Draft');
    expect(badge).not.toHaveAttribute('role', 'button');
  });

  it('calls onStatusChange when status is changed', async () => {
    render(
      <RFQStatusBadge 
        status="draft" 
        allowTransitions={true}
        rfqId={1}
        onStatusChange={mockOnStatusChange}
      />
    );

    // Click the badge to open dropdown
    const badge = screen.getByRole('button');
    fireEvent.click(badge);

    // Wait for dropdown to appear and click "Send to Vendors"
    await waitFor(() => {
      const sendOption = screen.getByText('Send to Vendors');
      fireEvent.click(sendOption);
    });

    await waitFor(() => {
      expect(mockOnStatusChange).toHaveBeenCalledWith('sent', undefined);
    });
  });

  it('shows confirmation dialog for destructive actions', async () => {
    render(
      <RFQStatusBadge 
        status="sent" 
        allowTransitions={true}
        rfqId={1}
        onStatusChange={mockOnStatusChange}
      />
    );

    // Click the badge to open dropdown
    const badge = screen.getByRole('button');
    fireEvent.click(badge);

    // Click "Cancel RFQ"
    await waitFor(() => {
      const cancelOption = screen.getByText('Cancel RFQ');
      fireEvent.click(cancelOption);
    });

    // Should show confirmation dialog
    await waitFor(() => {
      expect(screen.getByText('Confirm Status Change')).toBeInTheDocument();
    });
  });

  it('handles different status transitions correctly', () => {
    const testCases: Array<{ status: RFQStatus; expectedTransitions: RFQStatus[] }> = [
      { status: 'draft', expectedTransitions: ['sent', 'cancelled'] },
      { status: 'sent', expectedTransitions: ['in_progress', 'cancelled'] },
      { status: 'in_progress', expectedTransitions: ['closed', 'cancelled'] },
      { status: 'closed', expectedTransitions: ['in_progress'] },
      { status: 'cancelled', expectedTransitions: ['draft'] }
    ];

    testCases.forEach(({ status, expectedTransitions }) => {
      const { unmount } = render(
        <RFQStatusBadge 
          status={status} 
          allowTransitions={true}
          rfqId={1}
          onStatusChange={mockOnStatusChange}
        />
      );

      const badge = screen.getByRole('button');
      fireEvent.click(badge);

      expectedTransitions.forEach(transition => {
        // This would need more specific testing based on the transition labels
        // For now, just verify the dropdown appears
        expect(screen.getByText('Change Status')).toBeInTheDocument();
      });

      unmount();
    });
  });

  it('disables transitions when disabled prop is true', () => {
    render(
      <RFQStatusBadge 
        status="draft" 
        allowTransitions={true}
        disabled={true}
        rfqId={1}
        onStatusChange={mockOnStatusChange}
      />
    );

    const badge = screen.getByRole('button');
    expect(badge).toBeDisabled();
  });

  it('shows correct icons for different statuses', () => {
    const statuses: RFQStatus[] = ['draft', 'sent', 'in_progress', 'closed', 'cancelled'];
    
    statuses.forEach(status => {
      const { unmount } = render(
        <RFQStatusBadge 
          status={status} 
          showIcon={true}
        />
      );

      // Verify the badge renders (icon testing would require more specific selectors)
      expect(screen.getByText(status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '))).toBeInTheDocument();
      
      unmount();
    });
  });
});