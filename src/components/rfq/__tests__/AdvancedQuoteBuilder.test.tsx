import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AdvancedQuoteBuilder } from '../AdvancedQuoteBuilder';
import { QuoteApiService } from '@/services/api/quotes';
import { RFQApiService } from '@/services/api/rfq';

// Mock the API services
jest.mock('@/services/api/quotes');
jest.mock('@/services/api/rfq');
jest.mock('@/hooks/use-toast');

// Mock UI components
jest.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div data-testid="dialog-content">{children}</div>,
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <h2 data-testid="dialog-title">{children}</h2>,
  DialogDescription: ({ children }: any) => <p data-testid="dialog-description">{children}</p>,
  DialogFooter: ({ children }: any) => <div data-testid="dialog-footer">{children}</div>,
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} {...props}>{children}</button>
  ),
}));

jest.mock('@/components/ui/scroll-area', () => ({
  ScrollArea: ({ children }: any) => <div data-testid="scroll-area">{children}</div>,
}));

const mockRFQData = {
  id: 1,
  title: 'Test RFQ',
  allow_partial_selection: true,
  partial_selection_config: {
    enabled: true,
    requireVendorConfirmation: true,
    confirmationMessage: 'Do you allow partial selection?',
    instructions: 'Please confirm partial selection',
    defaultAllowed: false
  },
  items: [
    {
      id: '1',
      name: 'Test Item 1',
      description: 'Description 1',
      quantity: 10,
      unit: 'pieces',
      category: 'Electronics'
    },
    {
      id: '2',
      name: 'Test Item 2',
      description: 'Description 2',
      quantity: 5,
      unit: 'kg',
      category: 'Materials'
    }
  ]
};

const mockSubmissions = [
  {
    id: 1,
    vendor_id: 1,
    vendor_name: 'Vendor A',
    vendor_performance_score: 85,
    total_amount: 1000,
    currency: 'USD',
    allows_partial_selection: true,
    partial_selection_notes: 'Minimum order quantities apply',
    bid_items: [
      {
        id: 1,
        rfq_item_id: '1',
        item_name: 'Test Item 1',
        unit_price: 50,
        quantity: 10,
        total_price: 500,
        delivery_days: 7,
        specifications: {},
        notes: 'High quality'
      },
      {
        id: 2,
        rfq_item_id: '2',
        item_name: 'Test Item 2',
        unit_price: 100,
        quantity: 5,
        total_price: 500,
        delivery_days: 10,
        specifications: {},
        notes: 'Premium grade'
      }
    ]
  },
  {
    id: 2,
    vendor_id: 2,
    vendor_name: 'Vendor B',
    vendor_performance_score: 92,
    total_amount: 900,
    currency: 'USD',
    allows_partial_selection: false,
    bid_items: [
      {
        id: 3,
        rfq_item_id: '1',
        item_name: 'Test Item 1',
        unit_price: 45,
        quantity: 10,
        total_price: 450,
        delivery_days: 5,
        specifications: {},
        notes: 'Standard quality'
      },
      {
        id: 4,
        rfq_item_id: '2',
        item_name: 'Test Item 2',
        unit_price: 90,
        quantity: 5,
        total_price: 450,
        delivery_days: 8,
        specifications: {},
        notes: 'Good quality'
      }
    ]
  }
];

const mockQuoteApiService = QuoteApiService as jest.Mocked<typeof QuoteApiService>;
const mockRFQApiService = RFQApiService as jest.Mocked<typeof RFQApiService>;

describe('AdvancedQuoteBuilder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockQuoteApiService.getSubmissionsForAdvancedQuote.mockResolvedValue({
      success: true,
      data: {
        rfq: mockRFQData,
        submissions: mockSubmissions
      }
    });

    mockQuoteApiService.calculateQuotePricing.mockResolvedValue({
      success: true,
      data: {
        subtotal: 1000,
        totalCommission: 100,
        totalMargin: 100,
        finalAmount: 1100,
        itemBreakdown: []
      }
    });

    mockRFQApiService.searchAccounts.mockResolvedValue({
      success: true,
      data: [
        {
          id: 1,
          name: 'Test Account',
          type: 'Enterprise',
          industry: 'Technology',
          location: 'San Francisco',
          activeOpportunities: 5
        }
      ]
    });
  });

  it('renders dialog when open', () => {
    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={true}
        onClose={jest.fn()}
        onQuoteGenerated={jest.fn()}
      />
    );

    expect(screen.getByTestId('dialog')).toBeInTheDocument();
    expect(screen.getByTestId('dialog-title')).toHaveTextContent('Generate Advanced Quote');
  });

  it('does not render when closed', () => {
    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={false}
        onClose={jest.fn()}
        onQuoteGenerated={jest.fn()}
      />
    );

    expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
  });

  it('loads RFQ data and submissions when opened', async () => {
    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={true}
        onClose={jest.fn()}
        onQuoteGenerated={jest.fn()}
      />
    );

    await waitFor(() => {
      expect(mockQuoteApiService.getSubmissionsForAdvancedQuote).toHaveBeenCalledWith(1);
    });
  });

  it('displays loading state initially', () => {
    mockQuoteApiService.getSubmissionsForAdvancedQuote.mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={true}
        onClose={jest.fn()}
        onQuoteGenerated={jest.fn()}
      />
    );

    expect(screen.getByText('Loading RFQ data...')).toBeInTheDocument();
  });

  it('displays step navigation', async () => {
    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={true}
        onClose={jest.fn()}
        onQuoteGenerated={jest.fn()}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Select Items')).toBeInTheDocument();
      expect(screen.getByText('Configure Quote')).toBeInTheDocument();
      expect(screen.getByText('Review & Generate')).toBeInTheDocument();
    });
  });

  it('shows vendor submissions with partial selection indicators', async () => {
    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={true}
        onClose={jest.fn()}
        onQuoteGenerated={jest.fn()}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Vendor A')).toBeInTheDocument();
      expect(screen.getByText('Vendor B')).toBeInTheDocument();
      expect(screen.getByText('Partial OK')).toBeInTheDocument();
      expect(screen.getByText('Whole Only')).toBeInTheDocument();
    });
  });

  it('allows selection of whole submissions', async () => {
    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={true}
        onClose={jest.fn()}
        onQuoteGenerated={jest.fn()}
      />
    );

    await waitFor(() => {
      const selectAllCheckbox = screen.getAllByLabelText('Select All')[0];
      fireEvent.click(selectAllCheckbox);
    });

    // Should show selected items
    await waitFor(() => {
      expect(screen.getByText('Selected Items (1)')).toBeInTheDocument();
    });
  });

  it('calculates pricing when selections change', async () => {
    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={true}
        onClose={jest.fn()}
        onQuoteGenerated={jest.fn()}
      />
    );

    await waitFor(() => {
      const selectAllCheckbox = screen.getAllByLabelText('Select All')[0];
      fireEvent.click(selectAllCheckbox);
    });

    await waitFor(() => {
      expect(mockQuoteApiService.calculateQuotePricing).toHaveBeenCalled();
    });
  });

  it('prevents proceeding without selections', async () => {
    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={true}
        onClose={jest.fn()}
        onQuoteGenerated={jest.fn()}
      />
    );

    await waitFor(() => {
      const nextButton = screen.getByText('Next');
      expect(nextButton).toBeDisabled();
    });
  });

  it('allows proceeding with selections', async () => {
    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={true}
        onClose={jest.fn()}
        onQuoteGenerated={jest.fn()}
      />
    );

    await waitFor(() => {
      const selectAllCheckbox = screen.getAllByLabelText('Select All')[0];
      fireEvent.click(selectAllCheckbox);
    });

    await waitFor(() => {
      const nextButton = screen.getByText('Next');
      expect(nextButton).not.toBeDisabled();
    });
  });

  it('calls onClose when cancel is clicked', async () => {
    const mockOnClose = jest.fn();
    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={true}
        onClose={mockOnClose}
        onQuoteGenerated={jest.fn()}
      />
    );

    await waitFor(() => {
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
    });

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('handles account search', async () => {
    render(
      <AdvancedQuoteBuilder
        rfqId={1}
        isOpen={true}
        onClose={jest.fn()}
        onQuoteGenerated={jest.fn()}
      />
    );

    // Navigate to step 2
    await waitFor(() => {
      const selectAllCheckbox = screen.getAllByLabelText('Select All')[0];
      fireEvent.click(selectAllCheckbox);
    });

    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);

    await waitFor(() => {
      const accountSearch = screen.getByPlaceholderText('Search accounts...');
      fireEvent.change(accountSearch, { target: { value: 'Test' } });
    });

    await waitFor(() => {
      expect(mockRFQApiService.searchAccounts).toHaveBeenCalledWith('Test');
    });
  });
});