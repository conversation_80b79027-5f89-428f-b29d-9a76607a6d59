import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { EnhancedItemsDisplay } from '../EnhancedItemsDisplay';
import { RFQItem } from '@/services/api/rfq';

// Mock the UI components
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>{children}</button>
  ),
}));

jest.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div data-testid="dialog-content">{children}</div>,
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <h2 data-testid="dialog-title">{children}</h2>,
  DialogDescription: ({ children }: any) => <p data-testid="dialog-description">{children}</p>,
}));

jest.mock('@/components/ui/scroll-area', () => ({
  ScrollArea: ({ children }: any) => <div data-testid="scroll-area">{children}</div>,
}));

const mockItems: RFQItem[] = [
  {
    id: '1',
    name: 'Test Item 1',
    description: 'Description for test item 1',
    quantity: 10,
    unit: 'pieces',
    specifications: { color: 'blue', size: 'large' },
    category: 'Electronics',
    estimatedPrice: 100,
    customFields: {},
  },
  {
    id: '2',
    name: 'Test Item 2',
    description: 'Description for test item 2',
    quantity: 5,
    unit: 'kg',
    specifications: { material: 'steel' },
    category: 'Materials',
    estimatedPrice: 50,
    customFields: {},
  },
  {
    id: '3',
    name: 'Test Item 3',
    description: 'Description for test item 3',
    quantity: 20,
    unit: 'hours',
    specifications: {},
    category: 'Services',
    customFields: {},
  },
  {
    id: '4',
    name: 'Test Item 4',
    description: 'Description for test item 4',
    quantity: 15,
    specifications: {},
    category: 'Supplies',
    customFields: {},
  },
];

describe('EnhancedItemsDisplay', () => {
  it('renders items correctly when count is within limit', () => {
    const limitedItems = mockItems.slice(0, 2);
    render(
      <EnhancedItemsDisplay
        items={limitedItems}
        currency="USD"
        maxDisplayItems={3}
      />
    );

    expect(screen.getByText('Items (2)')).toBeInTheDocument();
    expect(screen.getByText('Test Item 1')).toBeInTheDocument();
    expect(screen.getByText('Test Item 2')).toBeInTheDocument();
    expect(screen.queryByText('Show All')).not.toBeInTheDocument();
  });

  it('shows "Show All Items" button when items exceed limit', () => {
    render(
      <EnhancedItemsDisplay
        items={mockItems}
        currency="USD"
        maxDisplayItems={3}
      />
    );

    expect(screen.getByText('Items (4)')).toBeInTheDocument();
    expect(screen.getByText('Show All 4 Items')).toBeInTheDocument();
    
    // Should only show first 3 items
    expect(screen.getByText('Test Item 1')).toBeInTheDocument();
    expect(screen.getByText('Test Item 2')).toBeInTheDocument();
    expect(screen.getByText('Test Item 3')).toBeInTheDocument();
    expect(screen.queryByText('Test Item 4')).not.toBeInTheDocument();
  });

  it('opens modal when "Show All Items" is clicked', () => {
    render(
      <EnhancedItemsDisplay
        items={mockItems}
        currency="USD"
        maxDisplayItems={2}
      />
    );

    const showAllButton = screen.getByText('Show All 4 Items');
    fireEvent.click(showAllButton);

    expect(screen.getByTestId('dialog')).toBeInTheDocument();
    expect(screen.getByTestId('dialog-title')).toHaveTextContent('All RFQ Items (4)');
  });

  it('displays item details correctly', () => {
    render(
      <EnhancedItemsDisplay
        items={mockItems.slice(0, 1)}
        currency="USD"
        maxDisplayItems={3}
      />
    );

    expect(screen.getByText('Test Item 1')).toBeInTheDocument();
    expect(screen.getByText('Electronics')).toBeInTheDocument();
    expect(screen.getByText('10 pieces')).toBeInTheDocument();
    expect(screen.getByText('$100.00')).toBeInTheDocument();
  });

  it('handles empty items array', () => {
    render(
      <EnhancedItemsDisplay
        items={[]}
        currency="USD"
        maxDisplayItems={3}
      />
    );

    expect(screen.getByText('No items defined for this RFQ')).toBeInTheDocument();
  });

  it('calls custom onShowAllItems callback when provided', () => {
    const mockCallback = jest.fn();
    render(
      <EnhancedItemsDisplay
        items={mockItems}
        currency="USD"
        maxDisplayItems={2}
        onShowAllItems={mockCallback}
      />
    );

    const showAllButton = screen.getByText('Show All 4 Items');
    fireEvent.click(showAllButton);

    expect(mockCallback).toHaveBeenCalledTimes(1);
  });

  it('renders in compact mode correctly', () => {
    render(
      <EnhancedItemsDisplay
        items={mockItems.slice(0, 1)}
        currency="USD"
        maxDisplayItems={3}
        compact={true}
      />
    );

    // In compact mode, description should not be visible
    expect(screen.queryByText('Description for test item 1')).not.toBeInTheDocument();
    expect(screen.getByText('Test Item 1')).toBeInTheDocument();
  });

  it('displays correct currency formatting', () => {
    render(
      <EnhancedItemsDisplay
        items={mockItems.slice(0, 1)}
        currency="EUR"
        maxDisplayItems={3}
      />
    );

    expect(screen.getByText('€100.00')).toBeInTheDocument();
  });
});