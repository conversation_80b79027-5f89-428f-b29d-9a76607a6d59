import React from 'react';
import { render, screen } from '@testing-library/react';
import { PublicSubmissionForm, PublicRFQData } from '../PublicSubmissionForm';

// Mock the form dependencies
jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: jest.fn(),
    watch: jest.fn(() => []),
    getValues: jest.fn(() => ({})),
    reset: jest.fn(),
  }),
}));

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

const mockRFQData: PublicRFQData = {
  rfq_id: 1,
  title: 'Test RFQ',
  description: 'Test RFQ Description',
  items: [
    {
      id: '1',
      name: 'Test Item',
      description: 'Test Item Description',
      quantity: 10,
      specifications: {},
      category: 'Test Category',
    },
  ],
  due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
  form_config: [],
  terms: 'Test terms',
  vendor_name: 'Test Vendor',
  vendor_email: '<EMAIL>',
  invitation_status: 'sent',
  has_existing_submission: false,
};

describe('PublicSubmissionForm', () => {
  const mockProps = {
    rfq: mockRFQData,
    token: 'test-token',
    onSubmit: jest.fn(),
    isLoading: false,
  };

  it('renders RFQ information correctly', () => {
    render(<PublicSubmissionForm {...mockProps} />);
    
    expect(screen.getByText('RFQ Submission Portal')).toBeInTheDocument();
    expect(screen.getByText('Test RFQ')).toBeInTheDocument();
    expect(screen.getByText('Test Vendor')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('renders items to quote section', () => {
    render(<PublicSubmissionForm {...mockProps} />);
    
    expect(screen.getByText('Items to Quote')).toBeInTheDocument();
    expect(screen.getByText('Test Item')).toBeInTheDocument();
    expect(screen.getByText('Test Item Description')).toBeInTheDocument();
  });

  it('shows existing submission notice when applicable', () => {
    const propsWithExisting = {
      ...mockProps,
      rfq: {
        ...mockRFQData,
        has_existing_submission: true,
      },
    };

    render(<PublicSubmissionForm {...propsWithExisting} />);
    
    expect(screen.getByText(/You have already submitted a bid/)).toBeInTheDocument();
  });

  it('shows deadline passed warning when deadline is passed', () => {
    const propsWithPastDeadline = {
      ...mockProps,
      rfq: {
        ...mockRFQData,
        due_date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Yesterday
      },
    };

    render(<PublicSubmissionForm {...propsWithPastDeadline} />);
    
    expect(screen.getByText(/The submission deadline has passed/)).toBeInTheDocument();
  });
});