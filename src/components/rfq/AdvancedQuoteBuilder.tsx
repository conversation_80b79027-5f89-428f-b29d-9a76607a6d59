import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calculator,
  DollarSign,
  Percent,
  CheckCircle2,
  AlertTriangle,
  Users,
  Package,
  Building,
  Target,
  Search,
  X,
  Plus,
  Minus,
  Eye,
  Save,
  Send
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { RFQApiService, AccountSearchResult, OpportunitySearchResult } from '@/services/api/rfq';
import { QuoteApiService, QuoteSelectionItem, QuoteConfiguration, QuotePricing } from '@/services/api/quotes';
import { formatCurrency } from '@/types/rfq';

interface AdvancedQuoteBuilderProps {
  rfqId: number;
  isOpen: boolean;
  onClose: () => void;
  onQuoteGenerated: (quote: any) => void;
}

interface SubmissionData {
  id: number;
  vendor_id: number;
  vendor_name: string;
  vendor_performance_score: number;
  total_amount: number;
  currency: string;
  allows_partial_selection: boolean;
  partial_selection_notes?: string;
  bid_items: Array<{
    id: number;
    rfq_item_id: string;
    item_name: string;
    unit_price: number;
    quantity: number;
    total_price: number;
    delivery_days?: number;
    specifications: Record<string, any>;
    notes: string;
  }>;
}

interface RFQData {
  id: number;
  title: string;
  allow_partial_selection: boolean;
  partial_selection_config: any;
  items: Array<{
    id: string;
    name: string;
    description: string;
    quantity: number;
    unit?: string;
    category: string;
  }>;
}

const AccountOpportunitySelector: React.FC<{
  selectedAccountId?: number;
  selectedOpportunityId?: number;
  onAccountChange: (accountId: number, accountName: string) => void;
  onOpportunityChange: (opportunityId: number, opportunityName: string) => void;
}> = ({ selectedAccountId, selectedOpportunityId, onAccountChange, onOpportunityChange }) => {
  const [accountSearch, setAccountSearch] = useState('');
  const [accounts, setAccounts] = useState<AccountSearchResult[]>([]);
  const [opportunities, setOpportunities] = useState<OpportunitySearchResult[]>([]);
  const [loadingAccounts, setLoadingAccounts] = useState(false);
  const [loadingOpportunities, setLoadingOpportunities] = useState(false);

  // Debounced account search
  useEffect(() => {
    const timer = setTimeout(async () => {
      if (accountSearch.length >= 2) {
        setLoadingAccounts(true);
        try {
          const response = await RFQApiService.searchAccounts(accountSearch);
          if (response.success) {
            setAccounts(response.data);
          }
        } catch (error) {
          console.error('Error searching accounts:', error);
        } finally {
          setLoadingAccounts(false);
        }
      } else {
        setAccounts([]);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [accountSearch]);

  // Load opportunities when account is selected
  useEffect(() => {
    const loadOpportunities = async () => {
      if (selectedAccountId) {
        setLoadingOpportunities(true);
        try {
          const response = await RFQApiService.getAccountOpportunities(selectedAccountId);
          if (response.success) {
            setOpportunities(response.data);
          }
        } catch (error) {
          console.error('Error loading opportunities:', error);
        } finally {
          setLoadingOpportunities(false);
        }
      } else {
        setOpportunities([]);
      }
    };

    loadOpportunities();
  }, [selectedAccountId]);

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="account-search">Account</Label>
        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            id="account-search"
            placeholder="Search accounts..."
            value={accountSearch}
            onChange={(e) => setAccountSearch(e.target.value)}
            className="pl-10"
          />
          {loadingAccounts && (
            <div className="absolute right-3 top-3">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            </div>
          )}
        </div>
        
        {accounts.length > 0 && (
          <div className="mt-2 border rounded-md max-h-40 overflow-y-auto">
            {accounts.map((account) => (
              <button
                key={account.id}
                onClick={() => {
                  onAccountChange(account.id, account.name);
                  setAccountSearch(account.name);
                  setAccounts([]);
                }}
                className="w-full text-left px-3 py-2 hover:bg-muted border-b last:border-b-0"
              >
                <div className="font-medium">{account.name}</div>
                <div className="text-sm text-muted-foreground">
                  {account.type} • {account.industry} • {account.activeOpportunities} opportunities
                </div>
              </button>
            ))}
          </div>
        )}
      </div>

      <div>
        <Label htmlFor="opportunity-select">Opportunity</Label>
        <Select
          value={selectedOpportunityId?.toString()}
          onValueChange={(value) => {
            const opp = opportunities.find(o => o.id.toString() === value);
            if (opp) {
              onOpportunityChange(opp.id, opp.name);
            }
          }}
          disabled={!selectedAccountId || loadingOpportunities}
        >
          <SelectTrigger>
            <SelectValue placeholder={
              !selectedAccountId 
                ? "Select an account first" 
                : loadingOpportunities 
                  ? "Loading opportunities..." 
                  : "Select opportunity"
            } />
          </SelectTrigger>
          <SelectContent>
            {opportunities.map((opportunity) => (
              <SelectItem key={opportunity.id} value={opportunity.id.toString()}>
                <div>
                  <div className="font-medium">{opportunity.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {opportunity.stage} • {formatCurrency(opportunity.value, opportunity.currency)} • {opportunity.probability}%
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

const SubmissionSelectionGrid: React.FC<{
  submissions: SubmissionData[];
  rfqData: RFQData;
  selections: QuoteSelectionItem[];
  onSelectionChange: (selections: QuoteSelectionItem[]) => void;
}> = ({ submissions, rfqData, selections, onSelectionChange }) => {
  const [expandedSubmissions, setExpandedSubmissions] = useState<Set<number>>(new Set());

  const toggleSubmissionExpansion = (submissionId: number) => {
    const newExpanded = new Set(expandedSubmissions);
    if (newExpanded.has(submissionId)) {
      newExpanded.delete(submissionId);
    } else {
      newExpanded.add(submissionId);
    }
    setExpandedSubmissions(newExpanded);
  };

  const isSubmissionSelected = (submissionId: number) => {
    return selections.some(s => s.submissionId === submissionId && s.isWholeSubmission);
  };

  const isItemSelected = (submissionId: number, bidItemId: number) => {
    return selections.some(s => s.submissionId === submissionId && s.bidItemId === bidItemId);
  };

  const handleWholeSubmissionToggle = (submission: SubmissionData) => {
    const newSelections = [...selections];
    const existingIndex = newSelections.findIndex(
      s => s.submissionId === submission.id && s.isWholeSubmission
    );

    if (existingIndex >= 0) {
      // Remove whole submission selection
      newSelections.splice(existingIndex, 1);
    } else {
      // Add whole submission selection (remove any individual item selections first)
      const filteredSelections = newSelections.filter(s => s.submissionId !== submission.id);
      filteredSelections.push({
        submissionId: submission.id,
        vendorId: submission.vendor_id,
        vendorName: submission.vendor_name,
        quantity: submission.bid_items.reduce((sum, item) => sum + item.quantity, 0),
        unitPrice: submission.total_amount,
        totalPrice: submission.total_amount,
        allowsPartialSelection: submission.allows_partial_selection,
        isWholeSubmission: true,
        selectionRationale: `Selected entire submission from ${submission.vendor_name}`
      });
      onSelectionChange(filteredSelections);
      return;
    }

    onSelectionChange(newSelections);
  };

  const handleItemToggle = (submission: SubmissionData, bidItem: any) => {
    if (!submission.allows_partial_selection) {
      return; // Should not happen due to UI restrictions
    }

    const newSelections = [...selections];
    const existingIndex = newSelections.findIndex(
      s => s.submissionId === submission.id && s.bidItemId === bidItem.id
    );

    if (existingIndex >= 0) {
      // Remove item selection
      newSelections.splice(existingIndex, 1);
    } else {
      // Add item selection (remove whole submission selection if exists)
      const filteredSelections = newSelections.filter(
        s => !(s.submissionId === submission.id && s.isWholeSubmission)
      );
      filteredSelections.push({
        submissionId: submission.id,
        bidItemId: bidItem.id,
        vendorId: submission.vendor_id,
        vendorName: submission.vendor_name,
        rfqItemId: bidItem.rfq_item_id,
        itemName: bidItem.item_name,
        quantity: bidItem.quantity,
        unitPrice: bidItem.unit_price,
        totalPrice: bidItem.total_price,
        allowsPartialSelection: submission.allows_partial_selection,
        isWholeSubmission: false,
        selectionRationale: `Selected ${bidItem.item_name} from ${submission.vendor_name}`
      });
      onSelectionChange(filteredSelections);
      return;
    }

    onSelectionChange(newSelections);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Vendor Submissions</h3>
        <Badge variant="outline">
          {submissions.length} submission{submissions.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      <div className="space-y-3">
        {submissions.map((submission) => (
          <Card key={submission.id} className="overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-base">{submission.vendor_name}</CardTitle>
                    <CardDescription>
                      Performance: {submission.vendor_performance_score.toFixed(1)} • 
                      Total: {formatCurrency(submission.total_amount, submission.currency)}
                    </CardDescription>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {submission.allows_partial_selection ? (
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      <CheckCircle2 className="w-3 h-3 mr-1" />
                      Partial OK
                    </Badge>
                  ) : (
                    <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                      <AlertTriangle className="w-3 h-3 mr-1" />
                      Whole Only
                    </Badge>
                  )}

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`submission-${submission.id}`}
                      checked={isSubmissionSelected(submission.id)}
                      onCheckedChange={() => handleWholeSubmissionToggle(submission)}
                    />
                    <Label htmlFor={`submission-${submission.id}`} className="text-sm">
                      Select All
                    </Label>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleSubmissionExpansion(submission.id)}
                  >
                    {expandedSubmissions.has(submission.id) ? (
                      <Minus className="w-4 h-4" />
                    ) : (
                      <Plus className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </div>
            </CardHeader>

            <AnimatePresence>
              {expandedSubmissions.has(submission.id) && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      {submission.bid_items.map((bidItem) => (
                        <div
                          key={bidItem.id}
                          className={`p-3 border rounded-lg ${
                            isItemSelected(submission.id, bidItem.id) 
                              ? 'border-primary bg-primary/5' 
                              : 'border-border'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Package className="w-4 h-4 text-muted-foreground" />
                                <span className="font-medium">{bidItem.item_name}</span>
                              </div>
                              <div className="text-sm text-muted-foreground">
                                Qty: {bidItem.quantity} • 
                                Unit: {formatCurrency(bidItem.unit_price, submission.currency)} • 
                                Total: {formatCurrency(bidItem.total_price, submission.currency)}
                                {bidItem.delivery_days && ` • Delivery: ${bidItem.delivery_days} days`}
                              </div>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id={`item-${bidItem.id}`}
                                checked={isItemSelected(submission.id, bidItem.id)}
                                onCheckedChange={() => handleItemToggle(submission, bidItem)}
                                disabled={!submission.allows_partial_selection}
                              />
                              <Label 
                                htmlFor={`item-${bidItem.id}`} 
                                className={`text-sm ${
                                  !submission.allows_partial_selection 
                                    ? 'text-muted-foreground' 
                                    : ''
                                }`}
                              >
                                Select
                              </Label>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {submission.partial_selection_notes && (
                      <div className="mt-3 p-3 bg-muted/50 rounded-lg">
                        <div className="text-sm font-medium mb-1">Vendor Notes:</div>
                        <div className="text-sm text-muted-foreground">
                          {submission.partial_selection_notes}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </motion.div>
              )}
            </AnimatePresence>
          </Card>
        ))}
      </div>
    </div>
  );
};

const CommissionConfiguration: React.FC<{
  globalCommissionRate: number;
  itemCommissionOverrides: Record<string, number>;
  selections: QuoteSelectionItem[];
  onGlobalRateChange: (rate: number) => void;
  onItemRateChange: (itemId: string, rate: number) => void;
}> = ({ 
  globalCommissionRate, 
  itemCommissionOverrides, 
  selections,
  onGlobalRateChange, 
  onItemRateChange 
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Percent className="w-5 h-5" />
          Commission Configuration
        </CardTitle>
        <CardDescription>
          Set commission rates for pricing calculations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="global-commission">Global Commission Rate (%)</Label>
          <Input
            id="global-commission"
            type="number"
            min="0"
            max="100"
            step="0.1"
            value={globalCommissionRate}
            onChange={(e) => onGlobalRateChange(parseFloat(e.target.value) || 0)}
            className="mt-1"
          />
        </div>

        {selections.length > 0 && (
          <div>
            <Label>Item-Specific Overrides</Label>
            <div className="mt-2 space-y-2">
              {selections.map((selection) => (
                <div key={`${selection.submissionId}-${selection.bidItemId || 'whole'}`} className="flex items-center gap-2">
                  <div className="flex-1 text-sm">
                    {selection.isWholeSubmission 
                      ? `${selection.vendorName} (Whole Submission)`
                      : `${selection.itemName} (${selection.vendorName})`
                    }
                  </div>
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    placeholder={globalCommissionRate.toString()}
                    value={selection.rfqItemId ? itemCommissionOverrides[selection.rfqItemId] || '' : ''}
                    onChange={(e) => {
                      if (selection.rfqItemId) {
                        onItemRateChange(selection.rfqItemId, parseFloat(e.target.value) || 0);
                      }
                    }}
                    className="w-20"
                  />
                  <span className="text-sm text-muted-foreground">%</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

const QuotePricingPreview: React.FC<{
  pricing: QuotePricing | null;
  currency: string;
  loading: boolean;
}> = ({ pricing, currency, loading }) => {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="w-5 h-5" />
            Pricing Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!pricing) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="w-5 h-5" />
            Pricing Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Select items to see pricing preview</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="w-5 h-5" />
          Pricing Preview
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex justify-between">
          <span className="text-muted-foreground">Subtotal:</span>
          <span className="font-medium">{formatCurrency(pricing.subtotal, currency)}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">Commission:</span>
          <span className="font-medium text-green-600">
            +{formatCurrency(pricing.totalCommission, currency)}
          </span>
        </div>
        <Separator />
        <div className="flex justify-between text-lg font-semibold">
          <span>Total Amount:</span>
          <span>{formatCurrency(pricing.finalAmount, currency)}</span>
        </div>
        <div className="text-sm text-muted-foreground">
          Margin: {formatCurrency(pricing.totalMargin, currency)} 
          ({((pricing.totalMargin / pricing.subtotal) * 100).toFixed(1)}%)
        </div>
      </CardContent>
    </Card>
  );
};

export const AdvancedQuoteBuilder: React.FC<AdvancedQuoteBuilderProps> = ({
  rfqId,
  isOpen,
  onClose,
  onQuoteGenerated
}) => {
  const [rfqData, setRfqData] = useState<RFQData | null>(null);
  const [submissions, setSubmissions] = useState<SubmissionData[]>([]);
  const [selections, setSelections] = useState<QuoteSelectionItem[]>([]);
  const [config, setConfig] = useState<QuoteConfiguration>({
    clientName: '',
    clientEmail: '',
    globalCommissionRate: 10,
    itemCommissionOverrides: {},
    terms: '',
    notes: ''
  });
  const [pricing, setPricing] = useState<QuotePricing | null>(null);
  const [loading, setLoading] = useState(false);
  const [pricingLoading, setPricingLoading] = useState(false);
  const [step, setStep] = useState(1);
  const { toast } = useToast();

  // Load RFQ data and submissions
  useEffect(() => {
    if (isOpen && rfqId) {
      loadData();
    }
  }, [isOpen, rfqId]);

  // Calculate pricing when selections or commission rates change
  useEffect(() => {
    if (selections.length > 0) {
      calculatePricing();
    } else {
      setPricing(null);
    }
  }, [selections, config.globalCommissionRate, config.itemCommissionOverrides]);

  const loadData = async () => {
    setLoading(true);
    try {
      const response = await QuoteApiService.getSubmissionsForAdvancedQuote(rfqId);
      if (response.success) {
        setRfqData(response.data.rfq);
        setSubmissions(response.data.submissions);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load RFQ data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const calculatePricing = async () => {
    setPricingLoading(true);
    try {
      const response = await QuoteApiService.calculateQuotePricing(
        selections,
        config.globalCommissionRate,
        config.itemCommissionOverrides
      );
      if (response.success) {
        setPricing(response.data);
      }
    } catch (error) {
      console.error('Error calculating pricing:', error);
    } finally {
      setPricingLoading(false);
    }
  };

  const handleGenerateQuote = async () => {
    if (selections.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select at least one item or submission',
        variant: 'destructive',
      });
      return;
    }

    if (!config.clientName || !config.clientEmail) {
      toast({
        title: 'Error',
        description: 'Please provide client name and email',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      const quoteData = {
        rfq_id: rfqId,
        title: `Quote for ${rfqData?.title}`,
        selections,
        config,
        currency: submissions[0]?.currency || 'USD',
        expires_in_days: 30
      };

      const response = await QuoteApiService.generateAdvancedQuote(rfqId, quoteData);
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Quote generated successfully',
        });
        onQuoteGenerated(response.data);
        onClose();
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to generate quote',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const canProceedToNext = () => {
    switch (step) {
      case 1:
        return selections.length > 0;
      case 2:
        return config.clientName && config.clientEmail;
      default:
        return true;
    }
  };

  if (loading && !rfqData) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-6xl max-h-[90vh]">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3">Loading RFQ data...</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calculator className="w-5 h-5" />
            Generate Advanced Quote
          </DialogTitle>
          <DialogDescription>
            Create a quote by selecting individual items or whole submissions from vendors
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          {/* Step Navigation */}
          <div className="flex items-center gap-4 mb-6">
            {[1, 2, 3].map((stepNum) => (
              <div key={stepNum} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step >= stepNum
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground'
                  }`}
                >
                  {stepNum}
                </div>
                <span className={`ml-2 text-sm ${
                  step >= stepNum ? 'text-foreground' : 'text-muted-foreground'
                }`}>
                  {stepNum === 1 && 'Select Items'}
                  {stepNum === 2 && 'Configure Quote'}
                  {stepNum === 3 && 'Review & Generate'}
                </span>
                {stepNum < 3 && (
                  <div className={`w-8 h-px mx-4 ${
                    step > stepNum ? 'bg-primary' : 'bg-muted'
                  }`} />
                )}
              </div>
            ))}
          </div>

          <ScrollArea className="h-[60vh]">
            {/* Step 1: Selection */}
            {step === 1 && rfqData && (
              <div className="space-y-6">
                <SubmissionSelectionGrid
                  submissions={submissions}
                  rfqData={rfqData}
                  selections={selections}
                  onSelectionChange={setSelections}
                />

                {selections.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Selected Items ({selections.length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {selections.map((selection, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                            <div>
                              <span className="font-medium">
                                {selection.isWholeSubmission 
                                  ? `${selection.vendorName} (Complete Submission)`
                                  : `${selection.itemName} from ${selection.vendorName}`
                                }
                              </span>
                              <div className="text-sm text-muted-foreground">
                                {formatCurrency(selection.totalPrice, submissions[0]?.currency || 'USD')}
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                const newSelections = selections.filter((_, i) => i !== index);
                                setSelections(newSelections);
                              }}
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* Step 2: Configuration */}
            {step === 2 && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Building className="w-5 h-5" />
                        Client Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="client-name">Client Name</Label>
                        <Input
                          id="client-name"
                          value={config.clientName}
                          onChange={(e) => setConfig(prev => ({ ...prev, clientName: e.target.value }))}
                          placeholder="Enter client name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="client-email">Client Email</Label>
                        <Input
                          id="client-email"
                          type="email"
                          value={config.clientEmail}
                          onChange={(e) => setConfig(prev => ({ ...prev, clientEmail: e.target.value }))}
                          placeholder="Enter client email"
                        />
                      </div>
                    </CardContent>
                  </Card>

                  <AccountOpportunitySelector
                    selectedAccountId={config.accountId}
                    selectedOpportunityId={config.opportunityId}
                    onAccountChange={(accountId, accountName) => 
                      setConfig(prev => ({ ...prev, accountId, accountName }))
                    }
                    onOpportunityChange={(opportunityId, opportunityName) => 
                      setConfig(prev => ({ ...prev, opportunityId, opportunityName }))
                    }
                  />

                  <Card>
                    <CardHeader>
                      <CardTitle>Terms & Notes</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="terms">Terms & Conditions</Label>
                        <Textarea
                          id="terms"
                          value={config.terms}
                          onChange={(e) => setConfig(prev => ({ ...prev, terms: e.target.value }))}
                          placeholder="Enter terms and conditions"
                          rows={3}
                        />
                      </div>
                      <div>
                        <Label htmlFor="notes">Additional Notes</Label>
                        <Textarea
                          id="notes"
                          value={config.notes}
                          onChange={(e) => setConfig(prev => ({ ...prev, notes: e.target.value }))}
                          placeholder="Enter additional notes"
                          rows={3}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="space-y-6">
                  <CommissionConfiguration
                    globalCommissionRate={config.globalCommissionRate}
                    itemCommissionOverrides={config.itemCommissionOverrides}
                    selections={selections}
                    onGlobalRateChange={(rate) => 
                      setConfig(prev => ({ ...prev, globalCommissionRate: rate }))
                    }
                    onItemRateChange={(itemId, rate) => 
                      setConfig(prev => ({
                        ...prev,
                        itemCommissionOverrides: {
                          ...prev.itemCommissionOverrides,
                          [itemId]: rate
                        }
                      }))
                    }
                  />

                  <QuotePricingPreview
                    pricing={pricing}
                    currency={submissions[0]?.currency || 'USD'}
                    loading={pricingLoading}
                  />
                </div>
              </div>
            )}

            {/* Step 3: Review */}
            {step === 3 && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Quote Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Client</Label>
                        <p className="font-medium">{config.clientName}</p>
                        <p className="text-sm text-muted-foreground">{config.clientEmail}</p>
                      </div>
                      <div>
                        <Label>RFQ</Label>
                        <p className="font-medium">{rfqData?.title}</p>
                      </div>
                    </div>

                    {(config.accountName || config.opportunityName) && (
                      <div className="grid grid-cols-2 gap-4">
                        {config.accountName && (
                          <div>
                            <Label>Account</Label>
                            <p className="font-medium">{config.accountName}</p>
                          </div>
                        )}
                        {config.opportunityName && (
                          <div>
                            <Label>Opportunity</Label>
                            <p className="font-medium">{config.opportunityName}</p>
                          </div>
                        )}
                      </div>
                    )}

                    <div>
                      <Label>Selected Items ({selections.length})</Label>
                      <div className="mt-2 space-y-2">
                        {selections.map((selection, index) => (
                          <div key={index} className="p-2 bg-muted/50 rounded text-sm">
                            {selection.isWholeSubmission 
                              ? `${selection.vendorName} (Complete Submission)`
                              : `${selection.itemName} from ${selection.vendorName}`
                            } - {formatCurrency(selection.totalPrice, submissions[0]?.currency || 'USD')}
                          </div>
                        ))}
                      </div>
                    </div>

                    {pricing && (
                      <div className="p-4 bg-primary/5 rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-lg font-semibold">Total Quote Value:</span>
                          <span className="text-2xl font-bold text-primary">
                            {formatCurrency(pricing.finalAmount, submissions[0]?.currency || 'USD')}
                          </span>
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">
                          Includes {formatCurrency(pricing.totalCommission, submissions[0]?.currency || 'USD')} commission
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}
          </ScrollArea>
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            {step > 1 && (
              <Button variant="outline" onClick={() => setStep(step - 1)}>
                Previous
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            
            {step < 3 ? (
              <Button 
                onClick={() => setStep(step + 1)}
                disabled={!canProceedToNext()}
              >
                Next
              </Button>
            ) : (
              <Button 
                onClick={handleGenerateQuote}
                disabled={loading || !pricing}
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Generating...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Generate Quote
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AdvancedQuoteBuilder;