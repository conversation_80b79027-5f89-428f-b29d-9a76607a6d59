import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  Trash2, 
  Settings, 
  Eye,
  GripVertical,
  Type,
  Hash,
  Calendar,
  FileText,
  List,
  Upload,
  ToggleLeft,
  Edit3,
  Save,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { FormFieldConfig, RFQItem } from './RFQCreationForm';

// Field Editor with local state to prevent focus loss
interface FieldEditorProps {
  field: FormFieldConfig;
  onUpdate: (id: string, updates: Partial<FormFieldConfig>) => void;
  onAddOption: (fieldId: string, option: string) => void;
  onRemoveOption: (fieldId: string, optionIndex: number) => void;
  onUpdateOption: (fieldId: string, optionIndex: number, newValue: string) => void;
}

const FieldEditor: React.FC<FieldEditorProps> = ({ 
  field, 
  onUpdate, 
  onAddOption, 
  onRemoveOption, 
  onUpdateOption 
}) => {
  const [localLabel, setLocalLabel] = useState(field.label);
  const [localOptions, setLocalOptions] = useState<string[]>(field.options || []);

  // Update local state when field changes from outside
  useEffect(() => {
    setLocalLabel(field.label);
    setLocalOptions(field.options || []);
  }, [field.label, field.options]);

  // onBlur handlers
  const handleLabelBlur = () => {
    if (localLabel !== field.label) {
      onUpdate(field.id, { label: localLabel });
    }
  };

  const handleOptionsBlur = () => {
    if (JSON.stringify(localOptions) !== JSON.stringify(field.options || [])) {
      onUpdate(field.id, { options: localOptions });
    }
  };

  const updateLocalOption = (index: number, value: string) => {
    const newOptions = [...localOptions];
    newOptions[index] = value;
    setLocalOptions(newOptions);
  };

  const addLocalOption = () => {
    setLocalOptions([...localOptions, '']);
  };

  const removeLocalOption = (index: number) => {
    setLocalOptions(localOptions.filter((_, i) => i !== index));
    handleOptionsBlur(); // Save immediately when removing
  };

  return (
    <div className="space-y-4">
      {/* Basic Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="text-sm font-medium text-foreground mb-2 block">
            Field Label *
          </label>
          <Input
            placeholder="Enter field label"
            value={localLabel}
            onChange={(e) => setLocalLabel(e.target.value)}
            onBlur={handleLabelBlur}
            className="input-neumorphic"
          />
        </div>
        <div>
          <label className="text-sm font-medium text-foreground mb-2 block">
            Field Type
          </label>
          <Select
            value={field.type}
            onValueChange={(value: FormFieldConfig['type']) => 
              onUpdate(field.id, { type: value })
            }
          >
            <SelectTrigger className="input-neumorphic">
              <SelectValue placeholder="Select field type" />
            </SelectTrigger>
            <SelectContent>
              {FIELD_TYPES.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  <div className="flex items-center space-x-2">
                    <type.icon className="w-4 h-4" />
                    <span>{type.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Field Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="flex items-center space-x-2">
          <Switch
            checked={field.required}
            onCheckedChange={(checked) => onUpdate(field.id, { required: checked })}
          />
          <label className="text-sm font-medium text-foreground">
            Required Field
          </label>
        </div>
        <div className="flex items-center space-x-2">
          <Switch
            checked={field.itemSpecific}
            onCheckedChange={(checked) => onUpdate(field.id, { itemSpecific: checked })}
          />
          <label className="text-sm font-medium text-foreground">
            Item Specific
          </label>
        </div>
      </div>

      {/* Select Options */}
      {field.type === 'select' && (
        <div>
          <label className="text-sm font-medium text-foreground mb-2 block">
            Options
          </label>
          <div className="space-y-2">
            {localOptions.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Input
                  placeholder="Enter option"
                  value={option}
                  onChange={(e) => updateLocalOption(index, e.target.value)}
                  onBlur={handleOptionsBlur}
                  className="input-neumorphic flex-1"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeLocalOption(index)}
                  className="btn-neumorphic text-destructive hover:text-destructive"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addLocalOption}
              className="btn-neumorphic"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Option
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

interface FormConfigBuilderProps {
  fields: FormFieldConfig[];
  onChange: (fields: FormFieldConfig[]) => void;
  items: RFQItem[];
}

const FIELD_TYPES = [
  { value: 'text', label: 'Text Input', icon: Type },
  { value: 'number', label: 'Number Input', icon: Hash },
  { value: 'date', label: 'Date Input', icon: Calendar },
  { value: 'textarea', label: 'Text Area', icon: FileText },
  { value: 'select', label: 'Dropdown', icon: List },
  { value: 'file', label: 'File Upload', icon: Upload },
];

const VALIDATION_RULES = [
  { value: 'required', label: 'Required Field' },
  { value: 'minLength', label: 'Minimum Length' },
  { value: 'maxLength', label: 'Maximum Length' },
  { value: 'min', label: 'Minimum Value' },
  { value: 'max', label: 'Maximum Value' },
  { value: 'pattern', label: 'Pattern/Regex' },
  { value: 'email', label: 'Email Format' },
  { value: 'url', label: 'URL Format' },
];

export const FormConfigBuilder: React.FC<FormConfigBuilderProps> = ({
  fields,
  onChange,
  items,
}) => {
  const [editingField, setEditingField] = useState<string | null>(null);
  const [previewMode, setPreviewMode] = useState(false);
  const [draggedField, setDraggedField] = useState<string | null>(null);

  const generateId = () => `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const addField = (type: FormFieldConfig['type']) => {
    const newField: FormFieldConfig = {
      id: generateId(),
      type,
      label: `New ${type} field`,
      required: false,
      itemSpecific: true,
      ...(type === 'select' && { options: ['Option 1', 'Option 2'] }),
    };
    onChange([...fields, newField]);
    setEditingField(newField.id);
  };

  const updateField = (id: string, updates: Partial<FormFieldConfig>) => {
    const updatedFields = fields.map(field =>
      field.id === id ? { ...field, ...updates } : field
    );
    onChange(updatedFields);
  };

  const removeField = (id: string) => {
    const filteredFields = fields.filter(field => field.id !== id);
    onChange(filteredFields);
    if (editingField === id) {
      setEditingField(null);
    }
  };

  const duplicateField = (field: FormFieldConfig) => {
    const duplicatedField: FormFieldConfig = {
      ...field,
      id: generateId(),
      label: `${field.label} (Copy)`,
    };
    onChange([...fields, duplicatedField]);
  };

  const moveField = (fromIndex: number, toIndex: number) => {
    const newFields = [...fields];
    const [movedField] = newFields.splice(fromIndex, 1);
    newFields.splice(toIndex, 0, movedField);
    onChange(newFields);
  };

  const addSelectOption = (fieldId: string, option: string) => {
    const field = fields.find(f => f.id === fieldId);
    if (field && field.options) {
      updateField(fieldId, {
        options: [...field.options, option],
      });
    }
  };

  const removeSelectOption = (fieldId: string, optionIndex: number) => {
    const field = fields.find(f => f.id === fieldId);
    if (field && field.options) {
      updateField(fieldId, {
        options: field.options.filter((_, index) => index !== optionIndex),
      });
    }
  };

  const updateSelectOption = (fieldId: string, optionIndex: number, newValue: string) => {
    const field = fields.find(f => f.id === fieldId);
    if (field && field.options) {
      const newOptions = [...field.options];
      newOptions[optionIndex] = newValue;
      updateField(fieldId, { options: newOptions });
    }
  };

  const FieldCard: React.FC<{ field: FormFieldConfig }> = ({ field }) => (
    <Card className="card-neumorphic">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2 text-base">
            {React.createElement(FIELD_TYPES.find(t => t.value === field.type)?.icon || Type, {
              className: "w-4 h-4 text-primary"
            })}
            <span>{field.label}</span>
            <Badge variant="outline" className="text-xs">
              {field.type}
            </Badge>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setEditingField(editingField === field.id ? null : field.id)}
              className="btn-neumorphic"
            >
              {editingField === field.id ? <Save className="w-4 h-4" /> : <Edit3 className="w-4 h-4" />}
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => duplicateField(field)}
              className="btn-neumorphic"
            >
              <Plus className="w-4 h-4" />
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => removeField(field.id)}
              className="btn-neumorphic text-destructive hover:text-destructive"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {editingField === field.id ? (
          <FieldEditor
            field={field}
            onUpdate={updateField}
            onAddOption={addSelectOption}
            onRemoveOption={removeSelectOption}
            onUpdateOption={updateSelectOption}
          />
        ) : (
          <div className="space-y-3">
            <div className="flex items-center space-x-4 text-sm">
              <Badge variant={field.required ? 'default' : 'secondary'} className="text-xs">
                {field.required ? 'Required' : 'Optional'}
              </Badge>
              <Badge variant={field.itemSpecific ? 'default' : 'outline'} className="text-xs">
                {field.itemSpecific ? 'Per Item' : 'General'}
              </Badge>
            </div>
            
            {field.type === 'select' && field.options && (
              <div>
                <p className="text-sm font-medium text-foreground mb-2">Options:</p>
                <div className="flex flex-wrap gap-1">
                  {field.options.map((option, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {option}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  const FormPreview: React.FC = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-foreground">Form Preview</h3>
      <p className="text-sm text-muted-foreground">
        This is how vendors will see the submission form
      </p>
      
      <Card className="card-neumorphic">
        <CardHeader>
          <CardTitle>Vendor Submission Form</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* General Fields */}
          <div>
            <h4 className="font-medium text-foreground mb-4">General Information</h4>
            <div className="space-y-4">
              {fields.filter(f => !f.itemSpecific).map((field) => (
                <div key={field.id}>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    {field.label} {field.required && <span className="text-destructive">*</span>}
                  </label>
                  {field.type === 'text' && (
                    <Input placeholder={`Enter ${field.label.toLowerCase()}`} className="input-neumorphic" disabled />
                  )}
                  {field.type === 'number' && (
                    <Input type="number" placeholder="0" className="input-neumorphic" disabled />
                  )}
                  {field.type === 'date' && (
                    <Input type="date" className="input-neumorphic" disabled />
                  )}
                  {field.type === 'textarea' && (
                    <Textarea placeholder={`Enter ${field.label.toLowerCase()}`} className="input-neumorphic" disabled />
                  )}
                  {field.type === 'select' && (
                    <Select disabled>
                      <SelectTrigger className="input-neumorphic">
                        <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
                      </SelectTrigger>
                    </Select>
                  )}
                  {field.type === 'file' && (
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 text-center">
                      <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">Upload {field.label.toLowerCase()}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Item-Specific Fields */}
          {items.length > 0 && fields.some(f => f.itemSpecific) && (
            <div>
              <h4 className="font-medium text-foreground mb-4">Item-Specific Information</h4>
              {items.slice(0, 2).map((item, itemIndex) => (
                <div key={item.id} className="mb-6">
                  <h5 className="font-medium text-foreground mb-3">
                    {item.name || `Item ${itemIndex + 1}`}
                  </h5>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {fields.filter(f => f.itemSpecific).map((field) => (
                      <div key={`${item.id}-${field.id}`}>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          {field.label} {field.required && <span className="text-destructive">*</span>}
                        </label>
                        {field.type === 'text' && (
                          <Input placeholder={`Enter ${field.label.toLowerCase()}`} className="input-neumorphic" disabled />
                        )}
                        {field.type === 'number' && (
                          <Input type="number" placeholder="0" className="input-neumorphic" disabled />
                        )}
                        {field.type === 'textarea' && (
                          <Textarea placeholder={`Enter ${field.label.toLowerCase()}`} className="input-neumorphic" disabled />
                        )}
                        {field.type === 'select' && (
                          <Select disabled>
                            <SelectTrigger className="input-neumorphic">
                              <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
                            </SelectTrigger>
                          </Select>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
              {items.length > 2 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  ... and {items.length - 2} more items
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-foreground">Form Configuration</h3>
          <p className="text-sm text-muted-foreground">
            {fields.length} field{fields.length !== 1 ? 's' : ''} configured
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" className="btn-neumorphic">
                <Eye className="w-4 h-4 mr-2" />
                Preview Form
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Form Preview</DialogTitle>
                <DialogDescription>
                  Preview how vendors will see the submission form
                </DialogDescription>
              </DialogHeader>
              <FormPreview />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Field Type Selector */}
      <Card className="card-neumorphic">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2 text-base">
            <Plus className="w-4 h-4 text-primary" />
            <span>Add Form Field</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            {FIELD_TYPES.map((type) => (
              <Button
                key={type.value}
                type="button"
                variant="outline"
                onClick={() => addField(type.value as FormFieldConfig['type'])}
                className="btn-neumorphic h-auto p-4 flex flex-col items-center space-y-2"
              >
                <type.icon className="w-6 h-6" />
                <span className="text-xs text-center">{type.label}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Form Fields */}
      {fields.length === 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <Settings className="w-16 h-16 text-muted-foreground mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium text-foreground mb-2">No form fields configured</h3>
          <p className="text-muted-foreground mb-4">
            Add form fields to customize how vendors submit their bids
          </p>
        </motion.div>
      ) : (
        <div className="space-y-4">
          <AnimatePresence>
            {fields.map((field, index) => (
              <motion.div
                key={field.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                layout
              >
                <FieldCard field={field} />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Default Fields Info */}
      {fields.length > 0 && (
        <Card className="card-neumorphic bg-primary/5 border-primary/20">
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3">
              <Settings className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium text-foreground mb-1">Form Configuration Complete</p>
                <p className="text-sm text-muted-foreground">
                  Your custom form fields have been configured. Vendors will see these fields when submitting their bids.
                  {fields.some(f => f.itemSpecific) && ' Item-specific fields will be repeated for each item in your RFQ.'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};