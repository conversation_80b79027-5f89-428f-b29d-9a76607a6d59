import React, { useState, useMemo } from 'react';
import { 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown,
  CheckCircle2,
  Star,
  TrendingUp,
  TrendingDown,
  Clock,
  Truck,
  DollarSign,
  Eye,
  Download,
  Filter,
  BarChart3
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { RFQSubmission, BidItem } from '@/types/rfq';
import { formatCurrency } from '@/types/rfq';

interface BidComparisonProps {
  rfqId: number;
  submissions: RFQSubmission[];
  onSelectBid: (itemId: string, submissionId: number) => void;
  selectedBids: Record<string, number>;
  showAIRecommendations?: boolean;
  currency: string;
}

type SortField = 'vendor' | 'total' | 'performance' | 'delivery' | 'submitted';
type SortOrder = 'asc' | 'desc';

export const BidComparison: React.FC<BidComparisonProps> = ({
  rfqId,
  submissions,
  onSelectBid,
  selectedBids,
  showAIRecommendations = true,
  currency
}) => {
  const [sortField, setSortField] = useState<SortField>('total');
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc');
  const [selectedSubmissions, setSelectedSubmissions] = useState<number[]>([]);
  const [comparisonView, setComparisonView] = useState<'overview' | 'detailed' | 'items'>('overview');

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const sortedSubmissions = useMemo(() => {
    return [...submissions].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortField) {
        case 'vendor':
          aValue = a.vendor_name.toLowerCase();
          bValue = b.vendor_name.toLowerCase();
          break;
        case 'total':
          aValue = a.total_amount;
          bValue = b.total_amount;
          break;
        case 'performance':
          aValue = a.vendor_performance_score;
          bValue = b.vendor_performance_score;
          break;
        case 'delivery':
          aValue = a.delivery_days || 999;
          bValue = b.delivery_days || 999;
          break;
        case 'submitted':
          aValue = new Date(a.submitted_at).getTime();
          bValue = new Date(b.submitted_at).getTime();
          break;
        default:
          return 0;
      }

      if (typeof aValue === 'string') {
        return sortOrder === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });
  }, [submissions, sortField, sortOrder]);

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown className="w-4 h-4 text-muted-foreground" />;
    }
    return sortOrder === 'asc' 
      ? <ArrowUp className="w-4 h-4 text-primary" />
      : <ArrowDown className="w-4 h-4 text-primary" />;
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRankBadge = (index: number) => {
    const rank = index + 1;
    if (rank === 1) return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">1st</Badge>;
    if (rank === 2) return <Badge className="bg-gray-100 text-gray-800 border-gray-200">2nd</Badge>;
    if (rank === 3) return <Badge className="bg-orange-100 text-orange-800 border-orange-200">3rd</Badge>;
    return <Badge variant="outline">{rank}th</Badge>;
  };

  const calculateStats = () => {
    if (submissions.length === 0) return { min: 0, max: 0, avg: 0, spread: 0 };
    
    const amounts = submissions.map(s => s.total_amount);
    const min = Math.min(...amounts);
    const max = Math.max(...amounts);
    const avg = amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
    const spread = ((max - min) / min) * 100;

    return { min, max, avg, spread };
  };

  const stats = calculateStats();

  const handleSubmissionSelect = (submissionId: number, checked: boolean) => {
    setSelectedSubmissions(prev => 
      checked 
        ? [...prev, submissionId]
        : prev.filter(id => id !== submissionId)
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectedSubmissions(checked ? submissions.map(s => s.id) : []);
  };

  const getSelectedSubmissions = () => {
    return submissions.filter(s => selectedSubmissions.includes(s.id));
  };

  if (submissions.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Submissions to Compare</h3>
          <p className="text-muted-foreground">
            Once vendors submit their bids, you'll be able to compare them here.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Lowest Bid</p>
                <p className="text-xl font-bold text-green-600">{formatCurrency(stats.min, currency)}</p>
              </div>
              <TrendingDown className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Highest Bid</p>
                <p className="text-xl font-bold text-red-600">{formatCurrency(stats.max, currency)}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Bid</p>
                <p className="text-xl font-bold">{formatCurrency(stats.avg, currency)}</p>
              </div>
              <BarChart3 className="w-8 h-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Price Spread</p>
                <p className="text-xl font-bold">{stats.spread.toFixed(1)}%</p>
              </div>
              <div className="w-8 h-8 flex items-center justify-center">
                <Progress value={Math.min(stats.spread, 100)} className="w-6 h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Comparison Interface */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Bid Comparison
              </CardTitle>
              <CardDescription>
                Compare vendor submissions and select the best bids
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Select value={comparisonView} onValueChange={(value: any) => setComparisonView(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="overview">Overview</SelectItem>
                  <SelectItem value="detailed">Detailed</SelectItem>
                  <SelectItem value="items">Item-by-Item</SelectItem>
                </SelectContent>
              </Select>
              
              {selectedSubmissions.length > 0 && (
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export Selected
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={comparisonView} onValueChange={(value: any) => setComparisonView(value)}>
            <TabsContent value="overview" className="mt-0">
              <div className="space-y-4">
                {/* Selection Controls */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Checkbox
                      checked={selectedSubmissions.length === submissions.length}
                      onCheckedChange={handleSelectAll}
                    />
                    <span className="text-sm text-muted-foreground">
                      {selectedSubmissions.length > 0 
                        ? `${selectedSubmissions.length} selected`
                        : 'Select all'
                      }
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">Sort by:</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('total')}
                      className="flex items-center gap-1"
                    >
                      Price {getSortIcon('total')}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('performance')}
                      className="flex items-center gap-1"
                    >
                      Performance {getSortIcon('performance')}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('delivery')}
                      className="flex items-center gap-1"
                    >
                      Delivery {getSortIcon('delivery')}
                    </Button>
                  </div>
                </div>

                {/* Submissions Table */}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]"></TableHead>
                      <TableHead>Rank</TableHead>
                      <TableHead 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('vendor')}
                      >
                        <div className="flex items-center gap-1">
                          Vendor {getSortIcon('vendor')}
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('performance')}
                      >
                        <div className="flex items-center gap-1">
                          Performance {getSortIcon('performance')}
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('total')}
                      >
                        <div className="flex items-center gap-1">
                          Total Bid {getSortIcon('total')}
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('delivery')}
                      >
                        <div className="flex items-center gap-1">
                          Delivery {getSortIcon('delivery')}
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('submitted')}
                      >
                        <div className="flex items-center gap-1">
                          Submitted {getSortIcon('submitted')}
                        </div>
                      </TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedSubmissions.map((submission, index) => (
                      <TableRow key={submission.id} className="hover:bg-muted/50">
                        <TableCell>
                          <Checkbox
                            checked={selectedSubmissions.includes(submission.id)}
                            onCheckedChange={(checked) => handleSubmissionSelect(submission.id, checked as boolean)}
                          />
                        </TableCell>
                        
                        <TableCell>
                          {getRankBadge(index)}
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="w-8 h-8">
                              <AvatarFallback className="text-xs">
                                {getInitials(submission.vendor_name)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{submission.vendor_name}</div>
                              <div className="text-sm text-muted-foreground">
                                {submission.vendor_category}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div className="text-sm font-medium">
                              {submission.vendor_performance_score.toFixed(1)}
                            </div>
                            <Progress 
                              value={submission.vendor_performance_score} 
                              className="w-16 h-2" 
                            />
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div className="font-medium">
                            {formatCurrency(submission.total_amount, submission.currency)}
                          </div>
                          {submission.total_amount === stats.min && (
                            <Badge variant="secondary" className="text-xs mt-1 bg-green-100 text-green-800">
                              Best Price
                            </Badge>
                          )}
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4 text-muted-foreground" />
                            <span className="text-sm">
                              {submission.delivery_days ? `${submission.delivery_days} days` : 'Not specified'}
                            </span>
                          </div>
                        </TableCell>
                        
                        <TableCell className="text-sm text-muted-foreground">
                          {formatDate(submission.submitted_at)}
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Eye className="w-4 h-4 mr-1" />
                              View
                            </Button>
                            <Button variant="outline" size="sm">
                              <CheckCircle2 className="w-4 h-4 mr-1" />
                              Select
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="detailed" className="mt-0">
              <div className="text-center py-12">
                <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Detailed comparison view will show side-by-side analysis
                </p>
              </div>
            </TabsContent>

            <TabsContent value="items" className="mt-0">
              <div className="text-center py-12">
                <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Item-by-item comparison will show individual item pricing
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default BidComparison;