import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  X,
  Calendar,
  User,
  DollarSign,
  SlidersHorizontal,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  RFQFilters as RFQFiltersType,
  RFQ_STATUSES,
  CURRENCIES,
} from "@/types/rfq";

interface RFQFiltersProps {
  filters: RFQFiltersType;
  onFiltersChange: (filters: RFQFiltersType) => void;
  onSearch: (query: string) => void;
  searchQuery: string;
  totalResults?: number;
  loading?: boolean;
}

export const RFQFilters: React.FC<RFQFiltersProps> = ({
  filters,
  onFiltersChange,
  onSearch,
  searchQuery,
  totalResults,
  loading = false,
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [dueDateFromOpen, setDueDateFromOpen] = useState(false);
  const [dueDateToOpen, setDueDateToOpen] = useState(false);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      onSearch(localSearchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [localSearchQuery, onSearch]);

  const handleFilterChange = (key: keyof RFQFiltersType, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const handleStatusToggle = (status: string) => {
    const currentStatuses = filters.status || [];
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter((s) => s !== status)
      : [...currentStatuses, status];

    handleFilterChange("status", newStatuses);
  };

  const clearAllFilters = () => {
    onFiltersChange({});
    setLocalSearchQuery("");
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.status && filters.status.length > 0) count++;
    if (filters.due_date_from) count++;
    if (filters.due_date_to) count++;
    if (filters.creator_id) count++;
    if (filters.currency) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex items-center gap-3">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search RFQs by title or description..."
            value={localSearchQuery}
            onChange={(e) => setLocalSearchQuery(e.target.value)}
            className="pl-10 pr-4"
            disabled={loading}
          />
          {localSearchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              onClick={() => setLocalSearchQuery("")}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        <Button
          variant="outline"
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="flex items-center gap-2"
        >
          <SlidersHorizontal className="h-4 w-4" />
          Filters
          {activeFiltersCount > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
            >
              {activeFiltersCount}
            </Badge>
          )}
        </Button>

        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Results Summary */}
      {totalResults !== undefined && (
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            {loading
              ? "Loading..."
              : `${totalResults} RFQ${totalResults !== 1 ? "s" : ""} found`}
          </span>

          {/* Sort Options */}
          <div className="flex items-center gap-2">
            <Label htmlFor="sort-select" className="text-xs">
              Sort by:
            </Label>
            <Select
              value={`${filters.sortBy || "created_at"}-${
                filters.sortOrder || "desc"
              }`}
              onValueChange={(value) => {
                const [sortBy, sortOrder] = value.split("-");
                handleFilterChange("sortBy", sortBy);
                handleFilterChange("sortOrder", sortOrder);
              }}
            >
              <SelectTrigger className="w-40 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at-desc">Newest First</SelectItem>
                <SelectItem value="created_at-asc">Oldest First</SelectItem>
                <SelectItem value="due_date-asc">
                  Due Date (Earliest)
                </SelectItem>
                <SelectItem value="due_date-desc">Due Date (Latest)</SelectItem>
                <SelectItem value="title-asc">Title (A-Z)</SelectItem>
                <SelectItem value="title-desc">Title (Z-A)</SelectItem>
                <SelectItem value="response_rate-desc">
                  Response Rate (High)
                </SelectItem>
                <SelectItem value="response_rate-asc">
                  Response Rate (Low)
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div className="card-neumorphic p-4 space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <Filter className="h-4 w-4" />
            <span className="font-medium">Advanced Filters</span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Status Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Status</Label>
              <div className="flex flex-wrap gap-2">
                {RFQ_STATUSES.map((status) => (
                  <Badge
                    key={status.value}
                    variant={
                      filters.status?.includes(status.value)
                        ? "default"
                        : "outline"
                    }
                    className="cursor-pointer hover:scale-105 transition-transform"
                    onClick={() => handleStatusToggle(status.value)}
                  >
                    {status.label}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Due Date Range */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Due Date Range</Label>
              <div className="space-y-2">
                <Popover
                  open={dueDateFromOpen}
                  onOpenChange={setDueDateFromOpen}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                      size="sm"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {filters.due_date_from
                        ? new Date(filters.due_date_from).toLocaleDateString()
                        : "From date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={
                        filters.due_date_from
                          ? new Date(filters.due_date_from)
                          : undefined
                      }
                      onSelect={(date) => {
                        handleFilterChange(
                          "due_date_from",
                          date?.toISOString().split("T")[0]
                        );
                        setDueDateFromOpen(false);
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>

                <Popover open={dueDateToOpen} onOpenChange={setDueDateToOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                      size="sm"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {filters.due_date_to
                        ? new Date(filters.due_date_to).toLocaleDateString()
                        : "To date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={
                        filters.due_date_to
                          ? new Date(filters.due_date_to)
                          : undefined
                      }
                      onSelect={(date) => {
                        handleFilterChange(
                          "due_date_to",
                          date?.toISOString().split("T")[0]
                        );
                        setDueDateToOpen(false);
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Currency Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Currency</Label>
              <Select
                value={filters.currency || "all"}
                onValueChange={(value) =>
                  handleFilterChange("currency", value === "all" ? undefined : value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All currencies" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All currencies</SelectItem>
                  {CURRENCIES.map((currency) => (
                    <SelectItem key={currency.value} value={currency.value}>
                      {currency.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Creator Filter - Placeholder for now */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Creator</Label>
              <Select
                value={filters.creator_id?.toString() || "all"}
                onValueChange={(value) =>
                  handleFilterChange(
                    "creator_id",
                    value === "all" ? undefined : parseInt(value)
                  )
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All creators" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All creators</SelectItem>
                  {/* TODO: Load actual users from API */}
                  <SelectItem value="1">Current User</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Active Filters Display */}
          {activeFiltersCount > 0 && (
            <>
              <Separator />
              <div className="space-y-2">
                <Label className="text-sm font-medium">Active Filters:</Label>
                <div className="flex flex-wrap gap-2">
                  {filters.status?.map((status) => (
                    <Badge
                      key={status}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      Status: {status}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => handleStatusToggle(status)}
                      />
                    </Badge>
                  ))}

                  {filters.due_date_from && (
                    <Badge
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      From:{" "}
                      {new Date(filters.due_date_from).toLocaleDateString()}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() =>
                          handleFilterChange("due_date_from", undefined)
                        }
                      />
                    </Badge>
                  )}

                  {filters.due_date_to && (
                    <Badge
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      To: {new Date(filters.due_date_to).toLocaleDateString()}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() =>
                          handleFilterChange("due_date_to", undefined)
                        }
                      />
                    </Badge>
                  )}

                  {filters.currency && (
                    <Badge
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      Currency: {filters.currency}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() =>
                          handleFilterChange("currency", undefined)
                        }
                      />
                    </Badge>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default RFQFilters;
