import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FileText,
  Plus,
  Check,
  X,
  DollarSign,
  Calculator,
  Eye,
  Save,
  Send,
  AlertCircle,
  Info
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { QuoteApiService, Quote } from '@/services/api/quotes';
import { formatCurrency } from '@/types/rfq';
import { useToast } from '@/hooks/use-toast';

interface QuoteBuilderProps {
  rfqId: number;
  onQuoteGenerated?: (quote: Quote) => void;
  onCancel?: () => void;
}

interface Submission {
  id: number;
  vendor_id: number;
  vendor_name: string;
  vendor_email: string;
  item_id: number;
  item_name: string;
  item_description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  currency: string;
  delivery_time: string;
  notes?: string;
  status: string;
}

interface SelectedBid {
  submissionId: number;
  vendorName: string;
  itemName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  currency: string;
}

const QuoteBuilder: React.FC<QuoteBuilderProps> = ({ rfqId, onQuoteGenerated, onCancel }) => {
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [selectedBids, setSelectedBids] = useState<Record<string, SelectedBid>>({});
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Quote configuration
  const [quoteConfig, setQuoteConfig] = useState({
    title: '',
    client_name: '',
    client_email: '',
    currency: 'USD',
    margin_percentage: 10,
    taxes: 0,
    expires_in_days: 30,
    terms: '',
    notes: ''
  });

  useEffect(() => {
    loadSubmissions();
  }, [rfqId]);

  const loadSubmissions = async () => {
    try {
      setLoading(true);
      const response = await QuoteApiService.getSubmissionsForQuote(rfqId);
      if (response.success) {
        setSubmissions(response.data.submissions || []);
        // Auto-generate quote title
        if (response.data.rfq) {
          setQuoteConfig(prev => ({
            ...prev,
            title: `Quote for ${response.data.rfq.title}`,
            client_name: response.data.rfq.client_name || '',
            client_email: response.data.rfq.client_email || ''
          }));
        }
      } else {
        setError(response.message || 'Failed to load submissions');
      }
    } catch (err) {
      console.error('Error loading submissions:', err);
      setError('Failed to load submissions');
    } finally {
      setLoading(false);
    }
  };

  const handleBidSelection = (submission: Submission, selected: boolean) => {
    const bidKey = `${submission.item_id}_${submission.vendor_id}`;
    
    if (selected) {
      setSelectedBids(prev => ({
        ...prev,
        [bidKey]: {
          submissionId: submission.id,
          vendorName: submission.vendor_name,
          itemName: submission.item_name,
          quantity: submission.quantity,
          unitPrice: submission.unit_price,
          totalPrice: submission.total_price,
          currency: submission.currency
        }
      }));
    } else {
      setSelectedBids(prev => {
        const newSelected = { ...prev };
        delete newSelected[bidKey];
        return newSelected;
      });
    }
  };

  const calculateTotals = () => {
    const subtotal = Object.values(selectedBids).reduce((sum, bid) => sum + bid.totalPrice, 0);
    const margin = subtotal * (quoteConfig.margin_percentage / 100);
    const taxes = quoteConfig.taxes;
    const total = subtotal + margin + taxes;
    
    return { subtotal, margin, taxes, total };
  };

  const handleGenerateQuote = async () => {
    if (Object.keys(selectedBids).length === 0) {
      toast({
        title: 'No Items Selected',
        description: 'Please select at least one bid to generate a quote.',
        variant: 'destructive',
      });
      return;
    }

    if (!quoteConfig.title || !quoteConfig.client_name || !quoteConfig.client_email) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setGenerating(true);
      const response = await QuoteApiService.generateQuoteFromSubmissions(rfqId, {
        selectedBids,
        ...quoteConfig
      });
      
      if (response.success) {
        toast({
          title: 'Quote Generated',
          description: 'Quote has been generated successfully.',
        });
        onQuoteGenerated?.(response.data);
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to generate quote',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error generating quote:', err);
      toast({
        title: 'Error',
        description: 'Failed to generate quote',
        variant: 'destructive',
      });
    } finally {
      setGenerating(false);
    }
  };

  const groupedSubmissions = submissions.reduce((acc, submission) => {
    const key = submission.item_name;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(submission);
    return acc;
  }, {} as Record<string, Submission[]>);

  const totals = calculateTotals();

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading submissions...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Generate Quote</h3>
          <p className="text-sm text-muted-foreground">
            Select vendor bids and configure quote details
          </p>
        </div>
        <div className="flex gap-2">
          {onCancel && (
            <Button variant="outline" onClick={onCancel}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
          )}
          <Button onClick={handleGenerateQuote} disabled={generating}>
            {generating ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <FileText className="w-4 h-4 mr-2" />
            )}
            Generate Quote
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Bid Selection */}
        <div className="lg:col-span-2 space-y-4">
          {Object.keys(groupedSubmissions).length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Submissions Found</h3>
                <p className="text-muted-foreground">
                  No vendor submissions are available for this RFQ.
                </p>
              </CardContent>
            </Card>
          ) : (
            Object.entries(groupedSubmissions).map(([itemName, itemSubmissions]) => (
              <Card key={itemName}>
                <CardHeader>
                  <CardTitle className="text-base">{itemName}</CardTitle>
                  <CardDescription>
                    {itemSubmissions.length} vendor{itemSubmissions.length !== 1 ? 's' : ''} submitted bids
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">Select</TableHead>
                        <TableHead>Vendor</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead>Delivery</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {itemSubmissions.map((submission) => {
                        const bidKey = `${submission.item_id}_${submission.vendor_id}`;
                        const isSelected = bidKey in selectedBids;
                        
                        return (
                          <TableRow key={submission.id} className={isSelected ? 'bg-blue-50' : ''}>
                            <TableCell>
                              <Checkbox
                                checked={isSelected}
                                onCheckedChange={(checked) => 
                                  handleBidSelection(submission, checked as boolean)
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium">{submission.vendor_name}</div>
                                <div className="text-sm text-muted-foreground">{submission.vendor_email}</div>
                              </div>
                            </TableCell>
                            <TableCell>
                              {formatCurrency(submission.unit_price, submission.currency)}
                            </TableCell>
                            <TableCell>{submission.quantity || 0}</TableCell>
                            <TableCell className="font-medium">
                              {formatCurrency(submission.total_price, submission.currency)}
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">{submission.delivery_time}</Badge>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Right Column - Quote Configuration */}
        <div className="space-y-4">
          {/* Quote Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Quote Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Quote Title *</Label>
                <Input
                  id="title"
                  value={quoteConfig.title}
                  onChange={(e) => setQuoteConfig(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter quote title"
                />
              </div>
              
              <div>
                <Label htmlFor="client_name">Client Name *</Label>
                <Input
                  id="client_name"
                  value={quoteConfig.client_name}
                  onChange={(e) => setQuoteConfig(prev => ({ ...prev, client_name: e.target.value }))}
                  placeholder="Enter client name"
                />
              </div>
              
              <div>
                <Label htmlFor="client_email">Client Email *</Label>
                <Input
                  id="client_email"
                  type="email"
                  value={quoteConfig.client_email}
                  onChange={(e) => setQuoteConfig(prev => ({ ...prev, client_email: e.target.value }))}
                  placeholder="Enter client email"
                />
              </div>
              
              <div>
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={quoteConfig.currency}
                  onValueChange={(value) => setQuoteConfig(prev => ({ ...prev, currency: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                    <SelectItem value="CAD">CAD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="margin">Margin Percentage</Label>
                <Input
                  id="margin"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={quoteConfig.margin_percentage}
                  onChange={(e) => setQuoteConfig(prev => ({ ...prev, margin_percentage: parseFloat(e.target.value) || 0 }))}
                />
              </div>
              
              <div>
                <Label htmlFor="taxes">Taxes</Label>
                <Input
                  id="taxes"
                  type="number"
                  min="0"
                  step="0.01"
                  value={quoteConfig.taxes}
                  onChange={(e) => setQuoteConfig(prev => ({ ...prev, taxes: parseFloat(e.target.value) || 0 }))}
                />
              </div>
              
              <div>
                <Label htmlFor="expires_in_days">Expires In (Days)</Label>
                <Input
                  id="expires_in_days"
                  type="number"
                  min="1"
                  value={quoteConfig.expires_in_days}
                  onChange={(e) => setQuoteConfig(prev => ({ ...prev, expires_in_days: parseInt(e.target.value) || 30 }))}
                />
              </div>
            </CardContent>
          </Card>

          {/* Quote Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center">
                <Calculator className="w-4 h-4 mr-2" />
                Quote Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>{formatCurrency(totals.subtotal, quoteConfig.currency)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Margin ({quoteConfig.margin_percentage}%):</span>
                <span>{formatCurrency(totals.margin, quoteConfig.currency)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Taxes:</span>
                <span>{formatCurrency(totals.taxes, quoteConfig.currency)}</span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between font-semibold">
                  <span>Total:</span>
                  <span>{formatCurrency(totals.total, quoteConfig.currency)}</span>
                </div>
              </div>
              
              {Object.keys(selectedBids).length > 0 && (
                <div className="mt-4">
                  <p className="text-xs text-muted-foreground mb-2">
                    Selected Items: {Object.keys(selectedBids).length}
                  </p>
                  <div className="space-y-1">
                    {Object.values(selectedBids).map((bid, index) => (
                      <div key={index} className="text-xs flex justify-between">
                        <span className="truncate">{bid.itemName}</span>
                        <span>{formatCurrency(bid.totalPrice, bid.currency)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Additional Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Additional Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="terms">Terms & Conditions</Label>
                <Textarea
                  id="terms"
                  value={quoteConfig.terms}
                  onChange={(e) => setQuoteConfig(prev => ({ ...prev, terms: e.target.value }))}
                  placeholder="Enter terms and conditions"
                  rows={3}
                />
              </div>
              
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={quoteConfig.notes}
                  onChange={(e) => setQuoteConfig(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Enter additional notes"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export { QuoteBuilder };