import React, { useState, useMemo } from 'react';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  DollarSign,
  Package,
  Download,
  Filter,
  Eye,
  PieChart
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { RFQSubmission, BidItem } from '@/types/rfq';
import { formatCurrency } from '@/types/rfq';

interface RFQItemLocal {
  id: string;
  description?: string;
  name?: string;
  quantity?: number;
  specifications?: Record<string, string | number | boolean>;
  unit?: string;
  category?: string;
}

interface ItemByItemAnalysisProps {
  submissions: RFQSubmission[];
  rfqItems: RFQItemLocal[];
  currency: string;
  onViewSubmission?: (submission: RFQSubmission) => void;
}

interface ItemAnalysis {
  rfqItemId: string;
  itemDescription: string;
  quantity: number;
  submissions: {
    submissionId: number;
    vendorName: string;
    unitPrice: number;
    totalPrice: number;
    isCompliant: boolean;
    specifications?: Record<string, string | number | boolean>;
    deliveryDays?: number;
    notes?: string;
  }[];
  priceStats: {
    min: number;
    max: number;
    average: number;
    variance: number;
    standardDeviation: number;
  };
  complianceRate: number;
  hasSignificantVariation: boolean;
}

interface PriceVariationAlert {
  itemId: string;
  itemDescription: string;
  variationPercentage: number;
  outlierSubmissions: {
    vendorName: string;
    price: number;
    deviationFromAverage: number;
  }[];
}

export const ItemByItemAnalysis: React.FC<ItemByItemAnalysisProps> = ({
  submissions,
  rfqItems,
  currency,
  onViewSubmission
}) => {
  const [selectedItem, setSelectedItem] = useState<string>('all');
  const [complianceFilter, setComplianceFilter] = useState<string>('all');
  const [variationFilter, setVariationFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Process item-by-item analysis
  const itemAnalysis = useMemo((): ItemAnalysis[] => {
    if (!rfqItems || rfqItems.length === 0) return [];

    return rfqItems.map(rfqItem => {
      const itemSubmissions = submissions.map(submission => {
        const bidItem = submission.bid_items?.find(
          item => item.rfq_item_id === rfqItem.id
        );

        return {
          submissionId: submission.id,
          vendorName: submission.vendor_name,
          unitPrice: bidItem?.unit_price || 0,
          totalPrice: bidItem?.total_price || 0,
          isCompliant: true, // Default to true since BidItem doesn't have compliance field
          specifications: bidItem?.specifications,
          deliveryDays: bidItem?.delivery_days,
          notes: bidItem?.notes
        };
      }).filter(item => item.unitPrice > 0); // Only include items with pricing

      const prices = itemSubmissions.map(item => item.unitPrice);
      const average = prices.length > 0 ? prices.reduce((a, b) => a + b, 0) / prices.length : 0;
      const variance = prices.length > 0 ? 
        prices.reduce((acc, price) => acc + Math.pow(price - average, 2), 0) / prices.length : 0;
      const standardDeviation = Math.sqrt(variance);
      const coefficientOfVariation = average > 0 ? (standardDeviation / average) * 100 : 0;

      return {
        rfqItemId: rfqItem.id,
        itemDescription: rfqItem.description || rfqItem.name || 'Unnamed Item',
        quantity: rfqItem.quantity || 1,
        submissions: itemSubmissions,
        priceStats: {
          min: prices.length > 0 ? Math.min(...prices) : 0,
          max: prices.length > 0 ? Math.max(...prices) : 0,
          average,
          variance,
          standardDeviation
        },
        complianceRate: itemSubmissions.length > 0 ? 
          (itemSubmissions.filter(s => s.isCompliant).length / itemSubmissions.length) * 100 : 0,
        hasSignificantVariation: coefficientOfVariation > 20 // Flag if CV > 20%
      };
    });
  }, [submissions, rfqItems]);

  // Identify price variation alerts
  const priceVariationAlerts = useMemo((): PriceVariationAlert[] => {
    return itemAnalysis
      .filter(item => item.hasSignificantVariation && item.submissions.length > 1)
      .map(item => {
        const outliers = item.submissions
          .map(submission => ({
            vendorName: submission.vendorName,
            price: submission.unitPrice,
            deviationFromAverage: Math.abs(submission.unitPrice - item.priceStats.average)
          }))
          .filter(submission => 
            submission.deviationFromAverage > item.priceStats.standardDeviation * 1.5
          )
          .sort((a, b) => b.deviationFromAverage - a.deviationFromAverage);

        const variationPercentage = item.priceStats.average > 0 ? 
          (item.priceStats.standardDeviation / item.priceStats.average) * 100 : 0;

        return {
          itemId: item.rfqItemId,
          itemDescription: item.itemDescription,
          variationPercentage,
          outlierSubmissions: outliers
        };
      })
      .sort((a, b) => b.variationPercentage - a.variationPercentage);
  }, [itemAnalysis]);

  // Filter items based on selected filters
  const filteredItems = useMemo(() => {
    let filtered = [...itemAnalysis];

    if (selectedItem !== 'all') {
      filtered = filtered.filter(item => item.rfqItemId === selectedItem);
    }

    if (complianceFilter !== 'all') {
      if (complianceFilter === 'compliant') {
        filtered = filtered.filter(item => item.complianceRate === 100);
      } else if (complianceFilter === 'non_compliant') {
        filtered = filtered.filter(item => item.complianceRate < 100);
      }
    }

    if (variationFilter !== 'all') {
      if (variationFilter === 'high_variation') {
        filtered = filtered.filter(item => item.hasSignificantVariation);
      } else if (variationFilter === 'low_variation') {
        filtered = filtered.filter(item => !item.hasSignificantVariation);
      }
    }

    return filtered;
  }, [itemAnalysis, selectedItem, complianceFilter, variationFilter]);

  const exportItemAnalysis = () => {
    const csvData = [];
    
    filteredItems.forEach(item => {
      item.submissions.forEach(submission => {
        csvData.push({
          'Item Description': item.itemDescription,
          'Quantity': item.quantity,
          'Vendor': submission.vendorName,
          'Unit Price': submission.unitPrice,
          'Total Price': submission.totalPrice,
          'Compliant': submission.isCompliant ? 'Yes' : 'No',
          'Delivery Days': submission.deliveryDays || 'N/A',
          'Specifications': submission.specifications || 'N/A',
          'Notes': submission.notes || 'N/A'
        });
      });
    });

    const csv = [
      Object.keys(csvData[0] || {}).join(','),
      ...csvData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'rfq-item-analysis.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (itemAnalysis.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Item Data Available</h3>
          <p className="text-muted-foreground">
            Item-by-item analysis requires RFQ items and submission data.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Price Variation Alerts */}
      {priceVariationAlerts.length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <AlertTriangle className="w-5 h-5" />
              Price Variation Alerts
            </CardTitle>
            <CardDescription className="text-orange-600">
              Items with significant price variations that may require review
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {priceVariationAlerts.slice(0, 3).map(alert => (
                <div key={alert.itemId} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                  <div>
                    <p className="font-medium">{alert.itemDescription}</p>
                    <p className="text-sm text-muted-foreground">
                      {alert.variationPercentage.toFixed(1)}% price variation
                    </p>
                  </div>
                  <Badge variant="outline" className="text-orange-700 border-orange-300">
                    {alert.outlierSubmissions.length} outlier{alert.outlierSubmissions.length !== 1 ? 's' : ''}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Item-by-Item Analysis</CardTitle>
              <CardDescription>
                Detailed breakdown of pricing and compliance for each RFQ item
              </CardDescription>
            </div>
            <Button onClick={exportItemAnalysis} variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Analysis
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filters:</span>
            </div>
            
            <Select value={selectedItem} onValueChange={setSelectedItem}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select Item" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Items</SelectItem>
                {itemAnalysis.map(item => (
                  <SelectItem key={item.rfqItemId} value={item.rfqItemId}>
                    {item.itemDescription}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={complianceFilter} onValueChange={setComplianceFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Compliance" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Items</SelectItem>
                <SelectItem value="compliant">Fully Compliant</SelectItem>
                <SelectItem value="non_compliant">Non-Compliant</SelectItem>
              </SelectContent>
            </Select>

            <Select value={variationFilter} onValueChange={setVariationFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Price Variation" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Variations</SelectItem>
                <SelectItem value="high_variation">High Variation</SelectItem>
                <SelectItem value="low_variation">Low Variation</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedItem('all');
                setComplianceFilter('all');
                setVariationFilter('all');
              }}
            >
              Clear Filters
            </Button>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="detailed">Detailed View</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Items</p>
                        <p className="text-2xl font-bold">{filteredItems.length}</p>
                      </div>
                      <Package className="w-8 h-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Avg Compliance</p>
                        <p className="text-2xl font-bold text-green-600">
                          {filteredItems.length > 0 ? 
                            (filteredItems.reduce((acc, item) => acc + item.complianceRate, 0) / filteredItems.length).toFixed(1)
                            : 0
                          }%
                        </p>
                      </div>
                      <CheckCircle2 className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">High Variation Items</p>
                        <p className="text-2xl font-bold text-orange-600">
                          {filteredItems.filter(item => item.hasSignificantVariation).length}
                        </p>
                      </div>
                      <BarChart3 className="w-8 h-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="border rounded-lg overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item Description</TableHead>
                      <TableHead>Qty</TableHead>
                      <TableHead>Submissions</TableHead>
                      <TableHead>Price Range</TableHead>
                      <TableHead>Avg Price</TableHead>
                      <TableHead>Compliance</TableHead>
                      <TableHead>Variation</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredItems.map(item => (
                      <TableRow key={item.rfqItemId}>
                        <TableCell>
                          <div className="font-medium">{item.itemDescription}</div>
                        </TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {item.submissions.length} bids
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatCurrency(item.priceStats.min, currency)} - {formatCurrency(item.priceStats.max, currency)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">
                            {formatCurrency(item.priceStats.average, currency)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Progress value={item.complianceRate} className="w-16 h-2" />
                            <span className="text-sm">{item.complianceRate.toFixed(0)}%</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {item.hasSignificantVariation ? (
                            <Badge variant="destructive" className="text-xs">
                              High
                            </Badge>
                          ) : (
                            <Badge variant="secondary" className="text-xs">
                              Low
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setSelectedItem(item.rfqItemId)}
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="detailed" className="space-y-4">
              {filteredItems.map(item => (
                <Card key={item.rfqItemId}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{item.itemDescription}</span>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">Qty: {item.quantity}</Badge>
                        {item.hasSignificantVariation && (
                          <Badge variant="destructive">
                            <AlertTriangle className="w-3 h-3 mr-1" />
                            High Variation
                          </Badge>
                        )}
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Min Price</p>
                          <p className="font-medium">{formatCurrency(item.priceStats.min, currency)}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Max Price</p>
                          <p className="font-medium">{formatCurrency(item.priceStats.max, currency)}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Average</p>
                          <p className="font-medium">{formatCurrency(item.priceStats.average, currency)}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Std Deviation</p>
                          <p className="font-medium">{formatCurrency(item.priceStats.standardDeviation, currency)}</p>
                        </div>
                      </div>

                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Vendor</TableHead>
                            <TableHead>Unit Price</TableHead>
                            <TableHead>Total Price</TableHead>
                            <TableHead>Compliant</TableHead>
                            <TableHead>Delivery</TableHead>
                            <TableHead>Notes</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {item.submissions.map(submission => (
                            <TableRow key={`${item.rfqItemId}-${submission.submissionId}`}>
                              <TableCell className="font-medium">
                                {submission.vendorName}
                              </TableCell>
                              <TableCell>
                                <span className={`font-medium ${
                                  submission.unitPrice === item.priceStats.min ? 'text-green-600' :
                                  submission.unitPrice === item.priceStats.max ? 'text-red-600' : ''
                                }`}>
                                  {formatCurrency(submission.unitPrice, currency)}
                                </span>
                              </TableCell>
                              <TableCell>
                                {formatCurrency(submission.totalPrice, currency)}
                              </TableCell>
                              <TableCell>
                                {submission.isCompliant ? (
                                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                                ) : (
                                  <XCircle className="w-4 h-4 text-red-600" />
                                )}
                              </TableCell>
                              <TableCell>
                                {submission.deliveryDays ? `${submission.deliveryDays} days` : 'N/A'}
                              </TableCell>
                              <TableCell>
                                <span className="text-sm text-muted-foreground">
                                  {submission.notes || 'No notes'}
                                </span>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <PieChart className="w-5 h-5" />
                      Compliance Distribution
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {[100, 75, 50, 25, 0].map(threshold => {
                        const count = filteredItems.filter(item => 
                          item.complianceRate >= threshold && 
                          (threshold === 0 || item.complianceRate < threshold + 25)
                        ).length;
                        const percentage = filteredItems.length > 0 ? (count / filteredItems.length) * 100 : 0;
                        
                        return (
                          <div key={threshold} className="flex items-center justify-between">
                            <span className="text-sm">
                              {threshold === 100 ? '100%' : 
                               threshold === 0 ? '0-24%' : 
                               `${threshold}-${threshold + 24}%`} Compliant
                            </span>
                            <div className="flex items-center gap-2">
                              <Progress value={percentage} className="w-20 h-2" />
                              <span className="text-sm font-medium w-8">{count}</span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="w-5 h-5" />
                      Price Variation Analysis
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Low Variation (&lt; 10%)</span>
                        <Badge variant="secondary">
                          {filteredItems.filter(item => {
                            const cv = item.priceStats.average > 0 ? 
                              (item.priceStats.standardDeviation / item.priceStats.average) * 100 : 0;
                            return cv < 10;
                          }).length}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Medium Variation (10-20%)</span>
                        <Badge variant="outline">
                          {filteredItems.filter(item => {
                            const cv = item.priceStats.average > 0 ? 
                              (item.priceStats.standardDeviation / item.priceStats.average) * 100 : 0;
                            return cv >= 10 && cv <= 20;
                          }).length}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">High Variation (&gt; 20%)</span>
                        <Badge variant="destructive">
                          {filteredItems.filter(item => item.hasSignificantVariation).length}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default ItemByItemAnalysis;