import React, { useState } from 'react';
import { 
  FileText, 
  Save, 
  Edit, 
  CheckCircle,
  AlertCircle,
  Brain,
  Target,
  Users,
  DollarSign
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { formatCurrency } from '@/types/rfq';

interface SelectionRationaleProps {
  selectedBids: Record<string, {
    submissionId: number;
    vendorId: number;
    vendorName: string;
    itemId: string;
    unitPrice: number;
    totalPrice: number;
    deliveryDays?: number;
    notes?: string;
  }>;
  selectionRationale: Record<string, string>;
  onRationaleChange: (itemId: string, rationale: string) => void;
  onSave: () => void;
  rfqItems: any[];
  currency: string;
  readOnly?: boolean;
}

export const SelectionRationale: React.FC<SelectionRationaleProps> = ({
  selectedBids,
  selectionRationale,
  onRationaleChange,
  onSave,
  rfqItems,
  currency,
  readOnly = false
}) => {
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [tempRationale, setTempRationale] = useState<string>('');

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const handleEditStart = (itemId: string) => {
    setEditingItem(itemId);
    setTempRationale(selectionRationale[itemId] || '');
  };

  const handleEditSave = (itemId: string) => {
    onRationaleChange(itemId, tempRationale);
    setEditingItem(null);
    setTempRationale('');
  };

  const handleEditCancel = () => {
    setEditingItem(null);
    setTempRationale('');
  };

  const calculateTotalValue = () => {
    return Object.values(selectedBids).reduce((sum, bid) => sum + bid.totalPrice, 0);
  };

  const getUniqueVendors = () => {
    const vendorIds = new Set(Object.values(selectedBids).map(bid => bid.vendorId));
    return vendorIds.size;
  };

  const generateSummaryRationale = () => {
    const totalValue = calculateTotalValue();
    const uniqueVendors = getUniqueVendors();
    const itemCount = Object.keys(selectedBids).length;
    
    const vendorCounts: Record<string, number> = {};
    Object.values(selectedBids).forEach(bid => {
      vendorCounts[bid.vendorName] = (vendorCounts[bid.vendorName] || 0) + 1;
    });

    const topVendor = Object.entries(vendorCounts).reduce((a, b) => a[1] > b[1] ? a : b);
    
    return `Selection summary: ${itemCount} items selected across ${uniqueVendors} vendors for a total value of ${formatCurrency(totalValue, currency)}. ${topVendor[0]} was selected for ${topVendor[1]} item${topVendor[1] !== 1 ? 's' : ''}, representing the highest confidence in their capabilities.`;
  };

  if (Object.keys(selectedBids).length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Target className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Selections Made</h3>
          <p className="text-muted-foreground">
            Make your vendor selections first to document your rationale.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Selection Summary */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-900">{Object.keys(selectedBids).length}</div>
              <div className="text-sm text-green-700">Items Selected</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-900">{getUniqueVendors()}</div>
              <div className="text-sm text-blue-700">Unique Vendors</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-900">{formatCurrency(calculateTotalValue(), currency)}</div>
              <div className="text-sm text-purple-700">Total Value</div>
            </div>
          </div>
          
          <div className="p-4 bg-white/50 rounded-lg border border-green-200">
            <h4 className="font-medium text-green-900 mb-2 flex items-center gap-2">
              <Brain className="w-4 h-4" />
              AI-Generated Summary
            </h4>
            <p className="text-sm text-green-800">{generateSummaryRationale()}</p>
          </div>
        </CardContent>
      </Card>

      {/* Individual Item Rationales */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Selection Rationale
          </CardTitle>
          <CardDescription>
            Document the reasoning behind each vendor selection
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {Object.entries(selectedBids).map(([itemId, bid]) => {
              const item = rfqItems.find(i => i.id === itemId);
              const hasRationale = selectionRationale[itemId]?.trim();
              const isEditing = editingItem === itemId;

              return (
                <div key={itemId} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h4 className="font-semibold text-lg">{item?.name || 'Unknown Item'}</h4>
                      <p className="text-sm text-muted-foreground">
                        Quantity: {item?.quantity || 'N/A'} | Category: {item?.category || 'N/A'}
                      </p>
                    </div>
                    <Badge className={hasRationale ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                      {hasRationale ? (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Documented
                        </>
                      ) : (
                        <>
                          <AlertCircle className="w-3 h-3 mr-1" />
                          Pending
                        </>
                      )}
                    </Badge>
                  </div>

                  {/* Selected Vendor Info */}
                  <div className="flex items-center gap-3 mb-4 p-3 bg-muted/50 rounded-lg">
                    <Avatar className="w-8 h-8">
                      <AvatarFallback className="text-xs">
                        {getInitials(bid.vendorName)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="font-medium">{bid.vendorName}</div>
                      <div className="text-sm text-muted-foreground">
                        Unit: {formatCurrency(bid.unitPrice, currency)} | 
                        Total: {formatCurrency(bid.totalPrice, currency)}
                        {bid.deliveryDays && ` | Delivery: ${bid.deliveryDays} days`}
                      </div>
                    </div>
                    <Badge variant="outline" className="bg-blue-50 text-blue-700">
                      Selected
                    </Badge>
                  </div>

                  {/* Rationale Section */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Selection Rationale</Label>
                    
                    {isEditing ? (
                      <div className="space-y-3">
                        <Textarea
                          value={tempRationale}
                          onChange={(e) => setTempRationale(e.target.value)}
                          placeholder="Explain why this vendor was selected for this item. Consider factors like price competitiveness, vendor performance history, delivery capabilities, technical specifications, risk factors, and strategic considerations..."
                          rows={4}
                          className="text-sm"
                        />
                        <div className="flex items-center gap-2">
                          <Button 
                            size="sm" 
                            onClick={() => handleEditSave(itemId)}
                            disabled={!tempRationale.trim()}
                          >
                            <Save className="w-3 h-3 mr-1" />
                            Save
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline" 
                            onClick={handleEditCancel}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {hasRationale ? (
                          <div className="p-3 bg-muted/30 rounded-lg border">
                            <p className="text-sm whitespace-pre-wrap">{selectionRationale[itemId]}</p>
                          </div>
                        ) : (
                          <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                            <p className="text-sm text-yellow-800">
                              No rationale provided yet. Click "Add Rationale" to document your selection reasoning.
                            </p>
                          </div>
                        )}
                        
                        {!readOnly && (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleEditStart(itemId)}
                          >
                            <Edit className="w-3 h-3 mr-1" />
                            {hasRationale ? 'Edit Rationale' : 'Add Rationale'}
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      {!readOnly && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                {Object.values(selectionRationale).filter(r => r?.trim()).length} of {Object.keys(selectedBids).length} items documented
              </div>
              
              <div className="flex items-center gap-2">
                <Button variant="outline">
                  <Brain className="w-4 h-4 mr-2" />
                  Generate AI Rationales
                </Button>
                <Button onClick={onSave}>
                  <Save className="w-4 h-4 mr-2" />
                  Save All Rationales
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SelectionRationale;