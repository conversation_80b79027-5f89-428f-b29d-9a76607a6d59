import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Send, 
  Eye, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Mail,
  RefreshCw,
  Plus,
  MoreHorizontal
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Progress } from '@/components/ui/progress';
import { RFQApiService } from '@/services/api/rfq';
import { RFQInvitation } from '@/types/rfq';
import { getInvitationStatusColor } from '@/types/rfq';
import RFQErrorHandler from '@/services/api/rfqErrorHandler';
import VendorSelectionDialog from '@/components/rfq/VendorSelectionDialog';

interface InvitationsTabProps {
  rfqId: number;
  canEdit?: boolean;
}

export const InvitationsTab: React.FC<InvitationsTabProps> = ({
  rfqId,
  canEdit = false
}) => {
  const [invitations, setInvitations] = useState<RFQInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showVendorSelection, setShowVendorSelection] = useState(false);

  const loadInvitations = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await RFQApiService.getRFQInvitations(rfqId);
      setInvitations(response.data || []);
    } catch (err) {
      setError('Failed to load invitations');
      RFQErrorHandler.handleError(err, 'Loading invitations');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInvitations();
  }, [rfqId]);

  const handleResendInvitation = async (invitationId: number) => {
    try {
      await RFQApiService.resendInvitation(invitationId);
      await loadInvitations();
    } catch (err) {
      RFQErrorHandler.handleError(err, 'Resending invitation');
    }
  };

  const handleSendReminder = async (invitationId: number, message?: string) => {
    try {
      await RFQApiService.sendReminder(invitationId, message);
      await loadInvitations();
    } catch (err) {
      RFQErrorHandler.handleError(err, 'Sending reminder');
    }
  };

  const handleAddVendors = async (vendorIds: number[]) => {
    try {
      await RFQApiService.addVendorsToRFQ(rfqId, vendorIds);
      await loadInvitations();
    } catch (err) {
      RFQErrorHandler.handleError(err, 'Adding vendors to RFQ');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Send className="w-4 h-4 text-blue-500" />;
      case 'viewed':
        return <Eye className="w-4 h-4 text-yellow-500" />;
      case 'submitted':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not yet';
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const calculateStats = () => {
    const total = invitations.length;
    const sent = invitations.filter(inv => inv.status === 'sent').length;
    const viewed = invitations.filter(inv => inv.viewed_at != null).length;
    const submitted = invitations.filter(inv => inv.status === 'submitted').length;
    const responseRate = total > 0 ? Math.round((submitted / total) * 100) : 0;

    return { total, sent, viewed, submitted, responseRate };
  };

  const stats = calculateStats();

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="h-12 bg-muted rounded"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <AlertCircle className="w-12 h-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Failed to Load Invitations</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={loadInvitations}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Invitations</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Users className="w-8 h-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Viewed</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.viewed}</p>
              </div>
              <Eye className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Submitted</p>
                <p className="text-2xl font-bold text-green-600">{stats.submitted}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Response Rate</p>
                <p className="text-2xl font-bold">{stats.responseRate}%</p>
              </div>
              <div className="w-8 h-8 flex items-center justify-center">
                <Progress value={stats.responseRate} className="w-6 h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Invitations Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Mail className="w-5 h-5" />
                Vendor Invitations
              </CardTitle>
              <CardDescription>
                Track invitation status and vendor engagement
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={loadInvitations}
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              
              {canEdit && (
                <Button size="sm" onClick={() => setShowVendorSelection(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Vendors
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {invitations.length === 0 ? (
            <div className="text-center py-12">
              <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Invitations Sent</h3>
              <p className="text-muted-foreground mb-4">
                No vendors have been invited to this RFQ yet.
              </p>
              {canEdit && (
                <Button onClick={() => setShowVendorSelection(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Invite Vendors
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Vendor</TableHead>
                  <TableHead>Performance Score</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Sent</TableHead>
                  <TableHead>Viewed</TableHead>
                  <TableHead>Submitted</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invitations.map((invitation) => (
                  <TableRow key={invitation.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="w-8 h-8">
                          <AvatarFallback className="text-xs">
                            {getInitials(invitation.vendor_name)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{invitation.vendor_name}</div>
                          <div className="text-sm text-muted-foreground">
                            {invitation.vendor_email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="text-sm font-medium">
                          {(invitation.vendor_performance_score || 0).toFixed(1)}
                        </div>
                        <Progress 
                          value={invitation.vendor_performance_score || 0} 
                          className="w-16 h-2" 
                        />
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge 
                        variant="secondary" 
                        className={`bg-${getInvitationStatusColor(invitation.status)}-100 text-${getInvitationStatusColor(invitation.status)}-800`}
                      >
                        {getStatusIcon(invitation.status)}
                        <span className="ml-1 capitalize">{invitation.status}</span>
                      </Badge>
                    </TableCell>
                    
                    <TableCell className="text-sm text-muted-foreground">
                      {formatDate(invitation.sent_at)}
                    </TableCell>
                    
                    <TableCell className="text-sm text-muted-foreground">
                      {formatDate(invitation.viewed_at)}
                    </TableCell>
                    
                    <TableCell className="text-sm text-muted-foreground">
                      {formatDate(invitation.submitted_at)}
                    </TableCell>
                    
                    <TableCell className="text-sm text-muted-foreground">
                      {formatDate(invitation.expires_at)}
                    </TableCell>
                    
                    <TableCell>
                      {canEdit && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem 
                              onClick={() => handleResendInvitation(invitation.id)}
                            >
                              <Send className="w-4 h-4 mr-2" />
                              Resend Invitation
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleSendReminder(invitation.id)}
                            >
                              <Mail className="w-4 h-4 mr-2" />
                              Send Reminder
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <VendorSelectionDialog
        isOpen={showVendorSelection}
        onClose={() => setShowVendorSelection(false)}
        onVendorsSelected={handleAddVendors}
        excludeVendorIds={invitations.map(inv => inv.vendor_id)}
        title="Add Vendors to RFQ"
        description="Select additional vendors to invite to this RFQ"
      />
    </div>
  );
};

export default InvitationsTab;