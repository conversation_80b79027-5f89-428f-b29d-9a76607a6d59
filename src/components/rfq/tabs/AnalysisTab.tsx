import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  TrendingUp, 
  Alert<PERSON>riangle,
  CheckCircle,
  Star,
  Target,
  BarChart3,
  Lightbulb,
  Shield,
  RefreshCw,
  Zap
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { RFQApiService } from '@/services/api/rfq';
import { formatCurrency } from '@/types/rfq';
import RFQErrorHandler from '@/services/api/rfqErrorHandler';

interface AnalysisTabProps {
  rfqId: number;
  currency: string;
}

interface AIRecommendation {
  vendor_recommendations: Array<{
    vendor_id: number;
    vendor_name: string;
    score: number;
    reasoning: string;
    risk_factors: string[];
    advantages: string[];
  }>;
  bid_analysis: Array<{
    submission_id: number;
    vendor_name: string;
    overall_score: number;
    price_score: number;
    performance_score: number;
    delivery_score: number;
    risk_score: number;
    recommendation: 'highly_recommended' | 'recommended' | 'consider' | 'not_recommended';
    reasoning: string;
  }>;
  diversification_suggestions: Array<{
    item_id: string;
    item_name: string;
    suggested_vendors: number[];
    reasoning: string;
  }>;
}

export const AnalysisTab: React.FC<AnalysisTabProps> = ({
  rfqId,
  currency
}) => {
  const [recommendations, setRecommendations] = useState<AIRecommendation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadAnalysis = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await RFQApiService.getAIRecommendations(rfqId);
      setRecommendations(response.data);
    } catch (err) {
      setError('Failed to load AI analysis');
      RFQErrorHandler.handleError(err, 'Loading AI analysis');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnalysis();
  }, [rfqId]);

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'highly_recommended':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'recommended':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'consider':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'not_recommended':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRecommendationIcon = (recommendation: string) => {
    switch (recommendation) {
      case 'highly_recommended':
        return <Star className="w-4 h-4" />;
      case 'recommended':
        return <CheckCircle className="w-4 h-4" />;
      case 'consider':
        return <AlertTriangle className="w-4 h-4" />;
      case 'not_recommended':
        return <Shield className="w-4 h-4" />;
      default:
        return <Target className="w-4 h-4" />;
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold mb-2">Analyzing Submissions</h3>
            <p className="text-muted-foreground">
              Our AI is analyzing vendor submissions and generating recommendations...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !recommendations) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Brain className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Analysis Unavailable</h3>
          <p className="text-muted-foreground mb-4">
            {error || 'AI analysis is not available for this RFQ yet.'}
          </p>
          <Button onClick={loadAnalysis}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* AI Analysis Header */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Brain className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900">AI-Powered Analysis</h3>
              <p className="text-blue-700">
                Smart recommendations based on price, performance, delivery, and risk factors
              </p>
            </div>
            <div className="ml-auto">
              <Button variant="outline" size="sm" onClick={loadAnalysis}>
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh Analysis
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {recommendations && (
        <Tabs defaultValue="recommendations" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="recommendations">Vendor Recommendations</TabsTrigger>
            <TabsTrigger value="analysis">Bid Analysis</TabsTrigger>
          <TabsTrigger value="diversification">Diversification</TabsTrigger>
        </TabsList>

        {/* Vendor Recommendations */}
        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                Top Vendor Recommendations
              </CardTitle>
              <CardDescription>
                AI-ranked vendors based on your criteria and historical performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recommendations?.vendor_recommendations?.map((vendor, index) => (
                  <div key={vendor.vendor_id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <Avatar className="w-10 h-10">
                            <AvatarFallback>
                              {getInitials(vendor.vendor_name)}
                            </AvatarFallback>
                          </Avatar>
                          {index === 0 && (
                            <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
                              <Star className="w-3 h-3 text-white" />
                            </div>
                          )}
                        </div>
                        <div>
                          <h4 className="font-semibold">{vendor.vendor_name}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm text-muted-foreground">AI Score:</span>
                            <div className="flex items-center gap-1">
                              <Progress value={vendor.score} className="w-20 h-2" />
                              <span className="text-sm font-medium">{vendor.score.toFixed(1)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        #{index + 1} Recommended
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h5 className="font-medium text-green-700 mb-2 flex items-center gap-1">
                          <CheckCircle className="w-4 h-4" />
                          Advantages
                        </h5>
                        <ul className="space-y-1">
                          {vendor.advantages.map((advantage, i) => (
                            <li key={i} className="text-sm text-muted-foreground flex items-start gap-2">
                              <div className="w-1 h-1 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h5 className="font-medium text-orange-700 mb-2 flex items-center gap-1">
                          <AlertTriangle className="w-4 h-4" />
                          Risk Factors
                        </h5>
                        <ul className="space-y-1">
                          {vendor.risk_factors.map((risk, i) => (
                            <li key={i} className="text-sm text-muted-foreground flex items-start gap-2">
                              <div className="w-1 h-1 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                              {risk}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    <div className="mt-3 p-3 bg-muted/50 rounded-lg">
                      <p className="text-sm text-muted-foreground">
                        <strong>AI Reasoning:</strong> {vendor.reasoning}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Bid Analysis */}
        <TabsContent value="analysis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-blue-500" />
                Detailed Bid Analysis
              </CardTitle>
              <CardDescription>
                Comprehensive scoring across multiple criteria
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {recommendations?.bid_analysis?.map((analysis) => (
                  <div key={analysis.submission_id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <Avatar className="w-8 h-8">
                          <AvatarFallback className="text-xs">
                            {getInitials(analysis.vendor_name)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h4 className="font-semibold">{analysis.vendor_name}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm text-muted-foreground">Overall Score:</span>
                            <span className="font-medium">{analysis.overall_score.toFixed(1)}/100</span>
                          </div>
                        </div>
                      </div>
                      <Badge className={getRecommendationColor(analysis.recommendation)}>
                        {getRecommendationIcon(analysis.recommendation)}
                        <span className="ml-1 capitalize">{analysis.recommendation.replace('_', ' ')}</span>
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-muted-foreground">Price</span>
                          <span className="text-sm font-medium">{analysis.price_score.toFixed(1)}</span>
                        </div>
                        <Progress value={analysis.price_score} className="h-2" />
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-muted-foreground">Performance</span>
                          <span className="text-sm font-medium">{analysis.performance_score.toFixed(1)}</span>
                        </div>
                        <Progress value={analysis.performance_score} className="h-2" />
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-muted-foreground">Delivery</span>
                          <span className="text-sm font-medium">{analysis.delivery_score.toFixed(1)}</span>
                        </div>
                        <Progress value={analysis.delivery_score} className="h-2" />
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-muted-foreground">Risk</span>
                          <span className="text-sm font-medium">{analysis.risk_score.toFixed(1)}</span>
                        </div>
                        <Progress value={analysis.risk_score} className="h-2" />
                      </div>
                    </div>

                    <div className="p-3 bg-muted/50 rounded-lg">
                      <p className="text-sm text-muted-foreground">
                        <strong>Analysis:</strong> {analysis.reasoning}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Diversification Suggestions */}
        <TabsContent value="diversification" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="w-5 h-5 text-yellow-500" />
                Diversification Suggestions
              </CardTitle>
              <CardDescription>
                AI recommendations for optimal vendor distribution across items
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recommendations?.diversification_suggestions?.length === 0 ? (
                <div className="text-center py-8">
                  <Zap className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Diversification Needed</h3>
                  <p className="text-muted-foreground">
                    Current vendor selection provides optimal diversification for this RFQ.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recommendations?.diversification_suggestions?.map((suggestion) => (
                    <div key={suggestion.item_id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-semibold">{suggestion.item_name}</h4>
                          <p className="text-sm text-muted-foreground">Item ID: {suggestion.item_id}</p>
                        </div>
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
                          <Lightbulb className="w-3 h-3 mr-1" />
                          Suggestion
                        </Badge>
                      </div>

                      <div className="mb-3">
                        <h5 className="font-medium mb-2">Suggested Vendors:</h5>
                        <div className="flex flex-wrap gap-2">
                          {suggestion.suggested_vendors.map((vendorId) => {
                            const vendor = recommendations?.vendor_recommendations?.find(v => v.vendor_id === vendorId);
                            return vendor ? (
                              <Badge key={vendorId} variant="secondary">
                                {vendor.vendor_name}
                              </Badge>
                            ) : (
                              <Badge key={vendorId} variant="secondary">
                                Vendor #{vendorId}
                              </Badge>
                            );
                          })}
                        </div>
                      </div>

                      <div className="p-3 bg-muted/50 rounded-lg">
                        <p className="text-sm text-muted-foreground">
                          <strong>Reasoning:</strong> {suggestion.reasoning}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      )}
    </div>
  );
};

export default AnalysisTab;