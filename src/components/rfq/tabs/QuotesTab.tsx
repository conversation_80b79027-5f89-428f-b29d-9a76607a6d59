import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FileText,
  Plus,
  Send,
  Eye,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Mail,
  User,
  Calendar,
  MoreHorizontal,
  Edit,
  Trash2,
  ExternalLink
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { QuoteApiService, Quote } from '@/services/api/quotes';
import { formatCurrency } from '@/types/rfq';
import { useToast } from '@/hooks/use-toast';
import { QuoteBuilderDialog } from '../QuoteBuilderDialog';
import AdvancedQuoteBuilder from '../AdvancedQuoteBuilder';

interface QuotesTabProps {
  rfqId: number;
  currency?: string;
  canEdit: boolean;
}

const QuotesTab: React.FC<QuotesTabProps> = ({ rfqId, currency = 'USD', canEdit }) => {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showQuoteBuilder, setShowQuoteBuilder] = useState(false);
  const [showAdvancedBuilder, setShowAdvancedBuilder] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadQuotes();
  }, [rfqId]);

  const loadQuotes = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await QuoteApiService.getRFQQuotes(rfqId);
      if (response.success) {
        setQuotes(response.data);
      } else {
        setError(response.message || 'Failed to load quotes');
      }
    } catch (err) {
      console.error('Error loading quotes:', err);
      setError('Failed to load quotes');
    } finally {
      setLoading(false);
    }
  };

  const handleSendQuote = async (quoteId: number) => {
    try {
      const response = await QuoteApiService.sendQuote(quoteId);
      if (response.success) {
        toast({
          title: 'Quote Sent',
          description: 'Quote has been sent to the client successfully.',
        });
        loadQuotes(); // Refresh the list
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to send quote',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error sending quote:', err);
      toast({
        title: 'Error',
        description: 'Failed to send quote',
        variant: 'destructive',
      });
    }
  };

  const handleDownloadPDF = async (quoteId: number) => {
    try {
      const response = await QuoteApiService.generateQuotePDF(quoteId);
      if (response.success && response.data) {
        // Create a blob from the PDF data and trigger download
        const blob = new Blob([response.data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `quote-${quoteId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to generate PDF',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error downloading PDF:', err);
      toast({
        title: 'Error',
        description: 'Failed to download PDF',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteQuote = async (quoteId: number) => {
    if (!confirm('Are you sure you want to delete this quote?')) {
      return;
    }

    try {
      const response = await QuoteApiService.deleteQuote(quoteId);
      if (response.success) {
        toast({
          title: 'Quote Deleted',
          description: 'Quote has been deleted successfully.',
        });
        loadQuotes(); // Refresh the list
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to delete quote',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error deleting quote:', err);
      toast({
        title: 'Error',
        description: 'Failed to delete quote',
        variant: 'destructive',
      });
    }
  };

  const handleQuoteGenerated = (newQuote: Quote) => {
    setQuotes(prev => [newQuote, ...prev]);
    toast({
      title: 'Quote Generated',
      description: 'Quote has been generated successfully.',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: 'secondary' as const, icon: Edit },
      sent: { variant: 'default' as const, icon: Send },
      viewed: { variant: 'outline' as const, icon: Eye },
      approved: { variant: 'default' as const, icon: CheckCircle, className: 'bg-green-100 text-green-800' },
      rejected: { variant: 'destructive' as const, icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className={config.className}>
        <Icon className="w-3 h-3 mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading quotes...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Generated Quotes</h3>
          <p className="text-sm text-muted-foreground">
            Client quotes generated from vendor submissions
          </p>
        </div>
        {canEdit && (
          <div className="flex items-center gap-2">
            <Button onClick={() => setShowQuoteBuilder(true)} variant="outline">
              <Plus className="w-4 h-4 mr-2" />
              Basic Quote
            </Button>
            <Button onClick={() => setShowAdvancedBuilder(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Advanced Quote
            </Button>
          </div>
        )}
      </div>

      {quotes.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Quotes Generated</h3>
            <p className="text-muted-foreground mb-4">
              No quotes have been generated for this RFQ yet.
            </p>
            {canEdit && (
              <div className="flex items-center gap-2">
                <Button onClick={() => setShowQuoteBuilder(true)} variant="outline">
                  <Plus className="w-4 h-4 mr-2" />
                  Basic Quote
                </Button>
                <Button onClick={() => setShowAdvancedBuilder(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Advanced Quote
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Quotes ({quotes.length})</CardTitle>
            <CardDescription>
              Manage and track client quotes for this RFQ
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Quote</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {quotes.map((quote) => (
                  <TableRow key={quote.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{quote.title}</div>
                        {quote.notes && (
                          <div className="text-sm text-muted-foreground truncate max-w-xs">
                            {quote.notes}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{quote.client_name}</div>
                        <div className="text-sm text-muted-foreground">{quote.client_email}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {formatCurrency(quote.total_amount, quote.currency)}
                      </div>
                      {quote.margin_percentage > 0 && (
                        <div className="text-sm text-muted-foreground">
                          {quote.margin_percentage}% margin
                        </div>
                      )}
                    </TableCell>
                    <TableCell>{getStatusBadge(quote.status)}</TableCell>
                    <TableCell>
                      <div className="text-sm">{formatDateTime(quote.created_at)}</div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {quote.expires_at ? formatDateTime(quote.expires_at) : 'No expiry'}
                      </div>
                      {quote.is_expired && (
                        <Badge variant="destructive" className="text-xs">
                          Expired
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleDownloadPDF(quote.id)}>
                            <Download className="w-4 h-4 mr-2" />
                            Download PDF
                          </DropdownMenuItem>
                          {quote.status === 'draft' && canEdit && (
                            <DropdownMenuItem onClick={() => handleSendQuote(quote.id)}>
                              <Send className="w-4 h-4 mr-2" />
                              Send to Client
                            </DropdownMenuItem>
                          )}
                          {quote.public_token && (
                            <DropdownMenuItem
                              onClick={() => {
                                const url = `${window.location.origin}/quotes/public/${quote.public_token}`;
                                navigator.clipboard.writeText(url);
                                toast({
                                  title: 'Link Copied',
                                  description: 'Public quote link copied to clipboard',
                                });
                              }}
                            >
                              <ExternalLink className="w-4 h-4 mr-2" />
                              Copy Public Link
                            </DropdownMenuItem>
                          )}
                          {canEdit && (
                            <DropdownMenuItem
                              onClick={() => handleDeleteQuote(quote.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
      
      <QuoteBuilderDialog
        open={showQuoteBuilder}
        onOpenChange={setShowQuoteBuilder}
        rfqId={rfqId}
        onQuoteGenerated={handleQuoteGenerated}
      />
      
      <AdvancedQuoteBuilder
        rfqId={rfqId}
        isOpen={showAdvancedBuilder}
        onClose={() => setShowAdvancedBuilder(false)}
        onQuoteGenerated={handleQuoteGenerated}
      />
    </div>
  );
};

export default QuotesTab;