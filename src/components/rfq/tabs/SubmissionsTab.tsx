import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Clock,
  Truck,
  Star,
  Eye,
  Download,
  Filter,
  ArrowUpDown,
  CheckCircle2,
  AlertCircle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { RFQApiService } from '@/services/api/rfq';
import { RFQSubmission, BidComparisonData, RFQ } from '@/types/rfq';
import { formatCurrency } from '@/types/rfq';
import RFQErrorHandler from '@/services/api/rfqErrorHandler';
import SubmissionDetailModal from '@/components/rfq/SubmissionDetailModal';
import SubmissionComparison from '@/components/rfq/SubmissionComparison';
import ItemByItemAnalysis from '@/components/rfq/ItemByItemAnalysis';

interface SubmissionsTabProps {
  rfqId: number;
  currency: string;
  canEdit?: boolean;
  rfq?: RFQ;
}

export const SubmissionsTab: React.FC<SubmissionsTabProps> = ({
  rfqId,
  currency,
  canEdit = false,
  rfq
}) => {
  const [submissions, setSubmissions] = useState<RFQSubmission[]>([]);
  const [comparisonData, setComparisonData] = useState<BidComparisonData | null>(null);
  const [rfqData, setRfqData] = useState<RFQ | null>(rfq || null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'price' | 'performance' | 'delivery'>('price');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedSubmissions, setSelectedSubmissions] = useState<number[]>([]);
  const [selectedSubmission, setSelectedSubmission] = useState<RFQSubmission | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  const loadSubmissions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [submissionsResponse, comparisonResponse] = await Promise.all([
        RFQApiService.getRFQSubmissions(rfqId),
        RFQApiService.getBidComparison(rfqId)
      ]);
      
      setSubmissions(submissionsResponse.data || []);
      setComparisonData(comparisonResponse.data || null);
      
      // Load RFQ data if not provided
      if (!rfqData) {
        try {
          const rfqResponse = await RFQApiService.getRFQById(rfqId);
          setRfqData(rfqResponse.data || null);
        } catch (rfqErr) {
          console.warn('Failed to load RFQ data:', rfqErr);
        }
      }
    } catch (err) {
      setError('Failed to load submissions');
      RFQErrorHandler.handleError(err, 'Loading submissions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSubmissions();
  }, [rfqId]);

  const handleSort = (field: 'price' | 'performance' | 'delivery') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const getSortedSubmissions = () => {
    return [...submissions].sort((a, b) => {
      let aValue: number;
      let bValue: number;

      switch (sortBy) {
        case 'price':
          aValue = a.total_amount;
          bValue = b.total_amount;
          break;
        case 'performance':
          aValue = a.vendor_performance_score || 0;
          bValue = b.vendor_performance_score || 0;
          break;
        case 'delivery':
          aValue = a.delivery_days || 999;
          bValue = b.delivery_days || 999;
          break;
        default:
          return 0;
      }

      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRankBadge = (index: number) => {
    const rank = index + 1;
    if (rank === 1) return <Badge className="bg-yellow-100 text-yellow-800">1st</Badge>;
    if (rank === 2) return <Badge className="bg-gray-100 text-gray-800">2nd</Badge>;
    if (rank === 3) return <Badge className="bg-orange-100 text-orange-800">3rd</Badge>;
    return <Badge variant="outline">{rank}th</Badge>;
  };

  const calculateStats = () => {
    if (submissions.length === 0) {
      return {
        totalSubmissions: 0,
        averageBid: 0,
        lowestBid: 0,
        highestBid: 0,
        averageDelivery: 0
      };
    }

    const amounts = submissions.map(s => s.total_amount);
    const deliveryTimes = submissions.filter(s => s.delivery_days).map(s => s.delivery_days!);

    return {
      totalSubmissions: submissions.length,
      averageBid: amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length,
      lowestBid: Math.min(...amounts),
      highestBid: Math.max(...amounts),
      averageDelivery: deliveryTimes.length > 0 
        ? deliveryTimes.reduce((sum, days) => sum + days, 0) / deliveryTimes.length 
        : 0
    };
  };

  const stats = calculateStats();

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="h-12 bg-muted rounded"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <AlertCircle className="w-12 h-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Failed to Load Submissions</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={loadSubmissions}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Submissions</p>
                <p className="text-2xl font-bold">{stats.totalSubmissions}</p>
              </div>
              <FileText className="w-8 h-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Bid</p>
                <p className="text-2xl font-bold">{formatCurrency(stats.averageBid, currency)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Best Price</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(stats.lowestBid, currency)}</p>
              </div>
              <TrendingDown className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg. Delivery</p>
                <p className="text-2xl font-bold">{Math.round(stats.averageDelivery)} days</p>
              </div>
              <Truck className="w-8 h-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Submissions Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Vendor Submissions
              </CardTitle>
              <CardDescription>
                Compare and analyze vendor bids
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Select value={sortBy} onValueChange={(value: string) => setSortBy(value as 'price' | 'performance' | 'delivery')}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="price">Sort by Price</SelectItem>
                  <SelectItem value="performance">Sort by Performance</SelectItem>
                  <SelectItem value="delivery">Sort by Delivery</SelectItem>
                </SelectContent>
              </Select>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              >
                <ArrowUpDown className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {submissions.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Submissions Yet</h3>
              <p className="text-muted-foreground">
                Vendors haven't submitted their bids for this RFQ yet.
              </p>
            </div>
          ) : (
            <Tabs defaultValue="overview" className="space-y-4">
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="detailed">Detailed Comparison</TabsTrigger>
                <TabsTrigger value="items">Item-by-Item</TabsTrigger>
              </TabsList>

              <TabsContent value="overview">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Rank</TableHead>
                      <TableHead>Vendor</TableHead>
                      <TableHead className="cursor-pointer" onClick={() => handleSort('performance')}>
                        Performance
                        {sortBy === 'performance' && (
                          <ArrowUpDown className="w-4 h-4 inline ml-1" />
                        )}
                      </TableHead>
                      <TableHead className="cursor-pointer" onClick={() => handleSort('price')}>
                        Total Bid
                        {sortBy === 'price' && (
                          <ArrowUpDown className="w-4 h-4 inline ml-1" />
                        )}
                      </TableHead>
                      <TableHead className="cursor-pointer" onClick={() => handleSort('delivery')}>
                        Delivery
                        {sortBy === 'delivery' && (
                          <ArrowUpDown className="w-4 h-4 inline ml-1" />
                        )}
                      </TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getSortedSubmissions().map((submission, index) => (
                      <TableRow key={submission.id}>
                        <TableCell>
                          {getRankBadge(index)}
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="w-8 h-8">
                              <AvatarFallback className="text-xs">
                                {getInitials(submission.vendor_name)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{submission.vendor_name}</div>
                              <div className="text-sm text-muted-foreground">
                                {submission.vendor_category}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div className="text-sm font-medium">
                              {(submission.vendor_performance_score || 0).toFixed(1)}
                            </div>
                            <Progress 
                              value={submission.vendor_performance_score || 0} 
                              className="w-16 h-2" 
                            />
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div className="font-medium">
                            {formatCurrency(submission.total_amount, submission.currency)}
                          </div>
                          {submission.total_amount === stats.lowestBid && (
                            <Badge variant="secondary" className="text-xs mt-1 bg-green-100 text-green-800">
                              Best Price
                            </Badge>
                          )}
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4 text-muted-foreground" />
                            <span className="text-sm">
                              {submission.delivery_days ? `${submission.delivery_days} days` : 'Not specified'}
                            </span>
                          </div>
                        </TableCell>
                        
                        <TableCell className="text-sm text-muted-foreground">
                          {formatDate(submission.submitted_at)}
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => {
                                setSelectedSubmission(submission);
                                setIsDetailModalOpen(true);
                              }}
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              View
                            </Button>
                            {canEdit && (
                              <Button variant="outline" size="sm">
                                <CheckCircle2 className="w-4 h-4 mr-1" />
                                Select
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent value="detailed">
                <SubmissionComparison
                  submissions={submissions}
                  currency={currency}
                  onViewSubmission={(submission) => {
                    setSelectedSubmission(submission);
                    setIsDetailModalOpen(true);
                  }}
                />
              </TabsContent>

              <TabsContent value="items">
                <ItemByItemAnalysis
                  submissions={submissions}
                  rfqItems={rfqData?.items?.map(item => ({
                    id: item.id,
                    name: item.name,
                    description: item.description,
                    quantity: item.quantity,
                    specifications: item.specifications,
                    unit: item.unit
                  })) || []}
                  currency={currency}
                  onViewSubmission={(submission) => {
                    setSelectedSubmission(submission);
                    setIsDetailModalOpen(true);
                  }}
                />
              </TabsContent>
            </Tabs>
          )}
        </CardContent>
      </Card>

      {/* Submission Detail Modal */}
       {selectedSubmission && (
         <SubmissionDetailModal
           submission={selectedSubmission}
           isOpen={isDetailModalOpen}
           onClose={() => {
             setIsDetailModalOpen(false);
             setSelectedSubmission(null);
           }}
         />
       )}
    </div>
  );
};

export default SubmissionsTab;