import React, { useState, useEffect } from 'react';
import { 
  History, 
  User, 
  Edit, 
  Plus, 
  Trash2, 
  Send, 
  Eye, 
  CheckCircle,
  AlertCircle,
  Clock,
  Filter,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { RFQApiService } from '@/services/api/rfq';
import { AuditEntry } from '@/types/rfq';
import RFQErrorHandler from '@/services/api/rfqErrorHandler';

interface HistoryTabProps {
  rfqId: number;
}

export const HistoryTab: React.FC<HistoryTabProps> = ({ rfqId }) => {
  const [auditHistory, setAuditHistory] = useState<AuditEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterAction, setFilterAction] = useState<string>('all');
  const [searchUser, setSearchUser] = useState('');

  const loadAuditHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await RFQApiService.getAuditHistory(rfqId);
      setAuditHistory(response.data || []);
    } catch (err) {
      setError('Failed to load audit history');
      RFQErrorHandler.handleError(err, 'Loading audit history');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAuditHistory();
  }, [rfqId]);

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'create':
        return <Plus className="w-4 h-4 text-green-500" />;
      case 'update':
        return <Edit className="w-4 h-4 text-blue-500" />;
      case 'delete':
        return <Trash2 className="w-4 h-4 text-red-500" />;
      case 'view':
        return <Eye className="w-4 h-4 text-gray-500" />;
      case 'approve':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'create':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'update':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'delete':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'view':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'approve':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEntityTypeLabel = (entityType: string) => {
    switch (entityType) {
      case 'rfqs':
        return 'RFQ';
      case 'rfq_invitations':
        return 'Invitation';
      case 'rfq_submissions':
        return 'Submission';
      default:
        return entityType.replace('_', ' ').toUpperCase();
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const getInitials = (email: string) => {
    return email.split('@')[0].split('.').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const getFilteredHistory = () => {
    return auditHistory.filter(entry => {
      const matchesAction = filterAction === 'all' || entry.action === filterAction;
      const matchesUser = searchUser === '' || 
        entry.user_email.toLowerCase().includes(searchUser.toLowerCase());
      return matchesAction && matchesUser;
    });
  };

  const formatFieldValue = (value: unknown): string => {
    if (value === null || value === undefined) return 'Not set';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'string') return value;
    if (typeof value === 'number') return value.toString();
    if (Array.isArray(value)) return `${value.length} items`;
    if (typeof value === 'object') {
      // Handle specific object types
      if (value && typeof value === 'object' && ('created_at' in value || 'updated_at' in value)) {
        return 'Object updated';
      }
      return 'Complex data';
    }
    return String(value);
  };

  const getFieldChanges = (oldValue: unknown, newValue: unknown) => {
    const changes: Array<{ field: string; oldVal: unknown; newVal: unknown }> = [];
    
    if (!oldValue && newValue && typeof newValue === 'object' && newValue !== null) {
      // New record created
      const importantFields = ['title', 'description', 'status', 'due_date', 'currency'];
      importantFields.forEach(field => {
        if (field in newValue) {
          changes.push({ field, oldVal: null, newVal: (newValue as Record<string, unknown>)[field] });
        }
      });
    } else if (oldValue && newValue && typeof oldValue === 'object' && typeof newValue === 'object' && oldValue !== null && newValue !== null) {
      // Record updated - compare fields
      const oldRecord = oldValue as Record<string, unknown>;
      const newRecord = newValue as Record<string, unknown>;
      const allFields = new Set([...Object.keys(oldRecord), ...Object.keys(newRecord)]);
      allFields.forEach(field => {
        if (field === 'updated_at' || field === 'created_at') return; // Skip timestamp fields
        
        const oldVal = oldRecord[field];
        const newVal = newRecord[field];
        
        if (JSON.stringify(oldVal) !== JSON.stringify(newVal)) {
          changes.push({ field, oldVal, newVal });
        }
      });
    }
    
    return changes;
  };

  const getFieldDisplayName = (field: string): string => {
    const fieldNames: Record<string, string> = {
      'title': 'Title',
      'description': 'Description',
      'status': 'Status',
      'due_date': 'Due Date',
      'currency': 'Currency',
      'items': 'Items',
      'form_config': 'Form Configuration',
      'ai_settings': 'AI Settings',
      'terms': 'Terms & Conditions',
      'allow_partial_selection': 'Allow Partial Selection',
      'partial_selection_config': 'Partial Selection Settings'
    };
    return fieldNames[field] || field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const renderChangeDetails = (entry: AuditEntry) => {
    if (!entry.old_value && !entry.new_value) return null;

    const changes = getFieldChanges(entry.old_value, entry.new_value);
    
    if (changes.length === 0) return null;

    return (
      <div className="mt-2 p-3 bg-muted/50 rounded-lg text-sm">
        <div className="font-medium mb-2">Changes:</div>
        <div className="space-y-2">
          {changes.map((change, index) => (
            <div key={index} className="border-l-2 border-muted pl-3">
              <div className="font-medium text-foreground">
                {getFieldDisplayName(change.field)}
              </div>
              {change.oldVal !== null && (
                <div className="text-red-600">
                  <span className="font-medium">From:</span> {formatFieldValue(change.oldVal)}
                </div>
              )}
              <div className="text-green-600">
                <span className="font-medium">{change.oldVal !== null ? 'To:' : 'Set to:'}</span> {formatFieldValue(change.newVal)}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-start gap-4">
                <div className="w-10 h-10 bg-muted rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <AlertCircle className="w-12 h-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Failed to Load History</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={loadAuditHistory}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  const filteredHistory = getFilteredHistory();

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="w-5 h-5" />
            Activity History
          </CardTitle>
          <CardDescription>
            Complete audit trail of all RFQ-related activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search by user email..."
                value={searchUser}
                onChange={(e) => setSearchUser(e.target.value)}
                className="max-w-sm"
              />
            </div>
            
            <Select value={filterAction} onValueChange={setFilterAction}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Actions</SelectItem>
                <SelectItem value="create">Created</SelectItem>
                <SelectItem value="update">Updated</SelectItem>
                <SelectItem value="delete">Deleted</SelectItem>
                <SelectItem value="view">Viewed</SelectItem>
                <SelectItem value="approve">Approved</SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={loadAuditHistory}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {filteredHistory.length === 0 ? (
            <div className="text-center py-12">
              <History className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Activity Found</h3>
              <p className="text-muted-foreground">
                {auditHistory.length === 0 
                  ? 'No activity has been recorded for this RFQ yet.'
                  : 'No activity matches your current filters.'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredHistory.map((entry, index) => (
                <div key={entry.id} className="relative">
                  {/* Timeline line */}
                  {index < filteredHistory.length - 1 && (
                    <div className="absolute left-5 top-12 w-px h-16 bg-border"></div>
                  )}
                  
                  <div className="flex items-start gap-4">
                    {/* Avatar */}
                    <Avatar className="w-10 h-10 border-2 border-background">
                      <AvatarFallback className="text-xs">
                        {getInitials(entry.user_email)}
                      </AvatarFallback>
                    </Avatar>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge className={getActionColor(entry.action)}>
                          {getActionIcon(entry.action)}
                          <span className="ml-1 capitalize">{entry.action}</span>
                        </Badge>
                        
                        <Badge variant="outline">
                          {getEntityTypeLabel(entry.entity_type)}
                        </Badge>
                        
                        <span className="text-sm text-muted-foreground">
                          {formatTimestamp(entry.timestamp)}
                        </span>
                      </div>
                      
                      <div className="mb-2">
                        <span className="font-medium">{entry.user_email}</span>
                        <span className="text-muted-foreground ml-1">
                          {entry.action}d {getEntityTypeLabel(entry.entity_type).toLowerCase()}
                          {entry.entity_id && ` #${entry.entity_id}`}
                        </span>
                      </div>
                      
                      {entry.details && (
                        <div className="text-sm text-muted-foreground mb-2">
                          {typeof entry.details === 'string' 
                            ? entry.details 
                            : JSON.stringify(entry.details)
                          }
                        </div>
                      )}
                      
                      {renderChangeDetails(entry)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary Statistics */}
      {auditHistory.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {auditHistory.filter(e => e.action === 'create').length}
              </div>
              <div className="text-sm text-muted-foreground">Created</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {auditHistory.filter(e => e.action === 'update').length}
              </div>
              <div className="text-sm text-muted-foreground">Updated</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-gray-600">
                {auditHistory.filter(e => e.action === 'view').length}
              </div>
              <div className="text-sm text-muted-foreground">Viewed</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {auditHistory.filter(e => e.action === 'approve').length}
              </div>
              <div className="text-sm text-muted-foreground">Approved</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">
                {new Set(auditHistory.map(e => e.user_email)).size}
              </div>
              <div className="text-sm text-muted-foreground">Users</div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default HistoryTab;