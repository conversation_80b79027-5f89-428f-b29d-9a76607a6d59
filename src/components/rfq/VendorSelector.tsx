import React, { useState, useMemo } from "react";
import { motion } from "framer-motion";
import {
  Search,
  Filter,
  Users,
  Star,
  Building2,
  Mail,
  Phone,
  MapPin,
  Award,
  CheckCircle2,
  Circle,
  Sparkles,
  TrendingUp,
  Shield,
  Clock,
  DollarSign,
  Target,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Vendor } from "@/store/slices/vendorsSlice";

export interface VendorFilterOptions {
  search?: string;
  category?: string;
  status?: "active" | "inactive" | "blacklisted";
  performance_min?: number;
  performance_max?: number;
}

interface VendorSelectorProps {
  vendors: Vendor[];
  selectedVendors: number[];
  onSelectionChange: (vendorIds: number[]) => void;
  filters: VendorFilterOptions;
  showPerformanceScores: boolean;
}

const VENDOR_CATEGORIES = [
  "All Categories",
  "Technology",
  "Manufacturing",
  "Logistics",
  "Services",
  "Healthcare",
  "Finance",
  "Construction",
  "Retail",
  "Education",
  "Other",
];

const PERFORMANCE_RANGES = [
  { label: "All Performance Levels", min: 0, max: 100 },
  { label: "Excellent (90-100)", min: 90, max: 100 },
  { label: "Good (80-89)", min: 80, max: 89 },
  { label: "Average (70-79)", min: 70, max: 79 },
  { label: "Below Average (60-69)", min: 60, max: 69 },
  { label: "Poor (0-59)", min: 0, max: 59 },
];

export const VendorSelector: React.FC<VendorSelectorProps> = ({
  vendors,
  selectedVendors,
  onSelectionChange,
  filters: initialFilters,
  showPerformanceScores,
}) => {
  const [searchTerm, setSearchTerm] = useState(initialFilters.search || "");
  const [categoryFilter, setCategoryFilter] = useState(
    initialFilters.category || "All Categories"
  );
  const [performanceFilter, setPerformanceFilter] = useState(
    "All Performance Levels"
  );
  const [statusFilter, setStatusFilter] = useState(
    initialFilters.status || "active"
  );
  const [showRecommendations, setShowRecommendations] = useState(true);
  const [sortBy, setSortBy] = useState<
    "name" | "performance" | "recent" | "recommended"
  >("recommended");

  // Generate AI recommendations
  const getVendorRecommendationScore = (vendor: Vendor) => {
    let score = 0;

    // Performance score weight (40%)
    score += (vendor.performance_score || 0) * 0.4;

    // Recent activity weight (20%)
    const daysSinceUpdate = Math.floor(
      (Date.now() - new Date(vendor.updated_at).getTime()) /
        (1000 * 60 * 60 * 24)
    );
    const recentActivityScore = Math.max(0, 100 - daysSinceUpdate);
    score += recentActivityScore * 0.2;

    // Certification weight (20%)
    const certificationScore = Math.min(
      100,
      (vendor.certifications?.length || 0) * 25
    );
    score += certificationScore * 0.2;

    // Category relevance weight (20%)
    const categoryRelevanceScore = 100; // Assume all vendors are relevant for now
    score += categoryRelevanceScore * 0.2;

    return Math.min(100, score);
  };

  const recommendedVendors = useMemo(() => {
    return vendors
      .map((vendor) => ({
        ...vendor,
        recommendationScore: getVendorRecommendationScore(vendor),
      }))
      .sort((a, b) => b.recommendationScore - a.recommendationScore)
      .slice(0, 5);
  }, [vendors]);

  // Filter and sort vendors based on current filters
  const filteredVendors = useMemo(() => {
    let filtered = vendors.filter((vendor) => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          vendor.name.toLowerCase().includes(searchLower) ||
          vendor.contact_email.toLowerCase().includes(searchLower) ||
          vendor.category.toLowerCase().includes(searchLower) ||
          vendor.address.city.toLowerCase().includes(searchLower);

        if (!matchesSearch) return false;
      }

      // Category filter
      if (
        categoryFilter !== "All Categories" &&
        vendor.category !== categoryFilter
      ) {
        return false;
      }

      // Status filter
      if (vendor.status !== statusFilter) {
        return false;
      }

      // Performance filter
      if (performanceFilter !== "All Performance Levels") {
        const range = PERFORMANCE_RANGES.find(
          (r) => r.label === performanceFilter
        );
        if (range) {
          const score = vendor.performance_score || 0;
          if (score < range.min || score > range.max) {
            return false;
          }
        }
      }

      return true;
    });

    // Sort vendors
    switch (sortBy) {
      case "name":
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case "performance":
        filtered.sort(
          (a, b) => (b.performance_score || 0) - (a.performance_score || 0)
        );
        break;
      case "recent":
        filtered.sort(
          (a, b) =>
            new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
        );
        break;
      case "recommended":
        filtered.sort(
          (a, b) =>
            getVendorRecommendationScore(b) - getVendorRecommendationScore(a)
        );
        break;
    }

    return filtered;
  }, [
    vendors,
    searchTerm,
    categoryFilter,
    performanceFilter,
    statusFilter,
    sortBy,
  ]);

  const handleVendorToggle = (vendorId: number) => {
    const isSelected = selectedVendors.includes(vendorId);
    if (isSelected) {
      onSelectionChange(selectedVendors.filter((id) => id !== vendorId));
    } else {
      onSelectionChange([...selectedVendors, vendorId]);
    }
  };

  const handleSelectAll = () => {
    const allFilteredIds = filteredVendors.map((v) => v.id);
    const allSelected = allFilteredIds.every((id) =>
      selectedVendors.includes(id)
    );

    if (allSelected) {
      // Deselect all filtered vendors
      onSelectionChange(
        selectedVendors.filter((id) => !allFilteredIds.includes(id))
      );
    } else {
      // Select all filtered vendors
      const newSelection = [
        ...new Set([...selectedVendors, ...allFilteredIds]),
      ];
      onSelectionChange(newSelection);
    }
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 80) return "text-blue-600";
    if (score >= 70) return "text-yellow-600";
    if (score >= 60) return "text-orange-600";
    return "text-red-600";
  };

  const getPerformanceBadgeVariant = (
    score: number
  ): "default" | "secondary" | "destructive" | "outline" => {
    if (score >= 90) return "default";
    if (score >= 80) return "secondary";
    if (score >= 70) return "outline";
    return "destructive";
  };

  const VendorCard: React.FC<{ vendor: Vendor; isSelected: boolean }> = ({
    vendor,
    isSelected,
  }) => {
    const recommendationScore = getVendorRecommendationScore(vendor);
    const isRecommended = recommendedVendors.some((rv) => rv.id === vendor.id);

    return (
      <Card
        className={`card-neumorphic cursor-pointer transition-all duration-200 ${
          isSelected ? "ring-2 ring-primary bg-primary/5" : "hover:shadow-lg"
        } ${isRecommended ? "border-primary/30" : ""}`}
        onClick={() => handleVendorToggle(vendor.id)}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <div className="mt-1">
                {isSelected ? (
                  <CheckCircle2 className="w-5 h-5 text-primary" />
                ) : (
                  <Circle className="w-5 h-5 text-muted-foreground" />
                )}
              </div>
              <div className="flex-1">
                <CardTitle className="flex items-center space-x-2 text-base">
                  <Building2 className="w-4 h-4 text-primary" />
                  <span>{vendor.name}</span>
                  {isRecommended && (
                    <Sparkles className="w-4 h-4 text-primary" />
                  )}
                </CardTitle>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {vendor.category}
                  </Badge>
                  <Badge
                    variant={
                      vendor.status === "active" ? "default" : "secondary"
                    }
                    className="text-xs"
                  >
                    {vendor.status}
                  </Badge>
                  {sortBy === "recommended" && (
                    <Badge variant="secondary" className="text-xs">
                      <Target className="w-3 h-3 mr-1" />
                      {recommendationScore.toFixed(0)}%
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className="text-right space-y-1">
              {showPerformanceScores && vendor.performance_score && (
                <Badge
                  variant={getPerformanceBadgeVariant(vendor.performance_score)}
                  className="text-xs block"
                >
                  <Star className="w-3 h-3 mr-1" />
                  {vendor.performance_score.toFixed(1)}
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2 text-sm text-muted-foreground">
            <div className="flex items-center space-x-2">
              <Mail className="w-4 h-4" />
              <span className="truncate">{vendor.contact_email}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="w-4 h-4" />
              <span>{vendor.contact_phone}</span>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4" />
              <span>
                {vendor.address.city}, {vendor.address.state}
              </span>
            </div>
            {vendor.certifications && vendor.certifications.length > 0 && (
              <div className="flex items-center space-x-2">
                <Award className="w-4 h-4" />
                <div className="flex flex-wrap gap-1">
                  {vendor.certifications.slice(0, 2).map((cert, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {cert}
                    </Badge>
                  ))}
                  {vendor.certifications.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{vendor.certifications.length - 2} more
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Additional vendor insights */}
            <div className="flex items-center justify-between pt-2 border-t border-border/50">
              <div className="flex items-center space-x-3 text-xs">
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>
                    {Math.floor(
                      (Date.now() - new Date(vendor.updated_at).getTime()) /
                        (1000 * 60 * 60 * 24)
                    )}
                    d ago
                  </span>
                </div>
                {vendor.certifications && vendor.certifications.length > 0 && (
                  <div className="flex items-center space-x-1">
                    <Shield className="w-3 h-3" />
                    <span>{vendor.certifications.length} certs</span>
                  </div>
                )}
              </div>
              {isRecommended && (
                <Badge
                  variant="outline"
                  className="text-xs bg-primary/10 text-primary border-primary/30"
                >
                  Recommended
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-foreground">
            Select Vendors
          </h3>
          <p className="text-sm text-muted-foreground">
            {selectedVendors.length} of {filteredVendors.length} vendors
            selected
          </p>
        </div>
        <Button
          type="button"
          variant="outline"
          onClick={handleSelectAll}
          className="btn-neumorphic"
        >
          {filteredVendors.every((v) => selectedVendors.includes(v.id))
            ? "Deselect All"
            : "Select All"}
        </Button>
      </div>

      {/* AI Recommendations */}
      {showRecommendations && (
        <Card className="card-neumorphic bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2 text-base">
                <Sparkles className="w-4 h-4 text-primary" />
                <span>AI Recommendations</span>
              </CardTitle>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowRecommendations(false)}
                className="text-muted-foreground hover:text-foreground"
              >
                ×
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Based on performance history, certifications, and recent activity
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {recommendedVendors.slice(0, 3).map((vendor) => (
                <div
                  key={vendor.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-all ${
                    selectedVendors.includes(vendor.id)
                      ? "bg-primary/10 border-primary"
                      : "bg-background/50 border-border hover:border-primary/50"
                  }`}
                  onClick={() => handleVendorToggle(vendor.id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <Building2 className="w-4 h-4 text-primary" />
                      <span className="font-medium text-sm">{vendor.name}</span>
                    </div>
                    {selectedVendors.includes(vendor.id) ? (
                      <CheckCircle2 className="w-4 h-4 text-primary" />
                    ) : (
                      <Circle className="w-4 h-4 text-muted-foreground" />
                    )}
                  </div>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>{vendor.category}</span>
                    <div className="flex items-center space-x-1">
                      <Target className="w-3 h-3" />
                      <span>
                        {getVendorRecommendationScore(vendor).toFixed(0)}% match
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            {recommendedVendors.length > 3 && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="mt-3 w-full"
                onClick={() => {
                  const topRecommended = recommendedVendors
                    .slice(0, 5)
                    .map((v) => v.id);
                  const newSelection = [
                    ...new Set([...selectedVendors, ...topRecommended]),
                  ];
                  onSelectionChange(newSelection);
                }}
              >
                Select Top 5 Recommended
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card className="card-neumorphic">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2 text-base">
            <Filter className="w-4 h-4 text-primary" />
            <span>Filters & Sorting</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <label className="text-sm font-medium text-foreground mb-2 block">
                Search
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search vendors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input-neumorphic pl-10"
                />
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-foreground mb-2 block">
                Category
              </label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="input-neumorphic">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {VENDOR_CATEGORIES.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {showPerformanceScores && (
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Performance
                </label>
                <Select
                  value={performanceFilter}
                  onValueChange={setPerformanceFilter}
                >
                  <SelectTrigger className="input-neumorphic">
                    <SelectValue placeholder="Select performance" />
                  </SelectTrigger>
                  <SelectContent>
                    {PERFORMANCE_RANGES.map((range) => (
                      <SelectItem key={range.label} value={range.label}>
                        {range.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div>
              <label className="text-sm font-medium text-foreground mb-2 block">
                Status
              </label>
              <Select
                value={statusFilter}
                onValueChange={(value: any) => setStatusFilter(value)}
              >
                <SelectTrigger className="input-neumorphic">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="blacklisted">Blacklisted</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-foreground mb-2 block">
                Sort By
              </label>
              <Select
                value={sortBy}
                onValueChange={(value: any) => setSortBy(value)}
              >
                <SelectTrigger className="input-neumorphic">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recommended">
                    <div className="flex items-center space-x-2">
                      <Sparkles className="w-4 h-4" />
                      <span>Recommended</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="performance">
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="w-4 h-4" />
                      <span>Performance</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="recent">
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4" />
                      <span>Recently Active</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="name">
                    <div className="flex items-center space-x-2">
                      <Building2 className="w-4 h-4" />
                      <span>Name</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vendor List */}
      {filteredVendors.length === 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <Users className="w-16 h-16 text-muted-foreground mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            No vendors found
          </h3>
          <p className="text-muted-foreground">
            Try adjusting your filters to see more vendors
          </p>
        </motion.div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredVendors.map((vendor) => (
            <motion.div
              key={vendor.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              layout
            >
              <VendorCard
                vendor={vendor}
                isSelected={selectedVendors.includes(vendor.id)}
              />
            </motion.div>
          ))}
        </div>
      )}

      {/* Selection Summary */}
      {selectedVendors.length > 0 && (
        <Card className="card-neumorphic bg-primary/5 border-primary/20">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-3">
              <CheckCircle2 className="w-5 h-5 text-primary" />
              <div>
                <p className="font-medium text-foreground">
                  {selectedVendors.length} vendor
                  {selectedVendors.length !== 1 ? "s" : ""} selected
                </p>
                <p className="text-sm text-muted-foreground">
                  RFQ invitations will be sent to the selected vendors
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
