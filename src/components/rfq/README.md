# RFQ Public Submission Interface

This module provides a complete public vendor submission interface for RFQ (Request for Quote) management. Vendors can access a secure, token-based submission portal without requiring system authentication.

## Components

### PublicSubmissionForm

A comprehensive form component that allows vendors to submit bids for RFQ items.

**Features:**
- Responsive design optimized for mobile and desktop
- Dynamic form field rendering based on RFQ configuration
- File upload with progress tracking
- Real-time total calculation
- Form validation with detailed error messages
- Support for existing submission modification
- Deadline tracking with visual indicators

**Props:**
```typescript
interface PublicSubmissionFormProps {
  rfq: PublicRFQData;           // RFQ details and configuration
  token: string;                // Secure submission token
  onSubmit: (data: BidSubmissionData) => void;
  isLoading: boolean;           // Loading state for submission
  existingSubmission?: any;     // Optional existing submission data
}
```

### SubmissionStatusTracker

A status tracking component that provides vendors with real-time submission status and management capabilities.

**Features:**
- Real-time submission status updates
- Submission history and version tracking
- File attachment management with download capability
- Submission modification and deletion (within deadline)
- Contact information for support
- Timeline visualization

**Props:**
```typescript
interface SubmissionStatusTrackerProps {
  token: string;                // Secure submission token
  onEditSubmission?: () => void; // Callback for edit action
  showEditButton?: boolean;     // Whether to show edit controls
}
```

## Pages

### PublicRFQSubmission

The main page component that orchestrates the submission flow.

**Features:**
- Token validation and error handling
- Automatic routing between form and status views
- API integration for submission management
- Success/error state management
- Responsive layout with loading states

**Route:** `/rfq/submit/:token`

## API Integration

The components integrate with the backend through the `publicSubmissionAPI` service:

```typescript
// Validate submission token
await publicSubmissionAPI.validateToken(token);

// Get RFQ data for submission
await publicSubmissionAPI.getRFQData(token);

// Submit new bid
await publicSubmissionAPI.createSubmission(formData);

// Update existing submission
await publicSubmissionAPI.updateSubmission(id, formData);

// Get submission status
await publicSubmissionAPI.getSubmissionStatus(token);
```

## Security Features

- **Token-based Access**: Secure, time-limited tokens for vendor access
- **File Upload Validation**: Type and size restrictions for attachments
- **Input Sanitization**: All form inputs are validated and sanitized
- **Rate Limiting**: Protection against spam submissions
- **Deadline Enforcement**: Automatic prevention of late submissions

## User Experience

### Submission Flow
1. Vendor receives email invitation with secure link
2. Token validation and RFQ data loading
3. Form presentation with item-by-item bidding
4. File attachment upload with progress tracking
5. Form validation and submission
6. Success confirmation with status tracking access

### Status Tracking
1. Real-time status updates
2. Submission modification (within deadline)
3. File download capabilities
4. Contact information for support
5. Submission history and timeline

## Responsive Design

The interface is fully responsive with:
- Mobile-first design approach
- Touch-friendly controls
- Optimized layouts for all screen sizes
- Progressive enhancement for desktop features

## Accessibility

- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Focus management and indicators

## File Upload

Supports multiple file types:
- PDF documents
- Microsoft Office files (Word, Excel)
- Images (JPEG, PNG, GIF)
- Text files (TXT, CSV)

**Limits:**
- Maximum 10 files per submission
- 10MB per file limit
- Total upload size monitoring

## Error Handling

Comprehensive error handling for:
- Invalid or expired tokens
- Network connectivity issues
- File upload failures
- Form validation errors
- Server-side errors

## Testing

Unit tests are provided for all components:
- Form rendering and validation
- Status tracking functionality
- API integration
- Error state handling
- User interaction flows

Run tests with:
```bash
npm test -- --testPathPattern=PublicSubmissionForm
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance

- Lazy loading for large file uploads
- Optimized bundle size with code splitting
- Efficient re-rendering with React hooks
- Cached API responses where appropriate
- Progressive image loading for attachments