import React from 'react';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RFQSubmission } from '@/types/rfq';
import { formatCurrency } from '@/types/rfq';

interface BidAnalysisChartsProps {
  submissions: RFQSubmission[];
  currency: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export const BidAnalysisCharts: React.FC<BidAnalysisChartsProps> = ({
  submissions,
  currency
}) => {
  // Prepare data for price comparison chart
  const priceData = submissions.map((submission, index) => ({
    name: submission.vendor_name.length > 10 
      ? submission.vendor_name.substring(0, 10) + '...' 
      : submission.vendor_name,
    fullName: submission.vendor_name,
    amount: submission.total_amount,
    performance: submission.vendor_performance_score,
    delivery: submission.delivery_days || 0,
    color: COLORS[index % COLORS.length]
  })).sort((a, b) => a.amount - b.amount);

  // Prepare data for performance vs price scatter
  const scatterData = submissions.map((submission, index) => ({
    name: submission.vendor_name,
    performance: submission.vendor_performance_score,
    price: submission.total_amount,
    delivery: submission.delivery_days || 0,
    color: COLORS[index % COLORS.length]
  }));

  // Prepare data for delivery time distribution
  const deliveryData = submissions
    .filter(s => s.delivery_days)
    .map((submission, index) => ({
      name: submission.vendor_name.length > 10 
        ? submission.vendor_name.substring(0, 10) + '...' 
        : submission.vendor_name,
      days: submission.delivery_days,
      color: COLORS[index % COLORS.length]
    }))
    .sort((a, b) => (a.days || 0) - (b.days || 0));

  // Prepare radar chart data for multi-criteria comparison
  const radarData = submissions.slice(0, 5).map(submission => {
    const priceScore = 100 - ((submission.total_amount - Math.min(...submissions.map(s => s.total_amount))) / 
      (Math.max(...submissions.map(s => s.total_amount)) - Math.min(...submissions.map(s => s.total_amount)))) * 100;
    
    return {
      vendor: submission.vendor_name.length > 15 
        ? submission.vendor_name.substring(0, 15) + '...' 
        : submission.vendor_name,
      price: Math.max(0, priceScore),
      performance: submission.vendor_performance_score,
      delivery: submission.delivery_days ? Math.max(0, 100 - submission.delivery_days) : 50,
      overall: (priceScore * 0.4) + (submission.vendor_performance_score * 0.3) + 
               ((submission.delivery_days ? Math.max(0, 100 - submission.delivery_days) : 50) * 0.3)
    };
  });

  // Custom tooltip for price chart
  const PriceTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.fullName}</p>
          <p className="text-sm text-muted-foreground">
            Amount: {formatCurrency(data.amount, currency)}
          </p>
          <p className="text-sm text-muted-foreground">
            Performance: {data.performance.toFixed(1)}
          </p>
          {data.delivery > 0 && (
            <p className="text-sm text-muted-foreground">
              Delivery: {data.delivery} days
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  // Custom tooltip for radar chart
  const RadarTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey}: {entry.value.toFixed(1)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (submissions.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <BarChart className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">
            No data available for analysis
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Price Comparison Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Price Comparison</CardTitle>
          <CardDescription>
            Total bid amounts by vendor (sorted by price)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={priceData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="name" 
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis 
                tickFormatter={(value) => formatCurrency(value, currency)}
                fontSize={12}
              />
              <Tooltip content={<PriceTooltip />} />
              <Bar dataKey="amount" fill="#8884d8">
                {priceData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Performance vs Price Scatter */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Performance vs Price</CardTitle>
          <CardDescription>
            Vendor performance score vs bid amount
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={scatterData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="performance" 
                domain={[0, 100]}
                label={{ value: 'Performance Score', position: 'insideBottom', offset: -5 }}
                fontSize={12}
              />
              <YAxis 
                dataKey="price"
                tickFormatter={(value) => formatCurrency(value, currency)}
                label={{ value: 'Bid Amount', angle: -90, position: 'insideLeft' }}
                fontSize={12}
              />
              <Tooltip 
                formatter={(value, name) => [
                  name === 'price' ? formatCurrency(value as number, currency) : value,
                  name === 'price' ? 'Bid Amount' : 'Performance'
                ]}
              />
              <Line 
                type="monotone" 
                dataKey="price" 
                stroke="#8884d8" 
                strokeWidth={2}
                dot={{ fill: '#8884d8', strokeWidth: 2, r: 6 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Delivery Time Comparison */}
      {deliveryData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Delivery Times</CardTitle>
            <CardDescription>
              Promised delivery times by vendor
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={deliveryData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="name" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis 
                  label={{ value: 'Days', angle: -90, position: 'insideLeft' }}
                  fontSize={12}
                />
                <Tooltip 
                  formatter={(value) => [`${value} days`, 'Delivery Time']}
                />
                <Bar dataKey="days" fill="#82ca9d">
                  {deliveryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      )}

      {/* Multi-Criteria Radar Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Multi-Criteria Analysis</CardTitle>
          <CardDescription>
            Comprehensive comparison across key factors
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <RadarChart data={radarData} margin={{ top: 20, right: 80, bottom: 20, left: 80 }}>
              <PolarGrid />
              <PolarAngleAxis dataKey="vendor" fontSize={10} />
              <PolarRadiusAxis 
                angle={90} 
                domain={[0, 100]} 
                fontSize={10}
                tickCount={5}
              />
              <Tooltip content={<RadarTooltip />} />
              <Radar
                name="Price Score"
                dataKey="price"
                stroke="#8884d8"
                fill="#8884d8"
                fillOpacity={0.1}
                strokeWidth={2}
              />
              <Radar
                name="Performance"
                dataKey="performance"
                stroke="#82ca9d"
                fill="#82ca9d"
                fillOpacity={0.1}
                strokeWidth={2}
              />
              <Radar
                name="Delivery Score"
                dataKey="delivery"
                stroke="#ffc658"
                fill="#ffc658"
                fillOpacity={0.1}
                strokeWidth={2}
              />
            </RadarChart>
          </ResponsiveContainer>
          
          {/* Legend */}
          <div className="flex justify-center gap-4 mt-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-[#8884d8] rounded"></div>
              <span className="text-sm">Price Score</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-[#82ca9d] rounded"></div>
              <span className="text-sm">Performance</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-[#ffc658] rounded"></div>
              <span className="text-sm">Delivery Score</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Statistics */}
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="text-lg">Bid Analysis Summary</CardTitle>
          <CardDescription>
            Key insights from the submitted bids
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(Math.min(...submissions.map(s => s.total_amount)), currency)}
              </div>
              <div className="text-sm text-muted-foreground">Lowest Bid</div>
              <Badge variant="outline" className="mt-1 text-xs">
                {submissions.find(s => s.total_amount === Math.min(...submissions.map(sub => sub.total_amount)))?.vendor_name}
              </Badge>
            </div>
            
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {(submissions.reduce((sum, s) => sum + s.vendor_performance_score, 0) / submissions.length).toFixed(1)}
              </div>
              <div className="text-sm text-muted-foreground">Avg Performance</div>
              <Badge variant="outline" className="mt-1 text-xs">
                Score out of 100
              </Badge>
            </div>
            
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {deliveryData.length > 0 
                  ? Math.round(deliveryData.reduce((sum, d) => sum + (d.days || 0), 0) / deliveryData.length)
                  : 'N/A'
                }
              </div>
              <div className="text-sm text-muted-foreground">Avg Delivery</div>
              <Badge variant="outline" className="mt-1 text-xs">
                {deliveryData.length > 0 ? 'Days' : 'Not specified'}
              </Badge>
            </div>
            
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {(((Math.max(...submissions.map(s => s.total_amount)) - Math.min(...submissions.map(s => s.total_amount))) / 
                   Math.min(...submissions.map(s => s.total_amount))) * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">Price Spread</div>
              <Badge variant="outline" className="mt-1 text-xs">
                Variation
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BidAnalysisCharts;