import React, { useState, useEffect, useCallback } from 'react';
import { useForm, Resolver } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { motion } from 'framer-motion';
import { 
  FileText, 
  Users, 
  Settings, 
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Calendar,
  Loader2,
  Save,
  Upload,
  AlertCircle,
  Clock,
  Sparkles,
  Send
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ItemListBuilder } from './ItemListBuilder';
import { VendorSelector } from './VendorSelector';
import { FormConfigBuilder } from './FormConfigBuilder';
import { rfqValidationSchema } from '@/utils/validation';
import { Vendor } from '@/store/slices/vendorsSlice';
import { Switch } from '@/components/ui/switch';

export interface RFQItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  specifications: Record<string, unknown>;
  estimatedPrice?: number;
  category: string;
}

export interface FormFieldConfig {
  id: string;
  type: 'text' | 'number' | 'date' | 'file' | 'select' | 'textarea';
  label: string;
  required: boolean;
  options?: string[];
  validation?: unknown[];
  itemSpecific: boolean;
}

export interface PartialSelectionConfig {
  enabled: boolean;
  requireVendorConfirmation: boolean;
  confirmationMessage: string;
  instructions: string;
  defaultAllowed: boolean;
}

export interface AIRecommendationSettings {
  priceWeight: number;
  performanceWeight: number;
  deliveryWeight: number;
  riskWeight: number;
  diversificationPreference: number;
}

export interface RFQDraft {
  id: string;
  title: string;
  description: string;
  items: RFQItem[];
  dueDate: Date;
  selectedVendors: number[];
  formConfig: FormFieldConfig[];
  terms: string;
  aiSettings: AIRecommendationSettings;
  lastSaved: Date;
  currentStep: number;
}

export interface RFQCreateData {
  title: string;
  description: string;
  items: RFQItem[];
  dueDate: Date;
  selectedVendors: number[];
  formConfig: FormFieldConfig[];
  terms: string;
  aiSettings: AIRecommendationSettings;
  allowPartialSelection?: boolean;
  partialSelectionConfig?: PartialSelectionConfig;
}

interface RFQCreationFormProps {
  onSubmit: (data: RFQCreateData) => void;
  vendors: Vendor[];
  templates?: unknown[];
  isLoading: boolean;
  initialDraft?: RFQDraft;
  onSaveDraft?: (draft: RFQDraft) => void;
}

const STEPS = [
  { id: 1, title: 'Basic Info', description: 'RFQ details and requirements', icon: FileText },
  { id: 2, title: 'Items', description: 'Add items and specifications', icon: Upload },
  { id: 3, title: 'Vendors', description: 'Select and filter vendors', icon: Users },
  { id: 4, title: 'Form Config', description: 'Configure submission form', icon: Settings },
  { id: 5, title: 'Partial Selection', description: 'Configure partial selection options', icon: CheckCircle },
  { id: 6, title: 'AI Settings', description: 'Configure recommendations', icon: Sparkles },
  { id: 7, title: 'Review', description: 'Review and send', icon: CheckCircle },
];

export const RFQCreationForm: React.FC<RFQCreationFormProps> = ({
  onSubmit,
  vendors,
  templates = [],
  isLoading = false,
  initialDraft,
  onSaveDraft,
}) => {
  const [currentStep, setCurrentStep] = useState(initialDraft?.currentStep || 1);
  const [isDraftSaving, setIsDraftSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(initialDraft?.lastSaved || null);
  const [draftId] = useState(initialDraft?.id || `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  
  const form = useForm<RFQCreateData>({
    resolver: yupResolver(rfqValidationSchema) as Resolver<RFQCreateData>,
    defaultValues: initialDraft ? {
      title: initialDraft.title,
      description: initialDraft.description,
      items: initialDraft.items,
      dueDate: initialDraft.dueDate,
      selectedVendors: initialDraft.selectedVendors,
      formConfig: initialDraft.formConfig,
      terms: initialDraft.terms,
      aiSettings: initialDraft.aiSettings,
      allowPartialSelection: false,
      partialSelectionConfig: {
        enabled: false,
        requireVendorConfirmation: true,
        confirmationMessage: 'Do you allow individual item purchases at the quoted rates?',
        instructions: 'Please confirm if you allow partial selection of items from your submission.',
        defaultAllowed: false
      },
    } : {
      title: '',
      description: '',
      items: [],
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      selectedVendors: [],
      formConfig: [
        {
          id: 'unit_price',
          type: 'number',
          label: 'Unit Price',
          required: true,
          itemSpecific: true,
        },
        {
          id: 'delivery_days',
          type: 'number',
          label: 'Delivery Time (Days)',
          required: true,
          itemSpecific: true,
        },
        {
          id: 'notes',
          type: 'textarea',
          label: 'Additional Notes',
          required: false,
          itemSpecific: true,
        },
      ],
      terms: '',
      aiSettings: {
        priceWeight: 0.4,
        performanceWeight: 0.3,
        deliveryWeight: 0.2,
        riskWeight: 0.1,
        diversificationPreference: 0.5,
      },
      allowPartialSelection: false,
      partialSelectionConfig: {
        enabled: false,
        requireVendorConfirmation: true,
        confirmationMessage: 'Do you allow individual item purchases at the quoted rates?',
        instructions: 'Please confirm if you allow partial selection of items from your submission.',
        defaultAllowed: false
      },
    },
  });

  const { watch, setValue, trigger, getValues } = form;
  const watchedItems = watch('items');
  const watchedVendors = watch('selectedVendors');

  // Auto-save draft functionality
  const saveDraft = useCallback(async () => {
    if (!onSaveDraft) return;
    
    setIsDraftSaving(true);
    try {
      const currentData = getValues();
      const draft: RFQDraft = {
        id: draftId,
        ...currentData,
        lastSaved: new Date(),
        currentStep,
      };
      
      await onSaveDraft(draft);
      setLastSaved(new Date());
    } catch (error) {
      console.error('Failed to save draft:', error);
    } finally {
      setIsDraftSaving(false);
    }
  }, [onSaveDraft, getValues, draftId, currentStep]);

  // Removed auto-save interval - only save manually or on step change

  // Auto-save removed - only save on manual action or step change

  const nextStep = async () => {
    let fieldsToValidate: (keyof RFQCreateData)[] = [];
    
    switch (currentStep) {
      case 1:
        fieldsToValidate = ['title', 'description', 'dueDate', 'terms'];
        break;
      case 2:
        fieldsToValidate = ['items'];
        break;
      case 3:
        fieldsToValidate = ['selectedVendors'];
        break;
      case 4:
        fieldsToValidate = ['formConfig'];
        break;
      case 5:
        // Partial selection step - no required validation
        fieldsToValidate = [];
        break;
      case 6:
        fieldsToValidate = ['aiSettings'];
        break;
    }

    const isValid = await trigger(fieldsToValidate);
    if (isValid && currentStep < 7) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      // Save draft when moving to next step
      if (onSaveDraft) {
        saveDraft();
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      // Save draft when moving to previous step
      if (onSaveDraft) {
        saveDraft();
      }
    }
  };

  const handleSubmit = async (data: RFQCreateData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('RFQ creation error:', error);
    }
  };

  const handleDirectSubmit = () => {
    const data = getValues();
    handleSubmit(data);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div
            key="step1"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            {/* Basic Information */}
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5 text-primary" />
                  <span>RFQ Details</span>
                </CardTitle>
                <CardDescription>
                  Provide basic information about your request for quote
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>RFQ Title *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Office Supplies Q1 2024"
                          className="input-neumorphic"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe the purpose and requirements of this RFQ..."
                          className="input-neumorphic min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Provide detailed information about what you're looking for
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dueDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Submission Deadline *</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          className="input-neumorphic"
                          value={field.value ? new Date(field.value.getTime() - field.value.getTimezoneOffset() * 60000).toISOString().slice(0, 16) : ''}
                          onChange={(e) => field.onChange(new Date(e.target.value))}
                        />
                      </FormControl>
                      <FormDescription>
                        When should vendors submit their bids by?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="terms"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Terms and Conditions</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter any specific terms, conditions, or requirements..."
                          className="input-neumorphic min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Optional terms and conditions for this RFQ
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </motion.div>
        );

      case 2:
        return (
          <motion.div
            key="step2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
          >
            {/* Items List */}
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Upload className="w-5 h-5 text-primary" />
                  <span>Items & Specifications</span>
                </CardTitle>
                <CardDescription>
                  Add the items or services you need quotes for with detailed specifications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="items"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <ItemListBuilder
                          items={field.value}
                          onChange={field.onChange}
                          allowCustomFields={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </motion.div>
        );

      case 3:
        return (
          <motion.div
            key="step3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="w-5 h-5 text-primary" />
                  <span>Select Vendors</span>
                </CardTitle>
                <CardDescription>
                  Choose which vendors to invite for this RFQ with advanced filtering and recommendations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="selectedVendors"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <VendorSelector
                          vendors={vendors}
                          selectedVendors={field.value}
                          onSelectionChange={field.onChange}
                          filters={{}}
                          showPerformanceScores={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </motion.div>
        );

      case 4:
        return (
          <motion.div
            key="step4"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="w-5 h-5 text-primary" />
                  <span>Configure Submission Form</span>
                </CardTitle>
                <CardDescription>
                  Customize the form that vendors will use to submit their bids
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="formConfig"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <FormConfigBuilder
                          fields={field.value}
                          onChange={field.onChange}
                          items={watchedItems}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </motion.div>
        );

      case 5:
        return (
          <motion.div
            key="step5"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-primary" />
                  <span>Partial Selection Configuration</span>
                </CardTitle>
                <CardDescription>
                  Configure whether vendors can allow individual item purchases
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="allowPartialSelection"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          Allow Partial Selection
                        </FormLabel>
                        <FormDescription>
                          Enable vendors to opt-in for individual item purchases at quoted rates
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {watch('allowPartialSelection') && (
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="partialSelectionConfig.requireVendorConfirmation"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Require Vendor Confirmation
                            </FormLabel>
                            <FormDescription>
                              Ask vendors to confirm if they allow partial selection
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {watch('partialSelectionConfig.requireVendorConfirmation') && (
                      <>
                        <FormField
                          control={form.control}
                          name="partialSelectionConfig.confirmationMessage"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Confirmation Message</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Do you allow individual item purchases at the quoted rates?"
                                  className="input-neumorphic"
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                The question vendors will see in their submission form
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="partialSelectionConfig.instructions"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Instructions for Vendors</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Please confirm if you allow partial selection of items from your submission."
                                  className="input-neumorphic"
                                  rows={3}
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Additional instructions to help vendors understand partial selection
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="partialSelectionConfig.defaultAllowed"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">
                                  Default to Allowing Partial Selection
                                </FormLabel>
                                <FormDescription>
                                  Pre-select "Yes" for the partial selection question
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        );

      case 6:
        return (
          <motion.div
            key="step6"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Sparkles className="w-5 h-5 text-primary" />
                  <span>AI Recommendation Settings</span>
                </CardTitle>
                <CardDescription>
                  Configure how AI will analyze and recommend vendor bids
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="aiSettings.priceWeight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price Weight ({Math.round(field.value * 100)}%)</FormLabel>
                        <FormControl>
                          <input
                            type="range"
                            min="0"
                            max="1"
                            step="0.1"
                            value={field.value}
                            onChange={(e) => field.onChange(parseFloat(e.target.value))}
                            className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer"
                          />
                        </FormControl>
                        <FormDescription>
                          How much weight to give to price in recommendations
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="aiSettings.performanceWeight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Performance Weight ({Math.round(field.value * 100)}%)</FormLabel>
                        <FormControl>
                          <input
                            type="range"
                            min="0"
                            max="1"
                            step="0.1"
                            value={field.value}
                            onChange={(e) => field.onChange(parseFloat(e.target.value))}
                            className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer"
                          />
                        </FormControl>
                        <FormDescription>
                          How much weight to give to vendor performance history
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="aiSettings.deliveryWeight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Delivery Weight ({Math.round(field.value * 100)}%)</FormLabel>
                        <FormControl>
                          <input
                            type="range"
                            min="0"
                            max="1"
                            step="0.1"
                            value={field.value}
                            onChange={(e) => field.onChange(parseFloat(e.target.value))}
                            className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer"
                          />
                        </FormControl>
                        <FormDescription>
                          How much weight to give to delivery time
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="aiSettings.riskWeight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Risk Weight ({Math.round(field.value * 100)}%)</FormLabel>
                        <FormControl>
                          <input
                            type="range"
                            min="0"
                            max="1"
                            step="0.1"
                            value={field.value}
                            onChange={(e) => field.onChange(parseFloat(e.target.value))}
                            className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer"
                          />
                        </FormControl>
                        <FormDescription>
                          How much weight to give to risk assessment
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="aiSettings.diversificationPreference"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Diversification Preference ({Math.round(field.value * 100)}%)</FormLabel>
                      <FormControl>
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={field.value}
                          onChange={(e) => field.onChange(parseFloat(e.target.value))}
                          className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer"
                        />
                      </FormControl>
                      <FormDescription>
                        Preference for vendor diversification vs. consolidation
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <Sparkles className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium text-foreground mb-1">AI Recommendations</p>
                      <p className="text-sm text-muted-foreground">
                        These settings will be used to generate AI-powered recommendations when analyzing vendor bids. 
                        You can adjust these weights based on your priorities for this RFQ.
                      </p>
                    </div>
                  </div>
                </div>


              </CardContent>
            </Card>
          </motion.div>
        );

      case 7:
        return (
          <motion.div
            key="step7"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            {/* Review Summary */}
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-primary" />
                  <span>Review RFQ</span>
                </CardTitle>
                <CardDescription>
                  Review your RFQ details before sending to vendors
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-foreground mb-2">Basic Information</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p><strong>Title:</strong> {watch('title')}</p>
                    <p><strong>Description:</strong> {watch('description')}</p>
                    <p><strong>Due Date:</strong> {watch('dueDate')?.toLocaleDateString()}</p>
                    <p><strong>Items:</strong> {watchedItems.length} items</p>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-foreground mb-2">Selected Vendors</h4>
                  <div className="flex flex-wrap gap-2">
                    {watchedVendors.map((vendorId) => {
                      const vendor = vendors.find(v => v.id === vendorId);
                      return vendor ? (
                        <Badge key={vendorId} variant="secondary">
                          {vendor.name}
                        </Badge>
                      ) : null;
                    })}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-foreground mb-2">Form Configuration</h4>
                  <p className="text-sm text-muted-foreground">
                    {watch('formConfig').length} custom fields configured
                  </p>
                </div>

                <div>
                  <h4 className="font-medium text-foreground mb-2">Partial Selection</h4>
                  <p className="text-sm text-muted-foreground">
                    {watch('allowPartialSelection') ? 'Enabled' : 'Disabled'}
                  </p>
                </div>

                <div>
                  <h4 className="font-medium text-foreground mb-2">AI Settings</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p><strong>Price Weight:</strong> {Math.round(watch('aiSettings.priceWeight') * 100)}%</p>
                    <p><strong>Performance Weight:</strong> {Math.round(watch('aiSettings.performanceWeight') * 100)}%</p>
                    <p><strong>Delivery Weight:</strong> {Math.round(watch('aiSettings.deliveryWeight') * 100)}%</p>
                    <p><strong>Risk Weight:</strong> {Math.round(watch('aiSettings.riskWeight') * 100)}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
          {/* Progress Steps */}
          <div className="card-neumorphic p-6">
            <div className="flex flex-wrap items-center justify-center gap-2 lg:gap-4">
              {STEPS.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center space-x-2 ${
                    currentStep >= step.id ? 'text-primary' : 'text-muted-foreground'
                  }`}>
                    <div className={`w-8 h-8 lg:w-10 lg:h-10 rounded-full flex items-center justify-center ${
                      currentStep >= step.id 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-muted text-muted-foreground'
                    }`}>
                      {currentStep > step.id ? (
                        <CheckCircle className="w-4 h-4 lg:w-5 lg:h-5" />
                      ) : (
                        <step.icon className="w-4 h-4 lg:w-5 lg:h-5" />
                      )}
                    </div>
                    <div className="hidden sm:block">
                      <p className="font-medium text-xs lg:text-sm">{step.title}</p>
                      <p className="text-xs text-muted-foreground hidden lg:block">{step.description}</p>
                    </div>
                  </div>
                  {index < STEPS.length - 1 && (
                    <ArrowRight className="w-4 h-4 lg:w-5 lg:h-5 text-muted-foreground mx-1 lg:mx-2" />
                  )}
                </div>
              ))}
            </div>
            
            {/* Current Step Info for Mobile */}
            <div className="sm:hidden mt-4 text-center">
              <p className="font-medium text-sm">{STEPS[currentStep - 1]?.title}</p>
              <p className="text-xs text-muted-foreground">{STEPS[currentStep - 1]?.description}</p>
            </div>
          </div>

          {/* Step Content */}
          {renderStepContent()}

          {/* Draft Status */}
          {onSaveDraft && (
            <div className="flex items-center justify-center py-4">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                {isDraftSaving ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Saving draft...</span>
                  </>
                ) : lastSaved ? (
                  <>
                    <Save className="w-4 h-4" />
                    <span>Draft saved at {lastSaved.toLocaleTimeString()}</span>
                  </>
                ) : (
                  <>
                    <Clock className="w-4 h-4" />
                    <span>Auto-save enabled</span>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Navigation */}
          <div className="flex items-center justify-between pt-6">
            <div className="flex items-center space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
                className="btn-neumorphic"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>
              
              {onSaveDraft && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={saveDraft}
                  disabled={isDraftSaving}
                  className="btn-neumorphic"
                >
                  {isDraftSaving ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="w-4 h-4 mr-2" />
                  )}
                  Save Draft
                </Button>
              )}
            </div>

            {currentStep < 7 ? (
              <Button
                type="button"
                onClick={nextStep}
                className="btn-neumorphic gradient-primary text-primary-foreground"
              >
                Next
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Button
                type="button"
                onClick={handleDirectSubmit}
                disabled={isLoading}
                className="btn-neumorphic gradient-primary text-primary-foreground px-8"
              >
                {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Create RFQ
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
};