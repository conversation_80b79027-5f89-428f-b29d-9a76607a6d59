import React, { useState, useEffect } from 'react';
import { 
  CheckCircle2,
  Star,
  AlertTriangle,
  Users,
  DollarSign,
  Clock,
  Truck,
  FileText,
  Brain,
  Target,
  Save,
  RotateCcw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { RFQSubmission, BidItem } from '@/types/rfq';
import { formatCurrency } from '@/types/rfq';

interface VendorSelectionProps {
  rfqId: number;
  submissions: RFQSubmission[];
  rfqItems: any[];
  selectedBids: Record<string, {
    submissionId: number;
    vendorId: number;
    vendorName: string;
    itemId: string;
    unitPrice: number;
    totalPrice: number;
    deliveryDays?: number;
    notes?: string;
  }>;
  onBidSelect: (itemId: string, submissionId: number, bidData: any) => void;
  onSelectionRationale: (itemId: string, rationale: string) => void;
  selectionRationale: Record<string, string>;
  diversificationEnabled: boolean;
  onDiversificationToggle: (enabled: boolean) => void;
  currency: string;
}

export const VendorSelection: React.FC<VendorSelectionProps> = ({
  rfqId,
  submissions,
  rfqItems,
  selectedBids,
  onBidSelect,
  onSelectionRationale,
  selectionRationale,
  diversificationEnabled,
  onDiversificationToggle,
  currency
}) => {
  const [aiRecommendations, setAiRecommendations] = useState<any>(null);
  const [showRecommendations, setShowRecommendations] = useState(true);

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const getBidForItem = (itemId: string, submissionId: number): BidItem | undefined => {
    const submission = submissions.find(s => s.id === submissionId);
    return submission?.bid_items.find(item => item.rfq_item_id === itemId);
  };

  const getSubmissionById = (submissionId: number): RFQSubmission | undefined => {
    return submissions.find(s => s.id === submissionId);
  };

  const calculateTotalSelection = () => {
    let total = 0;
    let totalItems = 0;
    const vendorCounts: Record<number, number> = {};

    Object.values(selectedBids).forEach(bid => {
      total += bid.totalPrice;
      totalItems++;
      vendorCounts[bid.vendorId] = (vendorCounts[bid.vendorId] || 0) + 1;
    });

    const uniqueVendors = Object.keys(vendorCounts).length;
    const diversificationScore = uniqueVendors / Math.max(totalItems, 1) * 100;

    return {
      total,
      totalItems,
      uniqueVendors,
      diversificationScore,
      vendorCounts
    };
  };

  const getRecommendationForItem = (itemId: string) => {
    // Mock AI recommendation logic
    const itemSubmissions = submissions.map(submission => {
      const bidItem = submission.bid_items.find(item => item.rfq_item_id === itemId);
      if (!bidItem) return null;

      const priceScore = 100 - ((bidItem.unit_price - Math.min(...submissions.map(s => s.bid_items.find(i => i.rfq_item_id === itemId)?.unit_price || 0))) / Math.min(...submissions.map(s => s.bid_items.find(i => i.rfq_item_id === itemId)?.unit_price || 1))) * 100;
      const performanceScore = submission.vendor_performance_score;
      const deliveryScore = bidItem.delivery_days ? Math.max(0, 100 - bidItem.delivery_days) : 50;
      
      const overallScore = (priceScore * 0.4) + (performanceScore * 0.3) + (deliveryScore * 0.3);

      return {
        submissionId: submission.id,
        vendorName: submission.vendor_name,
        overallScore,
        priceScore,
        performanceScore,
        deliveryScore,
        reasoning: `Best balance of price (${priceScore.toFixed(1)}), performance (${performanceScore.toFixed(1)}), and delivery (${deliveryScore.toFixed(1)})`
      };
    }).filter(Boolean).sort((a, b) => (b?.overallScore || 0) - (a?.overallScore || 0));

    return itemSubmissions[0];
  };

  const stats = calculateTotalSelection();

  return (
    <div className="space-y-6">
      {/* Selection Summary */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-900">{stats.totalItems}</div>
              <div className="text-sm text-blue-700">Items Selected</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-900">{formatCurrency(stats.total, currency)}</div>
              <div className="text-sm text-green-700">Total Value</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-900">{stats.uniqueVendors}</div>
              <div className="text-sm text-purple-700">Unique Vendors</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-900">{stats.diversificationScore.toFixed(0)}%</div>
              <div className="text-sm text-orange-700">Diversification</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Diversification Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                Selection Strategy
              </CardTitle>
              <CardDescription>
                Configure your vendor selection preferences
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Label htmlFor="diversification">Enable Diversification</Label>
              <Switch
                id="diversification"
                checked={diversificationEnabled}
                onCheckedChange={onDiversificationToggle}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {diversificationEnabled && (
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start gap-3">
                <Brain className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900">Smart Diversification Enabled</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    The system will recommend spreading selections across multiple vendors to reduce risk and improve negotiation power.
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Item Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="w-5 h-5" />
            Item-by-Item Selection
          </CardTitle>
          <CardDescription>
            Select the best vendor for each item in your RFQ
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {rfqItems.map((item) => {
              const itemSubmissions = submissions.filter(submission => 
                submission.bid_items.some(bidItem => bidItem.rfq_item_id === item.id)
              );
              
              const selectedBid = selectedBids[item.id];
              const recommendation = showRecommendations ? getRecommendationForItem(item.id) : null;

              return (
                <div key={item.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h4 className="font-semibold text-lg">{item.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        Quantity: {item.quantity} | Category: {item.category || 'N/A'}
                      </p>
                      {item.description && (
                        <p className="text-sm text-muted-foreground mt-1">{item.description}</p>
                      )}
                    </div>
                    {selectedBid && (
                      <Badge className="bg-green-100 text-green-800 border-green-200">
                        <CheckCircle2 className="w-3 h-3 mr-1" />
                        Selected
                      </Badge>
                    )}
                  </div>

                  {/* AI Recommendation */}
                  {recommendation && showRecommendations && (
                    <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-start gap-2">
                        <Brain className="w-4 h-4 text-blue-600 mt-0.5" />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium text-blue-900">AI Recommendation:</span>
                            <Badge variant="outline" className="text-xs">
                              {recommendation.vendorName}
                            </Badge>
                            <span className="text-xs text-blue-700">
                              Score: {recommendation.overallScore.toFixed(1)}
                            </span>
                          </div>
                          <p className="text-xs text-blue-700">{recommendation.reasoning}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Vendor Options */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4">
                    {itemSubmissions.map((submission) => {
                      const bidItem = submission.bid_items.find(bi => bi.rfq_item_id === item.id);
                      if (!bidItem) return null;

                      const isSelected = selectedBid?.submissionId === submission.id;
                      const isRecommended = recommendation?.submissionId === submission.id;

                      return (
                        <div
                          key={submission.id}
                          className={`border rounded-lg p-3 cursor-pointer transition-all ${
                            isSelected 
                              ? 'border-green-500 bg-green-50' 
                              : isRecommended
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-border hover:border-primary hover:bg-accent'
                          }`}
                          onClick={() => onBidSelect(item.id, submission.id, {
                            vendorId: submission.vendor_id,
                            vendorName: submission.vendor_name,
                            itemId: item.id,
                            unitPrice: bidItem.unit_price,
                            totalPrice: bidItem.total_price,
                            deliveryDays: bidItem.delivery_days,
                            notes: bidItem.notes
                          })}
                        >
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <Avatar className="w-6 h-6">
                                <AvatarFallback className="text-xs">
                                  {getInitials(submission.vendor_name)}
                                </AvatarFallback>
                              </Avatar>
                              <span className="font-medium text-sm">{submission.vendor_name}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              {isSelected && <CheckCircle2 className="w-4 h-4 text-green-600" />}
                              {isRecommended && <Star className="w-4 h-4 text-blue-600" />}
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-muted-foreground">Unit Price:</span>
                              <span className="font-medium text-sm">
                                {formatCurrency(bidItem.unit_price, submission.currency)}
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-muted-foreground">Total:</span>
                              <span className="font-bold text-sm">
                                {formatCurrency(bidItem.total_price, submission.currency)}
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-muted-foreground">Performance:</span>
                              <div className="flex items-center gap-1">
                                <Progress 
                                  value={submission.vendor_performance_score} 
                                  className="w-12 h-1" 
                                />
                                <span className="text-xs">{submission.vendor_performance_score.toFixed(1)}</span>
                              </div>
                            </div>
                            {bidItem.delivery_days && (
                              <div className="flex items-center justify-between">
                                <span className="text-xs text-muted-foreground">Delivery:</span>
                                <span className="text-xs">{bidItem.delivery_days} days</span>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Selection Rationale */}
                  {selectedBid && (
                    <div className="space-y-2">
                      <Label htmlFor={`rationale-${item.id}`} className="text-sm font-medium">
                        Selection Rationale (Optional)
                      </Label>
                      <Textarea
                        id={`rationale-${item.id}`}
                        placeholder="Explain why you selected this vendor for this item..."
                        value={selectionRationale[item.id] || ''}
                        onChange={(e) => onSelectionRationale(item.id, e.target.value)}
                        rows={2}
                        className="text-sm"
                      />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Selection Actions */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm">
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset All
              </Button>
              <Button variant="outline" size="sm">
                <Brain className="w-4 h-4 mr-2" />
                Apply AI Recommendations
              </Button>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <Save className="w-4 h-4 mr-2" />
                Save Draft
              </Button>
              <Button disabled={stats.totalItems === 0}>
                <CheckCircle2 className="w-4 h-4 mr-2" />
                Confirm Selection
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VendorSelection;