import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Calendar, 
  Users, 
  FileText, 
  TrendingUp, 
  MoreVertical,
  Edit,
  Trash2,
  Send,
  Eye
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import RFQStatusBadge from './RFQStatusBadge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/hooks/useAuth';
import { RFQ } from '@/types/rfq';
import { 
  getRFQStatusColor, 
  formatCurrency, 
  isRFQEditable, 
  canSendInvitations,
  isRFQCancellable 
} from '@/types/rfq';

interface RFQCardProps {
  rfq: RFQ;
  onView: (id: number) => void;
  onEdit?: (id: number) => void;
  onDelete?: (id: number) => void;
  onSendInvitations?: (id: number) => void;
  onStatusChange?: (id: number, newStatus: any, reason?: string) => Promise<void>;
  showActions?: boolean;
}

export const RFQCard: React.FC<RFQCardProps> = ({
  rfq,
  onView,
  onEdit,
  onDelete,
  onSendInvitations,
  onStatusChange,
  showActions = true
}) => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const handleCardClick = () => {
    onView(rfq.id);
  };

  const handleActionClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation();
    action();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getDaysUntilDue = () => {
    const dueDate = new Date(rfq.due_date);
    const today = new Date();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysUntilDue = getDaysUntilDue();
  const isOverdue = daysUntilDue < 0;
  const isDueSoon = daysUntilDue <= 3 && daysUntilDue >= 0;

  return (
    <Card 
      className="card-neumorphic hover:scale-105 transition-all duration-200 cursor-pointer group"
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-foreground truncate group-hover:text-primary transition-colors">
              {rfq.title}
            </h3>
            {rfq.description && (
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                {rfq.description}
              </p>
            )}
          </div>
          
          <div className="flex items-center gap-2 ml-4">
            <RFQStatusBadge 
              status={rfq.status} 
              showIcon={false}
              allowTransitions={hasPermission('manager')}
              rfqId={rfq.id}
              onStatusChange={onStatusChange ? (newStatus, reason) => onStatusChange(rfq.id, newStatus, reason) : undefined}
            />
            
            {showActions && hasPermission('manager') && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem onClick={(e) => handleActionClick(e, () => onView(rfq.id))}>
                    <Eye className="mr-2 h-4 w-4" />
                    View Details
                  </DropdownMenuItem>
                  
                  {isRFQEditable(rfq.status) && onEdit && (
                    <DropdownMenuItem onClick={(e) => handleActionClick(e, () => onEdit(rfq.id))}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit RFQ
                    </DropdownMenuItem>
                  )}
                  
                  {canSendInvitations(rfq.status) && onSendInvitations && (
                    <DropdownMenuItem onClick={(e) => handleActionClick(e, () => onSendInvitations(rfq.id))}>
                      <Send className="mr-2 h-4 w-4" />
                      Send Invitations
                    </DropdownMenuItem>
                  )}
                  
                  {isRFQCancellable(rfq.status) && onDelete && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={(e) => handleActionClick(e, () => onDelete(rfq.id))}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete RFQ
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="grid grid-cols-2 gap-4 mb-4">
          {/* Due Date */}
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-xs text-muted-foreground">Due Date</p>
              <p className={`text-sm font-medium ${
                isOverdue ? 'text-destructive' : 
                isDueSoon ? 'text-yellow-600' : 
                'text-foreground'
              }`}>
                {formatDate(rfq.due_date)}
                {isOverdue && (
                  <span className="ml-1 text-xs">({Math.abs(daysUntilDue)} days overdue)</span>
                )}
                {isDueSoon && !isOverdue && (
                  <span className="ml-1 text-xs">({daysUntilDue} days left)</span>
                )}
              </p>
            </div>
          </div>

          {/* Items Count */}
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-xs text-muted-foreground">Items</p>
              <p className="text-sm font-medium text-foreground">
                {rfq.item_count} {rfq.item_count === 1 ? 'item' : 'items'}
              </p>
            </div>
          </div>

          {/* Invitations */}
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-xs text-muted-foreground">Invitations</p>
              <p className="text-sm font-medium text-foreground">
                {rfq.invitation_count} vendors
              </p>
            </div>
          </div>

          {/* Response Rate */}
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-xs text-muted-foreground">Response Rate</p>
              <p className="text-sm font-medium text-foreground">
                {rfq.response_rate}% ({rfq.submission_count}/{rfq.invitation_count})
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-3 border-t border-border">
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Created</span>
            <span className="text-xs font-medium text-foreground">
              {formatDate(rfq.created_at)}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Currency</span>
            <Badge variant="outline" className="text-xs">
              {rfq.currency}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RFQCard;