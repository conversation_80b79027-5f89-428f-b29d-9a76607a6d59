import React, { useState } from 'react';
import { 
  FileText, 
  Send, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  ChevronDown,
  Play,
  Pause,
  X,
  RotateCcw
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Textarea } from '@/components/ui/textarea';
import { RFQStatus, getRFQStatusColor } from '@/types/rfq';
import { useToast } from '@/hooks/use-toast';

interface RFQStatusBadgeProps {
  status: RFQStatus;
  showIcon?: boolean;
  variant?: 'default' | 'outline' | 'secondary';
  size?: 'sm' | 'default' | 'lg';
  allowTransitions?: boolean;
  rfqId?: number;
  onStatusChange?: (newStatus: RFQStatus, reason?: string) => Promise<void>;
  disabled?: boolean;
}

export const RFQStatusBadge: React.FC<RFQStatusBadgeProps> = ({
  status,
  showIcon = true,
  variant = 'secondary',
  size = 'default',
  allowTransitions = false,
  rfqId,
  onStatusChange,
  disabled = false
}) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<RFQStatus | null>(null);
  const [reason, setReason] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();
  // Define valid status transitions
  const getValidTransitions = (currentStatus: RFQStatus): RFQStatus[] => {
    switch (currentStatus) {
      case 'draft':
        return ['sent', 'cancelled'];
      case 'sent':
        return ['in_progress', 'cancelled'];
      case 'in_progress':
        return ['closed', 'cancelled'];
      case 'closed':
        return ['in_progress']; // Allow reopening
      case 'cancelled':
        return ['draft']; // Allow reactivation
      default:
        return [];
    }
  };

  const getTransitionLabel = (fromStatus: RFQStatus, toStatus: RFQStatus): string => {
    const transitions: Record<string, string> = {
      'draft-sent': 'Send to Vendors',
      'draft-cancelled': 'Cancel RFQ',
      'sent-in_progress': 'Mark In Progress',
      'sent-cancelled': 'Cancel RFQ',
      'in_progress-closed': 'Close RFQ',
      'in_progress-cancelled': 'Cancel RFQ',
      'closed-in_progress': 'Reopen RFQ',
      'cancelled-draft': 'Reactivate as Draft'
    };
    return transitions[`${fromStatus}-${toStatus}`] || `Change to ${toStatus}`;
  };

  const getTransitionIcon = (toStatus: RFQStatus) => {
    switch (toStatus) {
      case 'sent':
        return <Send className="w-4 h-4" />;
      case 'in_progress':
        return <Play className="w-4 h-4" />;
      case 'closed':
        return <CheckCircle className="w-4 h-4" />;
      case 'cancelled':
        return <X className="w-4 h-4" />;
      case 'draft':
        return <RotateCcw className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const requiresConfirmation = (toStatus: RFQStatus): boolean => {
    return ['cancelled', 'closed'].includes(toStatus);
  };

  const handleStatusTransition = async (newStatus: RFQStatus) => {
    if (requiresConfirmation(newStatus)) {
      setPendingStatus(newStatus);
      setShowConfirmDialog(true);
      return;
    }

    await executeStatusChange(newStatus);
  };

  const executeStatusChange = async (newStatus: RFQStatus, changeReason?: string) => {
    if (!onStatusChange) return;

    try {
      setIsUpdating(true);
      await onStatusChange(newStatus, changeReason);
      
      toast({
        title: "Status Updated",
        description: `RFQ status changed to ${getStatusLabel(newStatus)}`,
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update RFQ status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
      setShowConfirmDialog(false);
      setPendingStatus(null);
      setReason('');
    }
  };

  const getStatusIcon = (status: RFQStatus) => {
    const iconSize = size === 'sm' ? 'w-3 h-3' : size === 'lg' ? 'w-5 h-5' : 'w-4 h-4';
    
    switch (status) {
      case 'draft':
        return <FileText className={`${iconSize} text-gray-500`} />;
      case 'sent':
        return <Send className={`${iconSize} text-blue-500`} />;
      case 'in_progress':
        return <Clock className={`${iconSize} text-yellow-500`} />;
      case 'closed':
        return <CheckCircle className={`${iconSize} text-green-500`} />;
      case 'cancelled':
        return <AlertTriangle className={`${iconSize} text-red-500`} />;
      default:
        return <FileText className={`${iconSize} text-gray-500`} />;
    }
  };

  const getStatusLabel = (status: RFQStatus) => {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'sent':
        return 'Sent';
      case 'in_progress':
        return 'In Progress';
      case 'closed':
        return 'Closed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const color = getRFQStatusColor(status);
  const badgeClass = variant === 'outline' 
    ? `border-${color}-200 text-${color}-700`
    : `bg-${color}-100 text-${color}-800 border-${color}-200`;

  const validTransitions = getValidTransitions(status);
  const canTransition = allowTransitions && validTransitions.length > 0 && !disabled;

  const badgeContent = (
    <Badge 
      variant={variant} 
      className={`${badgeClass} ${size === 'sm' ? 'text-xs px-2 py-0.5' : size === 'lg' ? 'text-sm px-3 py-1' : ''} ${canTransition ? 'cursor-pointer' : ''}`}
    >
      {showIcon && (
        <>
          {getStatusIcon(status)}
          <span className="ml-1">{getStatusLabel(status)}</span>
        </>
      )}
      {!showIcon && getStatusLabel(status)}
      {canTransition && <ChevronDown className="w-3 h-3 ml-1" />}
    </Badge>
  );

  if (!canTransition) {
    return badgeContent;
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={isUpdating}>
          {badgeContent}
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-56">
          <DropdownMenuLabel>Change Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {validTransitions.map((newStatus) => (
            <DropdownMenuItem
              key={newStatus}
              onClick={() => handleStatusTransition(newStatus)}
              className="flex items-center gap-2"
            >
              {getTransitionIcon(newStatus)}
              <span>{getTransitionLabel(status, newStatus)}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Confirm Status Change
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to {getTransitionLabel(status, pendingStatus!).toLowerCase()}?
              {pendingStatus === 'cancelled' && ' This action cannot be easily undone.'}
              {pendingStatus === 'closed' && ' This will finalize the RFQ process.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="py-4">
            <label className="text-sm font-medium mb-2 block">
              Reason for change (optional):
            </label>
            <Textarea
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Enter reason for status change..."
              className="min-h-[80px]"
            />
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={isUpdating}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => executeStatusChange(pendingStatus!, reason)}
              disabled={isUpdating}
              className={pendingStatus === 'cancelled' ? 'bg-destructive hover:bg-destructive/90' : ''}
            >
              {isUpdating ? 'Updating...' : 'Confirm'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default RFQStatusBadge;