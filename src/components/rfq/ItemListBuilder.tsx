import React, { useState, useCallback, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Plus,
  Trash2,
  Package,
  Edit3,
  DollarSign,
  Hash,
  FileText,
  Tag,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RFQItem } from "./RFQCreationForm";

interface ItemListBuilderProps {
  items: RFQItem[];
  onChange: (items: RFQItem[]) => void;
  allowCustomFields: boolean;
}

const ITEM_CATEGORIES = [
  "Office Supplies",
  "Technology",
  "Manufacturing",
  "Services",
  "Equipment",
  "Materials",
  "Software",
  "Consulting",
  "Other",
];

export const ItemListBuilder: React.FC<ItemListBuilderProps> = React.memo(
  ({ items, onChange, allowCustomFields }) => {
    const [editingItem, setEditingItem] = useState<string | null>(null);

    const generateId = () =>
      `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const addItem = () => {
      const newItem: RFQItem = {
        id: generateId(),
        name: "",
        description: "",
        quantity: 1,
        specifications: {},
        category: "Other",
      };
      onChange([...items, newItem]);
      setEditingItem(newItem.id);
    };

    const updateItem = useCallback(
      (id: string, updates: Partial<RFQItem>) => {
        const updatedItems = items.map((item) =>
          item.id === id ? { ...item, ...updates } : item
        );
        onChange(updatedItems);
      },
      [items, onChange]
    );

    const removeItem = (id: string) => {
      const filteredItems = items.filter((item) => item.id !== id);
      onChange(filteredItems);
      if (editingItem === id) {
        setEditingItem(null);
      }
    };

    const duplicateItem = (item: RFQItem) => {
      const duplicatedItem: RFQItem = {
        ...item,
        id: generateId(),
        name: `${item.name} (Copy)`,
      };
      onChange([...items, duplicatedItem]);
    };

    const addSpecification = (itemId: string, key: string, value: string) => {
      const item = items.find((i) => i.id === itemId);
      if (item) {
        updateItem(itemId, {
          specifications: {
            ...item.specifications,
            [key]: value,
          },
        });
      }
    };

    const removeSpecification = (itemId: string, key: string) => {
      const item = items.find((i) => i.id === itemId);
      if (item) {
        const { [key]: removed, ...rest } = item.specifications;
        updateItem(itemId, { specifications: rest });
      }
    };

    const ItemCard: React.FC<{ item: RFQItem; isEditing: boolean }> = ({
      item,
      isEditing,
    }) => {
      const [localName, setLocalName] = useState(item.name);
      const [localDescription, setLocalDescription] = useState(
        item.description
      );
      const [localSpecs, setLocalSpecs] = useState<Record<string, string>>(
        item.specifications as Record<string, string>
      );
      const [localQuantity, setLocalQuantity] = useState(item.quantity);
      const [localEstimatedPrice, setLocalEstimatedPrice] = useState(
        item.estimatedPrice || ""
      );

      // Update local state when item changes from outside
      useEffect(() => {
        setLocalName(item.name);
        setLocalDescription(item.description);
        setLocalSpecs(item.specifications as Record<string, string>);
        setLocalQuantity(item.quantity);
        setLocalEstimatedPrice(item.estimatedPrice || "");
      }, [
        item.name,
        item.description,
        item.specifications,
        item.quantity,
        item.estimatedPrice,
      ]);

      // onBlur handlers to save changes when user finishes editing
      const handleNameBlur = () => {
        if (localName !== item.name) {
          updateItem(item.id, { name: localName });
        }
      };

      const handleDescriptionBlur = () => {
        if (localDescription !== item.description) {
          updateItem(item.id, { description: localDescription });
        }
      };

      const handleSpecsBlur = () => {
        if (
          JSON.stringify(localSpecs) !== JSON.stringify(item.specifications)
        ) {
          updateItem(item.id, { specifications: localSpecs });
        }
      };

      const handleQuantityBlur = () => {
        if (localQuantity !== item.quantity) {
          updateItem(item.id, { quantity: localQuantity });
        }
      };

      const handleEstimatedPriceBlur = () => {
        const priceValue = localEstimatedPrice
          ? parseFloat(localEstimatedPrice.toString())
          : undefined;
        if (priceValue !== item.estimatedPrice) {
          updateItem(item.id, { estimatedPrice: priceValue });
        }
      };

      // Helper functions for specifications
      const updateSpecValue = (key: string, value: string) => {
        setLocalSpecs((prev) => ({ ...prev, [key]: value }));
      };

      const updateSpecKey = (oldKey: string, newKey: string, value: string) => {
        setLocalSpecs((prev) => {
          const newSpecs = { ...prev };
          delete newSpecs[oldKey];
          newSpecs[newKey] = value;
          return newSpecs;
        });
      };

      const removeSpec = (key: string) => {
        setLocalSpecs((prev) => {
          const newSpecs = { ...prev };
          delete newSpecs[key];
          return newSpecs;
        });
      };

      const addSpec = (key: string, value: string) => {
        setLocalSpecs((prev) => ({ ...prev, [key]: value }));
      };

      return (
        <Card className="card-neumorphic">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2 text-base">
                <Package className="w-4 h-4 text-primary" />
                <span>{item.name || "Untitled Item"}</span>
                <Badge variant="outline" className="text-xs">
                  {item.category}
                </Badge>
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setEditingItem(isEditing ? null : item.id)}
                  className="btn-neumorphic"
                >
                  <Edit3 className="w-4 h-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => duplicateItem(item)}
                  className="btn-neumorphic"
                >
                  <Plus className="w-4 h-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeItem(item.id)}
                  className="btn-neumorphic text-destructive hover:text-destructive"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {isEditing ? (
              <div className="space-y-4">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">
                      Item Name *
                    </label>
                    <Input
                      placeholder="Enter item name"
                      value={localName}
                      onChange={(e) => setLocalName(e.target.value)}
                      onBlur={handleNameBlur}
                      className="input-neumorphic"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">
                      Category *
                    </label>
                    <Select
                      value={item.category}
                      onValueChange={(value) =>
                        updateItem(item.id, { category: value })
                      }
                    >
                      <SelectTrigger className="input-neumorphic">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {ITEM_CATEGORIES.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Description
                  </label>
                  <Textarea
                    placeholder="Describe the item requirements..."
                    value={localDescription}
                    onChange={(e) => setLocalDescription(e.target.value)}
                    onBlur={handleDescriptionBlur}
                    className="input-neumorphic min-h-[80px]"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">
                      Quantity *
                    </label>
                    <Input
                      type="number"
                      min="1"
                      placeholder="1"
                      value={localQuantity}
                      onChange={(e) =>
                        setLocalQuantity(parseInt(e.target.value) || 1)
                      }
                      onBlur={handleQuantityBlur}
                      className="input-neumorphic"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">
                      Estimated Price (Optional)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      value={localEstimatedPrice}
                      onChange={(e) => setLocalEstimatedPrice(e.target.value)}
                      onBlur={handleEstimatedPriceBlur}
                      className="input-neumorphic"
                    />
                  </div>
                </div>

                {/* Specifications */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="text-sm font-medium text-foreground">
                      Specifications & Requirements
                    </label>
                    <div className="flex items-center space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Add common specifications quickly
                          const commonSpecs = [
                            "Material",
                            "Color",
                            "Size",
                            "Weight",
                            "Brand",
                            "Model",
                          ];
                          const unusedSpecs = commonSpecs.filter(
                            (spec) => !localSpecs[spec]
                          );
                          if (unusedSpecs.length > 0) {
                            addSpec(unusedSpecs[0], "");
                          }
                        }}
                        className="btn-neumorphic"
                      >
                        <Plus className="w-4 h-4 mr-1" />
                        Quick Add
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const key = prompt("Custom specification name:");
                          if (key) {
                            addSpec(key, "");
                          }
                        }}
                        className="btn-neumorphic"
                      >
                        <Plus className="w-4 h-4 mr-1" />
                        Custom
                      </Button>
                    </div>
                  </div>

                  {/* Common specification templates */}
                  {Object.keys(localSpecs).length === 0 && (
                    <div className="mb-4 p-3 bg-muted/30 rounded-lg">
                      <p className="text-sm font-medium text-foreground mb-2">
                        Common Specifications:
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {[
                          "Material",
                          "Color",
                          "Size",
                          "Weight",
                          "Brand",
                          "Model",
                          "Warranty",
                          "Compliance",
                        ].map((spec) => (
                          <Button
                            key={spec}
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => addSpec(spec, "")}
                            className="btn-neumorphic text-xs h-7"
                          >
                            + {spec}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="space-y-3">
                    {Object.entries(localSpecs).map(([key, value], index) => (
                      <div
                        key={`${item.id}-spec-${index}`}
                        className="p-3 border border-border rounded-lg bg-background/50"
                      >
                        <div className="flex items-center space-x-2 mb-2">
                          <Input
                            placeholder="Specification name"
                            value={key}
                            onChange={(e) => {
                              const newKey = e.target.value;
                              if (newKey !== key) {
                                updateSpecKey(key, newKey, value as string);
                              }
                            }}
                            onBlur={handleSpecsBlur}
                            className="input-neumorphic flex-1 font-medium"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeSpec(key)}
                            className="btn-neumorphic text-destructive hover:text-destructive"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                        <Textarea
                          placeholder="Enter specification details, requirements, or constraints..."
                          value={value as string}
                          onChange={(e) => updateSpecValue(key, e.target.value)}
                          onBlur={handleSpecsBlur}
                          className="input-neumorphic min-h-[60px] text-sm"
                        />
                      </div>
                    ))}
                    {Object.keys(localSpecs).length === 0 && (
                      <div className="text-center py-6">
                        <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-3 opacity-50" />
                        <p className="text-sm text-muted-foreground mb-2">
                          No specifications added yet
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Add specifications to help vendors provide accurate
                          quotes
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                {item.description && (
                  <p className="text-sm text-muted-foreground">
                    {item.description}
                  </p>
                )}

                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-1">
                    <Hash className="w-4 h-4 text-muted-foreground" />
                    <span>Qty: {item.quantity}</span>
                  </div>
                  {item.estimatedPrice && (
                    <div className="flex items-center space-x-1">
                      <DollarSign className="w-4 h-4 text-muted-foreground" />
                      <span>Est: ${item.estimatedPrice.toFixed(2)}</span>
                    </div>
                  )}
                </div>

                {Object.keys(item.specifications).length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-foreground mb-2">
                      Specifications:
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(item.specifications).map(
                        ([key, value]) => (
                          <Badge
                            key={key}
                            variant="outline"
                            className="text-xs"
                          >
                            {key}: {value}
                          </Badge>
                        )
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      );
    };

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground">
              Items List
            </h3>
            <p className="text-sm text-muted-foreground">
              {items.length} item{items.length !== 1 ? "s" : ""} added
            </p>
          </div>
          <Button
            type="button"
            onClick={addItem}
            className="btn-neumorphic gradient-primary text-primary-foreground"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Item
          </Button>
        </div>

        <AnimatePresence>
          {items.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-12"
            >
              <Package className="w-16 h-16 text-muted-foreground mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                No items added yet
              </h3>
              <p className="text-muted-foreground mb-4">
                Start by adding the items or services you need quotes for
              </p>
              <Button
                type="button"
                onClick={addItem}
                className="btn-neumorphic gradient-primary text-primary-foreground"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Item
              </Button>
            </motion.div>
          ) : (
            <div className="space-y-4">
              {items.map((item) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  layout
                >
                  <ItemCard item={item} isEditing={editingItem === item.id} />
                </motion.div>
              ))}
            </div>
          )}
        </AnimatePresence>

        {items.length > 0 && (
          <div className="flex items-center justify-center pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={addItem}
              className="btn-neumorphic"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Another Item
            </Button>
          </div>
        )}
      </div>
    );
  }
);
