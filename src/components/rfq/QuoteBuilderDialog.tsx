import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { QuoteBuilder } from './QuoteBuilder';
import { Quote } from '@/services/api/quotes';

interface QuoteBuilderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  rfqId: number;
  onQuoteGenerated: (quote: Quote) => void;
}

export const QuoteBuilderDialog: React.FC<QuoteBuilderDialogProps> = ({
  open,
  onOpenChange,
  rfqId,
  onQuoteGenerated,
}) => {
  const handleQuoteGenerated = (quote: Quote) => {
    onQuoteGenerated(quote);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Generate Quote</DialogTitle>
        </DialogHeader>
        <QuoteBuilder
          rfqId={rfqId}
          onQuoteGenerated={handleQuoteGenerated}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  );
};