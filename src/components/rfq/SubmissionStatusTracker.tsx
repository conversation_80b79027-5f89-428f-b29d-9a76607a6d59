import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Check<PERSON><PERSON>cle,
  Clock,
  AlertCircle,
  FileText,
  Calendar,
  Edit,
  Download,
  Mail,
  Phone,
  Building,
  Trash2,
  RefreshCw,
  ExternalLink,
  History
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';

interface SubmissionStatus {
  token_valid: boolean;
  rfq_title: string;
  rfq_due_date: string;
  rfq_status: string;
  vendor_name: string;
  invitation_status: string;
  has_submission: boolean;
  submission_id?: number;
  can_submit: boolean;
  can_modify: boolean;
}

interface SubmissionDetails {
  id: number;
  rfq_id: number;
  vendor_id: number;
  bid_items: BidItem[];
  total_amount: number;
  currency: string;
  delivery_days?: number;
  payment_terms: string;
  validity_period: number;
  additional_notes: string;
  attachments: FileAttachment[];
  submitted_at: string;
  updated_at: string;
  rfq_title: string;
  rfq_description: string;
  rfq_due_date: string;
  vendor_name: string;
  vendor_email: string;
}

interface BidItem {
  id: number;
  rfq_item_id: string;
  item_name: string;
  unit_price: number;
  quantity: number;
  total_price: number;
  delivery_days?: number;
  notes: string;
}

interface FileAttachment {
  filename: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: string;
}

interface SubmissionVersion {
  version: number;
  submitted_at: string;
  total_amount: number;
  changes: string[];
}

interface SubmissionStatusTrackerProps {
  token: string;
  onEditSubmission?: () => void;
  showEditButton?: boolean;
}

export const SubmissionStatusTracker: React.FC<SubmissionStatusTrackerProps> = ({
  token,
  onEditSubmission,
  showEditButton = true
}) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [status, setStatus] = useState<SubmissionStatus | null>(null);
  const [submissionDetails, setSubmissionDetails] = useState<SubmissionDetails | null>(null);
  const [submissionHistory, setSubmissionHistory] = useState<SubmissionVersion[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleting, setDeleting] = useState(false);

  const loadStatusData = async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      // Get submission status
      const statusResponse = await fetch(`/api/public/rfq/${token}/status`);
      const statusResult = await statusResponse.json();

      if (!statusResponse.ok) {
        throw new Error(statusResult.message || 'Failed to load submission status');
      }

      setStatus(statusResult.data);

      // If there's a submission, get the details
      if (statusResult.data.has_submission) {
        const detailsResponse = await fetch(`/api/public/rfq/${token}/submission`);
        const detailsResult = await detailsResponse.json();

        if (detailsResponse.ok) {
          setSubmissionDetails(detailsResult.data);
        }

        // Load submission history (mock data for now - would need backend implementation)
        setSubmissionHistory([
          {
            version: 1,
            submitted_at: detailsResult.data?.submitted_at || new Date().toISOString(),
            total_amount: detailsResult.data?.total_amount || 0,
            changes: ['Initial submission']
          }
        ]);
      }

    } catch (err: unknown) {
      console.error('Error loading status data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load submission status');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadStatusData();
  }, [token]);

  const handleRefresh = () => {
    loadStatusData(true);
  };

  const handleDeleteSubmission = async () => {
    if (!submissionDetails) return;

    try {
      setDeleting(true);

      const response = await fetch(`/api/public/rfq/submission/${submissionDetails.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to delete submission');
      }

      toast.success('Submission deleted successfully');
      setShowDeleteDialog(false);
      
      // Reload status data
      await loadStatusData();

    } catch (err: unknown) {
      console.error('Delete error:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to delete submission');
    } finally {
      setDeleting(false);
    }
  };

  const handleDownloadAttachment = async (filename: string, originalName: string) => {
    try {
      const response = await fetch(`/api/public/rfq/${token}/attachment/${filename}`);
      
      if (!response.ok) {
        throw new Error('Failed to download file');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = originalName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('File downloaded successfully');
    } catch (err: unknown) {
      console.error('Download error:', err);
      toast.error('Failed to download file');
    }
  };

  const getStatusColor = (rfqStatus: string, hasSubmission: boolean) => {
    if (rfqStatus === 'closed' || rfqStatus === 'cancelled') {
      return 'destructive';
    }
    if (hasSubmission) {
      return 'default';
    }
    return 'secondary';
  };

  const getStatusText = (status: SubmissionStatus) => {
    if (status.rfq_status === 'closed') {
      return 'RFQ Closed';
    }
    if (status.rfq_status === 'cancelled') {
      return 'RFQ Cancelled';
    }
    if (status.has_submission) {
      return 'Submitted';
    }
    if (status.can_submit) {
      return 'Pending Submission';
    }
    return 'Cannot Submit';
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const isDeadlinePassed = status ? new Date() > new Date(status.rfq_due_date) : false;

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-muted-foreground">Loading submission status...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertCircle className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!status) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          No submission status available
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card className="card-neumorphic">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <FileText className="w-5 h-5 text-primary" />
                <span>Submission Status</span>
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Badge variant={getStatusColor(status.rfq_status, status.has_submission)}>
                  {getStatusText(status)}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={refreshing}
                  className="btn-neumorphic"
                >
                  <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            </div>
            <CardDescription>
              Track your bid submission for: {status.rfq_title}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Building className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">
                  <strong>Vendor:</strong> {status.vendor_name}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">
                  <strong>Deadline:</strong> {new Date(status.rfq_due_date).toLocaleString()}
                </span>
              </div>
            </div>

            {/* Deadline Warning */}
            {isDeadlinePassed && (
              <Alert className="border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  The submission deadline has passed. No further modifications are allowed.
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            {!isDeadlinePassed && (
              <div className="flex flex-wrap gap-2 pt-4">
                {status.can_submit && !status.has_submission && showEditButton && (
                  <Button
                    onClick={onEditSubmission}
                    className="btn-neumorphic gradient-primary text-primary-foreground"
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Create Submission
                  </Button>
                )}

                {status.can_modify && status.has_submission && showEditButton && (
                  <Button
                    onClick={onEditSubmission}
                    className="btn-neumorphic"
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Modify Submission
                  </Button>
                )}

                {status.has_submission && status.can_modify && (
                  <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                    <DialogTrigger asChild>
                      <Button
                        variant="destructive"
                        className="btn-neumorphic"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete Submission
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Delete Submission</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to delete your submission? This action cannot be undone.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="flex justify-end space-x-2 pt-4">
                        <Button
                          variant="outline"
                          onClick={() => setShowDeleteDialog(false)}
                          disabled={deleting}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={handleDeleteSubmission}
                          disabled={deleting}
                        >
                          {deleting && <RefreshCw className="w-4 h-4 mr-2 animate-spin" />}
                          Delete
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Submission Details */}
      {submissionDetails && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="card-neumorphic">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span>Submission Details</span>
              </CardTitle>
              <CardDescription>
                Your current bid submission
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Summary */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-primary">
                    ${Number(submissionDetails.total_amount || 0).toFixed(2)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Total Bid Amount ({submissionDetails.currency})
                  </div>
                </div>
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-primary">
                    {submissionDetails.bid_items.length}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Items Quoted
                  </div>
                </div>
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-primary">
                    {submissionDetails.validity_period}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Days Valid
                  </div>
                </div>
              </div>

              <Separator />

              {/* Bid Items */}
              <div>
                <h4 className="font-medium mb-4">Quoted Items</h4>
                <div className="space-y-3">
                  {submissionDetails.bid_items.map((item, index) => (
                    <div key={item.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h5 className="font-medium">{item.item_name}</h5>
                          <p className="text-sm text-muted-foreground">
                            Quantity: {item.quantity}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">
                            ${Number(item.total_price || 0).toFixed(2)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            ${Number(item.unit_price || 0).toFixed(2)} per unit
                          </div>
                        </div>
                      </div>
                      {item.delivery_days && (
                        <p className="text-sm text-muted-foreground">
                          <Clock className="w-3 h-3 inline mr-1" />
                          Delivery: {item.delivery_days} days
                        </p>
                      )}
                      {item.notes && (
                        <p className="text-sm text-muted-foreground mt-2">
                          <strong>Notes:</strong> {item.notes}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Additional Information */}
              {(submissionDetails.payment_terms || submissionDetails.additional_notes) && (
                <>
                  <Separator />
                  <div className="space-y-3">
                    {submissionDetails.payment_terms && (
                      <div>
                        <h5 className="font-medium mb-1">Payment Terms</h5>
                        <p className="text-sm text-muted-foreground">
                          {submissionDetails.payment_terms}
                        </p>
                      </div>
                    )}
                    {submissionDetails.additional_notes && (
                      <div>
                        <h5 className="font-medium mb-1">Additional Notes</h5>
                        <p className="text-sm text-muted-foreground">
                          {submissionDetails.additional_notes}
                        </p>
                      </div>
                    )}
                  </div>
                </>
              )}

              {/* Attachments */}
              {submissionDetails.attachments && submissionDetails.attachments.length > 0 && (
                <>
                  <Separator />
                  <div>
                    <h4 className="font-medium mb-4">Attachments</h4>
                    <div className="space-y-2">
                      {submissionDetails.attachments.map((attachment, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm font-medium">{attachment.originalName}</p>
                              <p className="text-xs text-muted-foreground">
                                {formatFileSize(attachment.fileSize)} • 
                                Uploaded {new Date(attachment.uploadedAt).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDownloadAttachment(attachment.filename, attachment.originalName)}
                            className="btn-neumorphic"
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}

              {/* Submission Timeline */}
              <Separator />
              <div>
                <h4 className="font-medium mb-4 flex items-center space-x-2">
                  <History className="w-4 h-4" />
                  <span>Submission Timeline</span>
                </h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium">Submission Created</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(submissionDetails.submitted_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  {submissionDetails.updated_at !== submissionDetails.submitted_at && (
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div>
                        <p className="text-sm font-medium">Last Updated</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(submissionDetails.updated_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Contact Information */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="card-neumorphic">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="w-5 h-5 text-primary" />
              <span>Need Help?</span>
            </CardTitle>
            <CardDescription>
              Contact information for support and questions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Mail className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Email Support</p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-sm text-primary hover:underline"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Phone Support</p>
                  <a
                    href="tel:******-0123"
                    className="text-sm text-primary hover:underline"
                  >
                    +****************
                  </a>
                </div>
              </div>
            </div>
            
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                For questions about this specific RFQ, please reference the RFQ title: "{status.rfq_title}" 
                in your communication.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};