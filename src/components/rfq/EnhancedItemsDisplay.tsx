import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  Eye,
  ChevronDown,
  ChevronUp,
  FileText,
  Hash,
  DollarSign,
  Tag,
  Paperclip
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { RFQItem } from '@/services/api/rfq';
import { formatCurrency } from '@/types/rfq';

interface EnhancedItemsDisplayProps {
  items: RFQItem[];
  currency?: string;
  maxDisplayItems?: number;
  showExpandButton?: boolean;
  onShowAllItems?: () => void;
  compact?: boolean;
}

interface ItemsModalProps {
  items: RFQItem[];
  currency: string;
  isOpen: boolean;
  onClose: () => void;
}

const ItemCard: React.FC<{
  item: RFQItem;
  currency: string;
  compact?: boolean;
  index?: number;
}> = ({ item, currency, compact = false, index }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const hasSpecifications = item.specifications && Object.keys(item.specifications).length > 0;
  const hasAttachments = item.attachments && item.attachments.length > 0;
  const hasCustomFields = item.customFields && Object.keys(item.customFields).length > 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: (index || 0) * 0.1 }}
      className={`border rounded-lg ${compact ? 'p-3' : 'p-4'} bg-card hover:shadow-md transition-shadow`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-2">
            <Package className="w-4 h-4 text-primary flex-shrink-0" />
            <h4 className={`font-medium truncate ${compact ? 'text-sm' : 'text-base'}`}>
              {item.name}
            </h4>
            {item.category && (
              <Badge variant="secondary" className="text-xs">
                {item.category}
              </Badge>
            )}
          </div>

          {item.description && !compact && (
            <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
              {item.description}
            </p>
          )}

          <div className={`grid ${compact ? 'grid-cols-2' : 'grid-cols-3'} gap-3 text-sm`}>
            <div className="flex items-center gap-1">
              <Hash className="w-3 h-3 text-muted-foreground" />
              <span className="text-muted-foreground">Qty:</span>
              <span className="font-medium">
                {item.quantity} {item.unit && `${item.unit}`}
              </span>
            </div>

            {item.estimatedPrice && (
              <div className="flex items-center gap-1">
                <DollarSign className="w-3 h-3 text-muted-foreground" />
                <span className="text-muted-foreground">Est:</span>
                <span className="font-medium">
                  {formatCurrency(item.estimatedPrice, currency)}
                </span>
              </div>
            )}

            {!compact && (
              <div className="flex items-center gap-1">
                <Tag className="w-3 h-3 text-muted-foreground" />
                <span className="text-muted-foreground">ID:</span>
                <span className="font-mono text-xs">{item.id}</span>
              </div>
            )}
          </div>

          {!compact && (hasSpecifications || hasAttachments || hasCustomFields) && (
            <div className="mt-3 flex items-center gap-2">
              {hasSpecifications && (
                <Badge variant="outline" className="text-xs">
                  <FileText className="w-3 h-3 mr-1" />
                  Specs
                </Badge>
              )}
              {hasAttachments && (
                <Badge variant="outline" className="text-xs">
                  <Paperclip className="w-3 h-3 mr-1" />
                  {item.attachments!.length} file{item.attachments!.length !== 1 ? 's' : ''}
                </Badge>
              )}
              {hasCustomFields && (
                <Badge variant="outline" className="text-xs">
                  Custom Fields
                </Badge>
              )}
            </div>
          )}
        </div>

        {!compact && (hasSpecifications || hasAttachments || hasCustomFields) && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-2 flex-shrink-0"
          >
            {isExpanded ? (
              <ChevronUp className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </Button>
        )}
      </div>

      <AnimatePresence>
        {isExpanded && !compact && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="mt-4 pt-4 border-t"
          >
            {hasSpecifications && (
              <div className="mb-4">
                <h5 className="font-medium text-sm mb-2">Specifications</h5>
                <div className="space-y-1">
                  {Object.entries(item.specifications).map(([key, value]) => (
                    <div key={key} className="flex justify-between text-sm">
                      <span className="text-muted-foreground capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}:
                      </span>
                      <span className="font-medium">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {hasCustomFields && (
              <div className="mb-4">
                <h5 className="font-medium text-sm mb-2">Custom Fields</h5>
                <div className="space-y-1">
                  {Object.entries(item.customFields).map(([key, value]) => (
                    <div key={key} className="flex justify-between text-sm">
                      <span className="text-muted-foreground capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}:
                      </span>
                      <span className="font-medium">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {hasAttachments && (
              <div>
                <h5 className="font-medium text-sm mb-2">Attachments</h5>
                <div className="space-y-2">
                  {item.attachments!.map((attachment, idx) => (
                    <div key={idx} className="flex items-center gap-2 text-sm">
                      <Paperclip className="w-3 h-3 text-muted-foreground" />
                      <span className="flex-1 truncate">{attachment.originalName}</span>
                      <span className="text-muted-foreground text-xs">
                        {(attachment.fileSize / 1024).toFixed(1)} KB
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

const ItemsModal: React.FC<ItemsModalProps> = ({ items, currency, isOpen, onClose }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = Array.from(new Set(items.map(item => item.category).filter(Boolean)));

  const filteredItems = items.filter(item => {
    const matchesSearch = !searchTerm || 
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const totalEstimatedValue = items.reduce((sum, item) => 
    sum + (item.estimatedPrice ? item.estimatedPrice * item.quantity : 0), 0
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            All RFQ Items ({items.length})
          </DialogTitle>
          <DialogDescription>
            Complete list of items in this RFQ with detailed specifications
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search and Filter Controls */}
          <div className="flex gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border rounded-md text-sm"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-md text-sm"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="text-center">
              <div className="text-2xl font-bold">{filteredItems.length}</div>
              <div className="text-sm text-muted-foreground">Items</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {filteredItems.reduce((sum, item) => sum + item.quantity, 0)}
              </div>
              <div className="text-sm text-muted-foreground">Total Quantity</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {formatCurrency(totalEstimatedValue, currency)}
              </div>
              <div className="text-sm text-muted-foreground">Est. Value</div>
            </div>
          </div>

          <Separator />

          {/* Items List */}
          <ScrollArea className="h-96">
            <div className="space-y-4 pr-4">
              {filteredItems.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No items match your search criteria</p>
                </div>
              ) : (
                filteredItems.map((item, index) => (
                  <ItemCard
                    key={item.id}
                    item={item}
                    currency={currency}
                    index={index}
                  />
                ))
              )}
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const EnhancedItemsDisplay: React.FC<EnhancedItemsDisplayProps> = ({
  items,
  currency = 'USD',
  maxDisplayItems = 3,
  showExpandButton = true,
  onShowAllItems,
  compact = false
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const displayItems = items.slice(0, maxDisplayItems);
  const hasMoreItems = items.length > maxDisplayItems;

  const handleShowAllItems = () => {
    if (onShowAllItems) {
      onShowAllItems();
    } else {
      setIsModalOpen(true);
    }
  };

  if (items.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p>No items defined for this RFQ</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">Items ({items.length})</h4>
        {hasMoreItems && showExpandButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleShowAllItems}
            className="text-sm"
          >
            <Eye className="w-4 h-4 mr-2" />
            Show All {items.length} Items
          </Button>
        )}
      </div>

      <div className="space-y-3">
        {displayItems.map((item, index) => (
          <ItemCard
            key={item.id}
            item={item}
            currency={currency}
            compact={compact}
            index={index}
          />
        ))}
      </div>

      {hasMoreItems && !showExpandButton && (
        <div className="text-center py-4">
          <p className="text-sm text-muted-foreground">
            And {items.length - maxDisplayItems} more items...
          </p>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleShowAllItems}
            className="mt-2"
          >
            <Eye className="w-4 h-4 mr-2" />
            View All Items
          </Button>
        </div>
      )}

      <ItemsModal
        items={items}
        currency={currency}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
};

export default EnhancedItemsDisplay;