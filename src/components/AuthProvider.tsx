import React, { useEffect, useState } from 'react';
import { useAuth } from '../hooks/useAuth';

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const [initialAuthCheck, setInitialAuthCheck] = useState(false);

  useEffect(() => {
    // Mark initial auth check as complete after first render
    if (!isLoading) {
      setInitialAuthCheck(true);
    }
  }, [isLoading]);

  // Show loading only during initial auth check
  if (!initialAuthCheck) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="card-neumorphic p-8">
          <div className="flex items-center space-x-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="text-muted-foreground">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};