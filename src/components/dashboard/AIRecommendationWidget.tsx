import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Brain,
  TrendingUp,
  Star,
  ArrowRight,
  RefreshCw,
  Lightbulb,
  Target,
  Zap
} from 'lucide-react';
import { VendorRecommendation, RecommendationCriteria } from '@/services/ai/types';
import { AIService } from '@/services/ai/AIService';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';

interface AIRecommendationWidgetProps {
  className?: string;
  maxRecommendations?: number;
}

const AIRecommendationWidget: React.FC<AIRecommendationWidgetProps> = ({
  className = '',
  maxRecommendations = 3
}) => {
  const [recommendations, setRecommendations] = useState<VendorRecommendation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { toast } = useToast();

  const generateRecommendations = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Mock vendor data for demonstration
      const mockVendors = [
        {
          id: '1',
          name: 'TechCorp Solutions',
          category: 'IT Services',
          location: 'San Francisco, CA',
          performanceScore: 92,
          contractHistory: { totalContracts: 15, completedOnTime: 14 },
          complianceRecords: { score: 95 },
          costEffectiveness: { averageCost: 125000 },
          certifications: ['ISO 27001', 'SOC 2']
        },
        {
          id: '2',
          name: 'Global Services Ltd',
          category: 'Consulting',
          location: 'New York, NY',
          performanceScore: 88,
          contractHistory: { totalContracts: 22, completedOnTime: 20 },
          complianceRecords: { score: 90 },
          costEffectiveness: { averageCost: 95000 },
          certifications: ['ISO 9001', 'PMP']
        },
        {
          id: '3',
          name: 'Innovation Partners',
          category: 'Software Development',
          location: 'Austin, TX',
          performanceScore: 94,
          contractHistory: { totalContracts: 8, completedOnTime: 8 },
          complianceRecords: { score: 92 },
          costEffectiveness: { averageCost: 110000 },
          certifications: ['ISO 27001', 'CMMI Level 3']
        }
      ];

      const criteria: RecommendationCriteria = {
        category: undefined,
        maxBudget: 150000,
        location: undefined,
        minPerformanceScore: 85,
        requiredCertifications: []
      };

      const aiService = AIService.getInstance();
      const result = await aiService.generateVendorRecommendations(mockVendors, criteria);
      setRecommendations(result.slice(0, maxRecommendations));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate recommendations';
      setError(errorMessage);
      toast.error('Failed to generate AI recommendations', {
        description: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    generateRecommendations();
  }, []);

  const handleViewAll = () => {
    navigate('/vendors/list?view=ai-recommendations');
  };

  const handleViewVendor = (vendorId: string) => {
    navigate(`/vendors/view/${vendorId}`);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceBadgeVariant = (confidence: number): "default" | "secondary" | "destructive" | "outline" => {
    if (confidence >= 0.8) return 'default';
    if (confidence >= 0.6) return 'secondary';
    return 'outline';
  };

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            AI Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Lightbulb className="h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-sm text-muted-foreground text-center mb-4">{error}</p>
          <Button onClick={generateRecommendations} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            AI Recommendations
          </CardTitle>
          <Button onClick={generateRecommendations} variant="ghost" size="sm" disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <Brain className="h-8 w-8 text-primary animate-pulse mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Analyzing vendors...</p>
            </div>
          </div>
        ) : recommendations.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Target className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground text-center">
              No recommendations available
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {recommendations.map((recommendation, index) => (
              <motion.div
                key={recommendation.vendor.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-3 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                onClick={() => handleViewVendor(recommendation.vendor.id)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">{recommendation.vendor.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {recommendation.vendor.category}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2 mb-2">
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3 text-yellow-500" />
                        <span className="text-xs text-muted-foreground">
                          {Math.round(recommendation.score)}/100
                        </span>
                      </div>
                      <Badge 
                        variant={getConfidenceBadgeVariant(recommendation.confidence)}
                        className="text-xs"
                      >
                        {Math.round(recommendation.confidence * 100)}% confidence
                      </Badge>
                    </div>
                    <Progress 
                      value={recommendation.score} 
                      className="h-1.5 mb-2" 
                    />
                  </div>
                  <ArrowRight className="h-4 w-4 text-muted-foreground ml-2" />
                </div>
                <p className="text-xs text-muted-foreground line-clamp-2">
                  {recommendation.reasoning}
                </p>
              </motion.div>
            ))}
            
            <div className="pt-2 border-t">
              <Button 
                onClick={handleViewAll} 
                variant="ghost" 
                size="sm" 
                className="w-full justify-center"
              >
                <Zap className="h-4 w-4 mr-2" />
                View All AI Recommendations
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AIRecommendationWidget;