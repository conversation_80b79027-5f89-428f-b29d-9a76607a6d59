import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  History,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  BarChart3,
  Calendar
} from 'lucide-react';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';

interface RecommendationHistoryItem {
  id: string;
  vendorName: string;
  vendorId: string;
  recommendedAt: Date;
  status: 'applied' | 'rejected' | 'pending' | 'expired';
  confidence: number;
  outcome?: {
    performanceImprovement?: number;
    costSavings?: number;
    satisfactionScore?: number;
  };
  category: string;
  reasoning: string;
}

interface RecommendationHistoryWidgetProps {
  className?: string;
  maxItems?: number;
}

const RecommendationHistoryWidget: React.FC<RecommendationHistoryWidgetProps> = ({
  className = '',
  maxItems = 5
}) => {
  const [historyItems, setHistoryItems] = useState<RecommendationHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Mock data for demonstration
  const mockHistoryItems: RecommendationHistoryItem[] = [
    {
      id: '1',
      vendorName: 'TechCorp Solutions',
      vendorId: '1',
      recommendedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      status: 'applied',
      confidence: 0.92,
      outcome: {
        performanceImprovement: 15,
        costSavings: 25000,
        satisfactionScore: 4.8
      },
      category: 'IT Services',
      reasoning: 'High performance score and excellent compliance record'
    },
    {
      id: '2',
      vendorName: 'Global Services Ltd',
      vendorId: '2',
      recommendedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      status: 'rejected',
      confidence: 0.78,
      category: 'Consulting',
      reasoning: 'Good cost-effectiveness but location concerns'
    },
    {
      id: '3',
      vendorName: 'Innovation Partners',
      vendorId: '3',
      recommendedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
      status: 'pending',
      confidence: 0.85,
      category: 'Software Development',
      reasoning: 'Excellent track record in similar projects'
    },
    {
      id: '4',
      vendorName: 'Digital Solutions Inc',
      vendorId: '4',
      recommendedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 2 weeks ago
      status: 'applied',
      confidence: 0.81,
      outcome: {
        performanceImprovement: 8,
        costSavings: 12000,
        satisfactionScore: 4.2
      },
      category: 'Cloud Services',
      reasoning: 'Strong cloud expertise and competitive pricing'
    },
    {
      id: '5',
      vendorName: 'Enterprise Systems',
      vendorId: '5',
      recommendedAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000), // 3 weeks ago
      status: 'expired',
      confidence: 0.73,
      category: 'Hardware',
      reasoning: 'Good hardware solutions but response time concerns'
    }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setHistoryItems(mockHistoryItems.slice(0, maxItems));
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [maxItems]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'applied':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'expired':
        return <XCircle className="h-4 w-4 text-gray-400" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'applied':
        return 'default';
      case 'rejected':
        return 'destructive';
      case 'pending':
        return 'secondary';
      case 'expired':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const handleViewVendor = (vendorId: string) => {
    navigate(`/vendors/view/${vendorId}`);
  };

  const handleViewAllHistory = () => {
    navigate('/analytics/ai-insights?tab=history');
  };

  const appliedRecommendations = historyItems.filter(item => item.status === 'applied');
  const totalCostSavings = appliedRecommendations.reduce((sum, item) => 
    sum + (item.outcome?.costSavings || 0), 0
  );
  const avgPerformanceImprovement = appliedRecommendations.length > 0 
    ? appliedRecommendations.reduce((sum, item) => 
        sum + (item.outcome?.performanceImprovement || 0), 0
      ) / appliedRecommendations.length
    : 0;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5 text-primary" />
            Recommendation History
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <BarChart3 className="h-8 w-8 text-primary animate-pulse mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading history...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5 text-primary" />
            Recommendation History
          </CardTitle>
          <Button onClick={handleViewAllHistory} variant="ghost" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            View All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="recent" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="recent">Recent</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
          </TabsList>
          
          <TabsContent value="recent" className="space-y-3">
            {historyItems.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8">
                <History className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground text-center">
                  No recommendation history available
                </p>
              </div>
            ) : (
              historyItems.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-3 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                  onClick={() => handleViewVendor(item.vendorId)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">{item.vendorName}</span>
                        <Badge variant="outline" className="text-xs">
                          {item.category}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 mb-1">
                        {getStatusIcon(item.status)}
                        <Badge 
                          variant={getStatusBadgeVariant(item.status)}
                          className="text-xs capitalize"
                        >
                          {item.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {Math.round(item.confidence * 100)}% confidence
                        </span>
                      </div>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        {format(item.recommendedAt, 'MMM d, yyyy')}
                      </div>
                    </div>
                  </div>
                  
                  {item.outcome && (
                    <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-xs">
                      <div className="flex items-center gap-4">
                        {item.outcome.performanceImprovement && (
                          <div className="flex items-center gap-1">
                            <TrendingUp className="h-3 w-3 text-green-600" />
                            <span>+{item.outcome.performanceImprovement}% performance</span>
                          </div>
                        )}
                        {item.outcome.costSavings && (
                          <div className="flex items-center gap-1">
                            <TrendingDown className="h-3 w-3 text-green-600" />
                            <span>${item.outcome.costSavings.toLocaleString()} saved</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </motion.div>
              ))
            )}
          </TabsContent>
          
          <TabsContent value="insights" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-center">
                <div className="text-lg font-bold text-green-700">
                  ${totalCostSavings.toLocaleString()}
                </div>
                <div className="text-xs text-green-600">Total Savings</div>
              </div>
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg text-center">
                <div className="text-lg font-bold text-blue-700">
                  +{Math.round(avgPerformanceImprovement)}%
                </div>
                <div className="text-xs text-blue-600">Avg Performance</div>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Applied Recommendations</span>
                <span className="font-medium">
                  {appliedRecommendations.length}/{historyItems.length}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Success Rate</span>
                <span className="font-medium text-green-600">
                  {historyItems.length > 0 
                    ? Math.round((appliedRecommendations.length / historyItems.length) * 100)
                    : 0
                  }%
                </span>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default RecommendationHistoryWidget;