import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
// Note: react-beautiful-dnd would need to be installed for drag and drop functionality
// For now, we'll implement a basic kanban without drag and drop
// import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import {
  DollarSign,
  Calendar,
  TrendingUp,
  Building2,
  User,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Plus,
  Filter,
  Search
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Opportunity,
  OpportunityStage,
  OpportunityType,
  OpportunityLeadSource,
  OpportunityStatus,
  OPPORTUNITY_STAGES,
  OPPORTUNITY_TYPES,
  OPPORTUNITY_LEAD_SOURCES,
  OPPORTUNITY_STATUSES
} from '../../types/opportunity';

interface OpportunityKanbanProps {
  opportunities: Opportunity[];
  onOpportunityMove?: (opportunityId: number, newStage: OpportunityStage) => void;
  onOpportunityEdit?: (opportunity: Opportunity) => void;
  onOpportunityDelete?: (opportunityId: number) => void;
  onOpportunityView?: (opportunity: Opportunity) => void;
  onOpportunityCreate?: (stage: OpportunityStage) => void;
  onSearch?: (searchTerm: string) => void;
  onFilter?: (filters: {
    type?: string;
    leadSource?: string;
    status?: string;
  }) => void;
  isLoading?: boolean;
  showActions?: boolean;
  showFilters?: boolean;
  accountId?: number;
}

interface KanbanColumn {
  id: OpportunityStage;
  title: string;
  color: string;
  opportunities: Opportunity[];
}

export const OpportunityKanban: React.FC<OpportunityKanbanProps> = ({
  opportunities,
  onOpportunityMove,
  onOpportunityEdit,
  onOpportunityDelete,
  onOpportunityView,
  onOpportunityCreate,
  onSearch,
  onFilter,
  isLoading = false,
  showActions = true,
  showFilters = true,
  accountId
}) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    type: '',
    leadSource: '',
    status: ''
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Define kanban columns
  const columns: KanbanColumn[] = [
    {
      id: OpportunityStage.PROSPECTING,
      title: 'Prospecting',
      color: 'border-gray-200 bg-gray-50',
      opportunities: []
    },
    {
      id: OpportunityStage.QUALIFICATION,
      title: 'Qualification',
      color: 'border-blue-200 bg-blue-50',
      opportunities: []
    },
    {
      id: OpportunityStage.NEEDS_ANALYSIS,
      title: 'Needs Analysis',
      color: 'border-indigo-200 bg-indigo-50',
      opportunities: []
    },
    {
      id: OpportunityStage.VALUE_PROPOSITION,
      title: 'Value Proposition',
      color: 'border-purple-200 bg-purple-50',
      opportunities: []
    },
    {
      id: OpportunityStage.ID_DECISION_MAKERS,
      title: 'Decision Makers',
      color: 'border-pink-200 bg-pink-50',
      opportunities: []
    },
    {
      id: OpportunityStage.PERCEPTION_ANALYSIS,
      title: 'Perception Analysis',
      color: 'border-orange-200 bg-orange-50',
      opportunities: []
    },
    {
      id: OpportunityStage.PROPOSAL_PRICE_QUOTE,
      title: 'Proposal/Quote',
      color: 'border-yellow-200 bg-yellow-50',
      opportunities: []
    },
    {
      id: OpportunityStage.NEGOTIATION_REVIEW,
      title: 'Negotiation',
      color: 'border-amber-200 bg-amber-50',
      opportunities: []
    },
    {
      id: OpportunityStage.CLOSED_WON,
      title: 'Closed Won',
      color: 'border-green-200 bg-green-50',
      opportunities: []
    },
    {
      id: OpportunityStage.CLOSED_LOST,
      title: 'Closed Lost',
      color: 'border-red-200 bg-red-50',
      opportunities: []
    }
  ];

  // Group opportunities by stage
  const groupedOpportunities = React.useMemo(() => {
    if (!opportunities || !Array.isArray(opportunities)) {
      return columns.map(column => ({
        ...column,
        opportunities: []
      }));
    }

    const filtered = opportunities.filter(opp => {
      const matchesSearch = !searchTerm || 
        opp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        opp.account_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        opp.description?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesType = !filters.type || opp.type === filters.type;
      const matchesLeadSource = !filters.leadSource || opp.lead_source === filters.leadSource;
      const matchesStatus = !filters.status || opp.status === filters.status;
      
      return matchesSearch && matchesType && matchesLeadSource && matchesStatus;
    });

    const grouped = columns.map(column => ({
      ...column,
      opportunities: filtered.filter(opp => opp.stage === column.id)
    }));

    return grouped;
  }, [opportunities, searchTerm, filters]);

  const handleDragEnd = useCallback((opportunityId: number, newStage: OpportunityStage) => {
    if (onOpportunityMove) {
      onOpportunityMove(opportunityId, newStage);
    }
  }, [onOpportunityMove]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (date?: Date | string) => {
    if (!date) return '-';
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getInitials = (name?: string) => {
    if (!name) return 'U';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const getTotalValue = (opportunities: Opportunity[]) => {
    return opportunities.reduce((sum, opp) => sum + (opp.amount || 0), 0);
  };

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Search & Filter
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                {showAdvancedFilters ? 'Hide' : 'Show'} Filters
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Basic Search */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search opportunities by name, description, or account..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>

            {/* Advanced Filters */}
            {showAdvancedFilters && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Select
                  value={filters.type}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Types</SelectItem>
                    {OPPORTUNITY_TYPES.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={filters.leadSource}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, leadSource: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by lead source" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Lead Sources</SelectItem>
                    {OPPORTUNITY_LEAD_SOURCES.map((source) => (
                      <SelectItem key={source} value={source}>
                        {source}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={filters.status}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Statuses</SelectItem>
                    {OPPORTUNITY_STATUSES.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Kanban Board */}
      <div className="flex gap-6 overflow-x-auto pb-6">
        {isLoading ? (
          <div className="flex items-center justify-center w-full py-12">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Loading opportunities...</span>
            </div>
          </div>
        ) : (
          groupedOpportunities.map((column) => (
          <div key={column.id} className="flex-shrink-0 w-80">
            <Card className={`h-full ${column.color} border-2`}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-sm font-medium">
                      {column.title}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-muted-foreground">
                        {column.opportunities.length} opportunities
                      </span>
                      <span className="text-xs font-medium">
                        {formatCurrency(getTotalValue(column.opportunities))}
                      </span>
                    </div>
                  </div>
                  {onOpportunityCreate && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onOpportunityCreate(column.id)}
                      className="h-8 w-8 p-0"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3 min-h-[200px]">
                  <AnimatePresence>
                    {column.opportunities.map((opportunity, index) => (
                      <motion.div
                        key={opportunity.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="cursor-move"
                      >
                                  <Card className="bg-white shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                                    <CardContent className="p-4">
                                      <div className="space-y-3">
                                        {/* Header */}
                                        <div className="flex items-start justify-between">
                                          <div className="flex-1 min-w-0">
                                            <h4 
                                              className="font-medium text-sm truncate text-blue-600 hover:text-blue-800 cursor-pointer"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                navigate(`/opportunities/${opportunity.id}`);
                                              }}
                                            >
                                              {opportunity.name}
                                            </h4>
                                            {opportunity.account_name && (
                                              <div className="flex items-center gap-1 mt-1">
                                                <Building2 className="h-3 w-3 text-muted-foreground" />
                                                <span 
                                                  className="text-xs text-blue-600 hover:text-blue-800 cursor-pointer truncate"
                                                  onClick={(e) => {
                                                    e.stopPropagation();
                                                    if (opportunity.account_id) {
                                                      navigate(`/accounts/${opportunity.account_id}`);
                                                    }
                                                  }}
                                                >
                                                  {opportunity.account_name}
                                                </span>
                                              </div>
                                            )}
                                          </div>
                                          {showActions && (
                                            <DropdownMenu>
                                              <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" className="h-6 w-6 p-0">
                                                  <MoreHorizontal className="h-3 w-3" />
                                                </Button>
                                              </DropdownMenuTrigger>
                                              <DropdownMenuContent align="end">
                                                {onOpportunityView && (
                                                  <DropdownMenuItem onClick={() => onOpportunityView(opportunity)}>
                                                    <Eye className="mr-2 h-4 w-4" />
                                                    View
                                                  </DropdownMenuItem>
                                                )}
                                                {onOpportunityEdit && (
                                                  <DropdownMenuItem onClick={() => onOpportunityEdit(opportunity)}>
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit
                                                  </DropdownMenuItem>
                                                )}
                                                {onOpportunityDelete && (
                                                  <DropdownMenuItem 
                                                    onClick={() => onOpportunityDelete(opportunity.id)}
                                                    className="text-destructive"
                                                  >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Delete
                                                  </DropdownMenuItem>
                                                )}
                                              </DropdownMenuContent>
                                            </DropdownMenu>
                                          )}
                                        </div>

                                        {/* Amount and Probability */}
                                        <div className="flex items-center justify-between">
                                          <div className="flex items-center gap-1">
                                            <DollarSign className="h-3 w-3 text-muted-foreground" />
                                            <span className="text-sm font-medium">
                                              {formatCurrency(opportunity.amount)}
                                            </span>
                                          </div>
                                          <div className="flex items-center gap-1">
                                            <TrendingUp className="h-3 w-3 text-muted-foreground" />
                                            <span className="text-xs text-muted-foreground">
                                              {opportunity.probability || 0}%
                                            </span>
                                          </div>
                                        </div>

                                        {/* Close Date */}
                                        {opportunity.close_date && (
                                          <div className="flex items-center gap-1">
                                            <Calendar className="h-3 w-3 text-muted-foreground" />
                                            <span className="text-xs text-muted-foreground">
                                              {formatDate(opportunity.close_date || opportunity.closeDate)}
                                            </span>
                                          </div>
                                        )}

                                        {/* Type Badge */}
                                        {opportunity.type && (
                                          <Badge variant="secondary" className="text-xs">
                                            {opportunity.type}
                                          </Badge>
                                        )}

                                        {/* Owner */}
                                        {opportunity.owner_email && (
                                          <div className="flex items-center gap-2">
                                            <Avatar className="h-6 w-6">
                                              <AvatarImage src="" />
                                              <AvatarFallback className="text-xs">
                                                {getInitials(opportunity.owner_email)}
                                              </AvatarFallback>
                                            </Avatar>
                                            <span className="text-xs text-muted-foreground truncate">
                                              {opportunity.owner_email}
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                    </CardContent>
                                  </Card>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </CardContent>
            </Card>
          </div>
          ))
        )}
      </div>
    </div>
  );
};