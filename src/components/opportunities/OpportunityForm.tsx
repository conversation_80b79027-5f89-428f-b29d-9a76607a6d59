import React, { useEffect, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { motion } from 'framer-motion';
import { 
  Target, 
  DollarSign, 
  Calendar, 
  Building2,
  Users,
  TrendingUp,
  Package,
  Plus,
  Trash2,
  Loader2,
  User
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  opportunityCreateSchema, 
  opportunityUpdateSchema,
  getDefaultProbabilityForStage,
  getDefaultForecastCategoryForStage
} from '../../lib/validation/opportunityValidation';
import { 
  Opportunity, 
  OpportunityCreateRequest, 
  OpportunityUpdateRequest,
  OpportunityStage,
  OpportunityType,
  OpportunityLeadSource,
  ForecastCategory,
  OPPORTUNITY_STAGES,
  OPPORTUNITY_TYPES,
  OPPORTUNITY_LEAD_SOURCES,
  FORECAST_CATEGORIES,
  CONTACT_ROLES,
  ContactRoleType
} from '../../types/opportunity';
import { Account } from '../../types/account';
import { Contact } from '../../types/contact';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';

interface OpportunityFormProps {
  initialData?: Partial<Opportunity>;
  onSubmit: (data: OpportunityCreateRequest | OpportunityUpdateRequest) => Promise<void>;
  isLoading?: boolean;
  mode: 'create' | 'edit';
  accountId?: number;
  availableAccounts?: Account[];
  availableContacts?: Contact[];
  availableUsers?: { id: number; email: string; name?: string }[];
  onCancel?: () => void;
  onAccountSearch?: (query: string) => void;
  onUserSearch?: (query: string) => void;
  accountSearchLoading?: boolean;
  userSearchLoading?: boolean;
}

export const OpportunityForm: React.FC<OpportunityFormProps> = ({
  initialData,
  onSubmit,
  isLoading = false,
  mode,
  accountId,
  availableAccounts = [],
  availableContacts = [],
  availableUsers = [],
  onAccountSearch,
  onUserSearch,
  accountSearchLoading = false,
  userSearchLoading = false
}) => {
  const [selectedAccountContacts, setSelectedAccountContacts] = useState<Contact[]>([]);

  const validationSchema = mode === 'create' ? opportunityCreateSchema : opportunityUpdateSchema;

  const form = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      account_id: accountId || initialData?.account_id || initialData?.accountId || undefined,
      name: initialData?.name || '',
      stage: initialData?.stage || OpportunityStage.PROSPECTING,
      amount: initialData?.amount || undefined,
      probability: initialData?.probability || 10,
      close_date: initialData?.close_date || initialData?.closeDate || undefined,
      type: initialData?.type || undefined,
      lead_source: initialData?.lead_source || undefined,
      next_step: initialData?.next_step || '',
      description: initialData?.description || '',
      forecast_category: initialData?.forecast_category || ForecastCategory.PIPELINE,
      campaign_id: initialData?.campaign_id || undefined,
      owner_id: initialData?.owner_id || initialData?.ownerId || undefined,
      contact_roles: initialData?.contact_roles || initialData?.contactRoles || [],
      line_items: initialData?.line_items || initialData?.lineItems || [],
      custom_fields: initialData?.custom_fields || {}
    }
  });

  const { fields: contactRoleFields, append: appendContactRole, remove: removeContactRole } = useFieldArray({
    control: form.control,
    name: 'contact_roles'
  });

  const { fields: lineItemFields, append: appendLineItem, remove: removeLineItem } = useFieldArray({
    control: form.control,
    name: 'line_items'
  });

  const watchedAccountId = form.watch('account_id');
  const watchedStage = form.watch('stage');
  const watchedLineItems = form.watch('line_items');

  // Update contacts when account changes
  useEffect(() => {
    if (watchedAccountId) {
      const accountContacts = availableContacts.filter(
        contact => contact.account_id === watchedAccountId || contact.accountId === watchedAccountId
      );
      setSelectedAccountContacts(accountContacts);
    } else {
      setSelectedAccountContacts([]);
    }
  }, [watchedAccountId, availableContacts]);

  // Update probability and forecast category when stage changes
  useEffect(() => {
    if (watchedStage) {
      const defaultProbability = getDefaultProbabilityForStage(watchedStage);
      const defaultForecastCategory = getDefaultForecastCategoryForStage(watchedStage);
      
      form.setValue('probability', defaultProbability);
      form.setValue('forecast_category', defaultForecastCategory);
    }
  }, [watchedStage, form]);

  // Calculate total amount from line items
  useEffect(() => {
    if (watchedLineItems && watchedLineItems.length > 0) {
      const total = watchedLineItems.reduce((sum, item) => {
        return sum + (item?.total_price || 0);
      }, 0);
      form.setValue('amount', total);
    }
  }, [watchedLineItems, form]);

  const handleSubmit = async (data: OpportunityCreateRequest | OpportunityUpdateRequest) => {
    try {
      // Transform data to match backend expectations
      const transformedData = {
        ...data,
        // Transform camelCase to snake_case for backend
        stage_name: data.stage,
        close_date: data.close_date,
        lead_source: data.lead_source,
        forecast_category: data.forecast_category,
        next_step: data.next_step,
        owner_id: data.owner_id,
        contact_roles: data.contact_roles,
        line_items: data.line_items,
        custom_fields: data.custom_fields || {}
      };
      
      await onSubmit(transformedData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const addContactRole = () => {
    appendContactRole({
      contact_id: 0,
      role: 'Decision Maker',
      is_primary: false
    });
  };

  const addLineItem = () => {
    appendLineItem({
      product_name: '',
      description: '',
      quantity: 1,
      unit_price: 0,
      total_price: 0,
      discount_percent: 0
    });
  };

  const calculateLineItemTotal = (index: number) => {
    const quantity = form.watch(`line_items.${index}.quantity`) || 0;
    const unitPrice = form.watch(`line_items.${index}.unit_price`) || 0;
    const discountPercent = form.watch(`line_items.${index}.discount_percent`) || 0;
    
    const subtotal = quantity * unitPrice;
    const discount = subtotal * (discountPercent / 100);
    const total = subtotal - discount;
    
    form.setValue(`line_items.${index}.total_price`, total);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Account Selection (if not pre-selected) */}
            {!accountId && (
              <FormField
                control={form.control}
                name="account_id"
                render={({ field }) => {
                  const accountOptions: SearchableSelectOption[] = availableAccounts.map(account => ({
                    value: account.id.toString(),
                    label: account.name,
                    subtitle: account.industry || account.type
                  }));

                  return (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        Account *
                      </FormLabel>
                      <FormControl>
                        <SearchableSelect
                          options={accountOptions}
                          value={field.value?.toString()}
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          onSearch={onAccountSearch}
                          placeholder="Search and select an account"
                          searchPlaceholder="Search accounts..."
                          emptyText="No accounts found"
                          loading={accountSearchLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Opportunity Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2.5">Opportunity Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter opportunity name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Stage */}
              <FormField
                control={form.control}
                name="stage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2">
                      <TrendingUp className="h-4 w-4" />
                      Stage *
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select stage" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {OPPORTUNITY_STAGES.map((stage) => (
                          <SelectItem key={stage} value={stage}>
                            {stage}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Amount */}
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2">
                      <DollarSign className="h-4 w-4" />
                      Amount
                    </FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.01"
                        placeholder="0.00" 
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Probability */}
              <FormField
                control={form.control}
                name="probability"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2.5">Probability (%)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="0"
                        max="100"
                        placeholder="0" 
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Close Date */}
              <FormField
                control={form.control}
                name="close_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2">
                      <Calendar className="h-4 w-4" />
                      Close Date
                    </FormLabel>
                    <FormControl>
                      <Input 
                        type="date" 
                        {...field}
                        value={field.value ? new Date(field.value).toISOString().split('T')[0] : ''}
                        onChange={(e) => field.onChange(e.target.value ? new Date(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Type */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2.5">Type</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {OPPORTUNITY_TYPES.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Lead Source */}
              <FormField
                control={form.control}
                name="lead_source"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lead Source</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select lead source" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {OPPORTUNITY_LEAD_SOURCES.map((source) => (
                          <SelectItem key={source} value={source}>
                            {source}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Forecast Category */}
              <FormField
                control={form.control}
                name="forecast_category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Forecast Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select forecast category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {FORECAST_CATEGORIES.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Owner */}
              <FormField
                control={form.control}
                name="owner_id"
                render={({ field }) => {
                  const userOptions: SearchableSelectOption[] = availableUsers.map(user => ({
                    value: user.id.toString(),
                    label: user.name || user.email,
                    subtitle: user.name ? user.email : undefined
                  }));

                  return (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Owner
                      </FormLabel>
                      <FormControl>
                        <SearchableSelect
                          options={userOptions}
                          value={field.value?.toString()}
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          onSearch={onUserSearch}
                          placeholder="Search and select an owner"
                          searchPlaceholder="Search users..."
                          emptyText="No users found"
                          loading={userSearchLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>

            {/* Next Step */}
            <FormField
              control={form.control}
              name="next_step"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Next Step</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter next step" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Enter opportunity description"
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Contact Roles */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Contact Roles
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {contactRoleFields.map((field, index) => (
              <div key={field.id} className="flex items-end gap-4 p-4 border rounded-lg">
                <FormField
                  control={form.control}
                  name={`contact_roles.${index}.contact_id`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Contact</FormLabel>
                      <Select 
                        onValueChange={(value) => field.onChange(parseInt(value))} 
                        value={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select contact" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {selectedAccountContacts.map((contact) => (
                            <SelectItem key={contact.id} value={contact.id.toString()}>
                              {contact.first_name || contact.firstName} {contact.last_name || contact.lastName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`contact_roles.${index}.role`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Role</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {CONTACT_ROLES.map((role) => (
                            <SelectItem key={role} value={role}>
                              {role}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`contact_roles.${index}.is_primary`}
                  render={({ field }) => (
                    <FormItem className="flex items-center space-x-2">
                      <FormLabel>Primary</FormLabel>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeContactRole(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <Button
              type="button"
              variant="outline"
              onClick={addContactRole}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Contact Role
            </Button>
          </CardContent>
        </Card>

        {/* Line Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Line Items
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {lineItemFields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-1 md:grid-cols-6 gap-4 p-4 border rounded-lg">
                <FormField
                  control={form.control}
                  name={`line_items.${index}.product_name`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Product Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Product name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`line_items.${index}.quantity`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quantity</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="1"
                          {...field}
                          onChange={(e) => {
                            field.onChange(parseInt(e.target.value) || 1);
                            setTimeout(() => calculateLineItemTotal(index), 0);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`line_items.${index}.unit_price`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unit Price</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="0.01"
                          min="0"
                          {...field}
                          onChange={(e) => {
                            field.onChange(parseFloat(e.target.value) || 0);
                            setTimeout(() => calculateLineItemTotal(index), 0);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`line_items.${index}.discount_percent`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Discount %</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => {
                            field.onChange(parseFloat(e.target.value) || 0);
                            setTimeout(() => calculateLineItemTotal(index), 0);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`line_items.${index}.total_price`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Total</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="0.01"
                          readOnly
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex items-end">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeLineItem(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
            
            <Button
              type="button"
              variant="outline"
              onClick={addLineItem}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Line Item
            </Button>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {mode === 'create' ? 'Create Opportunity' : 'Update Opportunity'}
          </Button>
        </div>
      </form>
    </Form>
  );
};