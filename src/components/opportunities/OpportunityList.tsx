import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  DollarSign,
  Calendar,
  TrendingUp,
  Building2,
  User,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Opportunity,
  PaginatedOpportunities,
  OpportunitySearchFilters,
  OpportunityStage,
  OpportunityType,
  OpportunityLeadSource,
  ForecastCategory,
  OpportunityStatus,
  OPPORTUNITY_STAGES,
  OPPORTUNITY_TYPES,
  OPPORTUNITY_LEAD_SOURCES,
  FORECAST_CATEGORIES,
  OPPORTUNITY_STATUSES
} from '../../types/opportunity';

interface OpportunityListProps {
  opportunities: PaginatedOpportunities;
  onOpportunitySelect?: (opportunity: Opportunity) => void;
  onOpportunityEdit?: (opportunity: Opportunity) => void;
  onOpportunityDelete?: (opportunityId: number) => void;
  onOpportunityView?: (opportunity: Opportunity) => void;
  onSearch?: (filters: OpportunitySearchFilters) => void;
  onExport?: () => void;
  onBulkAction?: (action: string, opportunityIds: number[]) => void;
  isLoading?: boolean;
  showActions?: boolean;
  showBulkActions?: boolean;
  showFilters?: boolean;
  accountId?: number;
}

export const OpportunityList: React.FC<OpportunityListProps> = ({
  opportunities,
  onOpportunitySelect,
  onOpportunityEdit,
  onOpportunityDelete,
  onOpportunityView,
  onSearch,
  onExport,
  onBulkAction,
  isLoading = false,
  showActions = true,
  showBulkActions = true,
  showFilters = true,
  accountId
}) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOpportunities, setSelectedOpportunities] = useState<number[]>([]);
  const [filters, setFilters] = useState<OpportunitySearchFilters>({
    account_id: accountId
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Handle search
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (onSearch) {
        onSearch({
          ...filters,
          search: searchTerm || undefined
        });
      }
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters, onSearch]);

  const handleSelectAll = (checked: boolean) => {
    if (checked && opportunities?.opportunities) {
      setSelectedOpportunities(opportunities.opportunities.map(opp => opp.id));
    } else {
      setSelectedOpportunities([]);
    }
  };

  const handleSelectOpportunity = (opportunityId: number, checked: boolean) => {
    if (checked) {
      setSelectedOpportunities(prev => [...prev, opportunityId]);
    } else {
      setSelectedOpportunities(prev => prev.filter(id => id !== opportunityId));
    }
  };

  const handleBulkAction = (action: string) => {
    if (onBulkAction && selectedOpportunities.length > 0) {
      onBulkAction(action, selectedOpportunities);
      setSelectedOpportunities([]);
    }
  };

  const getStageColor = (stage: OpportunityStage) => {
    const colors: Record<OpportunityStage, string> = {
      [OpportunityStage.PROSPECTING]: 'bg-gray-100 text-gray-800',
      [OpportunityStage.QUALIFICATION]: 'bg-blue-100 text-blue-800',
      [OpportunityStage.NEEDS_ANALYSIS]: 'bg-indigo-100 text-indigo-800',
      [OpportunityStage.VALUE_PROPOSITION]: 'bg-purple-100 text-purple-800',
      [OpportunityStage.ID_DECISION_MAKERS]: 'bg-pink-100 text-pink-800',
      [OpportunityStage.PERCEPTION_ANALYSIS]: 'bg-orange-100 text-orange-800',
      [OpportunityStage.PROPOSAL_PRICE_QUOTE]: 'bg-yellow-100 text-yellow-800',
      [OpportunityStage.NEGOTIATION_REVIEW]: 'bg-amber-100 text-amber-800',
      [OpportunityStage.CLOSED_WON]: 'bg-green-100 text-green-800',
      [OpportunityStage.CLOSED_LOST]: 'bg-red-100 text-red-800'
    };
    return colors[stage] || 'bg-gray-100 text-gray-800';
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (date?: Date | string) => {
    if (!date) return '-';
    return new Date(date).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Search & Filter
              </span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  {showAdvancedFilters ? 'Hide' : 'Show'} Filters
                </Button>
                {onExport && (
                  <Button variant="outline" size="sm" onClick={onExport}>
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                )}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Basic Search */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search opportunities by name, description, or account..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>

            {/* Advanced Filters */}
            {showAdvancedFilters && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Select
                  value={filters.stage?.[0] || ''}
                  onValueChange={(value) => setFilters(prev => ({
                    ...prev,
                    stage: value ? [value as OpportunityStage] : undefined
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by stage" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Stages</SelectItem>
                    {OPPORTUNITY_STAGES.map((stage) => (
                      <SelectItem key={stage} value={stage}>
                        {stage}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={filters.type?.[0] || ''}
                  onValueChange={(value) => setFilters(prev => ({
                    ...prev,
                    type: value ? [value as OpportunityType] : undefined
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Types</SelectItem>
                    {OPPORTUNITY_TYPES.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={filters.lead_source?.[0] || ''}
                  onValueChange={(value) => setFilters(prev => ({
                    ...prev,
                    lead_source: value ? [value as OpportunityLeadSource] : undefined
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by lead source" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Lead Sources</SelectItem>
                    {OPPORTUNITY_LEAD_SOURCES.map((source) => (
                      <SelectItem key={source} value={source}>
                        {source}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={filters.status?.[0] || ''}
                  onValueChange={(value) => setFilters(prev => ({
                    ...prev,
                    status: value ? [value as OpportunityStatus] : undefined
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Statuses</SelectItem>
                    {OPPORTUNITY_STATUSES.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Bulk Actions */}
      {showBulkActions && selectedOpportunities.length > 0 && (
        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {selectedOpportunities.length} opportunity(ies) selected
              </span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('export')}
                >
                  Export Selected
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('delete')}
                >
                  Delete Selected
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Opportunities Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Opportunities ({opportunities?.pagination?.total || 0})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  {showBulkActions && (
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedOpportunities.length === (opportunities?.opportunities?.length || 0)}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                  )}
                  <TableHead>Name</TableHead>
                  <TableHead>Account</TableHead>
                  <TableHead>Stage</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Probability</TableHead>
                  <TableHead>Close Date</TableHead>
                  <TableHead>Owner</TableHead>
                  {showActions && <TableHead className="w-12">Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={showBulkActions ? 8 : 7} className="text-center py-8">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                        <span className="ml-2">Loading opportunities...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (opportunities?.opportunities || []).length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={showBulkActions ? 8 : 7} className="text-center py-8">
                      <div className="text-muted-foreground">
                        No opportunities found. Try adjusting your search filters.
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (opportunities?.opportunities || []).map((opportunity) => (
                  <TableRow
                    key={opportunity.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => onOpportunitySelect?.(opportunity)}
                  >
                    {showBulkActions && (
                      <TableCell onClick={(e) => e.stopPropagation()}>
                        <Checkbox
                          checked={selectedOpportunities.includes(opportunity.id)}
                          onCheckedChange={(checked) => 
                            handleSelectOpportunity(opportunity.id, checked as boolean)
                          }
                        />
                      </TableCell>
                    )}
                    <TableCell>
                      <div className="flex flex-col">
                        <span 
                          className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/opportunities/${opportunity.id}`);
                          }}
                        >
                          {opportunity.name}
                        </span>
                        {opportunity.type && (
                          <span className="text-sm text-muted-foreground">
                            {opportunity.type}
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span 
                          className="text-blue-600 hover:text-blue-800 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (opportunity.account_id) {
                              navigate(`/accounts/${opportunity.account_id}`);
                            }
                          }}
                        >
                          {opportunity.account_name || 'Unknown Account'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStageColor(opportunity.stage)}>
                        {opportunity.stage}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span>{formatCurrency(opportunity.amount)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        <span>{opportunity.probability || 0}%</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>{formatDate(opportunity.close_date || opportunity.closeDate)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span>{opportunity.owner_email || 'Unassigned'}</span>
                      </div>
                    </TableCell>
                    {showActions && (
                      <TableCell onClick={(e) => e.stopPropagation()}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {onOpportunityView && (
                              <DropdownMenuItem onClick={() => onOpportunityView(opportunity)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </DropdownMenuItem>
                            )}
                            {onOpportunityEdit && (
                              <DropdownMenuItem onClick={() => onOpportunityEdit(opportunity)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                            )}
                            {onOpportunityDelete && (
                              <DropdownMenuItem 
                                onClick={() => onOpportunityDelete(opportunity.id)}
                                className="text-destructive"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {(opportunities?.pagination?.totalPages || 0) > 1 && (
            <div className="flex items-center justify-between space-x-2 py-4">
              <div className="text-sm text-muted-foreground">
                Showing {((opportunities?.pagination?.page || 1) - 1) * (opportunities?.pagination?.limit || 10) + 1} to{' '}
                {Math.min((opportunities?.pagination?.page || 1) * (opportunities?.pagination?.limit || 10), opportunities?.pagination?.total || 0)} of{' '}
                {opportunities?.pagination?.total || 0} results
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newFilters = { ...filters };
                    onSearch?.(newFilters);
                  }}
                  disabled={(opportunities?.pagination?.page || 1) <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <div className="flex items-center gap-1">
                  <span className="text-sm">
                    Page {opportunities?.pagination?.page || 1} of {opportunities?.pagination?.totalPages || 1}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newFilters = { ...filters };
                    onSearch?.(newFilters);
                  }}
                  disabled={(opportunities?.pagination?.page || 1) >= (opportunities?.pagination?.totalPages || 1)}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};