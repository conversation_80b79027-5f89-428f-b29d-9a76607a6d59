import * as React from 'react';
import { useForm, use<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Input } from './ui/input';
import { Button } from './ui/button';
import { Checkbox } from './ui/checkbox';
import { Trash2, Plus } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { LineItem } from '@/store/slices/invoicesSlice';

const lineItemSchema = z.object({
  id: z.string(),
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().min(0, 'Quantity must be positive'),
  unitPrice: z.number().min(0, 'Price must be positive'),
  totalPrice: z.number().min(0),
  taxable: z.boolean(),
});

const formSchema = z.object({
  lineItems: z.array(lineItemSchema),
});

type FormValues = z.infer<typeof formSchema>;

export interface LineItemsTableProps {
  lineItems: LineItem[];
  onChange?: (items: LineItem[]) => void;
  mode: 'view' | 'edit';
  className?: string;
}

function LineItemsTable({ lineItems: initialItems, onChange, mode = 'view', className }: LineItemsTableProps) {
  const { control, watch, formState: { errors } } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: { lineItems: initialItems.map(item => ({ ...item, totalPrice: item.quantity * item.unitPrice })) },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'lineItems',
  });

  const items = watch('lineItems');
  // Remove: const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  React.useEffect(() => {
    if (onChange) onChange(items);
  }, [items, onChange]);

  const addItem = () => {
    append({ id: crypto.randomUUID(), description: '', quantity: 1, unitPrice: 0, totalPrice: 0, taxable: true });
  };
  // In the render for edit mode, after updating quantity or unitPrice, update totalPrice.
  // But since it's in onChange, perhaps calculate in useEffect or add a watcher.
  // For simplicity, calculate in the TableCell: ${((items[index]?.quantity || 0) * (items[index]?.unitPrice || 0)).toFixed(2)}
  // And in useEffect, before onChange, map to add totalPrice.
  React.useEffect(() => {
    if (onChange) onChange(items.map(item => ({ ...item, totalPrice: (item.quantity || 0) * (item.unitPrice || 0) })));
  }, [items, onChange]);
  // Remove the subtotal calculation if not needed, or adjust.
  const subtotal = items.reduce((sum, item) => sum + (item.totalPrice || 0), 0);
  // In view mode, use lineItems prop directly, assume it has totalPrice.
  if (mode === 'view') {
    return (
      <Table className={cn('w-full', className)}>
        <TableHeader>
          <TableRow>
            <TableHead>Description</TableHead>
            <TableHead>Quantity</TableHead>
            <TableHead>Unit Price</TableHead>
            <TableHead>Total</TableHead>
            <TableHead>Taxable</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {initialItems.map((item) => (
            <TableRow key={item.id}>
              <TableCell>{item.description}</TableCell>
              <TableCell>{item.quantity}</TableCell>
              <TableCell>${item.unitPrice.toFixed(2)}</TableCell>
              <TableCell>${item.totalPrice.toFixed(2)}</TableCell>
              <TableCell>{item.taxable ? 'Yes' : 'No'}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Description</TableHead>
            <TableHead>Quantity</TableHead>
            <TableHead>Unit Price</TableHead>
            <TableHead>Total</TableHead>
            <TableHead>Taxable</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <AnimatePresence>
            {fields.map((field, index) => (
              <motion.tr
                key={field.id}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                <TableCell>
                  <Controller
                    control={control}
                    name={`lineItems.${index}.description`}
                    render={({ field }) => <Input {...field} placeholder="Description" />}
                  />
                  {errors.lineItems?.[index]?.description && <p className="text-red-500 text-xs">{errors.lineItems?.[index]?.description?.message}</p>}
                </TableCell>
                <TableCell>
                  <Controller
                    control={control}
                    name={`lineItems.${index}.quantity`}
                    render={({ field }) => <Input type="number" {...field} onChange={e => field.onChange(parseFloat(e.target.value))} />}
                  />
                  {errors.lineItems?.[index]?.quantity && <p className="text-red-500 text-xs">{errors.lineItems?.[index]?.quantity?.message}</p>}
                </TableCell>
                <TableCell>
                  <Controller
                    control={control}
                    name={`lineItems.${index}.unitPrice`}
                    render={({ field }) => <Input type="number" {...field} onChange={e => field.onChange(parseFloat(e.target.value))} />}
                  />
                  {errors.lineItems?.[index]?.unitPrice && <p className="text-red-500 text-xs">{errors.lineItems?.[index]?.unitPrice?.message}</p>}
                </TableCell>
                <TableCell>${(items[index]?.quantity * items[index]?.unitPrice || 0).toFixed(2)}</TableCell>
                <TableCell>
                  <Controller
                    control={control}
                    name={`lineItems.${index}.taxable`}
                    render={({ field }) => <Checkbox checked={field.value} onCheckedChange={field.onChange} />}
                  />
                </TableCell>
                <TableCell>
                  <Button variant="ghost" size="sm" onClick={() => remove(index)}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </motion.tr>
            ))}
          </AnimatePresence>
        </TableBody>
      </Table>
      <Button onClick={addItem} variant="outline">
        <Plus className="mr-2 h-4 w-4" /> Add Line Item
      </Button>
      <div className="text-right font-semibold">Subtotal: ${subtotal.toFixed(2)}</div>
    </div>
  );
}

export { LineItemsTable };