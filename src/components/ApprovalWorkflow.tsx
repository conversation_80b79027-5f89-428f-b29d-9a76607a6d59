import * as React from 'react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  User, 
  MessageSquare,
  AlertTriangle,
  ArrowRight,
  Users
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { Badge } from './ui/badge';
import { cn } from '@/lib/utils';
import { ApprovalLevel } from '@/store/slices/invoicesSlice';

export interface ApprovalWorkflowProps {
  approvalLevels: ApprovalLevel[];
  currentLevel: number;
  canApprove?: boolean;
  canReject?: boolean;
  onApprove?: (comments?: string) => void;
  onReject?: (reason: string) => void;
  className?: string;
}

const getRoleIcon = (role: string) => {
  switch (role) {
    case 'manager':
      return User;
    case 'executive':
      return Users;
    case 'finance':
      return Users;
    default:
      return User;
  }
};

const getRoleColor = (role: string) => {
  switch (role) {
    case 'manager':
      return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-300';
    case 'executive':
      return 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900 dark:text-purple-300';
    case 'finance':
      return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900 dark:text-gray-300';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'approved':
      return CheckCircle;
    case 'rejected':
      return XCircle;
    case 'pending':
      return Clock;
    default:
      return AlertTriangle;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300';
    case 'rejected':
      return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-300';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-300';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900 dark:text-gray-300';
  }
};

export function ApprovalWorkflow({ 
  approvalLevels, 
  currentLevel, 
  canApprove = false, 
  canReject = false,
  onApprove,
  onReject,
  className 
}: ApprovalWorkflowProps) {
  const [showApprovalForm, setShowApprovalForm] = React.useState(false);
  const [showRejectionForm, setShowRejectionForm] = React.useState(false);
  const [comments, setComments] = React.useState('');
  const [rejectionReason, setRejectionReason] = React.useState('');

  const handleApprove = () => {
    if (onApprove) {
      onApprove(comments);
      setComments('');
      setShowApprovalForm(false);
    }
  };

  const handleReject = () => {
    if (onReject && rejectionReason.trim()) {
      onReject(rejectionReason);
      setRejectionReason('');
      setShowRejectionForm(false);
    }
  };

  if (!approvalLevels || approvalLevels.length === 0) {
    return (
      <Card className={cn('card-neumorphic', className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5" />
            Approval Workflow
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">No approval required</h3>
            <p className="text-muted-foreground">
              This invoice does not require approval workflow.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentLevelData = approvalLevels.find(level => level.level === currentLevel);
  const isCurrentUserApprover = canApprove && currentLevelData?.status === 'pending';

  return (
    <Card className={cn('card-neumorphic', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="w-5 h-5" />
          Approval Workflow
          <Badge variant="secondary" className="ml-2">
            Level {currentLevel} of {approvalLevels.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Approval Levels */}
          {approvalLevels.map((level, index) => {
            const RoleIcon = getRoleIcon(level.role);
            const StatusIcon = getStatusIcon(level.status);
            const isActive = level.level === currentLevel;
            const isCompleted = level.status === 'approved';
            const isRejected = level.status === 'rejected';
            
            return (
              <motion.div
                key={level.level}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`flex items-center gap-4 p-4 rounded-xl border transition-colors ${
                  isActive ? 'border-primary bg-primary/5' :
                  isCompleted ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950' :
                  isRejected ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950' :
                  'border-border'
                }`}
              >
                <div className={`p-2 rounded-lg ${
                  isCompleted ? 'bg-green-100 dark:bg-green-900' :
                  isRejected ? 'bg-red-100 dark:bg-red-900' :
                  isActive ? 'bg-primary/10' :
                  'bg-gray-100 dark:bg-gray-900'
                }`}>
                  <StatusIcon className={`w-5 h-5 ${
                    isCompleted ? 'text-green-600 dark:text-green-400' :
                    isRejected ? 'text-red-600 dark:text-red-400' :
                    isActive ? 'text-primary' :
                    'text-gray-600 dark:text-gray-400'
                  }`} />
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-foreground">
                      Level {level.level}
                    </span>
                    <Badge className={getRoleColor(level.role)}>
                      <RoleIcon className="w-3 h-3 mr-1" />
                      {level.role.charAt(0).toUpperCase() + level.role.slice(1)}
                    </Badge>
                    <Badge className={getStatusColor(level.status)}>
                      {level.status.charAt(0).toUpperCase() + level.status.slice(1)}
                    </Badge>
                  </div>
                  
                  <div className="text-sm text-muted-foreground">
                    {level.approvedBy && level.approvedAt && (
                      <div className="flex items-center gap-4">
                        <span>Approved by User ID: {level.approvedBy}</span>
                        <span>{format(new Date(level.approvedAt), 'MMM dd, yyyy HH:mm')}</span>
                      </div>
                    )}
                    
                    {level.comments && (
                      <div className="mt-2 p-2 bg-muted/50 rounded-lg">
                        <div className="flex items-center gap-1 mb-1">
                          <MessageSquare className="w-3 h-3" />
                          <span className="font-medium">Comments:</span>
                        </div>
                        <p>{level.comments}</p>
                      </div>
                    )}
                    
                    {level.status === 'pending' && isActive && (
                      <div className="mt-2 text-primary font-medium">
                        Awaiting approval...
                      </div>
                    )}
                  </div>
                </div>
                
                {index < approvalLevels.length - 1 && (
                  <ArrowRight className="w-4 h-4 text-muted-foreground" />
                )}
              </motion.div>
            );
          })}
        </div>
        
        {/* Action Buttons */}
        {isCurrentUserApprover && (
          <div className="mt-6 pt-4 border-t border-border">
            <div className="flex gap-2 mb-4">
              <Button
                onClick={() => setShowApprovalForm(true)}
                className="flex items-center gap-2"
              >
                <CheckCircle className="w-4 h-4" />
                Approve
              </Button>
              
              <Button
                variant="destructive"
                onClick={() => setShowRejectionForm(true)}
                className="flex items-center gap-2"
              >
                <XCircle className="w-4 h-4" />
                Reject
              </Button>
            </div>
            
            {/* Approval Form */}
            {showApprovalForm && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="space-y-4 p-4 bg-green-50 dark:bg-green-950 rounded-lg border border-green-200 dark:border-green-800"
              >
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Approval Comments (Optional)
                  </label>
                  <Textarea
                    value={comments}
                    onChange={(e) => setComments(e.target.value)}
                    placeholder="Add any comments about this approval..."
                    rows={3}
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={handleApprove}>
                    Confirm Approval
                  </Button>
                  <Button variant="outline" onClick={() => setShowApprovalForm(false)}>
                    Cancel
                  </Button>
                </div>
              </motion.div>
            )}
            
            {/* Rejection Form */}
            {showRejectionForm && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="space-y-4 p-4 bg-red-50 dark:bg-red-950 rounded-lg border border-red-200 dark:border-red-800"
              >
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Rejection Reason (Required)
                  </label>
                  <Textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    placeholder="Please provide a reason for rejection..."
                    rows={3}
                    required
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    variant="destructive" 
                    onClick={handleReject}
                    disabled={!rejectionReason.trim()}
                  >
                    Confirm Rejection
                  </Button>
                  <Button variant="outline" onClick={() => setShowRejectionForm(false)}>
                    Cancel
                  </Button>
                </div>
              </motion.div>
            )}
          </div>
        )}
        
        {/* Progress Summary */}
        <div className="mt-6 pt-4 border-t border-border">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">
                {approvalLevels.filter(l => l.status === 'approved').length}
              </div>
              <div className="text-xs text-muted-foreground">Approved</div>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-yellow-600">
                {approvalLevels.filter(l => l.status === 'pending').length}
              </div>
              <div className="text-xs text-muted-foreground">Pending</div>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-red-600">
                {approvalLevels.filter(l => l.status === 'rejected').length}
              </div>
              <div className="text-xs text-muted-foreground">Rejected</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}