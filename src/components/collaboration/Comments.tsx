import React, { useState, useEffect } from 'react';
import { MessageCircle, Send, Edit2, Trash2, Reply } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { useSocket } from '@/hooks/useSocket';
import { useTypingIndicator, TypingDisplay } from './TypingIndicator';

interface Comment {
  id: number;
  text: string;
  parent_comment_id?: number;
  created_at: string;
  updated_at: string;
  author_name: string;
  author_email: string;
}

interface CommentsProps {
  entityType: string;
  entityId: string;
}

export const Comments: React.FC<CommentsProps> = ({ entityType, entityId }) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [editingId, setEditingId] = useState<number | null>(null);
  const [editText, setEditText] = useState('');
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyText, setReplyText] = useState('');
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const socket = useSocket();
  const { typingText, handleTypingStart, handleTypingStop } = useTypingIndicator({
    entityType,
    entityId,
    currentUserId: 1 // TODO: Get from auth context
  });

  useEffect(() => {
    fetchComments();
  }, [entityType, entityId]);

  useEffect(() => {
    if (socket) {
      // Join the room for this entity
      socket.emit('join_room', `${entityType}_${entityId}`);

      // Listen for real-time comment events
      socket.on('new_comment', (comment: Comment) => {
        setComments(prev => [...prev, comment]);
      });

      socket.on('comment_updated', (updatedComment: { id: number; text: string; updatedAt: string }) => {
        setComments(prev => prev.map(comment => 
          comment.id === updatedComment.id 
            ? { ...comment, text: updatedComment.text, updated_at: updatedComment.updatedAt }
            : comment
        ));
      });

      socket.on('comment_deleted', (deletedComment: { id: number }) => {
        setComments(prev => prev.filter(comment => comment.id !== deletedComment.id));
      });

      return () => {
        socket.off('new_comment');
        socket.off('comment_updated');
        socket.off('comment_deleted');
        socket.emit('leave_room', `${entityType}_${entityId}`);
      };
    }
  }, [socket, entityType, entityId]);

  const fetchComments = async () => {
    try {
      const response = await fetch(`/api/collaboration/comments/${entityType}/${entityId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setComments(data);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load comments',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return;

    try {
      const response = await fetch('/api/collaboration/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          entityType,
          entityId,
          text: newComment
        })
      });

      if (response.ok) {
        setNewComment('');
        toast({
          title: 'Success',
          description: 'Comment added successfully'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add comment',
        variant: 'destructive'
      });
    }
  };

  const handleSubmitReply = async (parentId: number) => {
    if (!replyText.trim()) return;

    try {
      const response = await fetch('/api/collaboration/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          entityType,
          entityId,
          text: replyText,
          parentCommentId: parentId
        })
      });

      if (response.ok) {
        setReplyText('');
        setReplyingTo(null);
        toast({
          title: 'Success',
          description: 'Reply added successfully'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add reply',
        variant: 'destructive'
      });
    }
  };

  const handleEditComment = async (id: number) => {
    if (!editText.trim()) return;

    try {
      const response = await fetch(`/api/collaboration/comments/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ text: editText })
      });

      if (response.ok) {
        setEditingId(null);
        setEditText('');
        toast({
          title: 'Success',
          description: 'Comment updated successfully'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update comment',
        variant: 'destructive'
      });
    }
  };

  const handleDeleteComment = async (id: number) => {
    try {
      const response = await fetch(`/api/collaboration/comments/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Comment deleted successfully'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete comment',
        variant: 'destructive'
      });
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  interface CommentWithReplies extends Comment {
    replies: CommentWithReplies[];
  }

  const organizeComments = (comments: Comment[]): CommentWithReplies[] => {
    const commentMap = new Map<number, CommentWithReplies>();
    const rootComments: Comment[] = [];
    
    // First pass: create map and identify root comments
    comments.forEach(comment => {
      commentMap.set(comment.id, { ...comment, replies: [] });
      if (!comment.parent_comment_id) {
        rootComments.push(comment);
      }
    });
    
    // Second pass: organize replies
    comments.forEach(comment => {
      if (comment.parent_comment_id) {
        const parent = commentMap.get(comment.parent_comment_id);
        if (parent) {
          const childComment = commentMap.get(comment.id);
          if (childComment) {
            parent.replies.push(childComment);
          }
        }
      }
    });
    
    return rootComments.map(comment => commentMap.get(comment.id)!).filter(Boolean);
  };

  const renderComment = (comment: CommentWithReplies, isReply = false) => (
    <div key={comment.id} className={`${isReply ? 'ml-8 mt-2' : 'mb-4'}`}>
      <div className="flex space-x-3">
        <Avatar className="h-8 w-8">
          <AvatarFallback className="text-xs">
            {getInitials(comment.author_name)}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex justify-between items-start mb-1">
              <span className="font-medium text-sm">{comment.author_name}</span>
              <span className="text-xs text-gray-500">{formatDate(comment.created_at)}</span>
            </div>
            {editingId === comment.id ? (
              <div className="space-y-2">
                <Textarea
                  value={editText}
                  onChange={(e) => setEditText(e.target.value)}
                  className="min-h-[60px]"
                />
                <div className="flex space-x-2">
                  <Button size="sm" onClick={() => handleEditComment(comment.id)}>
                    Save
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => setEditingId(null)}>
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <p className="text-sm">{comment.text}</p>
            )}
          </div>
          <div className="flex space-x-2 mt-1">
            <Button
              size="sm"
              variant="ghost"
              className="h-6 px-2 text-xs"
              onClick={() => setReplyingTo(comment.id)}
            >
              <Reply className="h-3 w-3 mr-1" />
              Reply
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="h-6 px-2 text-xs"
              onClick={() => {
                setEditingId(comment.id);
                setEditText(comment.text);
              }}
            >
              <Edit2 className="h-3 w-3 mr-1" />
              Edit
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="h-6 px-2 text-xs text-red-600"
              onClick={() => handleDeleteComment(comment.id)}
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Delete
            </Button>
          </div>
          {replyingTo === comment.id && (
            <div className="mt-2 space-y-2">
              <Textarea
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                placeholder="Write a reply..."
                className="min-h-[60px]"
              />
              <div className="flex space-x-2">
                <Button size="sm" onClick={() => handleSubmitReply(comment.id)}>
                  Reply
                </Button>
                <Button size="sm" variant="outline" onClick={() => setReplyingTo(null)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
          {comment.replies && comment.replies.map((reply: CommentWithReplies) => renderComment(reply, true))}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return <div className="p-4">Loading comments...</div>;
  }

  const organizedComments = organizeComments(comments);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MessageCircle className="h-5 w-5" />
          <span>Comments ({comments.length})</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* New comment form */}
          <div className="space-y-2">
            <Textarea
              value={newComment}
              onChange={(e) => {
                setNewComment(e.target.value);
                if (e.target.value.trim()) {
                  handleTypingStart();
                } else {
                  handleTypingStop();
                }
              }}
              onBlur={handleTypingStop}
              placeholder="Add a comment..."
              className="min-h-[80px]"
            />
            <Button onClick={handleSubmitComment} disabled={!newComment.trim()}>
              <Send className="h-4 w-4 mr-2" />
              Post Comment
            </Button>
          </div>

          {/* Typing indicator */}
          <TypingDisplay typingText={typingText} />

          {/* Comments list */}
          <div className="space-y-4">
            {organizedComments.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No comments yet. Be the first to comment!</p>
            ) : (
              organizedComments.map(comment => renderComment(comment))
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};