import React, { useState } from 'react';
import { ThreadedComments } from './ThreadedComments';
import { PresenceIndicator } from './PresenceIndicator';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  MessageCircle,
  Users,
  Activity,
  Clock,
  ChevronRight,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface CollaborationPanelProps {
  entityType: string;
  entityId: string;
  currentUserId: string;
  className?: string;
  trigger?: React.ReactNode;
  defaultOpen?: boolean;
}

interface ActivityItem {
  id: string;
  type: 'comment' | 'edit' | 'view' | 'status_change';
  user: {
    name: string;
    avatar_url?: string;
  };
  description: string;
  timestamp: string;
}

export const CollaborationPanel: React.FC<CollaborationPanelProps> = ({
  entityType,
  entityId,
  currentUserId,
  className,
  trigger,
  defaultOpen = false
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [activeTab, setActiveTab] = useState('comments');
  const [recentActivity] = useState<ActivityItem[]>([
    {
      id: '1',
      type: 'comment',
      user: { name: 'John Doe' },
      description: 'Added a comment',
      timestamp: '2 minutes ago'
    },
    {
      id: '2',
      type: 'edit',
      user: { name: 'Jane Smith' },
      description: 'Updated vendor information',
      timestamp: '5 minutes ago'
    },
    {
      id: '3',
      type: 'view',
      user: { name: 'Mike Johnson' },
      description: 'Viewed this vendor',
      timestamp: '10 minutes ago'
    }
  ]);

  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'comment':
        return <MessageCircle className="h-4 w-4" />;
      case 'edit':
        return <Activity className="h-4 w-4" />;
      case 'view':
        return <Users className="h-4 w-4" />;
      case 'status_change':
        return <Clock className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getActivityColor = (type: ActivityItem['type']) => {
    switch (type) {
      case 'comment':
        return 'text-blue-600';
      case 'edit':
        return 'text-green-600';
      case 'view':
        return 'text-gray-600';
      case 'status_change':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="gap-2">
      <MessageCircle className="h-4 w-4" />
      Collaborate
      <ChevronRight className="h-4 w-4" />
    </Button>
  );

  const panelContent = (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5" />
          <h2 className="font-semibold">Collaboration</h2>
        </div>
        <PresenceIndicator entityType={entityType} entityId={entityId} />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 mx-4 mt-4">
          <TabsTrigger value="comments" className="gap-2">
            <MessageCircle className="h-4 w-4" />
            Comments
          </TabsTrigger>
          <TabsTrigger value="activity" className="gap-2">
            <Activity className="h-4 w-4" />
            Activity
          </TabsTrigger>
        </TabsList>

        <TabsContent value="comments" className="flex-1 p-4 overflow-auto">
          <ThreadedComments
            entityType={entityType}
            entityId={entityId}
            currentUserId={currentUserId}
          />
        </TabsContent>

        <TabsContent value="activity" className="flex-1 p-4 overflow-auto">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Recent Activity</h3>
              <Badge variant="secondary">{recentActivity.length}</Badge>
            </div>
            
            <div className="space-y-3">
              {recentActivity.map((item, index) => (
                <div key={item.id}>
                  <div className="flex items-start gap-3">
                    <div className={cn(
                      'flex items-center justify-center w-8 h-8 rounded-full bg-muted',
                      getActivityColor(item.type)
                    )}>
                      {getActivityIcon(item.type)}
                    </div>
                    
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">
                          {item.user.name}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {item.timestamp}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {item.description}
                      </p>
                    </div>
                  </div>
                  
                  {index < recentActivity.length - 1 && (
                    <Separator className="mt-3" />
                  )}
                </div>
              ))}
            </div>
            
            {recentActivity.length === 0 && (
              <Card>
                <CardContent className="p-6 text-center">
                  <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    No recent activity to show.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );

  // For mobile/responsive design, use Sheet
  if (window.innerWidth < 768) {
    return (
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          {trigger || defaultTrigger}
        </SheetTrigger>
        <SheetContent side="bottom" className="h-[80vh]">
          <SheetHeader className="sr-only">
            <SheetTitle>Collaboration Panel</SheetTitle>
            <SheetDescription>
              Collaborate with your team on this {entityType}
            </SheetDescription>
          </SheetHeader>
          {panelContent}
        </SheetContent>
      </Sheet>
    );
  }

  // For desktop, use a side panel
  return (
    <div className={cn('flex', className)}>
      {!isOpen && (
        <div className="flex flex-col items-center p-2 border-l bg-muted/30">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(true)}
            className="mb-2 h-8 w-8 p-0"
          >
            <MessageCircle className="h-4 w-4" />
          </Button>
          <PresenceIndicator 
            entityType={entityType} 
            entityId={entityId}
            compact
          />
        </div>
      )}
      
      {isOpen && (
        <div className="w-96 border-l bg-background flex flex-col">
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              <h2 className="font-semibold">Collaboration</h2>
            </div>
            <div className="flex items-center gap-2">
              <PresenceIndicator entityType={entityType} entityId={entityId} />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-2 mx-4 mt-4">
              <TabsTrigger value="comments" className="gap-2">
                <MessageCircle className="h-4 w-4" />
                Comments
              </TabsTrigger>
              <TabsTrigger value="activity" className="gap-2">
                <Activity className="h-4 w-4" />
                Activity
              </TabsTrigger>
            </TabsList>

            <TabsContent value="comments" className="flex-1 p-4 overflow-auto">
              <ThreadedComments
                entityType={entityType}
                entityId={entityId}
                currentUserId={currentUserId}
              />
            </TabsContent>

            <TabsContent value="activity" className="flex-1 p-4 overflow-auto">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Recent Activity</h3>
                  <Badge variant="secondary">{recentActivity.length}</Badge>
                </div>
                
                <div className="space-y-3">
                  {recentActivity.map((item, index) => (
                    <div key={item.id}>
                      <div className="flex items-start gap-3">
                        <div className={cn(
                          'flex items-center justify-center w-8 h-8 rounded-full bg-muted',
                          getActivityColor(item.type)
                        )}>
                          {getActivityIcon(item.type)}
                        </div>
                        
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">
                              {item.user.name}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {item.timestamp}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {item.description}
                          </p>
                        </div>
                      </div>
                      
                      {index < recentActivity.length - 1 && (
                        <Separator className="mt-3" />
                      )}
                    </div>
                  ))}
                </div>
                
                {recentActivity.length === 0 && (
                  <Card>
                    <CardContent className="p-6 text-center">
                      <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <p className="text-muted-foreground">
                        No recent activity to show.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
};