import React, { useState, useEffect } from 'react';
import { useSocket } from '@/hooks/useSocket';
import { RichTextEditor } from './RichTextEditor';
import { useTypingIndicator, TypingDisplay } from './TypingIndicator';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Reply, Edit, Trash2, MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

interface Comment {
  id: string;
  entity_type: string;
  entity_id: string;
  user_id: string;
  text: string;
  parent_comment_id?: string;
  created_at: string;
  updated_at: string;
  user?: {
    id: string;
    name: string;
    email: string;
    avatar_url?: string;
  };
}

interface CommentWithReplies extends Comment {
  replies: CommentWithReplies[];
}

interface ThreadedCommentsProps {
  entityType: string;
  entityId: string;
  currentUserId: string;
  className?: string;
}

export const ThreadedComments: React.FC<ThreadedCommentsProps> = ({
  entityType,
  entityId,
  currentUserId,
  className
}) => {
  const [comments, setComments] = useState<CommentWithReplies[]>([]);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editText, setEditText] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  const socket = useSocket();
  const { typingText, handleTypingStart, handleTypingStop } = useTypingIndicator({
    entityType,
    entityId,
    currentUserId: parseInt(currentUserId)
  });

  // Fetch comments
  const fetchComments = async () => {
    try {
      const response = await fetch(
        `/api/collaboration/comments?entityType=${entityType}&entityId=${entityId}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        setComments(organizeComments(data));
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
    } finally {
      setLoading(false);
    }
  };

  // Organize comments into threaded structure
  const organizeComments = (flatComments: Comment[]): CommentWithReplies[] => {
    const commentMap = new Map<string, CommentWithReplies>();
    const rootComments: CommentWithReplies[] = [];

    // First pass: create all comment objects
    flatComments.forEach(comment => {
      commentMap.set(comment.id, { ...comment, replies: [] });
    });

    // Second pass: organize into hierarchy
    flatComments.forEach(comment => {
      const commentWithReplies = commentMap.get(comment.id)!;
      
      if (comment.parent_comment_id) {
        const parent = commentMap.get(comment.parent_comment_id);
        if (parent) {
          parent.replies.push(commentWithReplies);
        }
      } else {
        rootComments.push(commentWithReplies);
      }
    });

    // Sort by creation date
    const sortComments = (comments: CommentWithReplies[]) => {
      comments.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      comments.forEach(comment => sortComments(comment.replies));
    };
    
    sortComments(rootComments);
    return rootComments;
  };

  // Submit new comment
  const handleSubmitComment = async () => {
    if (!newComment.trim() || submitting) return;

    setSubmitting(true);
    try {
      const response = await fetch('/api/collaboration/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          entityType,
          entityId,
          text: newComment,
        }),
      });

      if (response.ok) {
        setNewComment('');
        handleTypingStop();
        await fetchComments();
      }
    } catch (error) {
      console.error('Error submitting comment:', error);
    } finally {
      setSubmitting(false);
    }
  };

  // Submit reply
  const handleSubmitReply = async (parentId: string) => {
    if (!replyText.trim() || submitting) return;

    setSubmitting(true);
    try {
      const response = await fetch('/api/collaboration/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          entityType,
          entityId,
          text: replyText,
          parentCommentId: parentId,
        }),
      });

      if (response.ok) {
        setReplyText('');
        setReplyingTo(null);
        await fetchComments();
      }
    } catch (error) {
      console.error('Error submitting reply:', error);
    } finally {
      setSubmitting(false);
    }
  };

  // Update comment
  const handleUpdateComment = async (commentId: string) => {
    if (!editText.trim() || submitting) return;

    setSubmitting(true);
    try {
      const response = await fetch(`/api/collaboration/comments/${commentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ text: editText }),
      });

      if (response.ok) {
        setEditText('');
        setEditingComment(null);
        await fetchComments();
      }
    } catch (error) {
      console.error('Error updating comment:', error);
    } finally {
      setSubmitting(false);
    }
  };

  // Delete comment
  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('Are you sure you want to delete this comment?')) return;

    try {
      const response = await fetch(`/api/collaboration/comments/${commentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        await fetchComments();
      }
    } catch (error) {
      console.error('Error deleting comment:', error);
    }
  };

  // Socket event handlers
  useEffect(() => {
    if (socket) {
      socket.emit('join_room', `${entityType}_${entityId}`);

      const handleNewComment = (comment: Comment) => {
        fetchComments();
      };

      const handleCommentUpdated = (updatedComment: Comment) => {
        fetchComments();
      };

      const handleCommentDeleted = (deletedComment: { id: string }) => {
        fetchComments();
      };

      socket.on('new_comment', handleNewComment);
      socket.on('comment_updated', handleCommentUpdated);
      socket.on('comment_deleted', handleCommentDeleted);

      return () => {
        socket.off('new_comment', handleNewComment);
        socket.off('comment_updated', handleCommentUpdated);
        socket.off('comment_deleted', handleCommentDeleted);
        socket.emit('leave_room', `${entityType}_${entityId}`);
      };
    }
  }, [socket, entityType, entityId]);

  useEffect(() => {
    fetchComments();
  }, [entityType, entityId]);

  // Render individual comment
  const renderComment = (comment: CommentWithReplies, depth = 0) => {
    const isOwner = comment.user_id === currentUserId;
    const isEditing = editingComment === comment.id;
    const maxDepth = 3;

    return (
      <div key={comment.id} className={cn('space-y-3', depth > 0 && 'ml-8')}>
        <Card className="relative">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={comment.user?.avatar_url} />
                <AvatarFallback>
                  {comment.user?.name?.charAt(0) || 'U'}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">
                    {comment.user?.name || 'Unknown User'}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
                  </span>
                  {comment.updated_at !== comment.created_at && (
                    <Badge variant="secondary" className="text-xs">
                      edited
                    </Badge>
                  )}
                </div>
                
                {isEditing ? (
                  <div className="space-y-3">
                    <RichTextEditor
                      content={editText}
                      onChange={setEditText}
                      placeholder="Edit your comment..."
                      className="min-h-[80px]"
                    />
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleUpdateComment(comment.id)}
                        disabled={submitting || !editText.trim()}
                      >
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setEditingComment(null);
                          setEditText('');
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div
                    className="prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: comment.text }}
                  />
                )}
                
                {!isEditing && (
                  <div className="flex items-center gap-2">
                    {depth < maxDepth && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => {
                          setReplyingTo(replyingTo === comment.id ? null : comment.id);
                          setReplyText('');
                        }}
                        className="h-7 px-2 text-xs"
                      >
                        <Reply className="h-3 w-3 mr-1" />
                        Reply
                      </Button>
                    )}
                    
                    {isOwner && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button size="sm" variant="ghost" className="h-7 w-7 p-0">
                            <MoreHorizontal className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setEditingComment(comment.id);
                              setEditText(comment.text);
                            }}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteComment(comment.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Reply form */}
        {replyingTo === comment.id && (
          <div className="ml-8">
            <Card>
              <CardContent className="p-4 space-y-3">
                <RichTextEditor
                  content={replyText}
                  onChange={setReplyText}
                  placeholder="Write a reply..."
                  className="min-h-[80px]"
                />
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleSubmitReply(comment.id)}
                    disabled={submitting || !replyText.trim()}
                  >
                    Reply
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setReplyingTo(null);
                      setReplyText('');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
        
        {/* Nested replies */}
        {comment.replies.length > 0 && (
          <div className="space-y-3">
            {comment.replies.map(reply => renderComment(reply, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="animate-pulse space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-20 bg-muted rounded-md" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* New comment form */}
      <Card>
        <CardContent className="p-4 space-y-4">
          <div className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            <h3 className="font-medium">Comments</h3>
            <Badge variant="secondary">{comments.length}</Badge>
          </div>
          
          <RichTextEditor
            content={newComment}
            onChange={(content) => {
              setNewComment(content);
              if (content.trim()) {
                handleTypingStart();
              } else {
                handleTypingStop();
              }
            }}
            onBlur={handleTypingStop}
            placeholder="Share your thoughts..."
            className="min-h-[120px]"
          />
          
          <TypingDisplay typingText={typingText} />
          
          <div className="flex justify-end">
            <Button
              onClick={handleSubmitComment}
              disabled={submitting || !newComment.trim()}
            >
              {submitting ? 'Posting...' : 'Post Comment'}
            </Button>
          </div>
        </CardContent>
      </Card>
      
      {/* Comments list */}
      {comments.length > 0 ? (
        <div className="space-y-4">
          {comments.map(comment => renderComment(comment))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <MessageCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">
              No comments yet. Be the first to share your thoughts!
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};