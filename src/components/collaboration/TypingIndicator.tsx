import React, { useState, useEffect } from 'react';
import { useSocket } from '@/hooks/useSocket';

interface TypingUser {
  id: number;
  name: string;
}

interface TypingIndicatorProps {
  entityType: string;
  entityId: string;
  currentUserId?: number;
}

export const useTypingIndicator = ({ 
  entityType, 
  entityId, 
  currentUserId 
}: TypingIndicatorProps) => {
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const socket = useSocket();

  useEffect(() => {
    if (socket) {
      socket.on('typing_start', (user: TypingUser) => {
        if (user.id !== currentUserId) {
          setTypingUsers(prev => {
            const exists = prev.find(u => u.id === user.id);
            if (!exists) {
              return [...prev, user];
            }
            return prev;
          });
        }
      });

      socket.on('typing_stop', (user: TypingUser) => {
        setTypingUsers(prev => prev.filter(u => u.id !== user.id));
      });

      return () => {
        socket.off('typing_start');
        socket.off('typing_stop');
      };
    }
  }, [socket, currentUserId]);

  const handleTypingStart = () => {
    if (socket) {
      socket.emit('typing_start', `${entityType}_${entityId}`);
    }
  };

  const handleTypingStop = () => {
    if (socket) {
      socket.emit('typing_stop', `${entityType}_${entityId}`);
    }
  };

  const getTypingText = () => {
    if (typingUsers.length === 0) return '';
    if (typingUsers.length === 1) return `${typingUsers[0].name} is typing...`;
    if (typingUsers.length === 2) return `${typingUsers[0].name} and ${typingUsers[1].name} are typing...`;
    return `${typingUsers[0].name} and ${typingUsers.length - 1} others are typing...`;
  };

  return {
    typingText: getTypingText(),
    handleTypingStart,
    handleTypingStop,
    isTyping: typingUsers.length > 0
  };
};

// Component to display typing indicator
export const TypingDisplay: React.FC<{ typingText: string }> = ({ typingText }) => {
  if (!typingText) return null;

  return (
    <div className="flex items-center space-x-2 text-sm text-gray-500 italic px-3 py-1">
      <div className="flex space-x-1">
        <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
        <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
        <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
      </div>
      <span>{typingText}</span>
    </div>
  );
};