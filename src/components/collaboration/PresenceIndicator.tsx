import React, { useState, useEffect } from 'react';
import { Users, Eye } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useSocket } from '@/hooks/useSocket';

interface ActiveUser {
  id: number;
  name: string;
  email: string;
}

interface PresenceIndicatorProps {
  entityType: string;
  entityId: string;
  compact?: boolean;
}

export const PresenceIndicator: React.FC<PresenceIndicatorProps> = ({
  entityType,
  entityId,
  compact = false
}) => {
  const [activeUsers, setActiveUsers] = useState<ActiveUser[]>([]);
  const socket = useSocket();

  useEffect(() => {
    fetchActiveUsers();
  }, [entityType, entityId]);

  useEffect(() => {
    if (socket) {
      // Join the room for this entity
      socket.emit('join_room', `${entityType}_${entityId}`);

      // Listen for presence updates
      socket.on('user_joined', (user: ActiveUser) => {
        setActiveUsers(prev => {
          const exists = prev.find(u => u.id === user.id);
          if (!exists) {
            return [...prev, user];
          }
          return prev;
        });
      });

      socket.on('user_left', (user: ActiveUser) => {
        setActiveUsers(prev => prev.filter(u => u.id !== user.id));
      });

      return () => {
        socket.off('user_joined');
        socket.off('user_left');
        socket.emit('leave_room', `${entityType}_${entityId}`);
      };
    }
  }, [socket, entityType, entityId]);

  const fetchActiveUsers = async () => {
    try {
      const response = await fetch(`/api/collaboration/presence/${entityType}/${entityId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setActiveUsers(data);
      }
    } catch (error) {
      console.error('Failed to fetch active users:', error);
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  if (activeUsers.length === 0) {
    return null;
  }

  if (compact) {
    return (
      <div className="flex flex-col items-center space-y-1">
        <div className="flex -space-x-1">
          <TooltipProvider>
            {activeUsers.slice(0, 2).map((user) => (
              <Tooltip key={user.id}>
                <TooltipTrigger>
                  <Avatar className="h-6 w-6 border border-white">
                    <AvatarFallback className="text-xs bg-blue-100 text-blue-600">
                      {getInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm">{user.name}</p>
                  <p className="text-xs text-gray-500">{user.email}</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </TooltipProvider>
          {activeUsers.length > 2 && (
            <Badge variant="secondary" className="h-6 w-6 rounded-full p-0 flex items-center justify-center text-xs border border-white">
              +{activeUsers.length - 2}
            </Badge>
          )}
        </div>
        {activeUsers.length > 0 && (
          <span className="text-xs text-gray-500">
            {activeUsers.length}
          </span>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      <Eye className="h-4 w-4 text-gray-500" />
      <div className="flex items-center space-x-1">
        <TooltipProvider>
          {activeUsers.slice(0, 3).map((user) => (
            <Tooltip key={user.id}>
              <TooltipTrigger>
                <Avatar className="h-6 w-6 border-2 border-white">
                  <AvatarFallback className="text-xs bg-blue-100 text-blue-600">
                    {getInitials(user.name)}
                  </AvatarFallback>
                </Avatar>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm">{user.name}</p>
                <p className="text-xs text-gray-500">{user.email}</p>
              </TooltipContent>
            </Tooltip>
          ))}
        </TooltipProvider>
        
        {activeUsers.length > 3 && (
          <Badge variant="secondary" className="h-6 w-6 rounded-full p-0 flex items-center justify-center text-xs">
            +{activeUsers.length - 3}
          </Badge>
        )}
        
        <span className="text-sm text-gray-500 ml-2">
          {activeUsers.length === 1 ? '1 person viewing' : `${activeUsers.length} people viewing`}
        </span>
      </div>
    </div>
  );
};