import React, { useState, useEffect, useMemo } from 'react';
import { Search, Building2, ChevronDown, Check, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { AccountApi } from '../../services/accountApi';
import { Account } from '../../types/account';
import { cn } from '@/lib/utils';

interface AccountSelectorProps {
  value?: number | number[];
  onChange: (value: number | number[] | undefined) => void;
  placeholder?: string;
  multiple?: boolean;
  disabled?: boolean;
  className?: string;
  excludeIds?: number[];
  filterByType?: string[];
  filterByIndustry?: string[];
  showHierarchy?: boolean;
  maxSelections?: number;
}

export const AccountSelector: React.FC<AccountSelectorProps> = ({
  value,
  onChange,
  placeholder = "Select account...",
  multiple = false,
  disabled = false,
  className,
  excludeIds = [],
  filterByType = [],
  filterByIndustry = [],
  showHierarchy = false,
  maxSelections,
}) => {
  const [open, setOpen] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Load accounts
  useEffect(() => {
    const loadAccounts = async () => {
      setLoading(true);
      try {
        const filters: any = {};
        
        if (filterByType.length > 0) {
          filters.type = filterByType;
        }
        
        if (filterByIndustry.length > 0) {
          filters.industry = filterByIndustry;
        }

        const result = await AccountApi.getAccounts(filters, 1, 100);
        
        // Filter out excluded accounts
        const filteredAccounts = result.accounts.filter(
          account => !excludeIds.includes(account.id)
        );
        
        setAccounts(filteredAccounts);
      } catch (error) {
        console.error('Failed to load accounts:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAccounts();
  }, [excludeIds, filterByType, filterByIndustry]);

  // Filter accounts based on search term
  const filteredAccounts = useMemo(() => {
    if (!searchTerm) return accounts;
    
    return accounts.filter(account =>
      account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.accountNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.industry?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [accounts, searchTerm]);

  // Get selected accounts
  const selectedAccounts = useMemo(() => {
    if (!value) return [];
    
    const selectedIds = Array.isArray(value) ? value : [value];
    return accounts.filter(account => selectedIds.includes(account.id));
  }, [accounts, value]);

  // Handle selection
  const handleSelect = (accountId: number) => {
    if (multiple) {
      const currentValue = Array.isArray(value) ? value : [];
      
      if (currentValue.includes(accountId)) {
        // Remove from selection
        const newValue = currentValue.filter(id => id !== accountId);
        onChange(newValue.length > 0 ? newValue : undefined);
      } else {
        // Add to selection (check max limit)
        if (maxSelections && currentValue.length >= maxSelections) {
          return; // Don't add if at max limit
        }
        onChange([...currentValue, accountId]);
      }
    } else {
      // Single selection
      if (value === accountId) {
        onChange(undefined);
      } else {
        onChange(accountId);
        setOpen(false);
      }
    }
  };

  // Clear all selections
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(undefined);
  };

  // Format account display with hierarchy
  const formatAccountDisplay = (account: Account) => {
    if (showHierarchy && account.parent) {
      return `${account.parent.name} > ${account.name}`;
    }
    return account.name;
  };

  // Get display text for trigger
  const getDisplayText = () => {
    if (selectedAccounts.length === 0) {
      return placeholder;
    }
    
    if (multiple) {
      if (selectedAccounts.length === 1) {
        return selectedAccounts[0].name;
      }
      return `${selectedAccounts.length} accounts selected`;
    }
    
    return selectedAccounts[0]?.name || placeholder;
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between",
            !value && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <Building2 className="h-4 w-4 flex-shrink-0" />
            <span className="truncate">{getDisplayText()}</span>
          </div>
          <div className="flex items-center gap-1 flex-shrink-0">
            {selectedAccounts.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                onClick={handleClear}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
            <ChevronDown className="h-4 w-4 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <CommandInput
              placeholder="Search accounts..."
              value={searchTerm}
              onValueChange={setSearchTerm}
            />
          </div>
          
          <CommandList>
            <ScrollArea className="h-[300px]">
              {loading ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  Loading accounts...
                </div>
              ) : filteredAccounts.length === 0 ? (
                <CommandEmpty>No accounts found.</CommandEmpty>
              ) : (
                <CommandGroup>
                  {filteredAccounts.map((account) => {
                    const isSelected = multiple 
                      ? Array.isArray(value) && value.includes(account.id)
                      : value === account.id;
                    
                    return (
                      <CommandItem
                        key={account.id}
                        value={account.id.toString()}
                        onSelect={() => handleSelect(account.id)}
                        className="flex items-center gap-2 p-2"
                      >
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          {multiple && (
                            <div className={cn(
                              "flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                              isSelected 
                                ? "bg-primary text-primary-foreground" 
                                : "opacity-50 [&_svg]:invisible"
                            )}>
                              <Check className="h-3 w-3" />
                            </div>
                          )}
                          
                          <Building2 className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          
                          <div className="flex-1 min-w-0">
                            <div className="font-medium truncate">
                              {formatAccountDisplay(account)}
                            </div>
                            <div className="text-xs text-muted-foreground flex items-center gap-2">
                              {account.accountNumber && (
                                <span>#{account.accountNumber}</span>
                              )}
                              {account.industry && (
                                <>
                                  {account.accountNumber && <span>•</span>}
                                  <span>{account.industry}</span>
                                </>
                              )}
                              {account.type && (
                                <>
                                  {(account.accountNumber || account.industry) && <span>•</span>}
                                  <span>{account.type}</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        {!multiple && isSelected && (
                          <Check className="h-4 w-4 text-primary" />
                        )}
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              )}
            </ScrollArea>
          </CommandList>
          
          {multiple && selectedAccounts.length > 0 && (
            <>
              <Separator />
              <div className="p-2">
                <div className="text-xs text-muted-foreground mb-2">
                  Selected ({selectedAccounts.length}
                  {maxSelections && `/${maxSelections}`}):
                </div>
                <div className="flex flex-wrap gap-1">
                  {selectedAccounts.map((account) => (
                    <Badge
                      key={account.id}
                      variant="secondary"
                      className="text-xs"
                    >
                      {account.name}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-3 w-3 p-0 ml-1 hover:bg-destructive hover:text-destructive-foreground"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSelect(account.id);
                        }}
                      >
                        <X className="h-2 w-2" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
};