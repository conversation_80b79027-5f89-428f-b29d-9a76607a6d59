import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Search,
  Filter,
  X,
  ChevronDown,
  Building2,
  DollarSign,
  Users,
  MapPin,
  Star,
  Briefcase,
  Building,
} from "lucide-react";
import { useDebounce } from "../../hooks/useDebounce";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AccountSearchFilters,
  ACCOUNT_TYPES,
  INDUSTRIES,
  RATINGS,
  OWNERSHIP_TYPES,
  ACCOUNT_STATUSES,
} from "../../types/account";

interface SearchFiltersProps {
  filters: AccountSearchFilters;
  onFiltersChange: (filters: AccountSearchFilters) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
}

const COUNTRIES = [
  "United States",
  "Canada",
  "United Kingdom",
  "Germany",
  "France",
  "Australia",
  "Japan",
  "China",
  "India",
  "Brazil",
  "Mexico",
  "Other",
];

const US_STATES = [
  "California",
  "Texas",
  "Florida",
  "New York",
  "Pennsylvania",
  "Illinois",
  "Ohio",
  "Georgia",
  "North Carolina",
  "Michigan",
  "Other",
];

export const SearchFilters: React.FC<SearchFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
  isLoading = false,
}) => {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(filters.search || "");

  // Debounce search input
  const debouncedSearchTerm = useDebounce(searchInput, 300);

  const updateFilter = useCallback(
    (key: keyof AccountSearchFilters, value: unknown) => {
      onFiltersChange({
        ...filters,
        [key]: value,
      });
    },
    [filters, onFiltersChange]
  );

  // Update search filter when debounced term changes
  useEffect(() => {
    if (debouncedSearchTerm !== filters.search) {
      updateFilter("search", debouncedSearchTerm || undefined);
    }
  }, [debouncedSearchTerm, filters.search, updateFilter]);

  // Update local search input when filters change externally
  useEffect(() => {
    if (filters.search !== searchInput) {
      setSearchInput(filters.search || "");
    }
  }, [filters.search, searchInput]);

  // Count active filters
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => {
    if (key === "search" && value) return true;
    if (Array.isArray(value) && value.length > 0) return true;
    if (typeof value === "number" && value !== undefined) return true;
    if (typeof value === "string" && value) return true;
    if (value instanceof Date) return true;
    return false;
  }).length;

  const toggleArrayFilter = (
    key: keyof AccountSearchFilters,
    value: string
  ) => {
    const currentArray = (filters[key] as string[]) || [];
    const newArray = currentArray.includes(value)
      ? currentArray.filter((item) => item !== value)
      : [...currentArray, value];

    updateFilter(key, newArray.length > 0 ? newArray : undefined);
  };

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search & Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search accounts by name or description..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="pl-10"
            disabled={isLoading}
          />
          {searchInput !== debouncedSearchTerm && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
            </div>
          )}
        </div>

        {/* Quick Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label className="text-sm font-medium mb-2 flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Industry
            </Label>
            <Select
              value={filters.industry?.[0] || "all"}
              onValueChange={(value) =>
                updateFilter("industry", value === "all" ? undefined : [value])
              }
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="All industries" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Industries</SelectItem>
                {INDUSTRIES.map((industry) => (
                  <SelectItem key={industry} value={industry}>
                    {industry}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-sm font-medium mb-2 flex items-center gap-2">
              <Briefcase className="h-4 w-4" />
              Type
            </Label>
            <Select
              value={filters.type?.[0] || "all"}
              onValueChange={(value) =>
                updateFilter("type", value === "all" ? undefined : [value])
              }
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {ACCOUNT_TYPES.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-sm font-medium mb-2 flex items-center gap-2">
              <Star className="h-4 w-4" />
              Rating
            </Label>
            <Select
              value={filters.rating?.[0] || "all"}
              onValueChange={(value) =>
                updateFilter("rating", value === "all" ? undefined : [value])
              }
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="All ratings" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Ratings</SelectItem>
                {RATINGS.map((rating) => (
                  <SelectItem key={rating} value={rating}>
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          rating === "Hot"
                            ? "bg-red-500"
                            : rating === "Warm"
                            ? "bg-yellow-500"
                            : "bg-blue-500"
                        }`}
                      />
                      {rating}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Advanced Filters */}
        <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className="w-full justify-between p-4 h-auto"
            >
              <span className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Advanced Filters
              </span>
              <ChevronDown
                className={`h-4 w-4 transition-transform ${
                  isAdvancedOpen ? "rotate-180" : ""
                }`}
              />
            </Button>
          </CollapsibleTrigger>

          <CollapsibleContent className="space-y-4 mt-4">
            <Separator />

            {/* Financial Filters */}
            <div>
              <Label className="text-sm font-medium mb-3 flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Annual Revenue (USD)
              </Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs text-muted-foreground">
                    Minimum
                  </Label>
                  <Input
                    type="number"
                    placeholder="0"
                    value={filters.annual_revenue_min || ""}
                    onChange={(e) =>
                      updateFilter(
                        "annual_revenue_min",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                    disabled={isLoading}
                  />
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">
                    Maximum
                  </Label>
                  <Input
                    type="number"
                    placeholder="No limit"
                    value={filters.annual_revenue_max || ""}
                    onChange={(e) =>
                      updateFilter(
                        "annual_revenue_max",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Employee Count */}
            <div>
              <Label className="text-sm font-medium mb-3 flex items-center gap-2">
                <Users className="h-4 w-4" />
                Number of Employees
              </Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs text-muted-foreground">
                    Minimum
                  </Label>
                  <Input
                    type="number"
                    placeholder="0"
                    value={filters.number_of_employees_min || ""}
                    onChange={(e) =>
                      updateFilter(
                        "number_of_employees_min",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                    disabled={isLoading}
                  />
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">
                    Maximum
                  </Label>
                  <Input
                    type="number"
                    placeholder="No limit"
                    value={filters.number_of_employees_max || ""}
                    onChange={(e) =>
                      updateFilter(
                        "number_of_employees_max",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Geographic Filters */}
            <div>
              <Label className="text-sm font-medium mb-3 flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Location
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                <div>
                  <Label className="text-xs text-muted-foreground">
                    Country
                  </Label>
                  <Select
                    value={filters.country?.[0] || "all"}
                    onValueChange={(value) =>
                      updateFilter(
                        "country",
                        value === "all" ? undefined : [value]
                      )
                    }
                    disabled={isLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Any country" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Any Country</SelectItem>
                      {COUNTRIES.map((country) => (
                        <SelectItem key={country} value={country}>
                          {country}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">State</Label>
                  <Select
                    value={filters.state?.[0] || "all"}
                    onValueChange={(value) =>
                      updateFilter(
                        "state",
                        value === "all" ? undefined : [value]
                      )
                    }
                    disabled={
                      isLoading || filters.country?.[0] !== "United States"
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Any state" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Any State</SelectItem>
                      {US_STATES.map((state) => (
                        <SelectItem key={state} value={state}>
                          {state}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">City</Label>
                  <Input
                    placeholder="Any city"
                    value={filters.city?.[0] || ""}
                    onChange={(e) =>
                      updateFilter(
                        "city",
                        e.target.value ? [e.target.value] : undefined
                      )
                    }
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Ownership Filter */}
            <div>
              <Label className="text-sm font-medium mb-3 flex items-center gap-2">
                <Building className="h-4 w-4" />
                Ownership
              </Label>
              <div className="flex flex-wrap gap-2">
                {OWNERSHIP_TYPES.map((ownership) => (
                  <Button
                    key={ownership}
                    variant={
                      filters.ownership?.includes(ownership)
                        ? "default"
                        : "outline"
                    }
                    size="sm"
                    onClick={() => toggleArrayFilter("ownership", ownership)}
                    disabled={isLoading}
                  >
                    {ownership}
                  </Button>
                ))}
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <Label className="text-sm font-medium mb-3">Status</Label>
              <div className="flex flex-wrap gap-2">
                {ACCOUNT_STATUSES.map((status) => (
                  <Button
                    key={status}
                    variant={
                      filters.status?.includes(status) ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => toggleArrayFilter("status", status)}
                    disabled={isLoading}
                  >
                    <div
                      className={`w-2 h-2 rounded-full mr-2 ${
                        status === "ACTIVE" ? "bg-green-500" : "bg-gray-400"
                      }`}
                    />
                    {status}
                  </Button>
                ))}
              </div>
            </div>

            {/* Date Range Filters */}
            <div>
              <Label className="text-sm font-medium mb-3">
                Created Date Range
              </Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs text-muted-foreground">From</Label>
                  <Input
                    type="date"
                    value={
                      filters.created_after
                        ? filters.created_after.toISOString().split("T")[0]
                        : ""
                    }
                    onChange={(e) =>
                      updateFilter(
                        "created_after",
                        e.target.value ? new Date(e.target.value) : undefined
                      )
                    }
                    disabled={isLoading}
                  />
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">To</Label>
                  <Input
                    type="date"
                    value={
                      filters.created_before
                        ? filters.created_before.toISOString().split("T")[0]
                        : ""
                    }
                    onChange={(e) =>
                      updateFilter(
                        "created_before",
                        e.target.value ? new Date(e.target.value) : undefined
                      )
                    }
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Hierarchy Filter */}
            <div>
              <Label className="text-sm font-medium mb-3">Hierarchy</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="has_children"
                  checked={filters.has_children || false}
                  onCheckedChange={(checked) =>
                    updateFilter("has_children", checked ? true : undefined)
                  }
                  disabled={isLoading}
                />
                <Label htmlFor="has_children" className="cursor-pointer">
                  Only accounts with child accounts
                </Label>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Active Filters Summary */}
        {activeFiltersCount > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="pt-4 border-t"
          >
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
              <Filter className="h-4 w-4" />
              Active Filters:
            </div>
            <div className="flex flex-wrap gap-2">
              {filters.search && (
                <Badge variant="secondary" className="gap-1">
                  Search: "{filters.search}"
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => updateFilter("search", undefined)}
                  />
                </Badge>
              )}
              {filters.industry?.map((industry) => (
                <Badge key={industry} variant="secondary" className="gap-1">
                  Industry: {industry}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => toggleArrayFilter("industry", industry)}
                  />
                </Badge>
              ))}
              {filters.type?.map((type) => (
                <Badge key={type} variant="secondary" className="gap-1">
                  Type: {type}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => toggleArrayFilter("type", type)}
                  />
                </Badge>
              ))}
              {filters.rating?.map((rating) => (
                <Badge key={rating} variant="secondary" className="gap-1">
                  Rating: {rating}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => toggleArrayFilter("rating", rating)}
                  />
                </Badge>
              ))}
              {filters.annual_revenue_min && (
                <Badge variant="secondary" className="gap-1">
                  Min Revenue: ${filters.annual_revenue_min.toLocaleString()}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => updateFilter("annual_revenue_min", undefined)}
                  />
                </Badge>
              )}
              {filters.annual_revenue_max && (
                <Badge variant="secondary" className="gap-1">
                  Max Revenue: ${filters.annual_revenue_max.toLocaleString()}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => updateFilter("annual_revenue_max", undefined)}
                  />
                </Badge>
              )}
              {filters.number_of_employees_min && (
                <Badge variant="secondary" className="gap-1">
                  Min Employees: {filters.number_of_employees_min.toLocaleString()}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() =>
                      updateFilter("number_of_employees_min", undefined)
                    }
                  />
                </Badge>
              )}
              {filters.number_of_employees_max && (
                <Badge variant="secondary" className="gap-1">
                  Max Employees: {filters.number_of_employees_max.toLocaleString()}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() =>
                      updateFilter("number_of_employees_max", undefined)
                    }
                  />
                </Badge>
              )}
              {filters.ownership?.map((ownership) => (
                <Badge key={ownership} variant="secondary" className="gap-1">
                  Ownership: {ownership}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => toggleArrayFilter("ownership", ownership)}
                  />
                </Badge>
              ))}
              {filters.status?.map((status) => (
                <Badge key={status} variant="secondary" className="gap-1">
                  Status: {status}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => toggleArrayFilter("status", status)}
                  />
                </Badge>
              ))}
              {filters.country?.map((country) => (
                <Badge key={country} variant="secondary" className="gap-1">
                  Country: {country}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => toggleArrayFilter("country", country)}
                  />
                </Badge>
              ))}
              {filters.state?.map((state) => (
                <Badge key={state} variant="secondary" className="gap-1">
                  State: {state}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => toggleArrayFilter("state", state)}
                  />
                </Badge>
              ))}
              {filters.city?.map((city) => (
                <Badge key={city} variant="secondary" className="gap-1">
                  City: {city}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => toggleArrayFilter("city", city)}
                  />
                </Badge>
              ))}
              {filters.created_after && (
                <Badge variant="secondary" className="gap-1">
                  From: {filters.created_after.toLocaleDateString()}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => updateFilter("created_after", undefined)}
                  />
                </Badge>
              )}
              {filters.created_before && (
                <Badge variant="secondary" className="gap-1">
                  To: {filters.created_before.toLocaleDateString()}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => updateFilter("created_before", undefined)}
                  />
                </Badge>
              )}
              {filters.has_children && (
                <Badge variant="secondary" className="gap-1">
                  Has Children
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => updateFilter("has_children", undefined)}
                  />
                </Badge>
              )}
            </div>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
};
