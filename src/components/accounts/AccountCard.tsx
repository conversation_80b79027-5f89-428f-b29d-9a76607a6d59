import React from 'react';
import { motion } from 'framer-motion';
import { 
  Building2, 
  MapPin, 
  Phone, 
  Globe, 
  DollarSign, 
  Users,
  Star,
  MoreHorizontal,
  Edit,
  Eye,
  Trash2
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Account } from '../../types/account';
import { useAuth } from '../../hooks/useAuth';

interface AccountCardProps {
  account: Account;
  onView?: (account: Account) => void;
  onEdit?: (account: Account) => void;
  onDelete?: (account: Account) => void;
  className?: string;
  viewMode?: 'grid' | 'list';
}

export const AccountCard: React.FC<AccountCardProps> = ({
  account,
  onView,
  onEdit,
  onDelete,
  className = '',
  viewMode = 'grid'
}) => {
  const { canEdit, canDelete } = useAuth();

  const formatCurrency = (amount: number | undefined) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number | undefined) => {
    if (!num) return 'N/A';
    return new Intl.NumberFormat('en-US').format(num);
  };

  const getRatingColor = (rating: string | undefined) => {
    switch (rating) {
      case 'Hot':
        return 'destructive';
      case 'Warm':
        return 'default';
      case 'Cold':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-500';
      case 'INACTIVE':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  // List view layout
  if (viewMode === 'list') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -1 }}
        transition={{ duration: 0.2 }}
        className={className}
      >
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 flex-1">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Building2 className="h-5 w-5 text-primary" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <h3 
                    className="font-semibold text-lg leading-tight hover:text-primary transition-colors cursor-pointer"
                    onClick={() => onView?.(account)}
                  >
                    {account.name}
                  </h3>
                  <div className="flex items-center gap-2 mt-1">
                    <div className={`w-2 h-2 rounded-full ${getStatusColor(account.status)}`} />
                    <span className="text-sm text-muted-foreground">{account.status}</span>
                    {account.type && (
                      <>
                        <span className="text-sm text-muted-foreground">•</span>
                        <span className="text-sm text-muted-foreground">{account.type}</span>
                      </>
                    )}
                    {account.industry && (
                      <>
                        <span className="text-sm text-muted-foreground">•</span>
                        <span className="text-sm text-muted-foreground">{account.industry}</span>
                      </>
                    )}
                  </div>
                </div>

                <div className="hidden md:flex items-center gap-6">
                  <div className="text-center">
                    <p className="text-sm font-medium">{formatCurrency(account.annual_revenue)}</p>
                    <p className="text-xs text-muted-foreground">Revenue</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium">{formatNumber(account.number_of_employees)}</p>
                    <p className="text-xs text-muted-foreground">Employees</p>
                  </div>
                  {account.rating && (
                    <Badge variant={getRatingColor(account.rating)} className="text-xs">
                      <Star className="mr-1 h-3 w-3" />
                      {account.rating}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onView?.(account)}>
                    <Eye className="mr-2 h-4 w-4" />
                    View Details
                  </DropdownMenuItem>
                  {canEdit() && (
                    <DropdownMenuItem onClick={() => onEdit?.(account)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Account
                    </DropdownMenuItem>
                  )}
                  {canDelete() && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => onDelete?.(account)}
                        className="text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Account
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  // Grid view layout (default)
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
      className={className}
    >
      <Card className="h-full hover:shadow-md transition-shadow cursor-pointer">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3 flex-1">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Building2 className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 
                  className="font-semibold text-lg leading-tight hover:text-primary transition-colors cursor-pointer"
                  onClick={() => onView?.(account)}
                >
                  {account.name}
                </h3>
                {account.account_number && (
                  <p className="text-sm text-muted-foreground">
                    #{account.account_number}
                  </p>
                )}
                <div className="flex items-center gap-2 mt-1">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(account.status)}`} />
                  <span className="text-xs text-muted-foreground">
                    {account.status}
                  </span>
                  {account.type && (
                    <>
                      <span className="text-xs text-muted-foreground">•</span>
                      <span className="text-xs text-muted-foreground">
                        {account.type}
                      </span>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            {/* Actions Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onView?.(account)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                {canEdit() && (
                  <DropdownMenuItem onClick={() => onEdit?.(account)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Account
                  </DropdownMenuItem>
                )}
                {canDelete() && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => onDelete?.(account)}
                      className="text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Account
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Industry and Rating */}
          <div className="flex items-center gap-2 flex-wrap">
            {account.industry && (
              <Badge variant="outline" className="text-xs">
                {account.industry}
              </Badge>
            )}
            {account.rating && (
              <Badge variant={getRatingColor(account.rating)} className="text-xs">
                <Star className="mr-1 h-3 w-3" />
                {account.rating}
              </Badge>
            )}
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-xs text-muted-foreground">Revenue</p>
                <p className="font-medium">{formatCurrency(account.annual_revenue)}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-xs text-muted-foreground">Employees</p>
                <p className="font-medium">{formatNumber(account.number_of_employees)}</p>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-2 text-sm">
            {account.phone && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Phone className="h-3 w-3" />
                <span className="truncate">{account.phone}</span>
              </div>
            )}
            {account.website && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Globe className="h-3 w-3" />
                <a 
                  href={account.website} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="truncate hover:text-primary transition-colors"
                  onClick={(e) => e.stopPropagation()}
                >
                  {account.website.replace(/^https?:\/\//, '')}
                </a>
              </div>
            )}
            {account.billing_address?.city && account.billing_address?.country && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <MapPin className="h-3 w-3" />
                <span className="truncate">
                  {account.billing_address.city}, {account.billing_address.country}
                </span>
              </div>
            )}
          </div>

          {/* Hierarchy Info */}
          {(account.parent_account_id || (account.children_count && account.children_count > 0)) && (
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                {account.parent_account_id && (
                  <span>Has parent account</span>
                )}
                {account.children_count && account.children_count > 0 && (
                  <span>{account.children_count} child account{account.children_count > 1 ? 's' : ''}</span>
                )}
              </div>
            </div>
          )}

          {/* Description Preview */}
          {account.description && (
            <div className="pt-2 border-t">
              <p className="text-xs text-muted-foreground line-clamp-2">
                {account.description}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};