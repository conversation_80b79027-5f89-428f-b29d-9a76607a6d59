import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  Download, 
  Grid3X3, 
  List, 
  Loader2, 
  AlertCircle,
  RefreshCw,
  Building2,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  Share2,
  Copy
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { AccountCard } from './AccountCard';
import { SearchFilters } from './SearchFilters';
import { ExportDialog } from './ExportDialog';
import { AccountApi } from '../../services/accountApi';
import { 
  Account, 
  AccountSearchFilters, 
  PaginatedAccounts 
} from '../../types/account';
import { useAuth } from '../../hooks/useAuth';
import { toast } from '../../components/ui/sonner';

type ViewMode = 'grid' | 'list';
type SortOption = 'name' | 'created_at' | 'annual_revenue' | 'number_of_employees';

// Define the stats type to be passed up
interface AccountStats {
  total: number;
  active: number;
}

interface AccountListProps {
  className?: string;
  onStatsChange?: (stats: AccountStats) => void;
}

export const AccountList: React.FC<AccountListProps> = ({ className = '',onStatsChange }) => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { canEdit } = useAuth();

  // State
  const [accounts, setAccounts] = useState<PaginatedAccounts | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [exporting, setExporting] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);

  // Parse filters from URL params
  const filters = useMemo(() => {
    const urlFilters: AccountSearchFilters = {};
    
    if (searchParams.get('search')) urlFilters.search = searchParams.get('search')!;
    
    // Handle array filters - support multiple values
    const industryParams = searchParams.getAll('industry');
    if (industryParams.length > 0) urlFilters.industry = industryParams;
    
    const typeParams = searchParams.getAll('type');
    if (typeParams.length > 0) urlFilters.type = typeParams;
    
    const ratingParams = searchParams.getAll('rating');
    if (ratingParams.length > 0) urlFilters.rating = ratingParams;
    
    const ownershipParams = searchParams.getAll('ownership');
    if (ownershipParams.length > 0) urlFilters.ownership = ownershipParams;
    
    const statusParams = searchParams.getAll('status');
    if (statusParams.length > 0) urlFilters.status = statusParams;
    
    const countryParams = searchParams.getAll('country');
    if (countryParams.length > 0) urlFilters.country = countryParams;
    
    const stateParams = searchParams.getAll('state');
    if (stateParams.length > 0) urlFilters.state = stateParams;
    
    const cityParams = searchParams.getAll('city');
    if (cityParams.length > 0) urlFilters.city = cityParams;
    
    // Handle numeric filters
    if (searchParams.get('annualRevenueMin')) {
      urlFilters.annualRevenueMin = parseInt(searchParams.get('annualRevenueMin')!);
    }
    if (searchParams.get('annualRevenueMax')) {
      urlFilters.annualRevenueMax = parseInt(searchParams.get('annualRevenueMax')!);
    }
    if (searchParams.get('numberOfEmployeesMin')) {
      urlFilters.numberOfEmployeesMin = parseInt(searchParams.get('numberOfEmployeesMin')!);
    }
    if (searchParams.get('numberOfEmployeesMax')) {
      urlFilters.numberOfEmployeesMax = parseInt(searchParams.get('numberOfEmployeesMax')!);
    }

    // Handle sort parameters
    const sortParam = searchParams.get('sort');
    if (sortParam && ['name', 'created_at', 'annual_revenue', 'number_of_employees'].includes(sortParam)) {
      setSortBy(sortParam as SortOption);
    }
    
    const orderParam = searchParams.get('order');
    if (orderParam && ['asc', 'desc'].includes(orderParam)) {
      setSortOrder(orderParam as 'asc' | 'desc');
    }

    return urlFilters;
  }, [searchParams]);

  const currentPage = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('limit') || '12');

  // Update URL params when filters change
  const updateFilters = (newFilters: AccountSearchFilters) => {
    const params = new URLSearchParams();
    
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value) && value.length > 0) {
          // Support multiple values for array filters
          value.forEach(v => params.append(key, v.toString()));
        } else if (typeof value === 'string' && value) {
          params.set(key, value);
        } else if (typeof value === 'number') {
          params.set(key, value.toString());
        }
      }
    });

    // Add sort parameters
    params.set('sort', sortBy);
    params.set('order', sortOrder);

    // Reset to page 1 when filters change
    params.set('page', '1');
    params.set('limit', pageSize.toString());

    setSearchParams(params);
  };

  const clearFilters = () => {
    setSearchParams({ page: '1', limit: pageSize.toString() });
  };

  // Load accounts
  const loadAccounts = useCallback(async () => {
    try {
      setLoading(true);
       // Create a specific filter set to count only 'ACTIVE' accounts,
      // while preserving other active filters (like industry, rating, etc.)
      const activeFilters = { ...filters, status: ['ACTIVE'] };

      // Fetch the main list and the active count in parallel
      const [mainResult, activeResult] = await Promise.all([
        AccountApi.getAccounts(filters, currentPage, pageSize),
        // We only need the count, so we fetch just one item to be efficient
        AccountApi.getAccounts(activeFilters, 1, 1) 
      ]);

      setAccounts(mainResult);
      
      // 3. Call the callback prop with the fetched stats
      if (onStatsChange) {
        onStatsChange({
          total: mainResult.pagination.total,
          active: activeResult.pagination.total,
        });
      }
    } catch (err) {
      console.error('Failed to load accounts:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load accounts';
      setError(errorMessage);
      toast.error('Failed to load accounts', {
        description: errorMessage,
      });
      // Optionally reset stats on error
      if (onStatsChange) {
        onStatsChange({ total: 0, active: 0 });
      }
    } finally {
      setLoading(false);
    }
  }, [filters, currentPage, pageSize, onStatsChange]);

  // Load accounts when filters or pagination change
  useEffect(() => {
    loadAccounts();
  }, [filters, currentPage, pageSize, loadAccounts]);

  // Sort accounts client-side
  const sortedAccounts = useMemo(() => {
    if (!accounts?.accounts) return [];

    return [...accounts.accounts].sort((a, b) => {
      let aValue: any;
      let bValue: unknown;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'created_at':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        case 'annual_revenue':
          aValue = a.annual_revenue || 0;
          bValue = b.annual_revenue || 0;
          break;
        case 'number_of_employees':
          aValue = a.number_of_employees || 0;
          bValue = b.number_of_employees || 0;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  }, [accounts?.accounts, sortBy, sortOrder]);

  // Handle pagination
  const goToPage = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', page.toString());
    setSearchParams(params);
  };

  // Handle account actions
  const handleViewAccount = (account: Account) => {
    navigate(`/accounts/${account.id}`);
  };

  const handleEditAccount = (account: Account) => {
    navigate(`/accounts/${account.id}/edit`);
  };

  const handleDeleteAccount = async (account: Account) => {
    if (!confirm(`Are you sure you want to delete "${account.name}"?`)) {
      return;
    }

    try {
      await AccountApi.deleteAccount(account.id);
      toast.success('Account deleted successfully');
      loadAccounts(); // Reload the list
    } catch (err) {
      console.error('Failed to delete account:', err);
      toast.error('Failed to delete account', {
        description: err instanceof Error ? err.message : 'An error occurred',
      });
    }
  };

  // Handle export
  const handleExport = () => {
    setShowExportDialog(true);
  };

  // Update sort in URL when changed
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    params.set('sort', sortBy);
    params.set('order', sortOrder);
    setSearchParams(params, { replace: true });
  }, [sortBy, sortOrder, searchParams, setSearchParams]);

  // Handle share URL
  const handleShareUrl = async () => {
    const currentUrl = window.location.href;
    try {
      await navigator.clipboard.writeText(currentUrl);
      toast.success('URL copied to clipboard', {
        description: 'Share this link to let others see the same filtered results',
      });
    } catch (err) {
      console.error('Failed to copy URL:', err);
      toast.error('Failed to copy URL');
    }
  };

  // Loading state
  if (loading && !accounts) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading accounts...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error && !accounts) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="text-center">
          <Button onClick={loadAccounts} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search Filters */}
      <SearchFilters
        filters={filters}
        onFiltersChange={updateFilters}
        onClearFilters={clearFilters}
        isLoading={loading}
      />

      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Building2 className="h-6 w-6" />
            Accounts
            {accounts && (
              <Badge variant="secondary" className="ml-2">
                {accounts.pagination.total}
              </Badge>
            )}
          </h2>
          
          {loading && accounts && (
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Sort Options */}
          <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="created_at">Created Date</SelectItem>
              <SelectItem value="annual_revenue">Revenue</SelectItem>
              <SelectItem value="number_of_employees">Employees</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          >
            <ArrowUpDown className="h-4 w-4" />
          </Button>

          {/* View Mode Toggle */}
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>

          {/* Share URL Button */}
          {Object.keys(filters).length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleShareUrl}
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
          )}

          {/* Export Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={!accounts?.accounts.length}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>

          {/* Create Button */}
          {canEdit() && (
            <Button onClick={() => navigate('/accounts/create')}>
              <Plus className="h-4 w-4 mr-2" />
              New Account
            </Button>
          )}
        </div>
      </div>

      {/* Results */}
      {accounts && accounts.accounts.length > 0 ? (
        <>
          {/* Account Grid/List */}
          <motion.div
            layout
            className={
              viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-4'
            }
          >
            <AnimatePresence>
              {sortedAccounts.map((account) => (
                <AccountCard
                  key={account.id}
                  account={account}
                  onView={handleViewAccount}
                  onEdit={canEdit() ? handleEditAccount : undefined}
                  onDelete={canEdit() ? handleDeleteAccount : undefined}
                  className={viewMode === 'list' ? 'w-full' : ''}
                  viewMode={viewMode}
                />
              ))}
            </AnimatePresence>
          </motion.div>

          {/* Pagination */}
          {accounts.pagination.totalPages > 1 && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {((currentPage - 1) * pageSize) + 1} to{' '}
                {Math.min(currentPage * pageSize, accounts.pagination.total)} of{' '}
                {accounts.pagination.total} accounts
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => goToPage(currentPage - 1)}
                  disabled={currentPage <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, accounts.pagination.totalPages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <Button
                        key={page}
                        variant={page === currentPage ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => goToPage(page)}
                      >
                        {page}
                      </Button>
                    );
                  })}
                  {accounts.pagination.totalPages > 5 && (
                    <>
                      <span className="text-muted-foreground">...</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => goToPage(accounts.pagination.totalPages)}
                      >
                        {accounts.pagination.totalPages}
                      </Button>
                    </>
                  )}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => goToPage(currentPage + 1)}
                  disabled={currentPage >= accounts.pagination.totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </>
      ) : (
        /* Empty State */
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">No accounts found</h3>
          <p className="text-muted-foreground mb-4">
            {Object.keys(filters).length > 0
              ? 'Try adjusting your search filters to find accounts.'
              : 'Get started by creating your first account.'}
          </p>
          <div className="flex items-center justify-center gap-2">
            {Object.keys(filters).length > 0 && (
              <Button variant="outline" onClick={clearFilters}>
                Clear Filters
              </Button>
            )}
            {canEdit() && (
              <Button onClick={() => navigate('/accounts/create')}>
                <Plus className="h-4 w-4 mr-2" />
                Create Account
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Export Dialog */}
      <ExportDialog
        open={showExportDialog}
        onOpenChange={setShowExportDialog}
        filters={filters}
        totalRecords={accounts?.pagination.total || 0}
      />
    </div>
  );
};