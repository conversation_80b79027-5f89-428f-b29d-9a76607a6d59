import React, { useState } from 'react';
import { Download, FileText, Database, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { AccountSearchFilters } from '../../types/account';
import { AccountApi } from '../../services/accountApi';
import { toast } from '../../components/ui/sonner';

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  filters: AccountSearchFilters;
  totalRecords: number;
}

type ExportFormat = 'csv' | 'json';

export const ExportDialog: React.FC<ExportDialogProps> = ({
  open,
  onOpenChange,
  filters,
  totalRecords
}) => {
  const [format, setFormat] = useState<ExportFormat>('csv');
  const [includeFilters, setIncludeFilters] = useState(true);
  const [exporting, setExporting] = useState(false);

  const handleExport = async () => {
    try {
      setExporting(true);
      
      const exportFilters = includeFilters ? filters : {};
      const blob = await AccountApi.exportAccounts(format, exportFilters);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `accounts-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success(`Accounts exported successfully`, {
        description: `Downloaded ${totalRecords} accounts as ${format.toUpperCase()}`,
      });

      onOpenChange(false);
    } catch (err) {
      console.error('Failed to export accounts:', err);
      toast.error('Export failed', {
        description: err instanceof Error ? err.message : 'An error occurred during export',
      });
    } finally {
      setExporting(false);
    }
  };

  const getRecordCount = () => {
    if (includeFilters && Object.keys(filters).length > 0) {
      return `${totalRecords} filtered records`;
    }
    return `All records`;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Accounts
          </DialogTitle>
          <DialogDescription>
            Choose your export format and options. {getRecordCount()} will be exported.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Format Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Export Format</Label>
            <RadioGroup value={format} onValueChange={(value: ExportFormat) => setFormat(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="csv" id="csv" />
                <Label htmlFor="csv" className="flex items-center gap-2 cursor-pointer">
                  <FileText className="h-4 w-4" />
                  <div>
                    <div className="font-medium">CSV (Comma Separated Values)</div>
                    <div className="text-xs text-muted-foreground">
                      Best for spreadsheet applications like Excel
                    </div>
                  </div>
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="json" id="json" />
                <Label htmlFor="json" className="flex items-center gap-2 cursor-pointer">
                  <Database className="h-4 w-4" />
                  <div>
                    <div className="font-medium">JSON (JavaScript Object Notation)</div>
                    <div className="text-xs text-muted-foreground">
                      Best for data processing and API integrations
                    </div>
                  </div>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Filter Options */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Export Options</Label>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="includeFilters"
                checked={includeFilters}
                onCheckedChange={(checked) => setIncludeFilters(checked as boolean)}
              />
              <Label htmlFor="includeFilters" className="cursor-pointer">
                <div className="font-medium">Apply current filters</div>
                <div className="text-xs text-muted-foreground">
                  {Object.keys(filters).length > 0
                    ? `Export only the ${totalRecords} filtered accounts`
                    : 'No filters currently applied'
                  }
                </div>
              </Label>
            </div>
          </div>

          {/* Export Preview */}
          <div className="bg-muted/50 rounded-lg p-3">
            <div className="text-sm">
              <div className="font-medium mb-1">Export Summary:</div>
              <ul className="text-muted-foreground space-y-1">
                <li>• Format: {format.toUpperCase()}</li>
                <li>• Records: {getRecordCount()}</li>
                <li>• Filters: {includeFilters ? 'Applied' : 'Ignored'}</li>
              </ul>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={exporting}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={exporting}>
            {exporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export {format.toUpperCase()}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};