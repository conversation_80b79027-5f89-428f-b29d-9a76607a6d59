import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { motion } from 'framer-motion';
import { 
  Building2, 
  Globe, 
  DollarSign,
  Users,
  Award, // Kept for UI consistency
  Hash,
  FileText,
  Star,
  Loader2,
  Building,
  Phone,
  CheckCircle2, // Added for Status field
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { AddressForm } from './AddressForm';
import { 
  accountCreateValidationSchema, 
  accountUpdateValidationSchema,
  AccountCreateFormData,
  AccountUpdateFormData
} from '../../utils/accountValidation';
import { 
  Account, 
  AccountCreateRequest, 
  AccountUpdateRequest,
  ACCOUNT_TYPES,
  INDUSTRIES,
  RATINGS,
  OWNERSHIP_TYPES,
  ACCOUNT_STATUSES // Assumed this exists based on your interface
} from '../../types/account';
import { AccountApi } from '../../services/accountApi';

interface AccountFormProps {
  initialData?: Partial<Account>;
  onSubmit: (data: AccountCreateRequest | AccountUpdateRequest) => Promise<void>;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

export const AccountForm: React.FC<AccountFormProps> = ({
  initialData,
  onSubmit,
  isLoading = false,
  mode,
}) => {
  const [parentAccounts, setParentAccounts] = useState<Account[]>([]);
  const [loadingParents, setLoadingParents] = useState(false);

  const validationSchema = mode === 'create' 
    ? accountCreateValidationSchema 
    : accountUpdateValidationSchema;

  const form = useForm<AccountCreateFormData | AccountUpdateFormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      name: initialData?.name || '',
      accountNumber: initialData?.account_number || '',
      type: initialData?.type || undefined,
      industry: initialData?.industry || undefined,
      annualRevenue: initialData?.annual_revenue || undefined,
      numberOfEmployees: initialData?.number_of_employees || undefined,
      ownership: initialData?.ownership || undefined,
      phone: initialData?.phone || '',
      fax: initialData?.fax || '',
      website: initialData?.website || '',
      tickerSymbol: initialData?.ticker_symbol || '',
      site: initialData?.site || '',
      rating: initialData?.rating || undefined,
      description: initialData?.description || '',
      // status: initialData?.status || 'Active', // Added status field
      billingAddress: {
        street: initialData?.billing_address?.street || '',
        city: initialData?.billing_address?.city || '',
        state: initialData?.billing_address?.state || '',
        // Corrected mapping from snake_case to camelCase
        postalCode: initialData?.billing_address?.postalCode || '', 
        country: initialData?.billing_address?.country || 'United States',
        latitude: initialData?.billing_address?.latitude || undefined,
        longitude: initialData?.billing_address?.longitude || undefined,
      },
      shippingAddress: {
        street: initialData?.shipping_address?.street || '',
        city: initialData?.shipping_address?.city || '',
        state: initialData?.shipping_address?.state || '',
        // Corrected mapping from snake_case to camelCase
        postalCode: initialData?.shipping_address?.postalCode || '',
        country: initialData?.shipping_address?.country || 'United States',
        latitude: initialData?.shipping_address?.latitude || undefined,
        longitude: initialData?.shipping_address?.longitude || undefined,
      },
      parentAccountId: initialData?.parent_account_id || undefined,
      ownerId: initialData?.owner_id || undefined,
      customFields: initialData?.custom_fields || {},
    },
  });

  // Load parent accounts for hierarchy selection
  useEffect(() => {
    const loadParentAccounts = async () => {
      setLoadingParents(true);
      try {
        const accounts = await AccountApi.getAccounts({}, 1, 100);
        const filteredAccounts = mode === 'edit' && initialData?.id
          ? accounts.accounts.filter(account => account.id !== initialData.id)
          : accounts.accounts;
        setParentAccounts(filteredAccounts);
      } catch (error) {
        console.error('Failed to load parent accounts:', error);
      } finally {
        setLoadingParents(false);
      }
    };

    loadParentAccounts();
  }, [mode, initialData?.id]);

  // Copy billing address to shipping address
  const copyBillingToShipping = () => {
    const billingAddress = form.getValues('billingAddress');
    form.setValue('shippingAddress', { ...billingAddress });
  };
  
  // Handle form submission
  const handleSubmit = async (data: AccountCreateFormData | AccountUpdateFormData) => {
    // Helper to map form address (camelCase) to API address (snake_case)
    const mapAddressToApi = (address: typeof data.billingAddress) => {
      if (!address || Object.values(address).every(v => v === '' || v === undefined)) {
        return undefined;
      }
      return {
        street: address.street,
        city: address.city,
        state: address.state,
        postal_code: address.postalCode, // Corrected mapping
        country: address.country,
        latitude: address.latitude,
        longitude: address.longitude,
      };
    };

    try {
      // **FIXED**: Transform form data (camelCase) to API format (snake_case)
      const apiData: AccountCreateRequest | AccountUpdateRequest = {
        name: data.name!,
        account_number: data.accountNumber ?? null,
        type: data.type ?? null,
        industry: data.industry ?? null,
        annual_revenue: data.annualRevenue ?? null,
        number_of_employees: data.numberOfEmployees ?? null,
        ownership: data.ownership ?? null,
        phone: data.phone ?? null,
        fax: data.fax ?? null,
        website: data.website ?? null,
        ticker_symbol: data.tickerSymbol ?? null,
        site: data.site ?? null,
        rating: data.rating ?? null,
        description: data.description ?? null,
        billing_address: data.billingAddress ?? null,
        shipping_address: data.shippingAddress ?? null,
        parent_account_id: data.parentAccountId ?? null,
        owner_id: data.ownerId ?? null,
        custom_fields: data.customFields ?? {},
      };

      await onSubmit(apiData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Account Name and Number Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      Account Name *
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Acme Corporation"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="accountNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Hash className="h-4 w-4" />
                      Account Number
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="ACC-001"
                        {...field}
                        value={field.value || ''}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional reference number for this account
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Type and Industry Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account Type</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value || ''}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select account type" />
                        </SelectTrigger>
                        <SelectContent>
                          {ACCOUNT_TYPES.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="industry"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Industry</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value || ''}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select industry" />
                        </SelectTrigger>
                        <SelectContent>
                          {INDUSTRIES.map((industry) => (
                            <SelectItem key={industry} value={industry}>
                              {industry}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            

            {/* Parent Account */}
            <FormField
              control={form.control}
              name="parentAccountId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Parent Account</FormLabel>
                  <FormControl>
                    <Select
                      value={field.value?.toString() || 'none'}
                      onValueChange={(value) => field.onChange(value === "none" ? undefined : Number(value))}
                      disabled={isLoading || loadingParents}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={loadingParents ? "Loading..." : "Select parent account (optional)"} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No Parent Account</SelectItem>
                        {parentAccounts.map((account) => (
                          <SelectItem key={account.id} value={account.id.toString()}>
                            {account.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>
                    Select a parent account to create a hierarchy
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Financial Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Financial Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="annualRevenue"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2.5">Annual Revenue</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="1000000"
                        {...field}
                        value={field.value ?? ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(value === '' ? null : parseFloat(value));
                        }}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Annual revenue in USD
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="numberOfEmployees"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2">
                      <Users className="h-4 w-4" />
                      Number of Employees
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="100"
                        {...field}
                        value={field.value ?? ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(value === '' ? null : parseInt(value));
                        }}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="ownership"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2">
                      <Award className="h-4 w-4" /> {/* Added Icon */}
                      Ownership
                    </FormLabel>
                    <FormControl>
                      <Select
                        value={field.value || ''}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select ownership type" />
                        </SelectTrigger>
                        <SelectContent>
                          {OWNERSHIP_TYPES.map((ownership) => (
                            <SelectItem key={ownership} value={ownership}>
                              {ownership}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="tickerSymbol"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2.5">Ticker Symbol</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="ACME"
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Stock ticker symbol (for public companies)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rating"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2">
                      <Star className="h-4 w-4" />
                      Rating
                    </FormLabel>
                    <FormControl>
                      <Select
                        value={field.value || ''}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select rating" />
                        </SelectTrigger>
                        <SelectContent>
                          {RATINGS.map((rating) => (
                            <SelectItem key={rating} value={rating}>
                              <div className="flex items-center gap-2">
                                <Badge 
                                  variant={
                                    rating === 'Hot' ? 'destructive' : 
                                    rating === 'Warm' ? 'default' : 
                                    'secondary'
                                  }
                                  className="w-2 h-2 p-0"
                                />
                                {rating}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>
        
        {/* Contact and Address Sections remain largely the same... */}
        
        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Phone
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="******-0123"
                        {...field}
                        value={field.value || ''}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fax"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Fax
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="******-0124"
                        {...field}
                        value={field.value || ''}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      Website
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://example.com"
                        {...field}
                        value={field.value || ''}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="site"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Site</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Headquarters, Branch Office, etc."
                      {...field}
                      value={field.value || ''}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    Site or location identifier
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Address Information */}
        <div className="space-y-6">
          <AddressForm
            form={form as undefined}
            fieldPrefix="billingAddress"
            title="Billing Address"
          />

          <div className="flex justify-center">
            <Button
              type="button"
              variant="outline"
              onClick={copyBillingToShipping}
              disabled={isLoading}
            >
              Copy Billing to Shipping Address
            </Button>
          </div>

          <AddressForm
            form={form as undefined}
            fieldPrefix="shippingAddress"
            title="Shipping Address"
          />
        </div>

        {/* Description */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Description
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Account Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter a detailed description of this account..."
                      className="min-h-[100px]"
                      {...field}
                      value={field.value || ''}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide additional details about this account
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end gap-4 pt-6">
          <Button
            type="button"
            variant="outline"
            disabled={isLoading}
            onClick={() => window.history.back()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {mode === 'create' ? 'Create Account' : 'Update Account'}
          </Button>
        </div>
      </form>
    </Form>
  );
};