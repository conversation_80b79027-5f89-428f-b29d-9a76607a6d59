/* Comments Component Styles */
.comments-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.comment-item {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.comment-author {
  font-weight: 500;
  color: #111827;
}

.comment-timestamp {
  font-size: 0.875rem;
  color: #6b7280;
}

.comment-content {
  color: #374151;
  margin-bottom: 0.75rem;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.comment-reply-btn {
  color: #2563eb;
  cursor: pointer;
}

.comment-reply-btn:hover {
  color: #1d4ed8;
}

.comment-edit-btn {
  color: #6b7280;
  cursor: pointer;
}

.comment-edit-btn:hover {
  color: #374151;
}

.comment-delete-btn {
  color: #dc2626;
  cursor: pointer;
}

.comment-delete-btn:hover {
  color: #b91c1c;
}

.comment-replies {
  margin-left: 1.5rem;
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  border-left: 2px solid #f3f4f6;
  padding-left: 1rem;
}

.comment-form {
  margin-top: 1rem;
}

.comment-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  resize: none;
}

.comment-textarea:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  border-color: transparent;
}

.comment-submit-btn {
  margin-top: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #2563eb;
  color: white;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
}

.comment-submit-btn:hover {
  background-color: #1d4ed8;
}

.comment-submit-btn:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
}

.comment-cancel-btn {
  margin-top: 0.5rem;
  margin-left: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #d1d5db;
  color: #374151;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
}

.comment-cancel-btn:hover {
  background-color: #9ca3af;
}

.comment-cancel-btn:focus {
  outline: none;
  ring: 2px;
  ring-color: #6b7280;
}

.comment-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.comment-error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #b91c1c;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
}

.comment-empty {
  text-align: center;
  padding: 2rem 0;
  color: #6b7280;
}

.comment-attachment {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  background-color: #f3f4f6;
  color: #374151;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.comment-mention {
  color: #2563eb;
  font-weight: 500;
}

.comment-category-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: #dbeafe;
  color: #1e40af;
  font-size: 0.75rem;
  border-radius: 9999px;
}