import React from 'react';
import { TypingUser } from '../../types/comments';
import { MessageCircle } from 'lucide-react';

interface TypingIndicatorProps {
  users: TypingUser[];
  className?: string;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({ users, className = '' }) => {
  if (users.length === 0) {
    return null;
  }

  const formatTypingText = () => {
    if (users.length === 1) {
      return `${users[0].userName} is typing...`;
    } else if (users.length === 2) {
      return `${users[0].userName} and ${users[1].userName} are typing...`;
    } else {
      return `${users[0].userName} and ${users.length - 1} others are typing...`;
    }
  };

  return (
    <div className={`typing-indicator ${className}`}>
      <div className="flex items-center space-x-2 text-sm text-gray-500 py-2 border-l-2 border-gray-200 pl-3 my-2">
        <MessageCircle className="w-4 h-4" />
        <span>{formatTypingText()}</span>
        <div className="flex space-x-1">
          <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms', animationDuration: '1.4s' }}></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms', animationDuration: '1.4s' }}></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms', animationDuration: '1.4s' }}></div>
        </div>
      </div>
    </div>
  );
};

export default TypingIndicator;