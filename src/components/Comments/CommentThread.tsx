import React, { useState, useCallback, useMemo } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { Comment, CommentFormData, User } from '../../types/comments';
import CommentForm from './CommentForm';
import { Button } from '../ui/button';
import { ConfirmDialog } from '../ui/confirm-dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { 
  MoreHorizontal,
  Edit,
  Trash2,
  Reply,
  Paperclip,
  AtSign,
  Check,
  X
} from 'lucide-react';
// import './CommentThread.css'; // Temporarily disabled due to Tailwind compilation issues

interface CommentThreadProps {
  comment: Comment;
  onUpdate: (commentId: number, data: Partial<CommentFormData>) => Promise<Comment>;
  onDelete: (commentId: number) => Promise<boolean>;
  onReply: (data: CommentFormData) => Promise<Comment>;
  onTypingStart?: () => void;
  onTypingStop?: () => void;
  currentUser?: User | null;
  allowAttachments?: boolean;
  allowMentions?: boolean;
  maxDepth?: number;
  depth?: number;
  className?: string;
}

const CommentThread: React.FC<CommentThreadProps> = ({
  comment,
  onUpdate,
  onDelete,
  onReply,
  onTypingStart,
  onTypingStop,
  currentUser,
  allowAttachments = true,
  allowMentions = true,
  maxDepth = 3,
  depth = 0,
  className = ''
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isReplying, setIsReplying] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isExpanded, setIsExpanded] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if current user can edit/delete this comment
  const canEdit = currentUser?.id === comment.user_id;
  const canReply = depth < maxDepth;
  const hasReplies = comment.replies && comment.replies.length > 0;

  // Format comment content with mentions
  const formattedContent = useMemo(() => {
    let content = comment.text || '';
    
    // Replace mention patterns with styled mentions
    const mentionPattern = /@\[(\d+)\]/g;
    content = content.replace(mentionPattern, (match, userId) => {
      const mention = comment.mentions?.find(m => m.mentioned_user_id === parseInt(userId));
      if (mention) {
        return `<span class="mention" data-user-id="${userId}">@${mention.user_name}</span>`;
      }
      return match;
    });
    
    return content;
  }, [comment.text, comment.mentions]);

  // Handle comment update
  const handleUpdate = async (formData: CommentFormData) => {
    try {
      setLoading(true);
      setError(null);
      
      await onUpdate(comment.id, {
        content: formData.content
      });
      
      setIsEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update comment');
    } finally {
      setLoading(false);
    }
  };

  // Handle comment deletion
  const handleDelete = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const success = await onDelete(comment.id);
      if (success) {
        setShowDeleteConfirm(false);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete comment');
    } finally {
      setLoading(false);
    }
  };

  // Handle reply submission
  const handleReply = async (formData: CommentFormData) => {
    try {
      setLoading(true);
      setError(null);
      
      const replyData = {
        ...formData,
        parentId: comment.id
      };
      
      await onReply(replyData);
      setIsReplying(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to post reply');
    } finally {
      setLoading(false);
    }
  };



  // Render comment actions menu
  const renderActionsMenu = () => {
    const menuItems = [];

    if (canEdit) {
      menuItems.push({
        label: 'Edit',
        icon: <Edit className="w-4 h-4" />,
        onClick: () => setIsEditing(true)
      });
      
      menuItems.push({
        label: 'Delete',
        icon: <Trash2 className="w-4 h-4" />,
        onClick: () => setShowDeleteConfirm(true),
        className: 'text-red-600 hover:text-red-700'
      });
    }

    if (canReply) {
      menuItems.push({
        label: 'Reply',
        icon: <Reply className="w-4 h-4" />,
        onClick: () => setIsReplying(true)
      });
    }

    return menuItems.length > 0 ? (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="comment-actions-trigger" title="Comment actions">
            <MoreHorizontal className="w-5 h-5" />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {menuItems.map((item, index) => (
            <DropdownMenuItem
              key={index}
              onClick={item.onClick}
              className={item.className}
            >
              {item.icon && <span className="mr-2">{item.icon}</span>}
              {item.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    ) : null;
  };

  // Render attachments
  const renderAttachments = () => {
    if (!comment.attachments || comment.attachments.length === 0) {
      return null;
    }

    return (
      <div className="comment-attachments">
        <div className="attachments-header">
          <Paperclip className="w-4 h-4" />
          <span>Attachments ({comment.attachments.length})</span>
        </div>
        <div className="attachments-list">
          {comment.attachments.map((attachment, index) => (
            <div key={index} className="attachment-item">
              <a 
                href={`/api/attachments/${attachment.id}/download`}
                download={attachment.original_name}
                className="attachment-link"
              >
                <span className="attachment-name">{attachment.original_name}</span>
                <span className="attachment-size">
                  ({(attachment.file_size / 1024 / 1024).toFixed(2)} MB)
                </span>
              </a>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render mentions
  const renderMentions = () => {
    if (!comment.mentions || comment.mentions.length === 0) {
      return null;
    }

    return (
      <div className="comment-mentions">
        <AtSign className="w-4 h-4" />
        <span>Mentioned: </span>
        {comment.mentions.map((mention, index) => (
          <span key={mention.id} className="mentioned-user">
            {mention.user_name}
            {index < comment.mentions!.length - 1 && ', '}
          </span>
        ))}
      </div>
    );
  };

  const timeAgo = formatDistanceToNow(new Date(comment.created_at), { addSuffix: true });
  const isEdited = comment.updated_at !== comment.created_at;

  return (
    <div className={`comment-thread ${className}`} data-depth={depth}>
      <div className="comment-item">
        <div className="comment-header">
          <div className="comment-author">
            <img 
              src={comment.user_avatar || '/default-avatar.png'} 
              alt={comment.user_name}
              className="author-avatar"
            />
            <div className="author-info">
              <span className="author-name">{comment.user_name}</span>
              <span className="comment-time" title={new Date(comment.created_at).toLocaleString()}>
                {timeAgo}
                {isEdited && <span className="edited-indicator"> (edited)</span>}
              </span>
            </div>
          </div>
          
          <div className="comment-meta">
            {renderActionsMenu()}
          </div>
        </div>

        <div className="comment-body">
          {isEditing ? (
            <CommentForm
              onSubmit={handleUpdate}
              onCancel={() => setIsEditing(false)}
              initialData={{
                content: comment.text
              }}
              submitting={loading}
              allowAttachments={false} // Don't allow attachment changes in edit mode
              allowMentions={allowMentions}
              submitButtonText="Update Comment"
              className="edit-comment-form"
              autoFocus
            />
          ) : (
            <>
              <div 
                className="comment-content"
                dangerouslySetInnerHTML={{ __html: formattedContent }}
              />
              
              {renderAttachments()}
              {renderMentions()}
            </>
          )}
        </div>

        {error && (
          <div className="comment-error">
            {error}
          </div>
        )}

        {!isEditing && (
          <div className="comment-actions">
            {canReply && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsReplying(true)}
                className="reply-button"
              >
                <Reply className="w-4 h-4" />
                Reply
              </Button>
            )}
            
            {hasReplies && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="toggle-replies-button"
              >
                {isExpanded ? 'Hide' : 'Show'} {comment.replies!.length} 
                {comment.replies!.length === 1 ? 'reply' : 'replies'}
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Reply form */}
      {isReplying && (
        <div className="reply-form-container">
          <CommentForm
            onSubmit={handleReply}
            onCancel={() => setIsReplying(false)}
            onTypingStart={onTypingStart}
            onTypingStop={onTypingStop}
            submitting={loading}
            allowAttachments={allowAttachments}
            allowMentions={allowMentions}
            placeholder={`Reply to ${comment.user_name}...`}
            submitButtonText="Post Reply"
            className="reply-form"
            autoFocus
          />
        </div>
      )}

      {/* Nested replies */}
      {hasReplies && isExpanded && (
        <div className="comment-replies">
          {comment.replies!.map(reply => (
            <CommentThread
              key={reply.id}
              comment={reply}
              onUpdate={onUpdate}
              onDelete={onDelete}
              onReply={onReply}
              onTypingStart={onTypingStart}
              onTypingStop={onTypingStop}
              currentUser={currentUser}
              allowAttachments={allowAttachments}
              allowMentions={allowMentions}
              maxDepth={maxDepth}
              depth={depth + 1}
              className="nested-comment"
            />
          ))}
        </div>
      )}

      {/* Delete confirmation dialog */}
      <ConfirmDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        title="Delete Comment"
        description="Are you sure you want to delete this comment? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDelete}
        onCancel={() => setShowDeleteConfirm(false)}
        variant="destructive"
      />
    </div>
  );
};

export default CommentThread;