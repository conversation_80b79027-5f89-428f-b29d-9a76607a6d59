import React, { useState, useEffect, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from '../../hooks/useAuth';
import { CommentsAPI } from '../../services/api/comments';
import CommentForm from './CommentForm';
import CommentThread from './CommentThread';
import TypingIndicator from './TypingIndicator';
import LoadingSpinner from '../ui/LoadingSpinner';
import ErrorMessage from '../ui/ErrorMessage';
import { Comment, CommentFormData, CommentStats as ICommentStats } from '../../types/comments';
// import './Comments.css'; // Temporarily disabled due to Tailwind compilation issues

interface CommentsProps {
  objectType: 'vendor' | 'contract' | 'document' | 'user' | 'project';
  objectId: number;
  category?: string;
  allowAttachments?: boolean;
  allowMentions?: boolean;
  maxDepth?: number;
  className?: string;
}

const Comments: React.FC<CommentsProps> = ({
  objectType,
  objectId,
  category,
  allowAttachments = true,
  allowMentions = true,
  maxDepth = 3,
  className = ''
}) => {
  const { user, token } = useAuth();
  const [comments, setComments] = useState<Comment[]>([]);
  const [stats, setStats] = useState<ICommentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [typingUsers, setTypingUsers] = useState<Array<{ userId: number; userName: string }>>([]);
  const [onlineUsers, setOnlineUsers] = useState<Array<{ id: number; name: string; email: string }>>([]);
  const [filter, setFilter] = useState<{
    category?: string;
    status?: string;
    sortBy: 'newest' | 'oldest' | 'most_replies';
  }>({ sortBy: 'newest' });
  
  const socketRef = useRef<Socket | null>(null);
  const roomId = `${objectType}-${objectId}`;

  // Initialize socket connection
  useEffect(() => {
    if (!token || !user) return;

    const socket = io('http://localhost:3001', {
      auth: { token },
      transports: ['websocket', 'polling']
    });

    socketRef.current = socket;

    // Socket event handlers
    socket.on('connected', (data) => {
      console.log('Connected to comments socket:', data);
    });

    socket.on('comment:created', (data) => {
      handleCommentCreated(data.comment);
    });

    socket.on('comment:updated', (data) => {
      handleCommentUpdated(data.comment);
    });

    socket.on('comment:deleted', (data) => {
      handleCommentDeleted(data.commentId);
    });

    socket.on('user-typing', (data) => {
      handleTypingUpdate(data);
    });

    socket.on('user-joined-room', (data) => {
      console.log(`${data.userName} joined the conversation`);
    });

    socket.on('user-left-room', (data) => {
      console.log(`${data.userName} left the conversation`);
    });

    socket.on('room-joined', (data) => {
      console.log(`Joined room ${data.roomId} with ${data.userCount} users`);
    });

    socket.on('error', (error) => {
      console.error('Socket error:', error);
      setError(error.message || 'Connection error');
    });

    // Join the comment room
    socket.emit('join-comment-room', { objectType, objectId });

    return () => {
      socket.emit('leave-comment-room', { objectType, objectId });
      socket.disconnect();
    };
  }, [token, user, objectType, objectId]);

  // Load comments and stats
  const loadComments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [commentsResponse, statsResponse] = await Promise.all([
        CommentsAPI.getComments(objectType, objectId, { category: filter.category }),
        CommentsAPI.getStats({ objectType, objectId })
      ]);
      
      setComments(commentsResponse.data);
      setStats(statsResponse.data);
    } catch (err: unknown) {
      console.error('Error loading comments:', err);
      const error = err as { response?: { data?: { message?: string } } };
      setError(error.response?.data?.message || 'Failed to load comments');
    } finally {
      setLoading(false);
    }
  }, [objectType, objectId, filter.category]);

  useEffect(() => {
    loadComments();
  }, [loadComments]);

  // Socket event handlers
  const handleCommentCreated = useCallback((newComment: Comment) => {
    setComments(prev => {
      // Check if comment already exists to avoid duplicates
      if (prev.find(c => c.id === newComment.id)) {
        return prev;
      }
      return [...prev, newComment];
    });
    
    // Update stats
    setStats(prev => prev ? {
      ...prev,
      total_comments: prev.total_comments + 1,
      active_comments: prev.active_comments + 1
    } : null);
  }, []);

  const handleCommentUpdated = useCallback((updatedComment: Comment) => {
    setComments(prev => 
      prev.map(comment => 
        comment.id === updatedComment.id ? updatedComment : comment
      )
    );
  }, []);

  const handleCommentDeleted = useCallback((commentId: number) => {
    setComments(prev => prev.filter(comment => comment.id !== commentId));
    
    // Update stats
    setStats(prev => prev ? {
      ...prev,
      total_comments: prev.total_comments - 1,
      active_comments: prev.active_comments - 1
    } : null);
  }, []);

  const handleTypingUpdate = useCallback((data: {
    userId: number;
    userName: string;
    isTyping: boolean;
  }) => {
    setTypingUsers(prev => {
      if (data.isTyping) {
        // Add user to typing list if not already there
        if (!prev.find(u => u.userId === data.userId)) {
          return [...prev, { userId: data.userId, userName: data.userName }];
        }
        return prev;
      } else {
        // Remove user from typing list
        return prev.filter(u => u.userId !== data.userId);
      }
    });
  }, []);

  // Handle comment submission
  const handleCommentSubmit = async (formData: CommentFormData) => {
    if (!user) return;

    try {
      setSubmitting(true);
      setError(null);

      const commentData = {
        entity_type: objectType,
        entity_id: objectId,
        content: formData.content,
        parent_comment_id: formData.parentId
      };

      const response = await CommentsAPI.createComment(commentData);
      const newComment = response.data;

      // Emit to socket for real-time updates
      if (socketRef.current) {
        socketRef.current.emit('comment-create', {
          objectType,
          objectId,
          comment: newComment
        });
      }

      // Handle file attachments if any
      if (formData.attachments && formData.attachments.length > 0) {
        await CommentsAPI.uploadAttachments(newComment.id, formData.attachments);
      }

      // Refresh comments to get the latest data
      await loadComments();
      
      return newComment;
    } catch (err: unknown) {
      console.error('Error creating comment:', err);
      const error = err as { response?: { data?: { message?: string } } };
      const errorMessage = error.response?.data?.message || 'Failed to create comment';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle comment update
  const handleCommentUpdate = async (commentId: number, updateData: Partial<CommentFormData>) => {
    try {
      const response = await CommentsAPI.updateComment(commentId, updateData);
      const updatedComment = response.data;

      // Emit to socket for real-time updates
      if (socketRef.current) {
        socketRef.current.emit('comment-update', {
          objectType,
          objectId,
          comment: updatedComment
        });
      }

      return updatedComment;
    } catch (err: unknown) {
      console.error('Error updating comment:', err);
      const error = err as { response?: { data?: { message?: string } } };
      const errorMessage = error.response?.data?.message || 'Failed to update comment';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Handle comment deletion
  const handleCommentDelete = async (commentId: number) => {
    try {
      await CommentsAPI.deleteComment(commentId);
      
      // The socket event will handle UI updates
      return true;
    } catch (err: unknown) {
      console.error('Error deleting comment:', err);
      const error = err as { response?: { data?: { message?: string } } };
      const errorMessage = error.response?.data?.message || 'Failed to delete comment';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Handle typing indicators
  const handleTypingStart = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.emit('typing-start', { objectType, objectId });
    }
  }, [objectType, objectId]);

  const handleTypingStop = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.emit('typing-stop', { objectType, objectId });
    }
  }, [objectType, objectId]);

  // Sort comments based on filter
  const sortedComments = React.useMemo(() => {
    const sorted = [...comments];
    
    switch (filter.sortBy) {
      case 'oldest':
        return sorted.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      case 'newest':
        return sorted.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      case 'most_replies':
        return sorted.sort((a, b) => (b.replies?.length || 0) - (a.replies?.length || 0));
      default:
        return sorted;
    }
  }, [comments, filter.sortBy]);

  if (loading) {
    return (
      <div className={`comments-container ${className}`}>
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className={`comments-container ${className}`}>
      <div className="comments-header">
        <h3 className="comments-title">
          Comments {stats && `(${stats.total_comments})`}
        </h3>
        

        
        <div className="comments-filters">
          <select 
            value={filter.sortBy} 
            onChange={(e) => setFilter(prev => ({ ...prev, sortBy: e.target.value as 'newest' | 'oldest' | 'most_replies' }))}
            className="sort-select"
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="most_replies">Most Replies</option>
          </select>
          
          {category && (
            <select 
              value={filter.category || ''} 
              onChange={(e) => setFilter(prev => ({ ...prev, category: e.target.value || undefined }))}
              className="category-select"
            >
              <option value="">All Categories</option>
              <option value="general">General</option>
              <option value="legal">Legal</option>
              <option value="financial">Financial</option>
              <option value="technical">Technical</option>
              <option value="performance">Performance</option>
              <option value="compliance">Compliance</option>
            </select>
          )}
        </div>
      </div>

      {error && (
        <ErrorMessage 
          message={error} 
          onDismiss={() => setError(null)}
          className="comments-error"
        />
      )}

      <div className="comments-form-section">
        <CommentForm
          onSubmit={handleCommentSubmit}
          onTypingStart={handleTypingStart}
          onTypingStop={handleTypingStop}
          submitting={submitting}
          allowAttachments={allowAttachments}
          allowMentions={allowMentions}
          category={category}
          placeholder="Add a comment..."
          className="main-comment-form"
        />
      </div>

      {typingUsers.length > 0 && (
        <TypingIndicator 
          users={typingUsers}
          className="comments-typing"
        />
      )}

      <div className="comments-list">
        {sortedComments.length === 0 ? (
          <div className="no-comments">
            <p>No comments yet. Be the first to comment!</p>
          </div>
        ) : (
          sortedComments.map(comment => (
            <CommentThread
              key={comment.id}
              comment={comment}
              onUpdate={handleCommentUpdate}
              onDelete={handleCommentDelete}
              onReply={handleCommentSubmit}
              onTypingStart={handleTypingStart}
              onTypingStop={handleTypingStop}
              currentUser={user ? { ...user, id: Number(user.id) } : undefined}
              allowAttachments={allowAttachments}
              allowMentions={allowMentions}
              maxDepth={maxDepth}
              className="comment-thread"
            />
          ))
        )}
      </div>
    </div>
  );
};

export default Comments;