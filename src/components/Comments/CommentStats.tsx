import React from 'react';
import { CommentStats as ICommentStats } from '../../types/comments';
import { MessageCircle, CheckCircle, Users, MessageSquare } from 'lucide-react';

interface CommentStatsProps {
  stats: ICommentStats;
  className?: string;
}

const CommentStats: React.FC<CommentStatsProps> = ({ stats, className = '' }) => {
  const statItems = [
    {
      label: 'Total',
      value: stats.total_comments,
      icon: MessageCircle,
      color: 'text-blue-600'
    },
    {
      label: 'Active',
      value: stats.active_comments,
      icon: MessageSquare,
      color: 'text-green-600'
    },
    {
      label: 'Resolved',
      value: stats.resolved_comments,
      icon: CheckCircle,
      color: 'text-gray-600'
    },
    {
      label: 'Replies',
      value: stats.replies,
      icon: MessageCircle,
      color: 'text-purple-600'
    },
    {
      label: 'Contributors',
      value: stats.unique_commenters,
      icon: Users,
      color: 'text-orange-600'
    }
  ];

  return (
    <div className={`comment-stats ${className}`}>
      <div className="stats-grid grid grid-cols-2 md:grid-cols-5 gap-4">
        {statItems.map((item, index) => {
          const Icon = item.icon;
          return (
            <div key={index} className="stat-item flex flex-col items-center p-3 bg-gray-50 rounded-lg">
              <Icon className={`w-5 h-5 ${item.color} mb-1`} />
              <span className="text-lg font-semibold text-gray-900">{item.value}</span>
              <span className="text-xs text-gray-600">{item.label}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CommentStats;