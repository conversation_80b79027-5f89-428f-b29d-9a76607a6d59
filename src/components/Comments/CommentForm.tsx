import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { usersApi } from '../../services/api/users';
import RichTextEditor from '../ui/RichTextEditor';
import FileUpload from '../ui/FileUpload';
import UserMentions from '../ui/UserMentions';
import { Button } from '../ui/button';
import { CommentFormData, User } from '../../types/comments';
import { Paperclip, AtSign, X } from 'lucide-react';
// import './CommentForm.css'; // Temporarily disabled

interface CommentFormProps {
  onSubmit: (data: CommentFormData) => Promise<void>;
  onTypingStart?: () => void;
  onTypingStop?: () => void;
  initialData?: Partial<CommentFormData>;
  submitting?: boolean;
  allowAttachments?: boolean;
  allowMentions?: boolean;
  category?: string;
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
  onCancel?: () => void;
  submitButtonText?: string;
}

const CommentForm: React.FC<CommentFormProps> = ({
  onSubmit,
  onTypingStart,
  onTypingStop,
  initialData,
  submitting = false,
  allowAttachments = true,
  allowMentions = true,
  category,
  placeholder = 'Write a comment...',
  className = '',
  autoFocus = false,
  onCancel,
  submitButtonText = 'Post Comment'
}) => {
  const { user } = useAuth();
  const [content, setContent] = useState(initialData?.content || '');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [mentions, setMentions] = useState<User[]>([]);
  const [showAttachments, setShowAttachments] = useState(false);
  const [showMentions, setShowMentions] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const editorRef = useRef<{ focus: () => void; setContent: (content: string) => void } | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const formRef = useRef<HTMLFormElement>(null);

  // Handle typing indicators
  const handleTypingStart = useCallback(() => {
    if (!isTyping) {
      setIsTyping(true);
      onTypingStart?.();
    }
    
    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Set new timeout to stop typing after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      onTypingStop?.();
    }, 3000);
  }, [isTyping, onTypingStart, onTypingStop]);

  const handleTypingStop = useCallback(() => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    setIsTyping(false);
    onTypingStop?.();
  }, [onTypingStop]);

  // Handle content changes
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);
    setError(null);
    
    if (newContent.trim()) {
      handleTypingStart();
    } else {
      handleTypingStop();
    }
  }, [handleTypingStart, handleTypingStop]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim()) {
      setError('Comment content is required');
      return;
    }

    if (content.length > 5000) {
      setError('Comment is too long. Maximum 5000 characters allowed.');
      return;
    }

    try {
      setError(null);
      handleTypingStop();
      
      const formData: CommentFormData = {
          content: content.trim(),
          attachments: attachments.length > 0 ? attachments : undefined,
          mentions: mentions.length > 0 ? mentions.map(m => m.id) : undefined,
          parentId: initialData?.parentId
        };

      await onSubmit(formData);
      
      // Reset form after successful submission
      setContent('');
      setAttachments([]);
      setMentions([]);
      setShowAttachments(false);
      setShowMentions(false);
      
      // Focus back to editor
      if (editorRef.current) {
        editorRef.current.focus();
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to submit comment';
      setError(errorMessage);
    }
  };

  // Handle file attachments
  const handleAttachmentsChange = useCallback((files: File[]) => {
    setAttachments(files);
    setError(null);
  }, []);

  const removeAttachment = useCallback((index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  }, []);

  // Handle mentions
  const handleMentionSelect = useCallback((user: User) => {
    if (!mentions.find(m => m.id === user.id)) {
      setMentions(prev => [...prev, user]);
      
      // Insert mention into content
      const mentionText = `@[${user.id}]`;
      const newContent = content + mentionText;
      setContent(newContent);
    }
    setShowMentions(false);
  }, [mentions, content]);

  const removeMention = useCallback((userId: number) => {
    setMentions(prev => prev.filter(m => m.id !== userId));
    
    // Remove mention from content
    const mentionPattern = new RegExp(`@[${userId}]`, 'g');
    const newContent = content.replace(mentionPattern, '');
    setContent(newContent);
  }, [content]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      if (formRef.current) {
        formRef.current.requestSubmit();
      }
    }
    
    if (e.key === 'Escape' && onCancel) {
      e.preventDefault();
      onCancel();
    }
  }, [onCancel]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      handleTypingStop();
    };
  }, [handleTypingStop]);

  const isFormValid = content.trim().length > 0 && content.length <= 5000;
  const characterCount = content.length;
  const isNearLimit = characterCount > 4500;

  return (
    <form 
      ref={formRef}
      onSubmit={handleSubmit} 
      className={`comment-form ${className}`}
      onKeyDown={handleKeyDown}
    >
      <div className="comment-form-header">
        <div className="user-avatar">
          <img 
            src={'/default-avatar.png'} 
            alt={user?.name || 'User'}
            className="avatar-image"
          />
        </div>
        
        <div className="form-meta">
          <span className="user-name">{user?.name}</span>
        </div>
      </div>

      <div className="comment-form-content">
        <RichTextEditor
          ref={editorRef}
          value={content}
          onChange={handleContentChange}
          placeholder={placeholder}
          autoFocus={autoFocus}
          maxLength={5000}
          className="comment-editor"
        />
        
        <div className="character-counter">
          <span className={isNearLimit ? 'near-limit' : ''}>
            {characterCount}/5000
          </span>
        </div>
      </div>

      {/* Mentions Display */}
      {mentions.length > 0 && (
        <div className="mentions-list">
          <span className="mentions-label">Mentioning:</span>
          {mentions.map(mention => (
            <span key={mention.id} className="mention-tag">
              @{mention.name}
              <button
                type="button"
                onClick={() => removeMention(mention.id)}
                className="remove-mention"
                aria-label={`Remove mention of ${mention.name}`}
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      {/* Attachments Display */}
      {attachments.length > 0 && (
        <div className="attachments-list">
          <span className="attachments-label">Attachments:</span>
          {attachments.map((file, index) => (
            <div key={index} className="attachment-item">
              <span className="attachment-name">{file.name}</span>
              <span className="attachment-size">
                ({(file.size / 1024 / 1024).toFixed(2)} MB)
              </span>
              <button
                type="button"
                onClick={() => removeAttachment(index)}
                className="remove-attachment"
                aria-label={`Remove ${file.name}`}
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      )}

      {error && (
        <div className="form-error">
          {error}
        </div>
      )}

      <div className="comment-form-actions">
        <div className="form-tools">
          {allowAttachments && (
            <div className="tool-group">
              <button
                type="button"
                onClick={() => setShowAttachments(!showAttachments)}
                className={`tool-button ${showAttachments ? 'active' : ''}`}
                title="Add attachments"
              >
                <Paperclip className="w-5 h-5" />
              </button>
              
              {showAttachments && (
                <div className="tool-dropdown">
                  <FileUpload
                    onFilesSelected={handleAttachmentsChange}
                    maxFiles={5}
                    maxFileSize={10 * 1024 * 1024} // 10MB
                    acceptedTypes={[
                      'image/*',
                      'application/pdf',
                      'application/msword',
                      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                      'text/plain',
                      'text/csv',
                      'application/vnd.ms-excel',
                      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    ]}
                    className="attachment-uploader"
                  />
                </div>
              )}
            </div>
          )}
          
          {allowMentions && (
            <div className="tool-group">
              <button
                type="button"
                onClick={() => setShowMentions(!showMentions)}
                className={`tool-button ${showMentions ? 'active' : ''}`}
                title="Mention users"
              >
                <AtSign className="w-5 h-5" />
              </button>
              
              {showMentions && (
                <div className="tool-dropdown">
                  <UserMentions
                    onMention={handleMentionSelect}
                    className="mentions-selector"
                  />
                </div>
              )}
            </div>
          )}
        </div>

        <div className="form-buttons">
          {onCancel && (
            <Button
              type="button"
              variant="secondary"
              onClick={onCancel}
              disabled={submitting}
              className="cancel-button"
            >
              Cancel
            </Button>
          )}
          
          <Button
            type="submit"
            variant="default"
            disabled={!isFormValid || submitting}
            className="submit-button"
          >
            {submitting ? 'Posting...' : submitButtonText}
          </Button>
        </div>
      </div>
      
      <div className="form-help">
        <span className="keyboard-shortcut">
          Press Ctrl+Enter to submit
        </span>
      </div>
    </form>
  );
};

export default CommentForm;