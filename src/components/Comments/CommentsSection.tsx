import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { MessageSquare } from 'lucide-react';
import Comments from './Comments';
import { useAuth } from '../../hooks/useAuth';

interface CommentPermissions {
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canResolve: boolean;
  canViewAll: boolean;
}

interface CommentsSectionProps {
  objectType: 'vendor' | 'contract';
  objectId: number;
  permissions?: CommentPermissions;
  categories?: string[];
  showFilters?: boolean;
  className?: string;
}

const CommentsSection: React.FC<CommentsSectionProps> = ({
  objectType,
  objectId,
  permissions,
  categories = ['general'],
  showFilters = true,
  className = ''
}) => {
  const { canEdit, canDelete } = useAuth();

  // Default permissions based on user auth
  const defaultPermissions: CommentPermissions = {
    canCreate: true,
    canEdit: canEdit(),
    canDelete: canDelete(),
    canResolve: canEdit(),
    canViewAll: true
  };

  const effectivePermissions = permissions || defaultPermissions;

  return (
    <Card className={`card-neumorphic ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MessageSquare className="w-5 h-5 text-primary" />
          <span>Discussion</span>
        </CardTitle>
        <CardDescription>
          Team comments and notes about this {objectType}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Comments
          objectType={objectType}
          objectId={objectId}
          category={categories[0]} // Use first category as default
          allowAttachments={effectivePermissions.canCreate}
          allowMentions={effectivePermissions.canCreate}
          maxDepth={3}
          className="comments-section"
        />
      </CardContent>
    </Card>
  );
};

export default CommentsSection;