/* CommentThread.css */

.comment-thread {
  @apply border-l-2 border-gray-200 pl-4 mb-4;
}

.comment-thread.depth-0 {
  @apply border-l-0 pl-0;
}

.comment-thread.depth-1 {
  @apply border-l-blue-200;
}

.comment-thread.depth-2 {
  @apply border-l-green-200;
}

.comment-thread.depth-3 {
  @apply border-l-yellow-200;
}

.comment-thread.depth-4 {
  @apply border-l-purple-200;
}

.comment-thread.depth-5 {
  @apply border-l-pink-200;
}

.comment-item {
  @apply bg-white rounded-lg border border-gray-200 p-4 mb-3 shadow-sm;
}

.comment-item.editing {
  @apply border-blue-300 bg-blue-50;
}

.comment-item.resolved {
  @apply bg-green-50 border-green-200;
}

.comment-item.deleted {
  @apply bg-gray-50 border-gray-300 opacity-75;
}

.comment-header {
  @apply flex items-center justify-between mb-3;
}

.comment-author {
  @apply flex items-center space-x-2;
}

.comment-avatar {
  @apply w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700;
}

.comment-author-info {
  @apply flex flex-col;
}

.comment-author-name {
  @apply text-sm font-medium text-gray-900;
}

.comment-timestamp {
  @apply text-xs text-gray-500;
}

.comment-actions {
  @apply flex items-center space-x-2;
}

.comment-actions-trigger {
  @apply p-1 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors;
}

.comment-content {
  @apply text-gray-800 leading-relaxed mb-3;
}

.comment-content.html {
  @apply prose prose-sm max-w-none;
}

.comment-content.html h1,
.comment-content.html h2,
.comment-content.html h3,
.comment-content.html h4,
.comment-content.html h5,
.comment-content.html h6 {
  @apply mt-4 mb-2;
}

.comment-content.html p {
  @apply mb-2;
}

.comment-content.html ul,
.comment-content.html ol {
  @apply mb-2 pl-4;
}

.comment-content.html blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600;
}

.comment-content.html code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm font-mono;
}

.comment-content.html pre {
  @apply bg-gray-100 p-3 rounded overflow-x-auto;
}

.comment-content.html pre code {
  @apply bg-transparent p-0;
}

.comment-status {
  @apply inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium;
}

.comment-status.resolved {
  @apply bg-green-100 text-green-800;
}

.comment-status.active {
  @apply bg-blue-100 text-blue-800;
}

.comment-category {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800;
}

.comment-edited {
  @apply text-xs text-gray-500 italic;
}

.comment-attachments {
  @apply mt-3 p-3 bg-gray-50 rounded-md border;
}

.attachments-header {
  @apply flex items-center space-x-2 mb-2 text-sm font-medium text-gray-700;
}

.attachments-list {
  @apply space-y-2;
}

.attachment-item {
  @apply flex items-center justify-between p-2 bg-white rounded border hover:bg-gray-50 transition-colors;
}

.attachment-info {
  @apply flex items-center space-x-2;
}

.attachment-icon {
  @apply w-4 h-4 text-gray-500;
}

.attachment-details {
  @apply flex flex-col;
}

.attachment-name {
  @apply text-sm font-medium text-gray-900;
}

.attachment-size {
  @apply text-xs text-gray-500;
}

.attachment-actions {
  @apply flex items-center space-x-1;
}

.attachment-download {
  @apply p-1 rounded hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors;
}

.comment-mentions {
  @apply flex items-center space-x-2 mt-2 text-sm text-gray-600;
}

.mention-user {
  @apply text-blue-600 hover:text-blue-800 font-medium;
}

.comment-footer {
  @apply flex items-center justify-between mt-3 pt-3 border-t border-gray-100;
}

.comment-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500;
}

.reply-button {
  @apply inline-flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors;
}

.comment-replies {
  @apply mt-4 space-y-3;
}

.comment-form-container {
  @apply mt-4 p-4 bg-gray-50 rounded-lg border;
}

.comment-form-container.editing {
  @apply bg-blue-50 border-blue-200;
}

.comment-form-container.replying {
  @apply bg-green-50 border-green-200;
}

.error-message {
  @apply mt-2 p-3 bg-red-50 border border-red-200 rounded-md text-sm text-red-700;
}

.loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg;
}

.loading-spinner {
  @apply animate-spin h-5 w-5 text-blue-600;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .comment-thread {
    @apply pl-2;
  }
  
  .comment-item {
    @apply p-3;
  }
  
  .comment-header {
    @apply flex-col items-start space-y-2;
  }
  
  .comment-actions {
    @apply self-end;
  }
  
  .comment-footer {
    @apply flex-col items-start space-y-2;
  }
  
  .comment-meta {
    @apply flex-wrap;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .comment-item {
    @apply bg-gray-800 border-gray-700;
  }
  
  .comment-item.editing {
    @apply bg-blue-900 border-blue-600;
  }
  
  .comment-item.resolved {
    @apply bg-green-900 border-green-600;
  }
  
  .comment-author-name {
    @apply text-gray-100;
  }
  
  .comment-timestamp {
    @apply text-gray-400;
  }
  
  .comment-content {
    @apply text-gray-200;
  }
  
  .comment-attachments {
    @apply bg-gray-700 border-gray-600;
  }
  
  .attachment-item {
    @apply bg-gray-800 border-gray-600;
  }
  
  .attachment-item:hover {
    @apply bg-gray-700;
  }
}

/* Animation for new comments */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.comment-item.new {
  animation: slideIn 0.3s ease-out;
}

/* Hover effects */
.comment-item:hover {
  @apply shadow-md;
}

.comment-item:hover .comment-actions-trigger {
  @apply opacity-100;
}

.comment-actions-trigger {
  @apply opacity-0 transition-opacity;
}

/* Focus states */
.comment-actions-trigger:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

.reply-button:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Print styles */
@media print {
  .comment-actions,
  .reply-button,
  .comment-form-container {
    @apply hidden;
  }
  
  .comment-item {
    @apply shadow-none border-gray-300;
  }
}