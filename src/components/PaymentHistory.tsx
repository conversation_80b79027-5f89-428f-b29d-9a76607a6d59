import * as React from 'react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  CreditCard, 
  DollarSign,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { cn } from '@/lib/utils';
import { PaymentRecord } from '@/store/slices/invoicesSlice';

export interface PaymentHistoryProps {
  payments: PaymentRecord[];
  className?: string;
}

const getStatusIcon = (status: string) => {
  switch (status.toLowerCase()) {
    case 'completed':
    case 'success':
      return CheckCircle;
    case 'failed':
    case 'error':
      return XCircle;
    case 'pending':
    case 'processing':
      return Clock;
    case 'refunded':
      return RefreshCw;
    default:
      return AlertTriangle;
  }
};

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'completed':
    case 'success':
      return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300';
    case 'failed':
    case 'error':
      return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-300';
    case 'pending':
    case 'processing':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-300';
    case 'refunded':
      return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-300';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900 dark:text-gray-300';
  }
};

const getGatewayIcon = (gateway: string) => {
  switch (gateway.toLowerCase()) {
    case 'stripe':
      return CreditCard;
    case 'paypal':
      return DollarSign;
    case 'bank':
    case 'bank_transfer':
      return DollarSign;
    default:
      return CreditCard;
  }
};

const formatCurrency = (amount: number, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount);
};

export function PaymentHistory({ payments, className }: PaymentHistoryProps) {
  if (!payments || payments.length === 0) {
    return (
      <Card className={cn('card-neumorphic', className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Payment History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <DollarSign className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">No payments yet</h3>
            <p className="text-muted-foreground">
              Payment history will appear here once payments are processed.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('card-neumorphic', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="w-5 h-5" />
          Payment History
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {payments.map((payment, index) => {
            const StatusIcon = getStatusIcon(payment.status);
            const GatewayIcon = getGatewayIcon(payment.gateway);
            
            return (
              <motion.div
                key={payment.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-4 rounded-xl border border-border hover:bg-accent/50 transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className={`p-2 rounded-lg ${
                    payment.status === 'completed' ? 'bg-green-100 dark:bg-green-900' :
                    payment.status === 'failed' ? 'bg-red-100 dark:bg-red-900' :
                    payment.status === 'pending' ? 'bg-yellow-100 dark:bg-yellow-900' :
                    'bg-gray-100 dark:bg-gray-900'
                  }`}>
                    <StatusIcon className={`w-5 h-5 ${
                      payment.status === 'completed' ? 'text-green-600 dark:text-green-400' :
                      payment.status === 'failed' ? 'text-red-600 dark:text-red-400' :
                      payment.status === 'pending' ? 'text-yellow-600 dark:text-yellow-400' :
                      'text-gray-600 dark:text-gray-400'
                    }`} />
                  </div>
                  
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-foreground">
                        {formatCurrency(payment.amount)}
                      </span>
                      <Badge className={getStatusColor(payment.status)}>
                        {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <GatewayIcon className="w-4 h-4" />
                        <span className="capitalize">{payment.gateway}</span>
                      </div>
                      
                      {payment.transactionId && (
                        <span>ID: {payment.transactionId}</span>
                      )}
                      
                      <span>
                        {format(new Date(payment.processedAt), 'MMM dd, yyyy HH:mm')}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-sm text-muted-foreground">
                    {payment.method && (
                      <div className="capitalize">{payment.method}</div>
                    )}
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
        
        {/* Summary */}
        <div className="mt-6 pt-4 border-t border-border">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Total Payments:</span>
            <span className="font-semibold text-foreground">
              {formatCurrency(
                payments
                  .filter(p => p.status === 'completed')
                  .reduce((sum, p) => sum + p.amount, 0)
              )}
            </span>
          </div>
          
          <div className="flex justify-between items-center mt-2">
            <span className="text-sm text-muted-foreground">Successful Transactions:</span>
            <span className="text-sm text-foreground">
              {payments.filter(p => p.status === 'completed').length} of {payments.length}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}