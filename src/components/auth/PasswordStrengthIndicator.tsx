import React from 'react';
import { motion } from 'framer-motion';
import { Check, X } from 'lucide-react';

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
}

interface PasswordRequirement {
  label: string;
  test: (password: string) => boolean;
}

const requirements: PasswordRequirement[] = [
  {
    label: 'At least 8 characters',
    test: (password) => password.length >= 8,
  },
  {
    label: 'Contains uppercase letter',
    test: (password) => /[A-Z]/.test(password),
  },
  {
    label: 'Contains lowercase letter',
    test: (password) => /[a-z]/.test(password),
  },
  {
    label: 'Contains number',
    test: (password) => /\d/.test(password),
  },
];

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  className = '',
}) => {
  const getStrengthScore = () => {
    return requirements.reduce((score, req) => {
      return score + (req.test(password) ? 1 : 0);
    }, 0);
  };

  const getStrengthLevel = (score: number) => {
    if (score === 0) return { label: '', color: '' };
    if (score <= 1) return { label: 'Weak', color: 'text-destructive' };
    if (score <= 2) return { label: 'Fair', color: 'text-warning' };
    if (score <= 3) return { label: 'Good', color: 'text-info' };
    return { label: 'Strong', color: 'text-success' };
  };

  const score = getStrengthScore();
  const strength = getStrengthLevel(score);
  const progress = (score / requirements.length) * 100;

  if (!password) return null;

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      className={`mt-3 ${className}`}
    >
      {/* Strength Bar */}
      <div className="mb-3">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs text-muted-foreground">Password Strength</span>
          <span className={`text-xs font-medium ${strength.color}`}>
            {strength.label}
          </span>
        </div>
        <div className="w-full bg-muted rounded-full h-1.5">
          <motion.div
            className={`h-1.5 rounded-full transition-all duration-300 ${
              score <= 1
                ? 'bg-destructive'
                : score <= 2
                ? 'bg-warning'
                : score <= 3
                ? 'bg-info'
                : 'bg-success'
            }`}
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </div>

      {/* Requirements List */}
      <div className="space-y-1">
        {requirements.map((requirement, index) => {
          const isValid = requirement.test(password);
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center space-x-2"
            >
              <div
                className={`w-3 h-3 rounded-full flex items-center justify-center ${
                  isValid ? 'bg-success' : 'bg-muted'
                }`}
              >
                {isValid ? (
                  <Check className="w-2 h-2 text-success-foreground" />
                ) : (
                  <X className="w-2 h-2 text-muted-foreground" />
                )}
              </div>
              <span
                className={`text-xs ${
                  isValid ? 'text-success' : 'text-muted-foreground'
                }`}
              >
                {requirement.label}
              </span>
            </motion.div>
          );
        })}
      </div>
    </motion.div>
  );
};