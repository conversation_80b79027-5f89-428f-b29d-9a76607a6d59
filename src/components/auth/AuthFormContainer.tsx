import React from 'react';
import { motion } from 'framer-motion';
import { Building2 } from 'lucide-react';

interface AuthFormContainerProps {
  title: string;
  subtitle: string;
  children: React.ReactNode;
  showDemo?: boolean;
}

export const AuthFormContainer: React.FC<AuthFormContainerProps> = ({
  title,
  subtitle,
  children,
  showDemo = false,
}) => {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <div className="card-neumorphic p-8">
          {/* Logo */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl gradient-primary soft-shadow mb-4">
              <Building2 className="w-8 h-8 text-primary-foreground" />
            </div>
            <h1 className="text-2xl font-bold text-foreground">VendorMS</h1>
            <p className="text-muted-foreground mt-2">{subtitle}</p>
          </div>

          {/* Demo Credentials (only show on login) */}
          {showDemo && (
            <div className="mb-6 p-4 bg-info/10 border border-info/20 rounded-xl">
              <h3 className="text-sm font-medium text-info mb-2">Demo Credentials:</h3>
              <div className="text-xs text-muted-foreground space-y-1">
                <p><strong>Admin:</strong> <EMAIL> / password</p>
                <p><strong>Manager:</strong> <EMAIL> / password</p>
                <p><strong>Viewer:</strong> <EMAIL> / password</p>
              </div>
            </div>
          )}

          {children}
        </div>
      </motion.div>
    </div>
  );
};