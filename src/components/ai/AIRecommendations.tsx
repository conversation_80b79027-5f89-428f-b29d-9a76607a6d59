import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Brain,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Star,
  Filter,
  SortAsc,
  SortDesc,
  Eye,
  UserPlus,
  RefreshCw,
  Lightbulb,
  Target,
  Shield,
  DollarSign,
  MapPin,
  Award,
} from 'lucide-react';
import { VendorRecommendation, RecommendationCriteria } from '@/services/ai/types';
import { useNavigate } from 'react-router-dom';

interface AIRecommendationsProps {
  recommendations: VendorRecommendation[];
  criteria: RecommendationCriteria;
  isLoading?: boolean;
  error?: string | null;
  onApplyRecommendation?: (vendorId: string) => void;
  onViewVendor?: (vendorId: string) => void;
  onRefresh?: () => void;
}

type SortOption = 'score' | 'confidence' | 'name' | 'performance';
type FilterOption = 'all' | 'high-confidence' | 'low-risk' | 'budget-friendly';

export const AIRecommendations: React.FC<AIRecommendationsProps> = ({
  recommendations,
  criteria,
  isLoading = false,
  error = null,
  onApplyRecommendation,
  onViewVendor,
  onRefresh,
}) => {
  const navigate = useNavigate();
  const [sortBy, setSortBy] = useState<SortOption>('score');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [expandedCard, setExpandedCard] = useState<string | null>(null);

  // Filter and sort recommendations
  const filteredAndSortedRecommendations = React.useMemo(() => {
    let filtered = [...recommendations];

    // Apply filters
    switch (filterBy) {
      case 'high-confidence':
        filtered = filtered.filter(rec => rec.confidence >= 0.8);
        break;
      case 'low-risk':
        filtered = filtered.filter(rec => rec.risks.length <= 1);
        break;
      case 'budget-friendly':
        if (criteria.maxBudget) {
          filtered = filtered.filter(rec => 
            rec.vendor.costEffectiveness.averageCost <= criteria.maxBudget!
          );
        }
        break;
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: number;
      let bValue: number;

      switch (sortBy) {
        case 'score':
          aValue = a.score;
          bValue = b.score;
          break;
        case 'confidence':
          aValue = a.confidence;
          bValue = b.confidence;
          break;
        case 'name':
          return sortOrder === 'asc' 
            ? a.vendor.name.localeCompare(b.vendor.name)
            : b.vendor.name.localeCompare(a.vendor.name);
        case 'performance':
          aValue = a.vendor.performanceScore;
          bValue = b.vendor.performanceScore;
          break;
        default:
          aValue = a.score;
          bValue = b.score;
      }

      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });

    return filtered;
  }, [recommendations, sortBy, sortOrder, filterBy, criteria.maxBudget]);

  const handleViewVendor = (vendorId: string) => {
    if (onViewVendor) {
      onViewVendor(vendorId);
    } else {
      navigate(`/vendors/view/${vendorId}`);
    }
  };

  const handleApplyRecommendation = (vendorId: string) => {
    if (onApplyRecommendation) {
      onApplyRecommendation(vendorId);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getRiskLevel = (risks: string[]) => {
    if (risks.length === 0) return { level: 'Low', color: 'bg-green-100 text-green-800' };
    if (risks.length <= 2) return { level: 'Medium', color: 'bg-yellow-100 text-yellow-800' };
    return { level: 'High', color: 'bg-red-100 text-red-800' };
  };

  if (error) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Error Loading Recommendations</h3>
          <p className="text-muted-foreground text-center mb-4">{error}</p>
          {onRefresh && (
            <Button onClick={onRefresh} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Brain className="h-12 w-12 text-primary animate-pulse mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Analyzing Vendors</h3>
            <p className="text-muted-foreground">AI is processing vendor data and generating recommendations...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-primary" />
                AI Vendor Recommendations
              </CardTitle>
              <CardDescription>
                {filteredAndSortedRecommendations.length} vendors recommended based on your criteria
              </CardDescription>
            </div>
            {onRefresh && (
              <Button onClick={onRefresh} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Criteria Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Target className="h-4 w-4" />
            Search Criteria
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {criteria.category && (
              <Badge variant="secondary">
                Category: {criteria.category}
              </Badge>
            )}
            {criteria.maxBudget && (
              <Badge variant="secondary">
                <DollarSign className="h-3 w-3 mr-1" />
                Budget: ${criteria.maxBudget.toLocaleString()}
              </Badge>
            )}
            {criteria.location && (
              <Badge variant="secondary">
                <MapPin className="h-3 w-3 mr-1" />
                Location: {criteria.location}
              </Badge>
            )}
            {criteria.minPerformanceScore && (
              <Badge variant="secondary">
                Min Performance: {criteria.minPerformanceScore}%
              </Badge>
            )}
            {criteria.requiredCertifications && criteria.requiredCertifications.length > 0 && (
              <Badge variant="secondary">
                <Award className="h-3 w-3 mr-1" />
                Certifications: {criteria.requiredCertifications.length}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={filterBy} onValueChange={(value: FilterOption) => setFilterBy(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Vendors</SelectItem>
                  <SelectItem value="high-confidence">High Confidence</SelectItem>
                  <SelectItem value="low-risk">Low Risk</SelectItem>
                  <SelectItem value="budget-friendly">Budget Friendly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Sort by:</span>
              <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="score">Score</SelectItem>
                  <SelectItem value="confidence">Confidence</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="performance">Performance</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              >
                {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations List */}
      {filteredAndSortedRecommendations.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Lightbulb className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Recommendations Found</h3>
            <p className="text-muted-foreground text-center">
              No vendors match your current criteria and filters. Try adjusting your search parameters.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          <AnimatePresence>
            {filteredAndSortedRecommendations.map((recommendation, index) => {
              const riskLevel = getRiskLevel(recommendation.risks);
              const isExpanded = expandedCard === recommendation.vendor.id;

              return (
                <motion.div
                  key={recommendation.vendor.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                    <CardHeader 
                      className="pb-4"
                      onClick={() => setExpandedCard(isExpanded ? null : recommendation.vendor.id)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="flex items-center gap-2">
                              <span className="text-lg font-semibold">{index + 1}.</span>
                              <h3 className="text-lg font-semibold">{recommendation.vendor.name}</h3>
                            </div>
                            <Badge variant="outline">{recommendation.vendor.category}</Badge>
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {recommendation.vendor.location}
                            </span>
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3" />
                              ${recommendation.vendor.costEffectiveness.averageCost.toLocaleString()}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          <div className="text-right">
                            <div className={`text-2xl font-bold ${getScoreColor(recommendation.score)}`}>
                              {recommendation.score}
                            </div>
                            <div className="text-xs text-muted-foreground">Score</div>
                          </div>
                          <Badge className={getConfidenceColor(recommendation.confidence)}>
                            {(recommendation.confidence * 100).toFixed(0)}% confidence
                          </Badge>
                        </div>
                      </div>

                      {/* Progress bars */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-xs">
                          <span>Recommendation Score</span>
                          <span>{recommendation.score}/100</span>
                        </div>
                        <Progress value={recommendation.score} className="h-2" />
                        
                        <div className="flex items-center justify-between text-xs">
                          <span>Performance Score</span>
                          <span>{recommendation.vendor.performanceScore}/100</span>
                        </div>
                        <Progress value={recommendation.vendor.performanceScore} className="h-2" />
                      </div>
                    </CardHeader>

                    <CardContent>
                      {/* Quick info */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-4">
                          <Badge className={riskLevel.color}>
                            <Shield className="h-3 w-3 mr-1" />
                            {riskLevel.level} Risk
                          </Badge>
                          {recommendation.matchedCriteria.length > 0 && (
                            <Badge variant="secondary">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              {recommendation.matchedCriteria.length} criteria matched
                            </Badge>
                          )}
                        </div>

                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewVendor(recommendation.vendor.id);
                            }}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Button>
                          {onApplyRecommendation && (
                            <Button
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleApplyRecommendation(recommendation.vendor.id);
                              }}
                            >
                              <UserPlus className="h-4 w-4 mr-2" />
                              Select Vendor
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* Expanded details */}
                      <AnimatePresence>
                        {isExpanded && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Separator className="mb-4" />
                            
                            {/* AI Reasoning */}
                            <div className="space-y-4">
                              <div>
                                <h4 className="font-medium mb-2 flex items-center gap-2">
                                  <Brain className="h-4 w-4" />
                                  AI Analysis
                                </h4>
                                <p className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
                                  {recommendation.reasoning}
                                </p>
                              </div>

                              {/* Matched Criteria */}
                              {recommendation.matchedCriteria.length > 0 && (
                                <div>
                                  <h4 className="font-medium mb-2 flex items-center gap-2">
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                    Matched Criteria
                                  </h4>
                                  <div className="flex flex-wrap gap-2">
                                    {recommendation.matchedCriteria.map((criteria) => (
                                      <Badge key={criteria} variant="secondary" className="bg-green-100 text-green-800">
                                        {criteria}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Risk Factors */}
                              {recommendation.risks.length > 0 && (
                                <div>
                                  <h4 className="font-medium mb-2 flex items-center gap-2">
                                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                                    Risk Factors
                                  </h4>
                                  <div className="space-y-2">
                                    {recommendation.risks.map((risk, riskIndex) => (
                                      <Alert key={riskIndex} className="border-yellow-200 bg-yellow-50">
                                        <AlertTriangle className="h-4 w-4" />
                                        <AlertDescription className="text-sm">
                                          {risk}
                                        </AlertDescription>
                                      </Alert>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Vendor Details */}
                              <div>
                                <h4 className="font-medium mb-2">Vendor Details</h4>
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                  <div>
                                    <span className="text-muted-foreground">Compliance Score:</span>
                                    <span className="ml-2 font-medium">
                                      {recommendation.vendor.complianceRecords.score}/100
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-muted-foreground">Total Contracts:</span>
                                    <span className="ml-2 font-medium">
                                      {recommendation.vendor.contractHistory.totalContracts}
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-muted-foreground">On-time Delivery:</span>
                                    <span className="ml-2 font-medium">
                                      {Math.round((recommendation.vendor.contractHistory.completedOnTime / recommendation.vendor.contractHistory.totalContracts) * 100)}%
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-muted-foreground">Cost Trend:</span>
                                    <span className="ml-2 font-medium capitalize">
                                      {recommendation.vendor.costEffectiveness.costTrend}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              {/* Certifications */}
                              {recommendation.vendor.certifications.length > 0 && (
                                <div>
                                  <h4 className="font-medium mb-2 flex items-center gap-2">
                                    <Award className="h-4 w-4" />
                                    Certifications
                                  </h4>
                                  <div className="flex flex-wrap gap-2">
                                    {recommendation.vendor.certifications.map((cert) => (
                                      <Badge key={cert} variant="outline">
                                        {cert}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>
      )}
    </div>
  );
};

export default AIRecommendations;