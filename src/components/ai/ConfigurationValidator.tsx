import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  AlertCircle, 
  XCircle, 
  RefreshCw,
  Shield,
  Database,
  Zap
} from 'lucide-react';
import { SecureStorage } from '@/services/storage/SecureStorage';
import { APIKeyValidationService } from '@/services/ai/APIKeyValidationService';
import { AIProvider } from '@/services/ai/types';
import { useToast } from '@/hooks/use-toast';

interface ValidationResult {
  provider: AIProvider;
  configured: boolean;
  formatValid: boolean;
  connectionValid: boolean;
  error?: string;
}

export const ConfigurationValidator: React.FC = () => {
  const { toast } = useToast();
  const [isValidating, setIsValidating] = useState(false);
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [storageIntegrity, setStorageIntegrity] = useState<{
    isValid: boolean;
    errors: string[];
  } | null>(null);
  const [progress, setProgress] = useState(0);

  const runValidation = async () => {
    setIsValidating(true);
    setProgress(0);
    
    try {
      // Step 1: Check storage integrity (20%)
      setProgress(20);
      const integrity = await SecureStorage.validateIntegrity();
      setStorageIntegrity(integrity);

      if (!integrity.isValid) {
        toast({
          title: 'Storage Integrity Issues',
          description: 'Some storage validation errors were found. Check the details below.',
          variant: 'destructive',
        });
      }

      // Step 2: Load configurations (40%)
      setProgress(40);
      const configurations = await SecureStorage.getAIConfigurations();
      
      // Step 3: Validate each configured provider (60-100%)
      const providers: AIProvider[] = ['openai', 'anthropic', 'gemini'];
      const results: ValidationResult[] = [];
      
      for (let i = 0; i < providers.length; i++) {
        const provider = providers[i];
        const config = configurations[provider];
        
        setProgress(60 + (i * 13)); // 60, 73, 86
        
        if (!config || !config.apiKey) {
          results.push({
            provider,
            configured: false,
            formatValid: false,
            connectionValid: false
          });
          continue;
        }

        try {
          // Decrypt and validate the API key
          const decryptedConfig = await SecureStorage.getAIConfiguration(provider);
          if (!decryptedConfig) {
            results.push({
              provider,
              configured: true,
              formatValid: false,
              connectionValid: false,
              error: 'Failed to decrypt configuration'
            });
            continue;
          }

          // Validate the API key
          const validation = await APIKeyValidationService.validateApiKey(
            provider,
            decryptedConfig.apiKey,
            { testConnection: true, skipCache: true }
          );

          results.push({
            provider,
            configured: true,
            formatValid: validation.details?.formatValid || false,
            connectionValid: validation.details?.connectionValid || false,
            error: validation.error
          });
        } catch (error) {
          results.push({
            provider,
            configured: true,
            formatValid: false,
            connectionValid: false,
            error: error instanceof Error ? error.message : 'Validation failed'
          });
        }
      }

      setProgress(100);
      setValidationResults(results);

      // Show summary toast
      const configuredCount = results.filter(r => r.configured).length;
      const validCount = results.filter(r => r.configured && r.connectionValid).length;
      
      toast({
        title: 'Validation Complete',
        description: `${validCount}/${configuredCount} configured providers are working correctly.`,
        variant: validCount === configuredCount ? 'default' : 'destructive',
      });

    } catch (error) {
      toast({
        title: 'Validation Failed',
        description: error instanceof Error ? error.message : 'Failed to run validation',
        variant: 'destructive',
      });
    } finally {
      setIsValidating(false);
    }
  };

  const getProviderIcon = (provider: AIProvider) => {
    const icons = {
      openai: '🤖',
      anthropic: '🧠',
      gemini: '✨'
    };
    return icons[provider];
  };

  const getProviderName = (provider: AIProvider) => {
    const names = {
      openai: 'OpenAI',
      anthropic: 'Anthropic',
      gemini: 'Google Gemini'
    };
    return names[provider];
  };

  const getStatusBadge = (result: ValidationResult) => {
    if (!result.configured) {
      return <Badge variant="outline">Not Configured</Badge>;
    }
    
    if (result.connectionValid) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Valid</Badge>;
    }
    
    if (result.formatValid) {
      return <Badge variant="destructive">Connection Failed</Badge>;
    }
    
    return <Badge variant="destructive">Invalid Format</Badge>;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Configuration Validation
        </CardTitle>
        <CardDescription>
          Validate your AI provider configurations and check system integrity
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Validation Controls */}
        <div className="flex items-center justify-between">
          <Button
            onClick={runValidation}
            disabled={isValidating}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isValidating ? 'animate-spin' : ''}`} />
            {isValidating ? 'Validating...' : 'Run Validation'}
          </Button>
          
          {isValidating && (
            <div className="flex-1 ml-4">
              <Progress value={progress} className="w-full" />
              <p className="text-xs text-muted-foreground mt-1">
                {progress}% complete
              </p>
            </div>
          )}
        </div>

        {/* Storage Integrity */}
        {storageIntegrity && (
          <Alert className={storageIntegrity.isValid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <Database className="h-4 w-4" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <span>
                  <strong>Storage Integrity:</strong> {storageIntegrity.isValid ? 'Valid' : 'Issues Found'}
                </span>
                {storageIntegrity.isValid ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
              </div>
              {!storageIntegrity.isValid && storageIntegrity.errors.length > 0 && (
                <ul className="mt-2 text-sm list-disc list-inside">
                  {storageIntegrity.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Provider Validation Results */}
        {validationResults.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Provider Validation Results
            </h4>
            
            {validationResults.map((result) => (
              <div
                key={result.provider}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <span className="text-lg">{getProviderIcon(result.provider)}</span>
                  <div>
                    <p className="font-medium">{getProviderName(result.provider)}</p>
                    {result.error && (
                      <p className="text-xs text-red-600">{result.error}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {getStatusBadge(result)}
                  {result.configured && (
                    <div className="flex gap-1">
                      {result.formatValid ? (
                        <CheckCircle className="h-4 w-4 text-green-600" title="Format Valid" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" title="Format Invalid" />
                      )}
                      {result.connectionValid ? (
                        <CheckCircle className="h-4 w-4 text-green-600" title="Connection Valid" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" title="Connection Failed" />
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Help Text */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Validation checks:</strong> Format validation ensures API keys follow the correct pattern. 
            Connection validation tests if the keys work with the provider's API. 
            Run this validation after making configuration changes or if you experience issues.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};