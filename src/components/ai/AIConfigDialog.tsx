import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { AIProvider } from '@/services/ai/types';
import { AppDispatch } from '@/store';
import {
  validateApiKey,
  configureProvider,
  selectValidationStatus,
  selectIsConfiguring,
  selectAIError,
  clearError,
  resetValidationStatus
} from '@/store/slices/aiSlice';

interface AIConfigDialogProps {
  isOpen: boolean;
  provider: AIProvider | null;
  onClose: () => void;
}

const providerDetails = {
  openai: {
    name: 'OpenAI',
    apiKeyLabel: 'OpenAI API Key',
    apiKeyPlaceholder: 'sk-...',
    helpText: 'Get your API key from https://platform.openai.com/api-keys',
    models: ['gpt-4', 'gpt-4-turbo-preview', 'gpt-3.5-turbo'],
    defaultModel: 'gpt-4'
  },
  anthropic: {
    name: 'Anthropic',
    apiKeyLabel: 'Anthropic API Key',
    apiKeyPlaceholder: 'sk-ant-...',
    helpText: 'Get your API key from https://console.anthropic.com/',
    models: ['claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-2.1'],
    defaultModel: 'claude-3-sonnet-20240229'
  },
  gemini: {
    name: 'Google Gemini',
    apiKeyLabel: 'Google AI API Key',
    apiKeyPlaceholder: 'AI...',
    helpText: 'Get your API key from https://makersuite.google.com/app/apikey',
    models: ['gemini-pro', 'gemini-pro-vision'],
    defaultModel: 'gemini-pro'
  }
};

export const AIConfigDialog: React.FC<AIConfigDialogProps> = ({
  isOpen,
  provider,
  onClose
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const validationStatus = useSelector(selectValidationStatus);
  const isConfiguring = useSelector(selectIsConfiguring);
  const error = useSelector(selectAIError);

  const [formData, setFormData] = useState({
    apiKey: '',
    modelVersion: '',
    maxTokens: 2000,
    temperature: 0.3
  });
  const [showApiKey, setShowApiKey] = useState(false);
  const [hasValidated, setHasValidated] = useState(false);

  const currentValidation = provider ? validationStatus[provider] : null;
  const details = provider ? providerDetails[provider] : null;

  useEffect(() => {
    if (provider && isOpen) {
      setFormData({
        apiKey: '',
        modelVersion: details?.defaultModel || '',
        maxTokens: 2000,
        temperature: 0.3
      });
      setHasValidated(false);
      dispatch(clearError());
      dispatch(resetValidationStatus(provider));
    }
  }, [provider, isOpen, dispatch, details]);

  const handleValidateApiKey = async () => {
    if (!provider || !formData.apiKey.trim()) return;
    
    setHasValidated(true);
    dispatch(validateApiKey({ 
      provider, 
      apiKey: formData.apiKey.trim() 
    }));
  };

  const handleSave = async () => {
    if (!provider || !currentValidation?.isValid) return;

    const config = {
      provider,
      apiKey: formData.apiKey.trim(),
      modelVersion: formData.modelVersion,
      maxTokens: formData.maxTokens,
      temperature: formData.temperature
    };

    try {
      await dispatch(configureProvider(config)).unwrap();
      onClose();
    } catch (error) {
      // Error is handled by the slice
    }
  };

  const handleClose = () => {
    if (provider) {
      dispatch(resetValidationStatus(provider));
    }
    dispatch(clearError());
    onClose();
  };

  if (!provider || !details) return null;

  const canSave = currentValidation?.isValid && !isConfiguring;
  const showValidationButton = formData.apiKey.trim() && !currentValidation?.isValidating;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Configure {details.name}</DialogTitle>
          <DialogDescription>
            Set up your {details.name} API configuration for AI-powered features.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* API Key */}
          <div className="space-y-2">
            <Label htmlFor="apiKey">{details.apiKeyLabel}</Label>
            <div className="relative">
              <Input
                id="apiKey"
                type={showApiKey ? 'text' : 'password'}
                placeholder={details.apiKeyPlaceholder}
                value={formData.apiKey}
                onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              {details.helpText}
            </p>
          </div>

          {/* Validation */}
          {showValidationButton && (
            <Button
              type="button"
              variant="outline"
              onClick={handleValidateApiKey}
              disabled={currentValidation?.isValidating}
              className="w-full"
            >
              {currentValidation?.isValidating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Validating...
                </>
              ) : (
                'Validate API Key'
              )}
            </Button>
          )}

          {/* Validation Status */}
          {hasValidated && currentValidation && (
            <Alert className={currentValidation.isValid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              <div className="flex items-center">
                {currentValidation.isValid ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription className="ml-2">
                  {currentValidation.isValid 
                    ? 'API key is valid and ready to use'
                    : currentValidation.error || 'API key validation failed'
                  }
                </AlertDescription>
              </div>
            </Alert>
          )}

          {/* Advanced Settings */}
          {currentValidation?.isValid && (
            <div className="space-y-4 pt-4 border-t">
              <h4 className="text-sm font-medium">Advanced Settings</h4>
              
              {/* Model Version */}
              <div className="space-y-2">
                <Label htmlFor="modelVersion">Model Version</Label>
                <select
                  id="modelVersion"
                  value={formData.modelVersion}
                  onChange={(e) => setFormData({ ...formData, modelVersion: e.target.value })}
                  className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                >
                  {details.models.map((model) => (
                    <option key={model} value={model}>
                      {model}
                    </option>
                  ))}
                </select>
              </div>

              {/* Max Tokens */}
              <div className="space-y-2">
                <Label htmlFor="maxTokens">Max Tokens</Label>
                <Input
                  id="maxTokens"
                  type="number"
                  min="100"
                  max="4000"
                  value={formData.maxTokens}
                  onChange={(e) => setFormData({ ...formData, maxTokens: parseInt(e.target.value) || 2000 })}
                />
                <p className="text-xs text-muted-foreground">
                  Maximum tokens for AI responses (100-4000)
                </p>
              </div>

              {/* Temperature */}
              <div className="space-y-2">
                <Label htmlFor="temperature">Temperature</Label>
                <Input
                  id="temperature"
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  value={formData.temperature}
                  onChange={(e) => setFormData({ ...formData, temperature: parseFloat(e.target.value) || 0.3 })}
                />
                <p className="text-xs text-muted-foreground">
                  Controls randomness (0 = deterministic, 1 = very random)
                </p>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!canSave}
          >
            {isConfiguring ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Configuration'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};