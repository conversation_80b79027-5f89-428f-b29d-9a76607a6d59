import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertCircle, Settings, Loader2 } from 'lucide-react';
import { AIProvider } from '@/services/ai/types';

interface AIProviderCardProps {
  provider: AIProvider;
  isSelected: boolean;
  isConfigured: boolean;
  isAvailable: boolean;
  isValidating: boolean;
  validationError?: string | null;
  onSelect: (provider: AIProvider) => void;
  onConfigure: (provider: AIProvider) => void;
}

const providerInfo = {
  openai: {
    name: 'OpenAI',
    description: 'GPT-4 and GPT-3.5 models for advanced AI capabilities',
    icon: '🤖',
    features: ['GPT-4 Turbo', 'Function Calling', 'JSON Mode', 'High Accuracy'],
    color: 'bg-green-50 border-green-200 hover:bg-green-100'
  },
  anthropic: {
    name: 'Anthropic',
    description: 'Claude models with strong reasoning and safety features',
    icon: '🧠',
    features: ['Claude 3 Sonnet', 'Long Context', 'Safety Focused', 'Reasoning'],
    color: 'bg-blue-50 border-blue-200 hover:bg-blue-100'
  },
  gemini: {
    name: 'Google Gemini',
    description: 'Google\'s multimodal AI with strong analytical capabilities',
    icon: '✨',
    features: ['Gemini Pro', 'Multimodal', 'Fast Response', 'Cost Effective'],
    color: 'bg-purple-50 border-purple-200 hover:bg-purple-100'
  }
};

export const AIProviderCard: React.FC<AIProviderCardProps> = ({
  provider,
  isSelected,
  isConfigured,
  isAvailable,
  isValidating,
  validationError,
  onSelect,
  onConfigure
}) => {
  const info = providerInfo[provider];
  
  const getStatusBadge = () => {
    if (isValidating) {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Loader2 className="h-3 w-3 animate-spin" />
          Validating
        </Badge>
      );
    }
    
    if (validationError) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Error
        </Badge>
      );
    }
    
    if (isConfigured && isAvailable) {
      return (
        <Badge variant="default" className="flex items-center gap-1 bg-green-100 text-green-800">
          <CheckCircle className="h-3 w-3" />
          Active
        </Badge>
      );
    }
    
    if (isConfigured) {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Settings className="h-3 w-3" />
          Configured
        </Badge>
      );
    }
    
    return (
      <Badge variant="outline">
        Not Configured
      </Badge>
    );
  };

  return (
    <Card 
      className={`cursor-pointer transition-all duration-200 ${
        isSelected 
          ? 'ring-2 ring-primary ring-offset-2 shadow-md' 
          : 'hover:shadow-md'
      } ${info.color}`}
      onClick={() => onSelect(provider)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="text-2xl">{info.icon}</div>
            <div>
              <CardTitle className="text-lg">{info.name}</CardTitle>
              <CardDescription className="text-sm mt-1">
                {info.description}
              </CardDescription>
            </div>
          </div>
          {getStatusBadge()}
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Features */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Key Features</h4>
            <div className="flex flex-wrap gap-1">
              {info.features.map((feature) => (
                <Badge 
                  key={feature} 
                  variant="secondary" 
                  className="text-xs bg-white/60"
                >
                  {feature}
                </Badge>
              ))}
            </div>
          </div>
          
          {/* Error Message */}
          {validationError && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded border border-red-200">
              {validationError}
            </div>
          )}
          
          {/* Action Button */}
          <div className="flex gap-2">
            <Button
              variant={isConfigured ? "outline" : "default"}
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onConfigure(provider);
              }}
              disabled={isValidating}
              className="flex-1"
            >
              {isValidating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Validating...
                </>
              ) : (
                <>
                  <Settings className="h-4 w-4 mr-2" />
                  {isConfigured ? 'Reconfigure' : 'Configure'}
                </>
              )}
            </Button>
            
            {isConfigured && (
              <Button
                variant={isSelected ? "default" : "outline"}
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onSelect(provider);
                }}
                disabled={!isAvailable}
              >
                {isSelected ? 'Active' : 'Select'}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};