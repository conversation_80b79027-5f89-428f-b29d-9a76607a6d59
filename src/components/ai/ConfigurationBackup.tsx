import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { 
  Download, 
  Upload, 
  AlertCircle, 
  CheckCircle, 
  FileText,
  Shield
} from 'lucide-react';
import { SecureStorage } from '@/services/storage/SecureStorage';
import { useToast } from '@/hooks/use-toast';

export const ConfigurationBackup: React.FC = () => {
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importData, setImportData] = useState('');
  const [exportedData, setExportedData] = useState('');

  const handleExport = async () => {
    setIsExporting(true);
    try {
      const backupData = await SecureStorage.exportConfigurations();
      setExportedData(backupData);
      
      // Create downloadable file
      const blob = new Blob([backupData], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `vms-ai-config-backup-${new Date().toISOString().split('T')[0]}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: 'Export Successful',
        description: 'AI configurations have been exported and downloaded.',
      });
    } catch (error) {
      toast({
        title: 'Export Failed',
        description: error instanceof Error ? error.message : 'Failed to export configurations',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleImport = async () => {
    if (!importData.trim()) {
      toast({
        title: 'Import Error',
        description: 'Please paste the backup data to import.',
        variant: 'destructive',
      });
      return;
    }

    setIsImporting(true);
    try {
      await SecureStorage.importConfigurations(importData.trim());
      
      toast({
        title: 'Import Successful',
        description: 'AI configurations have been imported successfully. Please refresh the page.',
      });
      
      setImportData('');
      
      // Suggest page refresh
      setTimeout(() => {
        if (window.confirm('Import completed. Would you like to refresh the page to see the changes?')) {
          window.location.reload();
        }
      }, 1000);
      
    } catch (error) {
      toast({
        title: 'Import Failed',
        description: error instanceof Error ? error.message : 'Failed to import configurations',
        variant: 'destructive',
      });
    } finally {
      setIsImporting(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setImportData(content);
    };
    reader.readAsText(file);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Configuration Backup & Restore
        </CardTitle>
        <CardDescription>
          Export your AI configurations for backup or import from a previous backup
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Export Section */}
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-2">Export Configurations</h4>
            <p className="text-sm text-muted-foreground mb-3">
              Download an encrypted backup of your AI provider configurations
            </p>
            <Button
              onClick={handleExport}
              disabled={isExporting}
              className="w-full sm:w-auto"
            >
              <Download className="h-4 w-4 mr-2" />
              {isExporting ? 'Exporting...' : 'Export Configurations'}
            </Button>
          </div>

          {exportedData && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Configuration backup has been created and downloaded. Keep this file secure as it contains encrypted API keys.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <div className="border-t pt-6">
          {/* Import Section */}
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Import Configurations</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Restore AI configurations from a backup file
              </p>
            </div>

            {/* File Upload */}
            <div className="space-y-2">
              <Label htmlFor="backup-file">Upload Backup File</Label>
              <Input
                id="backup-file"
                type="file"
                accept=".txt"
                onChange={handleFileUpload}
                className="cursor-pointer"
              />
              <p className="text-xs text-muted-foreground">
                Select a backup file created by the export function
              </p>
            </div>

            {/* Manual Input */}
            <div className="space-y-2">
              <Label htmlFor="backup-data">Or Paste Backup Data</Label>
              <Textarea
                id="backup-data"
                placeholder="Paste your encrypted backup data here..."
                value={importData}
                onChange={(e) => setImportData(e.target.value)}
                rows={4}
                className="font-mono text-sm"
              />
            </div>

            <Button
              onClick={handleImport}
              disabled={isImporting || !importData.trim()}
              className="w-full sm:w-auto"
            >
              <Upload className="h-4 w-4 mr-2" />
              {isImporting ? 'Importing...' : 'Import Configurations'}
            </Button>
          </div>
        </div>

        {/* Security Notice */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Security Notice:</strong> Backup files contain encrypted API keys. 
            Store them securely and never share them with unauthorized users. 
            The encryption is tied to your browser session.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};