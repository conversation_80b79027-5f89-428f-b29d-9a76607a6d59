import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  Building2,
  Mail,
  Phone,
  MapPin,
  TrendingUp,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Award,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Vendor } from '../../store/slices/vendorsSlice';
import { useAuth } from '../../hooks/useAuth';

interface VendorCardProps {
  vendor: Vendor;
  index?: number;
  onDelete?: (id: number) => void;
}

const statusColors = {
  active: 'bg-success/10 text-success border-success/20',
  inactive: 'bg-warning/10 text-warning border-warning/20',
  blacklisted: 'bg-destructive/10 text-destructive border-destructive/20',
};

const categoryColors = {
  Technology: 'bg-primary/10 text-primary border-primary/20',
  Manufacturing: 'bg-info/10 text-info border-info/20',
  Logistics: 'bg-accent/10 text-accent-foreground border-accent/20',
  Services: 'bg-secondary/10 text-secondary-foreground border-secondary/20',
  Healthcare: 'bg-emerald-500/10 text-emerald-600 border-emerald-500/20',
  Finance: 'bg-blue-500/10 text-blue-600 border-blue-500/20',
  Construction: 'bg-orange-500/10 text-orange-600 border-orange-500/20',
  Retail: 'bg-purple-500/10 text-purple-600 border-purple-500/20',
  Education: 'bg-indigo-500/10 text-indigo-600 border-indigo-500/20',
  Other: 'bg-gray-500/10 text-gray-600 border-gray-500/20',
};

export const VendorCard: React.FC<VendorCardProps> = ({ vendor, index = 0, onDelete }) => {
  const navigate = useNavigate();
  const { canEdit, canDelete } = useAuth();

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'text-success';
    if (score >= 70) return 'text-warning';
    return 'text-destructive';
  };

  const getPerformanceBarColor = (score: number) => {
    if (score >= 90) return 'bg-success';
    if (score >= 70) return 'bg-warning';
    return 'bg-destructive';
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(vendor.id);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 + index * 0.05 }}
      whileHover={{ scale: 1.02 }}
      className="card-neumorphic p-6 cursor-pointer group"
      onClick={() => navigate(`/vendors/${vendor.id}`)}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
            <Building2 className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
              {vendor.name}
            </h3>
            <p className="text-sm text-muted-foreground">{vendor.category}</p>
          </div>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-40">
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                navigate(`/vendors/${vendor.id}`);
              }}
            >
              <Eye className="w-4 h-4 mr-2" />
              View
            </DropdownMenuItem>
            {canEdit() && (
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/vendors/${vendor.id}/edit`);
                }}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </DropdownMenuItem>
            )}
            {canDelete() && (
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete();
                }}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Status and Category */}
      <div className="flex items-center space-x-2 mb-4">
        <span
          className={`px-2 py-1 rounded-lg text-xs font-medium border ${
            statusColors[vendor.status]
          }`}
        >
          {vendor.status.charAt(0).toUpperCase() + vendor.status.slice(1)}
        </span>
        <span
          className={`px-2 py-1 rounded-lg text-xs font-medium border ${
            categoryColors[vendor.category as keyof typeof categoryColors] ||
            categoryColors.Other
          }`}
        >
          {vendor.category}
        </span>
      </div>

      {/* Contact Info */}
      <div className="space-y-2 mb-4">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Mail className="w-4 h-4 flex-shrink-0" />
          <span className="truncate">{vendor.contact_email}</span>
        </div>
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Phone className="w-4 h-4 flex-shrink-0" />
          <span>{vendor.contact_phone}</span>
        </div>
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <MapPin className="w-4 h-4 flex-shrink-0" />
          <span className="truncate">
            {vendor.address.city}, {vendor.address.state}
          </span>
        </div>
      </div>

      {/* Certifications */}
      {vendor.certifications && vendor.certifications.length > 0 && (
        <div className="mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <Award className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Certifications</span>
          </div>
          <div className="flex flex-wrap gap-1">
            {vendor.certifications.slice(0, 3).map((cert, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-muted/50 text-muted-foreground text-xs rounded-md"
              >
                {cert}
              </span>
            ))}
            {vendor.certifications.length > 3 && (
              <span className="px-2 py-1 bg-muted/50 text-muted-foreground text-xs rounded-md">
                +{vendor.certifications.length - 3} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Performance Score */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <TrendingUp className="w-4 h-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">Performance</span>
        </div>
        <span className={`font-semibold ${getPerformanceColor(vendor.performance_score)}`}>
          {vendor.performance_score}%
        </span>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-muted rounded-full h-2">
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: `${vendor.performance_score}%` }}
          transition={{ duration: 0.8, delay: 0.2 + index * 0.05 }}
          className={`h-2 rounded-full transition-all duration-300 ${getPerformanceBarColor(
            vendor.performance_score
          )}`}
        />
      </div>

      {/* Created Date */}
      <div className="mt-3 text-xs text-muted-foreground">
        Added {new Date(vendor.created_at).toLocaleDateString()}
      </div>
    </motion.div>
  );
};