import React from 'react';
import { motion } from 'framer-motion';
import { 
  Building2, 
  Users, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { Vendor } from '../../store/slices/vendorsSlice';

interface VendorSummaryProps {
  vendors: Vendor[];
}

export const VendorSummary: React.FC<VendorSummaryProps> = ({ vendors }) => {
  const stats = React.useMemo(() => {
    const total = vendors.length;
    const active = vendors.filter(v => v.status === 'active').length;
    const inactive = vendors.filter(v => v.status === 'inactive').length;
    const blacklisted = vendors.filter(v => v.status === 'blacklisted').length;
    
    const avgPerformance = vendors.length > 0 
      ? vendors.reduce((sum, v) => sum + v.performance_score, 0) / vendors.length 
      : 0;
    
    const highPerformers = vendors.filter(v => v.performance_score >= 90).length;
    const lowPerformers = vendors.filter(v => v.performance_score < 70).length;
    
    return {
      total,
      active,
      inactive,
      blacklisted,
      avgPerformance: Math.round(avgPerformance * 10) / 10,
      highPerformers,
      lowPerformers,
    };
  }, [vendors]);

  const summaryCards = [
    {
      title: 'Total Vendors',
      value: stats.total,
      icon: Building2,
      color: 'text-primary',
      bgColor: 'bg-primary/10',
    },
    {
      title: 'Active Vendors',
      value: stats.active,
      icon: CheckCircle,
      color: 'text-success',
      bgColor: 'bg-success/10',
    },
    {
      title: 'Inactive Vendors',
      value: stats.inactive,
      icon: Clock,
      color: 'text-warning',
      bgColor: 'bg-warning/10',
    },
    {
      title: 'Blacklisted',
      value: stats.blacklisted,
      icon: AlertTriangle,
      color: 'text-destructive',
      bgColor: 'bg-destructive/10',
    },
    {
      title: 'Avg Performance',
      value: `${stats.avgPerformance}%`,
      icon: TrendingUp,
      color: stats.avgPerformance >= 80 ? 'text-success' : stats.avgPerformance >= 60 ? 'text-warning' : 'text-destructive',
      bgColor: stats.avgPerformance >= 80 ? 'bg-success/10' : stats.avgPerformance >= 60 ? 'bg-warning/10' : 'bg-destructive/10',
    },
    {
      title: 'High Performers',
      value: stats.highPerformers,
      icon: Users,
      color: 'text-success',
      bgColor: 'bg-success/10',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
      {summaryCards.map((card, index) => {
        const Icon = card.icon;
        return (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="card-neumorphic p-4"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{card.title}</p>
                <p className={`text-2xl font-bold ${card.color}`}>{card.value}</p>
              </div>
              <div className={`w-10 h-10 rounded-lg ${card.bgColor} flex items-center justify-center`}>
                <Icon className={`w-5 h-5 ${card.color}`} />
              </div>
            </div>
          </motion.div>
        );
      })}
    </div>
  );
};