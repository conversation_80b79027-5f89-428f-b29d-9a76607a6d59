import React from "react";
import { motion } from "framer-motion";
import {
  Clock,
  User,
  Plus,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  AlertCircle,
  Info,
} from "lucide-react";
import { AuditEntry } from "../../services/vendorApi";
// Simple date formatting utility
const formatDistanceToNow = (date: Date) => {
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) return "today";
  if (diffInDays === 1) return "1 day ago";
  if (diffInDays < 30) return `${diffInDays} days ago`;
  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
  return `${Math.floor(diffInDays / 365)} years ago`;
};

interface AuditTimelineProps {
  auditEntries: AuditEntry[];
  isLoading?: boolean;
}

const actionIcons = {
  create: Plus,
  update: Edit,
  delete: Trash2,
  view: Eye,
  approve: CheckCircle,
};

const actionColors = {
  create: "text-success bg-success/10 border-success/20",
  update: "text-primary bg-primary/10 border-primary/20",
  delete: "text-destructive bg-destructive/10 border-destructive/20",
  view: "text-muted-foreground bg-muted/10 border-muted/20",
  approve: "text-success bg-success/10 border-success/20",
};

const actionLabels = {
  create: "Created",
  update: "Updated",
  delete: "Deleted",
  view: "Viewed",
  approve: "Approved",
};

export const AuditTimeline: React.FC<AuditTimelineProps> = ({
  auditEntries,
  isLoading = false,
}) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="flex items-start space-x-4">
            <div className="w-8 h-8 bg-muted rounded-full animate-pulse" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
              <div className="h-3 bg-muted rounded animate-pulse w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (auditEntries.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-3 opacity-50" />
        <h3 className="text-lg font-medium text-foreground mb-2">
          No audit history
        </h3>
        <p className="text-muted-foreground">
          Audit entries will appear here as changes are made to this vendor.
        </p>
      </div>
    );
  }

  const formatChangeDetails = (entry: AuditEntry) => {
    if (entry.action === "create") {
      return "Vendor was created in the system";
    }

    if (entry.action === "delete") {
      return "Vendor was deactivated";
    }

    if (entry.action === "update" && entry.old_value && entry.new_value) {
      const changes = [];
      const oldVal = entry.old_value as any;
      const newVal = entry.new_value as unknown;

      // Check for common field changes
      if (oldVal.name !== newVal.name) {
        changes.push(`Name changed from "${oldVal.name}" to "${newVal.name}"`);
      }
      if (oldVal.contact_email !== newVal.contact_email) {
        changes.push(
          `Email changed from "${oldVal.contact_email}" to "${newVal.contact_email}"`
        );
      }
      if (oldVal.contact_phone !== newVal.contact_phone) {
        changes.push(
          `Phone changed from "${oldVal.contact_phone}" to "${newVal.contact_phone}"`
        );
      }
      if (oldVal.category !== newVal.category) {
        changes.push(
          `Category changed from "${oldVal.category}" to "${newVal.category}"`
        );
      }
      if (oldVal.status !== newVal.status) {
        changes.push(
          `Status changed from "${oldVal.status}" to "${newVal.status}"`
        );
      }
      if (oldVal.performance_score !== newVal.performance_score) {
        changes.push(
          `Performance score changed from ${oldVal.performance_score}% to ${newVal.performance_score}%`
        );
      }

      return changes.length > 0
        ? changes.join(", ")
        : "Vendor information was updated";
    }

    return `Vendor was ${entry.action}d`;
  };

  return (
    <div className="space-y-6">
      {auditEntries.map((entry, index) => {
        const Icon = actionIcons[entry.action] || Info;
        const colorClass = actionColors[entry.action] || actionColors.view;
        const actionLabel = actionLabels[entry.action] || entry.action;

        return (
          <motion.div
            key={entry.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="relative flex items-start space-x-4"
          >
            {/* Timeline line */}
            {index < auditEntries.length - 1 && (
              <div className="absolute left-4 top-8 w-px h-16 bg-border" />
            )}

            {/* Action icon */}
            <div
              className={`w-8 h-8 rounded-full border flex items-center justify-center ${colorClass}`}
            >
              <Icon className="w-4 h-4" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-foreground">
                    {actionLabel}
                  </span>
                  <span className="text-muted-foreground">by</span>
                  <div className="flex items-center space-x-1">
                    <User className="w-3 h-3 text-muted-foreground" />
                    <span className="text-sm font-medium text-foreground">
                      {entry.user_name || "System"}
                    </span>
                  </div>
                </div>
                <span className="text-sm text-muted-foreground">
                  {formatDistanceToNow(new Date(entry.timestamp), {
                    addSuffix: true,
                  })}
                </span>
              </div>

              <p className="text-sm text-muted-foreground mt-1">
                {formatChangeDetails(entry)}
              </p>

              {/* Additional details */}
              {entry.details && (
                <div className="mt-2 p-2 bg-muted/50 rounded-lg">
                  <div className="text-xs text-muted-foreground">
                    <div className="flex items-center space-x-4">
                      {entry.details.ip && <span>IP: {entry.details.ip}</span>}
                      <span>{new Date(entry.timestamp).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        );
      })}
    </div>
  );
};
