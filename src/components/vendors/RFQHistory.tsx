import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  FileText,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Send,
  Award,
  TrendingUp,
  DollarSign,
  AlertCircle,
  ExternalLink
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { VendorApiService, RFQHistoryEntry, RFQStatistics, EngagementAnalytics } from '@/services/vendorApi';
import { toast } from '@/components/ui/use-toast';

interface RFQHistoryProps {
  vendorId: number;
}

export const RFQHistory: React.FC<RFQHistoryProps> = ({ vendorId }) => {
  const [rfqHistory, setRfqHistory] = useState<RFQHistoryEntry[]>([]);
  const [statistics, setStatistics] = useState<RFQStatistics | null>(null);
  const [analytics, setAnalytics] = useState<EngagementAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRFQData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const [historyResponse, statisticsResponse, analyticsResponse] = await Promise.all([
          VendorApiService.getVendorRFQHistory(vendorId),
          VendorApiService.getVendorRFQStatistics(vendorId),
          VendorApiService.getVendorEngagementAnalytics(vendorId)
        ]);

        if (historyResponse.success) {
          setRfqHistory(historyResponse.data);
        }

        if (statisticsResponse.success) {
          setStatistics(statisticsResponse.data);
        }

        if (analyticsResponse.success) {
          setAnalytics(analyticsResponse.data);
        }
      } catch (err) {
        console.error('Error fetching RFQ data:', err);
        setError('Failed to load RFQ data');
        toast({
          title: 'Error',
          description: 'Failed to load RFQ data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchRFQData();
  }, [vendorId]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Send className="w-4 h-4 text-blue-500" />;
      case 'viewed':
        return <Eye className="w-4 h-4 text-yellow-500" />;
      case 'submitted':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'approved':
        return <Award className="w-4 h-4 text-green-600" />;
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'expired':
        return <Clock className="w-4 h-4 text-gray-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'viewed':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'submitted':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'expired':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-muted rounded w-1/4 mb-2"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <AlertCircle className="w-12 h-12 text-destructive mx-auto mb-4" />
          <p className="text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="history">RFQ History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {statistics && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">{statistics.total_invitations}</div>
                  <div className="text-sm text-muted-foreground">Invitations</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{statistics.total_submissions}</div>
                  <div className="text-sm text-muted-foreground">Submissions</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{statistics.total_selections}</div>
                  <div className="text-sm text-muted-foreground">Selected</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-600">{statistics.total_wins}</div>
                  <div className="text-sm text-muted-foreground">Won</div>
                </CardContent>
              </Card>
            </div>
          )}

          {statistics && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Response Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{statistics.response_rate}%</div>
                  <Progress value={statistics.response_rate} className="mt-2" />
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Selection Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{statistics.selection_rate}%</div>
                  <Progress value={statistics.selection_rate} className="mt-2" />
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Win Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{statistics.win_rate}%</div>
                  <Progress value={statistics.win_rate} className="mt-2" />
                </CardContent>
              </Card>
            </div>
          )}

          {statistics && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  Financial Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Average Bid Amount</div>
                    <div className="text-xl font-semibold">
                      {formatCurrency(statistics.average_bid_amount)}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Total Won Value</div>
                    <div className="text-xl font-semibold text-green-600">
                      {formatCurrency(statistics.total_won_value)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          {rfqHistory.length > 0 ? (
            <div className="space-y-4">
              {rfqHistory.map((entry, index) => (
                <motion.div
                  key={entry.rfq_id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <FileText className="w-5 h-5 text-primary" />
                            <h3 className="font-semibold text-lg">{entry.rfq_title}</h3>
                            <Badge className={getStatusColor(entry.invitation_status)}>
                              {getStatusIcon(entry.invitation_status)}
                              <span className="ml-1 capitalize">{entry.invitation_status}</span>
                            </Badge>
                            {entry.was_selected && (
                              <Badge variant="outline" className="text-green-600 border-green-600">
                                <Award className="w-3 h-3 mr-1" />
                                Selected
                              </Badge>
                            )}
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <div className="text-muted-foreground">Due Date</div>
                              <div className="font-medium">{formatDate(entry.due_date)}</div>
                            </div>
                            <div>
                              <div className="text-muted-foreground">Invited</div>
                              <div className="font-medium">{formatDateTime(entry.invitation_sent_at)}</div>
                            </div>
                            {entry.bid_amount && (
                              <div>
                                <div className="text-muted-foreground">Bid Amount</div>
                                <div className="font-medium">
                                  {formatCurrency(entry.bid_amount, entry.bid_currency)}
                                </div>
                              </div>
                            )}
                            {entry.quote_status && (
                              <div>
                                <div className="text-muted-foreground">Quote Status</div>
                                <Badge className={getStatusColor(entry.quote_status)}>
                                  {entry.quote_status}
                                </Badge>
                              </div>
                            )}
                          </div>

                          {entry.invitation_viewed_at && (
                            <div className="mt-3 text-sm text-muted-foreground">
                              Viewed: {formatDateTime(entry.invitation_viewed_at)}
                            </div>
                          )}

                          {entry.bid_submitted_at && (
                            <div className="mt-1 text-sm text-muted-foreground">
                              Submitted: {formatDateTime(entry.bid_submitted_at)}
                            </div>
                          )}

                          {entry.quote_approved_at && (
                            <div className="mt-1 text-sm text-green-600 font-medium">
                              Quote Approved: {formatDateTime(entry.quote_approved_at)}
                            </div>
                          )}
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className={getStatusColor(entry.rfq_status)}>
                            {entry.rfq_status}
                          </Badge>
                          <Button variant="ghost" size="sm">
                            <ExternalLink className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No RFQ History</h3>
                <p className="text-muted-foreground">
                  This vendor hasn't participated in any RFQs yet.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {analytics && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Engagement Score
                  </CardTitle>
                  <CardDescription>
                    Overall engagement score based on response rate, selection rate, and recent activity
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-2">{analytics.engagement_score.toFixed(1)}/100</div>
                  <Progress value={analytics.engagement_score} className="mb-4" />
                  <div className="text-sm text-muted-foreground">
                    {analytics.engagement_score >= 80 ? 'Excellent engagement' :
                     analytics.engagement_score >= 60 ? 'Good engagement' :
                     analytics.engagement_score >= 40 ? 'Average engagement' : 'Low engagement'}
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Response Time</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {analytics.avg_response_time_hours && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Average</span>
                        <span className="font-medium">{analytics.avg_response_time_hours.toFixed(1)}h</span>
                      </div>
                    )}
                    {analytics.fastest_response_time_hours && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Fastest</span>
                        <span className="font-medium text-green-600">{analytics.fastest_response_time_hours.toFixed(1)}h</span>
                      </div>
                    )}
                    {analytics.slowest_response_time_hours && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Slowest</span>
                        <span className="font-medium text-red-600">{analytics.slowest_response_time_hours.toFixed(1)}h</span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Recent Activity (90 days)</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Invitations</span>
                      <span className="font-medium">{analytics.recent_invitations}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Submissions</span>
                      <span className="font-medium">{analytics.recent_submissions}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Activity Rate</span>
                      <span className="font-medium">
                        {analytics.recent_invitations > 0 
                          ? `${((analytics.recent_submissions / analytics.recent_invitations) * 100).toFixed(1)}%`
                          : '0%'
                        }
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {analytics.total_contract_value && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Financial Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <div className="text-sm text-muted-foreground">Total Contract Value</div>
                        <div className="text-xl font-semibold text-green-600">
                          {formatCurrency(analytics.total_contract_value)}
                        </div>
                      </div>
                      {analytics.average_bid_amount && (
                        <div>
                          <div className="text-sm text-muted-foreground">Average Bid</div>
                          <div className="text-xl font-semibold">
                            {formatCurrency(analytics.average_bid_amount)}
                          </div>
                        </div>
                      )}
                      {analytics.lowest_bid_amount && analytics.highest_bid_amount && (
                        <div>
                          <div className="text-sm text-muted-foreground">Bid Range</div>
                          <div className="text-sm font-medium">
                            {formatCurrency(analytics.lowest_bid_amount)} - {formatCurrency(analytics.highest_bid_amount)}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RFQHistory;