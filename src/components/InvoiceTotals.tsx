import * as React from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { cn } from '@/lib/utils';
import { LineItem } from '@/store/slices/invoicesSlice';

const formSchema = z.object({
  discount: z.number().min(0).max(100),
  taxRate: z.number().min(0).max(100),
});

type FormValues = z.infer<typeof formSchema>;

export interface InvoiceTotalsProps {
  lineItems: LineItem[];
  initialDiscount?: number;
  initialTaxRate?: number;
  currency: string;
  onChange?: (totals: { subtotal: number; discount: number; tax: number; total: number }) => void;
  mode: 'view' | 'edit';
  className?: string;
}

function InvoiceTotals({ lineItems, initialDiscount = 0, initialTaxRate = 0, currency = 'USD', onChange, mode = 'view', className }: InvoiceTotalsProps) {
  const { control, watch } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: { discount: initialDiscount, taxRate: initialTaxRate },
  });

  const discount = watch('discount');
  const taxRate = watch('taxRate');

  const subtotal = lineItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const discountAmount = subtotal * (discount / 100);
  const taxableAmount = subtotal - discountAmount;
  const taxAmount = taxableAmount * (taxRate / 100);
  const total = taxableAmount + taxAmount;

  React.useEffect(() => {
    if (onChange) onChange({ subtotal, discount: discountAmount, tax: taxAmount, total });
  }, [subtotal, discount, taxRate, onChange]);

  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency }).format(amount);

  if (mode === 'view') {
    return (
      <div className={cn('space-y-2 text-right', className)}>
        <div>Subtotal: {formatCurrency(subtotal)}</div>
        <div>Discount ({initialDiscount}%): -{formatCurrency(discountAmount)}</div>
        <div>Tax ({initialTaxRate}%): {formatCurrency(taxAmount)}</div>
        <div className="font-bold">Total: {formatCurrency(total)}</div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="discount">Discount (%)</Label>
          <Controller
            control={control}
            name="discount"
            render={({ field }) => <Input id="discount" type="number" {...field} onChange={e => field.onChange(parseFloat(e.target.value))} />}
          />
        </div>
        <div>
          <Label htmlFor="taxRate">Tax Rate (%)</Label>
          <Controller
            control={control}
            name="taxRate"
            render={({ field }) => <Input id="taxRate" type="number" {...field} onChange={e => field.onChange(parseFloat(e.target.value))} />}
          />
        </div>
      </div>
      <div className="space-y-2 text-right">
        <div>Subtotal: {formatCurrency(subtotal)}</div>
        <div>Discount ({discount}%): -{formatCurrency(discountAmount)}</div>
        <div>Tax ({taxRate}%): {formatCurrency(taxAmount)}</div>
        <div className="font-bold">Total: {formatCurrency(total)}</div>
      </div>
    </div>
  );
}

export { InvoiceTotals };