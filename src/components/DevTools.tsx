import React, { useState } from 'react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Trash2, RefreshCw, Database, AlertTriangle } from 'lucide-react';
import VendorApiService from '../services/vendorApi';
import { toast } from './ui/sonner';

export const DevTools: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  const handleClearData = async () => {
    if (window.confirm('Are you sure you want to clear ALL vendor data? This cannot be undone.')) {
      try {
        await VendorApiService.clearAllData();
        toast.success('All vendor data cleared');
        window.location.reload(); // Refresh to update the UI
      } catch (error) {
        toast.error('Failed to clear data');
      }
    }
  };

  const handleResetData = async () => {
    if (window.confirm('Reset to default mock data? This will replace all current data.')) {
      try {
        await VendorApiService.resetToDefaults();
        toast.success('Data reset to defaults');
        window.location.reload(); // Refresh to update the UI
      } catch (error) {
        toast.error('Failed to reset data');
      }
    }
  };

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {!isVisible ? (
        <Button
          onClick={() => setIsVisible(true)}
          className="rounded-full w-12 h-12 bg-orange-500 hover:bg-orange-600 text-white shadow-lg"
          title="Dev Tools"
        >
          <Database className="w-5 h-5" />
        </Button>
      ) : (
        <Card className="p-4 bg-background border-orange-500 shadow-xl min-w-[300px]">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-orange-500" />
              <h3 className="font-semibold text-foreground">Dev Tools</h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
          
          <div className="space-y-3">
            <div className="text-sm text-muted-foreground mb-3">
              <strong>⚠️ Mock Data Mode</strong><br />
              Currently using localStorage for data persistence.
              Data will persist between sessions but is not in a real database.
            </div>
            
            <Button
              onClick={handleClearData}
              variant="destructive"
              size="sm"
              className="w-full"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Clear All Data
            </Button>
            
            <Button
              onClick={handleResetData}
              variant="outline"
              size="sm"
              className="w-full"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Reset to Defaults
            </Button>
            
            <div className="text-xs text-muted-foreground pt-2 border-t">
              To connect to a real database, update the VendorApiService to use actual API endpoints.
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};