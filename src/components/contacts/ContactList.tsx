import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  User, 
  Mail, 
  Phone, 
  Building2,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  Users,
  Search,
  Filter,
  Download,
  Plus
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Contact, 
  ContactSearchFilters, 
  PaginatedContacts,
  ContactLevel,
  ContactStatus,
  CONTACT_LEVELS,
  CONTACT_STATUSES
} from '../../types/contact';

interface ContactListProps {
  contacts: PaginatedContacts;
  onContactSelect?: (contact: Contact) => void;
  onContactEdit?: (contact: Contact) => void;
  onContactDelete?: (contactId: number) => void;
  onContactView?: (contact: Contact) => void;
  onSearch?: (filters: ContactSearchFilters) => void;
  onExport?: () => void;
  onBulkAction?: (action: string, contactIds: number[]) => void;
  isLoading?: boolean;
  showActions?: boolean;
  showBulkActions?: boolean;
  showFilters?: boolean;
  accountId?: number;
}

export const ContactList: React.FC<ContactListProps> = ({
  contacts,
  onContactSelect,
  onContactEdit,
  onContactDelete,
  onContactView,
  onSearch,
  onExport,
  onBulkAction,
  isLoading = false,
  showActions = true,
  showBulkActions = false,
  showFilters = true,
  accountId
}) => {
  const [selectedContacts, setSelectedContacts] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<ContactSearchFilters>({
    account_id: accountId
  });

  // Handle search
  const handleSearch = (newSearchTerm: string) => {
    setSearchTerm(newSearchTerm);
    const newFilters = { ...filters, search: newSearchTerm };
    setFilters(newFilters);
    onSearch?.(newFilters);
  };

  // Handle filter change
  const handleFilterChange = (key: keyof ContactSearchFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onSearch?.(newFilters);
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedContacts(contacts.contacts.map(c => c.id));
    } else {
      setSelectedContacts([]);
    }
  };

  // Handle individual selection
  const handleSelectContact = (contactId: number, checked: boolean) => {
    if (checked) {
      setSelectedContacts([...selectedContacts, contactId]);
    } else {
      setSelectedContacts(selectedContacts.filter(id => id !== contactId));
    }
  };

  // Get contact initials for avatar
  const getContactInitials = (contact: Contact) => {
    const firstName = contact.first_name || contact.firstName || '';
    const lastName = contact.last_name || contact.lastName || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Get status badge variant
  const getStatusVariant = (status: ContactStatus) => {
    switch (status) {
      case ContactStatus.ACTIVE:
        return 'default';
      case ContactStatus.INACTIVE:
        return 'secondary';
      case ContactStatus.ARCHIVED:
        return 'outline';
      case ContactStatus.PENDING:
        return 'destructive';
      default:
        return 'default';
    }
  };

  // Get level badge variant
  const getLevelVariant = (level?: ContactLevel) => {
    switch (level) {
      case ContactLevel.PRIMARY:
        return 'default';
      case ContactLevel.SECONDARY:
        return 'secondary';
      case ContactLevel.TERTIARY:
        return 'outline';
      default:
        return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Search & Filter Contacts
              </div>
              <div className="flex items-center gap-2">
                {onExport && (
                  <Button variant="outline" size="sm" onClick={onExport}>
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                )}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search Bar */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search contacts by name, email, title, or department..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Select
                value={filters.department?.[0] || 'all'}
                onValueChange={(value) => handleFilterChange('department', value === 'all' ? undefined : [value])}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="Sales">Sales</SelectItem>
                  <SelectItem value="Marketing">Marketing</SelectItem>
                  <SelectItem value="Engineering">Engineering</SelectItem>
                  <SelectItem value="Finance">Finance</SelectItem>
                  <SelectItem value="HR">HR</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filters.level?.[0] || 'all'}
                onValueChange={(value) => handleFilterChange('level', value === 'all' ? undefined : [value as ContactLevel])}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  {CONTACT_LEVELS.map((level) => (
                    <SelectItem key={level} value={level}>
                      {level}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={filters.status?.[0] || 'all'}
                onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : [value as ContactStatus])}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {CONTACT_STATUSES.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status === ContactStatus.ACTIVE ? 'Active' : 
                       status === ContactStatus.INACTIVE ? 'Inactive' :
                       status === ContactStatus.ARCHIVED ? 'Archived' : 'Pending'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={filters.has_direct_reports?.toString() || 'all'}
                onValueChange={(value) => handleFilterChange('has_direct_reports', value === 'true' ? true : value === 'false' ? false : undefined)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Manager Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Contacts</SelectItem>
                  <SelectItem value="true">Managers Only</SelectItem>
                  <SelectItem value="false">Individual Contributors</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk Actions */}
      {showBulkActions && selectedContacts.length > 0 && (
        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {selectedContacts.length} contact{selectedContacts.length !== 1 ? 's' : ''} selected
              </span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onBulkAction?.('export', selectedContacts)}
                >
                  Export Selected
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onBulkAction?.('delete', selectedContacts)}
                >
                  Delete Selected
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Contacts Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Contacts ({contacts.pagination.total})
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  {showBulkActions && (
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedContacts.length === contacts.contacts.length && contacts.contacts.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                  )}
                  <TableHead>Contact</TableHead>
                  <TableHead>Title & Department</TableHead>
                  <TableHead>Contact Info</TableHead>
                  <TableHead>Reports To</TableHead>
                  <TableHead>Level</TableHead>
                  <TableHead>Status</TableHead>
                  {showActions && <TableHead className="w-12">Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {contacts.contacts.map((contact) => (
                  <motion.tr
                    key={contact.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="group hover:bg-muted/50 cursor-pointer"
                    onClick={() => onContactSelect?.(contact)}
                  >
                    {showBulkActions && (
                      <TableCell onClick={(e) => e.stopPropagation()}>
                        <Checkbox
                          checked={selectedContacts.includes(contact.id)}
                          onCheckedChange={(checked) => handleSelectContact(contact.id, checked as boolean)}
                        />
                      </TableCell>
                    )}
                    
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${contact.full_name}`} />
                          <AvatarFallback>{getContactInitials(contact)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {contact.full_name || `${contact.first_name || ''} ${contact.last_name}`.trim()}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {contact.account?.name}
                          </div>
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div>
                        {contact.title && (
                          <div className="font-medium">{contact.title}</div>
                        )}
                        {contact.department && (
                          <div className="text-sm text-muted-foreground">{contact.department}</div>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm">
                          <Mail className="h-3 w-3" />
                          {contact.email}
                        </div>
                        {contact.phone && (
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Phone className="h-3 w-3" />
                            {contact.phone}
                          </div>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      {contact.reportsTo ? (
                        <div className="flex items-center gap-2">
                          <UserCheck className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{contact.reportsTo.full_name || contact.reportsTo.fullName}</span>
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">—</span>
                      )}
                    </TableCell>

                    <TableCell>
                      {contact.level && (
                        <Badge variant={getLevelVariant(contact.level)}>
                          {contact.level}
                        </Badge>
                      )}
                    </TableCell>

                    <TableCell>
                      <Badge variant={getStatusVariant(contact.status)}>
                        {contact.status === ContactStatus.ACTIVE ? 'Active' : 
                         contact.status === ContactStatus.INACTIVE ? 'Inactive' :
                         contact.status === ContactStatus.ARCHIVED ? 'Archived' : 'Pending'}
                      </Badge>
                    </TableCell>

                    {showActions && (
                      <TableCell onClick={(e) => e.stopPropagation()}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            {onContactView && (
                              <DropdownMenuItem onClick={() => onContactView(contact)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                            )}
                            {onContactEdit && (
                              <DropdownMenuItem onClick={() => onContactEdit(contact)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Contact
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            {onContactDelete && (
                              <DropdownMenuItem 
                                onClick={() => onContactDelete(contact.id)}
                                className="text-destructive"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Contact
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    )}
                  </motion.tr>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {contacts.pagination.totalPages > 1 && (
            <div className="flex items-center justify-between px-2 py-4">
              <div className="text-sm text-muted-foreground">
                Showing {((contacts.pagination.page - 1) * contacts.pagination.limit) + 1} to{' '}
                {Math.min(contacts.pagination.page * contacts.pagination.limit, contacts.pagination.total)} of{' '}
                {contacts.pagination.total} contacts
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={contacts.pagination.page === 1}
                  onClick={() => handleFilterChange('page', contacts.pagination.page - 1)}
                >
                  Previous
                </Button>
                <span className="text-sm">
                  Page {contacts.pagination.page} of {contacts.pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={contacts.pagination.page === contacts.pagination.totalPages}
                  onClick={() => handleFilterChange('page', contacts.pagination.page + 1)}
                >
                  Next
                </Button>
              </div>
            </div>
          )}

          {/* Empty State */}
          {contacts.contacts.length === 0 && !isLoading && (
            <div className="text-center py-12">
              <User className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No contacts found</h3>
              <p className="mt-2 text-muted-foreground">
                {searchTerm || Object.keys(filters).length > 1
                  ? 'Try adjusting your search or filters'
                  : 'Get started by creating your first contact'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};