import React, { useState } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  ContactSearchFilters, 
  ContactLevel, 
  ContactStatus,
  CONTACT_LEVELS,
  CONTACT_STATUSES 
} from '../../types/contact';

interface ContactSearchProps {
  onSearch: (filters: ContactSearchFilters) => void;
  initialFilters?: ContactSearchFilters;
  showAdvanced?: boolean;
}

export const ContactSearch: React.FC<ContactSearchProps> = ({
  onSearch,
  initialFilters = {},
  showAdvanced = true
}) => {
  const [searchTerm, setSearchTerm] = useState(initialFilters.search || '');
  const [filters, setFilters] = useState<ContactSearchFilters>(initialFilters);
  const [showFilters, setShowFilters] = useState(false);

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    const newFilters = { ...filters, search: value };
    setFilters(newFilters);
    onSearch(newFilters);
  };

  // Handle filter change
  const handleFilterChange = (key: keyof ContactSearchFilters, value: unknown) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onSearch(newFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    const clearedFilters: ContactSearchFilters = { search: searchTerm };
    setFilters(clearedFilters);
    onSearch(clearedFilters);
  };

  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.department?.length) count++;
    if (filters.level?.length) count++;
    if (filters.status?.length) count++;
    if (filters.has_direct_reports !== undefined) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search contacts by name, email, title, or department..."
            value={searchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
        
        {showAdvanced && (
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-1">
                {activeFilterCount}
              </Badge>
            )}
          </Button>
        )}
      </div>

      {/* Advanced Filters */}
      {showAdvanced && showFilters && (
        <Card>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Advanced Filters</CardTitle>
              <div className="flex items-center gap-2">
                {activeFilterCount > 0 && (
                  <Button variant="ghost" size="sm" onClick={clearFilters}>
                    Clear All
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFilters(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Department Filter */}
              <div>
                <label className="text-sm font-medium mb-2 block">Department</label>
                <Select
                  value={filters.department?.[0] || 'all'}
                  onValueChange={(value) => 
                    handleFilterChange('department', value === 'all' ? undefined : [value])
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Departments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    <SelectItem value="Sales">Sales</SelectItem>
                    <SelectItem value="Marketing">Marketing</SelectItem>
                    <SelectItem value="Engineering">Engineering</SelectItem>
                    <SelectItem value="Finance">Finance</SelectItem>
                    <SelectItem value="HR">HR</SelectItem>
                    <SelectItem value="Operations">Operations</SelectItem>
                    <SelectItem value="Customer Success">Customer Success</SelectItem>
                    <SelectItem value="Legal">Legal</SelectItem>
                    <SelectItem value="IT">IT</SelectItem>
                    <SelectItem value="Executive">Executive</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Level Filter */}
              <div>
                <label className="text-sm font-medium mb-2 block">Level</label>
                <Select
                  value={filters.level?.[0] || 'all'}
                  onValueChange={(value) => 
                    handleFilterChange('level', value === 'all' ? undefined : [value as ContactLevel])
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Levels" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    {CONTACT_LEVELS.map((level) => (
                      <SelectItem key={level} value={level}>
                        {level}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Status Filter */}
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <Select
                  value={filters.status?.[0] || 'all'}
                  onValueChange={(value) => 
                    handleFilterChange('status', value === 'all' ? undefined : [value as ContactStatus])
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {CONTACT_STATUSES.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status === ContactStatus.ACTIVE ? 'Active' : 
                         status === ContactStatus.INACTIVE ? 'Inactive' :
                         status === ContactStatus.ARCHIVED ? 'Archived' : 'Pending'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Manager Status Filter */}
              <div>
                <label className="text-sm font-medium mb-2 block">Manager Status</label>
                <Select
                  value={filters.hasDirectReports?.toString() || 'all'}
                  onValueChange={(value) => 
                    handleFilterChange('hasDirectReports', 
                      value === 'true' ? true : value === 'false' ? false : undefined)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Contacts" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Contacts</SelectItem>
                    <SelectItem value="true">Managers Only</SelectItem>
                    <SelectItem value="false">Individual Contributors</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Active Filters Display */}
            {activeFilterCount > 0 && (
              <div className="pt-4 border-t">
                <div className="flex items-center gap-2 flex-wrap">
                  <span className="text-sm font-medium">Active filters:</span>
                  
                  {filters.department?.[0] && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      Department: {filters.department[0]}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => handleFilterChange('department', undefined)}
                      />
                    </Badge>
                  )}
                  
                  {filters.level?.[0] && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      Level: {filters.level[0]}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => handleFilterChange('level', undefined)}
                      />
                    </Badge>
                  )}
                  
                  {filters.status?.[0] && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      Status: {filters.status[0] === ContactStatus.ACTIVE ? 'Active' : 
                               filters.status[0] === ContactStatus.INACTIVE ? 'Inactive' :
                               filters.status[0] === ContactStatus.ARCHIVED ? 'Archived' : 'Pending'}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => handleFilterChange('status', undefined)}
                      />
                    </Badge>
                  )}
                  
                  {filters.hasDirectReports !== undefined && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      {filters.hasDirectReports ? 'Managers Only' : 'Individual Contributors'}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => handleFilterChange('hasDirectReports', undefined)}
                      />
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};