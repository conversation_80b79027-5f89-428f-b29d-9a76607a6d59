import React from 'react';
import { Users, ChevronRight, ChevronDown } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Contact } from '../../types/contact';

interface ContactHierarchyProps {
  contacts: Contact[];
  onContactClick?: (contact: Contact) => void;
  expandedNodes?: Set<number>;
  onToggleExpand?: (contactId: number) => void;
}

export const ContactHierarchy: React.FC<ContactHierarchyProps> = ({
  contacts,
  onContactClick,
  expandedNodes = new Set(),
  onToggleExpand
}) => {
  // Build hierarchy tree from flat contact list
  const buildHierarchyTree = (contacts: Contact[]) => {
    const contactMap = new Map<number, Contact & { children: Contact[] }>();
    const rootContacts: (Contact & { children: Contact[] })[] = [];

    // Initialize all contacts with children array
    contacts.forEach(contact => {
      contactMap.set(contact.id, { ...contact, children: [] });
    });

    // Build parent-child relationships
    contacts.forEach(contact => {
      const contactWithChildren = contactMap.get(contact.id)!;
      
      const reportsToId = contact.reports_to_id || contact.reportsToId;
      if (reportsToId && contactMap.has(reportsToId)) {
        const parent = contactMap.get(reportsToId)!;
        parent.children.push(contactWithChildren);
      } else {
        rootContacts.push(contactWithChildren);
      }
    });

    return rootContacts;
  };

  // Render contact node
  const renderContactNode = (contact: Contact & { children: Contact[] }, level = 0) => {
    const hasChildren = contact.children.length > 0;
    const isExpanded = expandedNodes.has(contact.id);
    const indentClass = level > 0 ? `ml-${level * 6}` : '';

    return (
      <div key={contact.id} className="space-y-2">
        <div className={`flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors ${indentClass}`}>
          {/* Expand/Collapse Button */}
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => onToggleExpand?.(contact.id)}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
          
          {/* Spacer for contacts without children */}
          {!hasChildren && <div className="w-6" />}

          {/* Contact Avatar */}
          <Avatar className="h-8 w-8">
            <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${contact.full_name || contact.fullName}`} />
            <AvatarFallback className="text-xs">
              {`${(contact.first_name || contact.firstName)?.charAt(0) || ''}${(contact.last_name || contact.lastName)?.charAt(0) || ''}`.toUpperCase()}
            </AvatarFallback>
          </Avatar>

          {/* Contact Info */}
          <div 
            className="flex-1 cursor-pointer"
            onClick={() => onContactClick?.(contact)}
          >
            <div className="font-medium">
              {contact.full_name || contact.fullName || `${contact.first_name || contact.firstName || ''} ${contact.last_name || contact.lastName}`.trim()}
            </div>
            <div className="text-sm text-muted-foreground">
              {contact.title && `${contact.title} • `}
              {contact.department}
            </div>
          </div>

          {/* Direct Reports Count */}
          {hasChildren && (
            <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
              {contact.children.length} report{contact.children.length !== 1 ? 's' : ''}
            </div>
          )}
        </div>

        {/* Render Children */}
        {hasChildren && isExpanded && (
          <div className="space-y-1">
            {contact.children.map(child => renderContactNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const hierarchyTree = buildHierarchyTree(contacts);

  if (contacts.length === 0) {
    return (
      <Card>
        <CardContent className="py-12 text-center">
          <Users className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">No contacts found</h3>
          <p className="mt-2 text-muted-foreground">
            No contacts available to display in hierarchy view.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Contact Hierarchy
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {hierarchyTree.map(contact => renderContactNode(contact))}
        </div>
      </CardContent>
    </Card>
  );
};