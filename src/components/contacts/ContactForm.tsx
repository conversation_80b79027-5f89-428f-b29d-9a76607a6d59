import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { motion } from 'framer-motion';
import { 
  User, 
  Mail, 
  Phone, 
  Building2,
  MapPin,
  Users,
  Briefcase,
  Calendar,
  MessageSquare,
  Globe,
  Loader2,
  <PERSON>r<PERSON>heck,
  PhoneCall
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { AddressForm } from '../accounts/AddressForm';
import { 
  contactCreateSchema, 
  contactUpdateSchema
} from '../../lib/validation/contactValidation';
import { 
  Contact, 
  ContactCreateRequest, 
  ContactUpdateRequest,
  ContactSalutation,
  ContactLeadSource,
  ContactLevel,
  CONTACT_SALUTATIONS,
  CONTACT_LEAD_SOURCES,
  CONTACT_LEVELS
} from '../../types/contact';
import { Account } from '../../types/account';

interface ContactFormProps {
  initialData?: Partial<Contact>;
  onSubmit: (data: ContactCreateRequest | ContactUpdateRequest) => Promise<void>;
  isLoading?: boolean;
  mode: 'create' | 'edit';
  accountId?: number;
  availableAccounts?: Account[];
  availableContacts?: Contact[];
}

export const ContactForm: React.FC<ContactFormProps> = ({
  initialData,
  onSubmit,
  isLoading = false,
  mode,
  accountId,
  availableAccounts = [],
  availableContacts = []
}) => {
  const [departments, setDepartments] = useState<string[]>([]);
  const [loadingDepartments, setLoadingDepartments] = useState(false);

  const validationSchema = mode === 'create' ? contactCreateSchema : contactUpdateSchema;

  const form = useForm<ContactCreateRequest | ContactUpdateRequest>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      account_id: accountId || initialData?.account_id || initialData?.accountId || undefined,
      first_name: initialData?.first_name || initialData?.firstName || '',
      last_name: initialData?.last_name || initialData?.lastName || '',
      salutation: initialData?.salutation || undefined,
      title: initialData?.title || '',
      department: initialData?.department || '',
      phone: initialData?.phone || '',
      mobile_phone: initialData?.mobile_phone || initialData?.mobilePhone || '',
      home_phone: initialData?.home_phone || initialData?.homePhone || '',
      other_phone: initialData?.other_phone || initialData?.otherPhone || '',
      fax: initialData?.fax || '',
      email: initialData?.email || '',
      mailing_address: {
        street: initialData?.mailing_address?.street || initialData?.mailingAddress?.street || '',
        city: initialData?.mailing_address?.city || initialData?.mailingAddress?.city || '',
        state: initialData?.mailing_address?.state || initialData?.mailingAddress?.state || '',
        postalCode: initialData?.mailing_address?.postalCode || initialData?.mailingAddress?.postalCode || '',
        country: initialData?.mailing_address?.country || initialData?.mailingAddress?.country || 'United States',
        latitude: initialData?.mailing_address?.latitude || initialData?.mailingAddress?.latitude || undefined,
        longitude: initialData?.mailing_address?.longitude || initialData?.mailingAddress?.longitude || undefined,
      },
      other_address: {
        street: initialData?.other_address?.street || initialData?.otherAddress?.street || '',
        city: initialData?.other_address?.city || initialData?.otherAddress?.city || '',
        state: initialData?.other_address?.state || initialData?.otherAddress?.state || '',
        postalCode: initialData?.other_address?.postalCode || initialData?.otherAddress?.postalCode || '',
        country: initialData?.other_address?.country || initialData?.otherAddress?.country || 'United States',
        latitude: initialData?.other_address?.latitude || initialData?.otherAddress?.latitude || undefined,
        longitude: initialData?.other_address?.longitude || initialData?.otherAddress?.longitude || undefined,
      },
      reports_to_id: initialData?.reports_to_id || initialData?.reportsToId || undefined,
      assistant_name: initialData?.assistant_name || initialData?.assistantName || '',
      assistant_phone: initialData?.assistant_phone || initialData?.assistantPhone || '',
      birthdate: initialData?.birthdate ? new Date(initialData.birthdate) : undefined,
      lead_source: initialData?.lead_source || initialData?.leadSource || undefined,
      description: initialData?.description || '',
      languages: initialData?.languages || '',
      level: initialData?.level || undefined,
      do_not_call: initialData?.do_not_call || initialData?.doNotCall || false,
      has_opted_out_of_email: initialData?.has_opted_out_of_email || initialData?.hasOptedOutOfEmail || false,
      owner_id: initialData?.owner_id || initialData?.ownerId || undefined,
      custom_fields: initialData?.custom_fields || initialData?.customFields || {},
    },
  });

  // Load departments for autocomplete
  useEffect(() => {
    const loadDepartments = async () => {
      setLoadingDepartments(true);
      try {
        // This would be replaced with actual API call
        const mockDepartments = [
          'Sales', 'Marketing', 'Engineering', 'Finance', 'HR', 
          'Operations', 'Customer Success', 'Legal', 'IT', 'Executive'
        ];
        setDepartments(mockDepartments);
      } catch (error) {
        console.error('Failed to load departments:', error);
      } finally {
        setLoadingDepartments(false);
      }
    };

    loadDepartments();
  }, []);

  // Copy mailing address to other address
  const copyMailingToOther = () => {
    const mailingAddress = form.getValues('mailing_address');
    form.setValue('other_address', { ...mailingAddress });
  };

  // Handle form submission
  const handleSubmit = async (data: ContactCreateRequest | ContactUpdateRequest) => {
    try {
      // Transform form data if needed
      const submitData = {
        ...data,
        birthdate: data.birthdate ? data.birthdate.toISOString().split('T')[0] : undefined,
      };
      
      await onSubmit(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  // Filter available contacts for reports_to (exclude self and subordinates)
  const availableManagers = availableContacts.filter(contact => 
    contact.id !== initialData?.id && 
    contact.accountId === (accountId || initialData?.accountId)
  );

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Account Selection (if not pre-selected) */}
            {!accountId && (
              <FormField
                control={form.control}
                name="account_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      Account *
                    </FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(parseInt(value))} 
                      value={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select an account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {availableAccounts.map((account) => (
                          <SelectItem key={account.id} value={account.id.toString()}>
                            {account.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Salutation */}
              <FormField
                control={form.control}
                name="salutation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Salutation</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {CONTACT_SALUTATIONS.map((salutation) => (
                          <SelectItem key={salutation} value={salutation}>
                            {salutation}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* First Name */}
              <FormField
                control={form.control}
                name="first_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Last Name */}
              <FormField
                control={form.control}
                name="last_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Title */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Briefcase className="h-4 w-4" />
                      Title
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="CEO, Manager, Developer..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Department */}
              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      Department
                    </FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Sales, Marketing, Engineering..." 
                        {...field}
                        list="departments"
                      />
                    </FormControl>
                    <datalist id="departments">
                      {departments.map((dept) => (
                        <option key={dept} value={dept} />
                      ))}
                    </datalist>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Email */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Email Address *
                  </FormLabel>
                  <FormControl>
                    <Input 
                      type="email" 
                      placeholder="<EMAIL>" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Phone */}
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Mobile Phone */}
              <FormField
                control={form.control}
                name="mobile_phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mobile Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Home Phone */}
              <FormField
                control={form.control}
                name="home_phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Home Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Other Phone */}
              <FormField
                control={form.control}
                name="other_phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Other Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Fax */}
              <FormField
                control={form.control}
                name="fax"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fax</FormLabel>
                    <FormControl>
                      <Input placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Address Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Address Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Mailing Address */}
            <div>
              <h4 className="text-sm font-medium mb-4">Mailing Address</h4>
              <AddressForm 
                form={form} 
                fieldPrefix="mailing_address" 
              />
            </div>

            <Separator />

            {/* Other Address */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-sm font-medium">Other Address</h4>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={copyMailingToOther}
                >
                  Copy from Mailing
                </Button>
              </div>
              <AddressForm 
                form={form} 
                fieldPrefix="other_address" 
              />
            </div>
          </CardContent>
        </Card>

        {/* Hierarchy & Relationships */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Hierarchy & Relationships
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Reports To */}
              <FormField
                control={form.control}
                name="reports_to_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <UserCheck className="h-4 w-4" />
                      Reports To
                    </FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(value === "none" ? undefined : parseInt(value))} 
                      value={field.value?.toString() || "none"}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select manager" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">No Manager</SelectItem>
                        {availableManagers.map((contact) => (
                          <SelectItem key={contact.id} value={contact.id.toString()}>
                            {contact.full_name || `${contact.first_name || contact.firstName} ${contact.last_name || contact.lastName}`}
                            {contact.title && ` - ${contact.title}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the person this contact reports to
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Assistant Name */}
              <FormField
                control={form.control}
                name="assistant_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assistant Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Assistant's name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Assistant Phone */}
              <FormField
                control={form.control}
                name="assistant_phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assistant Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Additional Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Lead Source */}
              <FormField
                control={form.control}
                name="lead_source"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lead Source</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select source" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {CONTACT_LEAD_SOURCES.map((source) => (
                          <SelectItem key={source} value={source}>
                            {source}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Level */}
              <FormField
                control={form.control}
                name="level"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Level</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select level" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {CONTACT_LEVELS.map((level) => (
                          <SelectItem key={level} value={level}>
                            {level}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Birthdate */}
              <FormField
                control={form.control}
                name="birthdate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 mt-2">
                      <Calendar className="h-4 w-4" />
                      Birthdate
                    </FormLabel>
                    <FormControl>
                      <Input 
                        type="date" 
                        {...field}
                        value={field.value ? field.value.toISOString().split('T')[0] : ''}
                        onChange={(e) => field.onChange(e.target.value ? new Date(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Languages */}
            <FormField
              control={form.control}
              name="languages"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    Languages
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="English, Spanish, French..." 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Comma-separated list of languages spoken
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Additional notes about this contact..."
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Communication Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PhoneCall className="h-5 w-5" />
              Communication Preferences
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              {/* Do Not Call */}
              <FormField
                control={form.control}
                name="do_not_call"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Do Not Call
                      </FormLabel>
                      <FormDescription>
                        This contact has requested not to be called
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Has Opted Out of Email */}
              <FormField
                control={form.control}
                name="has_opted_out_of_email"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Opted Out of Email
                      </FormLabel>
                      <FormDescription>
                        This contact has opted out of email communications
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => form.reset()}
            disabled={isLoading}
          >
            Reset
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {mode === 'create' ? 'Create Contact' : 'Update Contact'}
          </Button>
        </div>
      </form>
    </Form>
  );
};