import React from 'react';
import { motion } from 'framer-motion';
import { User, Mail, Phone, Building2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Contact, ContactStatus } from '../../types/contact';

interface ContactCardProps {
  contact: Contact;
  onClick?: (contact: Contact) => void;
  className?: string;
}

export const ContactCard: React.FC<ContactCardProps> = ({
  contact,
  onClick,
  className = ''
}) => {
  // Get contact initials for avatar
  const getContactInitials = (contact: Contact) => {
    const firstName = contact.first_name || contact.firstName || '';
    const lastName = contact.last_name || contact.lastName || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Get status badge variant
  const getStatusVariant = (status: ContactStatus) => {
    switch (status) {
      case ContactStatus.ACTIVE:
        return 'default';
      case ContactStatus.INACTIVE:
        return 'secondary';
      case ContactStatus.ARCHIVED:
        return 'outline';
      case ContactStatus.PENDING:
        return 'destructive';
      default:
        return 'default';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02 }}
      className={className}
    >
      <Card 
        className="cursor-pointer hover:shadow-md transition-shadow"
        onClick={() => onClick?.(contact)}
      >
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <Avatar className="h-12 w-12">
              <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${contact.fullName}`} />
              <AvatarFallback>{getContactInitials(contact)}</AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-semibold text-lg">
                    {contact.full_name || contact.fullName || `${contact.first_name || contact.firstName || ''} ${contact.last_name || contact.lastName}`.trim()}
                  </h3>
                  {contact.title && (
                    <p className="text-muted-foreground">{contact.title}</p>
                  )}
                </div>
                <Badge variant={getStatusVariant(contact.status)}>
                  {contact.status === ContactStatus.ACTIVE ? 'Active' : 
                   contact.status === ContactStatus.INACTIVE ? 'Inactive' :
                   contact.status === ContactStatus.ARCHIVED ? 'Archived' : 'Pending'}
                </Badge>
              </div>
              
              <div className="mt-3 space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  <span className="truncate">{contact.email}</span>
                </div>
                
                {contact.phone && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Phone className="h-4 w-4" />
                    <span>{contact.phone}</span>
                  </div>
                )}
                
                {contact.account?.name && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Building2 className="h-4 w-4" />
                    <span className="truncate">{contact.account.name}</span>
                  </div>
                )}
                
                {contact.department && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <User className="h-4 w-4" />
                    <span>{contact.department}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};