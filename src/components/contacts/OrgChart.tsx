import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Users, Maximize2, Minimize2, Download } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Contact } from '../../types/contact';

interface OrgChartNode extends Contact {
  children: OrgChartNode[];
  level: number;
}

interface OrgChartProps {
  contacts: Contact[];
  onContactClick?: (contact: Contact) => void;
  onExport?: () => void;
  className?: string;
}

export const OrgChart: React.FC<OrgChartProps> = ({
  contacts,
  onContactClick,
  onExport,
  className = ''
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set());

  // Build org chart tree from flat contact list
  const buildOrgChartTree = (contacts: Contact[]): OrgChartNode[] => {
    const contactMap = new Map<number, OrgChartNode>();
    const rootNodes: OrgChartNode[] = [];

    // Initialize all contacts as nodes
    contacts.forEach(contact => {
      contactMap.set(contact.id, {
        ...contact,
        children: [],
        level: 0
      });
    });

    // Build parent-child relationships and calculate levels
    contacts.forEach(contact => {
      const node = contactMap.get(contact.id)!;
      
      const reportsToId = contact.reports_to_id || contact.reportsToId;
      if (reportsToId && contactMap.has(reportsToId)) {
        const parent = contactMap.get(reportsToId)!;
        node.level = parent.level + 1;
        parent.children.push(node);
      } else {
        rootNodes.push(node);
      }
    });

    return rootNodes;
  };

  // Toggle node expansion
  const toggleNodeExpansion = (nodeId: number) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  // Render org chart node
  const renderOrgNode = (node: OrgChartNode) => {
    const hasChildren = node.children.length > 0;
    const isExpanded = expandedNodes.has(node.id) || node.level === 0; // Root nodes always expanded

    return (
      <motion.div
        key={node.id}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex flex-col items-center"
      >
        {/* Node Card */}
        <motion.div
          whileHover={{ scale: 1.05 }}
          className="relative"
        >
          <Card 
            className="w-64 cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => onContactClick?.(node)}
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${node.full_name || node.fullName}`} />
                  <AvatarFallback>
                    {`${(node.first_name || node.firstName)?.charAt(0) || ''}${(node.last_name || node.lastName)?.charAt(0) || ''}`.toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold truncate">
                    {node.full_name || node.fullName || `${node.first_name || node.firstName || ''} ${node.last_name || node.lastName}`.trim()}
                  </h4>
                  {node.title && (
                    <p className="text-sm text-muted-foreground truncate">{node.title}</p>
                  )}
                  {node.department && (
                    <p className="text-xs text-muted-foreground truncate">{node.department}</p>
                  )}
                </div>
              </div>
              
              {hasChildren && (
                <div className="mt-3 flex items-center justify-between">
                  <Badge variant="outline" className="text-xs">
                    {node.children.length} report{node.children.length !== 1 ? 's' : ''}
                  </Badge>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleNodeExpansion(node.id);
                    }}
                    className="h-6 px-2 text-xs"
                  >
                    {isExpanded ? 'Collapse' : 'Expand'}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Connection Lines and Children */}
        {hasChildren && isExpanded && (
          <div className="mt-6">
            {/* Vertical line down */}
            <div className="w-px h-6 bg-border mx-auto" />
            
            {/* Horizontal line across children */}
            {node.children.length > 1 && (
              <div className="relative h-px bg-border" style={{ width: `${(node.children.length - 1) * 280}px` }}>
                {/* Vertical lines to each child */}
                {node.children.map((_, index) => (
                  <div
                    key={index}
                    className="absolute w-px h-6 bg-border"
                    style={{ left: `${index * 280}px` }}
                  />
                ))}
              </div>
            )}
            
            {/* Single child gets direct line */}
            {node.children.length === 1 && (
              <div className="w-px h-6 bg-border mx-auto" />
            )}
            
            {/* Children nodes */}
            <div className="flex gap-4 mt-6 justify-center">
              {node.children.map(child => renderOrgNode(child))}
            </div>
          </div>
        )}
      </motion.div>
    );
  };

  const orgTree = buildOrgChartTree(contacts);

  if (contacts.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="py-12 text-center">
          <Users className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">No organizational data</h3>
          <p className="mt-2 text-muted-foreground">
            No contacts available to display in organizational chart.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className} ${isFullscreen ? 'fixed inset-4 z-50' : ''}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Organizational Chart
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {onExport && (
              <Button variant="outline" size="sm" onClick={onExport}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            )}
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFullscreen(!isFullscreen)}
            >
              {isFullscreen ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className={`${isFullscreen ? 'overflow-auto max-h-full' : ''}`}>
        <div className="flex justify-center">
          <div className="space-y-8">
            {orgTree.map(rootNode => renderOrgNode(rootNode))}
          </div>
        </div>
        
        {/* Legend */}
        <div className="mt-8 pt-4 border-t">
          <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-primary rounded" />
              <span>Active Contact</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-px h-4 bg-border" />
              <span>Reporting Line</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">N reports</Badge>
              <span>Direct Reports Count</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};