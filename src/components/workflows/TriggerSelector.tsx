import React, { useState } from 'react';
import { 
  UserPlus, 
  FileText, 
  Receipt, 
  TrendingUp, 
  Play, 
  Clock,
  Settings,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { WorkflowTrigger, TriggerCondition } from '@/store/slices/workflowsSlice';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface TriggerSelectorProps {
  selectedTrigger: WorkflowTrigger | null;
  onTriggerChange: (trigger: WorkflowTrigger) => void;
}

const TriggerSelector: React.FC<TriggerSelectorProps> = ({
  selectedTrigger,
  onTriggerChange
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const triggerTypes = [
    {
      type: 'vendor_onboarded',
      label: 'Vendor Onboarded',
      description: 'Triggers when a new vendor is successfully onboarded',
      icon: UserPlus,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      type: 'contract_expiring',
      label: 'Contract Expiring',
      description: 'Triggers when a contract is approaching expiration',
      icon: FileText,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      type: 'invoice_approved',
      label: 'Invoice Approved',
      description: 'Triggers when an invoice receives final approval',
      icon: Receipt,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      type: 'performance_score_changed',
      label: 'Performance Score Changed',
      description: 'Triggers when vendor performance score changes significantly',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      type: 'manual',
      label: 'Manual Trigger',
      description: 'Manually triggered workflow that can be started on demand',
      icon: Play,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100'
    }
  ];

  const handleTriggerTypeChange = (type: string) => {
    const triggerType = type as WorkflowTrigger['type'];
    const newTrigger: WorkflowTrigger = {
      type: triggerType,
      config: {}
    };
    
    onTriggerChange(newTrigger);
  };

  const handleConditionAdd = () => {
    if (!selectedTrigger) return;
    
    const newCondition: TriggerCondition = {
      field: '',
      operator: 'equals',
      value: ''
    };
    
    const updatedTrigger: WorkflowTrigger = {
      ...selectedTrigger,
      config: {
        ...selectedTrigger.config,
        conditions: [...(selectedTrigger.config.conditions || []), newCondition]
      }
    };
    
    onTriggerChange(updatedTrigger);
  };

  const handleConditionUpdate = (index: number, field: keyof TriggerCondition, value: any) => {
    if (!selectedTrigger?.config.conditions) return;
    
    const updatedConditions = [...selectedTrigger.config.conditions];
    updatedConditions[index] = {
      ...updatedConditions[index],
      [field]: value
    };
    
    const updatedTrigger: WorkflowTrigger = {
      ...selectedTrigger,
      config: {
        ...selectedTrigger.config,
        conditions: updatedConditions
      }
    };
    
    onTriggerChange(updatedTrigger);
  };

  const handleConditionRemove = (index: number) => {
    if (!selectedTrigger?.config.conditions) return;
    
    const updatedConditions = selectedTrigger.config.conditions.filter((_, i) => i !== index);
    
    const updatedTrigger: WorkflowTrigger = {
      ...selectedTrigger,
      config: {
        ...selectedTrigger.config,
        conditions: updatedConditions
      }
    };
    
    onTriggerChange(updatedTrigger);
  };

  const getFieldOptions = (triggerType: string) => {
    const fieldOptions: Record<string, { value: string; label: string }[]> = {
      vendor_onboarded: [
        { value: 'vendor.category', label: 'Vendor Category' },
        { value: 'vendor.risk_level', label: 'Risk Level' },
        { value: 'vendor.location', label: 'Location' }
      ],
      contract_expiring: [
        { value: 'contract.value', label: 'Contract Value' },
        { value: 'contract.type', label: 'Contract Type' },
        { value: 'contract.days_until_expiry', label: 'Days Until Expiry' }
      ],
      invoice_approved: [
        { value: 'invoice.amount', label: 'Invoice Amount' },
        { value: 'invoice.vendor_id', label: 'Vendor ID' },
        { value: 'invoice.category', label: 'Category' }
      ],
      performance_score_changed: [
        { value: 'performance.score', label: 'Performance Score' },
        { value: 'performance.change_amount', label: 'Change Amount' },
        { value: 'performance.category', label: 'Performance Category' }
      ]
    };
    
    return fieldOptions[triggerType] || [];
  };

  const selectedTriggerType = triggerTypes.find(t => t.type === selectedTrigger?.type);

  return (
    <div className="space-y-4">
      {/* Trigger Type Selection */}
      <div>
        <Label className="text-sm font-medium text-gray-700 mb-2 block">
          Select Trigger Type *
        </Label>
        <Select 
          value={selectedTrigger?.type || ''} 
          onValueChange={handleTriggerTypeChange}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Choose when this workflow should run" />
          </SelectTrigger>
          <SelectContent>
            {triggerTypes.map((trigger) => {
              const Icon = trigger.icon;
              return (
                <SelectItem key={trigger.type} value={trigger.type}>
                  <div className="flex items-center space-x-2">
                    <div className={`p-1 rounded ${trigger.bgColor}`}>
                      <Icon className={`h-4 w-4 ${trigger.color}`} />
                    </div>
                    <div>
                      <div className="font-medium">{trigger.label}</div>
                      <div className="text-xs text-gray-500">{trigger.description}</div>
                    </div>
                  </div>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>

      {/* Selected Trigger Preview */}
      {selectedTriggerType && (
        <Card className="border-2 border-dashed border-gray-200">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${selectedTriggerType.bgColor}`}>
                <selectedTriggerType.icon className={`h-5 w-5 ${selectedTriggerType.color}`} />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">{selectedTriggerType.label}</h4>
                <p className="text-sm text-gray-600">{selectedTriggerType.description}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Advanced Configuration */}
      {selectedTrigger && selectedTrigger.type !== 'manual' && (
        <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <div className="flex items-center space-x-2">
                <Settings className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Advanced Configuration</span>
              </div>
              {showAdvanced ? (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronRight className="h-4 w-4 text-gray-500" />
              )}
            </Button>
          </CollapsibleTrigger>
          
          <CollapsibleContent className="space-y-4 mt-4">
            {/* Conditions */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm font-medium text-gray-700">
                  Trigger Conditions
                </Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleConditionAdd}
                  className="text-xs"
                >
                  Add Condition
                </Button>
              </div>
              
              {selectedTrigger.config.conditions?.map((condition, index) => (
                <Card key={index} className="p-3 mb-2">
                  <div className="grid grid-cols-12 gap-2 items-center">
                    <div className="col-span-4">
                      <Select
                        value={condition.field}
                        onValueChange={(value) => handleConditionUpdate(index, 'field', value)}
                      >
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue placeholder="Field" />
                        </SelectTrigger>
                        <SelectContent>
                          {getFieldOptions(selectedTrigger.type).map((field) => (
                            <SelectItem key={field.value} value={field.value}>
                              {field.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="col-span-3">
                      <Select
                        value={condition.operator}
                        onValueChange={(value) => handleConditionUpdate(index, 'operator', value)}
                      >
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="equals">Equals</SelectItem>
                          <SelectItem value="greater_than">Greater Than</SelectItem>
                          <SelectItem value="less_than">Less Than</SelectItem>
                          <SelectItem value="contains">Contains</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="col-span-4">
                      <Input
                        value={condition.value}
                        onChange={(e) => handleConditionUpdate(index, 'value', e.target.value)}
                        placeholder="Value"
                        className="h-8 text-xs"
                      />
                    </div>
                    
                    <div className="col-span-1">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleConditionRemove(index)}
                        className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                      >
                        ×
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
              
              {(!selectedTrigger.config.conditions || selectedTrigger.config.conditions.length === 0) && (
                <div className="text-center py-4 text-sm text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
                  No conditions set - workflow will trigger for all events of this type
                </div>
              )}
            </div>

            {/* Schedule Configuration for applicable triggers */}
            {(selectedTrigger.type === 'contract_expiring' || selectedTrigger.type === 'performance_score_changed') && (
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  Check Frequency
                </Label>
                <Select
                  value={selectedTrigger.config.schedule || ''}
                  onValueChange={(value) => onTriggerChange({
                    ...selectedTrigger,
                    config: { ...selectedTrigger.config, schedule: value }
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="How often should we check?" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0 9 * * *">Daily at 9 AM</SelectItem>
                    <SelectItem value="0 9 * * 1">Weekly on Monday at 9 AM</SelectItem>
                    <SelectItem value="0 9 1 * *">Monthly on 1st at 9 AM</SelectItem>
                    <SelectItem value="0 */4 * * *">Every 4 hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      )}

      {/* Trigger Summary */}
      {selectedTrigger && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-3">
            <div className="flex items-start space-x-2">
              <Clock className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-900 mb-1">Trigger Summary:</p>
                <p className="text-blue-800">
                  This workflow will run when <strong>{selectedTriggerType?.label.toLowerCase()}</strong>
                  {selectedTrigger.config.conditions?.length ? (
                    <span> and meets {selectedTrigger.config.conditions.length} condition(s)</span>
                  ) : (
                    <span> (no additional conditions)</span>
                  )}
                  {selectedTrigger.config.schedule && (
                    <span> - checked {selectedTrigger.config.schedule}</span>
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TriggerSelector;