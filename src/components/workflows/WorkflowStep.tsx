import React, { useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  <PERSON><PERSON><PERSON>cle, 
  Bell, 
  Link, 
  GitBranch, 
  Clock,
  Settings,
  Trash2,
  Copy,
  MoreVertical,
  Circle,
  ArrowRight
} from 'lucide-react';
import { AppDispatch, RootState } from '@/store';
import { 
  WorkflowStep as WorkflowStepType,
  setSelectedStep,
  removeWorkflowStep,
  updateWorkflowStep,
  connectSteps,
  disconnectSteps
} from '@/store/slices/workflowsSlice';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface WorkflowStepProps {
  step: WorkflowStepType;
  isSelected: boolean;
  isConnecting: boolean;
  onPositionChange: (stepId: string, position: { x: number; y: number }) => void;
  onConnectionStart: (stepId: string) => void;
  onConnectionEnd: (stepId: string) => void;
  onDelete: (stepId: string) => void;
}

const WorkflowStep: React.FC<WorkflowStepProps> = ({
  step,
  isSelected,
  isConnecting,
  onPositionChange,
  onConnectionStart,
  onConnectionEnd,
  onDelete
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { currentWorkflow } = useSelector((state: RootState) => state.workflows);
  
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const stepRef = useRef<HTMLDivElement>(null);

  const stepTypeConfig = {
    approval: {
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      borderColor: 'border-green-200',
      label: 'Approval'
    },
    notification: {
      icon: Bell,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      borderColor: 'border-blue-200',
      label: 'Notification'
    },
    integration: {
      icon: Link,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      borderColor: 'border-purple-200',
      label: 'Integration'
    },
    condition: {
      icon: GitBranch,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      borderColor: 'border-orange-200',
      label: 'Condition'
    },
    delay: {
      icon: Clock,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      borderColor: 'border-gray-200',
      label: 'Delay'
    }
  };

  const config = stepTypeConfig[step.type];
  const Icon = config.icon;

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button !== 0) return; // Only handle left click
    
    const rect = stepRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
      setIsDragging(true);
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;
    
    const canvas = document.getElementById('workflow-canvas');
    if (!canvas) return;
    
    const canvasRect = canvas.getBoundingClientRect();
    const newPosition = {
      x: e.clientX - canvasRect.left - dragOffset.x,
      y: e.clientY - canvasRect.top - dragOffset.y
    };
    
    // Constrain to canvas bounds
    newPosition.x = Math.max(0, Math.min(newPosition.x, canvasRect.width - 200));
    newPosition.y = Math.max(0, Math.min(newPosition.y, canvasRect.height - 100));
    
    onPositionChange(step.id, newPosition);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  const handleStepClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    dispatch(setSelectedStep(step.id));
  };

  const handleConnectionClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isConnecting) {
      onConnectionEnd(step.id);
    } else {
      onConnectionStart(step.id);
    }
  };

  const handleDuplicate = () => {
    const duplicatedStep: WorkflowStepType = {
      ...step,
      id: `${step.id}_copy_${Date.now()}`,
      name: `${step.name} (Copy)`,
      position: {
        x: step.position.x + 20,
        y: step.position.y + 20
      },
      connections: []
    };
    
    dispatch(updateWorkflowStep({
      stepId: duplicatedStep.id,
      updates: duplicatedStep
    }));
  };

  const getStepStatus = () => {
    // This would be determined by workflow execution status
    // For now, return a default status
    return 'idle'; // 'idle' | 'running' | 'completed' | 'failed'
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'border-blue-400 bg-blue-50';
      case 'completed':
        return 'border-green-400 bg-green-50';
      case 'failed':
        return 'border-red-400 bg-red-50';
      default:
        return '';
    }
  };

  const status = getStepStatus();
  const hasConnections = step.connections.length > 0;
  const isConnectedTo = currentWorkflow?.steps.some(s => s.connections.includes(step.id));

  return (
    <div
      ref={stepRef}
      className={`absolute select-none ${isDragging ? 'z-50' : 'z-10'}`}
      style={{
        left: step.position.x,
        top: step.position.y,
        transform: isDragging ? 'scale(1.05)' : 'scale(1)',
        transition: isDragging ? 'none' : 'transform 0.2s ease'
      }}
    >
      <Card
        className={`
          w-48 cursor-pointer transition-all duration-200
          ${isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''}
          ${config.borderColor} border-2
          ${getStatusColor(status)}
          hover:shadow-lg
          ${isDragging ? 'shadow-xl' : 'shadow-md'}
        `}
        onClick={handleStepClick}
        onMouseDown={handleMouseDown}
      >
        <CardContent className="p-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <div className={`p-1.5 rounded ${config.bgColor}`}>
                <Icon className={`h-4 w-4 ${config.color}`} />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 text-sm truncate">
                  {step.name || config.label}
                </h4>
                <Badge variant="outline" className="text-xs">
                  {config.label}
                </Badge>
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                <DropdownMenuItem onClick={() => dispatch(setSelectedStep(step.id))}>
                  <Settings className="h-4 w-4 mr-2" />
                  Configure
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleDuplicate}>
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => onDelete(step.id)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Connection Points */}
          <div className="flex items-center justify-between">
            {/* Input Connection Point */}
            <div className="flex items-center">
              {isConnectedTo && (
                <div className="w-3 h-3 rounded-full bg-gray-300 border-2 border-white shadow-sm" />
              )}
            </div>

            {/* Output Connection Point */}
            <Button
              variant="ghost"
              size="sm"
              className={`
                h-6 w-6 p-0 rounded-full
                ${isConnecting ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100'}
                ${hasConnections ? 'bg-green-100 text-green-600' : ''}
              `}
              onClick={handleConnectionClick}
            >
              {hasConnections ? (
                <ArrowRight className="h-3 w-3" />
              ) : (
                <Circle className="h-3 w-3" />
              )}
            </Button>
          </div>

          {/* Status Indicator */}
          {status !== 'idle' && (
            <div className="mt-2 pt-2 border-t border-gray-100">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  status === 'running' ? 'bg-blue-500 animate-pulse' :
                  status === 'completed' ? 'bg-green-500' :
                  status === 'failed' ? 'bg-red-500' : 'bg-gray-300'
                }`} />
                <span className="text-xs text-gray-600 capitalize">{status}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default WorkflowStep;