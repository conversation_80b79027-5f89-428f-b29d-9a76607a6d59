import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { 
  CheckCircle, 
  Users, 
  Clock, 
  AlertTriangle, 
  Plus, 
  X,
  User,
  Shield
} from 'lucide-react';
import { AppDispatch } from '@/store';
import { updateWorkflowStep, ApprovalStepConfig as ApprovalConfig } from '@/store/slices/workflowsSlice';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ApprovalStepConfigProps {
  stepId: string;
  config: ApprovalConfig;
  onConfigChange: (config: ApprovalConfig) => void;
}

const ApprovalStepConfig: React.FC<ApprovalStepConfigProps> = ({
  stepId,
  config,
  onConfigChange
}) => {
  const dispatch = useDispatch<AppDispatch>();

  // Default config if not provided
  const defaultConfig: ApprovalConfig = {
    approvers: [],
    timeout_hours: 24,
    escalation: {
      enabled: false,
      escalate_to: '',
      after_hours: 48
    },
    approval_type: 'any'
  };

  const currentConfig = { ...defaultConfig, ...config };

  const [newApproverType, setNewApproverType] = useState<'role' | 'user'>('role');
  const [newApproverValue, setNewApproverValue] = useState('');

  const roleOptions = [
    { value: 'admin', label: 'Admin Users', icon: Shield },
    { value: 'manager', label: 'Manager Users', icon: Users },
    { value: 'viewer', label: 'Viewer Users', icon: User }
  ];

  const userOptions = [
    { value: '<EMAIL>', label: 'Admin User' },
    { value: '<EMAIL>', label: 'Manager User' },
    { value: '<EMAIL>', label: 'Viewer User' }
  ];

  const handleConfigUpdate = (updates: Partial<ApprovalConfig>) => {
    const newConfig = { ...currentConfig, ...updates };
    onConfigChange(newConfig);
    
    // Update the step in Redux store
    dispatch(updateWorkflowStep({
      stepId,
      updates: { config: newConfig }
    }));
  };

  const handleAddApprover = () => {
    if (!newApproverValue) return;

    const newApprover = {
      type: newApproverType,
      value: newApproverValue
    };

    const updatedApprovers = [...currentConfig.approvers, newApprover];
    handleConfigUpdate({ approvers: updatedApprovers });
    
    setNewApproverValue('');
  };

  const handleRemoveApprover = (index: number) => {
    const updatedApprovers = currentConfig.approvers.filter((_, i) => i !== index);
    handleConfigUpdate({ approvers: updatedApprovers });
  };

  const handleTimeoutChange = (hours: number) => {
    if (hours > 0 && hours <= 168) { // Max 1 week
      handleConfigUpdate({ timeout_hours: hours });
    }
  };

  const handleEscalationToggle = (enabled: boolean) => {
    handleConfigUpdate({
      escalation: {
        ...currentConfig.escalation,
        enabled
      }
    });
  };

  const handleEscalationUpdate = (field: keyof ApprovalConfig['escalation'], value: string | number | boolean) => {
    handleConfigUpdate({
      escalation: {
        ...currentConfig.escalation,
        [field]: value
      }
    });
  };

  const getApproverLabel = (approver: { type: 'role' | 'user'; value: string }) => {
    if (approver.type === 'role') {
      const role = roleOptions.find(r => r.value === approver.value);
      return role ? role.label : approver.value;
    } else {
      const user = userOptions.find(u => u.value === approver.value);
      return user ? user.label : approver.value;
    }
  };

  const getApproverIcon = (approver: { type: 'role' | 'user'; value: string }) => {
    if (approver.type === 'role') {
      const role = roleOptions.find(r => r.value === approver.value);
      return role ? role.icon : Users;
    }
    return User;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2 text-lg">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span>Approval Configuration</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600">
            Configure who needs to approve this step and how the approval process should work.
          </p>
        </CardContent>
      </Card>

      {/* Approvers Section */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>Approvers</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Approvers */}
          {currentConfig.approvers.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Current Approvers:</Label>
              {currentConfig.approvers.map((approver, index) => {
                const Icon = getApproverIcon(approver);
                return (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`p-1.5 rounded ${
                        approver.type === 'role' ? 'bg-blue-100' : 'bg-green-100'
                      }`}>
                        <Icon className={`h-4 w-4 ${
                          approver.type === 'role' ? 'text-blue-600' : 'text-green-600'
                        }`} />
                      </div>
                      <div>
                        <p className="font-medium text-sm">{getApproverLabel(approver)}</p>
                        <Badge variant="outline" className="text-xs">
                          {approver.type === 'role' ? 'Role' : 'User'}
                        </Badge>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveApprover(index)}
                      className="text-red-600 hover:text-red-700 h-8 w-8 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                );
              })}
            </div>
          )}

          {/* Add New Approver */}
          <div className="space-y-3 p-4 border-2 border-dashed border-gray-200 rounded-lg">
            <Label className="text-sm font-medium">Add Approver:</Label>
            
            <div className="grid grid-cols-3 gap-2">
              <Select value={newApproverType} onValueChange={(value: 'role' | 'user') => setNewApproverType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="role">Role</SelectItem>
                  <SelectItem value="user">User</SelectItem>
                </SelectContent>
              </Select>

              <Select value={newApproverValue} onValueChange={setNewApproverValue}>
                <SelectTrigger className="col-span-1">
                  <SelectValue placeholder={`Select ${newApproverType}`} />
                </SelectTrigger>
                <SelectContent>
                  {(newApproverType === 'role' ? roleOptions : userOptions).map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                onClick={handleAddApprover}
                disabled={!newApproverValue}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Approval Type */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Approval Type:</Label>
            <Select 
              value={currentConfig.approval_type} 
              onValueChange={(value: 'any' | 'all' | 'majority') => handleConfigUpdate({ approval_type: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="any">Any approver can approve</SelectItem>
                <SelectItem value="all">All approvers must approve</SelectItem>
                <SelectItem value="majority">Majority must approve</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500">
              {currentConfig.approval_type === 'any' && 'Workflow continues when any single approver approves'}
              {currentConfig.approval_type === 'all' && 'All selected approvers must approve before continuing'}
              {currentConfig.approval_type === 'majority' && 'More than half of the approvers must approve'}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Timeout Configuration */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center space-x-2">
            <Clock className="h-4 w-4" />
            <span>Timeout Settings</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Timeout (hours):</Label>
            <div className="flex items-center space-x-2">
              <Input
                type="number"
                min="1"
                max="168"
                value={currentConfig.timeout_hours}
                onChange={(e) => handleTimeoutChange(parseInt(e.target.value) || 24)}
                className="w-24"
              />
              <span className="text-sm text-gray-600">hours</span>
            </div>
            <p className="text-xs text-gray-500">
              How long to wait for approval before timing out (1-168 hours)
            </p>
          </div>

          <Alert>
            <Clock className="h-4 w-4" />
            <AlertDescription className="text-sm">
              If no approval is received within {currentConfig.timeout_hours} hours, 
              the workflow will {currentConfig.escalation.enabled ? 'escalate' : 'fail'}.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Escalation Configuration */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4" />
            <span>Escalation Settings</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">Enable Escalation</Label>
              <p className="text-xs text-gray-500">
                Escalate to another approver if timeout is reached
              </p>
            </div>
            <Switch
              checked={currentConfig.escalation.enabled}
              onCheckedChange={handleEscalationToggle}
            />
          </div>

          {currentConfig.escalation.enabled && (
            <>
              <Separator />
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Escalate To:</Label>
                  <Select 
                    value={currentConfig.escalation.escalate_to} 
                    onValueChange={(value) => handleEscalationUpdate('escalate_to', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select escalation target" />
                    </SelectTrigger>
                    <SelectContent>
                      {roleOptions.map((role) => (
                        <SelectItem key={role.value} value={role.value}>
                          {role.label}
                        </SelectItem>
                      ))}
                      {userOptions.map((user) => (
                        <SelectItem key={user.value} value={user.value}>
                          {user.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Escalate After (hours):</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      type="number"
                      min="1"
                      max="168"
                      value={currentConfig.escalation.after_hours}
                      onChange={(e) => handleEscalationUpdate('after_hours', parseInt(e.target.value) || 48)}
                      className="w-24"
                    />
                    <span className="text-sm text-gray-600">hours</span>
                  </div>
                  <p className="text-xs text-gray-500">
                    How long to wait before escalating (must be less than timeout)
                  </p>
                </div>

                {currentConfig.escalation.after_hours >= currentConfig.timeout_hours && (
                  <Alert className="border-yellow-200 bg-yellow-50">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <AlertDescription className="text-sm text-yellow-800">
                      Escalation time should be less than timeout time for escalation to work properly.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Configuration Summary */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">Configuration Summary:</h4>
          <div className="text-sm text-blue-800 space-y-1">
            <p>• <strong>{currentConfig.approvers.length}</strong> approver(s) configured</p>
            <p>• Approval type: <strong>{currentConfig.approval_type}</strong></p>
            <p>• Timeout: <strong>{currentConfig.timeout_hours} hours</strong></p>
            <p>• Escalation: <strong>{currentConfig.escalation.enabled ? 'Enabled' : 'Disabled'}</strong></p>
            {currentConfig.escalation.enabled && (
              <p>• Escalates to <strong>{currentConfig.escalation.escalate_to}</strong> after <strong>{currentConfig.escalation.after_hours} hours</strong></p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Validation Warnings */}
      {currentConfig.approvers.length === 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-sm text-red-800">
            At least one approver must be configured for this step to work properly.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default ApprovalStepConfig;