import React, { useState, useRef, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Plus, ZoomIn, ZoomOut, Maximize, Grid } from 'lucide-react';
import { AppDispatch, RootState } from '@/store';
import { 
  addWorkflowStep,
  updateWorkflowStep,
  removeWorkflowStep,
  connectSteps,
  disconnectSteps,
  setSelectedStep,
  updateCanvasPosition,
  setZoom,
  WorkflowStep as WorkflowStepType
} from '@/store/slices/workflowsSlice';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import WorkflowStep from './WorkflowStep';

interface WorkflowCanvasProps {
  isTestMode?: boolean;
  onStepAdd?: () => void;
  onStepUpdate?: () => void;
  onStepDelete?: () => void;
}

const WorkflowCanvas: React.FC<WorkflowCanvasProps> = ({
  isTestMode = false,
  onStepAdd,
  onStepUpdate,
  onStepDelete
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { 
    currentWorkflow, 
    selectedStep, 
    canvasPosition, 
    zoom 
  } = useSelector((state: RootState) => state.workflows);

  const canvasRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({ x: 0, y: 0 });
  const [connectingFrom, setConnectingFrom] = useState<string | null>(null);
  const [connectionPreview, setConnectionPreview] = useState<{ x: number; y: number } | null>(null);

  const handleCanvasClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      dispatch(setSelectedStep(null));
      setConnectingFrom(null);
      setConnectionPreview(null);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    
    try {
      const data = JSON.parse(e.dataTransfer.getData('application/json'));
      
      if (data.type === 'step') {
        const rect = canvasRef.current?.getBoundingClientRect();
        if (!rect) return;
        
        const position = {
          x: (e.clientX - rect.left - canvasPosition.x) / zoom,
          y: (e.clientY - rect.top - canvasPosition.y) / zoom
        };
        
        const newStep: WorkflowStepType = {
          id: `step_${Date.now()}`,
          type: data.stepType,
          name: `${data.stepType.charAt(0).toUpperCase() + data.stepType.slice(1)} Step`,
          config: {},
          position,
          connections: []
        };
        
        dispatch(addWorkflowStep(newStep));
        dispatch(setSelectedStep(newStep.id));
        onStepAdd?.();
      }
    } catch (error) {
      console.error('Error handling drop:', error);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  const handleStepPositionChange = (stepId: string, position: { x: number; y: number }) => {
    dispatch(updateWorkflowStep({
      stepId,
      updates: { position }
    }));
    onStepUpdate?.();
  };

  const handleStepDelete = (stepId: string) => {
    dispatch(removeWorkflowStep(stepId));
    if (selectedStep === stepId) {
      dispatch(setSelectedStep(null));
    }
    onStepDelete?.();
  };

  const handleConnectionStart = (stepId: string) => {
    setConnectingFrom(stepId);
  };

  const handleConnectionEnd = (stepId: string) => {
    if (connectingFrom && connectingFrom !== stepId) {
      dispatch(connectSteps({
        fromStepId: connectingFrom,
        toStepId: stepId
      }));
      onStepUpdate?.();
    }
    setConnectingFrom(null);
    setConnectionPreview(null);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (connectingFrom && canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect();
      setConnectionPreview({
        x: (e.clientX - rect.left - canvasPosition.x) / zoom,
        y: (e.clientY - rect.top - canvasPosition.y) / zoom
      });
    }
  };

  const handlePanStart = (e: React.MouseEvent) => {
    if (e.button === 1 || (e.button === 0 && e.ctrlKey)) { // Middle mouse or Ctrl+Left
      setIsPanning(true);
      setPanStart({ x: e.clientX - canvasPosition.x, y: e.clientY - canvasPosition.y });
      e.preventDefault();
    }
  };

  const handlePanMove = (e: React.MouseEvent) => {
    if (isPanning) {
      const newPosition = {
        x: e.clientX - panStart.x,
        y: e.clientY - panStart.y
      };
      dispatch(updateCanvasPosition(newPosition));
    }
  };

  const handlePanEnd = () => {
    setIsPanning(false);
  };

  const handleZoom = (delta: number) => {
    const newZoom = Math.max(0.25, Math.min(2, zoom + delta));
    dispatch(setZoom(newZoom));
  };

  const handleFitToScreen = () => {
    if (!currentWorkflow?.steps.length || !canvasRef.current) return;
    
    const steps = currentWorkflow.steps;
    const bounds = steps.reduce((acc, step) => ({
      minX: Math.min(acc.minX, step.position.x),
      minY: Math.min(acc.minY, step.position.y),
      maxX: Math.max(acc.maxX, step.position.x + 200), // Step width
      maxY: Math.max(acc.maxY, step.position.y + 100)  // Step height
    }), {
      minX: Infinity,
      minY: Infinity,
      maxX: -Infinity,
      maxY: -Infinity
    });
    
    const canvasRect = canvasRef.current.getBoundingClientRect();
    const contentWidth = bounds.maxX - bounds.minX;
    const contentHeight = bounds.maxY - bounds.minY;
    
    const scaleX = (canvasRect.width - 100) / contentWidth;
    const scaleY = (canvasRect.height - 100) / contentHeight;
    const newZoom = Math.min(scaleX, scaleY, 1);
    
    const centerX = (canvasRect.width - contentWidth * newZoom) / 2 - bounds.minX * newZoom;
    const centerY = (canvasRect.height - contentHeight * newZoom) / 2 - bounds.minY * newZoom;
    
    dispatch(setZoom(newZoom));
    dispatch(updateCanvasPosition({ x: centerX, y: centerY }));
  };

  const getStepCenter = (step: WorkflowStepType) => ({
    x: step.position.x + 96, // Half of step width (192px)
    y: step.position.y + 50  // Half of step height (100px)
  });

  const renderConnections = () => {
    if (!currentWorkflow?.steps) return null;
    
    return currentWorkflow.steps.map(step => 
      step.connections.map(connectionId => {
        const targetStep = currentWorkflow.steps.find(s => s.id === connectionId);
        if (!targetStep) return null;
        
        const from = getStepCenter(step);
        const to = getStepCenter(targetStep);
        
        // Calculate control points for curved line
        const dx = to.x - from.x;
        const dy = to.y - from.y;
        const controlOffset = Math.min(Math.abs(dx) * 0.5, 100);
        
        const path = `M ${from.x} ${from.y} C ${from.x + controlOffset} ${from.y}, ${to.x - controlOffset} ${to.y}, ${to.x} ${to.y}`;
        
        return (
          <g key={`${step.id}-${connectionId}`}>
            <path
              d={path}
              stroke="#3b82f6"
              strokeWidth="2"
              fill="none"
              markerEnd="url(#arrowhead)"
              className="drop-shadow-sm"
            />
          </g>
        );
      })
    ).flat();
  };

  const renderConnectionPreview = () => {
    if (!connectingFrom || !connectionPreview || !currentWorkflow?.steps) return null;
    
    const fromStep = currentWorkflow.steps.find(s => s.id === connectingFrom);
    if (!fromStep) return null;
    
    const from = getStepCenter(fromStep);
    const to = connectionPreview;
    
    const dx = to.x - from.x;
    const dy = to.y - from.y;
    const controlOffset = Math.min(Math.abs(dx) * 0.5, 100);
    
    const path = `M ${from.x} ${from.y} C ${from.x + controlOffset} ${from.y}, ${to.x - controlOffset} ${to.y}, ${to.x} ${to.y}`;
    
    return (
      <path
        d={path}
        stroke="#3b82f6"
        strokeWidth="2"
        strokeDasharray="5,5"
        fill="none"
        opacity="0.6"
      />
    );
  };

  return (
    <div className="flex-1 relative bg-gray-50 overflow-hidden">
      {/* Canvas Controls */}
      <div className="absolute top-4 right-4 z-20 flex items-center space-x-2">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-1 flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleZoom(-0.25)}
            disabled={zoom <= 0.25}
            className="h-8 w-8 p-0"
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          
          <span className="text-sm font-medium text-gray-600 px-2 min-w-[3rem] text-center">
            {Math.round(zoom * 100)}%
          </span>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleZoom(0.25)}
            disabled={zoom >= 2}
            className="h-8 w-8 p-0"
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          
          <div className="w-px h-6 bg-gray-200 mx-1" />
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleFitToScreen}
            className="h-8 w-8 p-0"
            title="Fit to screen"
          >
            <Maximize className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Canvas */}
      <div
        ref={canvasRef}
        id="workflow-canvas"
        className={`
          w-full h-full relative overflow-hidden
          ${isPanning ? 'cursor-grabbing' : 'cursor-grab'}
          ${connectingFrom ? 'cursor-crosshair' : ''}
        `}
        onClick={handleCanvasClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onMouseMove={handleMouseMove}
        onMouseDown={handlePanStart}
        onMouseMove={handlePanMove}
        onMouseUp={handlePanEnd}
        onMouseLeave={handlePanEnd}
      >
        {/* Grid Background */}
        <div 
          className="absolute inset-0 opacity-30"
          style={{
            backgroundImage: `
              radial-gradient(circle, #d1d5db 1px, transparent 1px)
            `,
            backgroundSize: `${20 * zoom}px ${20 * zoom}px`,
            backgroundPosition: `${canvasPosition.x}px ${canvasPosition.y}px`
          }}
        />

        {/* Canvas Content */}
        <div
          className="absolute inset-0"
          style={{
            transform: `translate(${canvasPosition.x}px, ${canvasPosition.y}px) scale(${zoom})`,
            transformOrigin: '0 0'
          }}
        >
          {/* SVG for connections */}
          <svg
            ref={svgRef}
            className="absolute inset-0 w-full h-full pointer-events-none"
            style={{ overflow: 'visible' }}
          >
            <defs>
              <marker
                id="arrowhead"
                markerWidth="10"
                markerHeight="7"
                refX="9"
                refY="3.5"
                orient="auto"
              >
                <polygon
                  points="0 0, 10 3.5, 0 7"
                  fill="#3b82f6"
                />
              </marker>
            </defs>
            {renderConnections()}
            {renderConnectionPreview()}
          </svg>

          {/* Workflow Steps */}
          {currentWorkflow?.steps.map(step => (
            <WorkflowStep
              key={step.id}
              step={step}
              isSelected={selectedStep === step.id}
              isConnecting={connectingFrom === step.id}
              onPositionChange={handleStepPositionChange}
              onConnectionStart={handleConnectionStart}
              onConnectionEnd={handleConnectionEnd}
              onDelete={handleStepDelete}
            />
          ))}

          {/* Empty State */}
          {(!currentWorkflow?.steps || currentWorkflow.steps.length === 0) && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Card className="w-96 border-2 border-dashed border-gray-300">
                <CardContent className="p-8 text-center">
                  <Grid className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Start Building Your Workflow
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Drag and drop steps from the left panel to create your automated workflow
                  </p>
                  <div className="text-sm text-gray-500">
                    <p>• Configure a trigger to start the workflow</p>
                    <p>• Add steps for approvals, notifications, and integrations</p>
                    <p>• Connect steps to define the flow</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>

      {/* Connection Mode Indicator */}
      {connectingFrom && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20">
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-3">
              <p className="text-sm text-blue-800">
                Click on another step to create a connection, or click anywhere else to cancel
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Test Mode Indicator */}
      {isTestMode && (
        <div className="absolute top-4 left-4 z-20">
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-3">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium text-blue-800">Test Mode Active</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default WorkflowCanvas;