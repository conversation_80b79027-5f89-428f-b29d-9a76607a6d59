import React from 'react';
import { useSelector } from 'react-redux';
import { Settings, AlertCircle } from 'lucide-react';
import { RootState } from '@/store';
import { ApprovalStepConfig as ApprovalConfig } from '@/store/slices/workflowsSlice';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import ApprovalStepConfig from './ApprovalStepConfig';

interface ConfigPanelProps {
  onConfigChange?: () => void;
}

const ConfigPanel: React.FC<ConfigPanelProps> = ({ onConfigChange }) => {
  const { currentWorkflow, selectedStep } = useSelector((state: RootState) => state.workflows);
  
  const selectedStepData = currentWorkflow?.steps.find(step => step.id === selectedStep);

  if (!selectedStepData) {
    return (
      <div className="p-4 h-full flex items-center justify-center">
        <Card className="w-full border-dashed border-gray-300">
          <CardContent className="p-6 text-center">
            <Settings className="h-8 w-8 text-gray-400 mx-auto mb-3" />
            <h3 className="text-sm font-medium text-gray-900 mb-2">
              No Step Selected
            </h3>
            <p className="text-xs text-gray-600">
              Click on a workflow step to configure its settings
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleConfigChange = (config: ApprovalConfig) => {
    onConfigChange?.();
  };

  const renderStepConfiguration = () => {
    switch (selectedStepData.type) {
      case 'approval':
        return (
          <ApprovalStepConfig
            stepId={selectedStepData.id}
            config={selectedStepData.config as ApprovalConfig}
            onConfigChange={handleConfigChange}
          />
        );
      
      case 'notification':
        return (
          <div className="p-4">
            <Card className="border-dashed border-gray-300">
              <CardContent className="p-6 text-center">
                <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-3" />
                <h3 className="text-sm font-medium text-gray-900 mb-2">
                  Notification Configuration
                </h3>
                <p className="text-xs text-gray-600">
                  Notification step configuration coming soon
                </p>
              </CardContent>
            </Card>
          </div>
        );
      
      case 'integration':
        return (
          <div className="p-4">
            <Card className="border-dashed border-gray-300">
              <CardContent className="p-6 text-center">
                <AlertCircle className="h-8 w-8 text-purple-500 mx-auto mb-3" />
                <h3 className="text-sm font-medium text-gray-900 mb-2">
                  Integration Configuration
                </h3>
                <p className="text-xs text-gray-600">
                  Integration step configuration coming soon
                </p>
              </CardContent>
            </Card>
          </div>
        );
      
      case 'condition':
        return (
          <div className="p-4">
            <Card className="border-dashed border-gray-300">
              <CardContent className="p-6 text-center">
                <AlertCircle className="h-8 w-8 text-orange-500 mx-auto mb-3" />
                <h3 className="text-sm font-medium text-gray-900 mb-2">
                  Condition Configuration
                </h3>
                <p className="text-xs text-gray-600">
                  Condition step configuration coming soon
                </p>
              </CardContent>
            </Card>
          </div>
        );
      
      case 'delay':
        return (
          <div className="p-4">
            <Card className="border-dashed border-gray-300">
              <CardContent className="p-6 text-center">
                <AlertCircle className="h-8 w-8 text-gray-500 mx-auto mb-3" />
                <h3 className="text-sm font-medium text-gray-900 mb-2">
                  Delay Configuration
                </h3>
                <p className="text-xs text-gray-600">
                  Delay step configuration coming soon
                </p>
              </CardContent>
            </Card>
          </div>
        );
      
      default:
        return (
          <div className="p-4">
            <Card className="border-dashed border-gray-300">
              <CardContent className="p-6 text-center">
                <Settings className="h-8 w-8 text-gray-400 mx-auto mb-3" />
                <h3 className="text-sm font-medium text-gray-900 mb-2">
                  Unknown Step Type
                </h3>
                <p className="text-xs text-gray-600">
                  Configuration not available for this step type
                </p>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  return (
    <div className="h-full overflow-y-auto">
      {renderStepConfiguration()}
    </div>
  );
};

export default ConfigPanel;