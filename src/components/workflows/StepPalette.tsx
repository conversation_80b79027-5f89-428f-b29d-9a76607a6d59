import React from 'react';
import { 
  CheckCircle, 
  Bell, 
  Link, 
  GitBranch, 
  Clock,
  GripVertical
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const StepPalette: React.FC = () => {
  const stepTypes = [
    {
      type: 'approval',
      label: 'Approval',
      description: 'Require approval from users or roles',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      borderColor: 'border-green-200'
    },
    {
      type: 'notification',
      label: 'Notification',
      description: 'Send email, SMS, or in-app notifications',
      icon: Bell,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      borderColor: 'border-blue-200'
    },
    {
      type: 'integration',
      label: 'Integration',
      description: 'Connect with external systems',
      icon: Link,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      borderColor: 'border-purple-200'
    },
    {
      type: 'condition',
      label: 'Condition',
      description: 'Add if/then logic and branching',
      icon: GitBranch,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      borderColor: 'border-orange-200'
    },
    {
      type: 'delay',
      label: 'Delay',
      description: 'Wait for a specified time period',
      icon: Clock,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      borderColor: 'border-gray-200'
    }
  ];

  const handleDragStart = (e: React.DragEvent, stepType: string) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'step',
      stepType: stepType
    }));
    e.dataTransfer.effectAllowed = 'copy';
  };

  return (
    <div className="space-y-3">
      <p className="text-sm text-gray-600 mb-4">
        Drag and drop steps onto the canvas to build your workflow
      </p>
      
      {stepTypes.map((step) => {
        const Icon = step.icon;
        
        return (
          <Card
            key={step.type}
            className={`cursor-grab active:cursor-grabbing hover:shadow-md transition-shadow duration-200 ${step.borderColor} border-2 border-dashed hover:border-solid`}
            draggable
            onDragStart={(e) => handleDragStart(e, step.type)}
          >
            <CardContent className="p-3">
              <div className="flex items-start space-x-3">
                <div className="flex items-center space-x-2">
                  <GripVertical className="h-4 w-4 text-gray-400" />
                  <div className={`p-2 rounded-lg ${step.bgColor}`}>
                    <Icon className={`h-4 w-4 ${step.color}`} />
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <h4 className="font-medium text-gray-900 text-sm">{step.label}</h4>
                    <Badge variant="outline" className="text-xs">
                      {step.type}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
      
      {/* Usage Instructions */}
      <Card className="bg-blue-50 border-blue-200 mt-6">
        <CardContent className="p-3">
          <h4 className="text-sm font-medium text-blue-900 mb-2">How to use:</h4>
          <ul className="text-xs text-blue-800 space-y-1">
            <li>• Drag steps from this palette to the canvas</li>
            <li>• Connect steps by dragging from output to input</li>
            <li>• Click on a step to configure its settings</li>
            <li>• Use conditions to create branching logic</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default StepPalette;