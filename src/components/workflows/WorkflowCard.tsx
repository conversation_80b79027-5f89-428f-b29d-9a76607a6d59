import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { 
  Play, 
  Pause, 
  Edit, 
  Trash2, 
  Eye, 
  Copy, 
  MoreVertical,
  Calendar,
  TrendingUp,
  Activity,
  AlertCircle
} from 'lucide-react';
import { AppDispatch } from '@/store';
import { toggleWorkflowStatus, deleteWorkflow } from '@/store/slices/workflowsSlice';
import { Workflow } from '@/store/slices/workflowsSlice';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';

interface WorkflowCardProps {
  workflow: Workflow;
  canEdit: boolean;
}

const WorkflowCard: React.FC<WorkflowCardProps> = ({ workflow, canEdit }) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isToggling, setIsToggling] = useState(false);

  const handleToggleStatus = async () => {
    if (!canEdit) return;
    
    setIsToggling(true);
    try {
      const newStatus = workflow.status === 'active' ? 'inactive' : 'active';
      await dispatch(toggleWorkflowStatus({ 
        id: workflow.id, 
        status: newStatus 
      })).unwrap();
      
      toast.success(`Workflow ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      toast.error('Failed to update workflow status');
    } finally {
      setIsToggling(false);
    }
  };

  const handleDelete = async () => {
    try {
      await dispatch(deleteWorkflow(workflow.id)).unwrap();
      toast.success('Workflow deleted successfully');
      setShowDeleteDialog(false);
    } catch (error) {
      toast.error('Failed to delete workflow');
    }
  };

  const handleDuplicate = () => {
    // Navigate to create page with workflow data for duplication
    navigate('/workflows/create', { 
      state: { duplicateFrom: workflow } 
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTriggerLabel = (triggerType: string) => {
    const labels: Record<string, string> = {
      vendor_onboarded: 'Vendor Onboarded',
      contract_expiring: 'Contract Expiring',
      invoice_approved: 'Invoice Approved',
      performance_score_changed: 'Performance Score Changed',
      manual: 'Manual Trigger'
    };
    return labels[triggerType] || triggerType;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <>
      <Card className="bg-white shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 group">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg font-semibold text-gray-900 truncate">
                {workflow.name}
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                {workflow.description}
              </p>
            </div>
            
            <div className="flex items-center space-x-2 ml-3">
              <Badge className={`text-xs ${getStatusColor(workflow.status)}`}>
                {workflow.status.charAt(0).toUpperCase() + workflow.status.slice(1)}
              </Badge>
              
              {canEdit && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem onClick={() => navigate(`/workflows/${workflow.id}`)}>
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate(`/workflows/${workflow.id}/edit`)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Workflow
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleDuplicate}>
                      <Copy className="h-4 w-4 mr-2" />
                      Duplicate
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => setShowDeleteDialog(true)}
                      className="text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-4">
            {/* Trigger Information */}
            <div className="flex items-center text-sm text-gray-600">
              <Activity className="h-4 w-4 mr-2 text-blue-500" />
              <span>Trigger: {getTriggerLabel(workflow.trigger.type)}</span>
            </div>

            {/* Metrics */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center">
                <TrendingUp className="h-4 w-4 mr-2 text-gray-400" />
                <div>
                  <p className="text-gray-600">Success Rate</p>
                  <p className={`font-semibold ${getSuccessRateColor(workflow.success_rate)}`}>
                    {workflow.success_rate.toFixed(1)}%
                  </p>
                </div>
              </div>
              
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                <div>
                  <p className="text-gray-600">Last Run</p>
                  <p className="font-semibold text-gray-900">
                    {formatDate(workflow.last_run)}
                  </p>
                </div>
              </div>
            </div>

            {/* Execution Count */}
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">
                Executed {workflow.execution_count} times
              </span>
              <span className="text-gray-500">
                Created {formatDate(workflow.created_at)}
              </span>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between pt-2 border-t border-gray-100">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate(`/workflows/${workflow.id}`)}
                className="text-blue-600 border-blue-200 hover:bg-blue-50"
              >
                <Eye className="h-4 w-4 mr-1" />
                View
              </Button>

              {canEdit && (
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">
                      {workflow.status === 'active' ? 'Active' : 'Inactive'}
                    </span>
                    <Switch
                      checked={workflow.status === 'active'}
                      onCheckedChange={handleToggleStatus}
                      disabled={isToggling || workflow.status === 'draft'}
                      className="data-[state=checked]:bg-green-600"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Warning for draft status */}
            {workflow.status === 'draft' && (
              <div className="flex items-center space-x-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">
                  This workflow is in draft mode and won't execute automatically
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Workflow</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{workflow.name}"? This action cannot be undone.
              All execution history will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete Workflow
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default WorkflowCard;