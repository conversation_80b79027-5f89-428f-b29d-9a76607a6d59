import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { motion } from 'framer-motion';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';
import { cn } from '@/lib/utils';
import { BadgeCheck, Clock, AlertCircle, DollarSign, Ban, Send, CheckCircle, XCircle } from 'lucide-react';

const badgeVariantsConfig = {
  variants: {
    status: {
      draft: 'bg-gray-100 text-gray-800 border-gray-200',
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      approved: 'bg-green-100 text-green-800 border-green-200',
      sent: 'bg-blue-100 text-blue-800 border-blue-200',
      paid: 'bg-purple-100 text-purple-800 border-purple-200',
      overdue: 'bg-red-100 text-red-800 border-red-200',
      disputed: 'bg-orange-100 text-orange-800 border-orange-200',
    },
  },
};
const statusVariants = cva(
  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  badgeVariantsConfig
);
type Status = keyof typeof badgeVariantsConfig.variants.status;
export interface InvoiceStatusBadgeProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof statusVariants> {
  status: Status;
}

const statusIcons = {
  draft: Clock,
  pending: AlertCircle,
  approved: BadgeCheck,
  sent: Send,
  paid: DollarSign,
  overdue: XCircle,
  disputed: Ban,
};

const statusDescriptions = {
  draft: 'Invoice is in draft state and can be edited',
  pending: 'Waiting for approval',
  approved: 'Invoice has been approved',
  sent: 'Invoice has been sent to vendor',
  paid: 'Invoice has been paid',
  overdue: 'Payment is overdue',
  disputed: 'Invoice is under dispute',
};

function InvoiceStatusBadge({ className, status, ...props }: InvoiceStatusBadgeProps) {
  const Icon = statusIcons[status] || CheckCircle;
  // @ts-ignore
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <motion.div
            className={cn(statusVariants({ status }), className)}
            whileHover={{ scale: 1.05 }}
            transition={{ type: 'spring', stiffness: 300 }}
            {...props}
          >
            <Icon className="mr-1 h-3 w-3" />
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </motion.div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{statusDescriptions[status]}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export { InvoiceStatusBadge };