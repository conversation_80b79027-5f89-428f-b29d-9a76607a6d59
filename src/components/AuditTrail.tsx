import * as React from 'react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { 
  History, 
  User, 
  Edit, 
  Plus, 
  Trash2, 
  Check, 
  X, 
  Send,
  DollarSign,
  FileText,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { cn } from '@/lib/utils';
import { AuditRecord } from '@/store/slices/invoicesSlice';

export interface AuditTrailProps {
  auditRecords: AuditRecord[];
  className?: string;
}

const getActionIcon = (action: string) => {
  switch (action.toLowerCase()) {
    case 'created':
    case 'create':
      return Plus;
    case 'updated':
    case 'update':
    case 'modified':
      return Edit;
    case 'deleted':
    case 'delete':
      return Trash2;
    case 'approved':
    case 'approve':
      return Check;
    case 'rejected':
    case 'reject':
      return X;
    case 'sent':
    case 'send':
      return Send;
    case 'paid':
    case 'payment':
      return DollarSign;
    case 'draft':
      return FileText;
    default:
      return History;
  }
};

const getActionColor = (action: string) => {
  switch (action.toLowerCase()) {
    case 'created':
    case 'create':
      return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-300';
    case 'updated':
    case 'update':
    case 'modified':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-300';
    case 'deleted':
    case 'delete':
      return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-300';
    case 'approved':
    case 'approve':
      return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300';
    case 'rejected':
    case 'reject':
      return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-300';
    case 'sent':
    case 'send':
      return 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900 dark:text-purple-300';
    case 'paid':
    case 'payment':
      return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300';
    case 'draft':
      return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900 dark:text-gray-300';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900 dark:text-gray-300';
  }
};

const formatChanges = (changes: Record<string, any>) => {
  if (!changes || Object.keys(changes).length === 0) {
    return null;
  }

  return (
    <div className="mt-2 space-y-1">
      {Object.entries(changes).map(([field, change]) => (
        <div key={field} className="text-xs text-muted-foreground">
          <span className="font-medium capitalize">{field.replace(/([A-Z])/g, ' $1').toLowerCase()}:</span>
          {typeof change === 'object' && change.from !== undefined && change.to !== undefined ? (
            <span className="ml-1">
              <span className="line-through text-red-500">{String(change.from)}</span>
              {' → '}
              <span className="text-green-600">{String(change.to)}</span>
            </span>
          ) : (
            <span className="ml-1">{String(change)}</span>
          )}
        </div>
      ))}
    </div>
  );
};

export function AuditTrail({ auditRecords, className }: AuditTrailProps) {
  if (!auditRecords || auditRecords.length === 0) {
    return (
      <Card className={cn('card-neumorphic', className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="w-5 h-5" />
            Audit Trail
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <History className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">No audit records</h3>
            <p className="text-muted-foreground">
              Activity history will appear here as changes are made to this invoice.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Sort records by timestamp (newest first)
  const sortedRecords = [...auditRecords].sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  return (
    <Card className={cn('card-neumorphic', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="w-5 h-5" />
          Audit Trail
          <Badge variant="secondary" className="ml-2">
            {auditRecords.length} {auditRecords.length === 1 ? 'record' : 'records'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedRecords.map((record, index) => {
            const ActionIcon = getActionIcon(record.action);
            
            return (
              <motion.div
                key={record.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-start gap-4 p-4 rounded-xl border border-border hover:bg-accent/50 transition-colors"
              >
                <div className={`p-2 rounded-lg ${
                  record.action.toLowerCase().includes('create') ? 'bg-blue-100 dark:bg-blue-900' :
                  record.action.toLowerCase().includes('update') ? 'bg-yellow-100 dark:bg-yellow-900' :
                  record.action.toLowerCase().includes('delete') ? 'bg-red-100 dark:bg-red-900' :
                  record.action.toLowerCase().includes('approve') ? 'bg-green-100 dark:bg-green-900' :
                  'bg-gray-100 dark:bg-gray-900'
                }`}>
                  <ActionIcon className={`w-5 h-5 ${
                    record.action.toLowerCase().includes('create') ? 'text-blue-600 dark:text-blue-400' :
                    record.action.toLowerCase().includes('update') ? 'text-yellow-600 dark:text-yellow-400' :
                    record.action.toLowerCase().includes('delete') ? 'text-red-600 dark:text-red-400' :
                    record.action.toLowerCase().includes('approve') ? 'text-green-600 dark:text-green-400' :
                    'text-gray-600 dark:text-gray-400'
                  }`} />
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-foreground">
                      {record.action.charAt(0).toUpperCase() + record.action.slice(1)}
                    </span>
                    <Badge className={getActionColor(record.action)}>
                      {record.action}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                    <div className="flex items-center gap-1">
                      <User className="w-4 h-4" />
                      <span>User ID: {record.userId}</span>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>
                        {format(new Date(record.timestamp), 'MMM dd, yyyy HH:mm:ss')}
                      </span>
                    </div>
                  </div>
                  
                  {record.comments && (
                    <div className="text-sm text-foreground mb-2 p-2 bg-muted/50 rounded-lg">
                      <span className="font-medium">Comment: </span>
                      {record.comments}
                    </div>
                  )}
                  
                  {formatChanges(record.changes)}
                </div>
              </motion.div>
            );
          })}
        </div>
        
        {/* Summary */}
        <div className="mt-6 pt-4 border-t border-border">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {auditRecords.filter(r => r.action.toLowerCase().includes('create')).length}
              </div>
              <div className="text-xs text-muted-foreground">Created</div>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-yellow-600">
                {auditRecords.filter(r => r.action.toLowerCase().includes('update')).length}
              </div>
              <div className="text-xs text-muted-foreground">Updated</div>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-green-600">
                {auditRecords.filter(r => r.action.toLowerCase().includes('approve')).length}
              </div>
              <div className="text-xs text-muted-foreground">Approved</div>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {auditRecords.filter(r => r.action.toLowerCase().includes('sent')).length}
              </div>
              <div className="text-xs text-muted-foreground">Sent</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}