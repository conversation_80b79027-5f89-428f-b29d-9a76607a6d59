import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield, ArrowLeft } from 'lucide-react';

interface ProtectedAdminRouteProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'manager';
  fallback?: React.ReactNode;
}

export const ProtectedAdminRoute: React.FC<ProtectedAdminRouteProps> = ({
  children,
  requiredRole = 'admin',
  fallback
}) => {
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const location = useLocation();

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role permissions
  const hasPermission = () => {
    if (!user) return false;
    
    if (requiredRole === 'admin') {
      return user.role === 'admin';
    }
    
    if (requiredRole === 'manager') {
      return user.role === 'admin' || user.role === 'manager';
    }
    
    return false;
  };

  // Show unauthorized page if user doesn't have permission
  if (!hasPermission()) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <Shield className="w-8 h-8 text-red-600" />
            </div>
            <CardTitle className="text-xl">Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access this page. This area is restricted to {requiredRole}s only.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600">
                <strong>Your current role:</strong> {user?.role || 'Unknown'}
              </p>
              <p className="text-sm text-gray-600">
                <strong>Required role:</strong> {requiredRole}
              </p>
            </div>
            
            <Button 
              onClick={() => window.history.back()} 
              variant="outline" 
              className="w-full"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <>{children}</>;
};

// Hook for checking permissions in components
export const usePermissions = () => {
  const { user } = useSelector((state: RootState) => state.auth);

  const hasRole = (role: 'admin' | 'manager' | 'viewer') => {
    if (!user) return false;
    return user.role === role;
  };

  const hasMinimumRole = (minimumRole: 'admin' | 'manager' | 'viewer') => {
    if (!user) return false;
    
    const roleHierarchy = { admin: 3, manager: 2, viewer: 1 };
    const userLevel = roleHierarchy[user.role];
    const requiredLevel = roleHierarchy[minimumRole];
    
    return userLevel >= requiredLevel;
  };

  const canAccessAdmin = () => hasRole('admin');
  const canManageUsers = () => hasRole('admin');
  const canEditContent = () => hasMinimumRole('manager');
  const canViewContent = () => hasMinimumRole('viewer');

  return {
    user,
    hasRole,
    hasMinimumRole,
    canAccessAdmin,
    canManageUsers,
    canEditContent,
    canViewContent,
  };
};

// Component for conditionally rendering content based on permissions
interface PermissionGateProps {
  role?: 'admin' | 'manager' | 'viewer';
  minimumRole?: 'admin' | 'manager' | 'viewer';
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const PermissionGate: React.FC<PermissionGateProps> = ({
  role,
  minimumRole,
  children,
  fallback = null
}) => {
  const { hasRole, hasMinimumRole } = usePermissions();

  let hasPermission = false;

  if (role) {
    hasPermission = hasRole(role);
  } else if (minimumRole) {
    hasPermission = hasMinimumRole(minimumRole);
  }

  return hasPermission ? <>{children}</> : <>{fallback}</>;
};

// Higher-order component for protecting components
export const withPermissions = <P extends object>(
  Component: React.ComponentType<P>,
  requiredRole: 'admin' | 'manager' | 'viewer'
) => {
  return (props: P) => {
    const { hasMinimumRole } = usePermissions();

    if (!hasMinimumRole(requiredRole)) {
      return (
        <div className="p-4 text-center">
          <p className="text-gray-500">You don't have permission to view this content.</p>
        </div>
      );
    }

    return <Component {...props} />;
  };
};