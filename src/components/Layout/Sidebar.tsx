import React, { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  LayoutDashboard,
  Users,
  FileText,
  Receipt,
  BarChart3,
  Settings,
  Building2,
  GitBranch,
  ChevronLeft,
  ChevronRight,
  Shield,
  Database,
  Brain,
  MessageSquareQuote,
  Building,
  ChevronDown,
  Target,
  Plus,
} from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../ui/collapsible";
import { RootState, AppDispatch } from "../../store";
import { toggleSidebar } from "../../store/slices/uiSlice";
import { useAuth } from "../../hooks/useAuth";

interface NavigationItem {
  name: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  requiredRole?: "admin" | "manager" | "viewer";
  children?: NavigationItem[];
}

interface NavigationSection {
  name: string;
  items: NavigationItem[];
  requiredRole?: "admin" | "manager" | "viewer";
}

const navigationSections: NavigationSection[] = [
  {
    name: "Overview",
    items: [
      {
        name: "Dashboard",
        path: "/dashboard",
        icon: LayoutDashboard,
      },
    ],
  },
  {
    name: "Account Management",
    items: [
      { name: "All Accounts", path: "/accounts/list", icon: Building },
      {
        name: "Create Account",
        path: "/accounts/create",
        icon: Building,
        requiredRole: "manager",
      },
      { name: "All Contacts", path: "/contacts/list", icon: Users },
      {
        name: "Create Contact",
        path: "/contacts/create",
        icon: Users,
        requiredRole: "manager",
      },
    ],
  },
  {
    name: "Opportunity Management",
    items: [
      { name: "All Opportunities", path: "/opportunities", icon: Target },
      {
        name: "Create Opportunity",
        path: "/opportunities/create",
        icon: Plus,
        requiredRole: "manager",
      },
    ],
  },
  {
    name: "Vendor Management",
    items: [
      { name: "All Vendors", path: "/vendors/list", icon: Building2 },
      {
        name: "Onboard Vendor",
        path: "/vendors/onboard",
        icon: Building2,
        requiredRole: "manager",
      },
    ],
  },
  {
    name: "Contract Management",
    items: [
      { name: "All Contracts", path: "/contracts/list", icon: FileText },
      {
        name: "Create Contract",
        path: "/contracts/create",
        icon: FileText,
        requiredRole: "manager",
      },
    ],
  },
  {
    name: "Financial Management",
    items: [
      { name: "All Invoices", path: "/invoices/list", icon: Receipt },
      {
        name: "Create Invoice",
        path: "/invoices/create",
        icon: Receipt,
        requiredRole: "manager",
      },
      { name: "All RFQs", path: "/rfqs/list", icon: MessageSquareQuote },
      {
        name: "Create RFQ",
        path: "/rfqs/create",
        icon: MessageSquareQuote,
        requiredRole: "manager",
      },
    ],
  },
  {
    name: "Performance & Analytics",
    items: [
      { name: "Scorecards", path: "/performance/scorecards", icon: BarChart3 },
      { name: "Risk Assessment", path: "/performance/risks", icon: BarChart3 },
      {
        name: "Analytics Dashboard",
        path: "/analytics/dashboard",
        icon: BarChart3,
      },
      { name: "Custom Reports", path: "/analytics/reports", icon: BarChart3 },
      { name: "AI Insights", path: "/analytics/ai-insights", icon: BarChart3 },
    ],
  },
  {
    name: "Automation",
    requiredRole: "manager",
    items: [
      { name: "All Workflows", path: "/workflows/list", icon: GitBranch },
      {
        name: "Create Workflow",
        path: "/workflows/create",
        icon: GitBranch,
        requiredRole: "admin",
      },
    ],
  },
  {
    name: "Settings",
    items: [{ name: "Settings Hub", path: "/settings", icon: Settings }],
  },
];

export const Sidebar: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);
  const { hasPermission } = useAuth();
  const location = useLocation();
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    Overview: true,
    "Account Management": false,
    "Opportunity Management": false,
    "Vendor Management": false,
    "Contract Management": false,
    "Financial Management": false,
    "Performance & Analytics": false,
    Automation: false,
    Settings: false,
  });

  const isActiveRoute = (path: string) => {
    return (
      location.pathname === path || location.pathname.startsWith(path + "/")
    );
  };

  const toggleSection = (sectionName: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionName]: !prev[sectionName],
    }));
  };

  const renderNavItem = (item: NavigationItem) => {
    if (item.requiredRole && !hasPermission(item.requiredRole)) {
      return null;
    }

    const isActive = isActiveRoute(item.path);
    const Icon = item.icon;

    return (
      <NavLink
        key={item.path}
        to={item.path}
        className={({ isActive }) => `
      flex items-center py-2 rounded-xl transition-all duration-200 mb-1
      ${sidebarCollapsed ? "justify-center w-10 h-10 mx-auto" : "px-3"}
      ${
        isActive
          ? "bg-primary text-primary-foreground soft-shadow"
          : "text-muted-foreground hover:bg-accent hover:text-accent-foreground hover:scale-105"
      }
    `}
      >
        <Icon className={`w-4 h-4 ${sidebarCollapsed ? "" : "mr-3"}`} />
        {!sidebarCollapsed && (
          <span className="font-medium text-sm">{item.name}</span>
        )}
      </NavLink>
    );
  };

  const renderSection = (section: NavigationSection) => {
    if (section.requiredRole && !hasPermission(section.requiredRole)) {
      return null;
    }

    const hasVisibleItems = section.items.some(
      (item) => !item.requiredRole || hasPermission(item.requiredRole)
    );

    if (!hasVisibleItems) {
      return null;
    }

    const isOpen = openSections[section.name];
    const hasActiveItem = section.items.some((item) =>
      isActiveRoute(item.path)
    );

    if (sidebarCollapsed) {
      return (
        <div key={section.name} className="mb-2">
          {section.items.map((item) => renderNavItem(item))}
        </div>
      );
    }

    return (
      <Collapsible
        key={section.name}
        open={isOpen}
        onOpenChange={() => toggleSection(section.name)}
      >
        <CollapsibleTrigger className="w-full">
          <div
            className={`flex items-center justify-between px-3 py-2 rounded-xl transition-all duration-200 mb-1 ${
              hasActiveItem
                ? "bg-accent/50 text-primary font-medium"
                : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
            }`}
          >
            <span className="font-semibold text-xs uppercase tracking-wide">
              {section.name}
            </span>
            <ChevronDown
              className={`w-4 h-4 transition-transform duration-200 ${
                isOpen ? "rotate-180" : ""
              }`}
            />
          </div>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-1 ml-3">
          {section.items.map((item) => renderNavItem(item))}
        </CollapsibleContent>
      </Collapsible>
    );
  };

  return (
    <div
      className={`fixed left-0 top-0 h-full bg-card border-r border-border transition-all duration-300 z-30 ${
        sidebarCollapsed ? "w-16" : "w-64"
      }`}
    >
      <div
        className={`flex flex-col h-full ${sidebarCollapsed ? "p-2" : "p-4"}`}
      >
        {/* Logo */}
        <div className="flex items-center mb-8">
          <div className="w-8 h-8 bg-primary rounded-lg gradient-primary soft-shadow flex items-center justify-center">
            <Building2 className="w-5 h-5 text-primary-foreground" />
          </div>
          {!sidebarCollapsed && (
            <div className="ml-3">
              <h1 className="text-lg font-bold text-foreground">VendorMS</h1>
              <p className="text-xs text-muted-foreground">Management System</p>
            </div>
          )}
        </div>

        {/* Toggle Button */}
        <button
          onClick={() => dispatch(toggleSidebar())}
          className="btn-neumorphic mb-6 w-full flex items-center justify-center"
        >
          {sidebarCollapsed ? (
            <ChevronRight className="w-4 h-4" />
          ) : (
            <ChevronLeft className="w-4 h-4" />
          )}
        </button>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto space-y-2">
          {navigationSections.map((section) => renderSection(section))}
        </nav>
      </div>
    </div>
  );
};
