import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

interface BreadcrumbConfig {
  [key: string]: {
    label: string;
    icon?: React.ComponentType<{ className?: string }>;
  };
}

const breadcrumbConfig: BreadcrumbConfig = {
  dashboard: { label: 'Dashboard', icon: Home },
  admin: { label: 'Administration' },
  users: { label: 'User Management' },
  settings: { label: 'Settings' },
  backup: { label: 'Backup' },
  profile: { label: 'Profile' },
  vendors: { label: 'Vendors' },
  contracts: { label: 'Contracts' },
  invoices: { label: 'Invoices' },
  rfqs: { label: 'RFQs' },
  analytics: { label: 'Analytics' },
  workflows: { label: 'Workflows' },
  performance: { label: 'Performance' },
  list: { label: 'List' },
  create: { label: 'Create' },
  edit: { label: 'Edit' },
  view: { label: 'View' },
};

export const Breadcrumbs: React.FC = () => {
  const location = useLocation();
  const pathSegments = location.pathname.split('/').filter(Boolean);

  // Don't show breadcrumbs on dashboard
  if (pathSegments.length <= 1) {
    return null;
  }

  const generateBreadcrumbs = () => {
    const breadcrumbs = [];
    let currentPath = '';

    // Always start with dashboard
    breadcrumbs.push({
      label: 'Dashboard',
      path: '/dashboard',
      icon: Home,
      isLast: false,
    });

    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;
      const config = breadcrumbConfig[segment];
      
      // Skip numeric IDs in breadcrumbs
      if (/^\d+$/.test(segment)) {
        return;
      }

      breadcrumbs.push({
        label: config?.label || segment.charAt(0).toUpperCase() + segment.slice(1),
        path: currentPath,
        icon: config?.icon,
        isLast,
      });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  return (
    <div className="mb-6">
      <Breadcrumb>
        <BreadcrumbList>
          {breadcrumbs.map((breadcrumb, index) => [
            <BreadcrumbItem key={breadcrumb.path}>
              {breadcrumb.isLast ? (
                <BreadcrumbPage className="flex items-center">
                  {breadcrumb.icon && (
                    <breadcrumb.icon className="w-4 h-4 mr-2" />
                  )}
                  {breadcrumb.label}
                </BreadcrumbPage>
              ) : (
                <BreadcrumbLink asChild>
                  <Link 
                    to={breadcrumb.path}
                    className="flex items-center hover:text-primary transition-colors"
                  >
                    {breadcrumb.icon && (
                      <breadcrumb.icon className="w-4 h-4 mr-2" />
                    )}
                    {breadcrumb.label}
                  </Link>
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>,
            !breadcrumb.isLast && <BreadcrumbSeparator key={`separator-${breadcrumb.path}`} />
          ].filter(Boolean))}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
};