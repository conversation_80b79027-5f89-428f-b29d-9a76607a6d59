export interface User {
  id: number;
  name: string;
  email: string;
  avatar?: string;
}

export interface CommentAttachment {
  id: number;
  comment_id: number;
  filename: string;
  original_name: string;
  file_size: number;
  mime_type: string;
  file_path: string;
  uploaded_by: number;
  created_at: string;
}

export interface CommentMention {
  id: number;
  comment_id: number;
  mentioned_user_id: number;
  user_name?: string;
  user_email?: string;
  created_at: string;
}

export interface Comment {
  id: number;
  entity_type: string;
  entity_id: number;
  parent_comment_id?: number;
  user_id: number;
  user_name?: string;
  user_email?: string;
  user_avatar?: string;
  text: string;
  created_at: string;
  updated_at: string;
  attachment_count?: number;
  mention_count?: number;
  attachments?: CommentAttachment[];
  mentions?: CommentMention[];
  replies?: Comment[];
}

export interface CommentFormData {
  content: string;
  parentId?: number;
  attachments?: File[];
  mentions?: number[];
}

export interface CommentStats {
  total_comments: number;
  active_comments: number;
  resolved_comments: number;
  replies: number;
  unique_commenters: number;
}

export interface CommentFilters {
  category?: string;
  status?: string;
  sortBy?: 'newest' | 'oldest' | 'most_replies';
  limit?: number;
  offset?: number;
}

export interface CommentSearchOptions {
  objectType?: string;
  category?: string;
  limit?: number;
  offset?: number;
}

export interface TypingUser {
  userId: number;
  userName: string;
}

export interface CommentNotification {
  id: number;
  type: 'mention' | 'reply' | 'status_change';
  comment_id: number;
  object_type: string;
  object_id: number;
  message: string;
  read: boolean;
  created_at: string;
}

export interface CommentReaction {
  id: number;
  comment_id: number;
  user_id: number;
  reaction_type: 'like' | 'dislike' | 'heart' | 'laugh' | 'angry';
  created_at: string;
}

export interface CommentActivity {
  id: number;
  comment_id: number;
  user_id: number;
  action: 'created' | 'updated' | 'deleted' | 'resolved' | 'replied';
  details?: Record<string, any>;
  created_at: string;
}

// API Response types
export interface CommentResponse {
  success: boolean;
  data: Comment;
  message?: string;
}

export interface CommentsResponse {
  success: boolean;
  data: Comment[];
  meta?: {
    total: number;
    objectType: string;
    objectId: number;
    limit?: number;
    offset?: number;
  };
}

export interface CommentStatsResponse {
  success: boolean;
  data: CommentStats;
}

export interface CommentSearchResponse {
  success: boolean;
  data: Comment[];
  meta?: {
    searchTerm: string;
    total: number;
    limit: number;
    offset: number;
  };
}

// Socket event types
export interface SocketCommentEvent {
  comment: Comment;
  mentions?: number[];
  timestamp: string;
}

export interface SocketTypingEvent {
  userId: number;
  userName: string;
  isTyping: boolean;
  timestamp: string;
}

export interface SocketUserEvent {
  userId: number;
  userName: string;
  roomId: string;
  timestamp: string;
}

export interface SocketPresenceEvent {
  userId: number;
  userName: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  timestamp: string;
}

export interface SocketNotificationEvent {
  type: 'mention' | 'reply' | 'status_change';
  commentId: number;
  objectType: string;
  objectId: number;
  message: string;
  timestamp: string;
}

// Error types
export interface CommentError {
  success: false;
  message: string;
  error?: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

// Configuration types
export interface CommentConfig {
  allowAttachments: boolean;
  allowMentions: boolean;
  maxDepth: number;
  maxFileSize: number;
  maxFiles: number;
  allowedFileTypes: string[];
  enableRealTime: boolean;
  enableTypingIndicators: boolean;
  autoSave: boolean;
  moderationEnabled: boolean;
}

// Utility types
export type CommentStatus = 'active' | 'resolved' | 'deleted';
export type CommentContentType = 'text' | 'html';
export type CommentCategory = 'general' | 'legal' | 'financial' | 'technical' | 'performance' | 'compliance';
export type CommentSortBy = 'newest' | 'oldest' | 'most_replies';
export type ObjectType = 'vendor' | 'contract' | 'document' | 'user' | 'project';
export type ReactionType = 'like' | 'dislike' | 'heart' | 'laugh' | 'angry';
export type UserPresenceStatus = 'online' | 'away' | 'busy' | 'offline';
export type NotificationType = 'mention' | 'reply' | 'status_change';
export type CommentAction = 'created' | 'updated' | 'deleted' | 'resolved' | 'replied';