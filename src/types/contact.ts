// Contact Management Types
// TypeScript interfaces for Contact entities and related data structures

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
}

export interface Contact {
  id: number;
  account_id: number;
  first_name?: string;
  last_name: string;
  salutation?: ContactSalutation;
  title?: string;
  department?: string;
  phone?: string;
  mobile_phone?: string;
  home_phone?: string;
  other_phone?: string;
  fax?: string;
  email: string;
  mailing_address?: Address;
  other_address?: Address;
  reports_to_id?: number;
  assistant_name?: string;
  assistant_phone?: string;
  birthdate?: Date;
  lead_source?: ContactLeadSource;
  description?: string;
  languages?: string;
  level?: ContactLevel;
  do_not_call: boolean;
  has_opted_out_of_email: boolean;
  owner_id?: number;
  integration_id?: string;
  custom_fields?: Record<string, unknown>;
  status: ContactStatus;
  created_at: Date;
  updated_at: Date;
  created_by_id?: number;
  last_modified_by_id?: number;
  is_deleted: boolean;
  
  // Computed fields
  full_name?: string;
  account_name?: string;
  reports_to_name?: string;
  owner_email?: string;
  direct_reports_count?: string;
  reportingChain?: ContactSummary[];
  
  // Legacy camelCase fields for backward compatibility
  accountId?: number;
  firstName?: string;
  lastName?: string;
  mobilePhone?: string;
  homePhone?: string;
  otherPhone?: string;
  mailingAddress?: Address;
  otherAddress?: Address;
  reportsToId?: number;
  assistantName?: string;
  assistantPhone?: string;
  leadSource?: ContactLeadSource;
  doNotCall?: boolean;
  hasOptedOutOfEmail?: boolean;
  ownerId?: number;
  integrationId?: string;
  customFields?: Record<string, unknown>;
  createdAt?: Date;
  updatedAt?: Date;
  createdById?: number;
  lastModifiedById?: number;
  isDeleted?: boolean;
  directReportsCount?: number;
  
  // Relationships (populated when needed)
  account?: {
    id: number;
    name: string;
  };
  reportsTo?: ContactSummary;
  directReports?: ContactSummary[];
  owner?: {
    id: number;
    email: string;
    role: string;
  };
  createdBy?: {
    id: number;
    email: string;
  };
  lastModifiedBy?: {
    id: number;
    email: string;
  };
}

export interface ContactSummary {
  id: number;
  full_name: string;
  first_name?: string;
  last_name: string;
  title?: string;
  department?: string;
  email: string;
  phone?: string;
  // Legacy camelCase for backward compatibility
  fullName?: string;
  firstName?: string;
  lastName?: string;
}

// Enums matching Salesforce picklists and database enums
export enum ContactSalutation {
  MR = 'Mr.',
  MS = 'Ms.',
  MRS = 'Mrs.',
  DR = 'Dr.',
  PROF = 'Prof.',
  REV = 'Rev.'
}

export enum ContactLeadSource {
  WEB = 'Web',
  PHONE_INQUIRY = 'Phone Inquiry',
  PARTNER_REFERRAL = 'Partner Referral',
  PURCHASED_LIST = 'Purchased List',
  OTHER = 'Other'
}

export enum ContactLevel {
  PRIMARY = 'Primary',
  SECONDARY = 'Secondary',
  TERTIARY = 'Tertiary'
}

export enum ContactStatus {
  ACTIVE = 'open',      // Maps to 'open' in database
  INACTIVE = 'dismissed', // Maps to 'dismissed' in database
  ARCHIVED = 'resolved',  // Maps to 'resolved' in database
  PENDING = 'escalated'   // Maps to 'escalated' in database
}

// Request/Response types
export interface ContactCreateRequest {
  account_id: number;
  first_name?: string;
  last_name: string;
  salutation?: ContactSalutation;
  title?: string;
  department?: string;
  phone?: string;
  mobile_phone?: string;
  home_phone?: string;
  other_phone?: string;
  fax?: string;
  email: string;
  mailing_address?: Address;
  other_address?: Address;
  reports_to_id?: number;
  assistant_name?: string;
  assistant_phone?: string;
  birthdate?: Date;
  lead_source?: ContactLeadSource;
  description?: string;
  languages?: string;
  level?: ContactLevel;
  do_not_call?: boolean;
  has_opted_out_of_email?: boolean;
  owner_id?: number;
  custom_fields?: Record<string, unknown>;
}

export interface ContactUpdateRequest extends Partial<ContactCreateRequest> {
  id?: number; // Should not be updated, but included for type safety
}

export interface ContactSearchFilters {
  search?: string;
  account_id?: number;
  department?: string[];
  level?: ContactLevel[];
  lead_source?: ContactLeadSource[];
  status?: ContactStatus[];
  owner_id?: number;
  reports_to_id?: number;
  has_direct_reports?: boolean;
  created_after?: Date;
  created_before?: Date;
}

export interface PaginatedContacts {
  contacts: Contact[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ContactHierarchy {
  id: number;
  full_name?: string;
  fullName?: string; // backward compatibility
  first_name?: string;
  firstName?: string; // backward compatibility
  last_name: string;
  lastName?: string; // backward compatibility
  reports_to_id?: number;
  reportsToId?: number; // backward compatibility
  level: number;
}

// Organizational Chart types
export interface OrgChartNode {
  id: number;
  full_name?: string;
  fullName?: string; // backward compatibility
  first_name?: string;
  firstName?: string; // backward compatibility
  last_name?: string;
  lastName?: string; // backward compatibility
  title?: string;
  department?: string;
  email: string;
  phone?: string;
  reports_to_id?: number;
  reportsToId?: number; // backward compatibility
  children: OrgChartNode[];
  level: number;
}

export interface OrgChartData {
  rootNode: OrgChartNode;
  totalNodes: number;
  maxDepth: number;
}

// Salesforce Integration types
export interface ContactSyncResult {
  success: boolean;
  salesforceId?: string;
  error?: string;
  conflictResolution?: ConflictResolution;
}

export interface ContactSyncConflict {
  field: string;
  vmsValue: unknown;
  salesforceValue: unknown;
  lastModifiedVms: Date;
  lastModifiedSalesforce: Date;
}

export interface ConflictResolution {
  strategy: 'VMS_WINS' | 'SALESFORCE_WINS' | 'MANUAL';
  resolvedValue: unknown;
  resolvedBy?: number;
  resolvedAt: Date;
}

export interface BatchContactSyncResult {
  successful: number;
  failed: number;
  results: ContactSyncResult[];
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ContactValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Communication preferences
export interface CommunicationPreferences {
  doNotCall: boolean;
  hasOptedOutOfEmail: boolean;
  preferredLanguage?: string;
  preferredContactMethod?: 'email' | 'phone' | 'mobile';
  customPreferences?: Record<string, unknown>;
}

// Export types
export interface ContactExportData {
  headers: string[];
  rows: (string | number | Date)[][];
}

// Form types for frontend
// Form types for frontend - using type alias instead of empty interface
export type ContactFormData = ContactCreateRequest;

export interface ContactFormErrors {
  [key: string]: string;
}

// API Response types
export interface ContactResponse {
  success: boolean;
  data?: Contact;
  error?: string;
  errors?: ValidationError[];
}

// Backend API Response type (snake_case format)
export interface ContactApiResponse {
  success: boolean;
  data?: {
    id: number;
    account_id: number;
    first_name?: string;
    last_name: string;
    salutation?: string;
    title?: string;
    department?: string;
    phone?: string;
    mobile_phone?: string;
    home_phone?: string;
    other_phone?: string;
    fax?: string;
    email: string;
    mailing_address?: Address;
    other_address?: Address;
    reports_to_id?: number | null;
    assistant_name?: string;
    assistant_phone?: string;
    birthdate?: string | null;
    lead_source?: string;
    description?: string | null;
    languages?: string | null;
    level?: string;
    do_not_call: boolean;
    has_opted_out_of_email: boolean;
    owner_id?: number;
    integration_id?: string | null;
    custom_fields?: Record<string, unknown>;
    status: string;
    created_at: string;
    updated_at: string;
    created_by_id?: number;
    last_modified_by_id?: number;
    is_deleted: boolean;
    full_name?: string;
    account_name?: string;
    reports_to_name?: string | null;
    owner_email?: string;
    direct_reports_count?: string;
    directReports?: ContactSummary[];
    reportingChain?: ContactSummary[];
  };
  message?: string;
  error?: string;
  errors?: ValidationError[];
}

export interface ContactListResponse {
  success: boolean;
  data?: PaginatedContacts;
  error?: string;
}

export interface ContactHierarchyResponse {
  success: boolean;
  data?: ContactHierarchy[];
  error?: string;
}

export interface OrgChartResponse {
  success: boolean;
  data?: OrgChartData;
  error?: string;
}

// Audit types
export interface ContactAuditEntry {
  id: number;
  entityType: string;
  entityId: number;
  action: 'create' | 'update' | 'delete' | 'view';
  userId?: number;
  userName?: string;
  oldValue?: Partial<Contact>;
  newValue?: Partial<Contact>;
  details?: Record<string, unknown>;
  timestamp: Date;
}

// Bulk operations
export interface BulkContactOperation {
  operation: 'create' | 'update' | 'delete';
  contacts: ContactCreateRequest[] | ContactUpdateRequest[] | number[];
}

export interface BulkContactResult {
  successful: number;
  failed: number;
  results: Array<{
    success: boolean;
    data?: Contact;
    error?: string;
    index: number;
  }>;
}

// Constants for picklist values
export const CONTACT_SALUTATIONS = Object.values(ContactSalutation);
export const CONTACT_LEAD_SOURCES = Object.values(ContactLeadSource);
export const CONTACT_LEVELS = Object.values(ContactLevel);
export const CONTACT_STATUSES = Object.values(ContactStatus);

// Helper type guards
export const isValidContactSalutation = (value: string): value is ContactSalutation => {
  return Object.values(ContactSalutation).includes(value as ContactSalutation);
};

export const isValidContactLeadSource = (value: string): value is ContactLeadSource => {
  return Object.values(ContactLeadSource).includes(value as ContactLeadSource);
};

export const isValidContactLevel = (value: string): value is ContactLevel => {
  return Object.values(ContactLevel).includes(value as ContactLevel);
};

export const isValidContactStatus = (value: string): value is ContactStatus => {
  return Object.values(ContactStatus).includes(value as ContactStatus);
};

// Validation helpers
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhoneNumber = (phone: string): boolean => {
  // Basic phone validation - can be enhanced based on requirements
  const phoneRegex = /^[+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-()]/g, ''));
};

export const validateContactHierarchy = (contactId: number, reportsToId: number): boolean => {
  // This would need to be implemented with actual hierarchy checking logic
  return contactId !== reportsToId;
};

// Default values
export const DEFAULT_CONTACT_STATUS = ContactStatus.ACTIVE;
export const DEFAULT_PAGINATION_LIMIT = 10;
export const MAX_HIERARCHY_DEPTH = 5;
export const DEFAULT_DO_NOT_CALL = false;
export const DEFAULT_HAS_OPTED_OUT_OF_EMAIL = false;

// Field mappings for Salesforce integration
export const SALESFORCE_FIELD_MAPPING = {
  firstName: 'FirstName',
  lastName: 'LastName',
  salutation: 'Salutation',
  title: 'Title',
  department: 'Department',
  phone: 'Phone',
  mobilePhone: 'MobilePhone',
  homePhone: 'HomePhone',
  otherPhone: 'OtherPhone',
  fax: 'Fax',
  email: 'Email',
  mailingAddress: {
    street: 'MailingStreet',
    city: 'MailingCity',
    state: 'MailingState',
    postalCode: 'MailingPostalCode',
    country: 'MailingCountry'
  },
  otherAddress: {
    street: 'OtherStreet',
    city: 'OtherCity',
    state: 'OtherState',
    postalCode: 'OtherPostalCode',
    country: 'OtherCountry'
  },
  reportsToId: 'ReportsToId',
  assistantName: 'AssistantName',
  assistantPhone: 'AssistantPhone',
  birthdate: 'Birthdate',
  leadSource: 'LeadSource',
  description: 'Description',
  doNotCall: 'DoNotCall',
  hasOptedOutOfEmail: 'HasOptedOutOfEmail',
  accountId: 'AccountId'
};

// Search configuration
export const CONTACT_SEARCH_FIELDS = [
  'firstName',
  'lastName',
  'email',
  'title',
  'department',
  'phone',
  'mobilePhone'
];

// Required fields for contact creation
export const REQUIRED_CONTACT_FIELDS = [
  'accountId',
  'lastName',
  'email'
];

// Fields that support fuzzy search
export const FUZZY_SEARCH_FIELDS = [
  'firstName',
  'lastName',
  'title',
  'department'
];