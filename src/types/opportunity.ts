// Opportunity Management Types
// TypeScript interfaces for Opportunity entities and related data structures

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
}

export interface ContactRole {
  contact_id: number;
  role: string;
  is_primary: boolean;
  contact_name?: string;
  contact_email?: string;
}

export interface LineItem {
  product_name: string;
  description?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  discount_percent?: number;
}

export interface Opportunity {
  id: number;
  account_id: number;
  name: string;
  stage: OpportunityStage;
  amount?: number;
  probability?: number;
  close_date?: Date;
  type?: OpportunityType;
  lead_source?: OpportunityLeadSource;
  next_step?: string;
  description?: string;
  forecast_category?: ForecastCategory;
  campaign_id?: number;
  owner_id?: number;
  contact_roles?: ContactRole[];
  line_items?: LineItem[];
  integration_id?: string;
  custom_fields?: Record<string, unknown>;
  status: OpportunityStatus;
  created_at: Date;
  updated_at: Date;
  created_by_id?: number;
  last_modified_by_id?: number;
  is_deleted: boolean;
  
  // Computed fields
  account_name?: string;
  owner_email?: string;
  days_to_close?: number;
  stage_duration?: number;
  
  // Legacy camelCase fields for backward compatibility
  accountId?: number;
  closeDate?: Date;
  ownerId?: number;
  contactRoles?: ContactRole[];
  lineItems?: LineItem[];
}

// Enums matching Salesforce picklists
export enum OpportunityStage {
  PROSPECTING = 'Prospecting',
  QUALIFICATION = 'Qualification',
  NEEDS_ANALYSIS = 'Needs Analysis',
  VALUE_PROPOSITION = 'Value Proposition',
  ID_DECISION_MAKERS = 'Id. Decision Makers',
  PERCEPTION_ANALYSIS = 'Perception Analysis',
  PROPOSAL_PRICE_QUOTE = 'Proposal/Price Quote',
  NEGOTIATION_REVIEW = 'Negotiation/Review',
  CLOSED_WON = 'Closed Won',
  CLOSED_LOST = 'Closed Lost'
}

export enum OpportunityType {
  EXISTING_CUSTOMER_UPGRADE = 'Existing Customer - Upgrade',
  EXISTING_CUSTOMER_REPLACEMENT = 'Existing Customer - Replacement',
  EXISTING_CUSTOMER_DOWNGRADE = 'Existing Customer - Downgrade',
  NEW_CUSTOMER = 'New Customer'
}

export enum OpportunityLeadSource {
  WEB = 'Web',
  PHONE_INQUIRY = 'Phone Inquiry',
  PARTNER_REFERRAL = 'Partner Referral',
  PURCHASED_LIST = 'Purchased List',
  OTHER = 'Other'
}

export enum ForecastCategory {
  OMITTED = 'Omitted',
  PIPELINE = 'Pipeline',
  BEST_CASE = 'Best Case',
  COMMIT = 'Commit',
  CLOSED = 'Closed'
}

export enum OpportunityStatus {
  OPEN = 'open',
  WON = 'won',
  LOST = 'lost',
  CANCELLED = 'cancelled'
}

// Request/Response types
export interface OpportunityCreateRequest {
  account_id: number;
  name: string;
  stage: OpportunityStage;
  amount?: number;
  probability?: number;
  close_date?: Date;
  type?: OpportunityType;
  lead_source?: OpportunityLeadSource;
  next_step?: string;
  description?: string;
  forecast_category?: ForecastCategory;
  campaign_id?: number;
  owner_id?: number;
  contact_roles?: ContactRole[];
  line_items?: LineItem[];
  custom_fields?: Record<string, unknown>;
}

export interface OpportunityUpdateRequest extends Partial<OpportunityCreateRequest> {
  id?: number; // Should not be updated, but included for type safety
}

export interface OpportunitySearchFilters {
  search?: string;
  account_id?: number;
  stage?: OpportunityStage[];
  type?: OpportunityType[];
  lead_source?: OpportunityLeadSource[];
  forecast_category?: ForecastCategory[];
  status?: OpportunityStatus[];
  owner_id?: number;
  amount_min?: number;
  amount_max?: number;
  probability_min?: number;
  probability_max?: number;
  close_date_after?: Date;
  close_date_before?: Date;
  created_after?: Date;
  created_before?: Date;
}

export interface PaginatedOpportunities {
  opportunities: Opportunity[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface OpportunityPipelineData {
  stage: OpportunityStage;
  count: number;
  total_amount: number;
  average_amount: number;
  opportunities: Opportunity[];
}

export interface OpportunityAnalytics {
  total_opportunities: number;
  total_amount: number;
  average_deal_size: number;
  win_rate: number;
  average_sales_cycle: number;
  pipeline_by_stage: OpportunityPipelineData[];
  forecast_summary: {
    commit: number;
    best_case: number;
    pipeline: number;
  };
  monthly_trends: {
    month: string;
    opportunities_created: number;
    opportunities_won: number;
    revenue: number;
  }[];
}

export interface OpportunityStatistics {
  total_count: number;
  open_count: number;
  won_count: number;
  lost_count: number;
  total_value: number;
  won_value: number;
  lost_value: number;
  average_deal_size: number;
  win_rate: number;
  conversion_rate_by_stage: {
    stage: OpportunityStage;
    conversion_rate: number;
  }[];
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface OpportunityValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Export types
export interface OpportunityExportData {
  headers: string[];
  rows: (string | number | Date)[][];
}

// Form types for frontend
export type OpportunityFormData = OpportunityCreateRequest;

export interface OpportunityFormErrors {
  [key: string]: string;
}

// API Response types
export interface OpportunityResponse {
  success: boolean;
  data?: Opportunity;
  error?: string;
  errors?: ValidationError[];
}

export interface OpportunityListResponse {
  success: boolean;
  data?: PaginatedOpportunities;
  error?: string;
}

export interface OpportunityPipelineResponse {
  success: boolean;
  data?: OpportunityPipelineData[];
  error?: string;
}

export interface OpportunityAnalyticsResponse {
  success: boolean;
  data?: OpportunityAnalytics;
  error?: string;
}

export interface OpportunityStatisticsResponse {
  success: boolean;
  data?: OpportunityStatistics;
  error?: string;
}

// Bulk operations
export interface BulkOpportunityOperation {
  operation: 'create' | 'update' | 'delete';
  opportunities: OpportunityCreateRequest[] | OpportunityUpdateRequest[] | number[];
}

export interface BulkOpportunityResult {
  successful: number;
  failed: number;
  results: Array<{
    success: boolean;
    data?: Opportunity;
    error?: string;
    index: number;
  }>;
}

// Stage advancement
export interface StageAdvanceRequest {
  opportunity_id: number;
  new_stage: OpportunityStage;
  notes?: string;
}

// Constants for picklist values
export const OPPORTUNITY_STAGES = Object.values(OpportunityStage);
export const OPPORTUNITY_TYPES = Object.values(OpportunityType);
export const OPPORTUNITY_LEAD_SOURCES = Object.values(OpportunityLeadSource);
export const FORECAST_CATEGORIES = Object.values(ForecastCategory);
export const OPPORTUNITY_STATUSES = Object.values(OpportunityStatus);

// Contact role constants
export const CONTACT_ROLES = [
  'Decision Maker',
  'Economic Buyer',
  'Technical Buyer',
  'Influencer',
  'End User',
  'Champion',
  'Evaluator'
] as const;

export type ContactRoleType = typeof CONTACT_ROLES[number];