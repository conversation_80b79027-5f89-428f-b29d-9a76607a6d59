// Import and re-export all RFQ types from the API service for centralized type management
import type {
  RFQ,
  RFQItem,
  FormField,
  AISettings,
  RFQInvitation,
  RFQSubmission,
  BidItem,
  BidAlternative,
  SubmissionAttachment,
  RFQAnalytics,
  RFQFilters,
  RFQCreateData,
  RFQUpdateData,
  BidComparisonData,
  SubmissionAnalytics,
  RFQStatusStatistics,
  AuditEntry
} from '../services/api/rfq';

// Re-export the types
export type {
  RFQ,
  RFQItem,
  FormField,
  AISettings,
  RFQInvitation,
  RFQSubmission,
  BidItem,
  BidAlternative,
  SubmissionAttachment,
  RFQAnalytics,
  RFQFilters,
  RFQCreateData,
  RFQUpdateData,
  BidComparisonData,
  SubmissionAnalytics,
  RFQStatusStatistics,
  AuditEntry
};

// Additional UI-specific types
export interface RFQListState {
  rfqs: RFQ[];
  loading: boolean;
  error: string | null;
  filters: RFQFilters;
  searchQuery: string;
  selectedRFQs: number[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface RFQDetailState {
  rfq: RFQ | null;
  invitations: RFQInvitation[];
  submissions: RFQSubmission[];
  analytics: RFQAnalytics | null;
  bidComparison: BidComparisonData | null;
  auditHistory: AuditEntry[];
  loading: boolean;
  error: string | null;
  activeTab: 'overview' | 'invitations' | 'submissions' | 'analysis' | 'quotes' | 'invoices' | 'history';
}

export interface RFQFormState {
  formData: RFQCreateData;
  currentStep: number;
  totalSteps: number;
  isValid: boolean;
  errors: Record<string, string>;
  isDraft: boolean;
  isSaving: boolean;
}

export interface BidSelectionState {
  selectedBids: Record<string, {
    submissionId: number;
    vendorId: number;
    vendorName: string;
    itemId: string;
    unitPrice: number;
    totalPrice: number;
    deliveryDays?: number;
    notes?: string;
  }>;
  selectionRationale: Record<string, string>;
  diversificationEnabled: boolean;
}

export interface QuoteGenerationData {
  rfqId: number;
  selectedBids: BidSelectionState['selectedBids'];
  clientInfo: {
    name: string;
    email: string;
    company?: string;
    address?: string;
  };
  pricing: {
    subtotal: number;
    markup: number;
    markupType: 'percentage' | 'fixed';
    taxes: number;
    total: number;
  };
  terms: {
    paymentTerms: string;
    deliveryTerms: string;
    validityPeriod: number;
    additionalTerms?: string;
  };
  customization: {
    includeVendorDetails: boolean;
    includeItemBreakdown: boolean;
    includeDeliverySchedule: boolean;
    customNotes?: string;
  };
}

// Utility types for form handling
export type RFQStatus = 'draft' | 'sent' | 'in_progress' | 'closed' | 'cancelled';
export type InvitationStatus = 'sent' | 'viewed' | 'submitted';
export type FormFieldType = 'text' | 'number' | 'date' | 'file' | 'select' | 'textarea';
export type SortField = 'created_at' | 'due_date' | 'title' | 'response_rate' | 'status';
export type SortOrder = 'asc' | 'desc';
export type ExportFormat = 'csv' | 'excel';

// Constants
export const RFQ_STATUSES: { value: RFQStatus; label: string; color: string }[] = [
  { value: 'draft', label: 'Draft', color: 'gray' },
  { value: 'sent', label: 'Sent', color: 'blue' },
  { value: 'in_progress', label: 'In Progress', color: 'yellow' },
  { value: 'closed', label: 'Closed', color: 'green' },
  { value: 'cancelled', label: 'Cancelled', color: 'red' }
];

export const INVITATION_STATUSES: { value: InvitationStatus; label: string; color: string }[] = [
  { value: 'sent', label: 'Sent', color: 'blue' },
  { value: 'viewed', label: 'Viewed', color: 'yellow' },
  { value: 'submitted', label: 'Submitted', color: 'green' }
];

export const CURRENCIES = [
  { value: 'USD', label: 'US Dollar ($)', symbol: '$' },
  { value: 'EUR', label: 'Euro (€)', symbol: '€' },
  { value: 'GBP', label: 'British Pound (£)', symbol: '£' },
  { value: 'JPY', label: 'Japanese Yen (¥)', symbol: '¥' },
  { value: 'CNY', label: 'Chinese Yuan (¥)', symbol: '¥' }
];

export const FORM_FIELD_TYPES: { value: FormFieldType; label: string }[] = [
  { value: 'text', label: 'Text Input' },
  { value: 'textarea', label: 'Text Area' },
  { value: 'number', label: 'Number Input' },
  { value: 'date', label: 'Date Picker' },
  { value: 'select', label: 'Dropdown Select' },
  { value: 'file', label: 'File Upload' }
];

// Validation schemas (for client-side validation)
export const RFQ_VALIDATION_RULES = {
  title: {
    required: true,
    minLength: 3,
    maxLength: 255
  },
  description: {
    maxLength: 5000
  },
  items: {
    required: true,
    minItems: 1,
    maxItems: 100
  },
  due_date: {
    required: true,
    futureDate: true
  },
  selectedVendors: {
    required: true,
    minItems: 1
  },
  terms: {
    maxLength: 10000
  }
};

export const ITEM_VALIDATION_RULES = {
  name: {
    required: true,
    minLength: 1,
    maxLength: 255
  },
  description: {
    maxLength: 1000
  },
  quantity: {
    required: true,
    min: 1,
    max: 1000000
  },
  category: {
    maxLength: 100
  },
  estimatedPrice: {
    min: 0
  }
};

// Helper functions
export const getRFQStatusColor = (status: RFQStatus): string => {
  return RFQ_STATUSES.find(s => s.value === status)?.color || 'gray';
};

export const getInvitationStatusColor = (status: InvitationStatus): string => {
  return INVITATION_STATUSES.find(s => s.value === status)?.color || 'gray';
};

export const getCurrencySymbol = (currency: string): string => {
  return CURRENCIES.find(c => c.value === currency)?.symbol || currency;
};

export const formatCurrency = (amount: number | undefined | null, currency: string | undefined | null): string => {
  const safeCurrency = currency || 'USD';
  if (amount === undefined || amount === null || isNaN(amount) || typeof amount !== 'number') {
    return `${getCurrencySymbol(safeCurrency)}0.00`;
  }
  const symbol = getCurrencySymbol(safeCurrency);
  const safeAmount = Number(amount);
  if (isNaN(safeAmount)) {
    return `${symbol}0.00`;
  }
  return `${symbol}${safeAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

export const calculateResponseRate = (invitations: number, submissions: number): number => {
  return invitations > 0 ? Math.round((submissions / invitations) * 100) : 0;
};

export const isRFQEditable = (status: RFQStatus): boolean => {
  return status === 'draft';
};

export const isRFQCancellable = (status: RFQStatus): boolean => {
  return status === 'draft' || status === 'sent';
};

export const canSendInvitations = (status: RFQStatus): boolean => {
  return status === 'draft';
};

export const generateRFQItemId = (): string => {
  return `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const generateFormFieldId = (): string => {
  return `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};