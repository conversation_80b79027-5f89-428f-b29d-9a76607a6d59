// Account Management Types
// TypeScript interfaces for Account entities and related data structures

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
}

export interface Account {
  id: number;
  name: string;
  account_number?: string;
  type?: AccountType;
  industry?: Industry;
  annual_revenue?: number;
  number_of_employees?: number;
  ownership?: Ownership;
  phone?: string;
  fax?: string;
  website?: string;
  ticker_symbol?: string;
  site?: string;
  rating?: Rating;
  description?: string;
  billing_address?: Address;
  shipping_address?: Address;
  parent_account_id?: number;
  owner_id?: number;
  integration_id?: string;
  custom_fields?: Record<string, undefined>;
  status: AccountStatus;
  created_at: Date;
  updated_at: Date;
  created_by_id?: number;
  last_modified_by_id?: number;
  is_deleted: boolean;
  
  // Computed fields
  children_count?: number;
  
  // Relationships (populated when needed)
  parent?: Account;
  children?: Account[];
  owner?: {
    id: number;
    email: string;
    role: string;
  };
  createdBy?: {
    id: number;
    email: string;
  };
  lastModifiedBy?: {
    id: number;
    email: string;
  };
}

// Enums matching Salesforce picklists
export enum AccountType {
  PROSPECT = 'Prospect',
  CUSTOMER_DIRECT = 'Customer - Direct',
  CUSTOMER_CHANNEL = 'Customer - Channel',
  CHANNEL_PARTNER = 'Channel Partner / Reseller',
  INSTALLATION_PARTNER = 'Installation Partner',
  TECHNOLOGY_PARTNER = 'Technology Partner',
  OTHER = 'Other'
}

export enum Industry {
  AGRICULTURE = 'Agriculture',
  APPAREL = 'Apparel',
  BANKING = 'Banking',
  BIOTECHNOLOGY = 'Biotechnology',
  CHEMICALS = 'Chemicals',
  COMMUNICATIONS = 'Communications',
  CONSTRUCTION = 'Construction',
  CONSULTING = 'Consulting',
  EDUCATION = 'Education',
  ELECTRONICS = 'Electronics',
  ENERGY = 'Energy',
  ENGINEERING = 'Engineering',
  ENTERTAINMENT = 'Entertainment',
  ENVIRONMENTAL = 'Environmental',
  FINANCE = 'Finance',
  FOOD_BEVERAGE = 'Food & Beverage',
  GOVERNMENT = 'Government',
  HEALTHCARE = 'Healthcare',
  HOSPITALITY = 'Hospitality',
  INSURANCE = 'Insurance',
  MACHINERY = 'Machinery',
  MANUFACTURING = 'Manufacturing',
  MEDIA = 'Media',
  NOT_FOR_PROFIT = 'Not For Profit',
  RECREATION = 'Recreation',
  RETAIL = 'Retail',
  SHIPPING = 'Shipping',
  TECHNOLOGY = 'Technology',
  TELECOMMUNICATIONS = 'Telecommunications',
  TRANSPORTATION = 'Transportation',
  UTILITIES = 'Utilities',
  OTHER = 'Other'
}

export enum Rating {
  HOT = 'Hot',
  WARM = 'Warm',
  COLD = 'Cold'
}

export enum Ownership {
  PUBLIC = 'Public',
  PRIVATE = 'Private',
  SUBSIDIARY = 'Subsidiary',
  OTHER = 'Other'
}

export enum AccountStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE'
}

// Request/Response types
export interface AccountCreateRequest {
  name: string;
  account_number?: string;
  type?: AccountType;
  industry?: Industry;
  annual_revenue?: number;
  number_of_employees?: number;
  ownership?: Ownership;
  phone?: string;
  fax?: string;
  website?: string;
  ticker_symbol?: string;
  site?: string;
  rating?: Rating;
  description?: string;
  billing_address?: Address;
  shipping_address?: Address;
  parent_account_id?: number;
  owner_id?: number;
  custom_fields?: Record<string, undefined>;
}

export interface AccountUpdateRequest extends Partial<AccountCreateRequest> {
  id?: number; // Should not be updated, but included for type safety
}

export interface AccountSearchFilters {
  search?: string;
  industry?: Industry[];
  type?: AccountType[];
  rating?: Rating[];
  ownership?: Ownership[];
  annual_revenue_min?: number;
  annual_revenue_max?: number;
  number_of_employees_min?: number;
  number_of_employees_max?: number;
  country?: string[];
  state?: string[];
  city?: string[];
  parent_account_id?: number;
  owner_id?: number;
  status?: AccountStatus[];
  has_children?: boolean;
  created_after?: Date;
  created_before?: Date;
}

export interface PaginatedAccounts {
  accounts: Account[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface AccountHierarchy {
  id: number;
  name: string;
  parentAccountId?: number;
  level: number;
}

// Salesforce Integration types
export interface SyncResult {
  success: boolean;
  salesforceId?: string;
  error?: string;
  conflictResolution?: ConflictResolution;
}

export interface SyncConflict {
  field: string;
  vmsValue: undefined;
  salesforceValue: undefined;
  lastModifiedVms: Date;
  lastModifiedSalesforce: Date;
}

export interface ConflictResolution {
  strategy: 'VMS_WINS' | 'SALESFORCE_WINS' | 'MANUAL';
  resolvedValue: undefined;
  resolvedBy?: number;
  resolvedAt: Date;
}

export interface BatchSyncResult {
  successful: number;
  failed: number;
  results: SyncResult[];
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface AccountValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Export types
export interface AccountExportData {
  headers: string[];
  rows: (string | number | Date)[][];
}

// Form types for frontend
// export interface AccountFormData extends AccountCreateRequest {
//   // Additional form-specific fields if needed
// }

export interface AccountFormErrors {
  [key: string]: string;
}

// API Response types
export interface AccountResponse {
  success: boolean;
  data?: Account;
  error?: string;
  errors?: ValidationError[];
}

export interface AccountListResponse {
  success: boolean;
  data?: PaginatedAccounts;
  error?: string;
}

export interface AccountHierarchyResponse {
  success: boolean;
  data?: AccountHierarchy[];
  error?: string;
}

// Audit types
export interface AccountAuditEntry {
  id: number;
  entityType: string;
  entityId: number;
  action: 'create' | 'update' | 'delete' | 'view';
  userId?: number;
  userName?: string;
  oldValue?: Partial<Account>;
  newValue?: Partial<Account>;
  details?: Record<string, undefined>;
  timestamp: Date;
}

// Constants for picklist values
export const ACCOUNT_TYPES = Object.values(AccountType);
export const INDUSTRIES = Object.values(Industry);
export const RATINGS = Object.values(Rating);
export const OWNERSHIP_TYPES = Object.values(Ownership);
export const ACCOUNT_STATUSES = Object.values(AccountStatus);

// Helper type guards
export const isValidAccountType = (value: string): value is AccountType => {
  return Object.values(AccountType).includes(value as AccountType);
};

export const isValidIndustry = (value: string): value is Industry => {
  return Object.values(Industry).includes(value as Industry);
};

export const isValidRating = (value: string): value is Rating => {
  return Object.values(Rating).includes(value as Rating);
};

export const isValidOwnership = (value: string): value is Ownership => {
  return Object.values(Ownership).includes(value as Ownership);
};

export const isValidAccountStatus = (value: string): value is AccountStatus => {
  return Object.values(AccountStatus).includes(value as AccountStatus);
};

// Default values
export const DEFAULT_ACCOUNT_STATUS = AccountStatus.ACTIVE;
export const DEFAULT_PAGINATION_LIMIT = 10;
export const MAX_HIERARCHY_DEPTH = 5;