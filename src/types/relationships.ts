// Relationship and linking types for Account Management
// Types for managing relationships between accounts and other entities

export interface RelatedEntity {
  id: number;
  type: EntityType;
  name: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  accountId: number;
  // Additional fields specific to entity type
  [key: string]: any;
}

export enum EntityType {
  OPPORTUNITY = 'opportunity',
  RFQ = 'rfq',
  QUOTE = 'quote',
  CONTRACT = 'contract',
  INVOICE = 'invoice',
  CONTACT = 'contact'
}

export interface AccountRelationships {
  accountId: number;
  opportunities: RelatedOpportunity[];
  rfqs: RelatedRFQ[];
  quotes: RelatedQuote[];
  contracts: RelatedContract[];
  invoices: RelatedInvoice[];
  contacts: RelatedContact[];
}

// Specific related entity types
export interface RelatedOpportunity extends RelatedEntity {
  type: EntityType.OPPORTUNITY;
  stage: string;
  amount?: number;
  closeDate?: Date;
  probability?: number;
  owner?: string;
}

export interface RelatedRFQ extends RelatedEntity {
  type: EntityType.RFQ;
  rfqNumber: string;
  dueDate?: Date;
  submissionCount: number;
  totalValue?: number;
}

export interface RelatedQuote extends RelatedEntity {
  type: EntityType.QUOTE;
  quoteNumber: string;
  amount: number;
  validUntil?: Date;
  rfqId?: number;
  opportunityId?: number;
}

export interface RelatedContract extends RelatedEntity {
  type: EntityType.CONTRACT;
  contractNumber: string;
  value: number;
  startDate: Date;
  endDate: Date;
  renewalDate?: Date;
}

export interface RelatedInvoice extends RelatedEntity {
  type: EntityType.INVOICE;
  invoiceNumber: string;
  amount: number;
  dueDate: Date;
  paidDate?: Date;
  contractId?: number;
}

export interface RelatedContact extends RelatedEntity {
  type: EntityType.CONTACT;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  isPrimary: boolean;
}

// Linking operations
export interface EntityLinkRequest {
  entityType: EntityType;
  entityId: number;
  accountId: number;
  linkType?: LinkType;
  metadata?: Record<string, any>;
}

export interface EntityUnlinkRequest {
  entityType: EntityType;
  entityId: number;
  accountId: number;
}

export enum LinkType {
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
  BILLING = 'billing',
  SHIPPING = 'shipping',
  TECHNICAL = 'technical'
}

// Relationship statistics
export interface RelationshipStats {
  accountId: number;
  totalRelatedEntities: number;
  entitiesByType: Record<EntityType, number>;
  recentActivity: RelatedEntityActivity[];
  topEntitiesByValue: RelatedEntity[];
}

export interface RelatedEntityActivity {
  entityType: EntityType;
  entityId: number;
  entityName: string;
  action: ActivityAction;
  timestamp: Date;
  userId?: number;
  userName?: string;
  details?: string;
}

export enum ActivityAction {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  LINKED = 'linked',
  UNLINKED = 'unlinked'
}
 