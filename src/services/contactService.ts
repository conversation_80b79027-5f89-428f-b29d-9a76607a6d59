import axios from "axios";
import {
  Contact,
  ContactCreateRequest,
  ContactUpdateRequest,
  ContactSearchFilters,
  PaginatedContacts,
  ContactHierarchy,
  OrgChartData,
  ContactValidationResult,
  ContactExportData,
  BulkContactResult,
} from "../types/contact";

const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:3001";

class ContactServiceClass {
  private static instance: ContactServiceClass;
  private baseURL: string;

  constructor() {
    this.baseURL = `${API_BASE_URL}/api/contacts`;
  }

  static getInstance(): ContactServiceClass {
    if (!ContactServiceClass.instance) {
      ContactServiceClass.instance = new ContactServiceClass();
    }
    return ContactServiceClass.instance;
  }

  // Get all contacts with search and filtering
  async searchContacts(
    filters: ContactSearchFilters
  ): Promise<PaginatedContacts> {
    try {
      const params = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (Array.isArray(value)) {
            value.forEach((v) => params.append(key, v.toString()));
          } else {
            params.set(key, value.toString());
          }
        }
      });

      const response = await axios.get(`${this.baseURL}?${params.toString()}`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to search contacts"
      );
    }
  }

  // Get contact by ID
  async getContact(id: number): Promise<Contact> {
    try {
      const response = await axios.get(`${this.baseURL}/${id}`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || "Failed to get contact");
    }
  }

  // Create new contact
  async createContact(data: ContactCreateRequest): Promise<Contact> {
    try {
      const response = await axios.post(this.baseURL, data);
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to create contact"
      );
    }
  }

  // Update contact
  async updateContact(
    id: number,
    data: ContactUpdateRequest
  ): Promise<Contact> {
    try {
      const response = await axios.put(`${this.baseURL}/${id}`, data);
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to update contact"
      );
    }
  }

  // Delete contact
  async deleteContact(id: number): Promise<void> {
    try {
      await axios.delete(`${this.baseURL}/${id}`);
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to delete contact"
      );
    }
  }

  // Get contacts for a specific account
  async getAccountContacts(
    accountId: number,
    options: {
      page?: number;
      limit?: number;
      includeHierarchy?: boolean;
      filters?: Partial<ContactSearchFilters>;
    } = {}
  ): Promise<PaginatedContacts> {
    try {
      const params = new URLSearchParams();

      if (options.page) params.set("page", options.page.toString());
      if (options.limit) params.set("limit", options.limit.toString());
      if (options.includeHierarchy) params.set("include_hierarchy", "true");

      if (options.filters) {
        Object.entries(options.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== "") {
            if (Array.isArray(value)) {
              value.forEach((v) => params.append(key, v.toString()));
            } else {
              params.set(key, value.toString());
            }
          }
        });
      }

      const response = await axios.get(
        `${this.baseURL}/account/${accountId}?${params.toString()}`
      );
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to get account contacts"
      );
    }
  }

  // Get contact hierarchy/org chart
  async getContactHierarchy(
    accountId: number,
    rootContactId?: number
  ): Promise<{
    orgChart: any[];
    statistics: any;
  }> {
    try {
      const params = new URLSearchParams();
      if (rootContactId)
        params.set("root_contact_id", rootContactId.toString());

      const response = await axios.get(
        `${this.baseURL}/account/${accountId}/hierarchy?${params.toString()}`
      );
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to get contact hierarchy"
      );
    }
  }

  // Update contact hierarchy
  async updateContactHierarchy(
    contactId: number,
    reportsToId?: number
  ): Promise<Contact> {
    try {
      const response = await axios.put(
        `${this.baseURL}/${contactId}/hierarchy`,
        {
          reports_to_id: reportsToId,
        }
      );
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to update contact hierarchy"
      );
    }
  }

  // Bulk create contacts
  async bulkCreateContacts(
    contacts: ContactCreateRequest[]
  ): Promise<BulkContactResult> {
    try {
      const response = await axios.post(`${this.baseURL}/bulk`, { contacts });
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to bulk create contacts"
      );
    }
  }

  // Bulk update contacts
  async bulkUpdateContacts(
    contacts: (ContactUpdateRequest & { id: number })[]
  ): Promise<BulkContactResult> {
    try {
      const response = await axios.put(`${this.baseURL}/bulk`, { contacts });
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to bulk update contacts"
      );
    }
  }

  // Bulk delete contacts
  async bulkDeleteContacts(contactIds: number[]): Promise<void> {
    try {
      // For now, delete contacts one by one
      // In a real implementation, you might want a bulk delete endpoint
      await Promise.all(contactIds.map((id) => this.deleteContact(id)));
    } catch (error: any) {
      throw new Error("Failed to bulk delete contacts");
    }
  }

  // Find duplicate contacts
  async findDuplicates(criteria: {
    email?: string;
    firstName?: string;
    lastName?: string;
    accountId?: number;
  }): Promise<Contact[]> {
    try {
      const params = new URLSearchParams();

      Object.entries(criteria).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          params.set(key, value.toString());
        }
      });

      const response = await axios.get(
        `${this.baseURL}/duplicates?${params.toString()}`
      );
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to find duplicate contacts"
      );
    }
  }

  // Merge contacts
  async mergeContacts(
    primaryContactId: number,
    duplicateContactIds: number[]
  ): Promise<Contact> {
    try {
      const response = await axios.post(`${this.baseURL}/merge`, {
        primary_contact_id: primaryContactId,
        duplicate_contact_ids: duplicateContactIds,
      });
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to merge contacts"
      );
    }
  }

  // Get contact statistics
  async getContactStatistics(accountId?: number): Promise<any> {
    try {
      const params = new URLSearchParams();
      if (accountId) params.set("account_id", accountId.toString());

      const response = await axios.get(
        `${this.baseURL}/statistics?${params.toString()}`
      );
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to get contact statistics"
      );
    }
  }

  // Export contacts
  async exportContacts(
    filters: Partial<ContactSearchFilters> & { contactIds?: number[] } = {}
  ): Promise<void> {
    try {
      const params = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (Array.isArray(value)) {
            value.forEach((v) => params.append(key, v.toString()));
          } else {
            params.set(key, value.toString());
          }
        }
      });

      const response = await axios.get(
        `${this.baseURL}/export?${params.toString()}`,
        {
          responseType: "blob",
        }
      );

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "contacts.csv");
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to export contacts"
      );
    }
  }

  // Get contact audit history
  async getContactAuditHistory(contactId: number): Promise<any[]> {
    try {
      const response = await axios.get(`${this.baseURL}/${contactId}/audit`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to get contact audit history"
      );
    }
  }

  // Get unique departments
  async getDepartments(accountId?: number): Promise<string[]> {
    try {
      const params = new URLSearchParams();
      if (accountId) params.set("account_id", accountId.toString());

      const response = await axios.get(
        `${this.baseURL}/departments?${params.toString()}`
      );
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to get departments"
      );
    }
  }

  // Get unique lead sources
  async getLeadSources(): Promise<string[]> {
    try {
      const response = await axios.get(`${this.baseURL}/lead-sources`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error || "Failed to get lead sources"
      );
    }
  }

  // Update communication preferences
  async updateCommunicationPreferences(
    contactId: number,
    preferences: {
      doNotCall?: boolean;
      hasOptedOutOfEmail?: boolean;
      preferredLanguage?: string;
      customPreferences?: Record<string, any>;
    }
  ): Promise<Contact> {
    try {
      const response = await axios.put(
        `${this.baseURL}/${contactId}/communication-preferences`,
        preferences
      );
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error ||
          "Failed to update communication preferences"
      );
    }
  }

  // Validate contact data
  async validateContact(
    data: ContactCreateRequest | ContactUpdateRequest
  ): Promise<ContactValidationResult> {
    try {
      // This would typically be a separate validation endpoint
      // For now, we'll do basic client-side validation
      const errors: any[] = [];

      if (!data.lastName) {
        errors.push({
          field: "lastName",
          message: "Last name is required",
          code: "required",
        });
      }

      if (!data.email) {
        errors.push({
          field: "email",
          message: "Email is required",
          code: "required",
        });
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
        errors.push({
          field: "email",
          message: "Invalid email format",
          code: "invalid_format",
        });
      }

      if (!data.accountId) {
        errors.push({
          field: "accountId",
          message: "Account is required",
          code: "required",
        });
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    } catch (error: any) {
      throw new Error("Failed to validate contact data");
    }
  }
}

// Export singleton instance
export const ContactService = ContactServiceClass.getInstance();
export default ContactService;
