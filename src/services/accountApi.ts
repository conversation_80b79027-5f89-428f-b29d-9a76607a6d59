import { apiClient, ApiResponse, PaginatedResponse } from "./api";
import {
  Account,
  AccountCreateRequest,
  AccountUpdateRequest,
  AccountSearchFilters,
  PaginatedAccounts,
  AccountHierarchy,
  AccountAuditEntry,
} from "../types/account";

// Account API service
export class AccountApi {
  // Get all accounts with filtering and pagination
  static async getAccounts(
    filters: AccountSearchFilters = {},
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedAccounts> {
    const params = new URLSearchParams();

    // Add filters to params
    if (filters.search) params.append("search", filters.search);
    if (filters.industry) {
      if (Array.isArray(filters.industry)) {
        filters.industry.forEach((industry) =>
          params.append("industry", industry)
        );
      } else {
        params.append("industry", filters.industry);
      }
    }
    if (filters.type) {
      if (Array.isArray(filters.type)) {
        filters.type.forEach((type) => params.append("type", type));
      } else {
        params.append("type", filters.type);
      }
    }
    if (filters.rating) {
      if (Array.isArray(filters.rating)) {
        filters.rating.forEach((rating) => params.append("rating", rating));
      } else {
        params.append("rating", filters.rating);
      }
    }
    if (filters.ownership) {
      if (Array.isArray(filters.ownership)) {
        filters.ownership.forEach((ownership) =>
          params.append("ownership", ownership)
        );
      } else {
        params.append("ownership", filters.ownership);
      }
    }
    if (filters.status) {
      if (Array.isArray(filters.status)) {
        filters.status.forEach((status) => params.append("status", status));
      } else {
        params.append("status", filters.status);
      }
    }
    if (filters.annualRevenueMin !== undefined) {
      params.append("annual_revenue_min", filters.annualRevenueMin.toString());
    }
    if (filters.annualRevenueMax !== undefined) {
      params.append("annual_revenue_max", filters.annualRevenueMax.toString());
    }
    if (filters.numberOfEmployeesMin !== undefined) {
      params.append(
        "number_of_employees_min",
        filters.numberOfEmployeesMin.toString()
      );
    }
    if (filters.numberOfEmployeesMax !== undefined) {
      params.append(
        "number_of_employees_max",
        filters.numberOfEmployeesMax.toString()
      );
    }
    if (filters.country) {
      if (Array.isArray(filters.country)) {
        filters.country.forEach((country) => params.append("country", country));
      } else {
        params.append("country", filters.country);
      }
    }
    if (filters.state) {
      if (Array.isArray(filters.state)) {
        filters.state.forEach((state) => params.append("state", state));
      } else {
        params.append("state", filters.state);
      }
    }
    if (filters.city) {
      if (Array.isArray(filters.city)) {
        filters.city.forEach((city) => params.append("city", city));
      } else {
        params.append("city", filters.city);
      }
    }
    if (filters.parentAccountId !== undefined) {
      params.append("parent_account_id", filters.parentAccountId.toString());
    }
    if (filters.ownerId !== undefined) {
      params.append("owner_id", filters.ownerId.toString());
    }
    if (filters.hasChildren !== undefined) {
      params.append("has_children", filters.hasChildren.toString());
    }
    if (filters.createdAfter) {
      params.append("created_after", filters.createdAfter.toISOString());
    }
    if (filters.createdBefore) {
      params.append("created_before", filters.createdBefore.toISOString());
    }

    params.append("page", page.toString());
    params.append("limit", limit.toString());

    const response = await apiClient.get<ApiResponse<PaginatedAccounts>>(
      `/accounts?${params.toString()}`
    );
    return response.data.data;
  }

  // Get account by ID
  static async getAccount(id: number): Promise<Account> {
    const response = await apiClient.get<ApiResponse<Account>>(
      `/accounts/${id}`
    );
    return response.data.data;
  }

  // Create new account
  static async createAccount(
    accountData: AccountCreateRequest
  ): Promise<Account> {
    const response = await apiClient.post<ApiResponse<Account>>(
      "/accounts",
      accountData
    );
    return response.data.data;
  }

  // Update account
  static async updateAccount(
    id: number,
    accountData: AccountUpdateRequest
  ): Promise<Account> {
    const response = await apiClient.put<ApiResponse<Account>>(
      `/accounts/${id}`,
      accountData
    );
    return response.data.data;
  }

  // Delete account (soft delete)
  static async deleteAccount(id: number): Promise<void> {
    await apiClient.delete(`/accounts/${id}`);
  }

  // Search accounts
  static async searchAccounts(
    searchParams: AccountSearchFilters
  ): Promise<PaginatedAccounts> {
    const params = new URLSearchParams();

    Object.entries(searchParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach((v) => params.append(key, v.toString()));
        } else {
          params.append(key, value.toString());
        }
      }
    });

    const response = await apiClient.get<ApiResponse<PaginatedAccounts>>(
      `/accounts/search?${params.toString()}`
    );
    return response.data.data;
  }

  // Get account hierarchy
  static async getAccountHierarchy(id: number): Promise<AccountHierarchy[]> {
    const response = await apiClient.get<ApiResponse<AccountHierarchy[]>>(
      `/accounts/${id}/hierarchy`
    );
    return response.data.data;
  }

  // Get account children
  static async getAccountChildren(id: number): Promise<Account[]> {
    const response = await apiClient.get<ApiResponse<Account[]>>(
      `/accounts/${id}/children`
    );
    return response.data.data;
  }

  // Get account ancestors
  static async getAccountAncestors(id: number): Promise<Account[]> {
    const response = await apiClient.get<ApiResponse<Account[]>>(
      `/accounts/${id}/ancestors`
    );
    return response.data.data;
  }

  // Get hierarchy tree
  static async getHierarchyTree(id: number): Promise<undefined> {
    const response = await apiClient.get<ApiResponse<undefined>>(
      `/accounts/${id}/tree`
    );
    return response.data.data;
  }

  // Get hierarchy path (breadcrumb)
  static async getHierarchyPath(id: number): Promise<undefined[]> {
    const response = await apiClient.get<ApiResponse<undefined[]>>(
      `/accounts/${id}/path`
    );
    return response.data.data;
  }

  // Get hierarchy statistics
  static async getHierarchyStatistics(id: number): Promise<undefined> {
    const response = await apiClient.get<ApiResponse<undefined>>(
      `/accounts/${id}/hierarchy-stats`
    );
    return response.data.data;
  }

  // Move account in hierarchy
  static async moveAccount(
    id: number,
    parentAccountId: number | null
  ): Promise<Account> {
    const response = await apiClient.put<ApiResponse<Account>>(
      `/accounts/${id}/move`,
      {
        parent_account_id: parentAccountId,
      }
    );
    return response.data.data;
  }

  // Get root accounts
  static async getRootAccounts(): Promise<Account[]> {
    const response = await apiClient.get<ApiResponse<Account[]>>(
      "/accounts/roots"
    );
    return response.data.data;
  }

  // Get hierarchy summary
  static async getHierarchySummary(): Promise<undefined[]> {
    const response = await apiClient.get<ApiResponse<undefined[]>>(
      "/accounts/hierarchy-summary"
    );
    return response.data.data;
  }

  // Get account audit history
  static async getAccountAuditHistory(
    id: number
  ): Promise<AccountAuditEntry[]> {
    const response = await apiClient.get<ApiResponse<AccountAuditEntry[]>>(
      `/accounts/${id}/audit`
    );
    return response.data.data;
  }

  // Get account types (for dropdowns)
  static async getAccountTypes(): Promise<string[]> {
    const response = await apiClient.get<ApiResponse<string[]>>(
      "/accounts/types"
    );
    return response.data.data;
  }

  // Get account industries (for dropdowns)
  static async getAccountIndustries(): Promise<string[]> {
    const response = await apiClient.get<ApiResponse<string[]>>(
      "/accounts/industries"
    );
    return response.data.data;
  }

  // Get account statistics
  static async getAccountStatistics(): Promise<undefined> {
    const response = await apiClient.get<ApiResponse<undefined>>(
      "/accounts/statistics"
    );
    return response.data.data;
  }

  // Export accounts
  static async exportAccounts(
    format: "csv" | "json" = "csv",
    filters: AccountSearchFilters = {}
  ): Promise<Blob> {
    const params = new URLSearchParams();
    params.append("format", format);

    // Add filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach((v) => params.append(key, v.toString()));
        } else {
          params.append(key, value.toString());
        }
      }
    });

    const response = await apiClient.get(
      `/accounts/export?${params.toString()}`,
      {
        responseType: "blob",
      }
    );
    return response.data;
  }
}

// Export individual functions for easier importing
export const {
  getAccounts,
  getAccount,
  createAccount,
  updateAccount,
  deleteAccount,
  searchAccounts,
  getAccountHierarchy,
  getAccountChildren,
  getAccountAncestors,
  getHierarchyTree,
  getHierarchyPath,
  getHierarchyStatistics,
  moveAccount,
  getRootAccounts,
  getHierarchySummary,
  getAccountAuditHistory,
  getAccountTypes,
  getAccountIndustries,
  getAccountStatistics,
  exportAccounts,
} = AccountApi;
