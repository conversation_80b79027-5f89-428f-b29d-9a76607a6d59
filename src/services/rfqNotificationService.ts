import { RFQStatus, RFQ, RFQInvitation, RFQSubmission } from '@/types/rfq';
import { useDispatch } from 'react-redux';
import { addNotification } from '@/store/slices/notificationsSlice';
import { toast } from '@/hooks/use-toast';

export interface RFQNotificationEvent {
  type: 'status_change' | 'invitation_sent' | 'submission_received' | 'due_date_approaching' | 'overdue';
  rfqId: number;
  rfqTitle: string;
  data: any;
  timestamp: string;
  userId?: number;
}

export class RFQNotificationService {
  private static instance: RFQNotificationService;
  private subscribers: Map<string, (event: RFQNotificationEvent) => void> = new Map();

  static getInstance(): RFQNotificationService {
    if (!RFQNotificationService.instance) {
      RFQNotificationService.instance = new RFQNotificationService();
    }
    return RFQNotificationService.instance;
  }

  // Subscribe to RFQ notifications
  subscribe(key: string, callback: (event: RFQNotificationEvent) => void): () => void {
    this.subscribers.set(key, callback);
    return () => this.subscribers.delete(key);
  }

  // Emit notification to all subscribers
  private emit(event: RFQNotificationEvent): void {
    this.subscribers.forEach(callback => callback(event));
  }

  // Status change notifications
  notifyStatusChange(
    rfq: RFQ, 
    oldStatus: RFQStatus, 
    newStatus: RFQStatus, 
    reason?: string,
    userId?: number
  ): void {
    const event: RFQNotificationEvent = {
      type: 'status_change',
      rfqId: rfq.id,
      rfqTitle: rfq.title,
      data: { oldStatus, newStatus, reason, userId },
      timestamp: new Date().toISOString(),
      userId
    };

    this.emit(event);

    // Show toast notification
    const statusLabels = {
      draft: 'Draft',
      sent: 'Sent',
      in_progress: 'In Progress',
      closed: 'Closed',
      cancelled: 'Cancelled'
    };

    toast({
      title: "RFQ Status Updated",
      description: `"${rfq.title}" changed from ${statusLabels[oldStatus]} to ${statusLabels[newStatus]}`,
      variant: newStatus === 'cancelled' ? 'destructive' : 'default',
    });

    // Add to notification store for persistent notifications
    this.addPersistentNotification({
      type: this.getNotificationTypeFromStatus(newStatus),
      title: 'RFQ Status Changed',
      message: `RFQ "${rfq.title}" is now ${statusLabels[newStatus]}`,
      data: { rfqId: rfq.id, oldStatus, newStatus, reason }
    });
  }

  // Invitation sent notifications
  notifyInvitationsSent(rfq: RFQ, invitations: RFQInvitation[]): void {
    const event: RFQNotificationEvent = {
      type: 'invitation_sent',
      rfqId: rfq.id,
      rfqTitle: rfq.title,
      data: { invitationCount: invitations.length, invitations },
      timestamp: new Date().toISOString()
    };

    this.emit(event);

    toast({
      title: "Invitations Sent",
      description: `${invitations.length} vendor${invitations.length !== 1 ? 's' : ''} invited for "${rfq.title}"`,
      variant: "default",
    });

    this.addPersistentNotification({
      type: 'info',
      title: 'RFQ Invitations Sent',
      message: `${invitations.length} vendors invited for "${rfq.title}"`,
      data: { rfqId: rfq.id, invitationCount: invitations.length }
    });
  }

  // Submission received notifications
  notifySubmissionReceived(rfq: RFQ, submission: RFQSubmission): void {
    const event: RFQNotificationEvent = {
      type: 'submission_received',
      rfqId: rfq.id,
      rfqTitle: rfq.title,
      data: { submission, vendorName: submission.vendor_name },
      timestamp: new Date().toISOString()
    };

    this.emit(event);

    toast({
      title: "New Submission",
      description: `${submission.vendor_name} submitted a bid for "${rfq.title}"`,
      variant: "default",
    });

    this.addPersistentNotification({
      type: 'success',
      title: 'New RFQ Submission',
      message: `${submission.vendor_name} submitted a bid for "${rfq.title}"`,
      data: { rfqId: rfq.id, submissionId: submission.id, vendorName: submission.vendor_name }
    });
  }

  // Due date approaching notifications
  notifyDueDateApproaching(rfq: RFQ, hoursRemaining: number): void {
    const event: RFQNotificationEvent = {
      type: 'due_date_approaching',
      rfqId: rfq.id,
      rfqTitle: rfq.title,
      data: { hoursRemaining, dueDate: rfq.due_date },
      timestamp: new Date().toISOString()
    };

    this.emit(event);

    const timeLabel = hoursRemaining < 24 
      ? `${hoursRemaining} hours`
      : `${Math.floor(hoursRemaining / 24)} days`;

    toast({
      title: "RFQ Due Soon",
      description: `"${rfq.title}" is due in ${timeLabel}`,
      variant: "warning",
    });

    this.addPersistentNotification({
      type: 'warning',
      title: 'RFQ Due Soon',
      message: `"${rfq.title}" is due in ${timeLabel}`,
      data: { rfqId: rfq.id, hoursRemaining, dueDate: rfq.due_date }
    });
  }

  // Overdue notifications
  notifyOverdue(rfq: RFQ, hoursOverdue: number): void {
    const event: RFQNotificationEvent = {
      type: 'overdue',
      rfqId: rfq.id,
      rfqTitle: rfq.title,
      data: { hoursOverdue, dueDate: rfq.due_date },
      timestamp: new Date().toISOString()
    };

    this.emit(event);

    const timeLabel = hoursOverdue < 24 
      ? `${hoursOverdue} hours`
      : `${Math.floor(hoursOverdue / 24)} days`;

    toast({
      title: "RFQ Overdue",
      description: `"${rfq.title}" is overdue by ${timeLabel}`,
      variant: "destructive",
    });

    this.addPersistentNotification({
      type: 'error',
      title: 'RFQ Overdue',
      message: `"${rfq.title}" is overdue by ${timeLabel}`,
      data: { rfqId: rfq.id, hoursOverdue, dueDate: rfq.due_date }
    });
  }

  // Helper method to add persistent notifications
  private addPersistentNotification(notification: {
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    data?: any;
  }): void {
    // This would typically dispatch to the Redux store
    // For now, we'll use a simple approach
    if (typeof window !== 'undefined' && (window as any).store) {
      (window as any).store.dispatch(addNotification(notification));
    }
  }

  private getNotificationTypeFromStatus(status: RFQStatus): 'info' | 'success' | 'warning' | 'error' {
    switch (status) {
      case 'sent':
        return 'info';
      case 'in_progress':
        return 'info';
      case 'closed':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'info';
    }
  }

  // Batch notification for multiple RFQs
  notifyBulkStatusChange(
    rfqs: RFQ[], 
    newStatus: RFQStatus, 
    reason?: string
  ): void {
    rfqs.forEach(rfq => {
      // Assuming we know the old status, in practice this would come from the API response
      const oldStatus = rfq.status;
      this.notifyStatusChange(rfq, oldStatus, newStatus, reason);
    });

    toast({
      title: "Bulk Status Update",
      description: `${rfqs.length} RFQ${rfqs.length !== 1 ? 's' : ''} updated to ${newStatus}`,
      variant: "default",
    });
  }

  // Check for due date notifications (would be called by a background service)
  checkDueDateNotifications(rfqs: RFQ[]): void {
    const now = new Date();
    
    rfqs.forEach(rfq => {
      if (rfq.status === 'sent' || rfq.status === 'in_progress') {
        const dueDate = new Date(rfq.due_date);
        const hoursUntilDue = (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60);
        
        if (hoursUntilDue < 0) {
          // Overdue
          this.notifyOverdue(rfq, Math.abs(hoursUntilDue));
        } else if (hoursUntilDue <= 24) {
          // Due within 24 hours
          this.notifyDueDateApproaching(rfq, hoursUntilDue);
        } else if (hoursUntilDue <= 72) {
          // Due within 3 days
          this.notifyDueDateApproaching(rfq, hoursUntilDue);
        }
      }
    });
  }

  // Real-time subscription management (for WebSocket integration)
  subscribeToRFQUpdates(rfqId: number): () => void {
    // This would integrate with WebSocket service
    // For now, return a placeholder unsubscribe function
    const unsubscribe = () => {
      console.log(`Unsubscribed from RFQ ${rfqId} updates`);
    };

    // Simulate WebSocket connection
    console.log(`Subscribed to RFQ ${rfqId} updates`);
    
    return unsubscribe;
  }

  // Email notification preferences (would integrate with user settings)
  shouldSendEmailNotification(
    userId: number, 
    notificationType: RFQNotificationEvent['type']
  ): boolean {
    // This would check user preferences from the store or API
    // For now, return true for all notifications
    return true;
  }

  // Format notification for different channels
  formatNotificationForChannel(
    event: RFQNotificationEvent, 
    channel: 'toast' | 'email' | 'push'
  ): any {
    const baseMessage = {
      title: this.getNotificationTitle(event),
      message: this.getNotificationMessage(event),
      data: event.data
    };

    switch (channel) {
      case 'toast':
        return {
          ...baseMessage,
          variant: this.getToastVariant(event.type)
        };
      case 'email':
        return {
          ...baseMessage,
          subject: baseMessage.title,
          body: this.getEmailBody(event)
        };
      case 'push':
        return {
          ...baseMessage,
          icon: this.getPushIcon(event.type),
          badge: this.getPushBadge(event.type)
        };
      default:
        return baseMessage;
    }
  }

  private getNotificationTitle(event: RFQNotificationEvent): string {
    switch (event.type) {
      case 'status_change':
        return 'RFQ Status Changed';
      case 'invitation_sent':
        return 'RFQ Invitations Sent';
      case 'submission_received':
        return 'New RFQ Submission';
      case 'due_date_approaching':
        return 'RFQ Due Soon';
      case 'overdue':
        return 'RFQ Overdue';
      default:
        return 'RFQ Update';
    }
  }

  private getNotificationMessage(event: RFQNotificationEvent): string {
    switch (event.type) {
      case 'status_change':
        return `"${event.rfqTitle}" status changed to ${event.data.newStatus}`;
      case 'invitation_sent':
        return `${event.data.invitationCount} vendors invited for "${event.rfqTitle}"`;
      case 'submission_received':
        return `${event.data.vendorName} submitted a bid for "${event.rfqTitle}"`;
      case 'due_date_approaching':
        const hours = event.data.hoursRemaining;
        const timeLabel = hours < 24 ? `${hours} hours` : `${Math.floor(hours / 24)} days`;
        return `"${event.rfqTitle}" is due in ${timeLabel}`;
      case 'overdue':
        const overdueHours = event.data.hoursOverdue;
        const overdueLabel = overdueHours < 24 ? `${overdueHours} hours` : `${Math.floor(overdueHours / 24)} days`;
        return `"${event.rfqTitle}" is overdue by ${overdueLabel}`;
      default:
        return `Update for "${event.rfqTitle}"`;
    }
  }

  private getToastVariant(type: RFQNotificationEvent['type']): 'default' | 'destructive' {
    return type === 'overdue' ? 'destructive' : 'default';
  }

  private getEmailBody(event: RFQNotificationEvent): string {
    // Generate HTML email body based on event type
    return `
      <h2>${this.getNotificationTitle(event)}</h2>
      <p>${this.getNotificationMessage(event)}</p>
      <p><strong>RFQ:</strong> ${event.rfqTitle}</p>
      <p><strong>Time:</strong> ${new Date(event.timestamp).toLocaleString()}</p>
      ${event.data.reason ? `<p><strong>Reason:</strong> ${event.data.reason}</p>` : ''}
    `;
  }

  private getPushIcon(type: RFQNotificationEvent['type']): string {
    switch (type) {
      case 'status_change':
        return '/icons/status-change.png';
      case 'invitation_sent':
        return '/icons/invitation.png';
      case 'submission_received':
        return '/icons/submission.png';
      case 'due_date_approaching':
        return '/icons/warning.png';
      case 'overdue':
        return '/icons/error.png';
      default:
        return '/icons/notification.png';
    }
  }

  private getPushBadge(type: RFQNotificationEvent['type']): number {
    // Return badge count for push notifications
    return 1;
  }
}

export default RFQNotificationService;