import { 
  Opportunity, 
  OpportunityCreateRequest, 
  OpportunityUpdateRequest, 
  OpportunitySearchFilters,
  PaginatedOpportunities,
  OpportunityPipelineData,
  OpportunityAnalytics,
  OpportunityStatistics,
  OpportunityStage,
  OpportunityType,
  OpportunityLeadSource,
  ForecastCategory
} from '../types/opportunity';

const API_BASE_URL = '/api';

class OpportunityService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Get all opportunities with pagination and filtering
  async getOpportunities(filters: OpportunitySearchFilters = {}, page = 1, limit = 10): Promise<PaginatedOpportunities> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    // Add filters to params
    if (filters.search) params.append('search', filters.search);
    if (filters.account_id) params.append('account_id', filters.account_id.toString());
    if (filters.stage && filters.stage.length > 0) params.append('stage_name', filters.stage[0]); // Backend expects single stage
    if (filters.type && filters.type.length > 0) params.append('type', filters.type[0]); // Backend expects single type
    if (filters.lead_source && filters.lead_source.length > 0) params.append('lead_source', filters.lead_source[0]);
    if (filters.forecast_category && filters.forecast_category.length > 0) params.append('forecast_category', filters.forecast_category[0]);
    if (filters.owner_id) params.append('owner_id', filters.owner_id.toString());
    if (filters.amount_min) params.append('min_amount', filters.amount_min.toString());
    if (filters.amount_max) params.append('max_amount', filters.amount_max.toString());
    if (filters.close_date_after) params.append('close_date_from', filters.close_date_after.toISOString().split('T')[0]);
    if (filters.close_date_before) params.append('close_date_to', filters.close_date_before.toISOString().split('T')[0]);
    if (filters.created_after) params.append('created_after', filters.created_after.toISOString());
    if (filters.created_before) params.append('created_before', filters.created_before.toISOString());

    const response = await this.request<{success: boolean, data: PaginatedOpportunities}>(`/opportunities?${params}`);
    return response.data;
  }

  // Get opportunity by ID
  async getOpportunityById(id: number): Promise<Opportunity> {
    const response = await this.request<{success: boolean, data: Opportunity}>(`/opportunities/${id}`);
    return response.data;
  }

  // Create new opportunity
  async createOpportunity(opportunity: OpportunityCreateRequest): Promise<Opportunity> {
    const response = await this.request<{success: boolean, data: Opportunity}>('/opportunities', {
      method: 'POST',
      body: JSON.stringify(opportunity),
    });
    return response.data;
  }

  // Update opportunity
  async updateOpportunity(id: number, opportunity: OpportunityUpdateRequest): Promise<Opportunity> {
    const response = await this.request<{success: boolean, data: Opportunity}>(`/opportunities/${id}`, {
      method: 'PUT',
      body: JSON.stringify(opportunity),
    });
    return response.data;
  }

  // Delete opportunity
  async deleteOpportunity(id: number): Promise<void> {
    await this.request<void>(`/opportunities/${id}`, {
      method: 'DELETE',
    });
  }

  // Search opportunities
  async searchOpportunities(filters: OpportunitySearchFilters = {}): Promise<Opportunity[]> {
    const params = new URLSearchParams();
    
    if (filters.search) params.append('search', filters.search);
    if (filters.account_id) params.append('account_id', filters.account_id.toString());
    if (filters.stage) params.append('stage', filters.stage.join(','));
    if (filters.owner_id) params.append('owner_id', filters.owner_id.toString());
    if (filters.type) params.append('type', filters.type.join(','));
    if (filters.lead_source) params.append('lead_source', filters.lead_source.join(','));
    if (filters.forecast_category) params.append('forecast_category', filters.forecast_category.join(','));
    if (filters.status) params.append('status', filters.status.join(','));
    if (filters.amount_min) params.append('amount_min', filters.amount_min.toString());
    if (filters.amount_max) params.append('amount_max', filters.amount_max.toString());
    if (filters.close_date_after) params.append('close_date_after', filters.close_date_after.toISOString());
    if (filters.close_date_before) params.append('close_date_before', filters.close_date_before.toISOString());
    if (filters.created_after) params.append('created_after', filters.created_after.toISOString());
    if (filters.created_before) params.append('created_before', filters.created_before.toISOString());

    const response = await this.request<{ data: Opportunity[] }>(`/opportunities/search?${params}`);
    return response.data;
  }

  // Get pipeline data for kanban view
  async getPipelineData(filters: OpportunitySearchFilters = {}): Promise<OpportunityPipelineData[]> {
    const params = new URLSearchParams();

    if (filters.search) params.append('search', filters.search);
    if (filters.account_id) params.append('account_id', filters.account_id.toString());
    if (filters.stage) params.append('stage', filters.stage.join(','));
    if (filters.owner_id) params.append('owner_id', filters.owner_id.toString());
    if (filters.type) params.append('type', filters.type.join(','));
    if (filters.lead_source) params.append('lead_source', filters.lead_source.join(','));
    if (filters.forecast_category) params.append('forecast_category', filters.forecast_category.join(','));
    if (filters.status) params.append('status', filters.status.join(','));
    if (filters.amount_min) params.append('amount_min', filters.amount_min.toString());
    if (filters.amount_max) params.append('amount_max', filters.amount_max.toString());
    if (filters.close_date_after) params.append('close_date_after', filters.close_date_after.toISOString());
    if (filters.close_date_before) params.append('close_date_before', filters.close_date_before.toISOString());

    const response = await this.request<{ data: OpportunityPipelineData[] }>(`/opportunities/pipeline?${params}`);
    return response.data;
  }

  // Get analytics data
  async getAnalytics(filters: OpportunitySearchFilters = {}): Promise<OpportunityAnalytics> {
    const params = new URLSearchParams();

    if (filters.search) params.append('search', filters.search);
    if (filters.account_id) params.append('account_id', filters.account_id.toString());
    if (filters.owner_id) params.append('owner_id', filters.owner_id.toString());
    if (filters.close_date_after) params.append('close_date_after', filters.close_date_after.toISOString());
    if (filters.close_date_before) params.append('close_date_before', filters.close_date_before.toISOString());

    const response = await this.request<{ data: OpportunityAnalytics }>(`/opportunities/analytics?${params}`);
    return response.data;
  }

  // Get statistics
  async getStatistics(filters: OpportunitySearchFilters = {}): Promise<OpportunityStatistics> {
    const params = new URLSearchParams();

    if (filters.search) params.append('search', filters.search);
    if (filters.account_id) params.append('account_id', filters.account_id.toString());
    if (filters.owner_id) params.append('owner_id', filters.owner_id.toString());

    const response = await this.request<{ data: OpportunityStatistics }>(`/opportunities/statistics?${params}`);
    return response.data;
  }

  // Get opportunities by account
  async getOpportunitiesByAccount(accountId: number): Promise<Opportunity[]> {
    const response = await this.request<{ data: Opportunity[] }>(`/opportunities/account/${accountId}`);
    return response.data;
  }

  // Get available stages
  async getStages(): Promise<OpportunityStage[]> {
    const response = await this.request<{ data: OpportunityStage[] }>('/opportunities/stages');
    return response.data;
  }

  // Get available types
  async getTypes(): Promise<OpportunityType[]> {
    const response = await this.request<{ data: OpportunityType[] }>('/opportunities/types');
    return response.data;
  }

  // Get available lead sources
  async getLeadSources(): Promise<OpportunityLeadSource[]> {
    const response = await this.request<{ data: OpportunityLeadSource[] }>('/opportunities/lead-sources');
    return response.data;
  }

  // Get available forecast categories
  async getForecastCategories(): Promise<ForecastCategory[]> {
    const response = await this.request<{ data: ForecastCategory[] }>('/opportunities/forecast-categories');
    return response.data;
  }

  // Bulk operations
  async bulkCreateOpportunities(opportunities: OpportunityCreateRequest[]): Promise<Opportunity[]> {
    const response = await this.request<{ data: Opportunity[] }>('/opportunities/bulk', {
      method: 'POST',
      body: JSON.stringify({ opportunities }),
    });
    return response.data;
  }

  async bulkUpdateOpportunities(updates: { id: number; updateData: OpportunityUpdateRequest }[]): Promise<Opportunity[]> {
    const response = await this.request<{ data: Opportunity[] }>('/opportunities/bulk', {
      method: 'PUT',
      body: JSON.stringify({ updates }),
    });
    return response.data;
  }

  async bulkDeleteOpportunities(ids: number[]): Promise<void> {
    await this.request<void>('/opportunities/bulk', {
      method: 'DELETE',
      body: JSON.stringify({ ids }),
    });
  }
}

// Export singleton instance
export const opportunityService = new OpportunityService();
export default opportunityService;