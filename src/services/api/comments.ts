import { Comment, CommentFormData, CommentStats, CommentsResponse, CommentResponse, CommentStatsResponse, CommentSearchResponse } from '../../types/comments';

// Use relative URL for API calls - the dev server will proxy to the backend
const API_BASE_URL = '/api';

/**
 * Comments API service for handling comment operations
 */
export class CommentsAPI {
  private static getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  /**
   * Get comments for a specific object
   */
  static async getComments(
    objectType: string,
    objectId: number,
    options: { category?: string; status?: string; limit?: number; offset?: number } = {}
  ): Promise<CommentsResponse> {
    const params = new URLSearchParams();
    if (options.category) params.append('category', options.category);
    if (options.status) params.append('status', options.status);
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.offset) params.append('offset', options.offset.toString());

    const queryString = params.toString();
    const url = `${API_BASE_URL}/comments/${objectType}/${objectId}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch comments: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Create a new comment
   */
  static async createComment(commentData: {
    object_type: string;
    object_id: number;
    content: string;
    content_type?: string;
    category?: string;
    parent_id?: number;
    metadata?: Record<string, string | number | boolean | null>;
  }): Promise<CommentResponse> {
    const response = await fetch(`${API_BASE_URL}/comments`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(commentData)
    });

    if (!response.ok) {
      throw new Error(`Failed to create comment: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Update an existing comment
   */
  static async updateComment(
    commentId: number,
    updateData: Partial<CommentFormData>
  ): Promise<CommentResponse> {
    const response = await fetch(`${API_BASE_URL}/comments/${commentId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(updateData)
    });

    if (!response.ok) {
      throw new Error(`Failed to update comment: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Delete a comment
   */
  static async deleteComment(commentId: number): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/comments/${commentId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to delete comment: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get a specific comment by ID
   */
  static async getComment(commentId: number): Promise<CommentResponse> {
    const response = await fetch(`${API_BASE_URL}/comments/comment/${commentId}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch comment: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Upload attachments to a comment
   */
  static async uploadAttachments(commentId: number, files: File[]): Promise<{ success: boolean; data: Array<{ id: number; filename: string; original_name: string; file_size: number; mime_type: string }>; message?: string }> {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('attachments', file);
    });

    const token = localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/comments/${commentId}/attachments`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Failed to upload attachments: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Search comments
   */
  static async searchComments(
    query: string,
    options: { objectType?: string; category?: string; limit?: number; offset?: number } = {}
  ): Promise<CommentSearchResponse> {
    const params = new URLSearchParams({ q: query });
    if (options.objectType) params.append('objectType', options.objectType);
    if (options.category) params.append('category', options.category);
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.offset) params.append('offset', options.offset.toString());

    const response = await fetch(`${API_BASE_URL}/comments/search?${params.toString()}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to search comments: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get comment statistics
   */
  static async getStats(options: { objectType?: string; objectId?: number } = {}): Promise<CommentStatsResponse> {
    const params = new URLSearchParams();
    if (options.objectType) params.append('objectType', options.objectType);
    if (options.objectId) params.append('objectId', options.objectId.toString());

    const queryString = params.toString();
    const url = `${API_BASE_URL}/comments/stats${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch comment stats: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get comments by user
   */
  static async getUserComments(
    userId: number,
    options: { objectType?: string; limit?: number; offset?: number } = {}
  ): Promise<CommentsResponse> {
    const params = new URLSearchParams();
    if (options.objectType) params.append('objectType', options.objectType);
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.offset) params.append('offset', options.offset.toString());

    const queryString = params.toString();
    const url = `${API_BASE_URL}/comments/user/${userId}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user comments: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Handle typing indicators
   */
  static async sendTypingIndicator(
    objectType: string,
    objectId: number,
    isTyping: boolean
  ): Promise<{ success: boolean; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/comments/typing/${objectType}/${objectId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ isTyping })
    });

    if (!response.ok) {
      throw new Error(`Failed to send typing indicator: ${response.statusText}`);
    }

    return response.json();
  }
}

// Export a default instance for easier importing
export const commentsApi = CommentsAPI;