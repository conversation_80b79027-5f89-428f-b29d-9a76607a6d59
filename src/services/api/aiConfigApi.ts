import { AI<PERSON>rovider, AIProviderConfig } from '../ai/types';
import { SecureStorage } from '../storage/SecureStorage';
import { APIKeyValidationService } from '../ai/APIKeyValidationService';

// Use relative URL for API calls - the dev server will proxy to the backend
const API_BASE_URL = '/api';

interface BackendConfigResponse {
  id: number;
  provider: AIProvider;
  model: string;
  maxTokens: number;
  temperature: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface BackendApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}

interface BackendConfigWithApiKey {
  id: number;
  provider: AIProvider;
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * API service for AI configuration management
 * Integrates with backend API for persistent storage and synchronization
 */
export class AIConfigAPI {
  /**
   * Save AI provider configuration
   */
  static async saveConfiguration(config: AIProviderConfig): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Validate the configuration first
      const validation = await APIKeyValidationService.validateApiKey(
        config.provider,
        config.apiKey,
        { testConnection: true }
      );

      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error || 'Invalid configuration'
        };
      }

      // Store the configuration securely
      await SecureStorage.storeAIConfiguration(config.provider, config);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save configuration'
      };
    }
  }

  /**
   * Get AI provider configuration
   */
  static async getConfiguration(provider: AIProvider): Promise<{
    success: boolean;
    data?: AIProviderConfig;
    error?: string;
  }> {
    try {
      const config = await SecureStorage.getAIConfiguration(provider);
      
      if (!config) {
        return {
          success: false,
          error: 'Configuration not found'
        };
      }

      return {
        success: true,
        data: config
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to retrieve configuration'
      };
    }
  }

  /**
   * Get all configurations
   */
  static async getAllConfigurations(): Promise<{
    success: boolean;
    data?: Record<AIProvider, AIProviderConfig | null>;
    error?: string;
  }> {
    try {
      // Try to get from backend API first
      const token = localStorage.getItem('token');
      if (token) {
        try {
          const response = await fetch(`${API_BASE_URL}/ai/configurations`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (response.ok) {
            const result: BackendApiResponse<BackendConfigResponse[]> = await response.json();
            const configurations: Record<AIProvider, AIProviderConfig | null> = {
              openai: null,
              anthropic: null,
              gemini: null
            };

            result.data.forEach((config: BackendConfigResponse) => {
              configurations[config.provider] = {
                provider: config.provider,
                apiKey: '', // Don't expose API key in list view
                maxTokens: config.maxTokens,
                temperature: config.temperature
              };
            });

            return {
              success: true,
              data: configurations
            };
          }
        } catch (apiError) {
          console.warn('Backend API unavailable, falling back to local storage');
        }
      }

      // Fallback to local storage
      const configurations = await SecureStorage.getAIConfigurations();
      
      // Decrypt all configurations
      const decryptedConfigs: Record<AIProvider, AIProviderConfig | null> = {
        openai: null,
        anthropic: null,
        gemini: null
      };

      for (const provider of Object.keys(configurations) as AIProvider[]) {
        if (configurations[provider]) {
          decryptedConfigs[provider] = await SecureStorage.getAIConfiguration(provider);
        }
      }

      return {
        success: true,
        data: decryptedConfigs
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to retrieve configurations'
      };
    }
  }

  /**
   * Delete AI provider configuration
   */
  static async deleteConfiguration(provider: AIProvider): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      await SecureStorage.removeAIConfiguration(provider);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete configuration'
      };
    }
  }

  /**
   * Test AI provider configuration
   */
  static async testConfiguration(provider: AIProvider): Promise<{
    success: boolean;
    details?: {
      formatValid: boolean;
      connectionValid: boolean;
      quotaInfo?: unknown;
    };
    error?: string;
  }> {
    try {
      const config = await SecureStorage.getAIConfiguration(provider);
      
      if (!config) {
        return {
          success: false,
          error: 'Configuration not found'
        };
      }

      const validation = await APIKeyValidationService.validateApiKey(
        provider,
        config.apiKey,
        { testConnection: true, checkQuota: true, skipCache: true }
      );

      return {
        success: validation.isValid,
        details: validation.details,
        error: validation.error
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to test configuration'
      };
    }
  }

  /**
   * Get current active provider
   */
  static getCurrentProvider(): AIProvider | null {
    return SecureStorage.getCurrentProvider();
  }

  /**
   * Set current active provider
   */
  static async setCurrentProvider(provider: AIProvider | null): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      if (provider) {
        // Verify the provider is configured
        const isConfigured = await SecureStorage.isProviderConfigured(provider);
        if (!isConfigured) {
          return {
            success: false,
            error: `Provider ${provider} is not configured`
          };
        }
      }

      SecureStorage.setCurrentProvider(provider);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to set current provider'
      };
    }
  }

  /**
   * Get provider status information
   */
  static async getProviderStatus(provider: AIProvider): Promise<{
    configured: boolean;
    available: boolean;
    lastTested?: Date;
    error?: string;
  }> {
    try {
      const isConfigured = await SecureStorage.isProviderConfigured(provider);
      
      if (!isConfigured) {
        return {
          configured: false,
          available: false
        };
      }

      // Test the configuration
      const testResult = await this.testConfiguration(provider);
      
      return {
        configured: true,
        available: testResult.success,
        lastTested: new Date(),
        error: testResult.error
      };
    } catch (error) {
      return {
        configured: false,
        available: false,
        error: error instanceof Error ? error.message : 'Failed to get provider status'
      };
    }
  }

  /**
   * Get all provider statuses
   */
  static async getAllProviderStatuses(): Promise<Record<AIProvider, {
    configured: boolean;
    available: boolean;
    lastTested?: Date;
    error?: string;
  }>> {
    const providers: AIProvider[] = ['openai', 'anthropic', 'gemini'];
    const statuses: Record<string, {
      configured: boolean;
      available: boolean;
      lastTested?: Date;
      error?: string;
    }> = {};

    await Promise.all(
      providers.map(async (provider) => {
        statuses[provider] = await this.getProviderStatus(provider);
      })
    );

    return statuses as Record<AIProvider, {
      configured: boolean;
      available: boolean;
      lastTested?: Date;
      error?: string;
    }>;
  }

  /**
   * Export configurations for backup
   */
  static async exportConfigurations(): Promise<{
    success: boolean;
    data?: string;
    error?: string;
  }> {
    try {
      // Try to export from backend API first
      const token = localStorage.getItem('token');
      if (token) {
        try {
          const response = await fetch(`${API_BASE_URL}/ai/export`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (response.ok) {
            const result: BackendApiResponse<{ backupData: string }> = await response.json();
            return {
              success: true,
              data: result.data.backupData
            };
          }
        } catch (apiError) {
          console.warn('Backend API unavailable for export, falling back to local storage');
        }
      }

      // Fallback to local storage export
      const configurations = await SecureStorage.getAIConfigurations();
      const exportData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        configurations: Object.entries(configurations)
          .filter(([, config]) => config !== null)
          .map(([provider, config]) => ({
            provider,
            ...config
          }))
      };

      return {
        success: true,
        data: JSON.stringify(exportData, null, 2)
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to export configurations'
      };
    }
  }

  /**
   * Import configurations from backup
   */
  static async importConfigurations(backupData: string): Promise<{
    success: boolean;
    imported?: number;
    error?: string;
  }> {
    try {
      const data = JSON.parse(backupData);
      
      if (!data.configurations || !Array.isArray(data.configurations)) {
        return {
          success: false,
          error: 'Invalid backup data format'
        };
      }

      // Try to import via backend API first
      const token = localStorage.getItem('token');
      if (token) {
        try {
          const response = await fetch(`${API_BASE_URL}/ai/import`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ backupData: data })
          });

          if (response.ok) {
            const result: BackendApiResponse<{ imported: number }> = await response.json();
            return {
              success: true,
              imported: result.data.imported
            };
          }
        } catch (apiError) {
          console.warn('Backend API unavailable for import, falling back to local storage');
        }
      }

      // Fallback to local storage import
      let imported = 0;
      for (const configData of data.configurations) {
        if (configData.provider && configData.apiKey) {
          const config: AIProviderConfig = {
            provider: configData.provider,
            apiKey: configData.apiKey,
            maxTokens: configData.maxTokens || 4000,
            temperature: configData.temperature || 0.7
          };
          
          await SecureStorage.storeAIConfiguration(config.provider, config);
          imported++;
        }
      }

      return {
        success: true,
        imported
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to import configurations'
      };
    }
  }

  /**
   * Rotate API key for a provider
   */
  static async rotateApiKey(provider: AIProvider, newApiKey: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const currentConfig = await SecureStorage.getAIConfiguration(provider);
      
      if (!currentConfig) {
        return {
          success: false,
          error: 'Provider not configured'
        };
      }

      // Test key rotation
      const rotationTest = await APIKeyValidationService.testKeyRotation(
        provider,
        currentConfig.apiKey,
        newApiKey
      );

      if (!rotationTest.canRotate) {
        return {
          success: false,
          error: rotationTest.error || 'New API key is invalid'
        };
      }

      // Update configuration with new key
      const updatedConfig = {
        ...currentConfig,
        apiKey: newApiKey
      };

      await SecureStorage.storeAIConfiguration(provider, updatedConfig);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to rotate API key'
      };
    }
  }

  /**
   * Clear all configurations (for logout/reset)
   */
  static async clearAllConfigurations(): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      await SecureStorage.clearAll();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to clear configurations'
      };
    }
  }

  /**
   * Get storage information
   */
  static getStorageInfo(): {
    totalKeys: number;
    secureKeys: number;
    estimatedSize: number;
  } {
    return SecureStorage.getStorageInfo();
  }
}