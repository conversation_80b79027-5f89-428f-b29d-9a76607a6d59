import {
  RFQ,
  RFQSubmission,
  RFQInvitation,
  BidItem,
  RFQItem,
  FormField,
  AISettings,
  SubmissionAttachment,
  BidAlternative,
  RFQAnalytics,
} from "./rfq";

/**
 * Transform utilities for RFQ API responses
 * Ensures consistent data structure and handles any backend format differences
 */

export class RFQTransforms {
  // Transform RFQ data from API response
  static transformRFQ(apiRFQ: Record<string, unknown>): RFQ {
    return {
      id: typeof apiRFQ.id === 'number' ? apiRFQ.id : (typeof apiRFQ.id === 'string' ? parseInt(apiRFQ.id) || 0 : 0),
      title: (apiRFQ.title as string) || "",
      description: (apiRFQ.description as string) || "",
      status: (apiRFQ.status as string) || "draft",
      items: RFQTransforms.transformRFQItems(apiRFQ.items),
      due_date: apiRFQ.due_date as string,
      currency: (apiRFQ.currency as string) || "USD",
      creator_id: typeof apiRFQ.creator_id === 'number' ? apiRFQ.creator_id : (typeof apiRFQ.creator_id === 'string' ? parseInt(apiRFQ.creator_id) || 0 : 0),
      creator_email: (apiRFQ.creator_email as string) || "",
      form_config: RFQTransforms.transformFormConfig(apiRFQ.form_config),
      ai_settings: RFQTransforms.transformAISettings(apiRFQ.ai_settings),
      terms: (apiRFQ.terms as string) || "",

      // Enhanced partial selection support
      allow_partial_selection: Boolean(apiRFQ.allow_partial_selection),
      partial_selection_config: (apiRFQ.partial_selection_config as Record<string, unknown>) || {
        enabled: false,
        requireVendorConfirmation: true,
        confirmationMessage:
          "Do you allow individual item purchases at the quoted rates?",
        instructions:
          "Please confirm if you allow partial selection of items from your submission.",
        defaultAllowed: false,
      },

      // Statistics
      invitation_count: parseInt((apiRFQ.invitation_count as string) || "0") || 0,
      submission_count: parseInt((apiRFQ.submission_count as string) || "0") || 0,
      response_rate: parseFloat((apiRFQ.response_rate as string) || "0") || 0,
      item_count: apiRFQ.items
        ? Array.isArray(apiRFQ.items)
          ? apiRFQ.items.length
          : Object.keys(apiRFQ.items as Record<string, unknown>).length
        : 0,
      viewed_invitations: parseInt((apiRFQ.viewed_invitations as string) || "0") || 0,
      view_rate: parseFloat((apiRFQ.view_rate as string) || "0") || 0,
      partial_selection_submissions:
        parseInt((apiRFQ.partial_selection_submissions as string) || "0") || 0,

      // Lifecycle tracking
      created_at: apiRFQ.created_at as string,
      updated_at: apiRFQ.updated_at as string,
      sent_at: (apiRFQ.sent_at as string) || undefined,
      closed_at: (apiRFQ.closed_at as string) || undefined,

      // Related data (if included)
      invitations: apiRFQ.invitations
        ? (apiRFQ.invitations as Record<string, unknown>[]).map(RFQTransforms.transformInvitation)
        : undefined,
      submissions: apiRFQ.submissions
        ? (apiRFQ.submissions as Record<string, unknown>[]).map(RFQTransforms.transformSubmission)
        : undefined,
      analytics: apiRFQ.analytics
        ? RFQTransforms.transformAnalytics(apiRFQ.analytics as Record<string, unknown>)
        : undefined,
    };
  }

  // Transform RFQ items (handle both array and object formats)
  static transformRFQItems(items: unknown): RFQItem[] {
    if (!items) return [];

    if (Array.isArray(items)) {
      return items.map((item: Record<string, unknown>) => ({
        id:
          (typeof item.id === 'string' ? item.id : null) ||
          (typeof item.item_id === 'string' ? item.item_id : null) ||
          `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: typeof item.name === 'string' ? item.name : "",
        description: typeof item.description === 'string' ? item.description : "",
        quantity: typeof item.quantity === 'number' ? item.quantity : (typeof item.quantity === 'string' ? parseInt(item.quantity) || 1 : 1),
        specifications: typeof item.specifications === 'object' && item.specifications !== null ? item.specifications as Record<string, unknown> : {},
        category: typeof item.category === 'string' ? item.category : "",
        estimatedPrice: typeof item.estimatedPrice === 'number' ? item.estimatedPrice : (typeof item.estimatedPrice === 'string' ? parseFloat(item.estimatedPrice) || undefined : undefined),
        customFields: typeof item.customFields === 'object' && item.customFields !== null ? item.customFields as Record<string, unknown> : {},
      }));
    }

    // Handle object format (legacy support)
    if (typeof items === "object") {
      return Object.entries(items as Record<string, unknown>).map(([key, item]: [string, unknown]) => {
        const itemObj = item as Record<string, unknown>;
        return {
          id: typeof itemObj.id === 'string' ? itemObj.id : key,
          name: typeof itemObj.name === 'string' ? itemObj.name : "",
          description: typeof itemObj.description === 'string' ? itemObj.description : "",
          quantity: typeof itemObj.quantity === 'number' ? itemObj.quantity : (typeof itemObj.quantity === 'string' ? parseInt(itemObj.quantity) || 1 : 1),
          specifications: typeof itemObj.specifications === 'object' && itemObj.specifications !== null ? itemObj.specifications as Record<string, unknown> : {},
          category: typeof itemObj.category === 'string' ? itemObj.category : "",
          estimatedPrice: typeof itemObj.estimatedPrice === 'number' ? itemObj.estimatedPrice : (typeof itemObj.estimatedPrice === 'string' ? parseFloat(itemObj.estimatedPrice) || undefined : undefined),
          customFields: typeof itemObj.customFields === 'object' && itemObj.customFields !== null ? itemObj.customFields as Record<string, unknown> : {},
        };
      });
    }

    return [];
  }

  // Transform form configuration
  static transformFormConfig(formConfig: unknown): FormField[] {
    if (!formConfig) return [];

    if (Array.isArray(formConfig)) {
      return formConfig.map((field) => ({
        id:
          field.id ||
          `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: field.type || "text",
        label: field.label || "",
        required: Boolean(field.required),
        options: field.options || [],
        validation: field.validation || [],
        itemSpecific: Boolean(field.itemSpecific),
      }));
    }

    return [];
  }

  // Transform AI settings
  static transformAISettings(aiSettings: unknown): AISettings {
    if (!aiSettings || typeof aiSettings !== 'object') {
      return {
        priceWeight: 0.4,
        performanceWeight: 0.3,
        deliveryWeight: 0.2,
        riskWeight: 0.1,
        diversificationPreference: 0.5,
      };
    }

    const settings = aiSettings as Record<string, unknown>;
    return {
      priceWeight: typeof settings.priceWeight === 'number' ? settings.priceWeight : (typeof settings.priceWeight === 'string' ? parseFloat(settings.priceWeight) || 0.4 : 0.4),
      performanceWeight: typeof settings.performanceWeight === 'number' ? settings.performanceWeight : (typeof settings.performanceWeight === 'string' ? parseFloat(settings.performanceWeight) || 0.3 : 0.3),
      deliveryWeight: typeof settings.deliveryWeight === 'number' ? settings.deliveryWeight : (typeof settings.deliveryWeight === 'string' ? parseFloat(settings.deliveryWeight) || 0.2 : 0.2),
      riskWeight: typeof settings.riskWeight === 'number' ? settings.riskWeight : (typeof settings.riskWeight === 'string' ? parseFloat(settings.riskWeight) || 0.1 : 0.1),
      diversificationPreference: typeof settings.diversificationPreference === 'number' ? settings.diversificationPreference : (typeof settings.diversificationPreference === 'string' ? parseFloat(settings.diversificationPreference) || 0.5 : 0.5),
    };
  }

  // Transform invitation data
  static transformInvitation(
    apiInvitation: Record<string, unknown>
  ): RFQInvitation {
    return {
      id: typeof apiInvitation.id === 'number' ? apiInvitation.id : (typeof apiInvitation.id === 'string' ? parseInt(apiInvitation.id) || 0 : 0),
      rfq_id: typeof apiInvitation.rfq_id === 'number' ? apiInvitation.rfq_id : (typeof apiInvitation.rfq_id === 'string' ? parseInt(apiInvitation.rfq_id) || 0 : 0),
      vendor_id: typeof apiInvitation.vendor_id === 'number' ? apiInvitation.vendor_id : (typeof apiInvitation.vendor_id === 'string' ? parseInt(apiInvitation.vendor_id) || 0 : 0),
      vendor_name: (apiInvitation.vendor_name as string) || "",
      vendor_email: (apiInvitation.vendor_email as string) || "",
      vendor_performance_score:
        parseFloat((apiInvitation.vendor_performance_score as string) || "0") || 0,
      status: ['sent', 'viewed', 'submitted'].includes(apiInvitation.status as string) 
         ? (apiInvitation.status as 'sent' | 'viewed' | 'submitted') 
         : 'sent',
      token: (apiInvitation.token as string) || "",
      sent_at: (apiInvitation.sent_at as string) || undefined,
      viewed_at: (apiInvitation.viewed_at as string) || undefined,
      submitted_at: (apiInvitation.submitted_at as string) || undefined,
      expires_at: apiInvitation.expires_at as string,
      created_at: apiInvitation.created_at as string,

      // Enhanced tracking
      reminder_count: parseInt((apiInvitation.reminder_count as string) || "0") || 0,
      last_reminded_at: (apiInvitation.last_reminded_at as string) || undefined,
      engagement_score: parseFloat((apiInvitation.engagement_score as string) || "0") || 0,
    };
  }

  // Transform submission data
  static transformSubmission(
    apiSubmission: Record<string, unknown>
  ): RFQSubmission {
    return {
      id: typeof apiSubmission.id === 'number' ? apiSubmission.id : (typeof apiSubmission.id === 'string' ? parseInt(apiSubmission.id) || 0 : 0),
      rfq_id: typeof apiSubmission.rfq_id === 'number' ? apiSubmission.rfq_id : (typeof apiSubmission.rfq_id === 'string' ? parseInt(apiSubmission.rfq_id) || 0 : 0),
      vendor_id: typeof apiSubmission.vendor_id === 'number' ? apiSubmission.vendor_id : (typeof apiSubmission.vendor_id === 'string' ? parseInt(apiSubmission.vendor_id) || 0 : 0),
      vendor_name: (apiSubmission.vendor_name as string) || "",
      vendor_email: (apiSubmission.vendor_email as string) || "",
      vendor_performance_score:
        parseFloat((apiSubmission.vendor_performance_score as string) || "0") || 0,
      vendor_category: (apiSubmission.vendor_category as string) || "",
      invitation_token: (apiSubmission.invitation_token as string) || "",
      bid_data: RFQTransforms.transformBidData(apiSubmission.bid_data),
      attachments: RFQTransforms.transformAttachments(
        apiSubmission.attachments
      ),
      total_amount: parseFloat((apiSubmission.total_amount as string) || "0") || 0,
      currency: (apiSubmission.currency as string) || "USD",
      delivery_days: apiSubmission.delivery_days
        ? parseInt((apiSubmission.delivery_days as string))
        : undefined,
      payment_terms: (apiSubmission.payment_terms as string) || "",
      validity_period: parseInt((apiSubmission.validity_period as string) || "30") || 30,
      additional_notes: (apiSubmission.additional_notes as string) || "",
      submitted_at: apiSubmission.submitted_at as string,
      updated_at: apiSubmission.updated_at as string,
      bid_items: apiSubmission.bid_items
        ? (apiSubmission.bid_items as Record<string, unknown>[]).map(RFQTransforms.transformBidItem)
        : [],
      invitation_sent_at: (apiSubmission.invitation_sent_at as string) || undefined,
      invitation_viewed_at: (apiSubmission.invitation_viewed_at as string) || undefined,

      // Enhanced partial selection support
      allows_partial_selection: Boolean(apiSubmission.allows_partial_selection),
      partial_selection_notes:
        (apiSubmission.partial_selection_notes as string) || undefined,
      viewed_at: (apiSubmission.viewed_at as string) || undefined,
      engagement_score: parseFloat((apiSubmission.engagement_score as string) || "0") || 0,
      response_time: parseFloat((apiSubmission.response_time as string) || "0") || 0,
    };
  }

  // Transform bid data
  static transformBidData(bidData: unknown): Record<string, unknown> {
    if (!bidData) return {};

    if (typeof bidData === "string") {
      try {
        return JSON.parse(bidData) as Record<string, unknown>;
      } catch {
        return {};
      }
    }

    if (typeof bidData === 'object') {
      const data = bidData as Record<string, unknown>;
      return {
        generalTerms: typeof data.generalTerms === 'object' && data.generalTerms !== null ? data.generalTerms : {},
        deliverySchedule: typeof data.deliverySchedule === 'object' && data.deliverySchedule !== null ? data.deliverySchedule : {},
        paymentTerms: typeof data.paymentTerms === 'string' ? data.paymentTerms : "",
        validityPeriod: typeof data.validityPeriod === 'number' ? data.validityPeriod : (typeof data.validityPeriod === 'string' ? parseInt(data.validityPeriod) || 30 : 30),
        additionalNotes: typeof data.additionalNotes === 'string' ? data.additionalNotes : "",
      };
    }

    return {};
  }

  // Transform attachments
  static transformAttachments(attachments: unknown): SubmissionAttachment[] {
    if (!attachments) return [];

    if (typeof attachments === "string") {
      try {
        attachments = JSON.parse(attachments);
      } catch {
        return [];
      }
    }

    if (Array.isArray(attachments)) {
      return attachments.map((attachment) => ({
        filename: attachment.filename || "",
        originalName: attachment.originalName || attachment.original_name || "",
        fileSize: parseInt(attachment.fileSize || attachment.file_size) || 0,
        mimeType: attachment.mimeType || attachment.mime_type || "",
        filePath: attachment.filePath || attachment.file_path || "",
        uploadedAt:
          attachment.uploadedAt ||
          attachment.uploaded_at ||
          new Date().toISOString(),
      }));
    }

    return [];
  }

  // Transform bid item
  static transformBidItem(apiBidItem: Record<string, unknown>): BidItem {
    return {
      id: typeof apiBidItem.id === 'number' ? apiBidItem.id : (typeof apiBidItem.id === 'string' ? parseInt(apiBidItem.id) || 0 : 0),
      submission_id: typeof apiBidItem.submission_id === 'number' ? apiBidItem.submission_id : (typeof apiBidItem.submission_id === 'string' ? parseInt(apiBidItem.submission_id) || 0 : 0),
      rfq_item_id: (apiBidItem.rfq_item_id as string) || "",
      item_name: (apiBidItem.item_name as string) || "",
      unit_price: parseFloat((apiBidItem.unit_price as string) || "0") || 0,
      quantity: parseInt((apiBidItem.quantity as string) || "1") || 1,
      total_price: parseFloat((apiBidItem.total_price as string) || "0") || 0,
      delivery_days: apiBidItem.delivery_days
        ? parseInt((apiBidItem.delivery_days as string))
        : undefined,
      specifications: RFQTransforms.transformSpecifications(
        apiBidItem.specifications
      ),
      alternatives: RFQTransforms.transformAlternatives(
        apiBidItem.alternatives
      ),
      notes: (apiBidItem.notes as string) || "",
    };
  }

  // Transform specifications
  static transformSpecifications(specifications: unknown): Record<string, unknown> {
    if (!specifications) return {};

    if (typeof specifications === "string") {
      try {
        return JSON.parse(specifications) as Record<string, unknown>;
      } catch {
        return {};
      }
    }

    if (typeof specifications === 'object' && specifications !== null) {
      return specifications as Record<string, unknown>;
    }

    return {};
  }

  // Transform alternatives
  static transformAlternatives(alternatives: unknown): BidAlternative[] {
    if (!alternatives) return [];

    if (typeof alternatives === "string") {
      try {
        alternatives = JSON.parse(alternatives);
      } catch {
        return [];
      }
    }

    if (Array.isArray(alternatives)) {
      return alternatives.map((alt) => ({
        description: alt.description || "",
        unitPrice: parseFloat(alt.unitPrice || alt.unit_price) || 0,
        totalPrice: parseFloat(alt.totalPrice || alt.total_price) || 0,
        notes: alt.notes || "",
      }));
    }

    return [];
  }

  // Transform analytics data
  static transformAnalytics(
    apiAnalytics: Record<string, unknown>
  ): RFQAnalytics {
    if (!apiAnalytics) return {} as RFQAnalytics;

    return {
      rfq_id: typeof apiAnalytics.rfq_id === 'number' ? apiAnalytics.rfq_id : (typeof apiAnalytics.rfq_id === 'string' ? parseInt(apiAnalytics.rfq_id) || 0 : 0),
      total_invitations: typeof apiAnalytics.total_invitations === 'number' ? apiAnalytics.total_invitations : (typeof apiAnalytics.total_invitations === 'string' ? parseInt(apiAnalytics.total_invitations) || 0 : 0),
      total_submissions: typeof apiAnalytics.total_submissions === 'number' ? apiAnalytics.total_submissions : (typeof apiAnalytics.total_submissions === 'string' ? parseInt(apiAnalytics.total_submissions) || 0 : 0),
      response_rate: typeof apiAnalytics.response_rate === 'number' ? apiAnalytics.response_rate : (typeof apiAnalytics.response_rate === 'string' ? parseFloat(apiAnalytics.response_rate) || 0 : 0),
      average_response_time_hours: typeof apiAnalytics.average_response_time_hours === 'number' ? apiAnalytics.average_response_time_hours : (typeof apiAnalytics.average_response_time_hours === 'string' ? parseFloat(apiAnalytics.average_response_time_hours) || undefined : undefined),
      lowest_bid_amount: typeof apiAnalytics.lowest_bid_amount === 'number' ? apiAnalytics.lowest_bid_amount : (typeof apiAnalytics.lowest_bid_amount === 'string' ? parseFloat(apiAnalytics.lowest_bid_amount) || undefined : undefined),
      highest_bid_amount: typeof apiAnalytics.highest_bid_amount === 'number' ? apiAnalytics.highest_bid_amount : (typeof apiAnalytics.highest_bid_amount === 'string' ? parseFloat(apiAnalytics.highest_bid_amount) || undefined : undefined),
      average_bid_amount: typeof apiAnalytics.average_bid_amount === 'number' ? apiAnalytics.average_bid_amount : (typeof apiAnalytics.average_bid_amount === 'string' ? parseFloat(apiAnalytics.average_bid_amount) || undefined : undefined),
      calculated_at: apiAnalytics.calculated_at as string,
    };
  }

  // Transform paginated response
  static transformPaginatedResponse<T>(
    apiResponse: Record<string, unknown>,
    transformFn: (item: Record<string, unknown>) => T
  ): { data: T[]; pagination: Record<string, unknown> } {
    const dataArray = Array.isArray(apiResponse.rfqs) ? apiResponse.rfqs : (Array.isArray(apiResponse.data) ? apiResponse.data : []);
    const paginationData = typeof apiResponse.pagination === 'object' && apiResponse.pagination !== null ? apiResponse.pagination as Record<string, unknown> : {};
    
    return {
      data: (dataArray as Record<string, unknown>[]).map(transformFn),
      pagination: {
        page: typeof paginationData.page === 'number' ? paginationData.page : (typeof paginationData.page === 'string' ? parseInt(paginationData.page) || 1 : 1),
        limit: typeof paginationData.limit === 'number' ? paginationData.limit : (typeof paginationData.limit === 'string' ? parseInt(paginationData.limit) || 10 : 10),
        total: typeof paginationData.total === 'number' ? paginationData.total : (typeof paginationData.total === 'string' ? parseInt(paginationData.total) || 0 : 0),
        totalPages: typeof paginationData.totalPages === 'number' ? paginationData.totalPages : (typeof paginationData.totalPages === 'string' ? parseInt(paginationData.totalPages) || 0 : 0),
      },
    };
  }

  // Transform for API request (reverse transformation)
  static transformForAPI(
    rfqData: Record<string, unknown>
  ): Record<string, unknown> {
    return {
      title: rfqData.title,
      description: rfqData.description || "",
      items: rfqData.items || [],
      due_date: rfqData.due_date,
      selectedVendors: rfqData.selectedVendors || [],
      formConfig: rfqData.formConfig || [],
      terms: rfqData.terms || "",
      currency: rfqData.currency || "USD",
      aiSettings: rfqData.aiSettings || {
        priceWeight: 0.4,
        performanceWeight: 0.3,
        deliveryWeight: 0.2,
        riskWeight: 0.1,
        diversificationPreference: 0.5,
      },
      // Include partial selection fields
      allow_partial_selection: rfqData.allowPartialSelection || false,
      partial_selection_config: rfqData.partialSelectionConfig || {
        enabled: false,
        requireVendorConfirmation: true,
        confirmationMessage: "Do you allow individual item purchases at the quoted rates?",
        instructions: "Please confirm if you allow partial selection of items from your submission.",
        defaultAllowed: false,
      },
    };
  }

  // Transform for API update request (excludes selectedVendors)
  static transformForUpdateAPI(
    rfqData: Record<string, unknown>
  ): Record<string, unknown> {
    const transformed: Record<string, unknown> = {};

    // Only include fields that are allowed for updates
    if (rfqData.title !== undefined) transformed.title = rfqData.title;
    if (rfqData.description !== undefined)
      transformed.description = rfqData.description || "";
    if (rfqData.items !== undefined) transformed.items = rfqData.items || [];
    if (rfqData.due_date !== undefined) transformed.due_date = rfqData.due_date;
    if (rfqData.formConfig !== undefined)
      transformed.formConfig = rfqData.formConfig || [];
    if (rfqData.terms !== undefined) transformed.terms = rfqData.terms || "";
    if (rfqData.currency !== undefined)
      transformed.currency = rfqData.currency || "USD";
    if (rfqData.status !== undefined) transformed.status = rfqData.status;
    // Include partial selection fields for updates
    if (rfqData.allowPartialSelection !== undefined)
      transformed.allow_partial_selection = rfqData.allowPartialSelection;
    if (rfqData.partialSelectionConfig !== undefined)
      transformed.partial_selection_config = rfqData.partialSelectionConfig;

    if (rfqData.aiSettings !== undefined) {
      transformed.aiSettings = rfqData.aiSettings || {
        priceWeight: 0.4,
        performanceWeight: 0.3,
        deliveryWeight: 0.2,
        riskWeight: 0.1,
        diversificationPreference: 0.5,
      };
    }

    return transformed;
  }

  // Validate transformed data
  static validateRFQ(rfq: RFQ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!rfq.title || rfq.title.trim().length === 0) {
      errors.push("Title is required");
    }

    if (!rfq.items || rfq.items.length === 0) {
      errors.push("At least one item is required");
    }

    if (!rfq.due_date || new Date(rfq.due_date) <= new Date()) {
      errors.push("Due date must be in the future");
    }

    if (rfq.invitation_count === 0) {
      errors.push("At least one vendor invitation is required");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

export default RFQTransforms;
