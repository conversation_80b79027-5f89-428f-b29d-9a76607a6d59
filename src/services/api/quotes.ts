import { apiClient, ApiResponse, PaginatedResponse } from '../api';

export interface SelectedBid {
  submissionId: number;
  vendorName: string;
  itemName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  currency: string;
}

// Enhanced interfaces for advanced quote generation
export interface QuoteSelectionItem {
  submissionId: number;
  bidItemId?: number; // null for whole submission
  vendorId: number;
  vendorName: string;
  rfqItemId?: string;
  itemName?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  allowsPartialSelection: boolean;
  commissionRate?: number; // override global rate
  isWholeSubmission: boolean;
  selectionRationale?: string;
}

export interface QuoteConfiguration {
  accountId?: number;
  opportunityId?: number;
  clientName: string;
  clientEmail: string;
  globalCommissionRate: number;
  itemCommissionOverrides: Record<string, number>;
  terms: string;
  notes: string;
  expiryDate?: string;
}

export interface QuotePricing {
  subtotal: number;
  totalCommission: number;
  totalMargin: number;
  finalAmount: number;
  itemBreakdown: QuoteItemPricing[];
}

export interface QuoteItemPricing {
  itemId: string;
  cost: number;
  commissionRate: number;
  commission: number;
  finalPrice: number;
}

export interface RFQItem {
  id: number;
  name: string;
  description: string;
  quantity: number;
  specifications?: Record<string, unknown>;
}

export interface VendorDetail {
  id: number;
  name: string;
  email: string;
  contact_person?: string;
}

export interface AuditEntry {
  id: number;
  action: string;
  entity_type: string;
  entity_id: number;
  user_id: number;
  old_values?: Record<string, unknown>;
  new_values?: Record<string, unknown>;
  created_at: string;
}

export interface SubmissionsData {
  submissions: Submission[];
  rfq?: {
    id: number;
    title: string;
    client_name?: string;
    client_email?: string;
  };
}

export interface Submission {
  id: number;
  vendor_id: number;
  vendor_name: string;
  vendor_email: string;
  item_id: number;
  item_name: string;
  item_description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  currency: string;
  delivery_time: string;
  notes?: string;
  status: string;
}

export interface QuoteGenerationData {
  selectedBids: Record<string, SelectedBid>;
  title: string;
  client_name: string;
  client_email: string;
  currency: string;
  margin_percentage: number;
  taxes: number;
  expires_in_days: number;
  terms?: string;
  notes?: string;
}

export interface Quote {
  id: number;
  rfq_id: number;
  creator_id: number;
  title: string;
  selected_bids: Record<string, SelectedBid>;
  total_amount: number;
  currency: string;
  margin_percentage: number;
  taxes: number;
  terms?: string;
  notes?: string;
  client_email: string;
  client_name: string;
  status: 'draft' | 'sent' | 'viewed' | 'approved' | 'rejected';
  expires_at: string;
  created_at: string;
  updated_at: string;
  sent_at?: string;
  viewed_at?: string;
  approved_at?: string;
  approved_by_name?: string;
  approved_by_email?: string;
  approval_signature?: string;
  feedback?: string;
  public_token: string;
  
  // Enhanced fields for advanced quote generation
  account_id?: number;
  opportunity_id?: number;
  account_name?: string;
  opportunity_name?: string;
  global_commission_rate: number;
  item_commission_overrides: Record<string, number>;
  total_cost: number;
  total_margin: number;
  selections?: QuoteSelectionItem[];
  pricing_breakdown?: QuotePricing;
  
  // Additional fields from joins
  creator_email?: string;
  rfq_title?: string;
  rfq_description?: string;
  rfq_items?: RFQItem[];
  rfq_due_date?: string;
  rfq_terms?: string;
  is_expired?: boolean;
  vendor_details?: Record<string, VendorDetail>;
}

export interface QuoteFilters {
  search?: string;
  status?: string;
  creator_id?: number;
  rfq_id?: number;
  client_email?: string;
  expires_from?: string;
  expires_to?: string;
}

export interface QuoteCreateData {
  rfq_id: number;
  title: string;
  selectedBids: Record<string, SelectedBid>;
  currency: string;
  margin_percentage: number;
  taxes: number;
  terms?: string;
  notes?: string;
  client_email: string;
  client_name: string;
  expires_in_days: number;
}

export interface QuoteUpdateData {
  title?: string;
  selectedBids?: Record<string, SelectedBid>;
  currency?: string;
  margin_percentage?: number;
  taxes?: number;
  terms?: string;
  notes?: string;
  client_email?: string;
  client_name?: string;
  expires_in_days?: number;
}

// Enhanced interfaces for advanced quote generation
export interface AdvancedQuoteCreateData {
  rfq_id: number;
  title: string;
  selections: QuoteSelectionItem[];
  config: QuoteConfiguration;
  currency: string;
  taxes?: number;
  expires_in_days: number;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface QuoteStatistics {
  total_quotes: number;
  draft_quotes: number;
  sent_quotes: number;
  approved_quotes: number;
  rejected_quotes: number;
  expired_quotes: number;
  total_value: number;
  average_value: number;
  approval_rate: number;
  avg_approval_time_days?: number;
}

export interface QuoteApprovalData {
  approved_by_name: string;
  approved_by_email: string;
  approval_signature?: string;
  feedback?: string;
}

export class QuoteApiService {
  // Get all quotes with filtering and pagination
  static async getQuotes(
    page: number = 1,
    limit: number = 10,
    filters: QuoteFilters = {}
  ): Promise<PaginatedResponse<Quote>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    // Add filters to params
    if (filters.search) params.append('search', filters.search);
    if (filters.status) params.append('status', filters.status);
    if (filters.creator_id) params.append('creator_id', filters.creator_id.toString());
    if (filters.rfq_id) params.append('rfq_id', filters.rfq_id.toString());
    if (filters.client_email) params.append('client_email', filters.client_email);
    if (filters.expires_from) params.append('expires_from', filters.expires_from);
    if (filters.expires_to) params.append('expires_to', filters.expires_to);

    const response = await apiClient.get(`/quotes?${params.toString()}`);
    return response.data;
  }

  // Get quote by ID
  static async getQuoteById(id: number): Promise<ApiResponse<Quote>> {
    const response = await apiClient.get(`/quotes/${id}`);
    return response.data;
  }

  // Get quotes for a specific RFQ
  static async getRFQQuotes(rfqId: number): Promise<ApiResponse<Quote[]>> {
    const response = await apiClient.get(`/quotes?rfq_id=${rfqId}`);
    return response.data;
  }

  // Create new quote
  static async createQuote(quoteData: QuoteCreateData): Promise<ApiResponse<Quote>> {
    const response = await apiClient.post('/quotes', quoteData);
    return response.data;
  }

  // Update quote
  static async updateQuote(id: number, quoteData: QuoteUpdateData): Promise<ApiResponse<Quote>> {
    const response = await apiClient.put(`/quotes/${id}`, quoteData);
    return response.data;
  }

  // Send quote to client
  static async sendQuote(id: number): Promise<ApiResponse<Quote>> {
    const response = await apiClient.post(`/quotes/${id}/send`);
    return response.data;
  }

  // Generate and download quote PDF
  static async generateQuotePDF(id: number): Promise<Blob> {
    const response = await apiClient.get(`/quotes/${id}/pdf`, {
      responseType: 'blob'
    });
    return response.data;
  }

  // Delete quote
  static async deleteQuote(id: number): Promise<ApiResponse<void>> {
    const response = await apiClient.delete(`/quotes/${id}`);
    return response.data;
  }

  // Get quote statistics
  static async getQuoteStatistics(): Promise<ApiResponse<QuoteStatistics>> {
    const response = await apiClient.get('/quotes/statistics');
    return response.data;
  }

  // Get quote audit history
  static async getQuoteAuditHistory(id: number): Promise<ApiResponse<AuditEntry[]>> {
    const response = await apiClient.get(`/quotes/${id}/audit`);
    return response.data;
  }

  // Public quote access methods (token-based)
  static async validateQuoteToken(token: string): Promise<ApiResponse<{ valid: boolean }>> {
    const response = await apiClient.get(`/quotes/public/validate/${token}`);
    return response.data;
  }

  static async getQuoteByToken(token: string): Promise<ApiResponse<Quote>> {
    const response = await apiClient.get(`/quotes/public/${token}`);
    return response.data;
  }

  static async generatePublicQuotePDF(token: string): Promise<Blob> {
    const response = await apiClient.get(`/quotes/public/${token}/pdf`, {
      responseType: 'blob'
    });
    return response.data;
  }

  static async approveQuote(token: string, approvalData: QuoteApprovalData): Promise<ApiResponse<Quote>> {
    const response = await apiClient.post(`/quotes/public/${token}/approve`, approvalData);
    return response.data;
  }

  static async rejectQuote(token: string, feedback?: string): Promise<ApiResponse<Quote>> {
    const response = await apiClient.post(`/quotes/public/${token}/reject`, { feedback });
    return response.data;
  }

  // Quote generation from RFQ submissions
  static async getSubmissionsForQuote(rfqId: number): Promise<ApiResponse<SubmissionsData>> {
    const response = await apiClient.get(`/quotes/rfq/${rfqId}/submissions`);
    return response.data;
  }

  static async generateQuoteFromSubmissions(rfqId: number, quoteData: QuoteGenerationData): Promise<ApiResponse<Quote>> {
    const response = await apiClient.post(`/quotes/rfq/${rfqId}/generate`, quoteData);
    return response.data;
  }

  // Advanced Quote Generation Methods

  // Generate advanced quote with partial selection support
  static async generateAdvancedQuote(rfqId: number, quoteData: AdvancedQuoteCreateData): Promise<ApiResponse<Quote>> {
    try {
      const response = await apiClient.post(`/rfqs/${rfqId}/quotes/advanced`, quoteData);
      return response.data;
    } catch (error) {
      console.error('Error generating advanced quote:', error);
      throw error;
    }
  }

  // Validate partial selections before quote generation
  static async validatePartialSelections(selections: QuoteSelectionItem[]): Promise<ApiResponse<ValidationResult>> {
    try {
      const response = await apiClient.post('/quotes/validate-selections', { selections });
      return response.data;
    } catch (error) {
      console.error('Error validating selections:', error);
      throw error;
    }
  }

  // Calculate quote pricing with commissions
  static async calculateQuotePricing(
    selections: QuoteSelectionItem[],
    globalCommissionRate: number,
    itemOverrides: Record<string, number>
  ): Promise<ApiResponse<QuotePricing>> {
    try {
      const response = await apiClient.post('/quotes/calculate-pricing', {
        selections,
        globalCommissionRate,
        itemOverrides
      });
      return response.data;
    } catch (error) {
      console.error('Error calculating quote pricing:', error);
      throw error;
    }
  }

  // Link quote to account and opportunity
  static async linkQuoteToAccountOpportunity(
    quoteId: number,
    accountId: number,
    opportunityId: number
  ): Promise<ApiResponse<Quote>> {
    try {
      const response = await apiClient.put(`/quotes/${quoteId}/link-account-opportunity`, {
        account_id: accountId,
        opportunity_id: opportunityId
      });
      return response.data;
    } catch (error) {
      console.error('Error linking quote to account/opportunity:', error);
      throw error;
    }
  }

  // Get detailed pricing breakdown for a quote
  static async getQuotePricingBreakdown(quoteId: number): Promise<ApiResponse<QuotePricing>> {
    try {
      const response = await apiClient.get(`/quotes/${quoteId}/pricing-breakdown`);
      return response.data;
    } catch (error) {
      console.error('Error getting pricing breakdown:', error);
      throw error;
    }
  }

  // Update quote selections (for editing existing quotes)
  static async updateQuoteSelections(
    quoteId: number,
    selections: QuoteSelectionItem[],
    config: QuoteConfiguration
  ): Promise<ApiResponse<Quote>> {
    try {
      const response = await apiClient.put(`/quotes/${quoteId}/selections`, {
        selections,
        config
      });
      return response.data;
    } catch (error) {
      console.error('Error updating quote selections:', error);
      throw error;
    }
  }

  // Get available submissions for quote generation (with partial selection info)
  static async getSubmissionsForAdvancedQuote(rfqId: number): Promise<ApiResponse<{
    rfq: {
      id: number;
      title: string;
      allow_partial_selection: boolean;
      partial_selection_config: any;
      items: RFQItem[];
    };
    submissions: Array<{
      id: number;
      vendor_id: number;
      vendor_name: string;
      vendor_performance_score: number;
      total_amount: number;
      currency: string;
      allows_partial_selection: boolean;
      partial_selection_notes?: string;
      bid_items: Array<{
        id: number;
        rfq_item_id: string;
        item_name: string;
        unit_price: number;
        quantity: number;
        total_price: number;
        delivery_days?: number;
        specifications: Record<string, any>;
        notes: string;
      }>;
    }>;
  }>> {
    try {
      const response = await apiClient.get(`/rfqs/${rfqId}/submissions/for-quote`);
      return response.data;
    } catch (error) {
      console.error('Error getting submissions for advanced quote:', error);
      throw error;
    }
  }
}

export default QuoteApiService;