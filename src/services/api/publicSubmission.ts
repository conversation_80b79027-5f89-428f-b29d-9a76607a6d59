import axios from 'axios';

const API_BASE_URL = '/api';

export interface TokenValidationResponse {
  success: boolean;
  data?: {
    invitation: any;
    has_existing_submission: boolean;
    existing_submission_id?: number;
  };
  message: string;
}

export interface RFQDataResponse {
  success: boolean;
  data?: {
    rfq_id: number;
    title: string;
    description: string;
    items: any[];
    due_date: string;
    form_config: any[];
    terms: string;
    vendor_name: string;
    vendor_email: string;
    invitation_status: string;
    has_existing_submission: boolean;
    existing_submission_id?: number;
  };
  message: string;
}

export interface SubmissionStatusResponse {
  success: boolean;
  data?: {
    token_valid: boolean;
    rfq_title: string;
    rfq_due_date: string;
    rfq_status: string;
    vendor_name: string;
    invitation_status: string;
    has_submission: boolean;
    submission_id?: number;
    can_submit: boolean;
    can_modify: boolean;
  };
  message: string;
}

export interface SubmissionResponse {
  success: boolean;
  data?: any;
  message: string;
}

class PublicSubmissionAPI {
  /**
   * Validate submission token
   */
  async validateToken(token: string): Promise<TokenValidationResponse> {
    try {
      const response = await axios.get(`${API_BASE_URL}/public/rfq/${token}/validate`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to validate token');
    }
  }

  /**
   * Get RFQ data for public submission
   */
  async getRFQData(token: string): Promise<RFQDataResponse> {
    try {
      const response = await axios.get(`${API_BASE_URL}/public/rfq/${token}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to load RFQ data');
    }
  }

  /**
   * Get submission status
   */
  async getSubmissionStatus(token: string): Promise<SubmissionStatusResponse> {
    try {
      const response = await axios.get(`${API_BASE_URL}/public/rfq/${token}/status`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to get submission status');
    }
  }

  /**
   * Get existing submission by token
   */
  async getExistingSubmission(token: string): Promise<SubmissionResponse> {
    try {
      const response = await axios.get(`${API_BASE_URL}/public/rfq/${token}/submission`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to load existing submission');
    }
  }

  /**
   * Create new submission
   */
  async createSubmission(formData: FormData): Promise<SubmissionResponse> {
    try {
      const response = await axios.post(`${API_BASE_URL}/public/rfq/submission`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create submission');
    }
  }

  /**
   * Update existing submission
   */
  async updateSubmission(submissionId: number, formData: FormData): Promise<SubmissionResponse> {
    try {
      const response = await axios.put(`${API_BASE_URL}/public/rfq/submission/${submissionId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update submission');
    }
  }

  /**
   * Delete submission
   */
  async deleteSubmission(submissionId: number, token: string): Promise<SubmissionResponse> {
    try {
      const response = await axios.delete(`${API_BASE_URL}/public/rfq/submission/${submissionId}`, {
        data: { token },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete submission');
    }
  }

  /**
   * Download attachment
   */
  async downloadAttachment(token: string, filename: string): Promise<Blob> {
    try {
      const response = await axios.get(`${API_BASE_URL}/public/rfq/${token}/attachment/${filename}`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to download attachment');
    }
  }
}

export const publicSubmissionAPI = new PublicSubmissionAPI();