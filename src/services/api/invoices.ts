import { apiClient, ApiResponse } from '../api';

export interface Invoice {
  id: number;
  vendor_id: number;
  vendor_name?: string;
  amount: number;
  currency: string;
  status: 'draft' | 'approved' | 'paid' | 'partially_paid' | 'disputed' | 'overdue';
  items: InvoiceItem[];
  taxes: number;
  penalties: number;
  due_date: string;
  rfq_id?: number;
  rfq_quote_id?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  taxable: boolean;
}

export interface RFQInvoiceStatus {
  invoice_id: number;
  vendor_id: number;
  vendor_name: string;
  amount: number;
  currency: string;
  invoice_status: string;
  due_date: string;
  invoice_created_at: string;
  quote_id: number;
  quote_title: string;
  quote_status: string;
  quote_approved_at: string;
  total_paid: number;
  payment_count: number;
  last_payment_date?: string;
  outstanding_amount: number;
}

export interface RFQInvoiceAnalytics {
  total_rfqs_with_invoices: number;
  total_rfq_invoices: number;
  unique_vendors_invoiced: number;
  total_invoice_value: number;
  average_invoice_amount: number;
  paid_invoices: number;
  overdue_invoices: number;
  total_paid_amount: number;
  avg_payment_days?: number;
  total_closed_rfqs: number;
  rfq_to_invoice_conversion_rate: number;
}

export class InvoiceApiService {
  // Generate invoice from RFQ quote
  static async generateFromRFQQuote(quoteId: number, additionalData?: any): Promise<ApiResponse<Invoice[]>> {
    const response = await apiClient.post(`/invoices/generate/rfq-quote/${quoteId}`, additionalData);
    return response.data;
  }

  // Get invoices linked to RFQ
  static async getByRFQ(rfqId: number): Promise<ApiResponse<Invoice[]>> {
    const response = await apiClient.get(`/invoices/rfq/${rfqId}`);
    return response.data;
  }

  // Get RFQ invoice status tracking
  static async getRFQInvoiceStatus(rfqId: number): Promise<ApiResponse<RFQInvoiceStatus[]>> {
    const response = await apiClient.get(`/invoices/rfq/${rfqId}/status`);
    return response.data;
  }

  // Update invoice status with RFQ tracking
  static async updateStatusWithRFQTracking(
    invoiceId: number, 
    status: string, 
    reason?: string
  ): Promise<ApiResponse<Invoice>> {
    const response = await apiClient.put(`/invoices/${invoiceId}/status-rfq`, { status, reason });
    return response.data;
  }

  // Get RFQ-to-Invoice analytics
  static async getRFQInvoiceAnalytics(filters?: {
    startDate?: string;
    endDate?: string;
    vendorId?: number;
    rfqId?: number;
  }): Promise<ApiResponse<RFQInvoiceAnalytics>> {
    const response = await apiClient.get('/invoices/analytics/rfq', { params: filters });
    return response.data;
  }

  // Get invoice by ID
  static async getById(id: number): Promise<ApiResponse<Invoice>> {
    const response = await apiClient.get(`/invoices/${id}`);
    return response.data;
  }

  // Update invoice status
  static async updateStatus(id: number, status: string, reason?: string): Promise<ApiResponse<Invoice>> {
    const response = await apiClient.put(`/invoices/${id}`, { status });
    return response.data;
  }

  // Approve invoice
  static async approve(id: number, comments?: string): Promise<ApiResponse<Invoice>> {
    const response = await apiClient.post(`/invoices/${id}/approve`, { comments });
    return response.data;
  }
}

export default InvoiceApiService;