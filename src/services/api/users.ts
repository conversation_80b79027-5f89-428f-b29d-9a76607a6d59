import { apiClient, ApiResponse, PaginatedResponse } from '../api';

// User interfaces
export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'manager' | 'viewer';
  status: 'active' | 'inactive' | 'pending';
  isVerified: boolean;
  avatar?: string;
  preferences?: Record<string, string | number | boolean | null>;
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;
}

export interface UserCreateData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  role: 'admin' | 'manager' | 'viewer';
}

export interface UserUpdateData extends Partial<UserCreateData> {
  id: number;
}

export interface UserFilters {
  search?: string;
  role?: 'admin' | 'manager' | 'viewer';
  status?: 'active' | 'inactive' | 'pending';
}

// Users API service
export class UsersApiService {
  // Get all users with filtering and pagination
  static async getUsers(
    page: number = 1,
    limit: number = 10,
    filters: UserFilters = {}
  ): Promise<PaginatedResponse<User>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    // Add filters to params
    if (filters.search) params.append('search', filters.search);
    if (filters.role) params.append('role', filters.role);
    if (filters.status) params.append('status', filters.status);

    const response = await apiClient.get(`/users?${params.toString()}`);
    return response.data;
  }

  // Get user by ID
  static async getUserById(id: number): Promise<ApiResponse<User>> {
    const response = await apiClient.get(`/users/${id}`);
    return response.data;
  }

  // Create new user
  static async createUser(userData: UserCreateData): Promise<ApiResponse<User>> {
    const response = await apiClient.post('/users', userData);
    return response.data;
  }

  // Update user
  static async updateUser(userData: UserUpdateData): Promise<ApiResponse<User>> {
    const response = await apiClient.put(`/users/${userData.id}`, userData);
    return response.data;
  }

  // Delete user
  static async deleteUser(id: number): Promise<ApiResponse<void>> {
    const response = await apiClient.delete(`/users/${id}`);
    return response.data;
  }

  // Update user role
  static async updateUserRole(id: number, role: string): Promise<ApiResponse<User>> {
    const response = await apiClient.put(`/users/${id}/role`, { role });
    return response.data;
  }

  // Set user password
  static async setUserPassword(id: number, password: string): Promise<ApiResponse<void>> {
    const response = await apiClient.put(`/users/${id}/password`, { password });
    return response.data;
  }

  // Search users for mentions
  static async searchUsers(query: string): Promise<ApiResponse<User[]>> {
    const params = new URLSearchParams({ search: query, limit: '10' });
    const response = await apiClient.get(`/users?${params.toString()}`);
    return {
      data: response.data.data,
      success: true
    };
  }

  // Get current user profile
  static async getCurrentUser(): Promise<ApiResponse<User>> {
    const response = await apiClient.get('/auth/me');
    return response.data;
  }
}

// Export default instance
export default UsersApiService;

// Named exports for convenience
export const usersApi = UsersApiService;