import { apiClient, ApiResponse, PaginatedResponse } from '../api';
import RFQErrorHandler from './rfqErrorHandler';
import RFQTransforms from './rfqTransforms';

// RFQ Data Interfaces
export interface RFQ {
  id: number;
  title: string;
  description: string;
  status: 'draft' | 'sent' | 'in_progress' | 'closed' | 'cancelled';
  items: RFQItem[];
  due_date: string;
  currency: string;
  creator_id: number;
  creator_email: string;
  form_config: FormField[];
  ai_settings: AISettings;
  terms: string;
  
  // Enhanced partial selection support
  allow_partial_selection: boolean;
  partial_selection_config: PartialSelectionConfig;
  
  // Statistics
  invitation_count: number;
  submission_count: number;
  response_rate: number;
  item_count: number;
  viewed_invitations: number;
  view_rate: number;
  partial_selection_submissions: number;
  
  // Lifecycle tracking
  created_at: string;
  updated_at: string;
  sent_at?: string;
  closed_at?: string;
  
  // Related data (populated when needed)
  invitations?: RFQInvitation[];
  submissions?: RFQSubmission[];
  analytics?: RFQAnalytics;
}

export interface PartialSelectionConfig {
  enabled: boolean;
  requireVendorConfirmation: boolean;
  confirmationMessage: string;
  instructions: string;
  defaultAllowed: boolean;
}

export interface RFQItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  unit?: string;
  specifications: Record<string, any>;
  category: string;
  estimatedPrice?: number;
  customFields: Record<string, any>;
  attachments?: FileAttachment[];
}

export interface FileAttachment {
  filename: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  filePath: string;
  uploadedAt: string;
}

export interface FormField {
  id: string;
  type: 'text' | 'number' | 'date' | 'file' | 'select' | 'textarea';
  label: string;
  required: boolean;
  options?: string[];
  validation: Record<string, unknown>[];
  itemSpecific: boolean;
}

export interface AISettings {
  priceWeight: number;
  performanceWeight: number;
  deliveryWeight: number;
  riskWeight: number;
  diversificationPreference: number;
}

export interface RFQInvitation {
  id: number;
  rfq_id: number;
  vendor_id: number;
  vendor_name: string;
  vendor_email: string;
  vendor_performance_score: number;
  status: 'sent' | 'viewed' | 'submitted';
  token: string;
  sent_at?: string;
  viewed_at?: string;
  submitted_at?: string;
  expires_at: string;
  created_at: string;
  
  // Enhanced tracking
  reminder_count: number;
  last_reminded_at?: string;
  engagement_score: number;
}

export interface RFQSubmission {
  id: number;
  rfq_id: number;
  vendor_id: number;
  vendor_name: string;
  vendor_email: string;
  vendor_performance_score: number;
  vendor_category: string;
  invitation_token: string;
  bid_data: Record<string, any>;
  attachments: SubmissionAttachment[];
  total_amount: number;
  currency: string;
  delivery_days?: number;
  payment_terms: string;
  validity_period: number;
  additional_notes: string;
  submitted_at: string;
  updated_at: string;
  bid_items: BidItem[];
  invitation_sent_at?: string;
  invitation_viewed_at?: string;
  
  // Enhanced partial selection support
  allows_partial_selection: boolean;
  partial_selection_notes?: string;
  viewed_at?: string;
  engagement_score: number;
  response_time: number; // hours from invitation to submission
}

export interface BidItem {
  id: number;
  submission_id: number;
  rfq_item_id: string;
  item_name: string;
  unit_price: number;
  quantity: number;
  total_price: number;
  delivery_days?: number;
  specifications: Record<string, unknown>;
  alternatives: BidAlternative[];
  notes: string;
}

export interface BidAlternative {
  description: string;
  unitPrice: number;
  totalPrice: number;
  notes: string;
}

export interface SubmissionAttachment {
  filename: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  filePath: string;
  uploadedAt: string;
}

export interface RFQAnalytics {
  rfq_id: number;
  total_invitations: number;
  total_submissions: number;
  response_rate: number;
  average_response_time_hours?: number;
  lowest_bid_amount?: number;
  highest_bid_amount?: number;
  average_bid_amount?: number;
  calculated_at: string;
}

// Filter and Query Interfaces
export interface RFQFilters {
  search?: string;
  status?: string[];
  creator_id?: number;
  due_date_from?: string;
  due_date_to?: string;
  currency?: string;
  sortBy?: 'created_at' | 'due_date' | 'title' | 'response_rate' | 'status';
  sortOrder?: 'asc' | 'desc';
}

export interface RFQCreateData {
  title: string;
  description?: string;
  items: RFQItem[];
  due_date: string;
  selectedVendors: number[];
  formConfig?: FormField[];
  terms?: string;
  currency?: string;
  aiSettings?: AISettings;
  allowPartialSelection?: boolean;
  partialSelectionConfig?: PartialSelectionConfig;
}

export interface RFQUpdateData {
  title?: string;
  description?: string;
  items?: RFQItem[];
  due_date?: string;
  formConfig?: FormField[];
  terms?: string;
  currency?: string;
  aiSettings?: AISettings;
  status?: 'draft' | 'sent' | 'in_progress' | 'closed' | 'cancelled';
  allowPartialSelection?: boolean;
  partialSelectionConfig?: PartialSelectionConfig;
}

export interface BidComparisonData {
  rfq_items: RFQItem[];
  submissions: RFQSubmission[];
  statistics: {
    total_submissions: number;
    lowest_total_bid: number;
    highest_total_bid: number;
    average_total_bid: number;
  };
}

export interface SubmissionAnalytics {
  submission_id: number;
  vendor_name: string;
  total_amount: number;
  rank_by_price: number;
  total_competitors: number;
  price_difference_from_lowest: number;
  price_difference_from_average: number;
  delivery_days?: number;
  response_time_hours?: number;
  item_count: number;
  has_alternatives: boolean;
  has_attachments: boolean;
}

export interface RFQStatusStatistics {
  status: string;
  count: number;
}

export interface AuditEntry {
  id: number;
  entity_type: string;
  entity_id: number;
  action: 'create' | 'update' | 'delete' | 'view' | 'approve';
  user_id: number;
  user_email: string;
  old_value?: Record<string, unknown>;
  new_value?: Record<string, unknown>;
  details?: Record<string, unknown>;
  timestamp: string;
}

// Account and Opportunity Integration Interfaces
export interface AccountSearchResult {
  id: number;
  name: string;
  type: string;
  industry: string;
  location: string;
  activeOpportunities: number;
}

export interface OpportunitySearchResult {
  id: number;
  name: string;
  stage: string;
  value: number;
  currency: string;
  closeDate: string;
  probability: number;
}

export interface Account {
  id: number;
  name: string;
  type: string;
  industry: string;
  location: string;
  contactEmail: string;
  contactPhone: string;
  website?: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Opportunity {
  id: number;
  accountId: number;
  name: string;
  stage: string;
  value: number;
  currency: string;
  closeDate: string;
  probability: number;
  description?: string;
  ownerId: number;
  ownerName: string;
  createdAt: string;
  updatedAt: string;
}

// RFQ API Service Class
export class RFQApiService {
  // Get all RFQs with filtering and pagination
  static async getRFQs(
    page: number = 1,
    limit: number = 10,
    filters: RFQFilters = {}
  ): Promise<PaginatedResponse<RFQ>> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      // Add filters to params
      if (filters.search) params.append('search', filters.search);
      if (filters.status && filters.status.length > 0) {
        filters.status.forEach(status => params.append('status', status));
      }
      if (filters.creator_id) params.append('creator_id', filters.creator_id.toString());
      if (filters.due_date_from) params.append('due_date_from', filters.due_date_from);
      if (filters.due_date_to) params.append('due_date_to', filters.due_date_to);
      if (filters.currency) params.append('currency', filters.currency);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);

      const response = await apiClient.get(`/rfqs?${params.toString()}`);
      
      // Transform the response data
      const transformedData = RFQTransforms.transformPaginatedResponse(
        response.data,
        RFQTransforms.transformRFQ
      );
      
      return {
        ...transformedData,
        success: response.data.success !== false,
        message: response.data.message
      };
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading RFQs');
      throw error;
    }
  }

  // Get RFQ by ID with detailed information
  static async getRFQById(id: number): Promise<ApiResponse<RFQ>> {
    try {
      const response = await apiClient.get(`/rfqs/${id}`);
      
      return {
        ...response.data,
        data: RFQTransforms.transformRFQ(response.data.data)
      };
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading RFQ details');
      throw error;
    }
  }

  // Create new RFQ
  static async createRFQ(rfqData: RFQCreateData): Promise<ApiResponse<RFQ>> {
    try {
      // Validate data before sending
      const validation = this.validateRFQData(rfqData);
      if (!validation.valid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      const transformedData = RFQTransforms.transformForAPI(rfqData);
      const response = await apiClient.post('/rfqs', transformedData);
      
      const createdRFQ = RFQTransforms.transformRFQ(response.data.data);
      RFQErrorHandler.showRFQCreated(createdRFQ.title);
      
      return {
        ...response.data,
        data: createdRFQ
      };
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Creating RFQ');
      throw error;
    }
  }

  // Update RFQ
  static async updateRFQ(id: number, updates: RFQUpdateData): Promise<ApiResponse<RFQ>> {
    try {
      const transformedData = RFQTransforms.transformForUpdateAPI(updates as Record<string, unknown>);
      const response = await apiClient.put(`/rfqs/${id}`, transformedData);
      
      return {
        ...response.data,
        data: RFQTransforms.transformRFQ(response.data.data)
      };
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Updating RFQ');
      throw error;
    }
  }

  // Delete RFQ (soft delete - sets status to cancelled)
  static async deleteRFQ(id: number): Promise<ApiResponse<{ id: number; status: string }>> {
    try {
      const response = await apiClient.delete(`/rfqs/${id}`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Deleting RFQ');
      throw error;
    }
  }

  // Send RFQ invitations (change status from draft to sent)
  static async sendInvitations(id: number): Promise<ApiResponse<{ rfq: RFQ; invitations: RFQInvitation[] }>> {
    try {
      const response = await apiClient.post(`/rfqs/${id}/send`);
      return {
        ...response.data,
        data: {
          rfq: RFQTransforms.transformRFQ(response.data.data.rfq),
          invitations: response.data.data.invitations
        }
      };
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Sending RFQ invitations');
      throw error;
    }
  }

  // Get RFQ invitations
  static async getRFQInvitations(id: number): Promise<ApiResponse<RFQInvitation[]>> {
    try {
      const response = await apiClient.get(`/rfqs/${id}/invitations`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading RFQ invitations');
      throw error;
    }
  }

  // Get RFQ submissions
  static async getRFQSubmissions(id: number): Promise<ApiResponse<RFQSubmission[]>> {
    try {
      const response = await apiClient.get(`/rfqs/${id}/submissions`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading RFQ submissions');
      throw error;
    }
  }

  // Get bid comparison data for analysis
  static async getBidComparison(id: number): Promise<ApiResponse<BidComparisonData>> {
    try {
      const response = await apiClient.get(`/rfqs/${id}/submissions/comparison`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading bid comparison data');
      throw error;
    }
  }

  // Get RFQ analytics
  static async getRFQAnalytics(id: number): Promise<ApiResponse<RFQAnalytics>> {
    try {
      const response = await apiClient.get(`/rfqs/${id}/analytics`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading RFQ analytics');
      throw error;
    }
  }

  // Get submission analytics
  static async getSubmissionAnalytics(submissionId: number): Promise<ApiResponse<SubmissionAnalytics>> {
    try {
      const response = await apiClient.get(`/rfq-submissions/${submissionId}/analytics`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading submission analytics');
      throw error;
    }
  }

  // Get RFQ status statistics for dashboard
  static async getStatusStatistics(): Promise<ApiResponse<RFQStatusStatistics[]>> {
    try {
      const response = await apiClient.get('/rfqs/statistics/status');
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading status statistics');
      throw error;
    }
  }

  // Get RFQ audit history
  static async getAuditHistory(id: number): Promise<ApiResponse<AuditEntry[]>> {
    try {
      const response = await apiClient.get(`/rfqs/${id}/audit`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading audit history');
      throw error;
    }
  }

  // Enhanced RFQ Management Methods

  // Get all items for an RFQ (for "Show All Items" functionality)
  static async getAllItems(id: number): Promise<ApiResponse<RFQItem[]>> {
    try {
      const response = await apiClient.get(`/rfqs/${id}/items/all`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading all RFQ items');
      throw error;
    }
  }

  // Enhanced invitation management
  static async resendInvitation(invitationId: number): Promise<ApiResponse<RFQInvitation>> {
    try {
      const response = await apiClient.post(`/rfq-invitations/${invitationId}/resend`);
      RFQErrorHandler.showSuccess('Invitation resent successfully');
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Resending invitation');
      throw error;
    }
  }

  static async sendReminder(invitationId: number, message?: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiClient.post(`/rfq-invitations/${invitationId}/remind`, {
        message: message || 'This is a friendly reminder about your pending RFQ submission.'
      });
      RFQErrorHandler.showSuccess('Reminder sent successfully');
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Sending reminder');
      throw error;
    }
  }

  static async addVendorsToRFQ(rfqId: number, vendorIds: number[]): Promise<ApiResponse<RFQInvitation[]>> {
    try {
      const response = await apiClient.post(`/rfqs/${rfqId}/invitations/add-vendors`, {
        vendor_ids: vendorIds
      });
      RFQErrorHandler.showSuccess(`Added ${vendorIds.length} vendor(s) to RFQ`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Adding vendors to RFQ');
      throw error;
    }
  }

  // Track invitation viewing (called from public submission form)
  static async trackInvitationViewing(token: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiClient.post('/rfq-invitations/track-view', {
        token
      });
      return response.data;
    } catch (error) {
      // Don't show error to user for tracking failures
      console.warn('Failed to track invitation viewing:', error);
      return { success: false, message: 'Tracking failed' };
    }
  }

  // Get engagement metrics for an RFQ
  static async getEngagementMetrics(rfqId: number): Promise<ApiResponse<{
    totalInvitations: number;
    viewedInvitations: number;
    submittedBids: number;
    averageViewTime: number;
    responseRate: number;
    engagementScore: number;
    vendorEngagement: Array<{
      vendorId: number;
      vendorName: string;
      viewedAt?: string;
      submittedAt?: string;
      responseTime?: number;
      engagementScore: number;
    }>;
  }>> {
    try {
      const response = await apiClient.get(`/rfqs/${rfqId}/engagement-metrics`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading engagement metrics');
      throw error;
    }
  }

  // Bulk operations
  static async bulkUpdateStatus(
    rfqIds: number[], 
    status: string, 
    reason?: string
  ): Promise<ApiResponse<{ updated: number; failed: number }>> {
    try {
      const response = await apiClient.post('/rfqs/bulk/status', {
        rfq_ids: rfqIds,
        status,
        reason
      });
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Bulk updating RFQ status');
      throw error;
    }
  }

  static async bulkDelete(rfqIds: number[]): Promise<ApiResponse<{ deleted: number; failed: number }>> {
    try {
      const response = await apiClient.post('/rfqs/bulk/delete', {
        rfq_ids: rfqIds
      });
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Bulk deleting RFQs');
      throw error;
    }
  }

  // Export functionality
  static async exportRFQs(
    filters: RFQFilters = {},
    format: 'csv' | 'excel' = 'csv'
  ): Promise<ApiResponse<{ download_url: string; filename: string }>> {
    try {
      const params = new URLSearchParams({ format });
      
      // Add filters to params
      if (filters.search) params.append('search', filters.search);
      if (filters.status && filters.status.length > 0) {
        filters.status.forEach(status => params.append('status', status));
      }
      if (filters.creator_id) params.append('creator_id', filters.creator_id.toString());
      if (filters.due_date_from) params.append('due_date_from', filters.due_date_from);
      if (filters.due_date_to) params.append('due_date_to', filters.due_date_to);
      if (filters.currency) params.append('currency', filters.currency);

      const response = await apiClient.get(`/rfqs/export?${params.toString()}`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Exporting RFQs');
      throw error;
    }
  }

  static async exportRFQSubmissions(
    rfqId: number,
    format: 'csv' | 'excel' = 'csv'
  ): Promise<ApiResponse<{ download_url: string; filename: string }>> {
    try {
      const response = await apiClient.get(`/rfqs/${rfqId}/submissions/export?format=${format}`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Exporting RFQ submissions');
      throw error;
    }
  }

  // AI Recommendations
  static async getAIRecommendations(rfqId: number): Promise<ApiResponse<{
    vendor_recommendations: Array<{
      vendor_id: number;
      vendor_name: string;
      score: number;
      reasoning: string;
      risk_factors: string[];
      advantages: string[];
    }>;
    bid_analysis: Array<{
      submission_id: number;
      vendor_name: string;
      overall_score: number;
      price_score: number;
      performance_score: number;
      delivery_score: number;
      risk_score: number;
      recommendation: 'highly_recommended' | 'recommended' | 'consider' | 'not_recommended';
      reasoning: string;
    }>;
    diversification_suggestions: Array<{
      item_id: string;
      item_name: string;
      suggested_vendors: number[];
      reasoning: string;
    }>;
  }>> {
    try {
      const response = await apiClient.get(`/rfqs/${rfqId}/ai-recommendations`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading AI recommendations');
      throw error;
    }
  }

  // Real-time updates
  static async subscribeToRFQUpdates(rfqId: number, callback: (update: Record<string, unknown>) => void): Promise<() => void> {
    // This would integrate with WebSocket service
    // For now, return a placeholder unsubscribe function
    return () => {};
  }

  // Account and Opportunity Integration Methods

  // Search accounts with real-time filtering
  static async searchAccounts(query: string, limit: number = 10): Promise<ApiResponse<AccountSearchResult[]>> {
    try {
      const params = new URLSearchParams({
        q: query,
        limit: limit.toString()
      });
      
      const response = await apiClient.get(`/accounts/search?${params.toString()}`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Searching accounts');
      throw error;
    }
  }

  // Get opportunities for a specific account
  static async getAccountOpportunities(accountId: number): Promise<ApiResponse<OpportunitySearchResult[]>> {
    try {
      const response = await apiClient.get(`/accounts/${accountId}/opportunities`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading account opportunities');
      throw error;
    }
  }

  // Get account details
  static async getAccountDetails(accountId: number): Promise<ApiResponse<Account>> {
    try {
      const response = await apiClient.get(`/accounts/${accountId}`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading account details');
      throw error;
    }
  }

  // Get opportunity details
  static async getOpportunityDetails(opportunityId: number): Promise<ApiResponse<Opportunity>> {
    try {
      const response = await apiClient.get(`/opportunities/${opportunityId}`);
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Loading opportunity details');
      throw error;
    }
  }

  // Validate account/opportunity relationship
  static async validateAccountOpportunityLink(accountId: number, opportunityId: number): Promise<ApiResponse<{ valid: boolean; message?: string }>> {
    try {
      const response = await apiClient.post('/quotes/validate-account-opportunity', {
        account_id: accountId,
        opportunity_id: opportunityId
      });
      return response.data;
    } catch (error) {
      RFQErrorHandler.handleError(error, 'Validating account/opportunity link');
      throw error;
    }
  }

  // Validation helpers
  static validateRFQData(data: RFQCreateData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.title || data.title.trim().length < 3) {
      errors.push('Title must be at least 3 characters long');
    }

    if (!data.items || data.items.length === 0) {
      errors.push('At least one item is required');
    }

    if (!data.due_date || new Date(data.due_date) <= new Date()) {
      errors.push('Due date must be in the future');
    }

    if (!data.selectedVendors || data.selectedVendors.length === 0) {
      errors.push('At least one vendor must be selected');
    }

    // Validate items
    data.items?.forEach((item, index) => {
      if (!item.name || item.name.trim().length === 0) {
        errors.push(`Item ${index + 1}: Name is required`);
      }
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Item ${index + 1}: Quantity must be greater than 0`);
      }
    });

    // Validate partial selection config if enabled
    if (data.allowPartialSelection && data.partialSelectionConfig) {
      if (data.partialSelectionConfig.requireVendorConfirmation && 
          (!data.partialSelectionConfig.confirmationMessage || 
           data.partialSelectionConfig.confirmationMessage.trim().length === 0)) {
        errors.push('Confirmation message is required when vendor confirmation is enabled');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

export default RFQApiService;