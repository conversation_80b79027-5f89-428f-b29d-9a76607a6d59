import { AxiosError } from 'axios';
import { toast } from '@/hooks/use-toast';

export interface RFQApiError {
  code: string;
  message: string;
  details?: Record<string, string[]>;
  timestamp: string;
}

export class RFQErrorHandler {
  static handleError(error: AxiosError<RFQApiError> | Error, context?: string): void {
    console.error('RFQ API Error:', error);

    if (error instanceof AxiosError && error.response?.data) {
      const apiError = error.response.data;
      this.handleApiError(apiError, context);
    } else {
      this.handleGenericError(error, context);
    }
  }

  private static handleApiError(apiError: RFQApiError, context?: string): void {
    const contextPrefix = context ? `${context}: ` : '';
    
    switch (apiError.code) {
      case 'RFQ_NOT_FOUND':
        toast({
          title: 'RFQ Not Found',
          description: `${contextPrefix}The requested RFQ could not be found.`,
          variant: 'destructive',
        });
        break;

      case 'RFQ_ACCESS_DENIED':
        toast({
          title: 'Access Denied',
          description: `${contextPrefix}You don't have permission to access this RFQ.`,
          variant: 'destructive',
        });
        break;

      case 'RFQ_VALIDATION_ERROR':
        this.handleValidationError(apiError, context);
        break;

      case 'RFQ_STATUS_INVALID':
        toast({
          title: 'Invalid Operation',
          description: `${contextPrefix}This operation cannot be performed on an RFQ with the current status.`,
          variant: 'destructive',
        });
        break;

      case 'RFQ_DEADLINE_PASSED':
        toast({
          title: 'Deadline Passed',
          description: `${contextPrefix}The RFQ deadline has passed and no further changes can be made.`,
          variant: 'destructive',
        });
        break;

      case 'VENDOR_NOT_FOUND':
        toast({
          title: 'Vendor Not Found',
          description: `${contextPrefix}One or more selected vendors could not be found.`,
          variant: 'destructive',
        });
        break;

      case 'INVITATION_TOKEN_INVALID':
        toast({
          title: 'Invalid Invitation',
          description: `${contextPrefix}The invitation token is invalid or has expired.`,
          variant: 'destructive',
        });
        break;

      case 'SUBMISSION_ALREADY_EXISTS':
        toast({
          title: 'Submission Exists',
          description: `${contextPrefix}A submission already exists for this vendor.`,
          variant: 'destructive',
        });
        break;

      case 'FILE_UPLOAD_ERROR':
        toast({
          title: 'File Upload Failed',
          description: `${contextPrefix}Failed to upload one or more files. Please try again.`,
          variant: 'destructive',
        });
        break;

      case 'EXPORT_ERROR':
        toast({
          title: 'Export Failed',
          description: `${contextPrefix}Failed to export RFQ data. Please try again.`,
          variant: 'destructive',
        });
        break;

      case 'AI_SERVICE_ERROR':
        toast({
          title: 'AI Analysis Unavailable',
          description: `${contextPrefix}AI recommendations are temporarily unavailable. Please try again later.`,
          variant: 'destructive',
        });
        break;

      case 'BULK_OPERATION_PARTIAL_FAILURE':
        toast({
          title: 'Partial Success',
          description: `${contextPrefix}Some operations completed successfully, but others failed. Please check the results.`,
          variant: 'destructive',
        });
        break;

      default:
        toast({
          title: 'Operation Failed',
          description: `${contextPrefix}${apiError.message || 'An unexpected error occurred.'}`,
          variant: 'destructive',
        });
        break;
    }
  }

  private static handleValidationError(apiError: RFQApiError, context?: string): void {
    const contextPrefix = context ? `${context}: ` : '';
    
    if (apiError.details) {
      // Show detailed validation errors
      const errorMessages = Object.entries(apiError.details)
        .map(([field, errors]) => `${field}: ${errors.join(', ')}`)
        .join('\n');

      toast({
        title: 'Validation Error',
        description: `${contextPrefix}Please correct the following errors:\n${errorMessages}`,
        variant: 'destructive',
      });
    } else {
      toast({
        title: 'Validation Error',
        description: `${contextPrefix}Please check your input and try again.`,
        variant: 'destructive',
      });
    }
  }

  private static handleGenericError(error: Error, context?: string): void {
    const contextPrefix = context ? `${context}: ` : '';
    
    if (error.message.includes('Network Error')) {
      toast({
        title: 'Connection Error',
        description: `${contextPrefix}Unable to connect to the server. Please check your internet connection.`,
        variant: 'destructive',
      });
    } else if (error.message.includes('timeout')) {
      toast({
        title: 'Request Timeout',
        description: `${contextPrefix}The request took too long to complete. Please try again.`,
        variant: 'destructive',
      });
    } else {
      toast({
        title: 'Unexpected Error',
        description: `${contextPrefix}An unexpected error occurred. Please try again.`,
        variant: 'destructive',
      });
    }
  }

  // Success message helpers
  static showSuccess(operation: string, details?: string): void {
    toast({
      title: 'Success',
      description: details || `${operation} completed successfully.`,
      variant: 'default',
    });
  }

  static showRFQCreated(rfqTitle: string): void {
    toast({
      title: 'RFQ Created',
      description: `"${rfqTitle}" has been created successfully.`,
      variant: 'default',
    });
  }

  static showRFQUpdated(rfqTitle: string): void {
    toast({
      title: 'RFQ Updated',
      description: `"${rfqTitle}" has been updated successfully.`,
      variant: 'default',
    });
  }

  static showInvitationsSent(count: number): void {
    toast({
      title: 'Invitations Sent',
      description: `${count} invitation${count !== 1 ? 's' : ''} sent successfully.`,
      variant: 'default',
    });
  }

  static showRFQDeleted(rfqTitle: string): void {
    toast({
      title: 'RFQ Deleted',
      description: `"${rfqTitle}" has been deleted successfully.`,
      variant: 'default',
    });
  }

  static showBulkOperationSuccess(operation: string, count: number): void {
    toast({
      title: 'Bulk Operation Complete',
      description: `${operation} completed for ${count} RFQ${count !== 1 ? 's' : ''}.`,
      variant: 'default',
    });
  }

  static showExportReady(filename: string): void {
    toast({
      title: 'Export Ready',
      description: `Your export "${filename}" is ready for download.`,
      variant: 'default',
    });
  }

  // Loading state helpers
  static getLoadingMessage(operation: string): string {
    const messages: Record<string, string> = {
      'loading': 'Loading RFQs...',
      'creating': 'Creating RFQ...',
      'updating': 'Updating RFQ...',
      'deleting': 'Deleting RFQ...',
      'sending': 'Sending invitations...',
      'exporting': 'Preparing export...',
      'analyzing': 'Analyzing submissions...',
      'generating': 'Generating recommendations...',
    };

    return messages[operation] || `${operation}...`;
  }

  // Retry helpers
  static shouldRetry(error: AxiosError): boolean {
    if (!error.response) {
      // Network errors should be retried
      return true;
    }

    const status = error.response.status;
    // Retry on server errors (5xx) and rate limiting (429)
    return status >= 500 || status === 429;
  }

  static getRetryDelay(attempt: number): number {
    // Exponential backoff: 1s, 2s, 4s, 8s, 16s
    return Math.min(1000 * Math.pow(2, attempt), 16000);
  }
}

export default RFQErrorHandler;