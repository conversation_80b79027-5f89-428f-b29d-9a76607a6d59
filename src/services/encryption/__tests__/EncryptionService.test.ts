import { EncryptionService } from '../EncryptionService';

// Mock crypto for testing
const mockCrypto = {
  getRandomValues: jest.fn((arr) => {
    for (let i = 0; i < arr.length; i++) {
      arr[i] = Math.floor(Math.random() * 256);
    }
    return arr;
  }),
  subtle: {
    importKey: jest.fn(),
    deriveKey: jest.fn(),
    encrypt: jest.fn(),
    decrypt: jest.fn(),
    digest: jest.fn()
  }
};

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn()
};

Object.defineProperty(global, 'crypto', {
  value: mockCrypto,
  writable: true
});

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true
});

describe('EncryptionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  describe('isSupported', () => {
    it('should return true when crypto.subtle is available', () => {
      expect(EncryptionService.isSupported()).toBe(true);
    });

    it('should return false when crypto.subtle is not available', () => {
      const originalCrypto = global.crypto;
      // @ts-ignore
      global.crypto = undefined;
      
      expect(EncryptionService.isSupported()).toBe(false);
      
      global.crypto = originalCrypto;
    });
  });

  describe('validateApiKeyFormat', () => {
    it('should validate OpenAI API key format', () => {
      const result = EncryptionService.validateApiKeyFormat('openai', 'sk-1234567890abcdef1234567890abcdef');
      expect(result.isValid).toBe(true);
    });

    it('should reject invalid OpenAI API key format', () => {
      const result = EncryptionService.validateApiKeyFormat('openai', 'invalid-key');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('must start with "sk-"');
    });

    it('should validate Anthropic API key format', () => {
      const result = EncryptionService.validateApiKeyFormat('anthropic', 'sk-ant-1234567890abcdef1234567890abcdef');
      expect(result.isValid).toBe(true);
    });

    it('should reject invalid Anthropic API key format', () => {
      const result = EncryptionService.validateApiKeyFormat('anthropic', 'sk-1234567890abcdef');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('must start with "sk-ant-"');
    });

    it('should validate Gemini API key format', () => {
      const result = EncryptionService.validateApiKeyFormat('gemini', 'AI1234567890abcdef');
      expect(result.isValid).toBe(true);
    });

    it('should reject short Gemini API key', () => {
      const result = EncryptionService.validateApiKeyFormat('gemini', 'short');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('too short');
    });

    it('should reject empty API key', () => {
      const result = EncryptionService.validateApiKeyFormat('openai', '');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('API key is required');
    });

    it('should reject unknown provider', () => {
      const result = EncryptionService.validateApiKeyFormat('unknown', 'some-key');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Unknown provider');
    });
  });

  describe('maskApiKey', () => {
    it('should mask a long API key correctly', () => {
      const apiKey = 'sk-1234567890abcdef1234567890abcdef';
      const masked = EncryptionService.maskApiKey(apiKey);
      expect(masked).toBe('sk-1••••••••••••••••••••••••••••cdef');
    });

    it('should mask a short API key', () => {
      const apiKey = 'short';
      const masked = EncryptionService.maskApiKey(apiKey);
      expect(masked).toBe('••••••••');
    });

    it('should handle empty API key', () => {
      const masked = EncryptionService.maskApiKey('');
      expect(masked).toBe('••••••••');
    });
  });

  describe('generateMask', () => {
    it('should generate mask of specified length', () => {
      const mask = EncryptionService.generateMask(10);
      expect(mask).toHaveLength(10);
      expect(mask).toMatch(/^[A-Za-z0-9]+$/);
    });

    it('should generate different masks on subsequent calls', () => {
      const mask1 = EncryptionService.generateMask(10);
      const mask2 = EncryptionService.generateMask(10);
      expect(mask1).not.toBe(mask2);
    });
  });

  describe('clearMasterPassword', () => {
    it('should remove master key from localStorage', () => {
      EncryptionService.clearMasterPassword();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('vms_master_key');
    });
  });

  describe('encrypt and decrypt', () => {
    beforeEach(() => {
      // Mock successful crypto operations
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue({});
      mockCrypto.subtle.encrypt.mockResolvedValue(new ArrayBuffer(32));
      mockCrypto.subtle.decrypt.mockResolvedValue(new TextEncoder().encode('test'));
    });

    it('should encrypt and decrypt successfully', async () => {
      const plaintext = 'test-api-key';
      
      // Mock master password
      mockLocalStorage.getItem.mockReturnValue('master-password');
      
      const encrypted = await EncryptionService.encrypt(plaintext);
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
      
      const decrypted = await EncryptionService.decrypt(encrypted);
      expect(decrypted).toBe(plaintext);
    });

    it('should handle encryption errors', async () => {
      mockCrypto.subtle.encrypt.mockRejectedValue(new Error('Encryption failed'));
      
      await expect(EncryptionService.encrypt('test')).rejects.toThrow('Failed to encrypt data');
    });

    it('should handle decryption errors', async () => {
      mockCrypto.subtle.decrypt.mockRejectedValue(new Error('Decryption failed'));
      
      await expect(EncryptionService.decrypt('invalid')).rejects.toThrow('Failed to decrypt data');
    });
  });
});