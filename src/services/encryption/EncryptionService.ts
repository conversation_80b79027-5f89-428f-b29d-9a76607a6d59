/**
 * Encryption Service for secure API key storage
 * Uses Web Crypto API for client-side encryption
 */

export class EncryptionService {
  private static readonly ALGORITHM = 'AES-GCM';
  private static readonly KEY_LENGTH = 256;
  private static readonly IV_LENGTH = 12; // 96 bits for GCM

  /**
   * Generate a cryptographic key from a password
   */
  private static async deriveKey(password: string, salt: Uint8Array): Promise<CryptoKey> {
    const encoder = new TextEncoder();
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      encoder.encode(password),
      'PBKDF2',
      false,
      ['deriveKey']
    );

    return crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      {
        name: this.ALGORITHM,
        length: this.KEY_LENGTH
      },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Generate a random salt
   */
  private static generateSalt(): Uint8Array {
    return crypto.getRandomValues(new Uint8Array(16));
  }

  /**
   * Generate a random IV
   */
  private static generateIV(): Uint8Array {
    return crypto.getRandomValues(new Uint8Array(this.IV_LENGTH));
  }

  /**
   * Get or generate master password for encryption
   * In a real application, this would be derived from user authentication
   */
  private static getMasterPassword(): string {
    let masterPassword = localStorage.getItem('vms_master_key');
    if (!masterPassword) {
      // Generate a random master password and store it
      masterPassword = crypto.getRandomValues(new Uint32Array(8)).join('');
      localStorage.setItem('vms_master_key', masterPassword);
    }
    return masterPassword;
  }

  /**
   * Encrypt a string value
   */
  static async encrypt(plaintext: string): Promise<string> {
    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(plaintext);
      
      const salt = this.generateSalt();
      const iv = this.generateIV();
      const masterPassword = this.getMasterPassword();
      
      const key = await this.deriveKey(masterPassword, salt);
      
      const encrypted = await crypto.subtle.encrypt(
        {
          name: this.ALGORITHM,
          iv
        },
        key,
        data
      );

      // Combine salt, iv, and encrypted data
      const combined = new Uint8Array(salt.length + iv.length + encrypted.byteLength);
      combined.set(salt, 0);
      combined.set(iv, salt.length);
      combined.set(new Uint8Array(encrypted), salt.length + iv.length);

      // Convert to base64 for storage
      return btoa(String.fromCharCode(...combined));
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt a string value
   */
  static async decrypt(encryptedData: string): Promise<string> {
    try {
      // Convert from base64
      const combined = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      );

      // Extract salt, iv, and encrypted data
      const salt = combined.slice(0, 16);
      const iv = combined.slice(16, 16 + this.IV_LENGTH);
      const encrypted = combined.slice(16 + this.IV_LENGTH);

      const masterPassword = this.getMasterPassword();
      const key = await this.deriveKey(masterPassword, salt);

      const decrypted = await crypto.subtle.decrypt(
        {
          name: this.ALGORITHM,
          iv
        },
        key,
        encrypted
      );

      const decoder = new TextDecoder();
      return decoder.decode(decrypted);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Check if encryption is supported
   */
  static isSupported(): boolean {
    return (
      typeof crypto !== 'undefined' &&
      typeof crypto.subtle !== 'undefined' &&
      typeof crypto.subtle.encrypt === 'function' &&
      typeof crypto.subtle.decrypt === 'function'
    );
  }

  /**
   * Securely clear master password (for logout)
   */
  static clearMasterPassword(): void {
    localStorage.removeItem('vms_master_key');
  }

  /**
   * Generate a secure random string for API key masking
   */
  static generateMask(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Mask an API key for display purposes
   */
  static maskApiKey(apiKey: string): string {
    if (!apiKey || apiKey.length < 8) {
      return '••••••••';
    }
    
    const start = apiKey.substring(0, 4);
    const end = apiKey.substring(apiKey.length - 4);
    const middle = '•'.repeat(Math.max(8, apiKey.length - 8));
    
    return `${start}${middle}${end}`;
  }

  /**
   * Validate API key format for different providers
   */
  static validateApiKeyFormat(provider: string, apiKey: string): {
    isValid: boolean;
    error?: string;
  } {
    if (!apiKey || apiKey.trim().length === 0) {
      return { isValid: false, error: 'API key is required' };
    }

    const trimmedKey = apiKey.trim();

    switch (provider) {
      case 'openai':
        if (!trimmedKey.startsWith('sk-')) {
          return { isValid: false, error: 'OpenAI API keys must start with "sk-"' };
        }
        if (trimmedKey.length < 20) {
          return { isValid: false, error: 'OpenAI API key appears to be too short' };
        }
        break;

      case 'anthropic':
        if (!trimmedKey.startsWith('sk-ant-')) {
          return { isValid: false, error: 'Anthropic API keys must start with "sk-ant-"' };
        }
        if (trimmedKey.length < 20) {
          return { isValid: false, error: 'Anthropic API key appears to be too short' };
        }
        break;

      case 'gemini':
        if (trimmedKey.length < 10) {
          return { isValid: false, error: 'Google AI API key appears to be too short' };
        }
        // Google AI keys don't have a consistent prefix, so we just check length
        break;

      default:
        return { isValid: false, error: 'Unknown provider' };
    }

    return { isValid: true };
  }
}