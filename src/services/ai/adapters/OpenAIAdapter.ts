import OpenAI from 'openai';
import { BaseAIAdapter } from '../BaseAIAdapter';
import { 
  AIProvider, 
  AIProviderResponse,
  VendorData,
  RecommendationCriteria,
  VendorRecommendation,
  AIInsight
} from '../types';

export class OpenAIAdapter extends BaseAIAdapter {
  readonly provider: AIProvider = 'openai';
  private client: OpenAI | null = null;
  private tokensUsed = 0;

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const testClient = new OpenAI({ 
        apiKey,
        dangerouslyAllowBrowser: true // For client-side usage
      });

      // Test with a simple completion
      const response = await testClient.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Test' }],
        max_tokens: 5
      });

      return !!response.choices[0]?.message;
    } catch (error: any) {
      console.error('OpenAI API key validation failed:', error);
      return false;
    }
  }

  async generateVendorRecommendations(
    vendors: VendorData[],
    criteria: RecommendationCriteria
  ): Promise<AIProviderResponse<VendorRecommendation[]>> {
    if (!this.isConfigured() || !this.client) {
      return {
        success: false,
        error: 'OpenAI adapter not configured'
      };
    }

    try {
      await this.checkRateLimit();

      const prompt = this.buildRecommendationPrompt(vendors, criteria);
      
      const response = await this.retryWithBackoff(async () => {
        return await this.client!.chat.completions.create({
          model: this.config?.modelVersion || 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are an expert procurement analyst specializing in vendor evaluation and recommendation.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: this.config?.maxTokens || 2000,
          temperature: this.config?.temperature || 0.3,
          response_format: { type: 'json_object' }
        });
      });

      // Track token usage
      if (response.usage) {
        this.tokensUsed += response.usage.total_tokens;
      }

      const content = response.choices[0]?.message?.content;
      if (!content) {
        return {
          success: false,
          error: 'No response content from OpenAI'
        };
      }

      try {
        const parsed = JSON.parse(content);
        const recommendations: VendorRecommendation[] = parsed.recommendations?.map((rec: any) => {
          const vendor = vendors.find(v => v.id === rec.vendorId);
          if (!vendor) return null;

          return {
            vendor,
            score: rec.score,
            reasoning: rec.reasoning,
            confidence: rec.confidence,
            matchedCriteria: rec.matchedCriteria || [],
            risks: rec.risks || []
          };
        }).filter(Boolean) || [];

        return {
          success: true,
          data: recommendations,
          usage: response.usage ? {
            promptTokens: response.usage.prompt_tokens,
            completionTokens: response.usage.completion_tokens,
            totalTokens: response.usage.total_tokens
          } : undefined
        };
      } catch (parseError) {
        console.error('Failed to parse OpenAI response:', parseError);
        return {
          success: false,
          error: 'Failed to parse AI response'
        };
      }
    } catch (error: any) {
      return this.handleApiError(error);
    }
  }

  async generateInsights(
    data: Record<string, any>,
    insightType: AIInsight['type']
  ): Promise<AIProviderResponse<AIInsight>> {
    if (!this.isConfigured() || !this.client) {
      return {
        success: false,
        error: 'OpenAI adapter not configured'
      };
    }

    try {
      await this.checkRateLimit();

      const prompt = this.buildInsightPrompt(data, insightType);
      
      const response = await this.retryWithBackoff(async () => {
        return await this.client!.chat.completions.create({
          model: this.config?.modelVersion || 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are a business intelligence analyst specializing in vendor management insights.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: this.config?.maxTokens || 1000,
          temperature: this.config?.temperature || 0.3,
          response_format: { type: 'json_object' }
        });
      });

      if (response.usage) {
        this.tokensUsed += response.usage.total_tokens;
      }

      const content = response.choices[0]?.message?.content;
      if (!content) {
        return {
          success: false,
          error: 'No response content from OpenAI'
        };
      }

      try {
        const parsed = JSON.parse(content);
        const insight: AIInsight = {
          id: `openai-${Date.now()}`,
          type: insightType,
          title: parsed.title,
          description: parsed.description,
          confidence: parsed.confidence || 0.8,
          data: parsed.data || {},
          createdAt: new Date(),
          expiresAt: parsed.expiresAt ? new Date(parsed.expiresAt) : undefined
        };

        return {
          success: true,
          data: insight,
          usage: response.usage ? {
            promptTokens: response.usage.prompt_tokens,
            completionTokens: response.usage.completion_tokens,
            totalTokens: response.usage.total_tokens
          } : undefined
        };
      } catch (parseError) {
        console.error('Failed to parse OpenAI insight response:', parseError);
        return {
          success: false,
          error: 'Failed to parse AI response'
        };
      }
    } catch (error: any) {
      return this.handleApiError(error);
    }
  }

  async analyzeRisk(
    vendorData: VendorData,
    context?: Record<string, any>
  ): Promise<AIProviderResponse<{
    riskLevel: 'low' | 'medium' | 'high';
    factors: string[];
    recommendations: string[];
  }>> {
    if (!this.isConfigured() || !this.client) {
      return {
        success: false,
        error: 'OpenAI adapter not configured'
      };
    }

    try {
      await this.checkRateLimit();

      const prompt = this.buildRiskAnalysisPrompt(vendorData, context);
      
      const response = await this.retryWithBackoff(async () => {
        return await this.client!.chat.completions.create({
          model: this.config?.modelVersion || 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are a risk assessment specialist with expertise in vendor risk analysis and supply chain management.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: this.config?.maxTokens || 800,
          temperature: this.config?.temperature || 0.2,
          response_format: { type: 'json_object' }
        });
      });

      if (response.usage) {
        this.tokensUsed += response.usage.total_tokens;
      }

      const content = response.choices[0]?.message?.content;
      if (!content) {
        return {
          success: false,
          error: 'No response content from OpenAI'
        };
      }

      try {
        const parsed = JSON.parse(content);
        
        return {
          success: true,
          data: {
            riskLevel: parsed.riskLevel,
            factors: parsed.factors || [],
            recommendations: parsed.recommendations || []
          },
          usage: response.usage ? {
            promptTokens: response.usage.prompt_tokens,
            completionTokens: response.usage.completion_tokens,
            totalTokens: response.usage.total_tokens
          } : undefined
        };
      } catch (parseError) {
        console.error('Failed to parse OpenAI risk response:', parseError);
        return {
          success: false,
          error: 'Failed to parse AI response'
        };
      }
    } catch (error: any) {
      return this.handleApiError(error);
    }
  }

  async getUsageStats(): Promise<{
    requestsToday: number;
    tokensUsed: number;
    remainingQuota?: number;
  }> {
    const baseStats = await super.getUsageStats();
    return {
      ...baseStats,
      tokensUsed: this.tokensUsed
    };
  }

  protected async configure(config: any): Promise<boolean> {
    const success = await super.configure(config);
    if (success) {
      this.client = new OpenAI({ 
        apiKey: config.apiKey,
        dangerouslyAllowBrowser: true
      });
    }
    return success;
  }

  private buildInsightPrompt(data: Record<string, any>, insightType: AIInsight['type']): string {
    const dataStr = JSON.stringify(data, null, 2);
    
    return `
Analyze the following vendor management data and generate a ${insightType} insight:

DATA:
${dataStr}

Please provide your analysis in JSON format:
{
  "title": "Brief insight title",
  "description": "Detailed insight description with actionable information",
  "confidence": number (0-1),
  "data": {
    "key_metrics": {},
    "trends": [],
    "recommendations": []
  }
}

Focus on actionable insights that can help improve vendor management processes.
`;
  }
}