import { GoogleGenerativeA<PERSON>, GenerativeModel } from '@google/generative-ai';
import { BaseAIAdapter } from '../BaseAIAdapter';
import { 
  AIProvider, 
  AIProviderResponse,
  VendorData,
  RecommendationCriteria,
  VendorRecommendation,
  AIInsight
} from '../types';

export class <PERSON><PERSON><PERSON><PERSON>er extends BaseAIAdapter {
  readonly provider: AIProvider = 'gemini';
  private client: GoogleGenerativeAI | null = null;
  private model: GenerativeModel | null = null;
  private tokensUsed = 0;

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const testClient = new GoogleGenerativeAI(apiKey);
      const testModel = testClient.getGenerativeModel({ model: 'gemini-pro' });

      // Test with a simple prompt
      const result = await testModel.generateContent('Test');
      const response = await result.response;
      
      return !!response.text();
    } catch (error: any) {
      console.error('Gemini API key validation failed:', error);
      return false;
    }
  }

  async generateVendorRecommendations(
    vendors: VendorData[],
    criteria: RecommendationCriteria
  ): Promise<AIProviderResponse<VendorRecommendation[]>> {
    if (!this.isConfigured() || !this.model) {
      return {
        success: false,
        error: 'Gemini adapter not configured'
      };
    }

    try {
      await this.checkRateLimit();

      const prompt = this.buildGeminiRecommendationPrompt(vendors, criteria);
      
      const result = await this.retryWithBackoff(async () => {
        return await this.model!.generateContent(prompt);
      });

      const response = await result.response;
      const content = response.text();

      if (!content) {
        return {
          success: false,
          error: 'No content from Gemini'
        };
      }

      try {
        // Extract JSON from Gemini's response
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          return {
            success: false,
            error: 'No JSON found in Gemini response'
          };
        }

        const parsed = JSON.parse(jsonMatch[0]);
        const recommendations: VendorRecommendation[] = parsed.recommendations?.map((rec: any) => {
          const vendor = vendors.find(v => v.id === rec.vendorId);
          if (!vendor) return null;

          return {
            vendor,
            score: rec.score,
            reasoning: rec.reasoning,
            confidence: rec.confidence,
            matchedCriteria: rec.matchedCriteria || [],
            risks: rec.risks || []
          };
        }).filter(Boolean) || [];

        // Estimate token usage (Gemini doesn't provide exact counts)
        this.tokensUsed += Math.ceil((prompt.length + content.length) / 4);

        return {
          success: true,
          data: recommendations,
          usage: {
            promptTokens: Math.ceil(prompt.length / 4),
            completionTokens: Math.ceil(content.length / 4),
            totalTokens: Math.ceil((prompt.length + content.length) / 4)
          }
        };
      } catch (parseError) {
        console.error('Failed to parse Gemini response:', parseError);
        return {
          success: false,
          error: 'Failed to parse AI response'
        };
      }
    } catch (error: any) {
      return this.handleApiError(error);
    }
  }

  async generateInsights(
    data: Record<string, any>,
    insightType: AIInsight['type']
  ): Promise<AIProviderResponse<AIInsight>> {
    if (!this.isConfigured() || !this.model) {
      return {
        success: false,
        error: 'Gemini adapter not configured'
      };
    }

    try {
      await this.checkRateLimit();

      const prompt = this.buildGeminiInsightPrompt(data, insightType);
      
      const result = await this.retryWithBackoff(async () => {
        return await this.model!.generateContent(prompt);
      });

      const response = await result.response;
      const content = response.text();

      if (!content) {
        return {
          success: false,
          error: 'No content from Gemini'
        };
      }

      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          return {
            success: false,
            error: 'No JSON found in Gemini response'
          };
        }

        const parsed = JSON.parse(jsonMatch[0]);
        const insight: AIInsight = {
          id: `gemini-${Date.now()}`,
          type: insightType,
          title: parsed.title,
          description: parsed.description,
          confidence: parsed.confidence || 0.8,
          data: parsed.data || {},
          createdAt: new Date(),
          expiresAt: parsed.expiresAt ? new Date(parsed.expiresAt) : undefined
        };

        this.tokensUsed += Math.ceil((prompt.length + content.length) / 4);

        return {
          success: true,
          data: insight,
          usage: {
            promptTokens: Math.ceil(prompt.length / 4),
            completionTokens: Math.ceil(content.length / 4),
            totalTokens: Math.ceil((prompt.length + content.length) / 4)
          }
        };
      } catch (parseError) {
        console.error('Failed to parse Gemini insight response:', parseError);
        return {
          success: false,
          error: 'Failed to parse AI response'
        };
      }
    } catch (error: any) {
      return this.handleApiError(error);
    }
  }

  async analyzeRisk(
    vendorData: VendorData,
    context?: Record<string, any>
  ): Promise<AIProviderResponse<{
    riskLevel: 'low' | 'medium' | 'high';
    factors: string[];
    recommendations: string[];
  }>> {
    if (!this.isConfigured() || !this.model) {
      return {
        success: false,
        error: 'Gemini adapter not configured'
      };
    }

    try {
      await this.checkRateLimit();

      const prompt = this.buildGeminiRiskPrompt(vendorData, context);
      
      const result = await this.retryWithBackoff(async () => {
        return await this.model!.generateContent(prompt);
      });

      const response = await result.response;
      const content = response.text();

      if (!content) {
        return {
          success: false,
          error: 'No content from Gemini'
        };
      }

      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          return {
            success: false,
            error: 'No JSON found in Gemini response'
          };
        }

        const parsed = JSON.parse(jsonMatch[0]);
        
        this.tokensUsed += Math.ceil((prompt.length + content.length) / 4);

        return {
          success: true,
          data: {
            riskLevel: parsed.riskLevel,
            factors: parsed.factors || [],
            recommendations: parsed.recommendations || []
          },
          usage: {
            promptTokens: Math.ceil(prompt.length / 4),
            completionTokens: Math.ceil(content.length / 4),
            totalTokens: Math.ceil((prompt.length + content.length) / 4)
          }
        };
      } catch (parseError) {
        console.error('Failed to parse Gemini risk response:', parseError);
        return {
          success: false,
          error: 'Failed to parse AI response'
        };
      }
    } catch (error: any) {
      return this.handleApiError(error);
    }
  }

  async getUsageStats(): Promise<{
    requestsToday: number;
    tokensUsed: number;
    remainingQuota?: number;
  }> {
    const baseStats = await super.getUsageStats();
    return {
      ...baseStats,
      tokensUsed: this.tokensUsed
    };
  }

  protected async configure(config: any): Promise<boolean> {
    const success = await super.configure(config);
    if (success) {
      this.client = new GoogleGenerativeAI(config.apiKey);
      this.model = this.client.getGenerativeModel({ 
        model: config.modelVersion || 'gemini-pro'
      });
    }
    return success;
  }

  // Gemini-specific prompt builders
  private buildGeminiRecommendationPrompt(vendors: VendorData[], criteria: RecommendationCriteria): string {
    const criteriaText = Object.entries(criteria)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');

    return `You are a procurement expert. Analyze these vendors and provide recommendations based on the given criteria.

SELECTION CRITERIA:
${criteriaText}

VENDOR PROFILES:
${vendors.map(vendor => `
Vendor ID: ${vendor.id}
Name: ${vendor.name}
Category: ${vendor.category}
Performance Score: ${vendor.performanceScore}/100
Contract History: ${vendor.contractHistory.totalContracts} contracts (${vendor.contractHistory.completedOnTime} completed on time)
Compliance Score: ${vendor.complianceRecords.score}/100 (${vendor.complianceRecords.violations} violations)
Cost: $${vendor.costEffectiveness.averageCost} (${vendor.costEffectiveness.costTrend} trend)
Location: ${vendor.location}
Certifications: ${vendor.certifications.join(', ')}
`).join('')}

Provide your analysis in this exact JSON format:
{
  "recommendations": [
    {
      "vendorId": "vendor_id",
      "score": score_0_to_100,
      "reasoning": "detailed_explanation",
      "confidence": confidence_0_to_1,
      "matchedCriteria": ["criterion1", "criterion2"],
      "risks": ["risk1", "risk2"]
    }
  ]
}

Rank vendors by how well they meet the criteria, considering performance, compliance, cost, and risk factors.`;
  }

  private buildGeminiInsightPrompt(data: Record<string, any>, insightType: AIInsight['type']): string {
    const dataStr = JSON.stringify(data, null, 2);
    
    return `Analyze this vendor management data and generate a ${insightType} insight.

DATA TO ANALYZE:
${dataStr}

Provide your analysis in this JSON format:
{
  "title": "insight_title",
  "description": "detailed_insight_description",
  "confidence": confidence_0_to_1,
  "data": {
    "key_metrics": {},
    "trends": [],
    "recommendations": []
  }
}

Focus on actionable insights that can improve vendor management and decision-making processes.`;
  }

  private buildGeminiRiskPrompt(vendorData: VendorData, context?: Record<string, any>): string {
    const contextText = context ? 
      `\nAdditional Context:\n${Object.entries(context).map(([k, v]) => `${k}: ${v}`).join('\n')}` : '';

    return `Assess the risk profile of this vendor:

VENDOR DETAILS:
Name: ${vendorData.name}
Category: ${vendorData.category}
Performance Score: ${vendorData.performanceScore}/100
Compliance Score: ${vendorData.complianceRecords.score}/100
Violations: ${vendorData.complianceRecords.violations}
Cost Trend: ${vendorData.costEffectiveness.costTrend}
On-time Delivery: ${vendorData.contractHistory.completedOnTime}/${vendorData.contractHistory.totalContracts}${contextText}

Provide risk assessment in this JSON format:
{
  "riskLevel": "low|medium|high",
  "factors": ["risk_factor_1", "risk_factor_2"],
  "recommendations": ["mitigation_1", "mitigation_2"]
}

Consider all aspects: performance, compliance, financial stability, and operational risks.`;
  }
}