import { OpenAIAdapter } from '../OpenAIAdapter';
import { VendorData, RecommendationCriteria } from '../../types';

// Mock OpenAI
jest.mock('openai', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn()
        }
      }
    }))
  };
});

describe('OpenAIAdapter', () => {
  let adapter: OpenAIAdapter;
  let mockOpenAI: any;

  beforeEach(() => {
    adapter = new OpenAIAdapter();
    mockOpenAI = require('openai').default;
    jest.clearAllMocks();
  });

  describe('provider', () => {
    it('should return openai as provider', () => {
      expect(adapter.provider).toBe('openai');
    });
  });

  describe('validateApiKey', () => {
    it('should return true for valid API key', async () => {
      const mockCreate = jest.fn().mockResolvedValue({
        choices: [{ message: { content: 'test' } }]
      });
      
      mockOpenAI.mockImplementation(() => ({
        chat: { completions: { create: mockCreate } }
      }));

      const result = await adapter.validateApiKey('valid-key');
      expect(result).toBe(true);
      expect(mockCreate).toHaveBeenCalledWith({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Test' }],
        max_tokens: 5
      });
    });

    it('should return false for invalid API key', async () => {
      const mockCreate = jest.fn().mockRejectedValue(new Error('Invalid API key'));
      
      mockOpenAI.mockImplementation(() => ({
        chat: { completions: { create: mockCreate } }
      }));

      const result = await adapter.validateApiKey('invalid-key');
      expect(result).toBe(false);
    });
  });

  describe('generateVendorRecommendations', () => {
    const mockVendors: VendorData[] = [
      {
        id: '1',
        name: 'Test Vendor',
        category: 'IT Services',
        performanceScore: 85,
        contractHistory: {
          totalContracts: 10,
          completedOnTime: 8,
          averageValue: 50000
        },
        complianceRecords: {
          score: 90,
          lastAudit: new Date(),
          violations: 0
        },
        costEffectiveness: {
          averageCost: 45000,
          costTrend: 'stable'
        },
        location: 'New York',
        certifications: ['ISO 9001']
      }
    ];

    const mockCriteria: RecommendationCriteria = {
      category: 'IT Services',
      maxBudget: 60000
    };

    it('should return error when not configured', async () => {
      const result = await adapter.generateVendorRecommendations(mockVendors, mockCriteria);
      expect(result.success).toBe(false);
      expect(result.error).toBe('OpenAI adapter not configured');
    });

    it('should generate recommendations when configured', async () => {
      // Configure adapter
      const mockCreate = jest.fn()
        .mockResolvedValueOnce({
          choices: [{ message: { content: 'test' } }]
        })
        .mockResolvedValueOnce({
          choices: [{
            message: {
              content: JSON.stringify({
                recommendations: [{
                  vendorId: '1',
                  score: 85,
                  reasoning: 'Good performance',
                  confidence: 0.9,
                  matchedCriteria: ['category'],
                  risks: []
                }]
              })
            }
          }],
          usage: {
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150
          }
        });

      mockOpenAI.mockImplementation(() => ({
        chat: { completions: { create: mockCreate } }
      }));

      await adapter.configure({
        provider: 'openai',
        apiKey: 'test-key'
      });

      const result = await adapter.generateVendorRecommendations(mockVendors, mockCriteria);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data![0].vendor.id).toBe('1');
      expect(result.data![0].score).toBe(85);
      expect(result.usage).toBeDefined();
    });

    it('should handle JSON parsing errors', async () => {
      const mockCreate = jest.fn()
        .mockResolvedValueOnce({
          choices: [{ message: { content: 'test' } }]
        })
        .mockResolvedValueOnce({
          choices: [{
            message: { content: 'invalid json' }
          }]
        });

      mockOpenAI.mockImplementation(() => ({
        chat: { completions: { create: mockCreate } }
      }));

      await adapter.configure({
        provider: 'openai',
        apiKey: 'test-key'
      });

      const result = await adapter.generateVendorRecommendations(mockVendors, mockCriteria);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to parse AI response');
    });
  });

  describe('analyzeRisk', () => {
    const mockVendor: VendorData = {
      id: '1',
      name: 'Test Vendor',
      category: 'IT Services',
      performanceScore: 85,
      contractHistory: {
        totalContracts: 10,
        completedOnTime: 8,
        averageValue: 50000
      },
      complianceRecords: {
        score: 90,
        lastAudit: new Date(),
        violations: 0
      },
      costEffectiveness: {
        averageCost: 45000,
        costTrend: 'stable'
      },
      location: 'New York',
      certifications: ['ISO 9001']
    };

    it('should return error when not configured', async () => {
      const result = await adapter.analyzeRisk(mockVendor);
      expect(result.success).toBe(false);
      expect(result.error).toBe('OpenAI adapter not configured');
    });

    it('should analyze risk when configured', async () => {
      const mockCreate = jest.fn()
        .mockResolvedValueOnce({
          choices: [{ message: { content: 'test' } }]
        })
        .mockResolvedValueOnce({
          choices: [{
            message: {
              content: JSON.stringify({
                riskLevel: 'low',
                factors: ['Good performance history'],
                recommendations: ['Continue monitoring']
              })
            }
          }],
          usage: {
            prompt_tokens: 80,
            completion_tokens: 30,
            total_tokens: 110
          }
        });

      mockOpenAI.mockImplementation(() => ({
        chat: { completions: { create: mockCreate } }
      }));

      await adapter.configure({
        provider: 'openai',
        apiKey: 'test-key'
      });

      const result = await adapter.analyzeRisk(mockVendor);
      
      expect(result.success).toBe(true);
      expect(result.data?.riskLevel).toBe('low');
      expect(result.data?.factors).toContain('Good performance history');
      expect(result.usage).toBeDefined();
    });
  });

  describe('getUsageStats', () => {
    it('should return usage statistics', async () => {
      const stats = await adapter.getUsageStats();
      expect(stats).toHaveProperty('requestsToday');
      expect(stats).toHaveProperty('tokensUsed');
      expect(typeof stats.tokensUsed).toBe('number');
    });
  });
});