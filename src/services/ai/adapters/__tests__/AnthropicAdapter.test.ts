import { AnthropicAdapter } from '../AnthropicAdapter';
import { VendorData, RecommendationCriteria } from '../../types';

// Mock Anthropic
jest.mock('@anthropic-ai/sdk', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      messages: {
        create: jest.fn()
      }
    }))
  };
});

describe('AnthropicAdapter', () => {
  let adapter: AnthropicAdapter;
  let mockAnthropic: any;

  beforeEach(() => {
    adapter = new AnthropicAdapter();
    mockAnthropic = require('@anthropic-ai/sdk').default;
    jest.clearAllMocks();
  });

  describe('provider', () => {
    it('should return anthropic as provider', () => {
      expect(adapter.provider).toBe('anthropic');
    });
  });

  describe('validateApiKey', () => {
    it('should return true for valid API key', async () => {
      const mockCreate = jest.fn().mockResolvedValue({
        content: [{ type: 'text', text: 'test response' }]
      });
      
      mockAnthropic.mockImplementation(() => ({
        messages: { create: mockCreate }
      }));

      const result = await adapter.validateApiKey('valid-key');
      expect(result).toBe(true);
      expect(mockCreate).toHaveBeenCalledWith({
        model: 'claude-3-haiku-20240307',
        max_tokens: 10,
        messages: [{ role: 'user', content: 'Test' }]
      });
    });

    it('should return false for invalid API key', async () => {
      const mockCreate = jest.fn().mockRejectedValue(new Error('Invalid API key'));
      
      mockAnthropic.mockImplementation(() => ({
        messages: { create: mockCreate }
      }));

      const result = await adapter.validateApiKey('invalid-key');
      expect(result).toBe(false);
    });
  });

  describe('generateVendorRecommendations', () => {
    const mockVendors: VendorData[] = [
      {
        id: '1',
        name: 'Test Vendor',
        category: 'IT Services',
        performanceScore: 85,
        contractHistory: {
          totalContracts: 10,
          completedOnTime: 8,
          averageValue: 50000
        },
        complianceRecords: {
          score: 90,
          lastAudit: new Date(),
          violations: 0
        },
        costEffectiveness: {
          averageCost: 45000,
          costTrend: 'stable'
        },
        location: 'New York',
        certifications: ['ISO 9001']
      }
    ];

    const mockCriteria: RecommendationCriteria = {
      category: 'IT Services',
      maxBudget: 60000
    };

    it('should return error when not configured', async () => {
      const result = await adapter.generateVendorRecommendations(mockVendors, mockCriteria);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Anthropic adapter not configured');
    });

    it('should generate recommendations when configured', async () => {
      // Configure adapter
      const mockCreate = jest.fn()
        .mockResolvedValueOnce({
          content: [{ type: 'text', text: 'test response' }]
        })
        .mockResolvedValueOnce({
          content: [{
            type: 'text',
            text: `Here's my analysis: ${JSON.stringify({
              recommendations: [{
                vendorId: '1',
                score: 85,
                reasoning: 'Good performance and compliance',
                confidence: 0.9,
                matchedCriteria: ['category', 'budget'],
                risks: []
              }]
            })}`
          }],
          usage: {
            input_tokens: 100,
            output_tokens: 50
          }
        });

      mockAnthropic.mockImplementation(() => ({
        messages: { create: mockCreate }
      }));

      await adapter.configure({
        provider: 'anthropic',
        apiKey: 'test-key'
      });

      const result = await adapter.generateVendorRecommendations(mockVendors, mockCriteria);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data![0].vendor.id).toBe('1');
      expect(result.data![0].score).toBe(85);
      expect(result.usage).toBeDefined();
      expect(result.usage!.totalTokens).toBe(150);
    });

    it('should handle responses without JSON', async () => {
      const mockCreate = jest.fn()
        .mockResolvedValueOnce({
          content: [{ type: 'text', text: 'test response' }]
        })
        .mockResolvedValueOnce({
          content: [{
            type: 'text',
            text: 'This is just text without JSON'
          }]
        });

      mockAnthropic.mockImplementation(() => ({
        messages: { create: mockCreate }
      }));

      await adapter.configure({
        provider: 'anthropic',
        apiKey: 'test-key'
      });

      const result = await adapter.generateVendorRecommendations(mockVendors, mockCriteria);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('No JSON found in Anthropic response');
    });

    it('should handle invalid JSON in response', async () => {
      const mockCreate = jest.fn()
        .mockResolvedValueOnce({
          content: [{ type: 'text', text: 'test response' }]
        })
        .mockResolvedValueOnce({
          content: [{
            type: 'text',
            text: 'Here is the analysis: { invalid json }'
          }]
        });

      mockAnthropic.mockImplementation(() => ({
        messages: { create: mockCreate }
      }));

      await adapter.configure({
        provider: 'anthropic',
        apiKey: 'test-key'
      });

      const result = await adapter.generateVendorRecommendations(mockVendors, mockCriteria);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to parse AI response');
    });
  });

  describe('analyzeRisk', () => {
    const mockVendor: VendorData = {
      id: '1',
      name: 'Test Vendor',
      category: 'IT Services',
      performanceScore: 85,
      contractHistory: {
        totalContracts: 10,
        completedOnTime: 8,
        averageValue: 50000
      },
      complianceRecords: {
        score: 90,
        lastAudit: new Date(),
        violations: 0
      },
      costEffectiveness: {
        averageCost: 45000,
        costTrend: 'stable'
      },
      location: 'New York',
      certifications: ['ISO 9001']
    };

    it('should return error when not configured', async () => {
      const result = await adapter.analyzeRisk(mockVendor);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Anthropic adapter not configured');
    });

    it('should analyze risk when configured', async () => {
      const mockCreate = jest.fn()
        .mockResolvedValueOnce({
          content: [{ type: 'text', text: 'test response' }]
        })
        .mockResolvedValueOnce({
          content: [{
            type: 'text',
            text: `Risk analysis: ${JSON.stringify({
              riskLevel: 'low',
              factors: ['Good performance history', 'Strong compliance'],
              recommendations: ['Continue standard monitoring', 'Review annually']
            })}`
          }],
          usage: {
            input_tokens: 80,
            output_tokens: 30
          }
        });

      mockAnthropic.mockImplementation(() => ({
        messages: { create: mockCreate }
      }));

      await adapter.configure({
        provider: 'anthropic',
        apiKey: 'test-key'
      });

      const result = await adapter.analyzeRisk(mockVendor);
      
      expect(result.success).toBe(true);
      expect(result.data?.riskLevel).toBe('low');
      expect(result.data?.factors).toContain('Good performance history');
      expect(result.data?.recommendations).toContain('Continue standard monitoring');
      expect(result.usage).toBeDefined();
    });
  });

  describe('generateInsights', () => {
    const mockData = {
      totalVendors: 50,
      averagePerformance: 82,
      complianceIssues: 3
    };

    it('should return error when not configured', async () => {
      const result = await adapter.generateInsights(mockData, 'trend');
      expect(result.success).toBe(false);
      expect(result.error).toBe('Anthropic adapter not configured');
    });

    it('should generate insights when configured', async () => {
      const mockCreate = jest.fn()
        .mockResolvedValueOnce({
          content: [{ type: 'text', text: 'test response' }]
        })
        .mockResolvedValueOnce({
          content: [{
            type: 'text',
            text: `Analysis results: ${JSON.stringify({
              title: 'Vendor Performance Trend',
              description: 'Overall vendor performance is improving',
              confidence: 0.85,
              data: {
                key_metrics: { improvement: '5%' },
                trends: ['upward'],
                recommendations: ['Continue monitoring']
              }
            })}`
          }],
          usage: {
            input_tokens: 60,
            output_tokens: 40
          }
        });

      mockAnthropic.mockImplementation(() => ({
        messages: { create: mockCreate }
      }));

      await adapter.configure({
        provider: 'anthropic',
        apiKey: 'test-key'
      });

      const result = await adapter.generateInsights(mockData, 'trend');
      
      expect(result.success).toBe(true);
      expect(result.data?.title).toBe('Vendor Performance Trend');
      expect(result.data?.type).toBe('trend');
      expect(result.data?.confidence).toBe(0.85);
      expect(result.usage).toBeDefined();
    });
  });

  describe('getUsageStats', () => {
    it('should return usage statistics', async () => {
      const stats = await adapter.getUsageStats();
      expect(stats).toHaveProperty('requestsToday');
      expect(stats).toHaveProperty('tokensUsed');
      expect(typeof stats.tokensUsed).toBe('number');
    });
  });
});