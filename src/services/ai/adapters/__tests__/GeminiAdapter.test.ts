import { GeminiAdapter } from '../GeminiAdapter';
import { VendorData, RecommendationCriteria } from '../../types';

// Mock Google Generative AI
jest.mock('@google/generative-ai', () => {
  return {
    GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
      getGenerativeModel: jest.fn().mockReturnValue({
        generateContent: jest.fn()
      })
    }))
  };
});

describe('GeminiAdapter', () => {
  let adapter: GeminiAdapter;
  let mockGoogleAI: any;
  let mockModel: any;

  beforeEach(() => {
    adapter = new GeminiAdapter();
    mockGoogleAI = require('@google/generative-ai').GoogleGenerativeAI;
    mockModel = {
      generateContent: jest.fn()
    };
    mockGoogleAI.mockImplementation(() => ({
      getGenerativeModel: jest.fn().mockReturnValue(mockModel)
    }));
    jest.clearAllMocks();
  });

  describe('provider', () => {
    it('should return gemini as provider', () => {
      expect(adapter.provider).toBe('gemini');
    });
  });

  describe('validateApiKey', () => {
    it('should return true for valid API key', async () => {
      const mockResult = {
        response: Promise.resolve({
          text: () => 'test response'
        })
      };
      
      mockModel.generateContent.mockResolvedValue(mockResult);

      const result = await adapter.validateApiKey('valid-key');
      expect(result).toBe(true);
      expect(mockModel.generateContent).toHaveBeenCalledWith('Test');
    });

    it('should return false for invalid API key', async () => {
      mockModel.generateContent.mockRejectedValue(new Error('Invalid API key'));

      const result = await adapter.validateApiKey('invalid-key');
      expect(result).toBe(false);
    });
  });

  describe('generateVendorRecommendations', () => {
    const mockVendors: VendorData[] = [
      {
        id: '1',
        name: 'Test Vendor',
        category: 'IT Services',
        performanceScore: 85,
        contractHistory: {
          totalContracts: 10,
          completedOnTime: 8,
          averageValue: 50000
        },
        complianceRecords: {
          score: 90,
          lastAudit: new Date(),
          violations: 0
        },
        costEffectiveness: {
          averageCost: 45000,
          costTrend: 'stable'
        },
        location: 'New York',
        certifications: ['ISO 9001']
      }
    ];

    const mockCriteria: RecommendationCriteria = {
      category: 'IT Services',
      maxBudget: 60000
    };

    it('should return error when not configured', async () => {
      const result = await adapter.generateVendorRecommendations(mockVendors, mockCriteria);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Gemini adapter not configured');
    });

    it('should generate recommendations when configured', async () => {
      // Configure adapter first
      const validationResult = {
        response: Promise.resolve({
          text: () => 'test response'
        })
      };
      
      const recommendationResult = {
        response: Promise.resolve({
          text: () => `Analysis complete: ${JSON.stringify({
            recommendations: [{
              vendorId: '1',
              score: 85,
              reasoning: 'Strong performance and compliance record',
              confidence: 0.9,
              matchedCriteria: ['category', 'budget'],
              risks: []
            }]
          })}`
        })
      };

      mockModel.generateContent
        .mockResolvedValueOnce(validationResult)
        .mockResolvedValueOnce(recommendationResult);

      await adapter.configure({
        provider: 'gemini',
        apiKey: 'test-key'
      });

      const result = await adapter.generateVendorRecommendations(mockVendors, mockCriteria);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data![0].vendor.id).toBe('1');
      expect(result.data![0].score).toBe(85);
      expect(result.usage).toBeDefined();
    });

    it('should handle responses without JSON', async () => {
      const validationResult = {
        response: Promise.resolve({
          text: () => 'test response'
        })
      };
      
      const recommendationResult = {
        response: Promise.resolve({
          text: () => 'This is just text without JSON'
        })
      };

      mockModel.generateContent
        .mockResolvedValueOnce(validationResult)
        .mockResolvedValueOnce(recommendationResult);

      await adapter.configure({
        provider: 'gemini',
        apiKey: 'test-key'
      });

      const result = await adapter.generateVendorRecommendations(mockVendors, mockCriteria);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('No JSON found in Gemini response');
    });

    it('should handle invalid JSON in response', async () => {
      const validationResult = {
        response: Promise.resolve({
          text: () => 'test response'
        })
      };
      
      const recommendationResult = {
        response: Promise.resolve({
          text: () => 'Here is the analysis: { invalid json }'
        })
      };

      mockModel.generateContent
        .mockResolvedValueOnce(validationResult)
        .mockResolvedValueOnce(recommendationResult);

      await adapter.configure({
        provider: 'gemini',
        apiKey: 'test-key'
      });

      const result = await adapter.generateVendorRecommendations(mockVendors, mockCriteria);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to parse AI response');
    });
  });

  describe('analyzeRisk', () => {
    const mockVendor: VendorData = {
      id: '1',
      name: 'Test Vendor',
      category: 'IT Services',
      performanceScore: 85,
      contractHistory: {
        totalContracts: 10,
        completedOnTime: 8,
        averageValue: 50000
      },
      complianceRecords: {
        score: 90,
        lastAudit: new Date(),
        violations: 0
      },
      costEffectiveness: {
        averageCost: 45000,
        costTrend: 'stable'
      },
      location: 'New York',
      certifications: ['ISO 9001']
    };

    it('should return error when not configured', async () => {
      const result = await adapter.analyzeRisk(mockVendor);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Gemini adapter not configured');
    });

    it('should analyze risk when configured', async () => {
      const validationResult = {
        response: Promise.resolve({
          text: () => 'test response'
        })
      };
      
      const riskResult = {
        response: Promise.resolve({
          text: () => `Risk assessment: ${JSON.stringify({
            riskLevel: 'low',
            factors: ['Strong performance history', 'Good compliance record'],
            recommendations: ['Continue regular monitoring', 'Annual review recommended']
          })}`
        })
      };

      mockModel.generateContent
        .mockResolvedValueOnce(validationResult)
        .mockResolvedValueOnce(riskResult);

      await adapter.configure({
        provider: 'gemini',
        apiKey: 'test-key'
      });

      const result = await adapter.analyzeRisk(mockVendor);
      
      expect(result.success).toBe(true);
      expect(result.data?.riskLevel).toBe('low');
      expect(result.data?.factors).toContain('Strong performance history');
      expect(result.data?.recommendations).toContain('Continue regular monitoring');
      expect(result.usage).toBeDefined();
    });
  });

  describe('generateInsights', () => {
    const mockData = {
      totalVendors: 50,
      averagePerformance: 82,
      complianceIssues: 3
    };

    it('should return error when not configured', async () => {
      const result = await adapter.generateInsights(mockData, 'trend');
      expect(result.success).toBe(false);
      expect(result.error).toBe('Gemini adapter not configured');
    });

    it('should generate insights when configured', async () => {
      const validationResult = {
        response: Promise.resolve({
          text: () => 'test response'
        })
      };
      
      const insightResult = {
        response: Promise.resolve({
          text: () => `Insight analysis: ${JSON.stringify({
            title: 'Vendor Performance Trends',
            description: 'Overall vendor performance shows positive trends',
            confidence: 0.85,
            data: {
              key_metrics: { improvement: '5%' },
              trends: ['performance_improving'],
              recommendations: ['Continue current strategy']
            }
          })}`
        })
      };

      mockModel.generateContent
        .mockResolvedValueOnce(validationResult)
        .mockResolvedValueOnce(insightResult);

      await adapter.configure({
        provider: 'gemini',
        apiKey: 'test-key'
      });

      const result = await adapter.generateInsights(mockData, 'trend');
      
      expect(result.success).toBe(true);
      expect(result.data?.title).toBe('Vendor Performance Trends');
      expect(result.data?.type).toBe('trend');
      expect(result.data?.confidence).toBe(0.85);
      expect(result.usage).toBeDefined();
    });
  });

  describe('getUsageStats', () => {
    it('should return usage statistics', async () => {
      const stats = await adapter.getUsageStats();
      expect(stats).toHaveProperty('requestsToday');
      expect(stats).toHaveProperty('tokensUsed');
      expect(typeof stats.tokensUsed).toBe('number');
    });
  });
});