import Anthropic from '@anthropic-ai/sdk';
import { BaseAIAdapter } from '../BaseAIAdapter';
import { 
  AIProvider, 
  AIProviderResponse,
  VendorData,
  RecommendationCriteria,
  VendorRecommendation,
  AIInsight
} from '../types';

export class AnthropicAdapter extends BaseAIAdapter {
  readonly provider: AIProvider = 'anthropic';
  private client: Anthropic | null = null;
  private tokensUsed = 0;

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const testClient = new Anthropic({ 
        apiKey,
        dangerouslyAllowBrowser: true // For client-side usage
      });

      // Test with a simple message
      const response = await testClient.messages.create({
        model: 'claude-3-haiku-20240307',
        max_tokens: 10,
        messages: [{ role: 'user', content: 'Test' }]
      });

      return !!response.content[0];
    } catch (error: any) {
      console.error('Anthropic API key validation failed:', error);
      return false;
    }
  }

  async generateVendorRecommendations(
    vendors: VendorData[],
    criteria: RecommendationCriteria
  ): Promise<AIProviderResponse<VendorRecommendation[]>> {
    if (!this.isConfigured() || !this.client) {
      return {
        success: false,
        error: 'Anthropic adapter not configured'
      };
    }

    try {
      await this.checkRateLimit();

      const prompt = this.buildAnthropicRecommendationPrompt(vendors, criteria);
      
      const response = await this.retryWithBackoff(async () => {
        return await this.client!.messages.create({
          model: this.config?.modelVersion || 'claude-3-sonnet-20240229',
          max_tokens: this.config?.maxTokens || 2000,
          temperature: this.config?.temperature || 0.3,
          messages: [{ role: 'user', content: prompt }]
        });
      });

      // Track token usage
      if (response.usage) {
        this.tokensUsed += response.usage.input_tokens + response.usage.output_tokens;
      }

      const content = response.content[0];
      if (!content || content.type !== 'text') {
        return {
          success: false,
          error: 'No text content from Anthropic'
        };
      }

      try {
        // Extract JSON from Claude's response (it might include explanation text)
        const jsonMatch = content.text.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          return {
            success: false,
            error: 'No JSON found in Anthropic response'
          };
        }

        const parsed = JSON.parse(jsonMatch[0]);
        const recommendations: VendorRecommendation[] = parsed.recommendations?.map((rec: any) => {
          const vendor = vendors.find(v => v.id === rec.vendorId);
          if (!vendor) return null;

          return {
            vendor,
            score: rec.score,
            reasoning: rec.reasoning,
            confidence: rec.confidence,
            matchedCriteria: rec.matchedCriteria || [],
            risks: rec.risks || []
          };
        }).filter(Boolean) || [];

        return {
          success: true,
          data: recommendations,
          usage: response.usage ? {
            promptTokens: response.usage.input_tokens,
            completionTokens: response.usage.output_tokens,
            totalTokens: response.usage.input_tokens + response.usage.output_tokens
          } : undefined
        };
      } catch (parseError) {
        console.error('Failed to parse Anthropic response:', parseError);
        return {
          success: false,
          error: 'Failed to parse AI response'
        };
      }
    } catch (error: any) {
      return this.handleApiError(error);
    }
  }

  async generateInsights(
    data: Record<string, any>,
    insightType: AIInsight['type']
  ): Promise<AIProviderResponse<AIInsight>> {
    if (!this.isConfigured() || !this.client) {
      return {
        success: false,
        error: 'Anthropic adapter not configured'
      };
    }

    try {
      await this.checkRateLimit();

      const prompt = this.buildAnthropicInsightPrompt(data, insightType);
      
      const response = await this.retryWithBackoff(async () => {
        return await this.client!.messages.create({
          model: this.config?.modelVersion || 'claude-3-sonnet-20240229',
          max_tokens: this.config?.maxTokens || 1000,
          temperature: this.config?.temperature || 0.3,
          messages: [{ role: 'user', content: prompt }]
        });
      });

      if (response.usage) {
        this.tokensUsed += response.usage.input_tokens + response.usage.output_tokens;
      }

      const content = response.content[0];
      if (!content || content.type !== 'text') {
        return {
          success: false,
          error: 'No text content from Anthropic'
        };
      }

      try {
        const jsonMatch = content.text.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          return {
            success: false,
            error: 'No JSON found in Anthropic response'
          };
        }

        const parsed = JSON.parse(jsonMatch[0]);
        const insight: AIInsight = {
          id: `anthropic-${Date.now()}`,
          type: insightType,
          title: parsed.title,
          description: parsed.description,
          confidence: parsed.confidence || 0.8,
          data: parsed.data || {},
          createdAt: new Date(),
          expiresAt: parsed.expiresAt ? new Date(parsed.expiresAt) : undefined
        };

        return {
          success: true,
          data: insight,
          usage: response.usage ? {
            promptTokens: response.usage.input_tokens,
            completionTokens: response.usage.output_tokens,
            totalTokens: response.usage.input_tokens + response.usage.output_tokens
          } : undefined
        };
      } catch (parseError) {
        console.error('Failed to parse Anthropic insight response:', parseError);
        return {
          success: false,
          error: 'Failed to parse AI response'
        };
      }
    } catch (error: any) {
      return this.handleApiError(error);
    }
  }

  async analyzeRisk(
    vendorData: VendorData,
    context?: Record<string, any>
  ): Promise<AIProviderResponse<{
    riskLevel: 'low' | 'medium' | 'high';
    factors: string[];
    recommendations: string[];
  }>> {
    if (!this.isConfigured() || !this.client) {
      return {
        success: false,
        error: 'Anthropic adapter not configured'
      };
    }

    try {
      await this.checkRateLimit();

      const prompt = this.buildAnthropicRiskPrompt(vendorData, context);
      
      const response = await this.retryWithBackoff(async () => {
        return await this.client!.messages.create({
          model: this.config?.modelVersion || 'claude-3-sonnet-20240229',
          max_tokens: this.config?.maxTokens || 800,
          temperature: this.config?.temperature || 0.2,
          messages: [{ role: 'user', content: prompt }]
        });
      });

      if (response.usage) {
        this.tokensUsed += response.usage.input_tokens + response.usage.output_tokens;
      }

      const content = response.content[0];
      if (!content || content.type !== 'text') {
        return {
          success: false,
          error: 'No text content from Anthropic'
        };
      }

      try {
        const jsonMatch = content.text.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          return {
            success: false,
            error: 'No JSON found in Anthropic response'
          };
        }

        const parsed = JSON.parse(jsonMatch[0]);
        
        return {
          success: true,
          data: {
            riskLevel: parsed.riskLevel,
            factors: parsed.factors || [],
            recommendations: parsed.recommendations || []
          },
          usage: response.usage ? {
            promptTokens: response.usage.input_tokens,
            completionTokens: response.usage.output_tokens,
            totalTokens: response.usage.input_tokens + response.usage.output_tokens
          } : undefined
        };
      } catch (parseError) {
        console.error('Failed to parse Anthropic risk response:', parseError);
        return {
          success: false,
          error: 'Failed to parse AI response'
        };
      }
    } catch (error: any) {
      return this.handleApiError(error);
    }
  }

  async getUsageStats(): Promise<{
    requestsToday: number;
    tokensUsed: number;
    remainingQuota?: number;
  }> {
    const baseStats = await super.getUsageStats();
    return {
      ...baseStats,
      tokensUsed: this.tokensUsed
    };
  }

  protected async configure(config: any): Promise<boolean> {
    const success = await super.configure(config);
    if (success) {
      this.client = new Anthropic({ 
        apiKey: config.apiKey,
        dangerouslyAllowBrowser: true
      });
    }
    return success;
  }

  // Anthropic-specific prompt builders (Claude prefers more conversational style)
  private buildAnthropicRecommendationPrompt(vendors: VendorData[], criteria: RecommendationCriteria): string {
    const criteriaText = Object.entries(criteria)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');

    return `I need help analyzing vendors for a procurement decision. Here are the requirements and vendor data:

REQUIREMENTS: ${criteriaText}

VENDOR DATA:
${vendors.map(vendor => `
Vendor: ${vendor.name} (ID: ${vendor.id})
Category: ${vendor.category}
Performance Score: ${vendor.performanceScore}/100
Contract History: ${vendor.contractHistory.totalContracts} total contracts, ${vendor.contractHistory.completedOnTime} completed on time
Compliance Score: ${vendor.complianceRecords.score}/100 (${vendor.complianceRecords.violations} violations)
Average Cost: $${vendor.costEffectiveness.averageCost} (trend: ${vendor.costEffectiveness.costTrend})
Location: ${vendor.location}
Certifications: ${vendor.certifications.join(', ')}
`).join('')}

Please analyze these vendors and provide recommendations in the following JSON format:

{
  "recommendations": [
    {
      "vendorId": "vendor_id",
      "score": number_0_to_100,
      "reasoning": "detailed_explanation_of_why_recommended",
      "confidence": number_0_to_1,
      "matchedCriteria": ["list", "of", "matched", "criteria"],
      "risks": ["list", "of", "potential", "risks"]
    }
  ]
}

Focus on how well each vendor matches the requirements, their track record, and any potential risks.`;
  }

  private buildAnthropicInsightPrompt(data: Record<string, any>, insightType: AIInsight['type']): string {
    const dataStr = JSON.stringify(data, null, 2);
    
    return `I need you to analyze vendor management data and generate a ${insightType} insight.

Here's the data to analyze:
${dataStr}

Please provide your analysis in JSON format:
{
  "title": "Brief insight title",
  "description": "Detailed insight description with actionable information",
  "confidence": number_between_0_and_1,
  "data": {
    "key_metrics": {},
    "trends": [],
    "recommendations": []
  }
}

Focus on providing actionable insights that can help improve vendor management processes and decision-making.`;
  }

  private buildAnthropicRiskPrompt(vendorData: VendorData, context?: Record<string, any>): string {
    const contextText = context ? 
      `\nAdditional Context: ${Object.entries(context).map(([k, v]) => `${k}: ${v}`).join(', ')}` : '';

    return `Please analyze the risk profile of this vendor:

VENDOR INFORMATION:
Name: ${vendorData.name}
Category: ${vendorData.category}
Performance Score: ${vendorData.performanceScore}/100
Compliance Score: ${vendorData.complianceRecords.score}/100
Recent Violations: ${vendorData.complianceRecords.violations}
Cost Trend: ${vendorData.costEffectiveness.costTrend}
Contract Performance: ${vendorData.contractHistory.completedOnTime}/${vendorData.contractHistory.totalContracts} completed on time${contextText}

Please provide a risk analysis in JSON format:
{
  "riskLevel": "low|medium|high",
  "factors": ["list", "of", "risk", "factors"],
  "recommendations": ["list", "of", "risk", "mitigation", "recommendations"]
}

Consider performance history, compliance record, financial trends, and operational risks.`;
  }
}