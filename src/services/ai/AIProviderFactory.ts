import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AIProvider, AIProviderConfig } from './types';

// Import adapters (will be created in subsequent tasks)
import { OpenAIAdapter } from './adapters/OpenAIAdapter';
import { AnthropicAdapter } from './adapters/AnthropicAdapter';
import { GeminiAdapter } from './adapters/GeminiAdapter';

export class AIProviderFactory {
  private static instances: Map<AIProvider, IAIProvider> = new Map();

  /**
   * Creates or returns existing AI provider instance
   */
  static async createProvider(
    provider: AIProvider,
    config?: AIProviderConfig
  ): Promise<IAIProvider> {
    // Return existing instance if available
    if (this.instances.has(provider)) {
      const instance = this.instances.get(provider)!;
      
      // Reconfigure if new config provided
      if (config) {
        await instance.configure(config);
      }
      
      return instance;
    }

    // Create new instance
    let instance: IAIProvider;

    switch (provider) {
      case 'openai':
        instance = new OpenAIAdapter();
        break;
      case 'anthropic':
        instance = new AnthropicAdapter();
        break;
      case 'gemini':
        instance = new GeminiAdapter();
        break;
      default:
        throw new Error(`Unsupported AI provider: ${provider}`);
    }

    // Configure if config provided
    if (config) {
      const configured = await instance.configure(config);
      if (!configured) {
        throw new Error(`Failed to configure ${provider} provider`);
      }
    }

    // Cache instance
    this.instances.set(provider, instance);
    
    return instance;
  }

  /**
   * Get available providers
   */
  static getAvailableProviders(): AIProvider[] {
    return ['openai', 'anthropic', 'gemini'];
  }

  /**
   * Check if provider is supported
   */
  static isProviderSupported(provider: string): provider is AIProvider {
    return ['openai', 'anthropic', 'gemini'].includes(provider as AIProvider);
  }

  /**
   * Get provider instance if exists
   */
  static getProvider(provider: AIProvider): IAIProvider | null {
    return this.instances.get(provider) || null;
  }

  /**
   * Remove provider instance (useful for testing or reconfiguration)
   */
  static removeProvider(provider: AIProvider): void {
    this.instances.delete(provider);
  }

  /**
   * Clear all provider instances
   */
  static clearAll(): void {
    this.instances.clear();
  }

  /**
   * Get provider status information
   */
  static async getProviderStatus(provider: AIProvider): Promise<{
    configured: boolean;
    available: boolean;
    lastError?: string;
  }> {
    try {
      const instance = this.instances.get(provider);
      
      if (!instance) {
        return {
          configured: false,
          available: false
        };
      }

      const configured = instance.isConfigured();
      
      // Test availability with a simple validation
      let available = false;
      try {
        if (configured) {
          // This would typically make a lightweight API call
          available = true; // Simplified for now
        }
      } catch (error) {
        return {
          configured,
          available: false,
          lastError: error instanceof Error ? error.message : 'Unknown error'
        };
      }

      return {
        configured,
        available
      };
    } catch (error) {
      return {
        configured: false,
        available: false,
        lastError: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get all provider statuses
   */
  static async getAllProviderStatuses(): Promise<Record<AIProvider, {
    configured: boolean;
    available: boolean;
    lastError?: string;
  }>> {
    const providers = this.getAvailableProviders();
    const statuses: Record<string, any> = {};

    for (const provider of providers) {
      statuses[provider] = await this.getProviderStatus(provider);
    }

    return statuses as Record<AIProvider, {
      configured: boolean;
      available: boolean;
      lastError?: string;
    }>;
  }
}