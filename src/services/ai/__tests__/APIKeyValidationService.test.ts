import { APIKeyValidationService } from '../APIKeyValidationService';
import { AIProviderFactory } from '../AIProviderFactory';

// Mock dependencies
jest.mock('../AIProviderFactory');
jest.mock('../../encryption/EncryptionService');

const mockAIProviderFactory = AIProviderFactory as jest.Mocked<typeof AIProviderFactory>;

describe('APIKeyValidationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    APIKeyValidationService.clearCache();
  });

  describe('validateApiKey', () => {
    it('should validate a correct API key', async () => {
      const mockProvider = {
        validateApiKey: jest.fn().mockResolvedValue(true),
        getUsageStats: jest.fn().mockResolvedValue({
          requestsToday: 10,
          tokensUsed: 1000,
          remainingQuota: 9000
        })
      };

      mockAIProviderFactory.createProvider.mockResolvedValue(mockProvider as any);

      // Mock format validation
      const { EncryptionService } = require('../../encryption/EncryptionService');
      EncryptionService.validateApiKeyFormat.mockReturnValue({ isValid: true });

      const result = await APIKeyValidationService.validateApiKey('openai', 'sk-test123456789');

      expect(result.isValid).toBe(true);
      expect(result.details?.formatValid).toBe(true);
      expect(result.details?.connectionValid).toBe(true);
    });

    it('should reject invalid format', async () => {
      const { EncryptionService } = require('../../encryption/EncryptionService');
      EncryptionService.validateApiKeyFormat.mockReturnValue({
        isValid: false,
        error: 'Invalid format'
      });

      const result = await APIKeyValidationService.validateApiKey('openai', 'invalid-key');

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid format');
      expect(result.details?.formatValid).toBe(false);
    });

    it('should handle connection failure', async () => {
      const mockProvider = {
        validateApiKey: jest.fn().mockResolvedValue(false)
      };

      mockAIProviderFactory.createProvider.mockResolvedValue(mockProvider as any);

      const { EncryptionService } = require('../../encryption/EncryptionService');
      EncryptionService.validateApiKeyFormat.mockReturnValue({ isValid: true });

      const result = await APIKeyValidationService.validateApiKey('openai', 'sk-test123456789');

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('API key authentication failed');
      expect(result.details?.connectionValid).toBe(false);
    });

    it('should handle provider creation errors', async () => {
      mockAIProviderFactory.createProvider.mockRejectedValue(new Error('Provider error'));

      const { EncryptionService } = require('../../encryption/EncryptionService');
      EncryptionService.validateApiKeyFormat.mockReturnValue({ isValid: true });

      const result = await APIKeyValidationService.validateApiKey('openai', 'sk-test123456789');

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Provider error');
    });

    it('should skip connection test when requested', async () => {
      const { EncryptionService } = require('../../encryption/EncryptionService');
      EncryptionService.validateApiKeyFormat.mockReturnValue({ isValid: true });

      const result = await APIKeyValidationService.validateApiKey(
        'openai',
        'sk-test123456789',
        { testConnection: false }
      );

      expect(result.isValid).toBe(true);
      expect(result.details?.formatValid).toBe(true);
      expect(result.details?.connectionValid).toBe(true);
      expect(mockAIProviderFactory.createProvider).not.toHaveBeenCalled();
    });

    it('should use cache for repeated validations', async () => {
      const mockProvider = {
        validateApiKey: jest.fn().mockResolvedValue(true)
      };

      mockAIProviderFactory.createProvider.mockResolvedValue(mockProvider as any);

      const { EncryptionService } = require('../../encryption/EncryptionService');
      EncryptionService.validateApiKeyFormat.mockReturnValue({ isValid: true });

      // First call
      await APIKeyValidationService.validateApiKey('openai', 'sk-test123456789');
      
      // Second call should use cache
      await APIKeyValidationService.validateApiKey('openai', 'sk-test123456789');

      // Provider should only be called once
      expect(mockProvider.validateApiKey).toHaveBeenCalledTimes(1);
    });

    it('should skip cache when requested', async () => {
      const mockProvider = {
        validateApiKey: jest.fn().mockResolvedValue(true)
      };

      mockAIProviderFactory.createProvider.mockResolvedValue(mockProvider as any);

      const { EncryptionService } = require('../../encryption/EncryptionService');
      EncryptionService.validateApiKeyFormat.mockReturnValue({ isValid: true });

      // First call
      await APIKeyValidationService.validateApiKey('openai', 'sk-test123456789');
      
      // Second call with skipCache
      await APIKeyValidationService.validateApiKey(
        'openai',
        'sk-test123456789',
        { skipCache: true }
      );

      // Provider should be called twice
      expect(mockProvider.validateApiKey).toHaveBeenCalledTimes(2);
    });
  });

  describe('validateMultipleKeys', () => {
    it('should validate multiple keys in parallel', async () => {
      const mockProvider = {
        validateApiKey: jest.fn().mockResolvedValue(true)
      };

      mockAIProviderFactory.createProvider.mockResolvedValue(mockProvider as any);

      const { EncryptionService } = require('../../encryption/EncryptionService');
      EncryptionService.validateApiKeyFormat.mockReturnValue({ isValid: true });

      const validations = [
        { provider: 'openai' as const, apiKey: 'sk-test1' },
        { provider: 'anthropic' as const, apiKey: 'sk-ant-test2' }
      ];

      const results = await APIKeyValidationService.validateMultipleKeys(validations);

      expect(results).toHaveLength(2);
      expect(results[0].provider).toBe('openai');
      expect(results[1].provider).toBe('anthropic');
      expect(results[0].isValid).toBe(true);
      expect(results[1].isValid).toBe(true);
    });
  });

  describe('testKeyRotation', () => {
    it('should test key rotation successfully', async () => {
      const mockProvider = {
        validateApiKey: jest.fn()
          .mockResolvedValueOnce(true)  // old key valid
          .mockResolvedValueOnce(true)  // new key valid
      };

      mockAIProviderFactory.createProvider.mockResolvedValue(mockProvider as any);

      const { EncryptionService } = require('../../encryption/EncryptionService');
      EncryptionService.validateApiKeyFormat.mockReturnValue({ isValid: true });

      const result = await APIKeyValidationService.testKeyRotation(
        'openai',
        'sk-oldkey',
        'sk-newkey'
      );

      expect(result.oldKeyValid).toBe(true);
      expect(result.newKeyValid).toBe(true);
      expect(result.canRotate).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should handle new key validation failure', async () => {
      const mockProvider = {
        validateApiKey: jest.fn()
          .mockResolvedValueOnce(true)   // old key valid
          .mockResolvedValueOnce(false)  // new key invalid
      };

      mockAIProviderFactory.createProvider.mockResolvedValue(mockProvider as any);

      const { EncryptionService } = require('../../encryption/EncryptionService');
      EncryptionService.validateApiKeyFormat.mockReturnValue({ isValid: true });

      const result = await APIKeyValidationService.testKeyRotation(
        'openai',
        'sk-oldkey',
        'sk-invalidnewkey'
      );

      expect(result.oldKeyValid).toBe(true);
      expect(result.newKeyValid).toBe(false);
      expect(result.canRotate).toBe(false);
    });
  });

  describe('clearCache', () => {
    it('should clear validation cache', () => {
      // This is mainly to ensure the method exists and doesn't throw
      expect(() => APIKeyValidationService.clearCache()).not.toThrow();
    });
  });

  describe('getValidationStats', () => {
    it('should return validation statistics', () => {
      const stats = APIKeyValidationService.getValidationStats();
      
      expect(stats).toHaveProperty('cacheSize');
      expect(stats).toHaveProperty('cacheHitRate');
      expect(stats).toHaveProperty('totalValidations');
      expect(typeof stats.cacheSize).toBe('number');
    });
  });
});