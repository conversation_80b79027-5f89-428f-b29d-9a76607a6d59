import { AIProviderFactory } from '../AIProviderFactory';
import { AIProvider } from '../types';

describe('AIProviderFactory', () => {
  beforeEach(() => {
    AIProviderFactory.clearAll();
  });

  describe('getAvailableProviders', () => {
    it('should return all supported providers', () => {
      const providers = AIProviderFactory.getAvailableProviders();
      expect(providers).toEqual(['openai', 'anthropic', 'gemini']);
    });
  });

  describe('isProviderSupported', () => {
    it('should return true for supported providers', () => {
      expect(AIProviderFactory.isProviderSupported('openai')).toBe(true);
      expect(AIProviderFactory.isProviderSupported('anthropic')).toBe(true);
      expect(AIProviderFactory.isProviderSupported('gemini')).toBe(true);
    });

    it('should return false for unsupported providers', () => {
      expect(AIProviderFactory.isProviderSupported('unsupported')).toBe(false);
      expect(AIProviderFactory.isProviderSupported('')).toBe(false);
    });
  });

  describe('createProvider', () => {
    it('should create provider instances', async () => {
      const provider = await AIProviderFactory.createProvider('openai');
      expect(provider).toBeDefined();
      expect(provider.provider).toBe('openai');
    });

    it('should return same instance on subsequent calls', async () => {
      const provider1 = await AIProviderFactory.createProvider('openai');
      const provider2 = await AIProviderFactory.createProvider('openai');
      expect(provider1).toBe(provider2);
    });

    it('should throw error for unsupported provider', async () => {
      await expect(
        AIProviderFactory.createProvider('unsupported' as AIProvider)
      ).rejects.toThrow('Unsupported AI provider: unsupported');
    });
  });

  describe('getProvider', () => {
    it('should return null for non-existent provider', () => {
      const provider = AIProviderFactory.getProvider('openai');
      expect(provider).toBeNull();
    });

    it('should return provider after creation', async () => {
      await AIProviderFactory.createProvider('openai');
      const provider = AIProviderFactory.getProvider('openai');
      expect(provider).toBeDefined();
      expect(provider?.provider).toBe('openai');
    });
  });

  describe('removeProvider', () => {
    it('should remove provider instance', async () => {
      await AIProviderFactory.createProvider('openai');
      expect(AIProviderFactory.getProvider('openai')).toBeDefined();
      
      AIProviderFactory.removeProvider('openai');
      expect(AIProviderFactory.getProvider('openai')).toBeNull();
    });
  });

  describe('clearAll', () => {
    it('should remove all provider instances', async () => {
      await AIProviderFactory.createProvider('openai');
      await AIProviderFactory.createProvider('anthropic');
      
      expect(AIProviderFactory.getProvider('openai')).toBeDefined();
      expect(AIProviderFactory.getProvider('anthropic')).toBeDefined();
      
      AIProviderFactory.clearAll();
      
      expect(AIProviderFactory.getProvider('openai')).toBeNull();
      expect(AIProviderFactory.getProvider('anthropic')).toBeNull();
    });
  });

  describe('getProviderStatus', () => {
    it('should return unconfigured status for non-existent provider', async () => {
      const status = await AIProviderFactory.getProviderStatus('openai');
      expect(status).toEqual({
        configured: false,
        available: false
      });
    });

    it('should return configured status for existing provider', async () => {
      await AIProviderFactory.createProvider('openai');
      const status = await AIProviderFactory.getProviderStatus('openai');
      expect(status.configured).toBe(false); // Not configured yet
      expect(status.available).toBe(false);
    });
  });
});