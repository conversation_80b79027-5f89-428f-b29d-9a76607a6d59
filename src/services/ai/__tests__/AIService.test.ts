import { AIService } from '../AIService';
import { VendorData, RecommendationCriteria } from '../types';

describe('AIService', () => {
  let aiService: AIService;

  beforeEach(() => {
    aiService = AIService.getInstance();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = AIService.getInstance();
      const instance2 = AIService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('isAIAvailable', () => {
    it('should return false when no provider configured', () => {
      expect(aiService.isAIAvailable()).toBe(false);
    });
  });

  describe('getCurrentProvider', () => {
    it('should return null when no provider configured', () => {
      expect(aiService.getCurrentProvider()).toBeNull();
    });
  });

  describe('generateVendorRecommendations', () => {
    const mockVendors: VendorData[] = [
      {
        id: '1',
        name: 'Test Vendor',
        category: 'IT Services',
        performanceScore: 85,
        contractHistory: {
          totalContracts: 10,
          completedOnTime: 8,
          averageValue: 50000
        },
        complianceRecords: {
          score: 90,
          lastAudit: new Date(),
          violations: 0
        },
        costEffectiveness: {
          averageCost: 45000,
          costTrend: 'stable'
        },
        location: 'New York',
        certifications: ['ISO 9001', 'SOC 2']
      }
    ];

    const mockCriteria: RecommendationCriteria = {
      category: 'IT Services',
      maxBudget: 60000,
      minPerformanceScore: 80
    };

    it('should use fallback recommendations when no AI provider configured', async () => {
      const recommendations = await aiService.generateVendorRecommendations(
        mockVendors,
        mockCriteria
      );

      expect(recommendations).toBeDefined();
      expect(recommendations.length).toBeGreaterThan(0);
      expect(recommendations[0].vendor).toBe(mockVendors[0]);
      expect(recommendations[0].confidence).toBe(0.7); // Fallback confidence
    });

    it('should generate recommendations based on criteria', async () => {
      const recommendations = await aiService.generateVendorRecommendations(
        mockVendors,
        mockCriteria
      );

      const recommendation = recommendations[0];
      expect(recommendation.matchedCriteria).toContain('category');
      expect(recommendation.matchedCriteria).toContain('budget');
      expect(recommendation.score).toBeGreaterThan(0);
    });
  });

  describe('setFallbackEnabled', () => {
    it('should control fallback behavior', () => {
      aiService.setFallbackEnabled(false);
      // This would be tested with actual AI provider failures
      // For now, just verify the method exists and can be called
      expect(() => aiService.setFallbackEnabled(true)).not.toThrow();
    });
  });
});