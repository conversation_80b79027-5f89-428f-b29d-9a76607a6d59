import { 
  IAIProvider, 
  AIProvider, 
  AIProviderConfig,
  VendorData,
  RecommendationCriteria,
  VendorRecommendation,
  AIInsight,
  AIProviderResponse
} from './types';
import { AIProviderFactory } from './AIProviderFactory';

export class AIService {
  private static instance: AIService;
  private currentProvider: AIProvider | null = null;
  private fallbackEnabled = true;

  private constructor() {}

  static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  /**
   * Configure AI provider
   */
  async configureProvider(provider: AIProvider, config: AIProviderConfig): Promise<boolean> {
    try {
      const providerInstance = await AIProviderFactory.createProvider(provider, config);
      this.currentProvider = provider;
      return true;
    } catch (error) {
      console.error('Failed to configure AI provider:', error);
      return false;
    }
  }

  /**
   * Get current provider
   */
  getCurrentProvider(): AIProvider | null {
    return this.currentProvider;
  }

  /**
   * Check if AI is available
   */
  isAIAvailable(): boolean {
    return this.currentProvider !== null;
  }

  /**
   * Generate vendor recommendations
   */
  async generateVendorRecommendations(
    vendors: VendorData[],
    criteria: RecommendationCriteria
  ): Promise<VendorRecommendation[]> {
    if (!this.currentProvider) {
      if (this.fallbackEnabled) {
        return this.generateFallbackRecommendations(vendors, criteria);
      }
      throw new Error('No AI provider configured');
    }

    try {
      const provider = await AIProviderFactory.createProvider(this.currentProvider);
      const response = await provider.generateVendorRecommendations(vendors, criteria);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      // Fallback on AI failure
      if (this.fallbackEnabled) {
        console.warn('AI recommendation failed, using fallback:', response.error);
        return this.generateFallbackRecommendations(vendors, criteria);
      }
      
      throw new Error(response.error || 'Failed to generate recommendations');
    } catch (error) {
      if (this.fallbackEnabled) {
        console.warn('AI service error, using fallback:', error);
        return this.generateFallbackRecommendations(vendors, criteria);
      }
      throw error;
    }
  }

  /**
   * Generate AI insights
   */
  async generateInsights(
    data: Record<string, any>,
    insightType: AIInsight['type']
  ): Promise<AIInsight | null> {
    if (!this.currentProvider) {
      return null;
    }

    try {
      const provider = await AIProviderFactory.createProvider(this.currentProvider);
      const response = await provider.generateInsights(data, insightType);
      
      return response.success ? response.data || null : null;
    } catch (error) {
      console.error('Failed to generate insights:', error);
      return null;
    }
  }

  /**
   * Analyze vendor risk
   */
  async analyzeVendorRisk(
    vendorData: VendorData,
    context?: Record<string, any>
  ): Promise<{
    riskLevel: 'low' | 'medium' | 'high';
    factors: string[];
    recommendations: string[];
  } | null> {
    if (!this.currentProvider) {
      return this.generateFallbackRiskAnalysis(vendorData);
    }

    try {
      const provider = await AIProviderFactory.createProvider(this.currentProvider);
      const response = await provider.analyzeRisk(vendorData, context);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      // Fallback on AI failure
      if (this.fallbackEnabled) {
        return this.generateFallbackRiskAnalysis(vendorData);
      }
      
      return null;
    } catch (error) {
      if (this.fallbackEnabled) {
        return this.generateFallbackRiskAnalysis(vendorData);
      }
      console.error('Failed to analyze vendor risk:', error);
      return null;
    }
  }

  /**
   * Get provider usage statistics
   */
  async getUsageStats(): Promise<{
    requestsToday: number;
    tokensUsed: number;
    remainingQuota?: number;
  } | null> {
    if (!this.currentProvider) {
      return null;
    }

    try {
      const provider = await AIProviderFactory.createProvider(this.currentProvider);
      return await provider.getUsageStats();
    } catch (error) {
      console.error('Failed to get usage stats:', error);
      return null;
    }
  }

  /**
   * Enable/disable fallback to rule-based recommendations
   */
  setFallbackEnabled(enabled: boolean): void {
    this.fallbackEnabled = enabled;
  }

  /**
   * Fallback recommendation system using rule-based logic
   */
  private generateFallbackRecommendations(
    vendors: VendorData[],
    criteria: RecommendationCriteria
  ): VendorRecommendation[] {
    return vendors
      .map(vendor => {
        let score = vendor.performanceScore * 0.4; // 40% weight on performance
        let matchedCriteria: string[] = [];
        let risks: string[] = [];

        // Category matching
        if (criteria.category && vendor.category === criteria.category) {
          score += 20;
          matchedCriteria.push('category');
        }

        // Budget considerations
        if (criteria.maxBudget && vendor.costEffectiveness.averageCost <= criteria.maxBudget) {
          score += 15;
          matchedCriteria.push('budget');
        } else if (criteria.maxBudget && vendor.costEffectiveness.averageCost > criteria.maxBudget) {
          score -= 10;
          risks.push('Cost exceeds budget');
        }

        // Location preference
        if (criteria.location && vendor.location === criteria.location) {
          score += 10;
          matchedCriteria.push('location');
        }

        // Certification requirements
        if (criteria.requiredCertifications) {
          const hasAllCerts = criteria.requiredCertifications.every(cert =>
            vendor.certifications.includes(cert)
          );
          if (hasAllCerts) {
            score += 15;
            matchedCriteria.push('certifications');
          } else {
            risks.push('Missing required certifications');
          }
        }

        // Performance threshold
        if (criteria.minPerformanceScore && vendor.performanceScore < criteria.minPerformanceScore) {
          score -= 20;
          risks.push('Performance below threshold');
        }

        // Compliance considerations
        if (vendor.complianceRecords.score < 70) {
          risks.push('Low compliance score');
          score -= 10;
        }

        // Contract history
        const onTimeRate = vendor.contractHistory.completedOnTime / vendor.contractHistory.totalContracts;
        if (onTimeRate < 0.8) {
          risks.push('Poor on-time delivery history');
          score -= 5;
        }

        return {
          vendor,
          score: Math.max(0, Math.min(100, score)),
          reasoning: `Rule-based recommendation based on ${matchedCriteria.length} matched criteria`,
          confidence: 0.7, // Lower confidence for rule-based
          matchedCriteria,
          risks
        };
      })
      .sort((a, b) => b.score - a.score)
      .slice(0, 10); // Top 10 recommendations
  }

  /**
   * Fallback risk analysis using rule-based logic
   */
  private generateFallbackRiskAnalysis(vendorData: VendorData): {
    riskLevel: 'low' | 'medium' | 'high';
    factors: string[];
    recommendations: string[];
  } {
    const factors: string[] = [];
    const recommendations: string[] = [];
    let riskScore = 0;

    // Performance risk
    if (vendorData.performanceScore < 60) {
      riskScore += 30;
      factors.push('Low performance score');
      recommendations.push('Monitor performance closely');
    } else if (vendorData.performanceScore < 80) {
      riskScore += 15;
      factors.push('Moderate performance concerns');
    }

    // Compliance risk
    if (vendorData.complianceRecords.score < 70) {
      riskScore += 25;
      factors.push('Poor compliance history');
      recommendations.push('Require compliance improvement plan');
    }

    if (vendorData.complianceRecords.violations > 3) {
      riskScore += 20;
      factors.push('Multiple compliance violations');
    }

    // Financial risk
    if (vendorData.costEffectiveness.costTrend === 'increasing') {
      riskScore += 10;
      factors.push('Rising costs');
      recommendations.push('Negotiate cost controls');
    }

    // Contract performance risk
    const onTimeRate = vendorData.contractHistory.completedOnTime / vendorData.contractHistory.totalContracts;
    if (onTimeRate < 0.7) {
      riskScore += 20;
      factors.push('Poor delivery track record');
      recommendations.push('Implement stricter delivery monitoring');
    }

    // Determine risk level
    let riskLevel: 'low' | 'medium' | 'high';
    if (riskScore >= 50) {
      riskLevel = 'high';
    } else if (riskScore >= 25) {
      riskLevel = 'medium';
    } else {
      riskLevel = 'low';
    }

    if (factors.length === 0) {
      factors.push('No significant risk factors identified');
    }

    if (recommendations.length === 0) {
      recommendations.push('Continue standard monitoring procedures');
    }

    return {
      riskLevel,
      factors,
      recommendations
    };
  }
}