import { 
  IAIProvider, 
  AIProvider, 
  AIProviderConfig, 
  AIProviderResponse,
  AIProviderError,
  VendorData,
  RecommendationCriteria,
  VendorRecommendation,
  AIInsight
} from './types';

export abstract class BaseAIAdapter implements IAIProvider {
  protected config: AIProviderConfig | null = null;
  protected rateLimitTracker: Map<string, number> = new Map();
  protected requestCount = 0;
  protected lastRequestTime = 0;

  abstract readonly provider: AIProvider;

  constructor() {
    // Reset rate limit tracker every hour
    setInterval(() => {
      this.rateLimitTracker.clear();
      this.requestCount = 0;
    }, 60 * 60 * 1000);
  }

  async configure(config: AIProviderConfig): Promise<boolean> {
    try {
      const isValid = await this.validateApiKey(config.apiKey);
      if (isValid) {
        this.config = { ...config };
        return true;
      }
      return false;
    } catch (error) {
      console.error(`Failed to configure ${this.provider} provider:`, error);
      return false;
    }
  }

  abstract validateApiKey(apiKey: string): Promise<boolean>;

  abstract generateVendorRecommendations(
    vendors: VendorData[],
    criteria: RecommendationCriteria
  ): Promise<AIProviderResponse<VendorRecommendation[]>>;

  abstract generateInsights(
    data: Record<string, any>,
    insightType: AIInsight['type']
  ): Promise<AIProviderResponse<AIInsight>>;

  abstract analyzeRisk(
    vendorData: VendorData,
    context?: Record<string, any>
  ): Promise<AIProviderResponse<{
    riskLevel: 'low' | 'medium' | 'high';
    factors: string[];
    recommendations: string[];
  }>>;

  isConfigured(): boolean {
    return this.config !== null && !!this.config.apiKey;
  }

  async getUsageStats(): Promise<{
    requestsToday: number;
    tokensUsed: number;
    remainingQuota?: number;
  }> {
    return {
      requestsToday: this.requestCount,
      tokensUsed: 0, // To be implemented by specific adapters
      remainingQuota: undefined
    };
  }

  // Rate limiting protection
  protected async checkRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    // Minimum 100ms between requests
    if (timeSinceLastRequest < 100) {
      await new Promise(resolve => setTimeout(resolve, 100 - timeSinceLastRequest));
    }
    
    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  // Error handling utilities
  protected createError(message: string, code: string, retryable = false): AIProviderError {
    const error = new Error(message) as AIProviderError;
    error.provider = this.provider;
    error.code = code;
    error.retryable = retryable;
    return error;
  }

  protected handleApiError(error: any): AIProviderResponse {
    console.error(`${this.provider} API Error:`, error);
    
    if (error.response?.status === 429) {
      return {
        success: false,
        error: 'Rate limit exceeded. Please try again later.'
      };
    }
    
    if (error.response?.status === 401) {
      return {
        success: false,
        error: 'Invalid API key. Please check your configuration.'
      };
    }
    
    if (error.response?.status >= 500) {
      return {
        success: false,
        error: 'Service temporarily unavailable. Please try again later.'
      };
    }
    
    return {
      success: false,
      error: error.message || 'An unexpected error occurred.'
    };
  }

  // Retry logic with exponential backoff
  protected async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries = 3,
    baseDelay = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          throw lastError;
        }
        
        // Exponential backoff with jitter
        const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }

  // Common prompt templates
  protected buildRecommendationPrompt(vendors: VendorData[], criteria: RecommendationCriteria): string {
    const criteriaText = Object.entries(criteria)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');

    return `
You are an expert procurement analyst. Analyze the following vendors and recommend the best matches based on the given criteria.

CRITERIA: ${criteriaText}

VENDORS:
${vendors.map(vendor => `
- ${vendor.name} (${vendor.category})
  Performance Score: ${vendor.performanceScore}/100
  Contract History: ${vendor.contractHistory.totalContracts} contracts, ${vendor.contractHistory.completedOnTime} on-time
  Compliance Score: ${vendor.complianceRecords.score}/100
  Average Cost: $${vendor.costEffectiveness.averageCost}
  Location: ${vendor.location}
  Certifications: ${vendor.certifications.join(', ')}
`).join('')}

Please provide recommendations in JSON format with the following structure:
{
  "recommendations": [
    {
      "vendorId": "string",
      "score": number (0-100),
      "reasoning": "detailed explanation",
      "confidence": number (0-1),
      "matchedCriteria": ["criteria1", "criteria2"],
      "risks": ["risk1", "risk2"]
    }
  ]
}

Focus on matching criteria, performance history, and risk assessment.
`;
  }

  protected buildRiskAnalysisPrompt(vendorData: VendorData, context?: Record<string, any>): string {
    const contextText = context ? 
      `CONTEXT: ${Object.entries(context).map(([k, v]) => `${k}: ${v}`).join(', ')}` : '';

    return `
Analyze the risk profile of this vendor:

VENDOR: ${vendorData.name}
Category: ${vendorData.category}
Performance Score: ${vendorData.performanceScore}/100
Compliance Score: ${vendorData.complianceRecords.score}/100
Recent Violations: ${vendorData.complianceRecords.violations}
Cost Trend: ${vendorData.costEffectiveness.costTrend}
${contextText}

Provide risk analysis in JSON format:
{
  "riskLevel": "low|medium|high",
  "factors": ["factor1", "factor2"],
  "recommendations": ["recommendation1", "recommendation2"]
}
`;
  }
}