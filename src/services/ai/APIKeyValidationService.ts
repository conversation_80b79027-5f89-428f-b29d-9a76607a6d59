import { AIProvider } from './types';
import { AIProviderFactory } from './AIProviderFactory';
import { EncryptionService } from '../encryption/EncryptionService';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  details?: {
    formatValid: boolean;
    connectionValid: boolean;
    quotaInfo?: {
      hasQuota: boolean;
      remainingRequests?: number;
      resetTime?: Date;
    };
    modelAccess?: string[];
  };
}

export class APIKeyValidationService {
  private static validationCache = new Map<string, {
    result: ValidationResult;
    timestamp: number;
    ttl: number;
  }>();

  private static readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Comprehensive API key validation
   */
  static async validateApiKey(
    provider: AIProvider,
    apiKey: string,
    options: {
      skipCache?: boolean;
      testConnection?: boolean;
      checkQuota?: boolean;
    } = {}
  ): Promise<ValidationResult> {
    const {
      skipCache = false,
      testConnection = true,
      checkQuota = false
    } = options;

    // Create cache key (hashed for security)
    const cacheKey = await this.createCacheKey(provider, apiKey);

    // Check cache first
    if (!skipCache) {
      const cached = this.getCachedResult(cacheKey);
      if (cached) {
        return cached;
      }
    }

    // Perform validation
    const result = await this.performValidation(
      provider,
      apiKey,
      testConnection,
      checkQuota
    );

    // Cache result
    this.cacheResult(cacheKey, result);

    return result;
  }

  /**
   * Perform the actual validation
   */
  private static async performValidation(
    provider: AIProvider,
    apiKey: string,
    testConnection: boolean,
    checkQuota: boolean
  ): Promise<ValidationResult> {
    // Step 1: Format validation
    const formatValidation = EncryptionService.validateApiKeyFormat(provider, apiKey);
    if (!formatValidation.isValid) {
      return {
        isValid: false,
        error: formatValidation.error,
        details: {
          formatValid: false,
          connectionValid: false
        }
      };
    }

    // Step 2: Connection test (if requested)
    let connectionValid = false;
    let quotaInfo;
    let modelAccess;

    if (testConnection) {
      try {
        const providerInstance = await AIProviderFactory.createProvider(provider);
        connectionValid = await providerInstance.validateApiKey(apiKey);

        if (!connectionValid) {
          return {
            isValid: false,
            error: 'API key authentication failed',
            details: {
              formatValid: true,
              connectionValid: false
            }
          };
        }

        // Step 3: Additional checks for valid connections
        if (checkQuota) {
          quotaInfo = await this.checkQuotaInfo(provider, apiKey);
        }

        modelAccess = await this.checkModelAccess(provider, apiKey);

      } catch (error) {
        return {
          isValid: false,
          error: error instanceof Error ? error.message : 'Connection test failed',
          details: {
            formatValid: true,
            connectionValid: false
          }
        };
      }
    }

    return {
      isValid: true,
      details: {
        formatValid: true,
        connectionValid: testConnection ? connectionValid : true,
        quotaInfo,
        modelAccess
      }
    };
  }

  /**
   * Check quota information for the API key
   */
  private static async checkQuotaInfo(
    provider: AIProvider,
    apiKey: string
  ): Promise<{
    hasQuota: boolean;
    remainingRequests?: number;
    resetTime?: Date;
  }> {
    try {
      const providerInstance = await AIProviderFactory.createProvider(provider, {
        provider,
        apiKey
      });

      const stats = await providerInstance.getUsageStats();
      
      return {
        hasQuota: true,
        remainingRequests: stats.remainingQuota,
        resetTime: undefined // Most APIs don't provide reset time
      };
    } catch (error) {
      return {
        hasQuota: false
      };
    }
  }

  /**
   * Check which models are accessible with this API key
   */
  private static async checkModelAccess(
    provider: AIProvider,
    apiKey: string
  ): Promise<string[]> {
    const defaultModels = {
      openai: ['gpt-3.5-turbo', 'gpt-4'],
      anthropic: ['claude-3-haiku-20240307', 'claude-3-sonnet-20240229'],
      gemini: ['gemini-pro']
    };

    try {
      // For now, return default models
      // In a real implementation, you might test each model
      return defaultModels[provider] || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Create a secure cache key
   */
  private static async createCacheKey(provider: AIProvider, apiKey: string): Promise<string> {
    const data = `${provider}:${apiKey}`;
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Get cached validation result
   */
  private static getCachedResult(cacheKey: string): ValidationResult | null {
    const cached = this.validationCache.get(cacheKey);
    
    if (!cached) {
      return null;
    }

    const now = Date.now();
    if (now - cached.timestamp > cached.ttl) {
      this.validationCache.delete(cacheKey);
      return null;
    }

    return cached.result;
  }

  /**
   * Cache validation result
   */
  private static cacheResult(cacheKey: string, result: ValidationResult): void {
    this.validationCache.set(cacheKey, {
      result,
      timestamp: Date.now(),
      ttl: this.CACHE_TTL
    });

    // Clean up old cache entries
    this.cleanupCache();
  }

  /**
   * Clean up expired cache entries
   */
  private static cleanupCache(): void {
    const now = Date.now();
    
    for (const [key, cached] of this.validationCache.entries()) {
      if (now - cached.timestamp > cached.ttl) {
        this.validationCache.delete(key);
      }
    }
  }

  /**
   * Clear validation cache
   */
  static clearCache(): void {
    this.validationCache.clear();
  }

  /**
   * Validate multiple API keys in parallel
   */
  static async validateMultipleKeys(
    validations: Array<{
      provider: AIProvider;
      apiKey: string;
      options?: {
        skipCache?: boolean;
        testConnection?: boolean;
        checkQuota?: boolean;
      };
    }>
  ): Promise<Array<ValidationResult & { provider: AIProvider }>> {
    const promises = validations.map(async ({ provider, apiKey, options }) => {
      const result = await this.validateApiKey(provider, apiKey, options);
      return { ...result, provider };
    });

    return Promise.all(promises);
  }

  /**
   * Get validation statistics
   */
  static getValidationStats(): {
    cacheSize: number;
    cacheHitRate: number;
    totalValidations: number;
  } {
    // This would be implemented with proper metrics tracking
    return {
      cacheSize: this.validationCache.size,
      cacheHitRate: 0, // Would need to track hits/misses
      totalValidations: 0 // Would need to track total validations
    };
  }

  /**
   * Test API key rotation
   */
  static async testKeyRotation(
    provider: AIProvider,
    oldKey: string,
    newKey: string
  ): Promise<{
    oldKeyValid: boolean;
    newKeyValid: boolean;
    canRotate: boolean;
    error?: string;
  }> {
    try {
      const [oldResult, newResult] = await Promise.all([
        this.validateApiKey(provider, oldKey, { testConnection: true }),
        this.validateApiKey(provider, newKey, { testConnection: true })
      ]);

      return {
        oldKeyValid: oldResult.isValid,
        newKeyValid: newResult.isValid,
        canRotate: newResult.isValid,
        error: newResult.isValid ? undefined : newResult.error
      };
    } catch (error) {
      return {
        oldKeyValid: false,
        newKeyValid: false,
        canRotate: false,
        error: error instanceof Error ? error.message : 'Key rotation test failed'
      };
    }
  }
}