// AI Provider Types and Interfaces

export type AIProvider = 'openai' | 'anthropic' | 'gemini';

export interface VendorData {
  id: string;
  name: string;
  category: string;
  performanceScore: number;
  contractHistory: {
    totalContracts: number;
    completedOnTime: number;
    averageValue: number;
  };
  complianceRecords: {
    score: number;
    lastAudit: Date;
    violations: number;
  };
  costEffectiveness: {
    averageCost: number;
    costTrend: 'increasing' | 'decreasing' | 'stable';
  };
  location: string;
  certifications: string[];
}

export interface RecommendationCriteria {
  category?: string;
  maxBudget?: number;
  location?: string;
  requiredCertifications?: string[];
  minPerformanceScore?: number;
  projectType?: string;
  urgency?: 'low' | 'medium' | 'high';
}

export interface VendorRecommendation {
  vendor: VendorData;
  score: number;
  reasoning: string;
  confidence: number;
  matchedCriteria: string[];
  risks: string[];
}

export interface AIInsight {
  id: string;
  type: 'recommendation' | 'risk_alert' | 'optimization' | 'trend';
  title: string;
  description: string;
  confidence: number;
  data: Record<string, any>;
  createdAt: Date;
  expiresAt?: Date;
}

export interface AIProviderConfig {
  provider: AIProvider;
  apiKey: string;
  modelVersion?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface AIProviderResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

// Base AI Provider Interface
export interface IAIProvider {
  readonly provider: AIProvider;
  
  // Configuration
  configure(config: AIProviderConfig): Promise<boolean>;
  validateApiKey(apiKey: string): Promise<boolean>;
  
  // Core AI Functions
  generateVendorRecommendations(
    vendors: VendorData[],
    criteria: RecommendationCriteria
  ): Promise<AIProviderResponse<VendorRecommendation[]>>;
  
  generateInsights(
    data: Record<string, any>,
    insightType: AIInsight['type']
  ): Promise<AIProviderResponse<AIInsight>>;
  
  analyzeRisk(
    vendorData: VendorData,
    context?: Record<string, any>
  ): Promise<AIProviderResponse<{
    riskLevel: 'low' | 'medium' | 'high';
    factors: string[];
    recommendations: string[];
  }>>;
  
  // Utility methods
  isConfigured(): boolean;
  getUsageStats(): Promise<{
    requestsToday: number;
    tokensUsed: number;
    remainingQuota?: number;
  }>;
}

export interface AIProviderError extends Error {
  provider: AIProvider;
  code: string;
  retryable: boolean;
}