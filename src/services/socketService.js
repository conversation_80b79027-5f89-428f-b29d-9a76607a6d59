const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const Comment = require('../models/Comment');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> Set of socketIds
    this.userSockets = new Map(); // socketId -> userId
    this.typingUsers = new Map(); // roomId -> Set of userIds
  }

  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await this.getUserById(decoded.id);
        
        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user.id;
        socket.user = user;
        next();
      } catch (error) {
        console.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });

    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
    });

    console.log('Socket.IO service initialized');
    return this.io;
  }

  handleConnection(socket) {
    const userId = socket.userId;
    console.log(`User ${userId} connected with socket ${socket.id}`);

    // Track connected users
    if (!this.connectedUsers.has(userId)) {
      this.connectedUsers.set(userId, new Set());
    }
    this.connectedUsers.get(userId).add(socket.id);
    this.userSockets.set(socket.id, userId);

    // Join user to their personal room for notifications
    socket.join(`user-${userId}`);

    // Handle joining comment rooms
    socket.on('join-comment-room', (data) => {
      this.handleJoinCommentRoom(socket, data);
    });

    // Handle leaving comment rooms
    socket.on('leave-comment-room', (data) => {
      this.handleLeaveCommentRoom(socket, data);
    });

    // Handle typing indicators
    socket.on('typing-start', (data) => {
      this.handleTypingStart(socket, data);
    });

    socket.on('typing-stop', (data) => {
      this.handleTypingStop(socket, data);
    });

    // Handle comment reactions (for future enhancement)
    socket.on('comment-reaction', (data) => {
      this.handleCommentReaction(socket, data);
    });

    // Handle real-time comment creation
    socket.on('comment-create', (data) => {
      this.handleCommentCreate(socket, data);
    });

    // Handle real-time comment updates
    socket.on('comment-update', (data) => {
      this.handleCommentUpdate(socket, data);
    });

    // Handle user presence
    socket.on('user-presence', (data) => {
      this.handleUserPresence(socket, data);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      this.handleDisconnection(socket);
    });

    // Send initial connection confirmation
    socket.emit('connected', {
      userId,
      socketId: socket.id,
      timestamp: new Date().toISOString()
    });
  }

  handleJoinCommentRoom(socket, data) {
    try {
      const { objectType, objectId } = data;
      const roomId = `${objectType}-${objectId}`;
      
      socket.join(roomId);
      
      // Notify others in the room about user joining
      socket.to(roomId).emit('user-joined-room', {
        userId: socket.userId,
        userName: socket.user.name,
        roomId,
        timestamp: new Date().toISOString()
      });

      // Send current room info to the user
      const roomSockets = this.io.sockets.adapter.rooms.get(roomId);
      const userCount = roomSockets ? roomSockets.size : 1;
      
      socket.emit('room-joined', {
        roomId,
        userCount,
        timestamp: new Date().toISOString()
      });

      console.log(`User ${socket.userId} joined room ${roomId}`);
    } catch (error) {
      console.error('Error joining comment room:', error);
      socket.emit('error', {
        message: 'Failed to join comment room',
        error: error.message
      });
    }
  }

  handleLeaveCommentRoom(socket, data) {
    try {
      const { objectType, objectId } = data;
      const roomId = `${objectType}-${objectId}`;
      
      socket.leave(roomId);
      
      // Stop typing if user was typing
      this.handleTypingStop(socket, { objectType, objectId });
      
      // Notify others in the room about user leaving
      socket.to(roomId).emit('user-left-room', {
        userId: socket.userId,
        userName: socket.user.name,
        roomId,
        timestamp: new Date().toISOString()
      });

      console.log(`User ${socket.userId} left room ${roomId}`);
    } catch (error) {
      console.error('Error leaving comment room:', error);
    }
  }

  handleTypingStart(socket, data) {
    try {
      const { objectType, objectId } = data;
      const roomId = `${objectType}-${objectId}`;
      
      if (!this.typingUsers.has(roomId)) {
        this.typingUsers.set(roomId, new Set());
      }
      
      this.typingUsers.get(roomId).add(socket.userId);
      
      // Broadcast typing indicator to others in the room
      socket.to(roomId).emit('user-typing', {
        userId: socket.userId,
        userName: socket.user.name,
        isTyping: true,
        timestamp: new Date().toISOString()
      });

      // Auto-stop typing after 3 seconds of inactivity
      clearTimeout(socket.typingTimeout);
      socket.typingTimeout = setTimeout(() => {
        this.handleTypingStop(socket, data);
      }, 3000);
    } catch (error) {
      console.error('Error handling typing start:', error);
    }
  }

  handleTypingStop(socket, data) {
    try {
      const { objectType, objectId } = data;
      const roomId = `${objectType}-${objectId}`;
      
      if (this.typingUsers.has(roomId)) {
        this.typingUsers.get(roomId).delete(socket.userId);
        
        if (this.typingUsers.get(roomId).size === 0) {
          this.typingUsers.delete(roomId);
        }
      }
      
      // Clear typing timeout
      clearTimeout(socket.typingTimeout);
      
      // Broadcast typing stop to others in the room
      socket.to(roomId).emit('user-typing', {
        userId: socket.userId,
        userName: socket.user.name,
        isTyping: false,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error handling typing stop:', error);
    }
  }

  handleCommentReaction(socket, data) {
    try {
      const { commentId, reaction, action } = data; // action: 'add' or 'remove'
      
      // Broadcast reaction to all users in relevant rooms
      // This would need to be enhanced based on your reaction system
      socket.broadcast.emit('comment-reaction-updated', {
        commentId,
        userId: socket.userId,
        userName: socket.user.name,
        reaction,
        action,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error handling comment reaction:', error);
    }
  }

  handleCommentCreate(socket, data) {
    try {
      const { objectType, objectId, comment } = data;
      const roomId = `${objectType}-${objectId}`;
      
      // Broadcast new comment to all users in the room except sender
      socket.to(roomId).emit('comment:created', {
        comment,
        author: {
          id: socket.userId,
          name: socket.user.name,
          email: socket.user.email
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error handling comment create:', error);
    }
  }

  handleCommentUpdate(socket, data) {
    try {
      const { objectType, objectId, comment } = data;
      const roomId = `${objectType}-${objectId}`;
      
      // Broadcast comment update to all users in the room except sender
      socket.to(roomId).emit('comment:updated', {
        comment,
        updatedBy: {
          id: socket.userId,
          name: socket.user.name
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error handling comment update:', error);
    }
  }

  handleUserPresence(socket, data) {
    try {
      const { status } = data; // 'online', 'away', 'busy'
      
      // Broadcast user presence to all connected users
      socket.broadcast.emit('user-presence-updated', {
        userId: socket.userId,
        userName: socket.user.name,
        status,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error handling user presence:', error);
    }
  }

  handleDisconnection(socket) {
    const userId = socket.userId;
    console.log(`User ${userId} disconnected from socket ${socket.id}`);

    // Clean up user tracking
    if (this.connectedUsers.has(userId)) {
      this.connectedUsers.get(userId).delete(socket.id);
      if (this.connectedUsers.get(userId).size === 0) {
        this.connectedUsers.delete(userId);
      }
    }
    this.userSockets.delete(socket.id);

    // Clean up typing indicators
    for (const [roomId, typingSet] of this.typingUsers.entries()) {
      if (typingSet.has(userId)) {
        typingSet.delete(userId);
        if (typingSet.size === 0) {
          this.typingUsers.delete(roomId);
        }
        
        // Notify room that user stopped typing
        socket.to(roomId).emit('user-typing', {
          userId,
          userName: socket.user?.name,
          isTyping: false,
          timestamp: new Date().toISOString()
        });
      }
    }

    // Clear any timeouts
    clearTimeout(socket.typingTimeout);

    // Broadcast user offline status if no more connections
    if (!this.connectedUsers.has(userId)) {
      socket.broadcast.emit('user-presence-updated', {
        userId,
        userName: socket.user?.name,
        status: 'offline',
        timestamp: new Date().toISOString()
      });
    }
  }

  // Public methods for external use
  
  // Emit comment created event
  emitCommentCreated(objectType, objectId, comment, mentions = []) {
    const roomId = `${objectType}-${objectId}`;
    this.io.to(roomId).emit('comment:created', {
      comment,
      mentions,
      timestamp: new Date().toISOString()
    });

    // Send notifications to mentioned users
    mentions.forEach(userId => {
      this.io.to(`user-${userId}`).emit('notification', {
        type: 'mention',
        commentId: comment.id,
        objectType,
        objectId,
        message: `You were mentioned in a comment`,
        timestamp: new Date().toISOString()
      });
    });
  }

  // Emit comment updated event
  emitCommentUpdated(objectType, objectId, comment) {
    const roomId = `${objectType}-${objectId}`;
    this.io.to(roomId).emit('comment:updated', {
      comment,
      timestamp: new Date().toISOString()
    });
  }

  // Emit comment deleted event
  emitCommentDeleted(objectType, objectId, commentId, userId) {
    const roomId = `${objectType}-${objectId}`;
    this.io.to(roomId).emit('comment:deleted', {
      commentId,
      deletedBy: userId,
      timestamp: new Date().toISOString()
    });
  }

  // Get online users count
  getOnlineUsersCount() {
    return this.connectedUsers.size;
  }

  // Get users in a specific room
  getRoomUsers(objectType, objectId) {
    const roomId = `${objectType}-${objectId}`;
    const room = this.io.sockets.adapter.rooms.get(roomId);
    
    if (!room) return [];
    
    const users = [];
    for (const socketId of room) {
      const userId = this.userSockets.get(socketId);
      if (userId && !users.find(u => u.id === userId)) {
        const socket = this.io.sockets.sockets.get(socketId);
        if (socket && socket.user) {
          users.push({
            id: userId,
            name: socket.user.name,
            email: socket.user.email
          });
        }
      }
    }
    
    return users;
  }

  // Helper method to get user by ID (you'll need to implement this based on your user model)
  async getUserById(userId) {
    // This should be replaced with your actual user model/service
    try {
      const { Pool } = require('pg');
      const pool = require('../config/database');
      const result = await pool.query('SELECT id, name, email FROM users WHERE id = $1', [userId]);
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  // Get the io instance
  getIO() {
    return this.io;
  }
}

// Export singleton instance
module.exports = new SocketService();