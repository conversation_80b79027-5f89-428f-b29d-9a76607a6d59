import { apiClient, ApiResponse, PaginatedResponse } from './api';
import { Vendor } from '../store/slices/vendorsSlice';
import { Vendor } from '../store/slices/vendorsSlice';

// Vendor API interfaces
export interface VendorFilters {
  search?: string;
  category?: string;
  status?: 'active' | 'inactive' | 'blacklisted';
  performance_min?: number;
  performance_max?: number;
}

export interface VendorCreateData {
  name: string;
  contact_email: string;
  contact_phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  category: string;
  certifications: string[];
  custom_fields?: Record<string, any>;
}

export interface VendorUpdateData extends Partial<VendorCreateData> {
  id: number;
}

export interface AuditEntry {
  id: number;
  entity_type: string;
  entity_id: number;
  action: 'create' | 'update' | 'delete' | 'view' | 'approve';
  user_id: number;
  old_value?: any;
  new_value?: any;
  details?: unknown;
  timestamp: string;
  user_name?: string;
}

export interface RFQHistoryEntry {
  rfq_id: number;
  rfq_title: string;
  rfq_status: 'draft' | 'sent' | 'in_progress' | 'closed' | 'cancelled';
  due_date: string;
  rfq_created_at: string;
  invitation_status: 'sent' | 'viewed' | 'submitted';
  invitation_sent_at: string;
  invitation_viewed_at?: string;
  invitation_submitted_at?: string;
  submission_id?: number;
  bid_amount?: number;
  bid_currency?: string;
  bid_submitted_at?: string;
  quote_id?: number;
  quote_status?: 'draft' | 'sent' | 'viewed' | 'approved' | 'rejected' | 'expired';
  quote_approved_at?: string;
  was_selected: boolean;
}

export interface RFQStatistics {
  total_invitations: number;
  total_submissions: number;
  total_selections: number;
  total_wins: number;
  average_bid_amount: number;
  total_won_value: number;
  response_rate: number;
  selection_rate: number;
  win_rate: number;
}

export interface EngagementAnalytics {
  rfq_invitations_received: number;
  rfq_responses_submitted: number;
  rfq_invitations_viewed: number;
  avg_response_time_hours?: number;
  fastest_response_time_hours?: number;
  slowest_response_time_hours?: number;
  times_selected: number;
  times_won: number;
  average_bid_amount?: number;
  lowest_bid_amount?: number;
  highest_bid_amount?: number;
  total_contract_value?: number;
  recent_invitations: number;
  recent_submissions: number;
  engagement_score: number;
}

export interface RFQOutcome {
  type: 'selected_for_quote' | 'quote_approved' | 'late_submission' | 'no_submission' | 'quality_issue';
  rfqTitle: string;
  rfqId?: number;
  quoteId?: number;
  details?: string;
}

// Real API service class that connects to PostgreSQL backend
export class VendorApiService {
  // Get all vendors with filtering and pagination
  static async getVendors(
    page: number = 1,
    limit: number = 10,
    filters: VendorFilters = {}
  ): Promise<PaginatedResponse<Vendor>> {
    console.log('=== FRONTEND VENDOR API CALL ===');
    console.log('Calling vendor API with:', { page, limit, filters });
    
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    // Add filters to params
    if (filters.search) params.append('search', filters.search);
    if (filters.category) params.append('category', filters.category);
    if (filters.status) params.append('status', filters.status);
    if (filters.performance_min !== undefined) params.append('performance_min', filters.performance_min.toString());
    if (filters.performance_max !== undefined) params.append('performance_max', filters.performance_max.toString());

    const url = `/vendors?${params.toString()}`;
    console.log('API URL:', url);
    console.log('Token in localStorage:', localStorage.getItem('vms_token'));
    
    const response = await apiClient.get(url);
    console.log('Vendor API response:', response.data);
    return response.data;
  }

  // Get vendor by ID
  static async getVendorById(id: number): Promise<ApiResponse<Vendor>> {
    const response = await apiClient.get(`/vendors/${id}`);
    return response.data;
  }

  // Create new vendor
  static async createVendor(vendorData: VendorCreateData): Promise<ApiResponse<Vendor>> {
    const response = await apiClient.post('/vendors', vendorData);
    return response.data;
  }

  // Update vendor
  static async updateVendor(id: number, vendorData: Partial<VendorCreateData>): Promise<ApiResponse<Vendor>> {
    const response = await apiClient.put(`/vendors/${id}`, vendorData);
    return response.data;
  }

  // Delete vendor (soft delete)
  static async deleteVendor(id: number): Promise<ApiResponse<{ id: number }>> {
    const response = await apiClient.delete(`/vendors/${id}`);
    return response.data;
  }

  // Get vendor audit history
  static async getVendorAudit(id: number): Promise<ApiResponse<AuditEntry[]>> {
    const response = await apiClient.get(`/vendors/${id}/audit`);
    return response.data;
  }

  // Get vendor categories (for dropdowns)
  static async getVendorCategories(): Promise<ApiResponse<string[]>> {
    const response = await apiClient.get('/vendors/categories');
    return response.data;
  }

  // Get vendor RFQ history
  static async getVendorRFQHistory(id: number): Promise<ApiResponse<RFQHistoryEntry[]>> {
    const response = await apiClient.get(`/vendors/${id}/rfq-history`);
    return response.data;
  }

  // Get vendor RFQ statistics
  static async getVendorRFQStatistics(id: number): Promise<ApiResponse<RFQStatistics>> {
    const response = await apiClient.get(`/vendors/${id}/rfq-statistics`);
    return response.data;
  }

  // Get vendor engagement analytics
  static async getVendorEngagementAnalytics(id: number): Promise<ApiResponse<EngagementAnalytics>> {
    const response = await apiClient.get(`/vendors/${id}/engagement-analytics`);
    return response.data;
  }

  // Update vendor performance from RFQ outcome
  static async updateVendorPerformanceFromRFQ(id: number, rfqOutcome: RFQOutcome): Promise<ApiResponse<Vendor>> {
    const response = await apiClient.put(`/vendors/${id}/performance-rfq`, { rfqOutcome });
    return response.data;
  }
}

// Export default instance
export default VendorApiService;

// OLD MOCK DATA (REMOVE THIS SECTION)
/*
const defaultMockVendors: Vendor[] = [
  {
    id: 1,
    name: 'TechCorp Solutions',
    contact_email: '<EMAIL>',
    contact_phone: '******-0123',
    address: {
      street: '123 Tech Street',
      city: 'San Francisco',
      state: 'CA',
      zip: '94105',
      country: 'USA',
    },
    category: 'Technology',
    certifications: ['ISO 9001', 'SOC 2'],
    performance_score: 92.5,
    status: 'active',
    custom_fields: {
      tax_id: 'TC123456789',
      website: 'https://techcorp.com',
    },
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-20T14:22:00Z',
  },
  {
    id: 2,
    name: 'Global Manufacturing Inc',
    contact_email: '<EMAIL>',
    contact_phone: '******-0456',
    address: {
      street: '456 Industrial Blvd',
      city: 'Detroit',
      state: 'MI',
      zip: '48201',
      country: 'USA',
    },
    category: 'Manufacturing',
    certifications: ['ISO 14001', 'OSHA'],
    performance_score: 87.3,
    status: 'active',
    custom_fields: {
      tax_id: 'GM987654321',
      specializations: ['Automotive', 'Aerospace'],
    },
    created_at: '2024-01-10T08:15:00Z',
    updated_at: '2024-01-25T11:45:00Z',
  },
  {
    id: 3,
    name: 'Logistics Express',
    contact_email: '<EMAIL>',
    contact_phone: '******-0789',
    address: {
      street: '789 Freight Way',
      city: 'Memphis',
      state: 'TN',
      zip: '38103',
      country: 'USA',
    },
    category: 'Logistics',
    certifications: ['C-TPAT', 'IATA'],
    performance_score: 78.9,
    status: 'inactive',
    custom_fields: {
      tax_id: 'LE456789123',
      fleet_size: 150,
    },
    created_at: '2024-01-05T16:20:00Z',
    updated_at: '2024-01-22T09:30:00Z',
    deactivated_at: '2024-01-22T09:30:00Z',
  },
  {
    id: 4,
    name: 'CloudTech Services',
    contact_email: '<EMAIL>',
    contact_phone: '******-0321',
    address: {
      street: '789 Cloud Avenue',
      city: 'Seattle',
      state: 'WA',
      zip: '98101',
      country: 'USA',
    },
    category: 'Technology',
    certifications: ['AWS Partner', 'ISO 27001'],
    performance_score: 95.2,
    status: 'active',
    custom_fields: {
      tax_id: 'CT987654321',
      website: 'https://cloudtech.com',
      specialties: ['Cloud Migration', 'DevOps'],
    },
    created_at: '2024-02-01T09:15:00Z',
    updated_at: '2024-02-15T16:30:00Z',
  },
  {
    id: 5,
    name: 'Premium Consulting Group',
    contact_email: '<EMAIL>',
    contact_phone: '******-0654',
    address: {
      street: '456 Business Plaza',
      city: 'New York',
      state: 'NY',
      zip: '10001',
      country: 'USA',
    },
    category: 'Services',
    certifications: ['PMP', 'Six Sigma'],
    performance_score: 88.7,
    status: 'active',
    custom_fields: {
      tax_id: 'PC456789123',
      website: 'https://premiumconsulting.com',
      team_size: 50,
    },
    created_at: '2024-01-20T14:45:00Z',
    updated_at: '2024-02-10T10:20:00Z',
  },
*/