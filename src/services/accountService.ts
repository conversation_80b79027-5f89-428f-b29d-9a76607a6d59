import { AccountApi } from './accountApi';
import { 
  Account, 
  AccountCreateRequest, 
  AccountUpdateRequest,
  AccountSearchFilters,
  PaginatedAccounts
} from '../types/account';

// Account Service wrapper for AccountApi
class AccountServiceClass {
  // Get all accounts with filtering and pagination
  async getAccounts(
    filters: AccountSearchFilters = {},
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedAccounts> {
    return AccountApi.getAccounts(filters, page, limit);
  }

  // Get account by ID
  async getAccount(id: number): Promise<Account> {
    return AccountApi.getAccount(id);
  }

  // Create new account
  async createAccount(data: AccountCreateRequest): Promise<Account> {
    return AccountApi.createAccount(data);
  }

  // Update account
  async updateAccount(id: number, data: AccountUpdateRequest): Promise<Account> {
    return AccountApi.updateAccount(id, data);
  }

  // Delete account
  async deleteAccount(id: number): Promise<void> {
    return AccountApi.deleteAccount(id);
  }

  // Get account hierarchy
  async getAccountHierarchy(id: number): Promise<any> {
    return AccountApi.getAccountHierarchy(id);
  }

  // Search accounts
  async searchAccounts(filters: AccountSearchFilters): Promise<PaginatedAccounts> {
    return AccountApi.getAccounts(filters);
  }
}

// Export singleton instance
export const AccountService = new AccountServiceClass();
export default AccountService;