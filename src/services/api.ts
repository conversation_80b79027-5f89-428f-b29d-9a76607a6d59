import axios from 'axios';

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

// Create axios instance with default configuration
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('vms_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('vms_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API Error interface
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, string[]>;
  timestamp: string;
}

// Generic API response interface
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// Pagination interface
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Contract interfaces
export interface ContractData {
  title: string;
  description?: string;
  vendor_id: number;
  start_date: string;
  end_date: string;
  value: number;
  status?: string;
  terms?: string;
}

export interface ContractAmendment {
  description: string;
  changes: Record<string, unknown>;
  effective_date: string;
}

export interface MilestoneUpdate {
  status: string;
  completion_date?: string;
  notes?: string;
}

export interface ESignatureSigner {
  name: string;
  email: string;
  role: string;
}

// Invoice interfaces
export interface InvoiceData {
  vendor_id: number;
  contract_id?: number;
  invoice_number: string;
  amount: number;
  due_date: string;
  description?: string;
  line_items?: InvoiceLineItem[];
}

export interface InvoiceLineItem {
  description: string;
  quantity: number;
  unit_price: number;
  total: number;
}

export interface InvoiceGenerationData {
  description?: string;
  line_items?: InvoiceLineItem[];
  due_date?: string;
}

// Payment interfaces
export interface PaymentData {
  invoice_id: number;
  amount: number;
  payment_method: string;
  gateway?: string;
  reference_number?: string;
}

// Contract API endpoints
export const contractApi = {
  // Get all contracts with filters and pagination
  getContracts: (params?: {
    search?: string;
    status?: string;
    vendor_id?: number;
    start_date?: string;
    end_date?: string;
    page?: number;
    limit?: number;
  }) => {
    // Filter out empty string values to avoid validation errors
    const cleanParams = Object.fromEntries(
      Object.entries(params || {}).filter(([key, value]) => 
        value !== '' && value !== null && value !== undefined
      )
    );
    
    console.log('Clean API params:', cleanParams);
    return apiClient.get('/contracts', { params: cleanParams });
  },

  // Get contract by ID
  getContract: (id: number) => apiClient.get(`/contracts/${id}`),

  // Create new contract
  createContract: (data: ContractData) => apiClient.post('/contracts', data),

  // Update contract
  updateContract: (id: number, data: Partial<ContractData>) => apiClient.put(`/contracts/${id}`, data),

  // Delete contract
  deleteContract: (id: number) => apiClient.delete(`/contracts/${id}`),

  // Get contract templates
  getTemplates: () => apiClient.get('/contracts/templates'),

  // Create contract amendment
  createAmendment: (id: number, data: ContractAmendment) => apiClient.post(`/contracts/${id}/amendments`, data),

  // Update milestone
  updateMilestone: (milestoneId: number, data: MilestoneUpdate) => apiClient.put(`/contracts/milestones/${milestoneId}`, data),

  // Initiate e-signature
  initiateESignature: (id: number, signers: ESignatureSigner[]) => apiClient.post(`/contracts/${id}/esign`, { signers })
};

// Invoice API endpoints
export const invoiceApi = {
  // Get all invoices with filters and pagination
  getInvoices: (params?: {
    search?: string;
    status?: string;
    vendorId?: number;
    contractId?: number;
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: string;
  }) => {
    const cleanParams = Object.fromEntries(
      Object.entries(params || {}).filter(([key, value]) => 
        value !== '' && value !== null && value !== undefined
      )
    );
    return apiClient.get('/invoices', { params: cleanParams });
  },

  // Get invoice by ID
  getInvoice: (id: number) => apiClient.get(`/invoices/${id}`),

  // Create new invoice
  createInvoice: (data: InvoiceData) => apiClient.post('/invoices', data),

  // Update invoice
  updateInvoice: (id: number, data: Partial<InvoiceData>) => apiClient.put(`/invoices/${id}`, data),

  // Delete invoice
  deleteInvoice: (id: number) => apiClient.delete(`/invoices/${id}`),

  // Approve invoice
  approveInvoice: (id: number, comments?: string) => 
    apiClient.post(`/invoices/${id}/approve`, { comments }),

  // Reject invoice
  rejectInvoice: (id: number, reason: string) => 
    apiClient.post(`/invoices/${id}/reject`, { reason }),

  // Submit invoice for approval
  submitForApproval: (id: number) => apiClient.post(`/invoices/${id}/submit`),

  // Generate invoice from contract
  generateFromContract: (contractId: number, data?: InvoiceGenerationData) => 
    apiClient.post(`/invoices/generate/contract/${contractId}`, data),

  // Get invoice statistics
  getStatistics: (params?: {
    vendorId?: number;
    startDate?: string;
    endDate?: string;
  }) => {
    const cleanParams = Object.fromEntries(
      Object.entries(params || {}).filter(([key, value]) => 
        value !== '' && value !== null && value !== undefined
      )
    );
    return apiClient.get('/invoices/statistics', { params: cleanParams });
  }
};

// Payment API endpoints
export const paymentApi = {
  // Get all payments with filters and pagination
  getPayments: (params?: {
    search?: string;
    status?: string;
    gateway?: string;
    invoiceId?: number;
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: string;
  }) => {
    const cleanParams = Object.fromEntries(
      Object.entries(params || {}).filter(([key, value]) => 
        value !== '' && value !== null && value !== undefined
      )
    );
    return apiClient.get('/payments', { params: cleanParams });
  },

  // Get payments for specific invoice
  getPaymentsByInvoice: (invoiceId: number) => 
    apiClient.get(`/payments/invoice/${invoiceId}`),

  // Get payment by transaction ID
  getPaymentByTransaction: (transactionId: string) => 
    apiClient.get(`/payments/transaction/${transactionId}`),

  // Create payment record
  createPayment: (data: PaymentData) => apiClient.post('/payments', data),

  // Update payment status
  updatePaymentStatus: (id: number, data: { status: string; conversionRate?: number; partialAmounts?: any[] }) => 
    apiClient.put(`/payments/${id}/status`, data),

  // Process refund
  processRefund: (id: number, data: { refundAmount: number; reason: string }) => 
    apiClient.post(`/payments/${id}/refund`, data),

  // Get payment statistics
  getStatistics: (params?: {
    startDate?: string;
    endDate?: string;
    gateway?: string;
    status?: string;
  }) => {
    const cleanParams = Object.fromEntries(
      Object.entries(params || {}).filter(([key, value]) => 
        value !== '' && value !== null && value !== undefined
      )
    );
    return apiClient.get('/payments/statistics', { params: cleanParams });
  }
};