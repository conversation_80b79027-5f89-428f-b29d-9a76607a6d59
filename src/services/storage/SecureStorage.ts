import { EncryptionService } from '../encryption/EncryptionService';
import { <PERSON><PERSON><PERSON><PERSON>, AIProviderConfig } from '../ai/types';

/**
 * Secure storage service for sensitive data like API keys
 */
export class SecureStorage {
  private static readonly STORAGE_PREFIX = 'vms_secure_';
  private static readonly CONFIG_KEY = 'ai_configurations';
  private static readonly USAGE_STATS_KEY = 'ai_usage_stats';

  /**
   * Store AI provider configuration securely
   */
  static async storeAIConfiguration(provider: AIProvider, config: AIProviderConfig): Promise<void> {
    try {
      // Get existing configurations
      const configurations = await this.getAIConfigurations();
      
      // Encrypt the API key
      const encryptedConfig = {
        ...config,
        apiKey: await EncryptionService.encrypt(config.apiKey)
      };
      
      // Update configurations
      configurations[provider] = encryptedConfig;
      
      // Store encrypted configurations
      const encryptedData = await EncryptionService.encrypt(JSON.stringify(configurations));
      localStorage.setItem(this.STORAGE_PREFIX + this.CONFIG_KEY, encryptedData);
      
      // Store last updated timestamp
      localStorage.setItem(this.STORAGE_PREFIX + 'last_updated', new Date().toISOString());
    } catch (error) {
      console.error('Failed to store AI configuration:', error);
      throw new Error('Failed to save configuration securely');
    }
  }

  /**
   * Retrieve AI provider configuration
   */
  static async getAIConfiguration(provider: AIProvider): Promise<AIProviderConfig | null> {
    try {
      const configurations = await this.getAIConfigurations();
      const config = configurations[provider];
      
      if (!config) {
        return null;
      }
      
      // Decrypt the API key
      return {
        ...config,
        apiKey: await EncryptionService.decrypt(config.apiKey)
      };
    } catch (error) {
      console.error('Failed to retrieve AI configuration:', error);
      return null;
    }
  }

  /**
   * Get all AI configurations
   */
  static async getAIConfigurations(): Promise<Record<AIProvider, any>> {
    try {
      const encryptedData = localStorage.getItem(this.STORAGE_PREFIX + this.CONFIG_KEY);
      
      if (!encryptedData) {
        return { openai: null, anthropic: null, gemini: null };
      }
      
      const decryptedData = await EncryptionService.decrypt(encryptedData);
      return JSON.parse(decryptedData);
    } catch (error) {
      console.error('Failed to retrieve AI configurations:', error);
      return { openai: null, anthropic: null, gemini: null };
    }
  }

  /**
   * Remove AI provider configuration
   */
  static async removeAIConfiguration(provider: AIProvider): Promise<void> {
    try {
      const configurations = await this.getAIConfigurations();
      delete configurations[provider];
      
      const encryptedData = await EncryptionService.encrypt(JSON.stringify(configurations));
      localStorage.setItem(this.STORAGE_PREFIX + this.CONFIG_KEY, encryptedData);
    } catch (error) {
      console.error('Failed to remove AI configuration:', error);
      throw new Error('Failed to remove configuration');
    }
  }

  /**
   * Check if provider is configured
   */
  static async isProviderConfigured(provider: AIProvider): Promise<boolean> {
    try {
      const config = await this.getAIConfiguration(provider);
      return config !== null && !!config.apiKey;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get current active provider
   */
  static getCurrentProvider(): AIProvider | null {
    return localStorage.getItem(this.STORAGE_PREFIX + 'current_provider') as AIProvider | null;
  }

  /**
   * Set current active provider
   */
  static setCurrentProvider(provider: AIProvider | null): void {
    if (provider) {
      localStorage.setItem(this.STORAGE_PREFIX + 'current_provider', provider);
    } else {
      localStorage.removeItem(this.STORAGE_PREFIX + 'current_provider');
    }
  }

  /**
   * Store usage statistics
   */
  static async storeUsageStats(stats: {
    requestsToday: number;
    tokensUsed: number;
    remainingQuota?: number;
    lastUpdated: string;
  }): Promise<void> {
    try {
      const encryptedData = await EncryptionService.encrypt(JSON.stringify(stats));
      localStorage.setItem(this.STORAGE_PREFIX + this.USAGE_STATS_KEY, encryptedData);
    } catch (error) {
      console.error('Failed to store usage stats:', error);
    }
  }

  /**
   * Retrieve usage statistics
   */
  static async getUsageStats(): Promise<{
    requestsToday: number;
    tokensUsed: number;
    remainingQuota?: number;
    lastUpdated: string;
  } | null> {
    try {
      const encryptedData = localStorage.getItem(this.STORAGE_PREFIX + this.USAGE_STATS_KEY);
      
      if (!encryptedData) {
        return null;
      }
      
      const decryptedData = await EncryptionService.decrypt(encryptedData);
      return JSON.parse(decryptedData);
    } catch (error) {
      console.error('Failed to retrieve usage stats:', error);
      return null;
    }
  }

  /**
   * Clear all secure storage (for logout or reset)
   */
  static async clearAll(): Promise<void> {
    try {
      const keys = Object.keys(localStorage).filter(key => 
        key.startsWith(this.STORAGE_PREFIX)
      );
      
      keys.forEach(key => localStorage.removeItem(key));
      EncryptionService.clearMasterPassword();
    } catch (error) {
      console.error('Failed to clear secure storage:', error);
    }
  }

  /**
   * Export configurations for backup (encrypted)
   */
  static async exportConfigurations(): Promise<string> {
    try {
      const configurations = await this.getAIConfigurations();
      const exportData = {
        configurations,
        timestamp: new Date().toISOString(),
        version: '1.0'
      };
      
      return await EncryptionService.encrypt(JSON.stringify(exportData));
    } catch (error) {
      console.error('Failed to export configurations:', error);
      throw new Error('Failed to export configurations');
    }
  }

  /**
   * Import configurations from backup
   */
  static async importConfigurations(encryptedData: string): Promise<void> {
    try {
      const decryptedData = await EncryptionService.decrypt(encryptedData);
      const importData = JSON.parse(decryptedData);
      
      if (!importData.configurations || !importData.version) {
        throw new Error('Invalid backup format');
      }
      
      // Store imported configurations
      const encryptedConfigs = await EncryptionService.encrypt(
        JSON.stringify(importData.configurations)
      );
      localStorage.setItem(this.STORAGE_PREFIX + this.CONFIG_KEY, encryptedConfigs);
    } catch (error) {
      console.error('Failed to import configurations:', error);
      throw new Error('Failed to import configurations');
    }
  }

  /**
   * Validate storage integrity
   */
  static async validateIntegrity(): Promise<{
    isValid: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];
    
    try {
      // Check if encryption is supported
      if (!EncryptionService.isSupported()) {
        errors.push('Encryption not supported in this environment');
      }
      
      // Try to decrypt stored configurations
      await this.getAIConfigurations();
      
      // Try to decrypt usage stats if they exist
      await this.getUsageStats();
      
    } catch (error) {
      errors.push(`Storage integrity check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get storage size information
   */
  static getStorageInfo(): {
    totalKeys: number;
    secureKeys: number;
    estimatedSize: number;
  } {
    const allKeys = Object.keys(localStorage);
    const secureKeys = allKeys.filter(key => key.startsWith(this.STORAGE_PREFIX));
    
    let estimatedSize = 0;
    secureKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        estimatedSize += key.length + value.length;
      }
    });
    
    return {
      totalKeys: allKeys.length,
      secureKeys: secureKeys.length,
      estimatedSize
    };
  }
}