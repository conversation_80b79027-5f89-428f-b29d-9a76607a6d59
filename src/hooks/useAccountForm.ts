import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { AccountApi } from '../services/accountApi';
import { Account, AccountCreateRequest, AccountUpdateRequest } from '../types/account';
import { toast } from '../components/ui/sonner';

interface UseAccountFormOptions {
  mode: 'create' | 'edit';
  accountId?: number;
  onSuccess?: (account: Account) => void;
  onError?: (error: Error) => void;
  redirectTo?: string;
}

export const useAccountForm = ({
  mode,
  accountId,
  onSuccess,
  onError,
  redirectTo
}: UseAccountFormOptions) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (data: AccountCreateRequest | AccountUpdateRequest) => {
    setIsSubmitting(true);
    
    try {
      let account: Account;
      
      if (mode === 'create') {
        account = await AccountApi.createAccount(data as AccountCreateRequest);
        toast.success('Account created successfully!', {
          description: `${account.name} has been added to your accounts.`,
        });
      } else if (mode === 'edit' && accountId) {
        account = await AccountApi.updateAccount(accountId, data as AccountUpdateRequest);
        toast.success('Account updated successfully!', {
          description: `${account.name} has been updated.`,
        });
      } else {
        throw new Error('Invalid mode or missing account ID for edit');
      }

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(account);
      }

      // Redirect if specified
      if (redirectTo) {
        navigate(redirectTo);
      }

    } catch (error) {
      console.error('Account form submission error:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      
      toast.error(
        mode === 'create' ? 'Failed to create account' : 'Failed to update account',
        {
          description: errorMessage,
        }
      );

      // Call error callback if provided
      if (onError) {
        onError(error instanceof Error ? error : new Error(errorMessage));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (redirectTo) {
      navigate(redirectTo);
    } else {
      navigate(-1); // Go back to previous page
    }
  };

  return {
    handleSubmit,
    handleCancel,
    isSubmitting
  };
};