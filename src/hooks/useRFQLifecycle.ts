import { useState, useEffect, useCallback, useMemo } from 'react';
import { RFQ, RFQStatus, RFQInvitation, RFQSubmission } from '@/types/rfq';
import { RFQApiService } from '@/services/api/rfq';
import RFQNotificationService from '@/services/rfqNotificationService';
import { useToast } from '@/hooks/use-toast';

interface RFQLifecycleState {
  rfq: RFQ | null;
  loading: boolean;
  error: string | null;
  statusHistory: Array<{
    status: RFQStatus;
    timestamp: string;
    reason?: string;
    userId?: number;
  }>;
  canTransition: (toStatus: RFQStatus) => boolean;
  isTransitioning: boolean;
}

interface RFQLifecycleActions {
  loadRFQ: (id: number) => Promise<void>;
  changeStatus: (newStatus: RFQStatus, reason?: string) => Promise<void>;
  sendInvitations: () => Promise<void>;
  refreshData: () => Promise<void>;
  subscribeToUpdates: () => () => void;
  checkDueDateStatus: () => {
    isOverdue: boolean;
    isDueSoon: boolean;
    hoursRemaining: number;
  };
}

export const useRFQLifecycle = (initialRFQId?: number): [RFQLifecycleState, RFQLifecycleActions] => {
  const [state, setState] = useState<RFQLifecycleState>({
    rfq: null,
    loading: false,
    error: null,
    statusHistory: [],
    canTransition: () => false,
    isTransitioning: false
  });

  const { toast } = useToast();
  const notificationService = RFQNotificationService.getInstance();

  // Define valid status transitions
  const getValidTransitions = useCallback((currentStatus: RFQStatus): RFQStatus[] => {
    switch (currentStatus) {
      case 'draft':
        return ['sent', 'cancelled'];
      case 'sent':
        return ['in_progress', 'cancelled'];
      case 'in_progress':
        return ['closed', 'cancelled'];
      case 'closed':
        return ['in_progress']; // Allow reopening
      case 'cancelled':
        return ['draft']; // Allow reactivation
      default:
        return [];
    }
  }, []);

  const canTransition = useCallback((toStatus: RFQStatus): boolean => {
    if (!state.rfq) return false;
    const validTransitions = getValidTransitions(state.rfq.status);
    return validTransitions.includes(toStatus) && !state.isTransitioning;
  }, [state.rfq, state.isTransitioning, getValidTransitions]);

  const loadRFQ = useCallback(async (id: number) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await RFQApiService.getRFQById(id);
      const rfq = response.data;
      
      // Load status history (this would come from audit trail)
      const auditResponse = await RFQApiService.getAuditHistory(id);
      const statusHistory = auditResponse.data
        .filter(entry => entry.action === 'update' && entry.new_value?.status)
        .map(entry => ({
          status: entry.new_value.status,
          timestamp: entry.timestamp,
          reason: entry.details?.reason,
          userId: entry.user_id
        }));

      setState(prev => ({
        ...prev,
        rfq,
        statusHistory,
        loading: false,
        canTransition: (toStatus: RFQStatus) => {
          const validTransitions = getValidTransitions(rfq.status);
          return validTransitions.includes(toStatus);
        }
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load RFQ'
      }));
      toast({
        title: "Error",
        description: "Failed to load RFQ details",
        variant: "destructive",
      });
    }
  }, [getValidTransitions, toast]);

  const changeStatus = useCallback(async (newStatus: RFQStatus, reason?: string) => {
    if (!state.rfq || !canTransition(newStatus)) {
      return;
    }

    setState(prev => ({ ...prev, isTransitioning: true }));
    
    try {
      const oldStatus = state.rfq.status;
      
      // Update RFQ status via API
      const response = await RFQApiService.updateRFQ(state.rfq.id, { 
        status: newStatus 
      });
      
      const updatedRFQ = response.data;
      
      // Update local state
      setState(prev => ({
        ...prev,
        rfq: updatedRFQ,
        statusHistory: [
          {
            status: newStatus,
            timestamp: new Date().toISOString(),
            reason,
            userId: 1 // This would come from auth context
          },
          ...prev.statusHistory
        ],
        isTransitioning: false
      }));

      // Send notification
      notificationService.notifyStatusChange(
        updatedRFQ,
        oldStatus,
        newStatus,
        reason,
        1 // This would come from auth context
      );

      // Handle special status transitions
      if (newStatus === 'sent' && oldStatus === 'draft') {
        // Automatically send invitations when RFQ is sent
        await sendInvitations();
      }

    } catch (error) {
      setState(prev => ({ ...prev, isTransitioning: false }));
      toast({
        title: "Status Update Failed",
        description: "Failed to update RFQ status. Please try again.",
        variant: "destructive",
      });
      throw error;
    }
  }, [state.rfq, canTransition, notificationService, toast]);

  const sendInvitations = useCallback(async () => {
    if (!state.rfq) return;

    try {
      const response = await RFQApiService.sendInvitations(state.rfq.id);
      const { rfq: updatedRFQ, invitations } = response.data;
      
      setState(prev => ({ ...prev, rfq: updatedRFQ }));
      
      // Send notification
      notificationService.notifyInvitationsSent(updatedRFQ, invitations);
      
    } catch (error) {
      toast({
        title: "Failed to Send Invitations",
        description: "Could not send invitations to vendors",
        variant: "destructive",
      });
      throw error;
    }
  }, [state.rfq, notificationService, toast]);

  const refreshData = useCallback(async () => {
    if (state.rfq) {
      await loadRFQ(state.rfq.id);
    }
  }, [state.rfq, loadRFQ]);

  const subscribeToUpdates = useCallback(() => {
    if (!state.rfq) return () => {};

    let unsubscribeRealtime: (() => void) | null = null;

    // Subscribe to real-time updates for this RFQ
    RFQApiService.subscribeToRFQUpdates(
      state.rfq.id,
      (update) => {
        // Handle real-time updates
        setState(prev => ({
          ...prev,
          rfq: { ...prev.rfq!, ...update }
        }));
      }
    ).then((unsubscribe) => {
      unsubscribeRealtime = unsubscribe;
    });

    // Subscribe to notifications
    const unsubscribeNotifications = notificationService.subscribe(
      `rfq-${state.rfq.id}`,
      (event) => {
        if (event.rfqId === state.rfq?.id) {
          // Handle notification events
          switch (event.type) {
            case 'submission_received':
              // Refresh RFQ data when new submission is received
              refreshData();
              break;
            case 'status_change':
              // Update local status if changed externally
              if (event.data.newStatus !== state.rfq?.status) {
                setState(prev => ({
                  ...prev,
                  rfq: prev.rfq ? { ...prev.rfq, status: event.data.newStatus } : null
                }));
              }
              break;
          }
        }
      }
    );

    return () => {
      if (unsubscribeRealtime) {
        unsubscribeRealtime();
      }
      unsubscribeNotifications();
    };
  }, [state.rfq, notificationService, refreshData]);

  const checkDueDateStatus = useCallback(() => {
    if (!state.rfq) {
      return { isOverdue: false, isDueSoon: false, hoursRemaining: 0 };
    }

    const now = new Date();
    const dueDate = new Date(state.rfq.due_date);
    const hoursRemaining = (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    return {
      isOverdue: hoursRemaining < 0,
      isDueSoon: hoursRemaining > 0 && hoursRemaining <= 72, // Due within 3 days
      hoursRemaining: Math.abs(hoursRemaining)
    };
  }, [state.rfq]);

  // Auto-check for due date notifications
  useEffect(() => {
    if (!state.rfq) return;

    const checkInterval = setInterval(() => {
      const dueDateStatus = checkDueDateStatus();
      
      if (dueDateStatus.isOverdue && (state.rfq?.status === 'sent' || state.rfq?.status === 'in_progress')) {
        notificationService.notifyOverdue(state.rfq, dueDateStatus.hoursRemaining);
      } else if (dueDateStatus.isDueSoon && (state.rfq?.status === 'sent' || state.rfq?.status === 'in_progress')) {
        notificationService.notifyDueDateApproaching(state.rfq, dueDateStatus.hoursRemaining);
      }
    }, 60000); // Check every minute

    return () => clearInterval(checkInterval);
  }, [state.rfq, checkDueDateStatus, notificationService]);

  // Load initial RFQ if provided
  useEffect(() => {
    if (initialRFQId) {
      loadRFQ(initialRFQId);
    }
  }, [initialRFQId, loadRFQ]);

  // Update canTransition function when state changes
  useEffect(() => {
    setState(prev => ({
      ...prev,
      canTransition: (toStatus: RFQStatus) => {
        if (!prev.rfq) return false;
        const validTransitions = getValidTransitions(prev.rfq.status);
        return validTransitions.includes(toStatus) && !prev.isTransitioning;
      }
    }));
  }, [state.rfq?.status, state.isTransitioning, getValidTransitions]);

  const actions: RFQLifecycleActions = useMemo(() => ({
    loadRFQ,
    changeStatus,
    sendInvitations,
    refreshData,
    subscribeToUpdates,
    checkDueDateStatus
  }), [loadRFQ, changeStatus, sendInvitations, refreshData, subscribeToUpdates, checkDueDateStatus]);

  return [state, actions];
};

export default useRFQLifecycle;