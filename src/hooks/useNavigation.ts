import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from './useAuth';
import { getRequiredRole } from '@/utils/routeUtils';

export const useNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission } = useAuth();

  const navigateWithPermissionCheck = (path: string, requiredRole?: 'admin' | 'manager' | 'viewer') => {
    const roleRequired = requiredRole || getRequiredRole(path);
    if (roleRequired && !hasPermission(roleRequired)) {
      navigate('/dashboard');
      return false;
    }
    navigate(path);
    return true;
  };

  const isCurrentPath = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const getQueryParam = (param: string) => {
    const searchParams = new URLSearchParams(location.search);
    return searchParams.get(param);
  };

  const setQueryParam = (param: string, value: string) => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.set(param, value);
    navigate(`${location.pathname}?${searchParams.toString()}`, { replace: true });
  };

  const removeQueryParam = (param: string) => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.delete(param);
    const queryString = searchParams.toString();
    navigate(`${location.pathname}${queryString ? `?${queryString}` : ''}`, { replace: true });
  };

  return {
    navigate: navigateWithPermissionCheck,
    isCurrentPath,
    getQueryParam,
    setQueryParam,
    removeQueryParam,
    currentPath: location.pathname,
    currentSearch: location.search,
  };
};