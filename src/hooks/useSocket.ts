import { useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';

let socket: Socket | null = null;

export const useSocket = () => {
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem('token');
    
    if (token && !socket) {
      socket = io('http://localhost:3001', {
        auth: {
          token
        }
      });

      socket.on('connect', () => {
        setIsConnected(true);
        console.log('Connected to server');
      });

      socket.on('disconnect', () => {
        setIsConnected(false);
        console.log('Disconnected from server');
      });

      socket.on('connect_error', (error) => {
        console.error('Connection error:', error);
        setIsConnected(false);
      });
    }

    return () => {
      if (socket) {
        socket.disconnect();
        socket = null;
        setIsConnected(false);
      }
    };
  }, []);

  return socket;
};

export const getSocket = () => socket;

export const disconnectSocket = () => {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
};