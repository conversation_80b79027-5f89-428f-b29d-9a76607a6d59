import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { toast } from '../components/ui/sonner';
import { AppDispatch } from '../store';
import { createVendorAsync, updateVendorAsync } from '../store/slices/vendorsSlice';
import { VendorCreateData } from '../services/vendorApi';
import { Vendor } from '../store/slices/vendorsSlice';

interface UseVendorFormOptions {
  mode: 'create' | 'edit';
  vendorId?: number;
  initialData?: Partial<Vendor>;
  onSuccess?: (vendor: Vendor) => void;
  redirectTo?: string;
}

export const useVendorForm = ({
  mode,
  vendorId,
  initialData,
  onSuccess,
  redirectTo,
}: UseVendorFormOptions) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const handleSubmit = useCallback(async (data: VendorCreateData) => {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      let result;
      
      if (mode === 'create') {
        result = await dispatch(createVendorAsync(data)).unwrap();
        toast.success('Vendor created successfully!', {
          description: `${data.name} has been added to your vendor list.`,
        });
      } else if (mode === 'edit' && vendorId) {
        result = await dispatch(updateVendorAsync({ id: vendorId, data })).unwrap();
        toast.success('Vendor updated successfully!', {
          description: `${data.name} information has been updated.`,
        });
      }

      // Call success callback if provided
      if (onSuccess && result) {
        onSuccess(result);
      }

      // Navigate to specified route or default
      if (redirectTo) {
        navigate(redirectTo);
      } else if (mode === 'create') {
        navigate('/vendors/list');
      } else if (mode === 'edit' && vendorId) {
        navigate(`/vendors/${vendorId}`);
      }

    } catch (error: any) {
      const errorMessage = error.message || 'An error occurred while saving the vendor';
      setSubmitError(errorMessage);
      
      toast.error(mode === 'create' ? 'Failed to create vendor' : 'Failed to update vendor', {
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [dispatch, mode, vendorId, onSuccess, redirectTo, navigate]);

  const handleCancel = useCallback(() => {
    if (mode === 'edit' && vendorId) {
      navigate(`/vendors/${vendorId}`);
    } else {
      navigate('/vendors/list');
    }
  }, [mode, vendorId, navigate]);

  return {
    handleSubmit,
    handleCancel,
    isSubmitting,
    submitError,
    clearError: () => setSubmitError(null),
  };
};

// Utility hook for form validation
export const useVendorFormValidation = () => {
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const validateEmail = useCallback((email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }, []);

  const validatePhone = useCallback((phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone);
  }, []);

  const validateRequired = useCallback((value: unknown, fieldName: string): string | null => {
    if (value === null || value === undefined || value === '') {
      return `${fieldName} is required`;
    }
    return null;
  }, []);

  const validateForm = useCallback((data: VendorCreateData): boolean => {
    const errors: Record<string, string> = {};

    // Validate required fields
    const requiredError = validateRequired(data.name, 'Vendor name');
    if (requiredError) errors.name = requiredError;

    const emailRequiredError = validateRequired(data.contact_email, 'Email');
    if (emailRequiredError) {
      errors.contact_email = emailRequiredError;
    } else if (!validateEmail(data.contact_email)) {
      errors.contact_email = 'Invalid email format';
    }

    const phoneRequiredError = validateRequired(data.contact_phone, 'Phone number');
    if (phoneRequiredError) {
      errors.contact_phone = phoneRequiredError;
    } else if (!validatePhone(data.contact_phone)) {
      errors.contact_phone = 'Invalid phone number format';
    }

    // Validate address
    if (!data.address.street) errors['address.street'] = 'Street address is required';
    if (!data.address.city) errors['address.city'] = 'City is required';
    if (!data.address.state) errors['address.state'] = 'State is required';
    if (!data.address.zip) errors['address.zip'] = 'ZIP code is required';
    if (!data.address.country) errors['address.country'] = 'Country is required';

    if (!data.category) errors.category = 'Category is required';

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [validateRequired, validateEmail, validatePhone]);

  return {
    validationErrors,
    validateForm,
    clearValidationErrors: () => setValidationErrors({}),
  };
};

// Hook for managing certifications
export const useCertifications = (initialCertifications: string[] = []) => {
  const [certifications, setCertifications] = useState<string[]>(initialCertifications);

  const addCertification = useCallback(() => {
    setCertifications(prev => [...prev, '']);
  }, []);

  const removeCertification = useCallback((index: number) => {
    setCertifications(prev => prev.filter((_, i) => i !== index));
  }, []);

  const updateCertification = useCallback((index: number, value: string) => {
    setCertifications(prev => {
      const updated = [...prev];
      updated[index] = value;
      return updated;
    });
  }, []);

  const getCleanCertifications = useCallback(() => {
    return certifications.filter(cert => cert.trim() !== '');
  }, [certifications]);

  return {
    certifications,
    addCertification,
    removeCertification,
    updateCertification,
    getCleanCertifications,
    setCertifications,
  };
};