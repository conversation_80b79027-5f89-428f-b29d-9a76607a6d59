import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { RFQApiService } from '@/services/api/rfq';
import { RFQ, RFQFilters, RFQListState } from '@/types/rfq';
import RFQErrorHandler from '@/services/api/rfqErrorHandler';

interface UseRFQListOptions {
  initialPage?: number;
  initialLimit?: number;
  initialFilters?: RFQFilters;
  autoLoad?: boolean;
}

export const useRFQList = (options: UseRFQListOptions = {}) => {
  const {
    initialPage = 1,
    initialLimit = 12,
    initialFilters = {},
    autoLoad = true
  } = options;

  const navigate = useNavigate();

  const [state, setState] = useState<RFQListState>({
    rfqs: [],
    loading: false,
    error: null,
    filters: initialFilters,
    searchQuery: '',
    selectedRFQs: [],
    pagination: {
      page: initialPage,
      limit: initialLimit,
      total: 0,
      totalPages: 0
    }
  });

  // Load RFQs from API
  const loadRFQs = useCallback(async (
    page: number = state.pagination.page,
    limit: number = state.pagination.limit,
    filters: RFQFilters = state.filters,
    searchQuery: string = state.searchQuery
  ) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Add search query to filters if present
      const searchFilters = searchQuery.trim() 
        ? { ...filters, search: searchQuery.trim() }
        : filters;

      const response = await RFQApiService.getRFQs(page, limit, searchFilters);

      setState(prev => ({
        ...prev,
        rfqs: response.data || [],
        pagination: response.pagination || prev.pagination,
        loading: false,
        error: null
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load RFQs'
      }));
    }
  }, [state.pagination.page, state.pagination.limit, state.filters, state.searchQuery]);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setState(prev => ({
      ...prev,
      searchQuery: query,
      pagination: { ...prev.pagination, page: 1 } // Reset to first page
    }));
  }, []);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: RFQFilters) => {
    setState(prev => ({
      ...prev,
      filters: newFilters,
      pagination: { ...prev.pagination, page: 1 } // Reset to first page
    }));
  }, []);

  // Handle pagination
  const handlePageChange = useCallback((page: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page }
    }));
  }, []);

  const handleLimitChange = useCallback((limit: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, limit, page: 1 }
    }));
  }, []);

  // Handle RFQ selection
  const handleRFQSelection = useCallback((rfqId: number, selected: boolean) => {
    setState(prev => ({
      ...prev,
      selectedRFQs: selected
        ? [...prev.selectedRFQs, rfqId]
        : prev.selectedRFQs.filter(id => id !== rfqId)
    }));
  }, []);

  const handleSelectAll = useCallback((selected: boolean) => {
    setState(prev => ({
      ...prev,
      selectedRFQs: selected ? prev.rfqs.map(rfq => rfq.id) : []
    }));
  }, []);

  // RFQ actions
  const handleViewRFQ = useCallback((id: number) => {
    navigate(`/rfqs/${id}`);
  }, [navigate]);

  const handleEditRFQ = useCallback((id: number) => {
    navigate(`/rfqs/${id}/edit`);
  }, [navigate]);

  const handleDeleteRFQ = useCallback(async (id: number) => {
    const rfq = state.rfqs.find(r => r.id === id);
    if (!rfq) return;

    if (!confirm(`Are you sure you want to delete "${rfq.title}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await RFQApiService.deleteRFQ(id);
      RFQErrorHandler.showRFQDeleted(rfq.title);
      
      // Reload the current page
      await loadRFQs();
    } catch (error) {
      // Error is handled by the API service
    }
  }, [state.rfqs, loadRFQs]);

  const handleSendInvitations = useCallback(async (id: number) => {
    const rfq = state.rfqs.find(r => r.id === id);
    if (!rfq) return;

    if (!confirm(`Send invitations for "${rfq.title}" to ${rfq.invitation_count} vendors?`)) {
      return;
    }

    try {
      await RFQApiService.sendInvitations(id);
      RFQErrorHandler.showInvitationsSent(rfq.invitation_count);
      
      // Reload the current page to reflect status change
      await loadRFQs();
    } catch (error) {
      // Error is handled by the API service
    }
  }, [state.rfqs, loadRFQs]);

  // Bulk operations
  const handleBulkDelete = useCallback(async () => {
    if (state.selectedRFQs.length === 0) return;

    const selectedRFQs = state.rfqs.filter(rfq => state.selectedRFQs.includes(rfq.id));
    const rfqTitles = selectedRFQs.map(rfq => rfq.title).join(', ');

    if (!confirm(`Are you sure you want to delete ${state.selectedRFQs.length} RFQ(s)?\n\n${rfqTitles}\n\nThis action cannot be undone.`)) {
      return;
    }

    try {
      const response = await RFQApiService.bulkDelete(state.selectedRFQs);
      RFQErrorHandler.showBulkOperationSuccess('Delete', response.data.deleted);
      
      // Clear selection and reload
      setState(prev => ({ ...prev, selectedRFQs: [] }));
      await loadRFQs();
    } catch (error) {
      // Error is handled by the API service
    }
  }, [state.selectedRFQs, state.rfqs, loadRFQs]);

  const handleBulkStatusUpdate = useCallback(async (status: string, reason?: string) => {
    if (state.selectedRFQs.length === 0) return;

    try {
      const response = await RFQApiService.bulkUpdateStatus(state.selectedRFQs, status, reason);
      RFQErrorHandler.showBulkOperationSuccess(`Status update to ${status}`, response.data.updated);
      
      // Clear selection and reload
      setState(prev => ({ ...prev, selectedRFQs: [] }));
      await loadRFQs();
    } catch (error) {
      // Error is handled by the API service
    }
  }, [state.selectedRFQs, loadRFQs]);

  // Export functionality
  const handleExport = useCallback(async (format: 'csv' | 'excel' = 'csv') => {
    try {
      const searchFilters = state.searchQuery.trim() 
        ? { ...state.filters, search: state.searchQuery.trim() }
        : state.filters;

      const response = await RFQApiService.exportRFQs(searchFilters, format);
      RFQErrorHandler.showExportReady(response.data.filename);
      
      // Trigger download
      window.open(response.data.download_url, '_blank');
    } catch (error) {
      // Error is handled by the API service
    }
  }, [state.filters, state.searchQuery]);

  // Refresh data
  const refresh = useCallback(() => {
    loadRFQs();
  }, [loadRFQs]);

  // Load data when dependencies change
  useEffect(() => {
    if (autoLoad) {
      loadRFQs(
        state.pagination.page,
        state.pagination.limit,
        state.filters,
        state.searchQuery
      );
    }
  }, [state.pagination.page, state.pagination.limit, state.filters, state.searchQuery]);

  // Initial load
  useEffect(() => {
    if (autoLoad && state.rfqs.length === 0 && !state.loading) {
      loadRFQs();
    }
  }, []);

  return {
    // State
    rfqs: state.rfqs,
    loading: state.loading,
    error: state.error,
    filters: state.filters,
    searchQuery: state.searchQuery,
    selectedRFQs: state.selectedRFQs,
    pagination: state.pagination,

    // Actions
    handleSearch,
    handleFiltersChange,
    handlePageChange,
    handleLimitChange,
    handleRFQSelection,
    handleSelectAll,
    handleViewRFQ,
    handleEditRFQ,
    handleDeleteRFQ,
    handleSendInvitations,
    handleBulkDelete,
    handleBulkStatusUpdate,
    handleExport,
    refresh,

    // Computed values
    hasSelection: state.selectedRFQs.length > 0,
    isAllSelected: state.selectedRFQs.length === state.rfqs.length && state.rfqs.length > 0,
    totalResults: state.pagination.total
  };
};

export default useRFQList;