import * as yup from 'yup';

// Vendor validation schema
export const vendorValidationSchema = yup.object({
  name: yup
    .string()
    .required('Vendor name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(255, 'Name must be less than 255 characters')
    .trim(),
  
  contact_email: yup
    .string()
    .email('Invalid email format')
    .required('Email is required')
    .max(255, 'Email must be less than 255 characters')
    .trim(),
  
  contact_phone: yup
    .string()
    .required('Phone number is required')
    .matches(
      /^[\+]?[1-9][\d]{0,15}$/,
      'Invalid phone number format'
    )
    .max(50, 'Phone number must be less than 50 characters'),
  
  address: yup.object({
    street: yup
      .string()
      .required('Street address is required')
      .max(255, 'Street address must be less than 255 characters')
      .trim(),
    
    city: yup
      .string()
      .required('City is required')
      .max(100, 'City must be less than 100 characters')
      .trim(),
    
    state: yup
      .string()
      .required('State is required')
      .max(100, 'State must be less than 100 characters')
      .trim(),
    
    zip: yup
      .string()
      .required('ZIP code is required')
      .max(20, 'ZIP code must be less than 20 characters')
      .trim(),
    
    country: yup
      .string()
      .required('Country is required')
      .max(100, 'Country must be less than 100 characters')
      .trim(),
  }).required('Address is required'),
  
  category: yup
    .string()
    .required('Category is required')
    .max(100, 'Category must be less than 100 characters'),
  
  certifications: yup
    .array()
    .of(yup.string().max(100, 'Certification name must be less than 100 characters'))
    .default([]),
  
  custom_fields: yup
    .object()
    .default({}),
});

// Vendor update schema (all fields optional except id)
export const vendorUpdateValidationSchema = yup.object({
  id: yup.number().required('Vendor ID is required'),
  name: yup
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(255, 'Name must be less than 255 characters')
    .trim(),
  
  contact_email: yup
    .string()
    .email('Invalid email format')
    .max(255, 'Email must be less than 255 characters')
    .trim(),
  
  contact_phone: yup
    .string()
    .matches(
      /^[\+]?[1-9][\d]{0,15}$/,
      'Invalid phone number format'
    )
    .max(50, 'Phone number must be less than 50 characters'),
  
  address: yup.object({
    street: yup
      .string()
      .max(255, 'Street address must be less than 255 characters')
      .trim(),
    
    city: yup
      .string()
      .max(100, 'City must be less than 100 characters')
      .trim(),
    
    state: yup
      .string()
      .max(100, 'State must be less than 100 characters')
      .trim(),
    
    zip: yup
      .string()
      .max(20, 'ZIP code must be less than 20 characters')
      .trim(),
    
    country: yup
      .string()
      .max(100, 'Country must be less than 100 characters')
      .trim(),
  }),
  
  category: yup
    .string()
    .max(100, 'Category must be less than 100 characters'),
  
  certifications: yup
    .array()
    .of(yup.string().max(100, 'Certification name must be less than 100 characters')),
  
  custom_fields: yup.object(),
});

// Filter validation schema
export const vendorFiltersValidationSchema = yup.object({
  search: yup.string().max(255, 'Search term must be less than 255 characters'),
  category: yup.string().max(100, 'Category must be less than 100 characters'),
  status: yup.string().oneOf(['active', 'inactive', 'blacklisted'], 'Invalid status'),
  performance_min: yup.number().min(0, 'Minimum performance must be at least 0').max(100, 'Minimum performance must be at most 100'),
  performance_max: yup.number().min(0, 'Maximum performance must be at least 0').max(100, 'Maximum performance must be at most 100'),
});

// Common validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone);
};

export const sanitizeString = (str: string): string => {
  return str.trim().replace(/[<>]/g, '');
};

export const validateRequired = (value: any, fieldName: string): string | null => {
  if (value === null || value === undefined || value === '') {
    return `${fieldName} is required`;
  }
  return null;
};

// Form validation helper
export const validateForm = async <T>(
  schema: yup.Schema<T>,
  data: any
): Promise<{ isValid: boolean; errors: Record<string, string>; data?: T }> => {
  try {
    const validatedData = await schema.validate(data, { abortEarly: false });
    return { isValid: true, errors: {}, data: validatedData };
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      const errors: Record<string, string> = {};
      error.inner.forEach((err) => {
        if (err.path) {
          errors[err.path] = err.message;
        }
      });
      return { isValid: false, errors };
    }
    return { isValid: false, errors: { general: 'Validation failed' } };
  }
};

// RFQ validation schema
export const rfqValidationSchema = yup.object({
  title: yup
    .string()
    .required('RFQ title is required')
    .min(3, 'Title must be at least 3 characters')
    .max(255, 'Title must be less than 255 characters')
    .trim(),
  
  description: yup
    .string()
    .required('Description is required')
    .min(10, 'Description must be at least 10 characters')
    .max(5000, 'Description must be less than 5000 characters')
    .trim(),
  
  dueDate: yup
    .date()
    .required('Due date is required')
    .min(new Date(), 'Due date must be in the future'),
  
  items: yup
    .array()
    .of(
      yup.object({
        id: yup.string().required(),
        name: yup
          .string()
          .required('Item name is required')
          .min(1, 'Item name is required')
          .max(255, 'Item name must be less than 255 characters')
          .trim(),
        description: yup
          .string()
          .max(1000, 'Item description must be less than 1000 characters')
          .trim(),
        quantity: yup
          .number()
          .required('Quantity is required')
          .min(1, 'Quantity must be at least 1')
          .integer('Quantity must be a whole number'),
        category: yup
          .string()
          .required('Category is required')
          .max(100, 'Category must be less than 100 characters'),
        estimatedPrice: yup
          .number()
          .min(0, 'Estimated price must be positive')
          .nullable(),
        specifications: yup.object().default({}),
      })
    )
    .min(1, 'At least one item is required')
    .max(100, 'Cannot have more than 100 items'),
  
  selectedVendors: yup
    .array()
    .of(yup.number().required())
    .min(1, 'At least one vendor must be selected')
    .max(50, 'Cannot select more than 50 vendors'),
  
  formConfig: yup
    .array()
    .of(
      yup.object({
        id: yup.string().required(),
        type: yup
          .string()
          .oneOf(['text', 'number', 'date', 'file', 'select', 'textarea'], 'Invalid field type')
          .required('Field type is required'),
        label: yup
          .string()
          .required('Field label is required')
          .min(1, 'Field label is required')
          .max(100, 'Field label must be less than 100 characters')
          .trim(),
        required: yup.boolean().default(false),
        itemSpecific: yup.boolean().default(true),
        options: yup
          .array()
          .of(yup.string().max(100, 'Option must be less than 100 characters'))
          .when('type', {
            is: 'select',
            then: (schema) => schema.min(1, 'Select fields must have at least one option'),
            otherwise: (schema) => schema.notRequired(),
          }),
        validation: yup.array().default([]),
      })
    )
    .min(1, 'At least one form field is required')
    .max(50, 'Cannot have more than 50 form fields'),
  
  terms: yup
    .string()
    .max(2000, 'Terms and conditions must be less than 2000 characters')
    .trim(),
  
  aiSettings: yup.object({
    priceWeight: yup
      .number()
      .min(0, 'Price weight must be between 0 and 1')
      .max(1, 'Price weight must be between 0 and 1')
      .default(0.4),
    performanceWeight: yup
      .number()
      .min(0, 'Performance weight must be between 0 and 1')
      .max(1, 'Performance weight must be between 0 and 1')
      .default(0.3),
    deliveryWeight: yup
      .number()
      .min(0, 'Delivery weight must be between 0 and 1')
      .max(1, 'Delivery weight must be between 0 and 1')
      .default(0.2),
    riskWeight: yup
      .number()
      .min(0, 'Risk weight must be between 0 and 1')
      .max(1, 'Risk weight must be between 0 and 1')
      .default(0.1),
    diversificationPreference: yup
      .number()
      .min(0, 'Diversification preference must be between 0 and 1')
      .max(1, 'Diversification preference must be between 0 and 1')
      .default(0.5),
  }).default({}),
  
  allowPartialSelection: yup
    .boolean()
    .default(false),
  
  partialSelectionConfig: yup.object({
    enabled: yup.boolean().default(false),
    requireVendorConfirmation: yup.boolean().default(true),
    confirmationMessage: yup
      .string()
      .max(500, 'Confirmation message must be less than 500 characters')
      .default('Do you allow individual item purchases at the quoted rates?'),
    instructions: yup
      .string()
      .max(1000, 'Instructions must be less than 1000 characters')
      .default('Please confirm if you allow partial selection of items from your submission.'),
    defaultAllowed: yup.boolean().default(false),
  }).default({}),
});

// Export validation schemas
export default {
  vendor: vendorValidationSchema,
  vendorUpdate: vendorUpdateValidationSchema,
  vendorFilters: vendorFiltersValidationSchema,
  rfq: rfqValidationSchema,
};