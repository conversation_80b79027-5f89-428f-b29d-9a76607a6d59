// Database connection utilities
// This file will be used when connecting to a real PostgreSQL backend

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
}

export interface QueryResult<T = any> {
  rows: T[];
  rowCount: number;
}

// Database query builder helpers
export class QueryBuilder {
  private query: string = '';
  private params: any[] = [];
  private paramCount: number = 0;

  select(columns: string[] | string = '*'): this {
    const cols = Array.isArray(columns) ? columns.join(', ') : columns;
    this.query += `SELECT ${cols}`;
    return this;
  }

  from(table: string): this {
    this.query += ` FROM ${table}`;
    return this;
  }

  where(condition: string, value?: any): this {
    const prefix = this.query.includes('WHERE') ? ' AND' : ' WHERE';
    if (value !== undefined) {
      this.paramCount++;
      this.query += `${prefix} ${condition.replace('?', `$${this.paramCount}`)}`;
      this.params.push(value);
    } else {
      this.query += `${prefix} ${condition}`;
    }
    return this;
  }

  orderBy(column: string, direction: 'ASC' | 'DESC' = 'ASC'): this {
    const prefix = this.query.includes('ORDER BY') ? ',' : ' ORDER BY';
    this.query += `${prefix} ${column} ${direction}`;
    return this;
  }

  limit(count: number): this {
    this.paramCount++;
    this.query += ` LIMIT $${this.paramCount}`;
    this.params.push(count);
    return this;
  }

  offset(count: number): this {
    this.paramCount++;
    this.query += ` OFFSET $${this.paramCount}`;
    this.params.push(count);
    return this;
  }

  build(): { query: string; params: any[] } {
    return {
      query: this.query,
      params: this.params,
    };
  }
}

// Vendor-specific database queries
export class VendorQueries {
  static getVendors(filters: any = {}, page: number = 1, limit: number = 10) {
    const builder = new QueryBuilder()
      .select([
        'id', 'name', 'contact_email', 'contact_phone', 'address',
        'category', 'certifications', 'performance_score', 'status',
        'custom_fields', 'created_at', 'updated_at', 'deactivated_at',
        'blacklisted_reason'
      ])
      .from('vendors');

    // Apply filters
    if (filters.search) {
      builder.where('(name ILIKE ? OR contact_email ILIKE ? OR category ILIKE ?)', 
        `%${filters.search}%`);
      builder.where('(name ILIKE ? OR contact_email ILIKE ? OR category ILIKE ?)', 
        `%${filters.search}%`);
      builder.where('(name ILIKE ? OR contact_email ILIKE ? OR category ILIKE ?)', 
        `%${filters.search}%`);
    }

    if (filters.category) {
      builder.where('category = ?', filters.category);
    }

    if (filters.status) {
      builder.where('status = ?', filters.status);
    }

    if (filters.performance_min !== undefined) {
      builder.where('performance_score >= ?', filters.performance_min);
    }

    if (filters.performance_max !== undefined) {
      builder.where('performance_score <= ?', filters.performance_max);
    }

    // Add ordering and pagination
    builder
      .orderBy('created_at', 'DESC')
      .limit(limit)
      .offset((page - 1) * limit);

    return builder.build();
  }

  static getVendorById(id: number) {
    return new QueryBuilder()
      .select()
      .from('vendors')
      .where('id = ?', id)
      .build();
  }

  static createVendor(vendorData: any) {
    const columns = Object.keys(vendorData);
    const placeholders = columns.map((_, index) => `$${index + 1}`).join(', ');
    const values = Object.values(vendorData);

    return {
      query: `
        INSERT INTO vendors (${columns.join(', ')})
        VALUES (${placeholders})
        RETURNING *
      `,
      params: values,
    };
  }

  static updateVendor(id: number, vendorData: any) {
    const columns = Object.keys(vendorData);
    const setClause = columns.map((col, index) => `${col} = $${index + 2}`).join(', ');
    const values = [id, ...Object.values(vendorData)];

    return {
      query: `
        UPDATE vendors 
        SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING *
      `,
      params: values,
    };
  }

  static deleteVendor(id: number) {
    return {
      query: `
        UPDATE vendors 
        SET status = 'inactive', deactivated_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING id
      `,
      params: [id],
    };
  }

  static getVendorAudit(id: number) {
    return new QueryBuilder()
      .select([
        'a.id', 'a.entity_type', 'a.entity_id', 'a.action',
        'a.user_id', 'a.old_value', 'a.new_value', 'a.details',
        'a.timestamp', 'u.email as user_name'
      ])
      .from('audits a')
      .where('a.entity_type = ?', 'vendors')
      .where('a.entity_id = ?', id)
      .orderBy('a.timestamp', 'DESC')
      .build();
  }

  static getVendorCategories() {
    return {
      query: 'SELECT DISTINCT category FROM vendors WHERE category IS NOT NULL ORDER BY category',
      params: [],
    };
  }
}

// Error handling utilities
export class DatabaseError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

// Connection pool management (for future backend implementation)
export interface ConnectionPool {
  query<T = any>(text: string, params?: any[]): Promise<QueryResult<T>>;
  connect(): Promise<void>;
  end(): Promise<void>;
}

// Mock connection pool for development
export class MockConnectionPool implements ConnectionPool {
  async query<T = any>(text: string, params?: any[]): Promise<QueryResult<T>> {
    console.log('Mock Query:', text, params);
    // This would be replaced with actual database queries
    return { rows: [], rowCount: 0 };
  }

  async connect(): Promise<void> {
    console.log('Mock database connection established');
  }

  async end(): Promise<void> {
    console.log('Mock database connection closed');
  }
}

// Export utilities
export const db = new MockConnectionPool();