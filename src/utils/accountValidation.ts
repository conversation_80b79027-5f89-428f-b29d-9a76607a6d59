import * as yup from 'yup';
import { 
  AccountType, 
  Industry, 
  Rating, 
  Ownership, 
  AccountStatus,
  ACCOUNT_TYPES,
  INDUSTRIES,
  RATINGS,
  OWNERSHIP_TYPES,
  ACCOUNT_STATUSES
} from '../types/account';

// Address validation schema
const addressSchema = yup.object({
  street: yup.string().max(255, 'Street address must be less than 255 characters'),
  city: yup.string().max(100, 'City must be less than 100 characters'),
  state: yup.string().max(100, 'State must be less than 100 characters'),
  postalCode: yup.string().max(20, 'Postal code must be less than 20 characters'),
  country: yup.string().max(100, 'Country must be less than 100 characters'),
  latitude: yup.number().min(-90, 'Latitude must be between -90 and 90').max(90, 'Latitude must be between -90 and 90'),
  longitude: yup.number().min(-180, 'Longitude must be between -180 and 180').max(180, 'Longitude must be between -180 and 180')
});

// Account creation validation schema
export const accountCreateValidationSchema = yup.object({
  name: yup
    .string()
    .required('Account name is required')
    .min(1, 'Account name cannot be empty')
    .max(255, 'Account name must be less than 255 characters')
    .trim(),
  
  accountNumber: yup
    .string()
    .max(100, 'Account number must be less than 100 characters')
    .trim(),
  
  type: yup
    .string()
    .oneOf(ACCOUNT_TYPES, `Account type must be one of: ${ACCOUNT_TYPES.join(', ')}`),
  
  industry: yup
    .string()
    .oneOf(INDUSTRIES, `Industry must be one of: ${INDUSTRIES.join(', ')}`),
  
  annualRevenue: yup
    .number()
    .min(0, 'Annual revenue cannot be negative')
    .transform((value, originalValue) => {
      // Handle empty string or null
      if (originalValue === '' || originalValue === null || originalValue === undefined) {
        return undefined;
      }
      return value;
    }),
  
  numberOfEmployees: yup
    .number()
    .integer('Number of employees must be a whole number')
    .min(0, 'Number of employees cannot be negative')
    .transform((value, originalValue) => {
      // Handle empty string or null
      if (originalValue === '' || originalValue === null || originalValue === undefined) {
        return undefined;
      }
      return value;
    }),
  
  ownership: yup
    .string()
    .oneOf(OWNERSHIP_TYPES, `Ownership must be one of: ${OWNERSHIP_TYPES.join(', ')}`),
  
  phone: yup
    .string()
    .max(50, 'Phone number must be less than 50 characters')
    .matches(/^[\+]?[1-9][\d\s\-\(\)]{0,20}$/, 'Invalid phone number format'),
  
  fax: yup
    .string()
    .max(50, 'Fax number must be less than 50 characters')
    .matches(/^[\+]?[1-9][\d\s\-\(\)]{0,20}$/, 'Invalid fax number format'),
  
  website: yup
    .string()
    .url('Website must be a valid URL')
    .max(255, 'Website URL must be less than 255 characters'),
  
  tickerSymbol: yup
    .string()
    .max(10, 'Ticker symbol must be less than 10 characters')
    .matches(/^[A-Z]{1,10}$/, 'Ticker symbol must contain only uppercase letters')
    .transform((value) => value?.toUpperCase()),
  
  site: yup
    .string()
    .max(255, 'Site must be less than 255 characters'),
  
  rating: yup
    .string()
    .oneOf(RATINGS, `Rating must be one of: ${RATINGS.join(', ')}`),
  
  description: yup
    .string()
    .max(5000, 'Description must be less than 5000 characters'),
  
  billingAddress: addressSchema,
  
  shippingAddress: addressSchema,
  
  parentAccountId: yup
    .number()
    .integer('Parent account ID must be a whole number')
    .positive('Parent account ID must be positive')
    .transform((value, originalValue) => {
      // Handle empty string or null
      if (originalValue === '' || originalValue === null || originalValue === undefined) {
        return undefined;
      }
      return value;
    }),
  
  ownerId: yup
    .number()
    .integer('Owner ID must be a whole number')
    .positive('Owner ID must be positive')
    .transform((value, originalValue) => {
      // Handle empty string or null
      if (originalValue === '' || originalValue === null || originalValue === undefined) {
        return undefined;
      }
      return value;
    }),
  
  customFields: yup
    .object()
    .default({})
});

// Account update validation schema (all fields optional except constraints)
export const accountUpdateValidationSchema = yup.object({
  name: yup
    .string()
    .min(1, 'Account name cannot be empty')
    .max(255, 'Account name must be less than 255 characters')
    .trim(),
  
  accountNumber: yup
    .string()
    .max(100, 'Account number must be less than 100 characters')
    .trim(),
  
  type: yup
    .string()
    .oneOf(ACCOUNT_TYPES, `Account type must be one of: ${ACCOUNT_TYPES.join(', ')}`),
  
  industry: yup
    .string()
    .oneOf(INDUSTRIES, `Industry must be one of: ${INDUSTRIES.join(', ')}`),
  
  annualRevenue: yup
    .number()
    .min(0, 'Annual revenue cannot be negative')
    .nullable()
    .transform((value, originalValue) => {
      if (originalValue === '' || originalValue === null || originalValue === undefined) {
        return null;
      }
      return value;
    }),
  
  numberOfEmployees: yup
    .number()
    .integer('Number of employees must be a whole number')
    .min(0, 'Number of employees cannot be negative')
    .nullable()
    .transform((value, originalValue) => {
      if (originalValue === '' || originalValue === null || originalValue === undefined) {
        return null;
      }
      return value;
    }),
  
  ownership: yup
    .string()
    .oneOf(OWNERSHIP_TYPES, `Ownership must be one of: ${OWNERSHIP_TYPES.join(', ')}`),
  
  phone: yup
    .string()
    .max(50, 'Phone number must be less than 50 characters')
    .matches(/^[\+]?[1-9][\d\s\-\(\)]{0,20}$/, 'Invalid phone number format')
    .nullable(),
  
  fax: yup
    .string()
    .max(50, 'Fax number must be less than 50 characters')
    .matches(/^[\+]?[1-9][\d\s\-\(\)]{0,20}$/, 'Invalid fax number format')
    .nullable(),
  
  website: yup
    .string()
    .url('Website must be a valid URL')
    .max(255, 'Website URL must be less than 255 characters')
    .nullable(),
  
  tickerSymbol: yup
    .string()
    .max(10, 'Ticker symbol must be less than 10 characters')
    .matches(/^[A-Z]{1,10}$/, 'Ticker symbol must contain only uppercase letters')
    .transform((value) => value?.toUpperCase())
    .nullable(),
  
  site: yup
    .string()
    .max(255, 'Site must be less than 255 characters')
    .nullable(),
  
  rating: yup
    .string()
    .oneOf(RATINGS, `Rating must be one of: ${RATINGS.join(', ')}`),
  
  description: yup
    .string()
    .max(5000, 'Description must be less than 5000 characters')
    .nullable(),
  
  billingAddress: addressSchema.nullable(),
  
  shippingAddress: addressSchema.nullable(),
  
  parentAccountId: yup
    .number()
    .integer('Parent account ID must be a whole number')
    .positive('Parent account ID must be positive')
    .nullable()
    .transform((value, originalValue) => {
      if (originalValue === '' || originalValue === null || originalValue === undefined) {
        return null;
      }
      return value;
    }),
  
  ownerId: yup
    .number()
    .integer('Owner ID must be a whole number')
    .positive('Owner ID must be positive')
    .transform((value, originalValue) => {
      if (originalValue === '' || originalValue === null || originalValue === undefined) {
        return undefined;
      }
      return value;
    }),
  
  status: yup
    .string()
    .oneOf(ACCOUNT_STATUSES, `Status must be one of: ${ACCOUNT_STATUSES.join(', ')}`),
  
  customFields: yup.object()
});

// Search filters validation schema
export const accountSearchValidationSchema = yup.object({
  search: yup
    .string()
    .max(255, 'Search term must be less than 255 characters'),
  
  industry: yup
    .array()
    .of(yup.string().oneOf(INDUSTRIES)),
  
  type: yup
    .array()
    .of(yup.string().oneOf(ACCOUNT_TYPES)),
  
  rating: yup
    .array()
    .of(yup.string().oneOf(RATINGS)),
  
  ownership: yup
    .array()
    .of(yup.string().oneOf(OWNERSHIP_TYPES)),
  
  status: yup
    .array()
    .of(yup.string().oneOf(ACCOUNT_STATUSES)),
  
  annualRevenueMin: yup
    .number()
    .min(0, 'Minimum annual revenue cannot be negative'),
  
  annualRevenueMax: yup
    .number()
    .min(0, 'Maximum annual revenue cannot be negative'),
  
  numberOfEmployeesMin: yup
    .number()
    .integer('Minimum number of employees must be a whole number')
    .min(0, 'Minimum number of employees cannot be negative'),
  
  numberOfEmployeesMax: yup
    .number()
    .integer('Maximum number of employees must be a whole number')
    .min(0, 'Maximum number of employees cannot be negative'),
  
  country: yup
    .array()
    .of(yup.string().max(100)),
  
  state: yup
    .array()
    .of(yup.string().max(100)),
  
  city: yup
    .array()
    .of(yup.string().max(100)),
  
  parentAccountId: yup
    .number()
    .integer('Parent account ID must be a whole number')
    .nullable(),
  
  ownerId: yup
    .number()
    .integer('Owner ID must be a whole number')
    .positive('Owner ID must be positive'),
  
  hasChildren: yup.boolean(),
  
  createdAfter: yup.date(),
  
  createdBefore: yup.date(),
  
  page: yup
    .number()
    .integer('Page must be a whole number')
    .min(1, 'Page must be at least 1')
    .default(1),
  
  limit: yup
    .number()
    .integer('Limit must be a whole number')
    .min(1, 'Limit must be at least 1')
    .max(100, 'Limit cannot exceed 100')
    .default(10)
});

// Type inference for form data
export type AccountCreateFormData = yup.InferType<typeof accountCreateValidationSchema>;
export type AccountUpdateFormData = yup.InferType<typeof accountUpdateValidationSchema>;
export type AccountSearchFormData = yup.InferType<typeof accountSearchValidationSchema>;