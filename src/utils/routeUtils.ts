/**
 * Utility functions for route handling and navigation
 */

export const routeConfig = {
  // Public routes
  login: '/login',
  register: '/register',
  forgotPassword: '/forgot-password',
  resetPassword: '/reset-password',
  verifyEmail: '/verify-email',
  verificationPending: '/verification-pending',
  
  // Protected routes
  dashboard: '/dashboard',
  
  // Vendor routes
  vendors: {
    list: '/vendors/list',
    create: '/vendors/create',
    view: (id: string) => `/vendors/${id}`,
    edit: (id: string) => `/vendors/${id}/edit`,
  },
  
  // Contract routes
  contracts: {
    list: '/contracts/list',
    create: '/contracts/create',
    view: (id: string) => `/contracts/${id}`,
    edit: (id: string) => `/contracts/${id}/edit`,
  },
  
  // Invoice routes
  invoices: {
    list: '/invoices/list',
    create: '/invoices/create',
    settings: '/invoices/settings',
    view: (id: string) => `/invoices/${id}`,
    edit: (id: string) => `/invoices/${id}/edit`,
  },
  
  // Performance routes
  performance: {
    scorecards: '/performance/scorecards',
    risks: '/performance/risks',
  },
  
  // Analytics routes
  analytics: {
    dashboard: '/analytics/dashboard',
    reports: '/analytics/reports',
    aiInsights: '/analytics/ai-insights',
  },
  
  // Workflow routes
  workflows: {
    list: '/workflows/list',
    create: '/workflows/create',
  },
  
  // Admin routes
  admin: {
    users: '/admin/users',
    settings: '/admin/settings',
    backup: '/admin/backup',
  },
  
  // Profile routes
  profile: {
    settings: '/profile/settings',
  },
};

/**
 * Check if a route requires admin permissions
 */
export const isAdminRoute = (path: string): boolean => {
  return path.startsWith('/admin/');
};

/**
 * Check if a route requires manager permissions
 */
export const isManagerRoute = (path: string): boolean => {
  const managerRoutes = [
    '/vendors/create',
    '/contracts/create',
    '/invoices/create',
    '/workflows/list',
    '/analytics/',
  ];
  
  return managerRoutes.some(route => path.startsWith(route)) || path.includes('/edit');
};

/**
 * Get the required role for a route
 */
export const getRequiredRole = (path: string): 'admin' | 'manager' | 'viewer' | null => {
  if (isAdminRoute(path)) return 'admin';
  if (isManagerRoute(path)) return 'manager';
  return 'viewer';
};

/**
 * Generate breadcrumb items for a given path
 */
export const generateBreadcrumbs = (path: string) => {
  const segments = path.split('/').filter(Boolean);
  const breadcrumbs = [];
  let currentPath = '';
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    
    // Skip numeric IDs
    if (/^\d+$/.test(segment)) {
      return;
    }
    
    breadcrumbs.push({
      label: segment.charAt(0).toUpperCase() + segment.slice(1),
      path: currentPath,
      isLast: index === segments.length - 1,
    });
  });
  
  return breadcrumbs;
};