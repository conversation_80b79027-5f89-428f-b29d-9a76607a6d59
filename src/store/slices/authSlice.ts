import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'manager' | 'viewer';
  status: 'active' | 'inactive' | 'pending';
  lastLogin: string;
  createdAt: string;
  updatedAt: string;
  profilePicture?: string;
  phone?: string;
  preferences: UserPreferences;
  isVerified: boolean;
  fullName?: string;
  organization?: string;
  consentGiven?: boolean;
  invitedBy?: string;
  invitationToken?: string;
  invitationExpiry?: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'en' | 'es' | 'fr';
  currency: 'USD' | 'EUR' | 'GBP';
  notifications: {
    email: boolean;
    browser: boolean;
    mobile: boolean;
  };
  timezone: string;
}

export interface Invitation {
  id: string;
  email: string;
  role: 'admin' | 'manager' | 'viewer';
  token: string;
  expiresAt: string;
  invitedBy: string;
  status: 'pending' | 'accepted' | 'expired';
  createdAt: string;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  organization: string;
  acceptTerms: boolean;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  token: string;
  password: string;
  confirmPassword: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  verificationStatus: 'pending' | 'verified' | 'expired' | null;
  resetTokenValid: boolean;
  users: User[]; // For admin user management
  invitations: Invitation[]; // For admin invitation tracking
  usersLoading: boolean;
  invitationsLoading: boolean;
}

const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  verificationStatus: null,
  resetTokenValid: false,
  users: [],
  invitations: [],
  usersLoading: false,
  invitationsLoading: false,
};

// Mock email service for development
const mockEmailService = {
  sendVerificationEmail: async (email: string, token: string) => {
    console.log(`📧 Verification email sent to ${email} with token: ${token}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  },
  sendPasswordResetEmail: async (email: string, token: string) => {
    console.log(`📧 Password reset email sent to ${email} with token: ${token}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  }
};

// Mock token storage for verification and reset tokens
const mockTokenStorage: Record<string, { email: string; expires: number; type: 'verification' | 'reset' }> = {};

// Mock user storage for registration
const mockUserStorage: Record<string, User> = {};

// Mock authentication - simulate API calls
export const registerAsync = createAsyncThunk(
  'auth/register',
  async (data: RegisterData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if user already exists
    if (mockUserStorage[data.email]) {
      throw new Error('Email already registered');
    }
    
    // Create new user
    const newUser: User = {
      id: Date.now().toString(),
      email: data.email,
      name: data.fullName,
      role: 'viewer',
      status: 'pending',
      lastLogin: 'Never',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isVerified: false,
      fullName: data.fullName,
      organization: data.organization,
      preferences: { 
        language: 'en', 
        currency: 'USD', 
        theme: 'light',
        notifications: { email: true, browser: true, mobile: false },
        timezone: 'UTC'
      },
      consentGiven: data.acceptTerms,
    };
    
    // Store user
    mockUserStorage[data.email] = newUser;
    
    // Generate verification token
    const verificationToken = `verify_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    mockTokenStorage[verificationToken] = {
      email: data.email,
      expires: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
      type: 'verification'
    };
    
    // Send verification email
    await mockEmailService.sendVerificationEmail(data.email, verificationToken);
    
    return { user: newUser, verificationToken };
  }
);

export const forgotPasswordAsync = createAsyncThunk(
  'auth/forgotPassword',
  async (data: ForgotPasswordData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if user exists (in mock storage or default users)
    const userExists = mockUserStorage[data.email] || 
      ['<EMAIL>', '<EMAIL>', '<EMAIL>'].includes(data.email);
    
    if (!userExists) {
      throw new Error('Email not found');
    }
    
    // Generate reset token
    const resetToken = `reset_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    mockTokenStorage[resetToken] = {
      email: data.email,
      expires: Date.now() + (60 * 60 * 1000), // 1 hour
      type: 'reset'
    };
    
    // Send reset email
    await mockEmailService.sendPasswordResetEmail(data.email, resetToken);
    
    return { email: data.email, resetToken };
  }
);

export const resetPasswordAsync = createAsyncThunk(
  'auth/resetPassword',
  async (data: ResetPasswordData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Validate token
    const tokenData = mockTokenStorage[data.token];
    if (!tokenData || tokenData.type !== 'reset' || tokenData.expires < Date.now()) {
      throw new Error('Invalid or expired reset token');
    }
    
    // Update password (in real app, hash the password)
    console.log(`🔐 Password updated for ${tokenData.email}`);
    
    // Remove used token
    delete mockTokenStorage[data.token];
    
    return { email: tokenData.email };
  }
);

export const verifyEmailAsync = createAsyncThunk(
  'auth/verifyEmail',
  async (token: string) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Validate token
    const tokenData = mockTokenStorage[token];
    if (!tokenData || tokenData.type !== 'verification' || tokenData.expires < Date.now()) {
      throw new Error('Invalid or expired verification token');
    }
    
    // Update user verification status
    if (mockUserStorage[tokenData.email]) {
      mockUserStorage[tokenData.email].isVerified = true;
      mockUserStorage[tokenData.email].updatedAt = new Date().toISOString();
    }
    
    // Remove used token
    delete mockTokenStorage[token];
    
    return { email: tokenData.email };
  }
);

export const resendVerificationAsync = createAsyncThunk(
  'auth/resendVerification',
  async (email: string) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if user exists
    const userExists = mockUserStorage[email];
    if (!userExists) {
      throw new Error('User not found');
    }
    
    // Generate new verification token
    const verificationToken = `verify_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    mockTokenStorage[verificationToken] = {
      email: email,
      expires: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
      type: 'verification'
    };
    
    // Send verification email
    await mockEmailService.sendVerificationEmail(email, verificationToken);
    
    return { email, verificationToken };
  }
);

export const loginAsync = createAsyncThunk(
  'auth/login',
  async ({ email, password }: { email: string; password: string }) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock user data based on email
    const mockUsers: Record<string, User> = {
      '<EMAIL>': {
        id: '1',
        email: '<EMAIL>',
        name: 'System Administrator',
        role: 'admin',
        status: 'active',
        lastLogin: new Date().toISOString(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-26T00:00:00Z',
        isVerified: true,
        fullName: 'System Administrator',
        organization: 'VendorMS Corp',
        preferences: { 
          language: 'en', 
          currency: 'USD', 
          theme: 'light',
          notifications: { email: true, browser: true, mobile: false },
          timezone: 'UTC'
        },
        consentGiven: true,
      },
      '<EMAIL>': {
        id: '2',
        email: '<EMAIL>',
        name: 'Procurement Manager',
        role: 'manager',
        status: 'active',
        lastLogin: new Date().toISOString(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-26T00:00:00Z',
        isVerified: true,
        fullName: 'Procurement Manager',
        organization: 'VendorMS Corp',
        preferences: { 
          language: 'en', 
          currency: 'USD', 
          theme: 'light',
          notifications: { email: true, browser: true, mobile: false },
          timezone: 'UTC'
        },
        consentGiven: true,
      },
      '<EMAIL>': {
        id: '3',
        email: '<EMAIL>',
        name: 'System Viewer',
        role: 'viewer',
        status: 'active',
        lastLogin: new Date().toISOString(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-26T00:00:00Z',
        isVerified: true,
        fullName: 'System Viewer',
        organization: 'VendorMS Corp',
        preferences: { 
          language: 'en', 
          currency: 'USD', 
          theme: 'light',
          notifications: { email: true, browser: true, mobile: false },
          timezone: 'UTC'
        },
        consentGiven: true,
      },
    };

    if (password === 'password' && mockUsers[email]) {
      const user = mockUsers[email];
      
      // Check if user exists in registered users (from registration flow)
      const registeredUser = mockUserStorage[email];
      if (registeredUser && !registeredUser.isVerified) {
        throw new Error('Please verify your email address before signing in');
      }
      
      const token = `mock_jwt_token_${Date.now()}`;
      localStorage.setItem('vms_token', token);
      return { user, token };
    } else {
      throw new Error('Invalid credentials');
    }
  }
);

export const logoutAsync = createAsyncThunk('auth/logout', async () => {
  localStorage.removeItem('vms_token');
  return null;
});

export const verifyTokenAsync = createAsyncThunk('auth/verifyToken', async () => {
  const token = localStorage.getItem('vms_token');
  if (!token) throw new Error('No token found');
  
  // Simulate token verification
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock user based on stored token (in real app, decode from JWT)
  const mockUser: User = {
    id: '1',
    email: '<EMAIL>',
    name: 'System Administrator',
    role: 'admin',
    status: 'active',
    lastLogin: new Date().toISOString(),
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-26T00:00:00Z',
    isVerified: true,
    preferences: { 
      language: 'en', 
      currency: 'USD', 
      theme: 'light',
      notifications: { email: true, browser: true, mobile: false },
      timezone: 'UTC'
    },
    consentGiven: true,
  };
  
  return { user: mockUser, token };
});

// Admin User Management Async Thunks
export const fetchUsersAsync = createAsyncThunk('auth/fetchUsers', async () => {
  const response = await fetch('http://localhost:3002/api/users', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('vms_token')}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch users');
  }

  const data = await response.json();
  
  // Transform backend response to frontend User format
  interface BackendUser {
    id: number;
    email: string;
    firstName?: string;
    lastName?: string;
    role: 'admin' | 'manager' | 'viewer';
    isVerified: boolean;
    lastLogin?: string;
    createdAt: string;
    updatedAt: string;
    preferences?: {
      theme?: string;
      language?: string;
      currency?: string;
      notifications?: {
        email?: boolean;
        browser?: boolean;
        mobile?: boolean;
      };
      timezone?: string;
    };
  }
  
  const users: User[] = data.data.users.map((user: BackendUser) => {
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';
    const fullName = firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName || user.email.split('@')[0];
    
    return {
      id: user.id.toString(),
      email: user.email,
      name: fullName,
      fullName: fullName,
      role: user.role,
      status: user.isVerified ? 'active' : 'inactive',
      lastLogin: user.lastLogin || 'Never',
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      isVerified: user.isVerified,
      preferences: user.preferences || {
        theme: 'light',
        language: 'en',
        currency: 'USD',
        notifications: {
          email: true,
          browser: true,
          mobile: false,
        },
        timezone: 'UTC',
      },
    };
  });
  
  return users;
});

export const inviteUserAsync = createAsyncThunk(
  'auth/inviteUser',
  async ({ email, role }: { email: string; role: 'admin' | 'manager' | 'viewer' }) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if user already exists
    const existingUser = Object.values(mockUserStorage).find(user => user.email === email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }
    
    // Create invitation
    const invitation: Invitation = {
      id: Date.now().toString(),
      email,
      role,
      token: `invite_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      invitedBy: '<EMAIL>', // In real app, get from current user
      status: 'pending',
      createdAt: new Date().toISOString(),
    };
    
    // Send invitation email
    await mockEmailService.sendVerificationEmail(email, invitation.token);
    
    return invitation;
  }
);

export const updateUserRoleAsync = createAsyncThunk(
  'auth/updateUserRole',
  async ({ userId, newRole }: { userId: string; newRole: 'admin' | 'manager' | 'viewer' }) => {
    const response = await fetch(`http://localhost:3002/api/users/${userId}/role`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('vms_token')}`,
      },
      body: JSON.stringify({ role: newRole }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update user role');
    }

    return { userId, newRole };
  }
);

export const updateUserStatusAsync = createAsyncThunk(
  'auth/updateUserStatus',
  async ({ userId, newStatus }: { userId: string; newStatus: 'active' | 'inactive' }) => {
    const response = await fetch(`http://localhost:3002/api/users/${userId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('vms_token')}`,
      },
      body: JSON.stringify({ is_verified: newStatus === 'active' }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update user status');
    }

    return { userId, newStatus };
  }
);

export const updateProfileAsync = createAsyncThunk(
  'auth/updateProfile',
  async (profileData: Partial<User>) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock update - in real app, make API call
    return profileData;
  }
);

// Create user directly (admin only)
export const createUserAsync = createAsyncThunk(
  'auth/createUser',
  async (userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    role: 'admin' | 'manager' | 'viewer';
  }) => {
    const response = await fetch('http://localhost:3002/api/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('vms_token')}`,
      },
      body: JSON.stringify({
        email: userData.email,
        password: userData.password,
        role: userData.role,
        firstName: userData.firstName,
        lastName: userData.lastName,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create user');
    }

    const data = await response.json();
    
    // Transform backend response to frontend User format
    const firstName = data.data.user.firstName || userData.firstName;
    const lastName = data.data.user.lastName || userData.lastName;
    const fullName = firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName || data.data.user.email.split('@')[0];
    
    const newUser: User = {
      id: data.data.user.id.toString(),
      email: data.data.user.email,
      name: fullName,
      fullName: fullName,
      role: data.data.user.role,
      status: 'active',
      lastLogin: 'Never',
      createdAt: data.data.user.createdAt,
      updatedAt: data.data.user.updatedAt,
      isVerified: data.data.user.isVerified,
      preferences: {
        theme: 'light',
        language: 'en',
        currency: 'USD',
        notifications: {
          email: true,
          browser: true,
          mobile: false,
        },
        timezone: 'UTC',
      },
    };
    
    return newUser;
  }
);

// Set password for any user (admin only)
export const setUserPasswordAsync = createAsyncThunk(
  'auth/setUserPassword',
  async ({ userId, newPassword }: { userId: string; newPassword: string }) => {
    const response = await fetch(`http://localhost:3002/api/users/${userId}/password`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('vms_token')}`,
      },
      body: JSON.stringify({ password: newPassword }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to set user password');
    }
    
    return { userId, success: true };
  }
);

export const changePasswordAsync = createAsyncThunk(
  'auth/changePassword',
  async ({ currentPassword, newPassword }: { currentPassword: string; newPassword: string }) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock password validation
    if (currentPassword !== 'password') {
      throw new Error('Current password is incorrect');
    }
    
    // Mock password change
    return { success: true };
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateUserPreferences: (state, action: PayloadAction<Partial<User['preferences']>>) => {
      if (state.user) {
        state.user.preferences = { ...state.user.preferences, ...action.payload };
      }
    },
    removeInvitation: (state, action: PayloadAction<string>) => {
      state.invitations = state.invitations.filter(inv => inv.id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
      })
      .addCase(loginAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Login failed';
        state.isAuthenticated = false;
      })
      // Logout
      .addCase(logoutAsync.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.error = null;
      })
      // Verify Token
      .addCase(verifyTokenAsync.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyTokenAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
      })
      .addCase(verifyTokenAsync.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        localStorage.removeItem('vms_token');
      })
      // Register
      .addCase(registerAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.verificationStatus = 'pending';
        state.error = null;
      })
      .addCase(registerAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Registration failed';
      })
      // Forgot Password
      .addCase(forgotPasswordAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(forgotPasswordAsync.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(forgotPasswordAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to send reset email';
      })
      // Reset Password
      .addCase(resetPasswordAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
        state.resetTokenValid = true;
      })
      .addCase(resetPasswordAsync.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
        state.resetTokenValid = false;
      })
      .addCase(resetPasswordAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Password reset failed';
        state.resetTokenValid = false;
      })
      // Verify Email
      .addCase(verifyEmailAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyEmailAsync.fulfilled, (state) => {
        state.isLoading = false;
        state.verificationStatus = 'verified';
        state.error = null;
      })
      .addCase(verifyEmailAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.verificationStatus = 'expired';
        state.error = action.error.message || 'Email verification failed';
      })
      // Resend Verification
      .addCase(resendVerificationAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resendVerificationAsync.fulfilled, (state) => {
        state.isLoading = false;
        state.verificationStatus = 'pending';
        state.error = null;
      })
      .addCase(resendVerificationAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to resend verification email';
      })
      // Fetch Users
      .addCase(fetchUsersAsync.pending, (state) => {
        state.usersLoading = true;
        state.error = null;
      })
      .addCase(fetchUsersAsync.fulfilled, (state, action) => {
        state.usersLoading = false;
        state.users = action.payload;
      })
      .addCase(fetchUsersAsync.rejected, (state, action) => {
        state.usersLoading = false;
        state.error = action.error.message || 'Failed to fetch users';
      })
      // Invite User
      .addCase(inviteUserAsync.pending, (state) => {
        state.invitationsLoading = true;
        state.error = null;
      })
      .addCase(inviteUserAsync.fulfilled, (state, action) => {
        state.invitationsLoading = false;
        state.invitations.push(action.payload);
      })
      .addCase(inviteUserAsync.rejected, (state, action) => {
        state.invitationsLoading = false;
        state.error = action.error.message || 'Failed to send invitation';
      })
      // Update User Role
      .addCase(updateUserRoleAsync.fulfilled, (state, action) => {
        const { userId, newRole } = action.payload;
        const userIndex = state.users.findIndex(user => user.id === userId);
        if (userIndex !== -1) {
          state.users[userIndex].role = newRole;
          state.users[userIndex].updatedAt = new Date().toISOString();
        }
      })
      // Update User Status
      .addCase(updateUserStatusAsync.fulfilled, (state, action) => {
        const { userId, newStatus } = action.payload;
        const userIndex = state.users.findIndex(user => user.id === userId);
        if (userIndex !== -1) {
          state.users[userIndex].status = newStatus;
          state.users[userIndex].updatedAt = new Date().toISOString();
        }
      })
      // Update Profile
      .addCase(updateProfileAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProfileAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user) {
          state.user = { ...state.user, ...action.payload, updatedAt: new Date().toISOString() };
        }
      })
      .addCase(updateProfileAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update profile';
      })
      // Change Password
      .addCase(changePasswordAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(changePasswordAsync.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(changePasswordAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to change password';
      })
      // Create User
      .addCase(createUserAsync.pending, (state) => {
        state.usersLoading = true;
        state.error = null;
      })
      .addCase(createUserAsync.fulfilled, (state, action) => {
        state.usersLoading = false;
        state.users.push(action.payload);
      })
      .addCase(createUserAsync.rejected, (state, action) => {
        state.usersLoading = false;
        state.error = action.error.message || 'Failed to create user';
      })
      // Set User Password
      .addCase(setUserPasswordAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(setUserPasswordAsync.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(setUserPasswordAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to set user password';
      });
  },
});

export const { clearError, updateUserPreferences, removeInvitation } = authSlice.actions;
export default authSlice.reducer;