import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
export interface DashboardData {
  spendAnalysis: {
    totalSpend: number;
    spendByVendor: Array<{ vendor: string; amount: number; }>;
    spendByCategory: Array<{ category: string; amount: number; }>;
    spendTrends: Array<{ date: string; amount: number; }>;
  };
  vendorPerformance: {
    averageScore: number;
    performanceTrends: Array<{ date: string; score: number; }>;
    topPerformers: Array<{ vendor: string; score: number; }>;
    riskVendors: Array<{ vendor: string; riskLevel: 'high' | 'medium' | 'low'; }>;
  };
  contractMetrics: {
    activeContracts: number;
    expiringContracts: number;
    renewalRate: number;
    complianceScore: number;
  };
  invoiceMetrics: {
    pendingInvoices: number;
    averageProcessingTime: number;
    paymentTrends: Array<{ date: string; amount: number; }>;
  };
}

export interface ReportConfig {
  id: string;
  name: string;
  description: string;
  dataSource: 'vendors' | 'contracts' | 'invoices' | 'performance';
  filters: {
    dateRange: { start: string; end: string; };
    vendors?: string[];
    categories?: string[];
    status?: string[];
  };
  columns: string[];
  chartType?: 'bar' | 'line' | 'pie' | 'table';
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    recipients: string[];
    enabled: boolean;
  };
}

export interface GeneratedReport {
  id: string;
  configId: string;
  generatedAt: string;
  format: 'pdf' | 'csv';
  downloadUrl: string;
  size: number;
}

export interface VendorPrediction {
  vendorId: string;
  vendorName: string;
  riskScore: number;
  confidenceLevel: number;
  predictions: {
    performanceDecline: { probability: number; timeframe: string; };
    contractRenewal: { probability: number; recommendation: string; };
    costOptimization: { potentialSavings: number; suggestions: string[]; };
  };
  factors: Array<{ factor: string; impact: number; description: string; }>;
}

export interface AIModel {
  type: 'risk_prediction' | 'performance_forecast' | 'cost_optimization';
  version: string;
  accuracy: number;
  lastTrained: string;
  features: string[];
}

export interface AnalyticsState {
  dashboard: {
    data: DashboardData | null;
    loading: boolean;
    error: string | null;
    lastUpdated: string | null;
    filters: {
      dateRange: { start: string; end: string; };
      vendors: string[];
      categories: string[];
    };
  };
  reports: {
    configs: ReportConfig[];
    generated: GeneratedReport[];
    building: ReportConfig | null;
    loading: boolean;
    error: string | null;
  };
  aiInsights: {
    predictions: VendorPrediction[];
    models: AIModel[];
    selectedVendors: string[];
    loading: boolean;
    error: string | null;
  };
  realtime: {
    connected: boolean;
    lastUpdate: string | null;
  };
}

// Mock data
const mockDashboardData: DashboardData = {
  spendAnalysis: {
    totalSpend: 2450000,
    spendByVendor: [
      { vendor: 'TechCorp Solutions', amount: 850000 },
      { vendor: 'Global Services Ltd', amount: 620000 },
      { vendor: 'Innovation Partners', amount: 480000 },
      { vendor: 'Digital Solutions Inc', amount: 320000 },
      { vendor: 'Enterprise Systems', amount: 180000 },
    ],
    spendByCategory: [
      { category: 'IT Services', amount: 1200000 },
      { category: 'Consulting', amount: 650000 },
      { category: 'Software Licenses', amount: 400000 },
      { category: 'Hardware', amount: 200000 },
    ],
    spendTrends: [
      { date: '2024-01', amount: 180000 },
      { date: '2024-02', amount: 220000 },
      { date: '2024-03', amount: 195000 },
      { date: '2024-04', amount: 240000 },
      { date: '2024-05', amount: 210000 },
      { date: '2024-06', amount: 285000 },
    ],
  },
  vendorPerformance: {
    averageScore: 87.5,
    performanceTrends: [
      { date: '2024-01', score: 85 },
      { date: '2024-02', score: 86 },
      { date: '2024-03', score: 88 },
      { date: '2024-04', score: 87 },
      { date: '2024-05', score: 89 },
      { date: '2024-06', score: 90 },
    ],
    topPerformers: [
      { vendor: 'TechCorp Solutions', score: 95 },
      { vendor: 'Innovation Partners', score: 92 },
      { vendor: 'Digital Solutions Inc', score: 89 },
    ],
    riskVendors: [
      { vendor: 'Legacy Systems Co', riskLevel: 'high' },
      { vendor: 'Budget Services Ltd', riskLevel: 'medium' },
    ],
  },
  contractMetrics: {
    activeContracts: 45,
    expiringContracts: 8,
    renewalRate: 92,
    complianceScore: 94,
  },
  invoiceMetrics: {
    pendingInvoices: 12,
    averageProcessingTime: 3.2,
    paymentTrends: [
      { date: '2024-01', amount: 165000 },
      { date: '2024-02', amount: 198000 },
      { date: '2024-03', amount: 185000 },
      { date: '2024-04', amount: 225000 },
      { date: '2024-05', amount: 205000 },
      { date: '2024-06', amount: 270000 },
    ],
  },
};

const mockReportConfigs: ReportConfig[] = [
  {
    id: '1',
    name: 'Monthly Vendor Spend Report',
    description: 'Comprehensive monthly spending analysis by vendor',
    dataSource: 'invoices',
    filters: {
      dateRange: { start: '2024-01-01', end: '2024-12-31' },
      vendors: [],
      categories: ['IT Services', 'Consulting'],
    },
    columns: ['vendor_name', 'total_amount', 'invoice_count', 'avg_amount'],
    chartType: 'bar',
    schedule: {
      frequency: 'monthly',
      recipients: ['<EMAIL>'],
      enabled: true,
    },
  },
  {
    id: '2',
    name: 'Vendor Performance Dashboard',
    description: 'Weekly performance metrics and trends',
    dataSource: 'performance',
    filters: {
      dateRange: { start: '2024-01-01', end: '2024-12-31' },
    },
    columns: ['vendor_name', 'performance_score', 'risk_level'],
    chartType: 'line',
  },
];

const mockVendorPredictions: VendorPrediction[] = [
  {
    vendorId: '1',
    vendorName: 'TechCorp Solutions',
    riskScore: 15,
    confidenceLevel: 87,
    predictions: {
      performanceDecline: { probability: 0.15, timeframe: '6 months' },
      contractRenewal: { probability: 0.92, recommendation: 'Highly recommended for renewal' },
      costOptimization: { potentialSavings: 45000, suggestions: ['Negotiate volume discounts', 'Consider annual contract'] },
    },
    factors: [
      { factor: 'Payment History', impact: 0.25, description: 'Consistently pays on time' },
      { factor: 'Performance Score', impact: 0.30, description: 'Above average performance' },
      { factor: 'Contract Compliance', impact: 0.20, description: 'Full compliance record' },
    ],
  },
  {
    vendorId: '6',
    vendorName: 'Legacy Systems Co',
    riskScore: 78,
    confidenceLevel: 82,
    predictions: {
      performanceDecline: { probability: 0.78, timeframe: '3 months' },
      contractRenewal: { probability: 0.35, recommendation: 'Consider alternative vendors' },
      costOptimization: { potentialSavings: 120000, suggestions: ['Explore competitive alternatives', 'Renegotiate terms'] },
    },
    factors: [
      { factor: 'Recent Performance', impact: -0.45, description: 'Declining performance metrics' },
      { factor: 'Customer Complaints', impact: -0.25, description: 'Increased complaint volume' },
      { factor: 'Technical Debt', impact: -0.30, description: 'Outdated technology stack' },
    ],
  },
];

const mockAIModels: AIModel[] = [
  {
    type: 'risk_prediction',
    version: '2.1.0',
    accuracy: 0.87,
    lastTrained: '2024-06-15T00:00:00.000Z',
    features: ['performance_score', 'payment_history', 'contract_compliance', 'complaint_count'],
  },
  {
    type: 'performance_forecast',
    version: '1.8.3',
    accuracy: 0.82,
    lastTrained: '2024-06-10T00:00:00.000Z',
    features: ['historical_performance', 'project_complexity', 'team_size', 'technology_stack'],
  },
];

// Async thunks
export const fetchDashboardData = createAsyncThunk(
  'analytics/fetchDashboardData',
  async (filters: { dateRange?: { start: string; end: string }; vendors?: string[]; categories?: string[] }) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    return mockDashboardData;
  }
);

export const fetchReportConfigs = createAsyncThunk(
  'analytics/fetchReportConfigs',
  async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockReportConfigs;
  }
);

export const generateReport = createAsyncThunk(
  'analytics/generateReport',
  async (params: { configId: string; format: 'pdf' | 'csv' }) => {
    await new Promise(resolve => setTimeout(resolve, 2000));
    const report: GeneratedReport = {
      id: `report_${Date.now()}`,
      configId: params.configId,
      generatedAt: new Date().toISOString(),
      format: params.format,
      downloadUrl: `/downloads/report_${Date.now()}.${params.format}`,
      size: Math.floor(Math.random() * 1000000) + 100000,
    };
    return report;
  }
);

export const fetchAIPredictions = createAsyncThunk(
  'analytics/fetchAIPredictions',
  async (vendorIds: string[]) => {
    await new Promise(resolve => setTimeout(resolve, 1500));
    return mockVendorPredictions.filter(p => 
      vendorIds.length === 0 || vendorIds.includes(p.vendorId)
    );
  }
);

export const fetchAIModels = createAsyncThunk(
  'analytics/fetchAIModels',
  async () => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockAIModels;
  }
);

// Initial state
const initialState: AnalyticsState = {
  dashboard: {
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
    filters: {
      dateRange: {
        start: new Date(new Date().setMonth(new Date().getMonth() - 6)).toISOString(),
        end: new Date().toISOString(),
      },
      vendors: [],
      categories: [],
    },
  },
  reports: {
    configs: [],
    generated: [],
    building: null,
    loading: false,
    error: null,
  },
  aiInsights: {
    predictions: [],
    models: [],
    selectedVendors: [],
    loading: false,
    error: null,
  },
  realtime: {
    connected: false,
    lastUpdate: null,
  },
};

// Slice
const analyticsSlice = createSlice({
  name: 'analytics',
  initialState,
  reducers: {
    setDashboardFilters: (state, action: PayloadAction<Partial<AnalyticsState['dashboard']['filters']>>) => {
      state.dashboard.filters = { ...state.dashboard.filters, ...action.payload };
    },
    setSelectedVendors: (state, action: PayloadAction<string[]>) => {
      state.aiInsights.selectedVendors = action.payload;
    },
    setRealtimeConnected: (state, action: PayloadAction<boolean>) => {
      state.realtime.connected = action.payload;
      if (action.payload) {
        state.realtime.lastUpdate = new Date().toISOString();
      }
    },
    updateDashboardData: (state, action: PayloadAction<Partial<DashboardData>>) => {
      if (state.dashboard.data) {
        state.dashboard.data = { ...state.dashboard.data, ...action.payload };
        state.dashboard.lastUpdated = new Date().toISOString();
      }
    },
    clearError: (state, action: PayloadAction<'dashboard' | 'reports' | 'aiInsights'>) => {
      state[action.payload].error = null;
    },
  },
  extraReducers: (builder) => {
    // Dashboard data
    builder
      .addCase(fetchDashboardData.pending, (state) => {
        state.dashboard.loading = true;
        state.dashboard.error = null;
      })
      .addCase(fetchDashboardData.fulfilled, (state, action) => {
        state.dashboard.loading = false;
        state.dashboard.data = action.payload;
        state.dashboard.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchDashboardData.rejected, (state, action) => {
        state.dashboard.loading = false;
        state.dashboard.error = action.error.message || 'Failed to fetch dashboard data';
      });

    // Report configs
    builder
      .addCase(fetchReportConfigs.pending, (state) => {
        state.reports.loading = true;
        state.reports.error = null;
      })
      .addCase(fetchReportConfigs.fulfilled, (state, action) => {
        state.reports.loading = false;
        state.reports.configs = action.payload;
      })
      .addCase(fetchReportConfigs.rejected, (state, action) => {
        state.reports.loading = false;
        state.reports.error = action.error.message || 'Failed to fetch report configs';
      });

    // Generate report
    builder
      .addCase(generateReport.pending, (state) => {
        state.reports.loading = true;
        state.reports.error = null;
      })
      .addCase(generateReport.fulfilled, (state, action) => {
        state.reports.loading = false;
        state.reports.generated.unshift(action.payload);
      })
      .addCase(generateReport.rejected, (state, action) => {
        state.reports.loading = false;
        state.reports.error = action.error.message || 'Failed to generate report';
      });

    // AI predictions
    builder
      .addCase(fetchAIPredictions.pending, (state) => {
        state.aiInsights.loading = true;
        state.aiInsights.error = null;
      })
      .addCase(fetchAIPredictions.fulfilled, (state, action) => {
        state.aiInsights.loading = false;
        state.aiInsights.predictions = action.payload;
      })
      .addCase(fetchAIPredictions.rejected, (state, action) => {
        state.aiInsights.loading = false;
        state.aiInsights.error = action.error.message || 'Failed to fetch AI predictions';
      });

    // AI models
    builder
      .addCase(fetchAIModels.pending, (state) => {
        state.aiInsights.loading = true;
      })
      .addCase(fetchAIModels.fulfilled, (state, action) => {
        state.aiInsights.loading = false;
        state.aiInsights.models = action.payload;
      })
      .addCase(fetchAIModels.rejected, (state, action) => {
        state.aiInsights.loading = false;
        state.aiInsights.error = action.error.message || 'Failed to fetch AI models';
      });
  },
});

export const {
  setDashboardFilters,
  setSelectedVendors,
  setRealtimeConnected,
  updateDashboardData,
  clearError,
} = analyticsSlice.actions;

export default analyticsSlice.reducer;