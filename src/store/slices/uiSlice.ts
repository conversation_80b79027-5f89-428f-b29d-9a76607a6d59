import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UIState {
  theme: 'light' | 'dark';
  language: 'en' | 'es' | 'fr';
  currency: 'USD' | 'EUR' | 'GBP';
  sidebarCollapsed: boolean;
  isLoading: boolean;
  globalError: string | null;
}

const initialState: UIState = {
  theme: (localStorage.getItem('vms_theme') as 'light' | 'dark') || 'light',
  language: (localStorage.getItem('vms_language') as 'en' | 'es' | 'fr') || 'en',
  currency: (localStorage.getItem('vms_currency') as 'USD' | 'EUR' | 'GBP') || 'USD',
  sidebarCollapsed: JSON.parse(localStorage.getItem('vms_sidebar_collapsed') || 'false'),
  isLoading: false,
  globalError: null,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
      localStorage.setItem('vms_theme', action.payload);
      
      // Apply theme to document
      if (action.payload === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    },
    setLanguage: (state, action: PayloadAction<'en' | 'es' | 'fr'>) => {
      state.language = action.payload;
      localStorage.setItem('vms_language', action.payload);
    },
    setCurrency: (state, action: PayloadAction<'USD' | 'EUR' | 'GBP'>) => {
      state.currency = action.payload;
      localStorage.setItem('vms_currency', action.payload);
    },
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
      localStorage.setItem('vms_sidebar_collapsed', JSON.stringify(state.sidebarCollapsed));
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
      localStorage.setItem('vms_sidebar_collapsed', JSON.stringify(action.payload));
    },
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setGlobalError: (state, action: PayloadAction<string | null>) => {
      state.globalError = action.payload;
    },
    clearGlobalError: (state) => {
      state.globalError = null;
    },
  },
});

export const {
  setTheme,
  setLanguage,
  setCurrency,
  toggleSidebar,
  setSidebarCollapsed,
  setGlobalLoading,
  setGlobalError,
  clearGlobalError,
} = uiSlice.actions;

export default uiSlice.reducer;