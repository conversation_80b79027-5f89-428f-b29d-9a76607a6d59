import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import VendorApiService, { VendorFilters, VendorCreateData, AuditEntry } from '../../services/vendorApi';

export interface Vendor {
  id: number;
  name: string;
  contact_email: string;
  contact_phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  category: string;
  certifications: string[];
  performance_score: number;
  status: 'active' | 'inactive' | 'blacklisted';
  custom_fields: Record<string, unknown>;
  created_at: string;
  updated_at: string;
  deactivated_at?: string;
  blacklisted_reason?: string;
}

interface VendorsState {
  vendors: Vendor[];
  currentVendor: Vendor | null;
  auditHistory: AuditEntry[];
  categories: string[];
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  filters: VendorFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

const initialState: VendorsState = {
  vendors: [],
  currentVendor: null,
  auditHistory: [],
  categories: [],
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  filters: {
    search: '',
    category: '',
    status: undefined,
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
};

// Async thunks for vendor operations
export const fetchVendorsAsync = createAsyncThunk(
  'vendors/fetchVendors',
  async ({ page = 1, limit = 10, filters = {} }: { page?: number; limit?: number; filters?: VendorFilters } = {}) => {
    const response = await VendorApiService.getVendors(page, limit, filters);
    return response;
  }
);

export const fetchVendorByIdAsync = createAsyncThunk(
  'vendors/fetchVendorById',
  async (id: number) => {
    const response = await VendorApiService.getVendorById(id);
    return response.data;
  }
);

export const createVendorAsync = createAsyncThunk(
  'vendors/createVendor',
  async (vendorData: VendorCreateData) => {
    const response = await VendorApiService.createVendor(vendorData);
    return response.data;
  }
);

export const updateVendorAsync = createAsyncThunk(
  'vendors/updateVendor',
  async ({ id, data }: { id: number; data: Partial<VendorCreateData> }) => {
    const response = await VendorApiService.updateVendor(id, data);
    return response.data;
  }
);

export const deleteVendorAsync = createAsyncThunk(
  'vendors/deleteVendor',
  async (id: number) => {
    await VendorApiService.deleteVendor(id);
    return id;
  }
);

export const fetchVendorAuditAsync = createAsyncThunk(
  'vendors/fetchVendorAudit',
  async (id: number) => {
    const response = await VendorApiService.getVendorAudit(id);
    return response.data;
  }
);

export const fetchVendorCategoriesAsync = createAsyncThunk(
  'vendors/fetchVendorCategories',
  async () => {
    const response = await VendorApiService.getVendorCategories();
    return response.data;
  }
);

const vendorsSlice = createSlice({
  name: 'vendors',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<typeof initialState.filters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearCurrentVendor: (state) => {
      state.currentVendor = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch vendors
      .addCase(fetchVendorsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchVendorsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.vendors = action.payload.data;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchVendorsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch vendors';
      })
      // Fetch vendor by ID
      .addCase(fetchVendorByIdAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchVendorByIdAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentVendor = action.payload;
      })
      .addCase(fetchVendorByIdAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch vendor';
      })
      // Create vendor
      .addCase(createVendorAsync.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createVendorAsync.fulfilled, (state, action) => {
        state.isCreating = false;
        state.vendors.unshift(action.payload);
      })
      .addCase(createVendorAsync.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.error.message || 'Failed to create vendor';
      })
      // Update vendor
      .addCase(updateVendorAsync.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateVendorAsync.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.vendors.findIndex(v => v.id === action.payload.id);
        if (index !== -1) {
          state.vendors[index] = action.payload;
        }
        if (state.currentVendor?.id === action.payload.id) {
          state.currentVendor = action.payload;
        }
      })
      .addCase(updateVendorAsync.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.error.message || 'Failed to update vendor';
      })
      // Delete vendor
      .addCase(deleteVendorAsync.pending, (state) => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteVendorAsync.fulfilled, (state, action) => {
        state.isDeleting = false;
        const index = state.vendors.findIndex(v => v.id === action.payload);
        if (index !== -1) {
          state.vendors[index] = { ...state.vendors[index], status: 'inactive', deactivated_at: new Date().toISOString() };
        }
      })
      .addCase(deleteVendorAsync.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.error.message || 'Failed to delete vendor';
      })
      // Fetch vendor audit
      .addCase(fetchVendorAuditAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchVendorAuditAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.auditHistory = action.payload;
      })
      .addCase(fetchVendorAuditAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch audit history';
      })
      // Fetch vendor categories
      .addCase(fetchVendorCategoriesAsync.fulfilled, (state, action) => {
        state.categories = action.payload;
      });
  },
});

export const { setFilters, clearCurrentVendor, clearError } = vendorsSlice.actions;
export default vendorsSlice.reducer;