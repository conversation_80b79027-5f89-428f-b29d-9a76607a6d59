import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types and Interfaces
export interface WorkflowTrigger {
  type: 'vendor_onboarded' | 'contract_expiring' | 'invoice_approved' | 'performance_score_changed' | 'manual';
  config: {
    conditions?: TriggerCondition[];
    schedule?: string; // Cron expression for scheduled triggers
  };
}

export interface TriggerCondition {
  field: string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
  value: any;
}

export interface ApprovalStepConfig {
  approvers: {
    type: 'role' | 'user';
    value: string;
  }[];
  timeout_hours: number;
  escalation: {
    enabled: boolean;
    escalate_to: string;
    after_hours: number;
  };
  approval_type: 'any' | 'all' | 'majority';
}

export interface NotificationStepConfig {
  recipients: {
    type: 'role' | 'user' | 'email';
    value: string;
  }[];
  template: {
    subject: string;
    body: string;
    variables: string[];
  };
  delivery_method: 'email' | 'sms' | 'in_app';
  delay_minutes?: number;
}

export interface IntegrationStepConfig {
  integration_type: 'quickbooks' | 'salesforce' | 'custom_api';
  action: string;
  data_mapping: {
    source_field: string;
    target_field: string;
  }[];
  error_handling: 'continue' | 'stop' | 'retry';
  retry_count?: number;
}

export interface ConditionStepConfig {
  conditions: {
    field: string;
    operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
    value: any;
  }[];
  logic: 'and' | 'or';
  branches: {
    true_path: string[];
    false_path: string[];
  };
}

export type StepConfig = ApprovalStepConfig | NotificationStepConfig | IntegrationStepConfig | ConditionStepConfig;

export interface WorkflowStep {
  id: string;
  type: 'approval' | 'notification' | 'integration' | 'condition' | 'delay';
  name: string;
  config: StepConfig;
  position: { x: number; y: number };
  connections: string[]; // Connected step IDs
}

export interface WorkflowExecution {
  id: string;
  workflow_id: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  started_at: string;
  completed_at?: string;
  trigger_data: any;
  step_results: {
    step_id: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    started_at: string;
    completed_at?: string;
    result_data?: any;
    error_message?: string;
  }[];
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'draft';
  trigger: WorkflowTrigger;
  steps: WorkflowStep[];
  created_at: string;
  updated_at: string;
  last_run?: string;
  success_rate: number;
  execution_count: number;
  created_by: string;
}

export interface WorkflowsState {
  workflows: Workflow[];
  currentWorkflow: Workflow | null;
  isLoading: boolean;
  error: string | null;
  filters: {
    search: string;
    status: 'all' | 'active' | 'inactive' | 'draft';
    trigger: string;
  };
  executionLogs: WorkflowExecution[];
  isExecutionLoading: boolean;
  selectedStep: string | null;
  canvasPosition: { x: number; y: number };
  zoom: number;
}

// Async Thunks
export const fetchWorkflows = createAsyncThunk(
  'workflows/fetchWorkflows',
  async (params?: { search?: string; status?: string; trigger?: string }) => {
    // Mock API call - replace with actual API
    const response = await new Promise<Workflow[]>((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '1',
            name: 'Vendor Onboarding Approval',
            description: 'Automated approval process for new vendor onboarding',
            status: 'active',
            trigger: {
              type: 'vendor_onboarded',
              config: {}
            },
            steps: [],
            created_at: '2024-01-15T10:00:00Z',
            updated_at: '2024-01-15T10:00:00Z',
            success_rate: 95.5,
            execution_count: 42,
            created_by: '<EMAIL>'
          },
          {
            id: '2',
            name: 'Contract Expiry Notification',
            description: 'Notify stakeholders when contracts are about to expire',
            status: 'active',
            trigger: {
              type: 'contract_expiring',
              config: {}
            },
            steps: [],
            created_at: '2024-01-10T14:30:00Z',
            updated_at: '2024-01-10T14:30:00Z',
            success_rate: 100,
            execution_count: 15,
            created_by: '<EMAIL>'
          }
        ]);
      }, 1000);
    });
    return response;
  }
);

export const createWorkflow = createAsyncThunk(
  'workflows/createWorkflow',
  async (workflowData: Omit<Workflow, 'id' | 'created_at' | 'updated_at' | 'success_rate' | 'execution_count'>) => {
    // Mock API call - replace with actual API
    const response = await new Promise<Workflow>((resolve) => {
      setTimeout(() => {
        resolve({
          ...workflowData,
          id: Date.now().toString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          success_rate: 0,
          execution_count: 0
        });
      }, 500);
    });
    return response;
  }
);

export const updateWorkflow = createAsyncThunk(
  'workflows/updateWorkflow',
  async ({ id, updates }: { id: string; updates: Partial<Workflow> }) => {
    // Mock API call - replace with actual API
    const response = await new Promise<Workflow>((resolve) => {
      setTimeout(() => {
        resolve({
          id,
          ...updates,
          updated_at: new Date().toISOString()
        } as Workflow);
      }, 500);
    });
    return response;
  }
);

export const deleteWorkflow = createAsyncThunk(
  'workflows/deleteWorkflow',
  async (id: string) => {
    // Mock API call - replace with actual API
    await new Promise((resolve) => setTimeout(resolve, 500));
    return id;
  }
);

export const toggleWorkflowStatus = createAsyncThunk(
  'workflows/toggleWorkflowStatus',
  async ({ id, status }: { id: string; status: 'active' | 'inactive' }) => {
    // Mock API call - replace with actual API
    const response = await new Promise<{ id: string; status: 'active' | 'inactive' }>((resolve) => {
      setTimeout(() => {
        resolve({ id, status });
      }, 300);
    });
    return response;
  }
);

export const fetchWorkflowExecutions = createAsyncThunk(
  'workflows/fetchWorkflowExecutions',
  async (workflowId: string) => {
    // Mock API call - replace with actual API
    const response = await new Promise<WorkflowExecution[]>((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '1',
            workflow_id: workflowId,
            status: 'completed',
            started_at: '2024-01-20T09:00:00Z',
            completed_at: '2024-01-20T09:15:00Z',
            trigger_data: { vendor_id: 'vendor_123' },
            step_results: []
          }
        ]);
      }, 800);
    });
    return response;
  }
);

// Initial State
const initialState: WorkflowsState = {
  workflows: [],
  currentWorkflow: null,
  isLoading: false,
  error: null,
  filters: {
    search: '',
    status: 'all',
    trigger: ''
  },
  executionLogs: [],
  isExecutionLoading: false,
  selectedStep: null,
  canvasPosition: { x: 0, y: 0 },
  zoom: 1
};

// Slice
const workflowsSlice = createSlice({
  name: 'workflows',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<WorkflowsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setCurrentWorkflow: (state, action: PayloadAction<Workflow | null>) => {
      state.currentWorkflow = action.payload;
    },
    setSelectedStep: (state, action: PayloadAction<string | null>) => {
      state.selectedStep = action.payload;
    },
    updateCanvasPosition: (state, action: PayloadAction<{ x: number; y: number }>) => {
      state.canvasPosition = action.payload;
    },
    setZoom: (state, action: PayloadAction<number>) => {
      state.zoom = action.payload;
    },
    addWorkflowStep: (state, action: PayloadAction<WorkflowStep>) => {
      if (state.currentWorkflow) {
        state.currentWorkflow.steps.push(action.payload);
      }
    },
    updateWorkflowStep: (state, action: PayloadAction<{ stepId: string; updates: Partial<WorkflowStep> }>) => {
      if (state.currentWorkflow) {
        const stepIndex = state.currentWorkflow.steps.findIndex(step => step.id === action.payload.stepId);
        if (stepIndex !== -1) {
          state.currentWorkflow.steps[stepIndex] = {
            ...state.currentWorkflow.steps[stepIndex],
            ...action.payload.updates
          };
        }
      }
    },
    removeWorkflowStep: (state, action: PayloadAction<string>) => {
      if (state.currentWorkflow) {
        state.currentWorkflow.steps = state.currentWorkflow.steps.filter(step => step.id !== action.payload);
        // Remove connections to this step
        state.currentWorkflow.steps.forEach(step => {
          step.connections = step.connections.filter(conn => conn !== action.payload);
        });
      }
    },
    connectSteps: (state, action: PayloadAction<{ fromStepId: string; toStepId: string }>) => {
      if (state.currentWorkflow) {
        const fromStep = state.currentWorkflow.steps.find(step => step.id === action.payload.fromStepId);
        if (fromStep && !fromStep.connections.includes(action.payload.toStepId)) {
          fromStep.connections.push(action.payload.toStepId);
        }
      }
    },
    disconnectSteps: (state, action: PayloadAction<{ fromStepId: string; toStepId: string }>) => {
      if (state.currentWorkflow) {
        const fromStep = state.currentWorkflow.steps.find(step => step.id === action.payload.fromStepId);
        if (fromStep) {
          fromStep.connections = fromStep.connections.filter(conn => conn !== action.payload.toStepId);
        }
      }
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Workflows
      .addCase(fetchWorkflows.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWorkflows.fulfilled, (state, action) => {
        state.isLoading = false;
        state.workflows = action.payload;
      })
      .addCase(fetchWorkflows.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch workflows';
      })
      // Create Workflow
      .addCase(createWorkflow.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createWorkflow.fulfilled, (state, action) => {
        state.isLoading = false;
        state.workflows.push(action.payload);
        state.currentWorkflow = action.payload;
      })
      .addCase(createWorkflow.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to create workflow';
      })
      // Update Workflow
      .addCase(updateWorkflow.fulfilled, (state, action) => {
        const index = state.workflows.findIndex(w => w.id === action.payload.id);
        if (index !== -1) {
          state.workflows[index] = action.payload;
        }
        if (state.currentWorkflow?.id === action.payload.id) {
          state.currentWorkflow = action.payload;
        }
      })
      // Delete Workflow
      .addCase(deleteWorkflow.fulfilled, (state, action) => {
        state.workflows = state.workflows.filter(w => w.id !== action.payload);
        if (state.currentWorkflow?.id === action.payload) {
          state.currentWorkflow = null;
        }
      })
      // Toggle Workflow Status
      .addCase(toggleWorkflowStatus.fulfilled, (state, action) => {
        const workflow = state.workflows.find(w => w.id === action.payload.id);
        if (workflow) {
          workflow.status = action.payload.status;
        }
      })
      // Fetch Workflow Executions
      .addCase(fetchWorkflowExecutions.pending, (state) => {
        state.isExecutionLoading = true;
      })
      .addCase(fetchWorkflowExecutions.fulfilled, (state, action) => {
        state.isExecutionLoading = false;
        state.executionLogs = action.payload;
      })
      .addCase(fetchWorkflowExecutions.rejected, (state, action) => {
        state.isExecutionLoading = false;
        state.error = action.error.message || 'Failed to fetch execution logs';
      });
  }
});

export const {
  setFilters,
  setCurrentWorkflow,
  setSelectedStep,
  updateCanvasPosition,
  setZoom,
  addWorkflowStep,
  updateWorkflowStep,
  removeWorkflowStep,
  connectSteps,
  disconnectSteps,
  clearError
} = workflowsSlice.actions;

export default workflowsSlice.reducer;