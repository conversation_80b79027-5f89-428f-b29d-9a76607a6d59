import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AIProvider, AIProviderConfig } from '@/services/ai/types';
import { AIService } from '@/services/ai/AIService';
import { AIProviderFactory } from '@/services/ai/AIProviderFactory';
import { SecureStorage } from '@/services/storage/SecureStorage';
import { APIKeyValidationService } from '@/services/ai/APIKeyValidationService';

export interface AIState {
  // Configuration
  currentProvider: AIProvider | null;
  configurations: Record<AIProvider, AIProviderConfig | null>;
  
  // UI State
  isConfiguring: boolean;
  validationStatus: Record<AIProvider, {
    isValidating: boolean;
    isValid: boolean;
    error: string | null;
  }>;
  
  // Provider Status
  providerStatuses: Record<AIProvider, {
    configured: boolean;
    available: boolean;
    lastError?: string;
  }>;
  
  // Usage Stats
  usageStats: {
    requestsToday: number;
    tokensUsed: number;
    remainingQuota?: number;
  } | null;
  
  // Settings UI
  settingsOpen: boolean;
  selectedProviderForConfig: AIProvider | null;
  
  // Error handling
  error: string | null;
  lastUpdated: string | null;
}

const initialState: AIState = {
  currentProvider: null,
  configurations: {
    openai: null,
    anthropic: null,
    gemini: null
  },
  isConfiguring: false,
  validationStatus: {
    openai: { isValidating: false, isValid: false, error: null },
    anthropic: { isValidating: false, isValid: false, error: null },
    gemini: { isValidating: false, isValid: false, error: null }
  },
  providerStatuses: {
    openai: { configured: false, available: false },
    anthropic: { configured: false, available: false },
    gemini: { configured: false, available: false }
  },
  usageStats: null,
  settingsOpen: false,
  selectedProviderForConfig: null,
  error: null,
  lastUpdated: null
};

// Async thunks
export const validateApiKey = createAsyncThunk(
  'ai/validateApiKey',
  async ({ provider, apiKey }: { provider: AIProvider; apiKey: string }) => {
    const result = await APIKeyValidationService.validateApiKey(provider, apiKey, {
      testConnection: true,
      checkQuota: true
    });
    return { provider, ...result };
  }
);

export const configureProvider = createAsyncThunk(
  'ai/configureProvider',
  async (config: AIProviderConfig) => {
    // Store configuration securely
    await SecureStorage.storeAIConfiguration(config.provider, config);
    
    // Configure the AI service
    const aiService = AIService.getInstance();
    const success = await aiService.configureProvider(config.provider, config);
    if (!success) {
      throw new Error(`Failed to configure ${config.provider} provider`);
    }
    
    // Set as current provider
    SecureStorage.setCurrentProvider(config.provider);
    
    return config;
  }
);

export const fetchProviderStatuses = createAsyncThunk(
  'ai/fetchProviderStatuses',
  async () => {
    return await AIProviderFactory.getAllProviderStatuses();
  }
);

export const fetchUsageStats = createAsyncThunk(
  'ai/fetchUsageStats',
  async () => {
    const aiService = AIService.getInstance();
    const stats = await aiService.getUsageStats();
    
    // Also store in secure storage for persistence
    if (stats) {
      await SecureStorage.storeUsageStats({
        ...stats,
        lastUpdated: new Date().toISOString()
      });
    }
    
    return stats;
  }
);

export const loadStoredConfigurations = createAsyncThunk(
  'ai/loadStoredConfigurations',
  async () => {
    const configurations = await SecureStorage.getAIConfigurations();
    const currentProvider = SecureStorage.getCurrentProvider();
    const usageStats = await SecureStorage.getUsageStats();
    
    return {
      configurations,
      currentProvider,
      usageStats
    };
  }
);

export const removeProviderConfiguration = createAsyncThunk(
  'ai/removeProviderConfiguration',
  async (provider: AIProvider) => {
    await SecureStorage.removeAIConfiguration(provider);
    
    // If this was the current provider, clear it
    const currentProvider = SecureStorage.getCurrentProvider();
    if (currentProvider === provider) {
      SecureStorage.setCurrentProvider(null);
    }
    
    return provider;
  }
);

export const switchProvider = createAsyncThunk(
  'ai/switchProvider',
  async (provider: AIProvider) => {
    // Get configuration from secure storage
    const config = await SecureStorage.getAIConfiguration(provider);
    
    if (!config) {
      throw new Error(`No configuration found for ${provider}`);
    }
    
    const aiService = AIService.getInstance();
    const success = await aiService.configureProvider(provider, config);
    
    if (!success) {
      throw new Error(`Failed to switch to ${provider}`);
    }
    
    // Update current provider in storage
    SecureStorage.setCurrentProvider(provider);
    
    return provider;
  }
);

const aiSlice = createSlice({
  name: 'ai',
  initialState,
  reducers: {
    // UI Actions
    openSettings: (state) => {
      state.settingsOpen = true;
    },
    closeSettings: (state) => {
      state.settingsOpen = false;
      state.selectedProviderForConfig = null;
      state.error = null;
    },
    selectProviderForConfig: (state, action: PayloadAction<AIProvider>) => {
      state.selectedProviderForConfig = action.payload;
    },
    
    // Configuration Management
    updateConfiguration: (state, action: PayloadAction<{ provider: AIProvider; config: Partial<AIProviderConfig> }>) => {
      const { provider, config } = action.payload;
      if (state.configurations[provider]) {
        state.configurations[provider] = { ...state.configurations[provider]!, ...config };
      } else {
        state.configurations[provider] = { provider, apiKey: '', ...config } as AIProviderConfig;
      }
    },
    
    removeConfiguration: (state, action: PayloadAction<AIProvider>) => {
      state.configurations[action.payload] = null;
      if (state.currentProvider === action.payload) {
        state.currentProvider = null;
      }
    },
    
    // Error handling
    clearError: (state) => {
      state.error = null;
    },
    
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    
    // Reset validation status
    resetValidationStatus: (state, action: PayloadAction<AIProvider>) => {
      state.validationStatus[action.payload] = {
        isValidating: false,
        isValid: false,
        error: null
      };
    }
  },
  extraReducers: (builder) => {
    // Validate API Key
    builder
      .addCase(validateApiKey.pending, (state, action) => {
        const provider = action.meta.arg.provider;
        state.validationStatus[provider].isValidating = true;
        state.validationStatus[provider].error = null;
      })
      .addCase(validateApiKey.fulfilled, (state, action) => {
        const { provider, isValid, error } = action.payload;
        state.validationStatus[provider] = {
          isValidating: false,
          isValid,
          error: error || null
        };
      })
      .addCase(validateApiKey.rejected, (state, action) => {
        const provider = action.meta.arg.provider;
        state.validationStatus[provider] = {
          isValidating: false,
          isValid: false,
          error: action.error.message || 'Validation failed'
        };
      });
    
    // Configure Provider
    builder
      .addCase(configureProvider.pending, (state) => {
        state.isConfiguring = true;
        state.error = null;
      })
      .addCase(configureProvider.fulfilled, (state, action) => {
        const config = action.payload;
        state.isConfiguring = false;
        state.configurations[config.provider] = config;
        state.currentProvider = config.provider;
        state.lastUpdated = new Date().toISOString();
        
        // Update provider status
        state.providerStatuses[config.provider] = {
          configured: true,
          available: true
        };
      })
      .addCase(configureProvider.rejected, (state, action) => {
        state.isConfiguring = false;
        state.error = action.error.message || 'Configuration failed';
      });
    
    // Fetch Provider Statuses
    builder
      .addCase(fetchProviderStatuses.fulfilled, (state, action) => {
        state.providerStatuses = action.payload;
      });
    
    // Fetch Usage Stats
    builder
      .addCase(fetchUsageStats.fulfilled, (state, action) => {
        state.usageStats = action.payload;
      });
    
    // Switch Provider
    builder
      .addCase(switchProvider.pending, (state) => {
        state.isConfiguring = true;
        state.error = null;
      })
      .addCase(switchProvider.fulfilled, (state, action) => {
        state.isConfiguring = false;
        state.currentProvider = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(switchProvider.rejected, (state, action) => {
        state.isConfiguring = false;
        state.error = action.error.message || 'Failed to switch provider';
      });
    
    // Load Stored Configurations
    builder
      .addCase(loadStoredConfigurations.fulfilled, (state, action) => {
        const { configurations, currentProvider, usageStats } = action.payload;
        
        // Update configurations (but don't store API keys in Redux state)
        Object.keys(configurations).forEach(provider => {
          const config = configurations[provider as AIProvider];
          if (config) {
            state.configurations[provider as AIProvider] = {
              ...config,
              apiKey: '***' // Mask API key in state
            };
            state.providerStatuses[provider as AIProvider] = {
              configured: true,
              available: true
            };
          }
        });
        
        state.currentProvider = currentProvider;
        if (usageStats) {
          state.usageStats = {
            requestsToday: usageStats.requestsToday,
            tokensUsed: usageStats.tokensUsed,
            remainingQuota: usageStats.remainingQuota
          };
        }
      });
    
    // Remove Provider Configuration
    builder
      .addCase(removeProviderConfiguration.fulfilled, (state, action) => {
        const provider = action.payload;
        state.configurations[provider] = null;
        state.providerStatuses[provider] = {
          configured: false,
          available: false
        };
        
        if (state.currentProvider === provider) {
          state.currentProvider = null;
        }
        
        state.lastUpdated = new Date().toISOString();
      });
  }
});

export const {
  openSettings,
  closeSettings,
  selectProviderForConfig,
  updateConfiguration,
  removeConfiguration,
  clearError,
  setError,
  resetValidationStatus
} = aiSlice.actions;

export default aiSlice.reducer;

// Selectors
export const selectCurrentProvider = (state: { ai: AIState }) => state.ai.currentProvider;
export const selectProviderConfigurations = (state: { ai: AIState }) => state.ai.configurations;
export const selectProviderStatuses = (state: { ai: AIState }) => state.ai.providerStatuses;
export const selectValidationStatus = (state: { ai: AIState }) => state.ai.validationStatus;
export const selectUsageStats = (state: { ai: AIState }) => state.ai.usageStats;
export const selectIsConfiguring = (state: { ai: AIState }) => state.ai.isConfiguring;
export const selectSettingsOpen = (state: { ai: AIState }) => state.ai.settingsOpen;
export const selectSelectedProviderForConfig = (state: { ai: AIState }) => state.ai.selectedProviderForConfig;
export const selectAIError = (state: { ai: AIState }) => state.ai.error;