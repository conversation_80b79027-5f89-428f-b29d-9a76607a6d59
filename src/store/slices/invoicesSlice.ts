import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '@/store';

// --- INTERFACES ---

export interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxable: boolean;
}

export interface PaymentRecord {
  id: string;
  amount: number;
  method: string;
  transactionId: string;
  status: string;
  processedAt: string;
  gateway: 'stripe' | 'paypal' | 'bank';
}

export interface AuditRecord {
  id: string;
  action: string;
  userId: number;
  timestamp: string;
  changes: Record<string, any>;
  comments?: string;
}

export interface ApprovalLevel {
  level: number;
  role: 'manager' | 'executive' | 'finance';
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: number;
  approvedAt?: string;
  comments?: string;
}

export interface Invoice {
  id: number;
  invoiceNumber: string;
  contractId?: number;
  vendorId: number;
  lineItems: LineItem[];
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  penalties: number;
  discounts: number;
  totalAmount: number;
  currency: 'USD' | 'EUR' | 'GBP';
  status: 'draft' | 'pending' | 'approved' | 'sent' | 'paid' | 'overdue' | 'disputed';
  approvalStatus?: 'pending' | 'approved' | 'rejected';
  approvalLevels: ApprovalLevel[];
  currentApprovalLevel: number;
  approvedBy?: number;
  approvedAt?: string;
  approvalComments?: string;
  createdAt: string;
  updatedAt: string;
  dueDate: string;
  sentDate?: string;
  paidDate?: string;
  notes?: string;
  attachments: unknown[];
  paymentHistory: PaymentRecord[];
  auditTrail: AuditRecord[];
  paymentMethod?: 'stripe' | 'paypal' | 'bank_transfer';
  transactionId?: string;
  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';
}

export interface InvoiceFilters {
  search?: string;
  status?: string;
  vendorId?: number;
  dateRange?: { start: string; end: string };
  amountRange?: { min: number; max: number };
}

interface InvoicesState {
  invoices: Invoice[];
  currentInvoice: Invoice | null;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  filters: InvoiceFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// --- INITIAL STATE ---

const initialState: InvoicesState = {
  invoices: [],
  currentInvoice: null,
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  filters: {},
  pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
};

// --- MOCK DATA & HELPERS ---

// Mock approval config helper
function getApprovalLevels(amount: number): ApprovalLevel[] {
  const levels: ApprovalLevel[] = [{ level: 1, role: 'manager', status: 'pending' }];
  if (amount > 10000) levels.push({ level: 2, role: 'executive', status: 'pending' });
  if (amount > 50000) levels.push({ level: 3, role: 'finance', status: 'pending' });
  return levels;
}

const mockInvoices: Invoice[] = [
  {
    id: 1,
    invoiceNumber: 'INV-001',
    contractId: 1,
    vendorId: 1,
    lineItems: [{ id: '1', description: 'Development Phase 1', quantity: 1, unitPrice: 25000, totalPrice: 25000, taxable: true }],
    subtotal: 25000,
    taxRate: 0.1,
    taxAmount: 2500,
    penalties: 0,
    discounts: 0,
    totalAmount: 27500,
    currency: 'USD',
    status: 'approved',
    approvalLevels: getApprovalLevels(27500),
    currentApprovalLevel: 1,
    dueDate: '2024-03-01T00:00:00.000Z',
    createdAt: '2024-02-01T10:30:00.000Z',
    updatedAt: '2024-02-15T14:22:00.000Z',
    attachments: [],
    paymentHistory: [],
    auditTrail: [],
  },
  {
    id: 2,
    invoiceNumber: 'INV-002',
    contractId: 2,
    vendorId: 2,
    lineItems: [
      { id: '2', description: 'Consulting Services', quantity: 40, unitPrice: 150, totalPrice: 6000, taxable: true },
      { id: '3', description: 'Travel Expenses', quantity: 1, unitPrice: 500, totalPrice: 500, taxable: false }
    ],
    subtotal: 6500,
    taxRate: 0.08,
    taxAmount: 480,
    penalties: 0,
    discounts: 0,
    totalAmount: 6980,
    currency: 'USD',
    status: 'pending',
    approvalLevels: getApprovalLevels(6980),
    currentApprovalLevel: 1,
    dueDate: '2024-04-15T00:00:00.000Z',
    createdAt: '2024-03-15T09:15:00.000Z',
    updatedAt: '2024-03-15T09:15:00.000Z',
    attachments: [],
    paymentHistory: [],
    auditTrail: [],
  },
  {
    id: 3,
    invoiceNumber: 'INV-003',
    contractId: 3,
    vendorId: 3,
    lineItems: [{ id: '4', description: 'Software License', quantity: 1, unitPrice: 12000, totalPrice: 12000, taxable: true }],
    subtotal: 12000,
    taxRate: 0.1,
    taxAmount: 1200,
    penalties: 0,
    discounts: 600,
    totalAmount: 12600,
    currency: 'USD',
    status: 'paid',
    approvalLevels: getApprovalLevels(12600),
    currentApprovalLevel: 1,
    dueDate: '2024-02-28T00:00:00.000Z',
    createdAt: '2024-01-28T14:20:00.000Z',
    updatedAt: '2024-02-25T16:45:00.000Z',
    paidDate: '2024-02-25T16:45:00.000Z',
    attachments: [],
    paymentHistory: [
      {
        id: 'pay-1',
        amount: 12600,
        method: 'credit_card',
        transactionId: 'txn_1234567890',
        status: 'completed',
        processedAt: '2024-02-25T16:45:00.000Z',
        gateway: 'stripe'
      }
    ],
    auditTrail: [
      {
        id: 'audit-1',
        action: 'created',
        userId: 1,
        timestamp: '2024-01-28T14:20:00.000Z',
        changes: { status: 'draft' }
      },
      {
        id: 'audit-2',
        action: 'approved',
        userId: 2,
        timestamp: '2024-02-01T10:00:00.000Z',
        changes: { status: 'approved' },
        comments: 'Approved for payment'
      }
    ],
  },
  {
    id: 4,
    invoiceNumber: 'INV-004',
    contractId: 4,
    vendorId: 4,
    lineItems: [{ id: '5', description: 'Marketing Campaign', quantity: 1, unitPrice: 8500, totalPrice: 8500, taxable: true }],
    subtotal: 8500,
    taxRate: 0.1,
    taxAmount: 850,
    penalties: 0,
    discounts: 0,
    totalAmount: 9350,
    currency: 'USD',
    status: 'overdue',
    approvalLevels: getApprovalLevels(9350),
    currentApprovalLevel: 1,
    dueDate: '2024-01-15T00:00:00.000Z',
    createdAt: '2023-12-15T11:30:00.000Z',
    updatedAt: '2023-12-15T11:30:00.000Z',
    attachments: [],
    paymentHistory: [],
    auditTrail: [],
  },
  {
    id: 5,
    invoiceNumber: 'INV-005',
    contractId: 5,
    vendorId: 5,
    lineItems: [{ id: '6', description: 'Equipment Rental', quantity: 3, unitPrice: 2000, totalPrice: 6000, taxable: true }],
    subtotal: 6000,
    taxRate: 0.1,
    taxAmount: 600,
    penalties: 0,
    discounts: 0,
    totalAmount: 6600,
    currency: 'USD',
    status: 'draft',
    approvalLevels: getApprovalLevels(6600),
    currentApprovalLevel: 1,
    dueDate: '2024-05-01T00:00:00.000Z',
    createdAt: '2024-04-01T08:00:00.000Z',
    updatedAt: '2024-04-01T08:00:00.000Z',
    attachments: [],
    paymentHistory: [],
    auditTrail: [],
  }
];

// --- ASYNC THUNKS ---

export const fetchInvoicesAsync = createAsyncThunk(
  'invoices/fetchInvoices',
  async ({ page = 1, limit = 10, filters = {} }: { page?: number; limit?: number; filters?: InvoiceFilters } = {}) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    let filteredInvoices = [...mockInvoices];
    
    // Apply filters
    if (filters.search) {
      filteredInvoices = filteredInvoices.filter(invoice =>
        invoice.invoiceNumber?.toLowerCase().includes(filters.search!.toLowerCase()) ||
        invoice.vendorId.toString().includes(filters.search!)
      );
    }
    
    if (filters.status) {
      filteredInvoices = filteredInvoices.filter(invoice => invoice.status === filters.status);
    }
    
    if (filters.vendorId) {
      filteredInvoices = filteredInvoices.filter(invoice => invoice.vendorId === filters.vendorId);
    }
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedInvoices = filteredInvoices.slice(startIndex, endIndex);
    
    return {
      data: paginatedInvoices,
      pagination: {
        page,
        limit,
        total: filteredInvoices.length,
        totalPages: Math.ceil(filteredInvoices.length / limit),
      },
    };
  }
);

export const fetchInvoiceByIdAsync = createAsyncThunk(
  'invoices/fetchInvoiceById',
  async (id: number) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const invoice = mockInvoices.find(inv => inv.id === id);
    if (!invoice) throw new Error('Invoice not found');
    return invoice;
  }
);

export const createInvoiceAsync = createAsyncThunk(
  'invoices/createInvoice',
  async (data: Partial<Invoice>) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newInvoice: Invoice = {
      id: Date.now(), // Mock ID generation
      invoiceNumber: `INV-${String(mockInvoices.length + 1).padStart(3, '0')}`,
      contractId: data.contractId,
      vendorId: data.vendorId || 1,
      lineItems: data.lineItems || [],
      subtotal: data.subtotal || 0,
      taxRate: data.taxRate || 0.1,
      taxAmount: data.taxAmount || 0,
      penalties: data.penalties || 0,
      discounts: data.discounts || 0,
      totalAmount: data.totalAmount || 0,
      currency: data.currency || 'USD',
      status: data.status || 'draft',
      approvalLevels: getApprovalLevels(data.totalAmount || 0),
      currentApprovalLevel: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      dueDate: data.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      notes: data.notes,
      attachments: data.attachments || [],
      paymentHistory: data.paymentHistory || [],
      auditTrail: data.auditTrail || [],
    };
    
    mockInvoices.push(newInvoice);
    return newInvoice;
  }
);

export const approveInvoiceAsync = createAsyncThunk(
  'invoices/approveInvoice',
  async ({ id, comments }: { id: number; comments?: string }) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const index = mockInvoices.findIndex(inv => inv.id === id);
    if (index === -1) throw new Error('Invoice not found');
    
    const invoice = mockInvoices[index];
    invoice.status = 'approved';
    invoice.updatedAt = new Date().toISOString();
    
    // Update approval levels
    if (invoice.approvalLevels && invoice.approvalLevels.length > 0) {
      const currentLevel = invoice.approvalLevels.find(level => level.level === invoice.currentApprovalLevel);
      if (currentLevel) {
        currentLevel.status = 'approved';
        currentLevel.approvedAt = new Date().toISOString();
        currentLevel.approvedBy = 1; // Mock user ID
        currentLevel.comments = comments;
      }
    }
    
    // Add audit trail
    invoice.auditTrail.push({
      id: `audit-${Date.now()}`,
      action: 'approved',
      userId: 1,
      timestamp: new Date().toISOString(),
      changes: { status: 'approved' },
      comments
    });
    
    mockInvoices[index] = invoice;
    return invoice;
  }
);

export const updateInvoiceAsync = createAsyncThunk(
  'invoices/updateInvoice',
  async ({ id, data }: { id: number; data: Partial<Invoice> }) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const index = mockInvoices.findIndex(inv => inv.id === id);
    if (index === -1) throw new Error('Invoice not found');
    
    const updatedInvoice = {
      ...mockInvoices[index],
      ...data,
      updatedAt: new Date().toISOString(),
    };
    
    // Add audit trail
    updatedInvoice.auditTrail.push({
      id: `audit-${Date.now()}`,
      action: 'updated',
      userId: 1,
      timestamp: new Date().toISOString(),
      changes: data,
    });
    
    mockInvoices[index] = updatedInvoice;
    return updatedInvoice;
  }
);

export const deleteInvoiceAsync = createAsyncThunk(
  'invoices/deleteInvoice',
  async (id: number) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const index = mockInvoices.findIndex(inv => inv.id === id);
    if (index === -1) throw new Error('Invoice not found');
    
    mockInvoices.splice(index, 1);
    return id;
  }
);

export const payInvoiceAsync = createAsyncThunk(
  'invoices/payInvoice',
  async ({ id, amount, method }: { id: number; amount: number; method: 'stripe' | 'paypal' | 'bank_transfer' }) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const success = Math.random() > 0.1; // 90% success rate for mock
    if (!success) throw new Error('Payment failed');

    const index = mockInvoices.findIndex(inv => inv.id === id);
    if (index !== -1) {
      const invoice = mockInvoices[index];
      const paymentRecord: PaymentRecord = {
        id: crypto.randomUUID(),
        amount,
        method,
        transactionId: `TX-${Date.now()}`,
        status: 'completed',
        processedAt: new Date().toISOString(),
        gateway: method === 'bank_transfer' ? 'bank' : method,
      };
      invoice.paymentHistory.push(paymentRecord);
      invoice.status = 'paid';
      invoice.paidDate = new Date().toISOString();
      invoice.updatedAt = new Date().toISOString();
      mockInvoices[index] = invoice;
      return invoice;
    }
    throw new Error('Invoice not found');
  }
);

// --- SLICE DEFINITION ---

const invoicesSlice = createSlice({
  name: 'invoices',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<InvoiceFilters>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearCurrentInvoice: (state) => {
      state.currentInvoice = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch All Invoices
      .addCase(fetchInvoicesAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchInvoicesAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.invoices = action.payload.data;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchInvoicesAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch invoices';
      })
      // Fetch Single Invoice
      .addCase(fetchInvoiceByIdAsync.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchInvoiceByIdAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentInvoice = action.payload;
      })
      .addCase(fetchInvoiceByIdAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch invoice';
      })
      // Create Invoice
      .addCase(createInvoiceAsync.pending, (state) => {
        state.isCreating = true;
      })
      .addCase(createInvoiceAsync.fulfilled, (state, action) => {
        state.isCreating = false;
        state.invoices.push(action.payload);
      })
      .addCase(createInvoiceAsync.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.error.message || 'Failed to create invoice';
      })
      // Update Invoice
      .addCase(updateInvoiceAsync.pending, (state) => {
        state.isUpdating = true;
      })
      .addCase(updateInvoiceAsync.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.invoices.findIndex(i => i.id === action.payload.id);
        if (index !== -1) state.invoices[index] = action.payload;
        if (state.currentInvoice?.id === action.payload.id) state.currentInvoice = action.payload;
      })
      .addCase(updateInvoiceAsync.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.error.message || 'Failed to update invoice';
      })
      // Delete Invoice
      .addCase(deleteInvoiceAsync.pending, (state) => {
        state.isDeleting = true;
      })
      .addCase(deleteInvoiceAsync.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.invoices = state.invoices.filter(i => i.id !== action.payload);
      })
      .addCase(deleteInvoiceAsync.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.error.message || 'Failed to delete invoice';
      })
      // Approve Invoice
      .addCase(approveInvoiceAsync.pending, (state) => {
        state.isUpdating = true;
      })
      .addCase(approveInvoiceAsync.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.invoices.findIndex(i => i.id === action.payload.id);
        if (index !== -1) state.invoices[index] = action.payload;
        if (state.currentInvoice?.id === action.payload.id) state.currentInvoice = action.payload;
      })
      .addCase(approveInvoiceAsync.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.error.message || 'Failed to approve invoice';
      })
      // Pay Invoice
      .addCase(payInvoiceAsync.pending, (state) => {
        state.isUpdating = true;
      })
      .addCase(payInvoiceAsync.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.invoices.findIndex(i => i.id === action.payload.id);
        if (index !== -1) state.invoices[index] = action.payload;
        if (state.currentInvoice?.id === action.payload.id) state.currentInvoice = action.payload;
      })
      .addCase(payInvoiceAsync.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.error.message || 'Failed to process payment';
      });
  },
});

// --- EXPORTS ---

export const { setFilters, clearCurrentInvoice, clearError } = invoicesSlice.actions;

export const selectInvoices = (state: RootState) => state.invoices.invoices;
export const selectInvoicesStatus = (state: RootState) => (state.invoices.isLoading ? 'loading' : 'idle');
export const selectInvoiceById = (state: RootState, id: string) =>
  state.invoices.invoices.find(inv => inv.id === Number(id)) || null;

export default invoicesSlice.reducer;
