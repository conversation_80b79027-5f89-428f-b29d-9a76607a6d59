import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface SystemSettings {
  id: string;
  organizationName: string;
  organizationLogo: string;
  contactEmail: string;
  contactPhone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  passwordPolicy: PasswordPolicy;
  sessionTimeout: number;
  maxLoginAttempts: number;
  notificationSettings: NotificationSettings;
  maintenanceMode: boolean;
  updatedAt: string;
  updatedBy: string;
}

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  expiryDays: number;
  preventReuse: number;
}

export interface NotificationSettings {
  emailEnabled: boolean;
  smsEnabled: boolean;
  pushEnabled: boolean;
  templates: {
    welcome: string;
    invitation: string;
    passwordReset: string;
    contractExpiry: string;
    invoiceReminder: string;
  };
}

export interface CustomField {
  id: string;
  entityType: 'vendor' | 'contract' | 'invoice';
  fieldName: string;
  fieldLabel: string;
  fieldType: 'text' | 'number' | 'date' | 'select' | 'boolean' | 'textarea';
  required: boolean;
  validation: ValidationRules;
  options?: SelectOption[];
  defaultValue?: any;
  helpText?: string;
  order: number;
  active: boolean;
  createdAt: string;
  createdBy: string;
}

export interface ValidationRules {
  min?: number;
  max?: number;
  pattern?: string;
  customValidator?: string;
}

export interface SelectOption {
  value: string;
  label: string;
}

export interface AuditLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  entityType: string;
  entityId: string;
  changes: Record<string, { old: any; new: any }>;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
}

interface SystemState {
  settings: SystemSettings | null;
  customFields: CustomField[];
  auditLogs: AuditLog[];
  isLoading: boolean;
  customFieldsLoading: boolean;
  auditLogsLoading: boolean;
  error: string | null;
}

const initialState: SystemState = {
  settings: null,
  customFields: [],
  auditLogs: [],
  isLoading: false,
  customFieldsLoading: false,
  auditLogsLoading: false,
  error: null,
};

// Mock system settings
const mockSystemSettings: SystemSettings = {
  id: '1',
  organizationName: 'VendorMS Corp',
  organizationLogo: '/placeholder.svg',
  contactEmail: '<EMAIL>',
  contactPhone: '+****************',
  address: {
    street: '123 Business Ave',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94105',
    country: 'USA',
  },
  passwordPolicy: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    expiryDays: 90,
    preventReuse: 5,
  },
  sessionTimeout: 30,
  maxLoginAttempts: 5,
  notificationSettings: {
    emailEnabled: true,
    smsEnabled: false,
    pushEnabled: true,
    templates: {
      welcome: 'Welcome to VendorMS! Your account has been created successfully.',
      invitation: 'You have been invited to join VendorMS. Click the link to get started.',
      passwordReset: 'Click the link below to reset your password.',
      contractExpiry: 'Your contract is expiring soon. Please review and renew.',
      invoiceReminder: 'You have pending invoices that require attention.',
    },
  },
  maintenanceMode: false,
  updatedAt: new Date().toISOString(),
  updatedBy: '<EMAIL>',
};

// Async thunks
export const fetchSystemSettingsAsync = createAsyncThunk(
  'system/fetchSettings',
  async () => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return mockSystemSettings;
  }
);

export const updateSystemSettingsAsync = createAsyncThunk(
  'system/updateSettings',
  async (settings: Partial<SystemSettings>) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const updatedSettings = {
      ...mockSystemSettings,
      ...settings,
      updatedAt: new Date().toISOString(),
      updatedBy: '<EMAIL>', // In real app, get from current user
    };
    
    return updatedSettings;
  }
);

export const fetchCustomFieldsAsync = createAsyncThunk(
  'system/fetchCustomFields',
  async () => {
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const mockCustomFields: CustomField[] = [
      {
        id: '1',
        entityType: 'vendor',
        fieldName: 'tax_id',
        fieldLabel: 'Tax ID',
        fieldType: 'text',
        required: true,
        validation: { pattern: '^[0-9]{2}-[0-9]{7}$' },
        helpText: 'Format: XX-XXXXXXX',
        order: 1,
        active: true,
        createdAt: '2024-01-01T00:00:00Z',
        createdBy: '<EMAIL>',
      },
      {
        id: '2',
        entityType: 'contract',
        fieldName: 'priority',
        fieldLabel: 'Priority Level',
        fieldType: 'select',
        required: false,
        validation: {},
        options: [
          { value: 'low', label: 'Low' },
          { value: 'medium', label: 'Medium' },
          { value: 'high', label: 'High' },
          { value: 'critical', label: 'Critical' },
        ],
        defaultValue: 'medium',
        order: 1,
        active: true,
        createdAt: '2024-01-01T00:00:00Z',
        createdBy: '<EMAIL>',
      },
    ];
    
    return mockCustomFields;
  }
);

export const createCustomFieldAsync = createAsyncThunk(
  'system/createCustomField',
  async (fieldData: Omit<CustomField, 'id' | 'createdAt' | 'createdBy'>) => {
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const newField: CustomField = {
      ...fieldData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      createdBy: '<EMAIL>', // In real app, get from current user
    };
    
    return newField;
  }
);

export const updateCustomFieldAsync = createAsyncThunk(
  'system/updateCustomField',
  async ({ id, fieldData }: { id: string; fieldData: Partial<CustomField> }) => {
    await new Promise(resolve => setTimeout(resolve, 800));
    
    return { id, fieldData };
  }
);

export const deleteCustomFieldAsync = createAsyncThunk(
  'system/deleteCustomField',
  async (id: string) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return id;
  }
);

export const fetchAuditLogsAsync = createAsyncThunk(
  'system/fetchAuditLogs',
  async (filters?: { startDate?: string; endDate?: string; userId?: string; action?: string }) => {
    await new Promise(resolve => setTimeout(resolve, 1200));
    
    const mockAuditLogs: AuditLog[] = [
      {
        id: '1',
        userId: '1',
        userName: 'System Administrator',
        action: 'USER_ROLE_UPDATED',
        entityType: 'user',
        entityId: '2',
        changes: {
          role: { old: 'viewer', new: 'manager' },
        },
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      },
      {
        id: '2',
        userId: '1',
        userName: 'System Administrator',
        action: 'SYSTEM_SETTINGS_UPDATED',
        entityType: 'settings',
        entityId: '1',
        changes: {
          sessionTimeout: { old: 60, new: 30 },
        },
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      },
      {
        id: '3',
        userId: '1',
        userName: 'System Administrator',
        action: 'USER_INVITED',
        entityType: 'invitation',
        entityId: '3',
        changes: {
          email: { old: null, new: '<EMAIL>' },
          role: { old: null, new: 'viewer' },
        },
        timestamp: new Date(Date.now() - 10800000).toISOString(),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      },
    ];
    
    return mockAuditLogs;
  }
);

const systemSlice = createSlice({
  name: 'system',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch System Settings
      .addCase(fetchSystemSettingsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSystemSettingsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.settings = action.payload;
      })
      .addCase(fetchSystemSettingsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch system settings';
      })
      // Update System Settings
      .addCase(updateSystemSettingsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateSystemSettingsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.settings = action.payload;
      })
      .addCase(updateSystemSettingsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update system settings';
      })
      // Fetch Custom Fields
      .addCase(fetchCustomFieldsAsync.pending, (state) => {
        state.customFieldsLoading = true;
        state.error = null;
      })
      .addCase(fetchCustomFieldsAsync.fulfilled, (state, action) => {
        state.customFieldsLoading = false;
        state.customFields = action.payload;
      })
      .addCase(fetchCustomFieldsAsync.rejected, (state, action) => {
        state.customFieldsLoading = false;
        state.error = action.error.message || 'Failed to fetch custom fields';
      })
      // Create Custom Field
      .addCase(createCustomFieldAsync.fulfilled, (state, action) => {
        state.customFields.push(action.payload);
      })
      // Update Custom Field
      .addCase(updateCustomFieldAsync.fulfilled, (state, action) => {
        const { id, fieldData } = action.payload;
        const fieldIndex = state.customFields.findIndex(field => field.id === id);
        if (fieldIndex !== -1) {
          state.customFields[fieldIndex] = { ...state.customFields[fieldIndex], ...fieldData };
        }
      })
      // Delete Custom Field
      .addCase(deleteCustomFieldAsync.fulfilled, (state, action) => {
        state.customFields = state.customFields.filter(field => field.id !== action.payload);
      })
      // Fetch Audit Logs
      .addCase(fetchAuditLogsAsync.pending, (state) => {
        state.auditLogsLoading = true;
        state.error = null;
      })
      .addCase(fetchAuditLogsAsync.fulfilled, (state, action) => {
        state.auditLogsLoading = false;
        state.auditLogs = action.payload;
      })
      .addCase(fetchAuditLogsAsync.rejected, (state, action) => {
        state.auditLogsLoading = false;
        state.error = action.error.message || 'Failed to fetch audit logs';
      });
  },
});

export const { clearError } = systemSlice.actions;
export default systemSlice.reducer;