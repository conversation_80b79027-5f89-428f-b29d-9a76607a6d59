import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import vendorsSlice from './slices/vendorsSlice';
import contractsSlice from './slices/contractsSlice';
import invoicesSlice from './slices/invoicesSlice';
import notificationsSlice from './slices/notificationsSlice';
import uiSlice from './slices/uiSlice';
import analyticsSlice from './slices/analyticsSlice';
import workflowsReducer from './slices/workflowsSlice';
import systemSlice from './slices/systemSlice';
import aiSlice from './slices/aiSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    vendors: vendorsSlice,
    contracts: contractsSlice,
    invoices: invoicesSlice,
    notifications: notificationsSlice,
    ui: uiSlice,
    analytics: analyticsSlice,
    workflows: workflowsReducer,
    system: systemSlice,
    ai: aiSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
        ignoredPaths: [
          'analytics.dashboard.filters.dateRange.start',
          'analytics.dashboard.filters.dateRange.end',
          'analytics.dashboard.lastUpdated',
          'analytics.realtime.lastUpdate',
          'reports.configs',
          'reports.generated',
        ],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;