import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, Building2, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AccountForm } from '../components/accounts';
import { useAccountForm } from '../hooks/useAccountForm';
import { useAuth } from '../hooks/useAuth';
import { toast } from '../components/ui/sonner';

export const AccountCreate: React.FC = () => {
  const navigate = useNavigate();
  const { canEdit } = useAuth();
  
  const { handleSubmit, handleCancel, isSubmitting } = useAccountForm({
    mode: 'create',
    onSuccess: (account) => {
      // Show success message with account details
      toast.success('Account created successfully!', {
        description: `${account.name} has been added to your accounts.`,
        action: {
          label: 'View Account',
          onClick: () => navigate(`/accounts/${account.id}`),
        },
      });
    },
    redirectTo: '/accounts/list',
  });

  // Check permissions on mount
  useEffect(() => {
    if (!canEdit()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to create accounts.',
      });
      navigate('/accounts/list');
    }
  }, [canEdit, navigate]);

  // Don't render if user doesn't have permission
  if (!canEdit()) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="border-b bg-card"
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/accounts/list')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Accounts
              </Button>
              <div className="h-6 w-px bg-border" />
              <div>
                <h1 className="text-2xl font-bold flex items-center gap-2">
                  <Building2 className="h-6 w-6 text-primary" />
                  Create New Account
                </h1>
                <p className="text-muted-foreground">
                  Add a new client account to your system
                </p>
              </div>
            </div>
            
            {/* Success Indicator */}
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4" />
              Ready to create
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="container mx-auto px-4 py-8"
      >
        <div className="max-w-4xl mx-auto">
          {/* Form Card */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="bg-card rounded-lg border shadow-sm"
          >
            <div className="p-6">
              <AccountForm
                mode="create"
                onSubmit={handleSubmit}
                isLoading={isSubmitting}
              />
            </div>
          </motion.div>

          {/* Help Text */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="mt-6 text-center text-sm text-muted-foreground"
          >
            <p>
              Need help? Check out our{' '}
              <Button variant="link" className="p-0 h-auto text-sm">
                account management guide
              </Button>{' '}
              or contact support.
            </p>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};