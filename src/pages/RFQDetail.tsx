import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  FileText,
  Users,
  Calendar,
  DollarSign,
  TrendingUp,
  Send,
  Eye,
  CheckCircle,
  Clock,
  AlertTriangle,
  MoreHorizontal,
  Edit,
  Download
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import RFQInvoiceTracking from '@/components/rfq/RFQInvoiceTracking';
import RFQStatusBadge from '@/components/rfq/RFQStatusBadge';
import RFQPerformanceMetrics from '@/components/rfq/RFQPerformanceMetrics';
import EnhancedItemsDisplay from '@/components/rfq/EnhancedItemsDisplay';
import InvitationsTab from '@/components/rfq/tabs/InvitationsTab';
import SubmissionsTab from '@/components/rfq/tabs/SubmissionsTab';
import AnalysisTab from '@/components/rfq/tabs/AnalysisTab';
import QuotesTab from '@/components/rfq/tabs/QuotesTab';
import HistoryTab from '@/components/rfq/tabs/HistoryTab';
import { RFQApiService } from '@/services/api/rfq';
import { RFQ, RFQStatus } from '@/types/rfq';
import { getRFQStatusColor } from '@/types/rfq';
import { useAuth } from '@/hooks/useAuth';
import { useRFQLifecycle } from '@/hooks/useRFQLifecycle';
import { toast } from '@/components/ui/use-toast';

// Using RFQ interface from types

const RFQDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { canEdit } = useAuth();
  
  const [rfq, setRfq] = useState<RFQ | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Use the RFQ lifecycle hook - temporarily disabled to fix infinite refresh
  // const [lifecycleState, lifecycleActions] = useRFQLifecycle();
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchRFQDetail = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        setError(null);

        const response = await RFQApiService.getRFQById(parseInt(id));
        setRfq(response.data);
      } catch (err) {
        console.error('Error fetching RFQ details:', err);
        setError('Failed to load RFQ details');
        toast({
          title: 'Error',
          description: 'Failed to load RFQ details',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchRFQDetail();
  }, [id]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Send className="w-4 h-4 text-blue-500" />;
      case 'in_progress':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'closed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'cancelled':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    const color = getRFQStatusColor(status as RFQStatus);
    return `bg-${color}-100 text-${color}-800 dark:bg-${color}-900 dark:text-${color}-300`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-muted rounded w-1/4 mb-4"></div>
            <div className="h-64 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !rfq) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card>
            <CardContent className="p-8 text-center">
              <AlertTriangle className="w-16 h-16 text-destructive mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">RFQ Not Found</h2>
              <p className="text-muted-foreground mb-6">
                {error || 'The RFQ you\'re looking for doesn\'t exist or has been removed.'}
              </p>
              <Button onClick={() => navigate('/rfqs')}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to RFQs
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm border-b border-border"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/rfqs')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to RFQs
              </Button>
              <div className="h-6 w-px bg-border" />
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                  <FileText className="w-4 h-4 text-primary" />
                </div>
                <div>
                  <h1 className="text-lg font-semibold">{rfq.title}</h1>
                  <p className="text-sm text-muted-foreground">RFQ #{rfq.id}</p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <RFQStatusBadge
                status={rfq.status as RFQStatus}
                allowTransitions={canEdit()}
                rfqId={rfq.id}
                onStatusChange={async (newStatus: RFQStatus, reason?: string) => {
                  // TODO: Implement status change without lifecycle hook
                  console.log('Status change requested:', newStatus, reason);
                  // Refresh the local RFQ data
                  const response = await RFQApiService.getRFQById(rfq.id);
                  setRfq(response.data);
                }}
                disabled={false}
              />

              {canEdit() && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => navigate(`/rfqs/${rfq.id}/edit`)}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit RFQ
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Download className="w-4 h-4 mr-2" />
                      Export Report
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-8">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="invitations">Invitations</TabsTrigger>
            <TabsTrigger value="submissions">Submissions</TabsTrigger>
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
            <TabsTrigger value="quotes">Quotes</TabsTrigger>
            <TabsTrigger value="invoices">Invoices</TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* RFQ Details */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="lg:col-span-2"
              >
                <Card>
                  <CardHeader>
                    <CardTitle>RFQ Details</CardTitle>
                    <CardDescription>
                      Created by {rfq.creator_email} on {formatDate(rfq.created_at)}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Description</h4>
                      <p className="text-muted-foreground">
                        {rfq.description || 'No description provided'}
                      </p>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-1">Due Date</h4>
                        <p className="text-muted-foreground">{formatDate(rfq.due_date)}</p>
                      </div>
                      <div>
                        <h4 className="font-medium mb-1">Currency</h4>
                        <p className="text-muted-foreground">{rfq.currency}</p>
                      </div>
                    </div>

                    {/* Partial Selection Information */}
                    <div>
                      <h4 className="font-medium mb-2">Partial Selection</h4>
                      <div className="flex items-center space-x-2">
                        {rfq.allow_partial_selection ? (
                          <>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span className="text-sm text-green-700 dark:text-green-400">
                              Partial selection requested
                            </span>
                          </>
                        ) : (
                          <>
                            <AlertTriangle className="w-4 h-4 text-amber-500" />
                            <span className="text-sm text-muted-foreground">
                              Full submission required
                            </span>
                          </>
                        )}
                      </div>
                      {rfq.allow_partial_selection && rfq.partial_selection_config && (
                        <div className="mt-2 p-3 bg-muted/50 rounded-lg">
                          <p className="text-xs text-muted-foreground mb-1">Configuration:</p>
                          <ul className="text-xs text-muted-foreground space-y-1">
                            <li>• Vendor confirmation: {rfq.partial_selection_config.requireVendorConfirmation ? 'Required' : 'Not required'}</li>
                            {rfq.partial_selection_config.confirmationMessage && (
                              <li>• Message: "{rfq.partial_selection_config.confirmationMessage}"</li>
                            )}
                            <li>• Default allowed: {rfq.partial_selection_config.defaultAllowed ? 'Yes' : 'No'}</li>
                          </ul>
                        </div>
                      )}
                    </div>

                    <div>
                      <EnhancedItemsDisplay
                        items={rfq.items || []}
                        currency={rfq.currency}
                        maxDisplayItems={3}
                        showExpandButton={true}
                        compact={true}
                      />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Statistics */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="space-y-6"
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="w-5 h-5" />
                      Participation
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Invitations Sent</span>
                      <span className="font-medium">{rfq.invitation_count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Submissions</span>
                      <span className="font-medium">{rfq.submission_count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Response Rate</span>
                      <span className="font-medium">{rfq.response_rate}%</span>
                    </div>
                    <div className="pt-2">
                      <Progress value={rfq.response_rate} className="h-2" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="w-5 h-5" />
                      Timeline
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Created</span>
                      <span>{formatDateTime(rfq.created_at)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Last Updated</span>
                      <span>{formatDateTime(rfq.updated_at)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Due Date</span>
                      <span className={new Date(rfq.due_date) < new Date() ? 'text-red-600' : ''}>
                        {formatDateTime(rfq.due_date)}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                {/* Partial Selection Status Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="w-5 h-5" />
                      Selection Options
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Partial Selection</span>
                      <Badge variant={rfq.allow_partial_selection ? "default" : "secondary"}>
                        {rfq.allow_partial_selection ? "Allowed" : "Not Allowed"}
                      </Badge>
                    </div>
                    {rfq.allow_partial_selection && (
                      <>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Partial Submissions</span>
                          <span className="font-medium">{rfq.partial_selection_submissions || 0}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Vendor Confirmation</span>
                          <span className="text-xs">
                            {rfq.partial_selection_config?.requireVendorConfirmation ? "Required" : "Optional"}
                          </span>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </TabsContent>

          {/* Invitations Tab */}
          <TabsContent value="invitations">
            <InvitationsTab rfqId={rfq.id} canEdit={canEdit()} />
          </TabsContent>

          {/* Submissions Tab */}
          <TabsContent value="submissions">
            <SubmissionsTab rfqId={rfq.id} currency={rfq.currency} canEdit={canEdit()} />
          </TabsContent>

          {/* Analysis Tab */}
          <TabsContent value="analysis">
            <AnalysisTab rfqId={rfq.id} currency={rfq.currency} />
          </TabsContent>

          {/* Quotes Tab */}
          <TabsContent value="quotes">
            <QuotesTab rfqId={rfq.id} currency={rfq.currency} canEdit={canEdit()} />
          </TabsContent>

          {/* Invoices Tab */}
          <TabsContent value="invoices">
            <RFQInvoiceTracking rfqId={rfq.id} rfqTitle={rfq.title} />
          </TabsContent>

          {/* Metrics Tab */}
          <TabsContent value="metrics">
            <RFQPerformanceMetrics rfqId={rfq.id} />
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history">
            <HistoryTab rfqId={rfq.id} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default RFQDetail;