import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CheckCircle, AlertCircle, RefreshCw, ArrowLeft } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useToast } from '../hooks/use-toast';
import { AuthFormContainer } from '../components/auth/AuthFormContainer';

type VerificationState = 'verifying' | 'success' | 'error' | 'expired';

export const VerifyEmail: React.FC = () => {
  const [verificationState, setVerificationState] = useState<VerificationState>('verifying');
  const [isResending, setIsResending] = useState(false);
  const [userEmail, setUserEmail] = useState<string>('');
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { verifyEmail, resendVerification, error } = useAuth();
  const { toast } = useToast();

  const token = searchParams.get('token');

  useEffect(() => {
    const performVerification = async () => {
      if (!token) {
        setVerificationState('error');
        toast({
          title: 'Invalid verification link',
          description: 'The verification link is invalid or missing.',
          variant: 'destructive',
        });
        return;
      }

      try {
        const result = await verifyEmail(token);
        setUserEmail(result.payload?.email || '');
        setVerificationState('success');
        toast({
          title: 'Email verified successfully!',
          description: 'Your account has been activated. You can now sign in.',
        });
      } catch (err) {
        const errorMessage = error || 'Verification failed';
        if (errorMessage.includes('expired')) {
          setVerificationState('expired');
        } else {
          setVerificationState('error');
        }
        toast({
          title: 'Verification failed',
          description: errorMessage,
          variant: 'destructive',
        });
      }
    };

    performVerification();
  }, [token, verifyEmail, error, toast]);

  const handleResendVerification = async () => {
    if (!userEmail) {
      toast({
        title: 'Error',
        description: 'Email address not found. Please try registering again.',
        variant: 'destructive',
      });
      return;
    }

    setIsResending(true);

    try {
      await resendVerification(userEmail);
      toast({
        title: 'Verification email sent!',
        description: 'Please check your inbox for the new verification link.',
      });
    } catch (err) {
      toast({
        title: 'Failed to resend email',
        description: error || 'Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setIsResending(false);
    }
  };

  const handleSignInClick = () => {
    navigate('/login');
  };

  // Success State
  if (verificationState === 'success') {
    return (
      <AuthFormContainer
        title="Email Verified!"
        subtitle="Your account has been successfully activated"
      >
        <div className="text-center space-y-6">
          {/* Success Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-success/10 rounded-full"
          >
            <CheckCircle className="w-10 h-10 text-success" />
          </motion.div>

          {/* Success Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-3"
          >
            <h2 className="text-xl font-semibold text-foreground">
              Verification Complete
            </h2>
            <p className="text-muted-foreground">
              Your email has been verified successfully. You can now access all features of VendorMS.
            </p>
          </motion.div>

          {/* Sign In Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleSignInClick}
              className="btn-neumorphic gradient-primary text-primary-foreground font-medium px-8 py-3"
            >
              Sign In to Your Account
            </motion.button>
          </motion.div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="pt-6 border-t border-border"
          >
            <p className="text-sm text-muted-foreground">
              Welcome to VendorMS! Start managing your vendors efficiently.
            </p>
          </motion.div>
        </div>
      </AuthFormContainer>
    );
  }

  // Expired State
  if (verificationState === 'expired') {
    return (
      <AuthFormContainer
        title="Verification Link Expired"
        subtitle="This verification link has expired"
      >
        <div className="text-center space-y-6">
          {/* Warning Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-warning/10 rounded-full"
          >
            <AlertCircle className="w-10 h-10 text-warning" />
          </motion.div>

          {/* Expired Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-3"
          >
            <h2 className="text-xl font-semibold text-foreground">
              Link Expired
            </h2>
            <p className="text-muted-foreground">
              This verification link has expired. Verification links are valid for 24 hours.
            </p>
          </motion.div>

          {/* Resend Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            <p className="text-sm text-muted-foreground">
              We can send you a new verification link
            </p>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleResendVerification}
              disabled={isResending || !userEmail}
              className="btn-neumorphic gradient-primary text-primary-foreground font-medium px-6 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isResending ? (
                <div className="flex items-center space-x-2">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Sending...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <RefreshCw className="w-4 h-4" />
                  <span>Send New Link</span>
                </div>
              )}
            </motion.button>
          </motion.div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="pt-6 border-t border-border"
          >
            <Link
              to="/login"
              className="inline-flex items-center space-x-2 text-primary hover:text-primary-dark transition-colors font-medium"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Sign In</span>
            </Link>
          </motion.div>
        </div>
      </AuthFormContainer>
    );
  }

  // Error State
  if (verificationState === 'error') {
    return (
      <AuthFormContainer
        title="Verification Failed"
        subtitle="Unable to verify your email address"
      >
        <div className="text-center space-y-6">
          {/* Error Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-destructive/10 rounded-full"
          >
            <AlertCircle className="w-10 h-10 text-destructive" />
          </motion.div>

          {/* Error Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-3"
          >
            <h2 className="text-xl font-semibold text-foreground">
              Verification Failed
            </h2>
            <p className="text-muted-foreground">
              We couldn't verify your email address. The link may be invalid or already used.
            </p>
          </motion.div>

          {/* Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            <div className="space-y-2">
              <Link
                to="/register"
                className="btn-neumorphic gradient-primary text-primary-foreground font-medium px-6 py-3 inline-block"
              >
                Try Registering Again
              </Link>
            </div>
          </motion.div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="pt-6 border-t border-border space-y-2"
          >
            <Link
              to="/login"
              className="inline-flex items-center space-x-2 text-primary hover:text-primary-dark transition-colors font-medium"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Sign In</span>
            </Link>
            <p className="text-sm text-muted-foreground">
              Need help?{' '}
              <Link
                to="/support"
                className="text-primary hover:text-primary-dark transition-colors font-medium"
              >
                Contact Support
              </Link>
            </p>
          </motion.div>
        </div>
      </AuthFormContainer>
    );
  }

  // Loading State (verifying)
  return (
    <AuthFormContainer
      title="Verifying Email"
      subtitle="Please wait while we verify your email address"
    >
      <div className="text-center space-y-6">
        {/* Loading Animation */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
          className="inline-flex items-center justify-center w-20 h-20 bg-primary/10 rounded-full"
        >
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </motion.div>

        {/* Loading Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-3"
        >
          <h2 className="text-xl font-semibold text-foreground">
            Verifying Your Email
          </h2>
          <p className="text-muted-foreground">
            Please wait while we verify your email address...
          </p>
        </motion.div>
      </div>
    </AuthFormContainer>
  );
};