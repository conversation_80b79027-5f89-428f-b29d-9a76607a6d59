import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Mail, Lock, Eye, EyeOff, User, Building, AlertCircle, Check } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useToast } from '../hooks/use-toast';
import { AuthFormContainer } from '../components/auth/AuthFormContainer';
import { PasswordStrengthIndicator } from '../components/auth/PasswordStrengthIndicator';
import { registerSchema, RegisterFormData } from '../lib/authValidation';

export const Register: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const { register: registerUser, isLoading, error } = useAuth();
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: yupResolver(registerSchema),
  });

  const watchedPassword = watch('password', '');

  const onSubmit = async (data: RegisterFormData) => {
    try {
      await registerUser(data);
      toast({
        title: 'Registration successful!',
        description: 'Please check your email to verify your account.',
      });
      navigate('/verification-pending', { state: { email: data.email } });
    } catch (err) {
      toast({
        title: 'Registration failed',
        description: error || 'Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <AuthFormContainer
      title="Create Account"
      subtitle="Join VendorMS to manage your vendors"
    >
      {/* Error Alert */}
      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-6 p-3 bg-destructive/10 border border-destructive/20 rounded-xl flex items-center space-x-2"
        >
          <AlertCircle className="w-4 h-4 text-destructive" />
          <span className="text-sm text-destructive">{error}</span>
        </motion.div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Email Field */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Email Address
          </label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              {...register('email')}
              type="email"
              className={`input-neumorphic w-full pl-10 pr-4 ${
                errors.email ? 'border-destructive' : ''
              }`}
              placeholder="Enter your email"
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-destructive">{errors.email.message}</p>
          )}
        </div>

        {/* Full Name Field */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Full Name
          </label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              {...register('fullName')}
              type="text"
              className={`input-neumorphic w-full pl-10 pr-4 ${
                errors.fullName ? 'border-destructive' : ''
              }`}
              placeholder="Enter your full name"
            />
          </div>
          {errors.fullName && (
            <p className="mt-1 text-sm text-destructive">{errors.fullName.message}</p>
          )}
        </div>

        {/* Organization Field */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Organization
          </label>
          <div className="relative">
            <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              {...register('organization')}
              type="text"
              className={`input-neumorphic w-full pl-10 pr-4 ${
                errors.organization ? 'border-destructive' : ''
              }`}
              placeholder="Enter your organization"
            />
          </div>
          {errors.organization && (
            <p className="mt-1 text-sm text-destructive">{errors.organization.message}</p>
          )}
        </div>

        {/* Password Field */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Password
          </label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              {...register('password')}
              type={showPassword ? 'text' : 'password'}
              className={`input-neumorphic w-full pl-10 pr-12 ${
                errors.password ? 'border-destructive' : ''
              }`}
              placeholder="Create a password"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
            >
              {showPassword ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-destructive">{errors.password.message}</p>
          )}
          <PasswordStrengthIndicator password={watchedPassword} />
        </div>

        {/* Confirm Password Field */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Confirm Password
          </label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              {...register('confirmPassword')}
              type={showConfirmPassword ? 'text' : 'password'}
              className={`input-neumorphic w-full pl-10 pr-12 ${
                errors.confirmPassword ? 'border-destructive' : ''
              }`}
              placeholder="Confirm your password"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
            >
              {showConfirmPassword ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="mt-1 text-sm text-destructive">{errors.confirmPassword.message}</p>
          )}
        </div>

        {/* Terms of Service */}
        <div>
          <label className="flex items-start space-x-3">
            <input
              {...register('acceptTerms')}
              type="checkbox"
              className="w-4 h-4 text-primary border-border rounded focus:ring-primary focus:ring-2 mt-0.5"
            />
            <span className="text-sm text-muted-foreground">
              I agree to the{' '}
              <Link
                to="/terms"
                className="text-primary hover:text-primary-dark transition-colors font-medium"
              >
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link
                to="/privacy"
                className="text-primary hover:text-primary-dark transition-colors font-medium"
              >
                Privacy Policy
              </Link>
            </span>
          </label>
          {errors.acceptTerms && (
            <p className="mt-1 text-sm text-destructive">{errors.acceptTerms.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          type="submit"
          disabled={isLoading}
          className="w-full btn-neumorphic gradient-primary text-primary-foreground font-medium py-3 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground"></div>
              <span>Creating account...</span>
            </div>
          ) : (
            <div className="flex items-center justify-center space-x-2">
              <Check className="w-4 h-4" />
              <span>Create Account</span>
            </div>
          )}
        </motion.button>
      </form>

      {/* Footer */}
      <div className="mt-8 text-center">
        <p className="text-sm text-muted-foreground">
          Already have an account?{' '}
          <Link
            to="/login"
            className="text-primary hover:text-primary-dark transition-colors font-medium"
          >
            Sign in
          </Link>
        </p>
      </div>
    </AuthFormContainer>
  );
};