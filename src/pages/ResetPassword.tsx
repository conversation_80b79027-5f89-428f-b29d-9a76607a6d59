import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Lock, Eye, EyeOff, CheckCircle, AlertCircle, ArrowLeft } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useToast } from '../hooks/use-toast';
import { AuthFormContainer } from '../components/auth/AuthFormContainer';
import { PasswordStrengthIndicator } from '../components/auth/PasswordStrengthIndicator';
import { resetPasswordSchema, ResetPasswordFormData } from '../lib/authValidation';

export const ResetPassword: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [tokenValid, setTokenValid] = useState<boolean | null>(null);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { resetPassword, isLoading, error } = useAuth();
  const { toast } = useToast();

  const token = searchParams.get('token');

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: yupResolver(resetPasswordSchema),
  });

  const watchedPassword = watch('password', '');

  useEffect(() => {
    // Validate token on component mount
    if (!token) {
      setTokenValid(false);
      toast({
        title: 'Invalid reset link',
        description: 'The password reset link is invalid or missing.',
        variant: 'destructive',
      });
    } else {
      // In a real app, you'd validate the token with the server
      // For now, we'll assume it's valid if it exists
      setTokenValid(true);
    }
  }, [token, toast]);

  const onSubmit = async (data: ResetPasswordFormData) => {
    if (!token) {
      toast({
        title: 'Invalid token',
        description: 'The reset token is missing or invalid.',
        variant: 'destructive',
      });
      return;
    }

    try {
      await resetPassword({ ...data, token });
      toast({
        title: 'Password reset successful!',
        description: 'You can now sign in with your new password.',
      });
      navigate('/login');
    } catch (err) {
      toast({
        title: 'Password reset failed',
        description: error || 'Please try again or request a new reset link.',
        variant: 'destructive',
      });
    }
  };

  // Show error state for invalid token
  if (tokenValid === false) {
    return (
      <AuthFormContainer
        title="Invalid Reset Link"
        subtitle="This password reset link is invalid or expired"
      >
        <div className="text-center space-y-6">
          {/* Error Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-destructive/10 rounded-full"
          >
            <AlertCircle className="w-10 h-10 text-destructive" />
          </motion.div>

          {/* Error Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-3"
          >
            <h2 className="text-xl font-semibold text-foreground">
              Link Expired or Invalid
            </h2>
            <p className="text-muted-foreground">
              This password reset link has expired or is invalid. Password reset links are only valid for 1 hour.
            </p>
          </motion.div>

          {/* Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            <Link
              to="/forgot-password"
              className="btn-neumorphic gradient-primary text-primary-foreground font-medium px-6 py-3 inline-block"
            >
              Request New Reset Link
            </Link>
          </motion.div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="pt-6 border-t border-border"
          >
            <Link
              to="/login"
              className="inline-flex items-center space-x-2 text-primary hover:text-primary-dark transition-colors font-medium"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Sign In</span>
            </Link>
          </motion.div>
        </div>
      </AuthFormContainer>
    );
  }

  // Show loading state while validating token
  if (tokenValid === null) {
    return (
      <AuthFormContainer
        title="Validating Reset Link"
        subtitle="Please wait while we validate your reset link"
      >
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground mt-4">Validating...</p>
        </div>
      </AuthFormContainer>
    );
  }

  return (
    <AuthFormContainer
      title="Reset Password"
      subtitle="Create a new password for your account"
    >
      {/* Error Alert */}
      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-6 p-3 bg-destructive/10 border border-destructive/20 rounded-xl flex items-center space-x-2"
        >
          <AlertCircle className="w-4 h-4 text-destructive" />
          <span className="text-sm text-destructive">{error}</span>
        </motion.div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Password Field */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            New Password
          </label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              {...register('password')}
              type={showPassword ? 'text' : 'password'}
              className={`input-neumorphic w-full pl-10 pr-12 ${
                errors.password ? 'border-destructive' : ''
              }`}
              placeholder="Create a new password"
              autoFocus
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
            >
              {showPassword ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-destructive">{errors.password.message}</p>
          )}
          <PasswordStrengthIndicator password={watchedPassword} />
        </div>

        {/* Confirm Password Field */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Confirm New Password
          </label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              {...register('confirmPassword')}
              type={showConfirmPassword ? 'text' : 'password'}
              className={`input-neumorphic w-full pl-10 pr-12 ${
                errors.confirmPassword ? 'border-destructive' : ''
              }`}
              placeholder="Confirm your new password"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
            >
              {showConfirmPassword ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="mt-1 text-sm text-destructive">{errors.confirmPassword.message}</p>
          )}
        </div>

        {/* Info Box */}
        <div className="p-4 bg-info/5 border border-info/20 rounded-xl">
          <p className="text-sm text-muted-foreground">
            Choose a strong password that you haven't used before. After resetting, you'll be signed out of all devices.
          </p>
        </div>

        {/* Submit Button */}
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          type="submit"
          disabled={isLoading}
          className="w-full btn-neumorphic gradient-primary text-primary-foreground font-medium py-3 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground"></div>
              <span>Resetting password...</span>
            </div>
          ) : (
            <div className="flex items-center justify-center space-x-2">
              <CheckCircle className="w-4 h-4" />
              <span>Reset Password</span>
            </div>
          )}
        </motion.button>
      </form>

      {/* Footer */}
      <div className="mt-8 text-center">
        <Link
          to="/login"
          className="inline-flex items-center space-x-2 text-primary hover:text-primary-dark transition-colors font-medium"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Sign In</span>
        </Link>
      </div>
    </AuthFormContainer>
  );
};