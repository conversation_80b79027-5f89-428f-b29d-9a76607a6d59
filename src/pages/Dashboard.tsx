import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { 
  Building2, 
  FileText, 
  Receipt, 
  TrendingUp, 
  Users, 
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { RootState, AppDispatch } from '../store';
import { fetchVendorsAsync } from '../store/slices/vendorsSlice';
import { fetchContractsAsync } from '../store/slices/contractsSlice';
import { fetchInvoicesAsync } from '../store/slices/invoicesSlice';
import { useAuth } from '../hooks/useAuth';
import AIRecommendationWidget from '../components/dashboard/AIRecommendationWidget';
import RecommendationHistoryWidget from '../components/dashboard/RecommendationHistoryWidget';

interface StatCardProps {
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'increase' | 'decrease';
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  change, 
  changeType, 
  icon: Icon, 
  color 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02 }}
      className="card-neumorphic p-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold text-foreground mt-2">{value}</p>
          {change && (
            <div className="flex items-center mt-2">
              {changeType === 'increase' ? (
                <ArrowUp className="w-4 h-4 text-success mr-1" />
              ) : (
                <ArrowDown className="w-4 h-4 text-destructive mr-1" />
              )}
              <span className={`text-sm font-medium ${
                changeType === 'increase' ? 'text-success' : 'text-destructive'
              }`}>
                {change}
              </span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-xl ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </motion.div>
  );
};

interface RecentActivityItem {
  id: string;
  type: 'vendor' | 'contract' | 'invoice';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'warning' | 'info' | 'error';
}

const recentActivities: RecentActivityItem[] = [
  {
    id: '1',
    type: 'vendor',
    title: 'New Vendor Onboarded',
    description: 'TechCorp Solutions completed onboarding process',
    timestamp: '2 hours ago',
    status: 'success',
  },
  {
    id: '2',
    type: 'contract',
    title: 'Contract Renewal Due',
    description: 'Software Development Agreement expires in 30 days',
    timestamp: '4 hours ago',
    status: 'warning',
  },
  {
    id: '3',
    type: 'invoice',
    title: 'Invoice Approved',
    description: 'Invoice #INV-2024-001 approved for payment',
    timestamp: '6 hours ago',
    status: 'success',
  },
  {
    id: '4',
    type: 'vendor',
    title: 'Performance Score Updated',
    description: 'Global Manufacturing Inc score increased to 87.3',
    timestamp: '1 day ago',
    status: 'info',
  },
];

export const Dashboard: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useAuth();
  const { vendors } = useSelector((state: RootState) => state.vendors);
  const { contracts } = useSelector((state: RootState) => state.contracts);
  const { invoices } = useSelector((state: RootState) => state.invoices);

  useEffect(() => {
    dispatch(fetchVendorsAsync({}));
    dispatch(fetchContractsAsync());
    dispatch(fetchInvoicesAsync());
  }, [dispatch]);

  const stats = [
    {
      title: 'Total Vendors',
      value: vendors.length,
      change: '+12%',
      changeType: 'increase' as const,
      icon: Building2,
      color: 'bg-primary',
    },
    {
      title: 'Active Contracts',
      value: contracts.filter(c => c.status === 'active').length,
      change: '+8%',
      changeType: 'increase' as const,
      icon: FileText,
      color: 'bg-success',
    },
    {
      title: 'Pending Invoices',
      value: invoices.filter(i => i.status === 'sent').length,
      change: '-5%',
      changeType: 'decrease' as const,
      icon: Receipt,
      color: 'bg-warning',
    },
    {
      title: 'Total Spend',
      value: '$1.2M',
      change: '+15%',
      changeType: 'increase' as const,
      icon: DollarSign,
      color: 'bg-info',
    },
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'vendor':
        return Building2;
      case 'contract':
        return FileText;
      case 'invoice':
        return Receipt;
      default:
        return Building2;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-success bg-success/10';
      case 'warning':
        return 'text-warning bg-warning/10';
      case 'error':
        return 'text-destructive bg-destructive/10';
      default:
        return 'text-info bg-info/10';
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-foreground">
          Welcome back, {user?.email?.split('@')[0]}!
        </h1>
        <p className="text-muted-foreground mt-2">
          Here's what's happening with your vendor management today.
        </p>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <StatCard {...stat} />
          </motion.div>
        ))}
      </div>

      {/* AI Recommendations Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <AIRecommendationWidget />
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <RecommendationHistoryWidget />
        </motion.div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
          className="lg:col-span-2"
        >
          <div className="card-neumorphic p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-foreground">Recent Activity</h2>
              <button className="text-sm text-primary hover:text-primary-dark transition-colors">
                View All
              </button>
            </div>
            
            <div className="space-y-4">
              {recentActivities.map((activity, index) => {
                const Icon = getActivityIcon(activity.type);
                return (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.7 + index * 0.1 }}
                    className="flex items-start space-x-4 p-4 rounded-xl hover:bg-accent/50 transition-colors cursor-pointer"
                  >
                    <div className={`p-2 rounded-lg ${getStatusColor(activity.status)}`}>
                      <Icon className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-foreground">{activity.title}</h3>
                      <p className="text-sm text-muted-foreground mt-1">{activity.description}</p>
                      <p className="text-xs text-muted-foreground mt-2">{activity.timestamp}</p>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </motion.div>

        {/* Quick Actions & Alerts */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.8 }}
          className="space-y-6"
        >
          {/* Quick Actions */}
          <div className="card-neumorphic p-6">
            <h2 className="text-xl font-semibold text-foreground mb-4">Quick Actions</h2>
            <div className="space-y-3">
              <button className="w-full btn-neumorphic text-left p-3 hover:scale-105 transition-transform">
                <div className="flex items-center space-x-3">
                  <Building2 className="w-5 h-5 text-primary" />
                  <span>Add New Vendor</span>
                </div>
              </button>
              <button className="w-full btn-neumorphic text-left p-3 hover:scale-105 transition-transform">
                <div className="flex items-center space-x-3">
                  <FileText className="w-5 h-5 text-success" />
                  <span>Create Contract</span>
                </div>
              </button>
              <button className="w-full btn-neumorphic text-left p-3 hover:scale-105 transition-transform">
                <div className="flex items-center space-x-3">
                  <Receipt className="w-5 h-5 text-warning" />
                  <span>Generate Invoice</span>
                </div>
              </button>
            </div>
          </div>

          {/* Alerts */}
          <div className="card-neumorphic p-6">
            <h2 className="text-xl font-semibold text-foreground mb-4">Alerts</h2>
            <div className="space-y-3">
              <div className="flex items-start space-x-3 p-3 bg-warning/10 border border-warning/20 rounded-xl">
                <AlertTriangle className="w-5 h-5 text-warning mt-0.5" />
                <div>
                  <p className="font-medium text-warning">3 Contracts Expiring</p>
                  <p className="text-sm text-muted-foreground">Review and renew soon</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3 p-3 bg-info/10 border border-info/20 rounded-xl">
                <Clock className="w-5 h-5 text-info mt-0.5" />
                <div>
                  <p className="font-medium text-info">5 Pending Approvals</p>
                  <p className="text-sm text-muted-foreground">Invoices awaiting review</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3 p-3 bg-success/10 border border-success/20 rounded-xl">
                <CheckCircle className="w-5 h-5 text-success mt-0.5" />
                <div>
                  <p className="font-medium text-success">All Systems Operational</p>
                  <p className="text-sm text-muted-foreground">No critical issues</p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};