import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, Building2, Loader2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AccountForm } from '../components/accounts';
import { useAccountForm } from '../hooks/useAccountForm';
import { useAuth } from '../hooks/useAuth';
import { AccountApi } from '../services/accountApi';
import { Account } from '../types/account';
import { toast } from '../components/ui/sonner';

export const AccountEdit: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { canEdit } = useAuth();
  
  const [account, setAccount] = useState<Account | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const accountId = id ? parseInt(id) : undefined;

  const { handleSubmit, handleCancel, isSubmitting } = useAccountForm({
    mode: 'edit',
    accountId,
    onSuccess: (updatedAccount) => {
      setAccount(updatedAccount);
      toast.success('Account updated successfully!', {
        description: `${updatedAccount.name} has been updated.`,
        action: {
          label: 'View Account',
          onClick: () => navigate(`/accounts/${updatedAccount.id}`),
        },
      });
    },
    redirectTo: `/accounts/${accountId}`,
  });

  // Load account data
  useEffect(() => {
    const loadAccount = async () => {
      if (!accountId) {
        setError('Invalid account ID');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const accountData = await AccountApi.getAccount(accountId);
        setAccount(accountData);
        setError(null);
      } catch (err) {
        console.error('Failed to load account:', err);
        setError(err instanceof Error ? err.message : 'Failed to load account');
        toast.error('Failed to load account', {
          description: 'The account could not be loaded. Please try again.',
        });
      } finally {
        setLoading(false);
      }
    };

    loadAccount();
  }, [accountId]);

  // Check permissions on mount
  useEffect(() => {
    if (!canEdit()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to edit accounts.',
      });
      navigate('/accounts/list');
    }
  }, [canEdit, navigate]);

  // Don't render if user doesn't have permission
  if (!canEdit()) {
    return null;
  }

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="text-muted-foreground">Loading account...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !account) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error || 'Account not found'}
              </AlertDescription>
            </Alert>
            <div className="mt-6">
              <Button onClick={() => navigate('/accounts/list')}>
                Back to Accounts
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="border-b bg-card"
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(`/accounts/${account.id}`)}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Account
              </Button>
              <div className="h-6 w-px bg-border" />
              <div>
                <h1 className="text-2xl font-bold flex items-center gap-2">
                  <Building2 className="h-6 w-6 text-primary" />
                  Edit Account
                </h1>
                <p className="text-muted-foreground">
                  Update information for {account.name}
                </p>
              </div>
            </div>
            
            {/* Account Status */}
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${
                account.status === 'ACTIVE' ? 'bg-green-500' : 'bg-gray-400'
              }`} />
              <span className="text-sm text-muted-foreground">
                {account.status}
              </span>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="container mx-auto px-4 py-8"
      >
        <div className="max-w-4xl mx-auto">
          {/* Form Card */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="bg-card rounded-lg border shadow-sm"
          >
            <div className="p-6">
              <AccountForm
                mode="edit"
                initialData={account}
                onSubmit={handleSubmit}
                isLoading={isSubmitting}
              />
            </div>
          </motion.div>

          {/* Help Text */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="mt-6 text-center text-sm text-muted-foreground"
          >
            <p>
              Changes will be saved to the account record and logged in the audit trail.
            </p>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};