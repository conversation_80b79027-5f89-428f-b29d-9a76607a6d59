import React, { useEffect, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { RootState, AppDispatch } from '@/store';
import { 
  fetchDashboardData, 
  setDashboardFilters,
  clearError,
  setRealtimeConnected 
} from '@/store/slices/analyticsSlice';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ChartContainer from '@/components/analytics/ChartContainer';
import FilterPanel from '@/components/analytics/FilterPanel';
import ExportButton from '@/components/analytics/ExportButton';
import { useToast } from '@/hooks/use-toast';
import { 
  DollarSign, 
  TrendingUp, 
  FileText, 
  Users, 
  AlertTriangle,
  Activity,
  Calendar,
  Zap
} from 'lucide-react';
import { format } from 'date-fns';

const AnalyticsDashboard: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { toast } = useToast();
  
  const { dashboard, realtime } = useSelector((state: RootState) => state.analytics);
  const { user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    dispatch(fetchDashboardData(dashboard.filters));
    
    // Simulate WebSocket connection
    dispatch(setRealtimeConnected(true));
    
    return () => {
      dispatch(setRealtimeConnected(false));
    };
  }, [dispatch, dashboard.filters]);

  // Transform data for charts
  const chartData = useMemo(() => {
    if (!dashboard.data) return {};

    return {
      spendByVendor: dashboard.data.spendAnalysis.spendByVendor.map(item => ({
        name: item.vendor,
        value: item.amount,
      })),
      spendByCategory: dashboard.data.spendAnalysis.spendByCategory.map(item => ({
        name: item.category,
        value: item.amount,
      })),
      spendTrends: dashboard.data.spendAnalysis.spendTrends.map(item => ({
        name: format(new Date(item.date), 'MMM'),
        value: item.amount,
      })),
      performanceTrends: dashboard.data.vendorPerformance.performanceTrends.map(item => ({
        name: format(new Date(item.date), 'MMM'),
        value: item.score,
      })),
      paymentTrends: dashboard.data.invoiceMetrics.paymentTrends.map(item => ({
        name: format(new Date(item.date), 'MMM'),
        value: item.amount,
      })),
    };
  }, [dashboard.data]);

  const filterConfigs = [
    {
      key: 'dateRange',
      label: 'Date Range',
      type: 'daterange' as const,
    },
    {
      key: 'vendors',
      label: 'Vendors',
      type: 'multiselect' as const,
      options: [
        { value: '1', label: 'TechCorp Solutions' },
        { value: '2', label: 'Global Services Ltd' },
        { value: '3', label: 'Innovation Partners' },
        { value: '4', label: 'Digital Solutions Inc' },
        { value: '5', label: 'Enterprise Systems' },
      ],
    },
    {
      key: 'categories',
      label: 'Categories',
      type: 'multiselect' as const,
      options: [
        { value: 'it-services', label: 'IT Services' },
        { value: 'consulting', label: 'Consulting' },
        { value: 'software', label: 'Software Licenses' },
        { value: 'hardware', label: 'Hardware' },
      ],
    },
  ];

  const handleFilterChange = (key: string, value: string[] | { start: string; end: string }) => {
    dispatch(setDashboardFilters({ [key]: value }));
  };

  const handleFilterReset = () => {
    dispatch(setDashboardFilters({
      dateRange: {
        start: new Date(new Date().setMonth(new Date().getMonth() - 6)).toISOString(),
        end: new Date().toISOString(),
      },
      vendors: [],
      categories: [],
    }));
  };

  const handleExport = async (format: string) => {
    // Simulate export
    await new Promise(resolve => setTimeout(resolve, 2000));
    // In real app, this would call an API endpoint
  };

  const handleRefreshData = () => {
    dispatch(fetchDashboardData(dashboard.filters));
  };

  const handleClearError = () => {
    dispatch(clearError('dashboard'));
  };

  if (dashboard.error) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
            <h3 className="text-lg font-semibold mb-2">Error Loading Dashboard</h3>
            <p className="text-muted-foreground mb-4">{dashboard.error}</p>
            <div className="flex space-x-2">
              <Button onClick={handleRefreshData}>Retry</Button>
              <Button variant="outline" onClick={handleClearError}>Dismiss</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold mb-2">Analytics Dashboard</h1>
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center">
              <Activity className={`h-4 w-4 mr-1 ${realtime.connected ? 'text-green-500' : 'text-red-500'}`} />
              {realtime.connected ? 'Live' : 'Offline'}
            </div>
            {dashboard.lastUpdated && (
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Last updated: {format(new Date(dashboard.lastUpdated), 'PPp')}
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <ExportButton
            data={dashboard.data ? Object.values(chartData).flat() : []}
            filename="dashboard-data"
            onExport={handleExport}
            disabled={dashboard.loading}
          />
          <Button onClick={handleRefreshData} disabled={dashboard.loading}>
            <Zap className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters */}
        <div className="lg:col-span-1">
          <FilterPanel
            filters={dashboard.filters}
            onFilterChange={handleFilterChange}
            onReset={handleFilterReset}
            availableFilters={filterConfigs}
          />
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* KPI Cards */}
          {dashboard.data && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4"
            >
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Spend</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${dashboard.data.spendAnalysis.totalSpend.toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    +12.5% from last period
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Performance</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {dashboard.data.vendorPerformance.averageScore}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    +2.1% from last period
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Contracts</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {dashboard.data.contractMetrics.activeContracts}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {dashboard.data.contractMetrics.expiringContracts} expiring soon
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending Invoices</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {dashboard.data.invoiceMetrics.pendingInvoices}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Avg {dashboard.data.invoiceMetrics.averageProcessingTime} days to process
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Charts */}
          <Tabs defaultValue="spending" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="spending">Spending Analysis</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
            </TabsList>

            <TabsContent value="spending" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <ChartContainer
                  title="Spend by Vendor"
                  data={chartData.spendByVendor || []}
                  type="bar"
                  loading={dashboard.loading}
                  onRefresh={handleRefreshData}
                  trend={{ value: 12.5, isPositive: true, label: 'vs last period' }}
                />
                <ChartContainer
                  title="Spend by Category"
                  data={chartData.spendByCategory || []}
                  type="pie"
                  loading={dashboard.loading}
                  onRefresh={handleRefreshData}
                />
              </div>
              <ChartContainer
                title="Spending Trends"
                data={chartData.spendTrends || []}
                type="area"
                loading={dashboard.loading}
                onRefresh={handleRefreshData}
                height={250}
              />
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <ChartContainer
                  title="Performance Trends"
                  data={chartData.performanceTrends || []}
                  type="line"
                  loading={dashboard.loading}
                  onRefresh={handleRefreshData}
                  trend={{ value: 2.1, isPositive: true, label: 'vs last period' }}
                />
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base font-medium">Risk Vendors</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {dashboard.data?.vendorPerformance.riskVendors.map((vendor, index) => (
                      <div key={index} className="flex items-center justify-between py-2">
                        <span className="text-sm">{vendor.vendor}</span>
                        <Badge variant={vendor.riskLevel === 'high' ? 'destructive' : 'secondary'}>
                          {vendor.riskLevel} risk
                        </Badge>
                      </div>
                    )) || <p className="text-sm text-muted-foreground">No risk vendors</p>}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="trends" className="space-y-4">
              <ChartContainer
                title="Payment Trends"
                data={chartData.paymentTrends || []}
                type="area"
                loading={dashboard.loading}
                onRefresh={handleRefreshData}
                height={300}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;