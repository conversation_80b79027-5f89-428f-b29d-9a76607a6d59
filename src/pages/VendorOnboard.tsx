import React from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { ArrowLeft, Building2, Mail, Phone, MapPin, Tag, Award, Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { createVendorAsync, type Vendor } from '@/store/slices/vendorsSlice';
import { AppDispatch } from '@/store';

const schema = yup.object({
  name: yup.string().required('Vendor name is required'),
  contact_email: yup.string().email('Invalid email').required('Email is required'),
  contact_phone: yup.string().required('Phone is required'),
  address: yup.object({
    street: yup.string().required('Street address is required'),
    city: yup.string().required('City is required'),
    state: yup.string().required('State is required'),
    zip: yup.string().required('ZIP code is required'),
    country: yup.string().required('Country is required'),
  }),
  category: yup.string().required('Category is required'),
  certifications: yup.array().of(yup.string()),
  custom_fields: yup.object({
    website: yup.string().url('Invalid website URL').optional(),
    tax_id: yup.string().optional(),
  }),
});

type FormData = yup.InferType<typeof schema>;

const categories = [
  'Technology',
  'Manufacturing',
  'Logistics',
  'Consulting',
  'Marketing',
  'Finance',
  'Legal',
  'Construction',
  'Healthcare',
  'Education',
];

const certificationOptions = [
  'ISO 9001',
  'ISO 14001',
  'SOC 2',
  'OSHA',
  'C-TPAT',
  'IATA',
  'PCI DSS',
  'HIPAA',
  'GDPR',
  'FedRAMP',
];

export default function VendorOnboard() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      contact_email: '',
      contact_phone: '',
      address: {
        street: '',
        city: '',
        state: '',
        zip: '',
        country: 'USA',
      },
      category: '',
      certifications: [],
      custom_fields: {
        website: '',
        tax_id: '',
      },
    },
  });

  const selectedCertifications = watch('certifications') || [];

  const onSubmit = async (data: FormData) => {
    try {
      const vendorData: Omit<Vendor, 'id' | 'created_at' | 'updated_at'> = {
        name: data.name,
        contact_email: data.contact_email,
        contact_phone: data.contact_phone,
        address: data.address,
        category: data.category,
        certifications: data.certifications || [],
        performance_score: 0,
        status: 'active' as const,
        custom_fields: data.custom_fields || {},
      };
      
      await dispatch(createVendorAsync(vendorData)).unwrap();
      navigate('/vendors/list');
    } catch (error) {
      console.error('Failed to create vendor:', error);
    }
  };

  const toggleCertification = (cert: string) => {
    const current = selectedCertifications;
    const updated = current.includes(cert)
      ? current.filter(c => c !== cert)
      : [...current, cert];
    setValue('certifications', updated);
  };

  return (
    <div className="min-h-screen bg-gradient-subtle p-6">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <Button
            variant="ghost"
            onClick={() => navigate('/vendors/list')}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Vendors
          </Button>
          
          <div className="flex items-center gap-3 mb-2">
            <Building2 className="w-8 h-8 text-primary" />
            <h1 className="text-3xl font-bold text-foreground">Onboard New Vendor</h1>
          </div>
          <p className="text-muted-foreground">
            Add a new vendor to your system with all necessary information
          </p>
        </motion.div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="w-5 h-5" />
                  Basic Information
                </CardTitle>
                <CardDescription>
                  Enter the vendor's primary details and contact information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Vendor Name *</Label>
                    <Controller
                      name="name"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="Enter vendor name"
                          className={errors.name ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.name && (
                      <p className="text-sm text-destructive mt-1">{errors.name.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="category">Category *</Label>
                    <Controller
                      name="category"
                      control={control}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger className={errors.category ? 'border-destructive' : ''}>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map((cat) => (
                              <SelectItem key={cat} value={cat}>
                                {cat}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.category && (
                      <p className="text-sm text-destructive mt-1">{errors.category.message}</p>
                    )}
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="contact_email">Email *</Label>
                    <Controller
                      name="contact_email"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="email"
                          placeholder="<EMAIL>"
                          className={errors.contact_email ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.contact_email && (
                      <p className="text-sm text-destructive mt-1">{errors.contact_email.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="contact_phone">Phone *</Label>
                    <Controller
                      name="contact_phone"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="******-0123"
                          className={errors.contact_phone ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.contact_phone && (
                      <p className="text-sm text-destructive mt-1">{errors.contact_phone.message}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Address Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="w-5 h-5" />
                  Address Information
                </CardTitle>
                <CardDescription>
                  Provide the vendor's business address
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="address.street">Street Address *</Label>
                  <Controller
                    name="address.street"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="123 Business Street"
                        className={errors.address?.street ? 'border-destructive' : ''}
                      />
                    )}
                  />
                  {errors.address?.street && (
                    <p className="text-sm text-destructive mt-1">{errors.address.street.message}</p>
                  )}
                </div>

                <div className="grid md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="address.city">City *</Label>
                    <Controller
                      name="address.city"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="San Francisco"
                          className={errors.address?.city ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.address?.city && (
                      <p className="text-sm text-destructive mt-1">{errors.address.city.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="address.state">State *</Label>
                    <Controller
                      name="address.state"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="CA"
                          className={errors.address?.state ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.address?.state && (
                      <p className="text-sm text-destructive mt-1">{errors.address.state.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="address.zip">ZIP Code *</Label>
                    <Controller
                      name="address.zip"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="94105"
                          className={errors.address?.zip ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.address?.zip && (
                      <p className="text-sm text-destructive mt-1">{errors.address.zip.message}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Certifications */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  Certifications
                </CardTitle>
                <CardDescription>
                  Select applicable certifications and standards
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {certificationOptions.map((cert) => (
                    <button
                      key={cert}
                      type="button"
                      onClick={() => toggleCertification(cert)}
                      className={`p-3 rounded-lg border text-sm transition-all ${
                        selectedCertifications.includes(cert)
                          ? 'bg-primary text-primary-foreground border-primary'
                          : 'bg-background border-border hover:border-primary/50'
                      }`}
                    >
                      {cert}
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Additional Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Tag className="w-5 h-5" />
                  Additional Information
                </CardTitle>
                <CardDescription>
                  Optional fields for additional vendor details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="custom_fields.website">Website</Label>
                    <Controller
                      name="custom_fields.website"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="https://vendor-website.com"
                          className={errors.custom_fields?.website ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.custom_fields?.website && (
                      <p className="text-sm text-destructive mt-1">{errors.custom_fields.website.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="custom_fields.tax_id">Tax ID</Label>
                    <Controller
                      name="custom_fields.tax_id"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="*********"
                        />
                      )}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Submit Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="flex justify-end"
          >
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-32"
            >
              {isSubmitting ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
                />
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Create Vendor
                </>
              )}
            </Button>
          </motion.div>
        </form>
      </div>
    </div>
  );
}