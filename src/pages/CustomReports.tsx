import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { RootState, AppDispatch } from '@/store';
import { 
  fetchReportConfigs, 
  generateReport,
  clearError 
} from '@/store/slices/analyticsSlice';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import ExportButton from '@/components/analytics/ExportButton';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus, 
  FileText, 
  Download, 
  Calendar as CalendarIcon,
  Clock,
  Users,
  Settings,
  Play,
  Pause,
  Copy,
  Trash2,
  Eye
} from 'lucide-react';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';

interface ReportFormData {
  name: string;
  description: string;
  dataSource: 'vendors' | 'contracts' | 'invoices' | 'performance';
  columns: string[];
  chartType?: 'bar' | 'line' | 'pie' | 'table';
  dateRange: { start: Date | null; end: Date | null; };
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    recipients: string[];
    enabled: boolean;
  };
}

const CustomReports: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { toast } = useToast();
  
  const { reports } = useSelector((state: RootState) => state.analytics);
  const [activeTab, setActiveTab] = useState('create');
  const [selectedConfig, setSelectedConfig] = useState<string | null>(null);

  const { register, handleSubmit, watch, setValue, reset, formState: { errors } } = useForm<ReportFormData>({
    defaultValues: {
      name: '',
      description: '',
      dataSource: 'vendors',
      columns: [],
      chartType: 'table',
      dateRange: { start: null, end: null },
      schedule: {
        frequency: 'monthly',
        recipients: [],
        enabled: false,
      },
    },
  });

  const watchedDataSource = watch('dataSource');
  const watchedColumns = watch('columns');

  useEffect(() => {
    dispatch(fetchReportConfigs());
  }, [dispatch]);

  const dataSourceOptions = {
    vendors: {
      label: 'Vendors',
      columns: ['name', 'category', 'performance_score', 'status', 'total_spend', 'contract_count'],
    },
    contracts: {
      label: 'Contracts',
      columns: ['title', 'vendor_name', 'status', 'start_date', 'end_date', 'value', 'compliance_score'],
    },
    invoices: {
      label: 'Invoices',
      columns: ['vendor_name', 'amount', 'status', 'due_date', 'paid_date', 'currency'],
    },
    performance: {
      label: 'Performance',
      columns: ['vendor_name', 'score', 'period', 'delivery_score', 'quality_score', 'cost_score'],
    },
  };

  const onSubmit = async (data: ReportFormData) => {
    try {
      // In a real app, this would create a new report config
      toast({
        title: 'Report Configuration Saved',
        description: `Report "${data.name}" has been created successfully.`,
      });
      reset();
      setActiveTab('saved');
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save report configuration.',
        variant: 'destructive',
      });
    }
  };

  const handleGenerateReport = async (configId: string, format: 'pdf' | 'csv') => {
    try {
      await dispatch(generateReport({ configId, format })).unwrap();
      toast({
        title: 'Report Generated',
        description: `Report has been generated and will be downloaded shortly.`,
      });
    } catch (error) {
      toast({
        title: 'Generation Failed',
        description: 'Failed to generate report. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleColumnToggle = (column: string) => {
    const currentColumns = watchedColumns || [];
    const newColumns = currentColumns.includes(column)
      ? currentColumns.filter(c => c !== column)
      : [...currentColumns, column];
    setValue('columns', newColumns);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold mb-2">Custom Reports</h1>
          <p className="text-muted-foreground">
            Create, schedule, and manage custom reports for your vendor data.
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="create">Create Report</TabsTrigger>
          <TabsTrigger value="saved">Saved Reports</TabsTrigger>
          <TabsTrigger value="history">Report History</TabsTrigger>
        </TabsList>

        {/* Create Report Tab */}
        <TabsContent value="create" className="space-y-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Configuration Panel */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Report Name</Label>
                        <Input
                          id="name"
                          {...register('name', { required: 'Report name is required' })}
                          placeholder="Enter report name"
                        />
                        {errors.name && (
                          <p className="text-sm text-destructive">{errors.name.message}</p>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="dataSource">Data Source</Label>
                        <Select
                          value={watchedDataSource}
                          onValueChange={(value) => setValue('dataSource', value as any)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select data source" />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.entries(dataSourceOptions).map(([key, option]) => (
                              <SelectItem key={key} value={key}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        {...register('description')}
                        placeholder="Describe what this report includes..."
                        rows={3}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Column Selection</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {dataSourceOptions[watchedDataSource]?.columns.map((column) => (
                        <div key={column} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={column}
                            checked={watchedColumns?.includes(column) || false}
                            onChange={() => handleColumnToggle(column)}
                            className="rounded border-gray-300"
                          />
                          <Label htmlFor={column} className="text-sm">
                            {column.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Visualization & Scheduling</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Chart Type</Label>
                        <Select
                          value={watch('chartType')}
                          onValueChange={(value) => setValue('chartType', value as any)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select chart type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="table">Data Table</SelectItem>
                            <SelectItem value="bar">Bar Chart</SelectItem>
                            <SelectItem value="line">Line Chart</SelectItem>
                            <SelectItem value="pie">Pie Chart</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label>Schedule Frequency</Label>
                        <Select
                          value={watch('schedule.frequency')}
                          onValueChange={(value) => setValue('schedule.frequency', value as any)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select frequency" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="enableSchedule"
                        checked={watch('schedule.enabled')}
                        onChange={(e) => setValue('schedule.enabled', e.target.checked)}
                        className="rounded border-gray-300"
                      />
                      <Label htmlFor="enableSchedule">Enable automatic scheduling</Label>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Preview Panel */}
              <div className="lg:col-span-1">
                <Card className="sticky top-6">
                  <CardHeader>
                    <CardTitle>Preview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium">Data Source:</p>
                        <p className="text-sm text-muted-foreground">
                          {dataSourceOptions[watchedDataSource]?.label}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Selected Columns:</p>
                        <div className="mt-1 space-y-1">
                          {(watchedColumns || []).map((column) => (
                            <Badge key={column} variant="secondary" className="text-xs mr-1 mb-1">
                              {column.replace(/_/g, ' ')}
                            </Badge>
                          ))}
                          {(!watchedColumns || watchedColumns.length === 0) && (
                            <p className="text-xs text-muted-foreground">No columns selected</p>
                          )}
                        </div>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Visualization:</p>
                        <p className="text-sm text-muted-foreground">
                          {watch('chartType')?.replace(/\b\w/g, l => l.toUpperCase())}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => reset()}>
                Reset
              </Button>
              <Button type="submit" disabled={reports.loading}>
                <Plus className="h-4 w-4 mr-2" />
                Create Report
              </Button>
            </div>
          </form>
        </TabsContent>

        {/* Saved Reports Tab */}
        <TabsContent value="saved" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {reports.configs.map((config) => (
              <motion.div
                key={config.id}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                whileHover={{ scale: 1.02 }}
                className="transition-transform"
              >
                <Card>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-base">{config.name}</CardTitle>
                        <p className="text-sm text-muted-foreground mt-1">
                          {config.description}
                        </p>
                      </div>
                      <Badge variant="outline">
                        {dataSourceOptions[config.dataSource]?.label}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Columns:</span>
                        <span>{config.columns.length}</span>
                      </div>
                      {config.schedule && (
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Schedule:</span>
                          <div className="flex items-center">
                            {config.schedule.enabled ? (
                              <div className="flex items-center text-green-600">
                                <Play className="h-3 w-3 mr-1" />
                                {config.schedule.frequency}
                              </div>
                            ) : (
                              <div className="flex items-center text-muted-foreground">
                                <Pause className="h-3 w-3 mr-1" />
                                Disabled
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                      <Separator />
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleGenerateReport(config.id, 'pdf')}
                          disabled={reports.loading}
                          className="flex-1"
                        >
                          <FileText className="h-3 w-3 mr-1" />
                          PDF
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleGenerateReport(config.id, 'csv')}
                          disabled={reports.loading}
                          className="flex-1"
                        >
                          <Download className="h-3 w-3 mr-1" />
                          CSV
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Report History Tab */}
        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Generated Reports</CardTitle>
            </CardHeader>
            <CardContent>
              {reports.generated.length > 0 ? (
                <div className="space-y-3">
                  {reports.generated.map((report) => (
                    <div key={report.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <FileText className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">
                            {reports.configs.find(c => c.id === report.configId)?.name || 'Unknown Report'}
                          </p>
                          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            <span>{format(report.generatedAt, 'PPp')}</span>
                            <span>•</span>
                            <span>{(report.size / 1024).toFixed(1)} KB</span>
                            <span>•</span>
                            <Badge variant="outline" className="text-xs">
                              {report.format.toUpperCase()}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <Button size="sm" variant="outline">
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Reports Generated</h3>
                  <p className="text-muted-foreground">
                    Generated reports will appear here for download.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CustomReports;