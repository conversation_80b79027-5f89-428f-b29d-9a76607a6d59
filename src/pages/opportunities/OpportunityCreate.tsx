import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { OpportunityForm } from "@/components/opportunities/OpportunityForm";
import { OpportunityCreateRequest } from "@/types/opportunity";
import { Account } from "@/types/account";
import { Contact } from "@/types/contact";
import { AccountService } from "@/services/accountService";
import { ContactService } from "@/services/contactService";
import { opportunityService } from "@/services/opportunityService";
import { usersApi } from "@/services/api/users";
import { toast } from "sonner";

const OpportunityCreate: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const preSelectedAccountId = searchParams.get("accountId")
    ? parseInt(searchParams.get("accountId")!)
    : undefined;

  const [accounts, setAccounts] = useState<Account[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [users, setUsers] = useState<
    { id: number; email: string; name?: string }[]
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const [accountSearchLoading, setAccountSearchLoading] = useState(false);
  const [userSearchLoading, setUserSearchLoading] = useState(false);

  // Fetch initial data for selection
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch accounts with default limit of 10
        const accountsResponse = await AccountService.getAccounts({}, 1, 10);
        setAccounts(accountsResponse.accounts);

        // Fetch contacts with default limit of 10
        const contactsResponse = await ContactService.searchContacts({});
        setContacts(contactsResponse.contacts);

        // Fetch users for owner selection
        const usersResponse = await usersApi.getUsers(1, 50);
        const usersList = usersResponse.data.users.map((user) => ({
          id: user.id,
          email: user.email,
          name:
            user.firstName && user.lastName
              ? `${user.firstName} ${user.lastName}`
              : user.email,
        }));
        setUsers(usersList);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load data. Please try again.");
      }
    };

    fetchData();
  }, []);

  // Search accounts function
  const handleAccountSearch = async (query: string) => {
    if (!query.trim()) {
      // If empty query, fetch default 10 accounts
      try {
        setAccountSearchLoading(true);
        const response = await AccountService.getAccounts({}, 1, 10);
        setAccounts(response.accounts);
      } catch (error) {
        console.error("Error fetching accounts:", error);
      } finally {
        setAccountSearchLoading(false);
      }
      return;
    }

    try {
      setAccountSearchLoading(true);
      const response = await AccountService.searchAccounts(
        { name: query },
        1,
        10
      );
      setAccounts(response.accounts);
    } catch (error) {
      console.error("Error searching accounts:", error);
      toast.error("Failed to search accounts");
    } finally {
      setAccountSearchLoading(false);
    }
  };

  // Search users function
  const handleUserSearch = async (query: string) => {
    if (!query.trim()) {
      // If empty query, fetch default users
      try {
        setUserSearchLoading(true);
        const usersResponse = await usersApi.getUsers(1, 50);
        const usersList = usersResponse.data.users.map((user) => ({
          id: user.id,
          email: user.email,
          name:
            user.firstName && user.lastName
              ? `${user.firstName} ${user.lastName}`
              : user.email,
        }));
        setUsers(usersList);
      } catch (error) {
        console.error("Error fetching users:", error);
      } finally {
        setUserSearchLoading(false);
      }
      return;
    }

    try {
      setUserSearchLoading(true);
      const usersResponse = await usersApi.getUsers(1, 20, { search: query });
      const usersList = usersResponse.data.users.map((user) => ({
        id: user.id,
        email: user.email,
        name:
          user.firstName && user.lastName
            ? `${user.firstName} ${user.lastName}`
            : user.email,
      }));
      setUsers(usersList);
    } catch (error) {
      console.error("Error searching users:", error);
      toast.error("Failed to search users");
    } finally {
      setUserSearchLoading(false);
    }
  };

  const handleSubmit = async (
    data: OpportunityCreateRequest
  ): Promise<void> => {
    setIsLoading(true);
    try {
      await opportunityService.createOpportunity(data);
      toast.success("Opportunity created successfully!");
      navigate("/opportunities");
    } catch (error) {
      console.error("Error creating opportunity:", error);
      toast.error("Failed to create opportunity. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    navigate("/opportunities");
  };

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate("/opportunities")}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Opportunities
        </Button>

        <h1 className="text-3xl font-bold">Create New Opportunity</h1>
        <p className="text-muted-foreground mt-2">
          Fill in the details below to create a new opportunity.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Opportunity Details</CardTitle>
        </CardHeader>
        <CardContent>
          <OpportunityForm
            mode="create"
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={isLoading}
            accountId={preSelectedAccountId}
            availableAccounts={accounts}
            availableContacts={contacts}
            availableUsers={users}
            onAccountSearch={handleAccountSearch}
            onUserSearch={handleUserSearch}
            accountSearchLoading={accountSearchLoading}
            userSearchLoading={userSearchLoading}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default OpportunityCreate;
