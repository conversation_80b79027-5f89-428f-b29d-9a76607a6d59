import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import {
  Plus,
  List,
  LayoutGrid,
  Download,
  Upload,
  BarChart3,
  Filter,
  Search,
  RefreshCw,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "@/components/ui/use-toast";
import { OpportunityForm } from "@/components/opportunities/OpportunityForm";
import { OpportunityList } from "@/components/opportunities/OpportunityList";
import { OpportunityKanban } from "@/components/opportunities/OpportunityKanban";
import { opportunityService } from "@/services/opportunityService";
import {
  Opportunity,
  OpportunityCreateRequest,
  OpportunityUpdateRequest,
  OpportunitySearchFilters,
  PaginatedOpportunities,
  OpportunityStage,
  OpportunityType,
  OpportunityLeadSource,
  ForecastCategory,
  OpportunityStatus,
  OpportunityAnalytics,
  OPPORTUNITY_STAGES,
} from "@/types/opportunity";

// Remove mock data - using real API calls

export const OpportunityManagement: React.FC = () => {
  const [opportunities, setOpportunities] = useState<PaginatedOpportunities>({
    opportunities: [],
    pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
  });
  const [analytics, setAnalytics] = useState<OpportunityAnalytics | null>(null);
  const [selectedOpportunity, setSelectedOpportunity] =
    useState<Opportunity | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [viewMode, setViewMode] = useState<"list" | "kanban">("list");
  const [searchFilters, setSearchFilters] = useState<OpportunitySearchFilters>(
    {}
  );
  const [isLoading, setIsLoading] = useState(false);

  const loadOpportunities = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await opportunityService.getOpportunities(searchFilters);
      setOpportunities(response);
    } catch (error) {
      console.error("Error loading opportunities:", error);
      toast({
        title: "Error",
        description: "Failed to load opportunities",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [searchFilters]);

  const loadAnalytics = useCallback(async () => {
    try {
      const response = await opportunityService.getAnalytics(searchFilters);
      setAnalytics(response);
    } catch (error) {
      console.error("Error loading analytics:", error);
      toast({
        title: "Error",
        description: "Failed to load analytics",
        variant: "destructive",
      });
    }
  }, [searchFilters]);

  // Load opportunities and analytics on component mount and when filters change
  // useEffect(() => {
  //   loadOpportunities();
  //   loadAnalytics();
  // }, [ ]);

  const handleCreateOpportunity = () => {
    setSelectedOpportunity(null);
    setIsEditMode(false);
    setIsFormOpen(true);
  };

  const handleEditOpportunity = (opportunity: Opportunity) => {
    setSelectedOpportunity(opportunity);
    setIsEditMode(true);
    setIsFormOpen(true);
  };

  const handleViewOpportunity = (opportunity: Opportunity) => {
    setSelectedOpportunity(opportunity);
    // TODO: Navigate to opportunity detail page or open view modal
    console.log("View opportunity:", opportunity);
  };

  const handleDeleteOpportunity = async (opportunityId: number) => {
    try {
      await opportunityService.deleteOpportunity(opportunityId);

      toast({
        title: "Success",
        description: "Opportunity deleted successfully",
      });
      loadOpportunities();
      loadAnalytics();
    } catch (error) {
      console.error("Error deleting opportunity:", error);
      toast({
        title: "Error",
        description: "Failed to delete opportunity",
        variant: "destructive",
      });
    }
  };

  const handleOpportunitySubmit = async (
    data: OpportunityCreateRequest | OpportunityUpdateRequest
  ) => {
    try {
      if (isEditMode && selectedOpportunity) {
        await opportunityService.updateOpportunity(
          selectedOpportunity.id,
          data as OpportunityUpdateRequest
        );
        toast({
          title: "Success",
          description: "Opportunity updated successfully",
        });
      } else {
        await opportunityService.createOpportunity(
          data as OpportunityCreateRequest
        );
        toast({
          title: "Success",
          description: "Opportunity created successfully",
        });
      }

      setIsFormOpen(false);
      loadOpportunities();
      loadAnalytics();
    } catch (error) {
      console.error("Error submitting opportunity:", error);
      toast({
        title: "Error",
        description: `Failed to ${
          isEditMode ? "update" : "create"
        } opportunity`,
        variant: "destructive",
      });
    }
  };

  const handleOpportunityMove = async (
    opportunityId: number,
    newStage: OpportunityStage
  ) => {
    try {
      await opportunityService.updateOpportunity(opportunityId, {
        stage: newStage,
      });

      toast({
        title: "Success",
        description: "Opportunity stage updated successfully",
      });
      loadOpportunities();
      loadAnalytics();
    } catch (error) {
      console.error("Error updating opportunity stage:", error);
      toast({
        title: "Error",
        description: "Failed to update opportunity stage",
        variant: "destructive",
      });
    }
  };

  const handleSearch = (filters: OpportunitySearchFilters) => {
    setSearchFilters(filters);
  };

  const handleExport = async () => {
    try {
      // TODO: Replace with actual API call
      // await opportunityService.exportOpportunities(searchFilters);

      toast({
        title: "Success",
        description: "Export started. You will receive an email when ready.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export opportunities",
        variant: "destructive",
      });
    }
  };

  const handleBulkAction = async (action: string, opportunityIds: number[]) => {
    try {
      switch (action) {
        case "delete":
          // TODO: Replace with actual API call
          // await opportunityService.bulkDelete(opportunityIds);
          toast({
            title: "Success",
            description: `${opportunityIds.length} opportunities deleted successfully`,
          });
          break;
        case "export":
          // TODO: Replace with actual API call
          // await opportunityService.exportOpportunities({ ids: opportunityIds });
          toast({
            title: "Success",
            description:
              "Export started. You will receive an email when ready.",
          });
          break;
        default:
          break;
      }

      loadOpportunities();
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${action} opportunities`,
        variant: "destructive",
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Opportunity Management
          </h1>
          <p className="text-muted-foreground">
            Manage your sales opportunities and track pipeline performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={loadOpportunities}
            disabled={isLoading}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button onClick={handleCreateOpportunity}>
                <Plus className="h-4 w-4 mr-2" />
                New Opportunity
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {isEditMode ? "Edit Opportunity" : "Create New Opportunity"}
                </DialogTitle>
                <DialogDescription>
                  {isEditMode
                    ? "Update the opportunity details below."
                    : "Fill in the details to create a new opportunity."}
                </DialogDescription>
              </DialogHeader>
              <OpportunityForm
                initialData={selectedOpportunity}
                onSubmit={handleOpportunitySubmit}
                mode={isEditMode ? "edit" : "create"}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Opportunities
            </CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.total_opportunities || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Active opportunities in pipeline
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Pipeline Value
            </CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics?.total_amount || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Combined value of all opportunities
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Average Deal Size
            </CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics?.average_deal_size || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Mean opportunity value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Win Rate</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.win_rate || 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Percentage of won opportunities
            </p>
          </CardContent>
        </Card>
      </div>

      {/* View Toggle and Content */}
      <Tabs
        value={viewMode}
        onValueChange={(value) => setViewMode(value as "list" | "kanban")}
      >
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="list" className="flex items-center gap-2">
              <List className="h-4 w-4" />
              List View
            </TabsTrigger>
            <TabsTrigger value="kanban" className="flex items-center gap-2">
              <LayoutGrid className="h-4 w-4" />
              Kanban View
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="list" className="space-y-6">
          <OpportunityList
            opportunities={opportunities}
            onOpportunitySelect={handleViewOpportunity}
            onOpportunityEdit={handleEditOpportunity}
            onOpportunityDelete={handleDeleteOpportunity}
            onOpportunityView={handleViewOpportunity}
            onSearch={handleSearch}
            onExport={handleExport}
            onBulkAction={handleBulkAction}
            isLoading={isLoading}
            showActions={true}
            showBulkActions={true}
            showFilters={true}
          />
        </TabsContent>

        <TabsContent value="kanban" className="space-y-6">
          <OpportunityKanban
            opportunities={opportunities.opportunities}
            onOpportunityMove={handleOpportunityMove}
            onOpportunityEdit={handleEditOpportunity}
            onOpportunityDelete={handleDeleteOpportunity}
            onOpportunityView={handleViewOpportunity}
            onOpportunityCreate={(stage) => {
              // Pre-fill the form with the selected stage
              setSelectedOpportunity({ stage } as Opportunity);
              setIsEditMode(false);
              setIsFormOpen(true);
            }}
            onSearch={(searchTerm) => handleSearch({ search: searchTerm })}
            isLoading={isLoading}
            showActions={true}
            showFilters={true}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};
