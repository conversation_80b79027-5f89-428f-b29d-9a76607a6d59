import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Building2,
  Calendar,
  DollarSign,
  User,
  Phone,
  Mail,
  FileText,
  Edit,
  Trash2,
  MoreHorizontal,
  Target,
  TrendingUp,
  Clock,
  Users
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle } from 'lucide-react';
import { opportunityService } from '@/services/opportunityService';
import { Opportunity } from '@/types/opportunity';
import { toast } from '@/components/ui/use-toast';

const OpportunityView: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [opportunity, setOpportunity] = useState<Opportunity | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadOpportunity = async () => {
      if (!id) {
        setError('Opportunity ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const opportunityData = await opportunityService.getOpportunityById(parseInt(id));
        setOpportunity(opportunityData);
      } catch (err) {
        console.error('Error loading opportunity:', err);
        setError('Failed to load opportunity details');
      } finally {
        setLoading(false);
      }
    };

    loadOpportunity();
  }, [id]);

  const handleEdit = () => {
    navigate(`/opportunities/${id}/edit`);
  };

  const handleDelete = async () => {
    if (!opportunity || !window.confirm('Are you sure you want to delete this opportunity?')) {
      return;
    }

    try {
      await opportunityService.deleteOpportunity(opportunity.id);
      toast({
        title: 'Success',
        description: 'Opportunity deleted successfully'
      });
      navigate('/opportunities');
    } catch (error) {
      console.error('Error deleting opportunity:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete opportunity',
        variant: 'destructive'
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStageColor = (stage: string) => {
    const stageColors: Record<string, string> = {
      'Prospecting': 'bg-gray-500',
      'Qualification': 'bg-blue-500',
      'Needs Analysis': 'bg-yellow-500',
      'Value Proposition': 'bg-orange-500',
      'Id. Decision Makers': 'bg-purple-500',
      'Perception Analysis': 'bg-pink-500',
      'Proposal/Price Quote': 'bg-indigo-500',
      'Negotiation/Review': 'bg-cyan-500',
      'Closed Won': 'bg-green-500',
      'Closed Lost': 'bg-red-500'
    };
    return stageColors[stage] || 'bg-gray-500';
  };

  const getProbabilityColor = (probability: number) => {
    if (probability >= 80) return 'text-green-600';
    if (probability >= 60) return 'text-yellow-600';
    if (probability >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="text-muted-foreground">Loading opportunity...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !opportunity) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-7xl mx-auto">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error || 'Opportunity not found'}
              </AlertDescription>
            </Alert>
            <div className="mt-6">
              <Button onClick={() => navigate('/opportunities')}>Back to Opportunities</Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-card border-b"
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/opportunities')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Opportunities
              </Button>
              <div className="h-6 w-px bg-border" />
              <div>
                <h1 className="text-2xl font-bold flex items-center gap-2">
                  <Target className="h-6 w-6 text-primary" />
                  {opportunity.name}
                </h1>
                <p className="text-muted-foreground">
                  {opportunity.account_name && `${opportunity.account_name} • `}
                  {opportunity.type || 'No type specified'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Stage Badge */}
              <Badge variant="outline" className="gap-2">
                <div className={`w-2 h-2 rounded-full ${getStageColor(opportunity.stage)}`} />
                {opportunity.stage}
              </Badge>

              {/* Status Badge */}
              <Badge variant={opportunity.status === 'open' ? 'default' : 'secondary'}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  opportunity.status === 'open' ? 'bg-green-500' : 'bg-gray-400'
                }`} />
                {opportunity.status}
              </Badge>

              {/* Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Opportunity
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleDelete}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Opportunity
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Overview Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Opportunity Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    {opportunity.amount && (
                      <div className="flex items-center gap-3">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Amount</p>
                          <p className="text-2xl font-bold">{formatCurrency(opportunity.amount)}</p>
                        </div>
                      </div>
                    )}
                    
                    {opportunity.probability !== undefined && (
                      <div className="flex items-center gap-3">
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Probability</p>
                          <p className={`text-lg font-semibold ${getProbabilityColor(opportunity.probability)}`}>
                            {opportunity.probability}%
                          </p>
                        </div>
                      </div>
                    )}
                    
                    {opportunity.close_date && (
                      <div className="flex items-center gap-3">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Close Date</p>
                          <p className="font-medium">{formatDate(opportunity.close_date)}</p>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-4">
                    {opportunity.lead_source && (
                      <div className="flex items-center gap-3">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Lead Source</p>
                          <p className="font-medium">{opportunity.lead_source}</p>
                        </div>
                      </div>
                    )}
                    
                    {opportunity.forecast_category && (
                      <div className="flex items-center gap-3">
                        <Target className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Forecast Category</p>
                          <p className="font-medium">{opportunity.forecast_category}</p>
                        </div>
                      </div>
                    )}
                    
                    {opportunity.owner_email && (
                      <div className="flex items-center gap-3">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Owner</p>
                          <p className="font-medium">{opportunity.owner_email}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {opportunity.description && (
                  <>
                    <Separator className="my-6" />
                    <div>
                      <p className="text-sm text-muted-foreground mb-2">Description</p>
                      <p className="text-sm leading-relaxed">{opportunity.description}</p>
                    </div>
                  </>
                )}

                {opportunity.next_step && (
                  <>
                    <Separator className="my-6" />
                    <div>
                      <p className="text-sm text-muted-foreground mb-2">Next Step</p>
                      <p className="text-sm leading-relaxed">{opportunity.next_step}</p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Contact Roles */}
            {opportunity.contact_roles && opportunity.contact_roles.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Contact Roles
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {opportunity.contact_roles.map((contactRole, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">{contactRole.contact_name || 'Unknown Contact'}</p>
                            <p className="text-sm text-muted-foreground">{contactRole.contact_email}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{contactRole.role}</Badge>
                          {contactRole.is_primary && (
                            <Badge variant="default">Primary</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Line Items */}
            {opportunity.line_items && opportunity.line_items.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Line Items
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {opportunity.line_items.map((item, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{item.product_name}</h4>
                          <p className="font-semibold">{formatCurrency(item.total_price)}</p>
                        </div>
                        {item.description && (
                          <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
                        )}
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>Qty: {item.quantity}</span>
                          <span>Unit Price: {formatCurrency(item.unit_price)}</span>
                          {item.discount_percent && (
                            <span>Discount: {item.discount_percent}%</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Account & Meta */}
          <div className="space-y-6">
            {/* Account Information */}
            {opportunity.account_name && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Account Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Account</p>
                      <button
                        onClick={() => navigate(`/accounts/${opportunity.account_id}`)}
                        className="font-medium hover:text-primary cursor-pointer"
                      >
                        {opportunity.account_name}
                      </button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* System Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  System Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground">Created</p>
                  <p className="font-medium">{formatDate(opportunity.created_at)}</p>
                </div>
                
                <div>
                  <p className="text-sm text-muted-foreground">Last Updated</p>
                  <p className="font-medium">{formatDate(opportunity.updated_at)}</p>
                </div>

                {opportunity.days_to_close !== undefined && (
                  <div>
                    <p className="text-sm text-muted-foreground">Days to Close</p>
                    <p className="font-medium">{opportunity.days_to_close} days</p>
                  </div>
                )}

                {opportunity.stage_duration !== undefined && (
                  <div>
                    <p className="text-sm text-muted-foreground">Stage Duration</p>
                    <p className="font-medium">{opportunity.stage_duration} days</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start" onClick={handleEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Opportunity
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start" 
                  onClick={() => navigate(`/accounts/${opportunity.account_id}`)}
                  disabled={!opportunity.account_id}
                >
                  <Building2 className="h-4 w-4 mr-2" />
                  View Account
                </Button>
                <Button variant="outline" className="w-full justify-start" disabled>
                  <FileText className="h-4 w-4 mr-2" />
                  View Related Activities
                </Button>
                <p className="text-xs text-muted-foreground mt-2">
                  Additional features coming soon
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OpportunityView;