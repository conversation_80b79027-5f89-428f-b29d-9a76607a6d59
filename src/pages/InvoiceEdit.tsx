import * as React from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { fetchInvoiceByIdAsync, updateInvoiceAsync, selectInvoiceById } from '@/store/slices/invoicesSlice';
import { fetchVendorsAsync, selectVendors } from '@/store/slices/vendorsSlice'; // Assume selectVendors added similarly
import { fetchContractsAsync, selectContracts } from '@/store/slices/contractsSlice'; // Assume selectContracts
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LineItemsTable } from '@/components/LineItemsTable';
import { InvoiceTotals } from '@/components/InvoiceTotals';
import { toast } from '@/components/ui/sonner';
import { ArrowLeft } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { LineItem } from '@/store/slices/invoicesSlice';

const formSchema = z.object({
  vendor_id: z.string().min(1, 'Vendor is required'),
  contract_id: z.string().optional(),
  due_date: z.string().min(1, 'Due date is required'),
  currency: z.string().default('USD'),
  lineItems: z.array(z.object({
    id: z.string(),
    description: z.string().min(1),
    quantity: z.number().min(0),
    unitPrice: z.number().min(0),
    totalPrice: z.number().min(0),
    taxable: z.boolean(),
  })),
  discount: z.number().min(0).max(100),
  taxRate: z.number().min(0).max(100),
});

type FormValues = z.infer<typeof formSchema>;

const InvoiceEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>(); // Corrected
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const invoice = useSelector((state: RootState) => selectInvoiceById(state, id || ''));
  const vendors = useSelector((state: RootState) => state.vendors.vendors);
  const contracts = useSelector((state: RootState) => state.contracts.contracts);
  const status = useSelector((state: RootState) => state.invoices.status);

  const { control, handleSubmit, watch, setValue, reset } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      vendor_id: '',
      contract_id: '',
      due_date: '',
      currency: 'USD',
      lineItems: [],
      discount: 0,
      taxRate: 0,
    },
  });

  React.useEffect(() => {
    if (id && !isNaN(Number(id))) {
      dispatch(fetchInvoiceByIdAsync(Number(id)));
    }
    dispatch(fetchVendorsAsync({})); // Provide empty if required
    dispatch(fetchContractsAsync({})); // Provide empty
  }, [dispatch, id]);

  React.useEffect(() => {
    if (invoice) {
      reset({
        vendor_id: String(invoice.vendor_id),
        contract_id: invoice.contract_id ? String(invoice.contract_id) : '',
        due_date: invoice.due_date.split('T')[0],
        currency: invoice.currency,
        lineItems: invoice.lineItems.map(item => ({ ...item, id: item.id || crypto.randomUUID() })),
        discount: 0,
        taxRate: invoice.taxRate || 0,
      });
    }
  }, [invoice, reset]);

  const lineItems = watch('lineItems');
  const discount = watch('discount');
  const taxRate = watch('taxRate');
  const currency = watch('currency');

  if (!id || isNaN(Number(id))) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">Invalid Invoice ID</h2>
          <p className="text-gray-600 mt-2">The invoice ID provided is not valid.</p>
          <Button onClick={() => navigate('/invoices/list')} className="mt-4">
            Back to Invoices
          </Button>
        </div>
      </div>
    );
  }

  const onSubmit = async (data: FormValues) => {
    if (!id) return;
    try {
      const subtotal = data.lineItems.reduce((sum, item) => sum + item.totalPrice, 0);
      const discountAmount = subtotal * (data.discount / 100);
      const taxable = subtotal - discountAmount;
      const tax = taxable * (data.taxRate / 100);
      const total = taxable + tax;

      const updatedInvoice = {
        ...invoice,
        ...data,
        vendor_id: Number(data.vendor_id),
        contract_id: data.contract_id ? Number(data.contract_id) : undefined,
        totalAmount: total,
        updatedAt: new Date().toISOString(),
        lineItems: data.lineItems.map(item => ({ ...item, totalPrice: item.quantity * item.unitPrice })),
      };
      await dispatch(updateInvoiceAsync({ id: Number(id), data: updatedInvoice }));
      toast.success('Invoice updated successfully');
      navigate(`/invoices/${id}`);
    } catch (error) {
      toast.error('Failed to update invoice');
    }
  };

  if (status === 'loading' || !invoice) {
    return <Skeleton className="w-full h-[500px]" />;
  }

  return (
    <div className="p-6">
      <Button variant="ghost" onClick={() => navigate(`/invoices/${id}`)} className="mb-4">
        <ArrowLeft className="mr-2 h-4 w-4" /> Back to Invoice
      </Button>
      <Card>
        <CardHeader>
          <CardTitle>Edit Invoice {id}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="vendor_id">Vendor</Label>
                <Controller
                  name="vendor_id"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select vendor" />
                      </SelectTrigger>
                      <SelectContent>
                        {vendors.map(vendor => (
                          <SelectItem key={vendor.id} value={String(vendor.id)}>{vendor.name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
              <div>
                <Label htmlFor="contract_id">Contract (Optional)</Label>
                <Controller
                  name="contract_id"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select contract" />
                      </SelectTrigger>
                      <SelectContent>
                        {contracts.map(contract => (
                          <SelectItem key={contract.id} value={String(contract.id)}>{contract.title}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
              <div>
                <Label htmlFor="due_date">Due Date</Label>
                <Controller
                  name="due_date"
                  control={control}
                  render={({ field }) => <Input type="date" {...field} />}
                />
              </div>
              <div>
                <Label htmlFor="currency">Currency</Label>
                <Controller
                  name="currency"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                        <SelectItem value="GBP">GBP</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
            <LineItemsTable
              lineItems={lineItems}
              onChange={(items) => setValue('lineItems', items.map(item => ({ ...item, totalPrice: item.quantity * item.unitPrice })))}
              mode="edit"
            />
            <InvoiceTotals
              lineItems={lineItems}
              initialDiscount={discount}
              initialTaxRate={taxRate}
              currency={currency}
              onChange={(totals) => {
                setValue('discount', (totals.discount / totals.subtotal) * 100 || 0);
                setValue('taxRate', (totals.tax / (totals.subtotal - totals.discount)) * 100 || 0);
              }}
              mode="edit"
            />
            <Button type="submit">Update Invoice</Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default InvoiceEdit;