import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  FileText, 
  Building2, 
  Calendar, 
  Users, 
  Edit,
  Download,
  MessageSquare,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  MapPin,
  Mail,
  Phone
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RootState } from '@/store';
import { useAuth } from '@/hooks/useAuth';
import { useDispatch } from 'react-redux';
import { fetchContractByIdAsync } from '@/store/slices/contractsSlice';
import CommentsSection from '@/components/Comments/CommentsSection';

export default function ContractView() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { canEdit } = useAuth();
  
  
  // Mock vendor data
  const vendor = {
    id: 1,
    name: 'TechCorp Solutions',
    contact_email: '<EMAIL>',
    contact_phone: '******-0123',
    address: {
      street: '123 Tech Street',
      city: 'San Francisco',
      state: 'CA',
      zip: '94105',
      country: 'USA',
    },
    performance_score: 92.5,
  };

  const dispatch = useDispatch();
  const contract = useSelector((state: RootState) => state.contracts.currentContract);
  const isLoading = useSelector((state: RootState) => state.contracts.isLoading);
  const error = useSelector((state: RootState) => state.contracts.error);

  // Debug logging
  console.log('=== CONTRACT VIEW STATE ===');
  console.log('Contract ID from URL:', id);
  console.log('Current Contract:', contract);
  console.log('IsLoading:', isLoading);
  console.log('Error:', error);

  useEffect(() => {
    if (id) {
      console.log('=== CONTRACT VIEW: Fetching contract ===');
      console.log('Contract ID:', id);
      dispatch(fetchContractByIdAsync(parseInt(id)));
    }
  }, [dispatch, id]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error || !contract) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center">
        <Card className="card-neumorphic max-w-md">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">{error ? 'Error' : 'Contract Not Found'}</h2>
            <p className="text-muted-foreground mb-4">
              {error ? error : 'The requested contract could not be found.'}
            </p>
            <Button onClick={() => navigate('/contracts/list')}>
              Back to Contracts
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'signed':
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      case 'draft':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'terminated':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'signed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'terminated':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getProjectProgress = () => {
    const completedMilestones = contract.milestones.filter(m => m.completed).length;
    return (completedMilestones / contract.milestones.length) * 100;
  };

  const getDaysRemaining = () => {
    const now = new Date();
    const end = new Date(contract.end_date);
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  return (
    <div className="min-h-screen bg-gradient-subtle p-6">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <Button
            variant="ghost"
            onClick={() => navigate('/contracts/list')}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Contracts
          </Button>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-primary/10 rounded-xl">
                <FileText className="w-8 h-8 text-primary" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-foreground">{contract.title}</h1>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className={getStatusColor(contract.status)}>
                    {getStatusIcon(contract.status)}
                    <span className="ml-1 capitalize">{contract.status}</span>
                  </Badge>
                  <span className="text-muted-foreground">Contract #{contract.id}</span>
                </div>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              {canEdit() && (
                <Button onClick={() => navigate(`/contracts/${id}/edit`)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Contract
                </Button>
              )}
            </div>
          </div>
        </motion.div>

        {/* Key Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"
        >
          <Card className="card-neumorphic">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <Calendar className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Days Remaining</p>
                  <p className="text-2xl font-bold">{getDaysRemaining()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="card-neumorphic">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Progress</p>
                  <p className="text-2xl font-bold">{getProjectProgress().toFixed(0)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="card-neumorphic">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                  <Users className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Milestones</p>
                  <p className="text-2xl font-bold">
                    {contract.milestones.filter(m => m.completed).length}/{contract.milestones.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="card-neumorphic">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                  <Building2 className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Vendor Score</p>
                  <p className="text-2xl font-bold">{vendor.performance_score}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="milestones">Milestones</TabsTrigger>
            <TabsTrigger value="vendor">Vendor</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="comments">Comments</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid lg:grid-cols-2 gap-6">
              {/* Contract Details */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="card-neumorphic">
                  <CardHeader>
                    <CardTitle>Contract Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Start Date</p>
                        <p className="font-medium">{formatDate(contract.start_date)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">End Date</p>
                        <p className="font-medium">{formatDate(contract.end_date)}</p>
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-sm text-muted-foreground">Payment Terms</p>
                      <p className="font-medium">{contract.clauses.payment_terms}</p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-muted-foreground">Created</p>
                      <p className="font-medium">{formatDate(contract.created_at)}</p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-muted-foreground">Last Updated</p>
                      <p className="font-medium">{formatDate(contract.updated_at)}</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Progress */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="card-neumorphic">
                  <CardHeader>
                    <CardTitle>Project Progress</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-sm font-medium">Overall Progress</span>
                        <span className="text-sm text-muted-foreground">{getProjectProgress().toFixed(0)}%</span>
                      </div>
                      <Progress value={getProjectProgress()} className="h-2" />
                    </div>
                    
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Milestones Completed</p>
                      {contract.milestones.map((milestone, index) => (
                        <div key={index} className="flex items-center gap-2">
                          {milestone.completed ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <Clock className="w-4 h-4 text-yellow-500" />
                          )}
                          <span className={`text-sm ${milestone.completed ? 'line-through text-muted-foreground' : ''}`}>
                            {milestone.name}
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Deliverables & Terms */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card className="card-neumorphic">
                <CardHeader>
                  <CardTitle>Deliverables & Terms</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Deliverables</p>
                    <p className="text-sm">{contract.clauses.deliverables}</p>
                  </div>
                  
                  {contract.clauses.additional_clauses && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-2">Additional Terms</p>
                      <p className="text-sm">{contract.clauses.additional_clauses}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          <TabsContent value="milestones">
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle>Project Milestones</CardTitle>
                <CardDescription>
                  Track progress through project milestones and deadlines
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {contract.milestones.map((milestone, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {milestone.completed ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <Clock className="w-5 h-5 text-yellow-500" />
                        )}
                        <div>
                          <p className={`font-medium ${milestone.completed ? 'line-through text-muted-foreground' : ''}`}>
                            {milestone.name}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Due: {formatDate(milestone.due_date)}
                          </p>
                        </div>
                      </div>
                      <Badge variant={milestone.completed ? "default" : "secondary"}>
                        {milestone.completed ? "Completed" : "Pending"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="vendor">
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle>Vendor Information</CardTitle>
                <CardDescription>
                  Details about the contracted vendor
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Contact Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4 text-muted-foreground" />
                          <span>{vendor.contact_email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-muted-foreground" />
                          <span>{vendor.contact_phone}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">Address</h4>
                      <div className="flex items-start gap-2 text-sm">
                        <MapPin className="w-4 h-4 text-muted-foreground mt-0.5" />
                        <div>
                          <p>{vendor.address.street}</p>
                          <p>{vendor.address.city}, {vendor.address.state} {vendor.address.zip}</p>
                          <p>{vendor.address.country}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Performance Score</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Overall Score</span>
                        <span className="text-sm font-medium">{vendor.performance_score}/100</span>
                      </div>
                      <Progress value={vendor.performance_score} className="h-2" />
                    </div>
                    
                    <Button 
                      variant="outline" 
                      className="mt-4"
                      onClick={() => navigate(`/vendors/${vendor.id}/profile`)}
                    >
                      View Full Profile
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents">
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle>Contract Documents</CardTitle>
                <CardDescription>
                  Upload and manage contract-related documents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No documents uploaded</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="comments">
            <CommentsSection 
              objectType="contract" 
              objectId={contract.id}
              categories={['general', 'legal', 'financial', 'technical']}
              showFilters={true}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}