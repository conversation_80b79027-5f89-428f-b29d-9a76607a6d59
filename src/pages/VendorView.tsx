import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Building2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  TrendingUp,
  Award,
  Edit,
  Trash2,
  MoreHorizontal,
  FileText,
  Clock,
  AlertTriangle,
  CheckCircle,
  Globe,
  Tag,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AuditTimeline } from '../components/vendors/AuditTimeline';
import CommentsSection from '../components/Comments/CommentsSection';
import RFQHistory from '../components/vendors/RFQHistory';
import { RootState, AppDispatch } from '@/store';
import { 
  fetchVendorByIdAsync, 
  fetchVendorAuditAsync,
  deleteVendorAsync,
  clearCurrentVendor 
} from '@/store/slices/vendorsSlice';
import { fetchContractsAsync } from '@/store/slices/contractsSlice';
import { useAuth } from '../hooks/useAuth';
import { toast } from '../components/ui/sonner';
// Simple date formatting utility
const formatDistanceToNow = (date: Date) => {
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) return 'today';
  if (diffInDays === 1) return '1 day ago';
  if (diffInDays < 30) return `${diffInDays} days ago`;
  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
  return `${Math.floor(diffInDays / 365)} years ago`;
};

export const VendorView: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { canEdit, canDelete } = useAuth();
  
  const { currentVendor, auditHistory, isLoading, error } = useSelector(
    (state: RootState) => state.vendors
  );
  const { contracts: allContracts } = useSelector((state: RootState) => state.contracts);
  
  // Filter contracts for this vendor
  const contracts = allContracts.filter(contract => 
    contract.vendor_id === parseInt(id || '0')
  );
  
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (id) {
      const vendorId = parseInt(id, 10);
      dispatch(fetchVendorByIdAsync(vendorId));
      dispatch(fetchVendorAuditAsync(vendorId));
      // Fetch all contracts (we'll filter on frontend)
      dispatch(fetchContractsAsync({}));
    }

    return () => {
      dispatch(clearCurrentVendor());
    };
  }, [dispatch, id]);

  const handleDelete = async () => {
    if (!currentVendor) return;
    
    if (window.confirm(`Are you sure you want to deactivate ${currentVendor.name}?`)) {
      try {
        await dispatch(deleteVendorAsync(currentVendor.id)).unwrap();
        toast.success('Vendor deactivated successfully');
        navigate('/vendors/list');
      } catch (error: unknown) {
        toast.error('Failed to deactivate vendor', {
          description: (error as Error).message,
        });
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-success bg-success/10 border-success/20';
      case 'inactive':
        return 'text-warning bg-warning/10 border-warning/20';
      case 'blacklisted':
        return 'text-destructive bg-destructive/10 border-destructive/20';
      default:
        return 'text-muted-foreground bg-muted/10 border-muted/20';
    }
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'text-success';
    if (score >= 70) return 'text-warning';
    return 'text-destructive';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="card-neumorphic p-8">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-4"></div>
              <span className="text-muted-foreground">Loading vendor details...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !currentVendor) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="card-neumorphic p-8 text-center">
            <AlertTriangle className="w-16 h-16 text-destructive mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-foreground mb-2">Vendor Not Found</h2>
            <p className="text-muted-foreground mb-6">
              The vendor you're looking for doesn't exist or has been removed.
            </p>
            <Button
              onClick={() => navigate('/vendors/list')}
              className="btn-neumorphic gradient-primary text-primary-foreground"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Vendors
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm border-b border-border"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/vendors/list')}
                className="btn-neumorphic"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Vendors
              </Button>
              <div className="h-6 w-px bg-border" />
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Building2 className="w-4 h-4 text-primary" />
                </div>
                <div>
                  <h1 className="text-lg font-semibold text-foreground">{currentVendor.name}</h1>
                  <p className="text-sm text-muted-foreground">{currentVendor.category}</p>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(
                  currentVendor.status
                )}`}
              >
                {currentVendor.status.charAt(0).toUpperCase() + currentVendor.status.slice(1)}
              </span>

              {(canEdit() || canDelete()) && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="btn-neumorphic">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {canEdit() && (
                      <DropdownMenuItem
                        onClick={() => navigate(`/vendors/${currentVendor.id}/edit`)}
                      >
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Vendor
                      </DropdownMenuItem>
                    )}
                    {canDelete() && (
                      <DropdownMenuItem
                        onClick={handleDelete}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Deactivate
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="contracts">Contracts</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="comments">Comments</TabsTrigger>
            <TabsTrigger value="audit">Audit History</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Basic Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="lg:col-span-2 card-neumorphic p-6"
              >
                <div className="flex items-center space-x-2 mb-6">
                  <Building2 className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-semibold text-foreground">Basic Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Vendor Name</label>
                    <p className="text-foreground font-medium">{currentVendor.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Category</label>
                    <div className="flex items-center space-x-2 mt-1">
                      <Tag className="w-4 h-4 text-muted-foreground" />
                      <span className="text-foreground">{currentVendor.category}</span>
                    </div>
                  </div>
                </div>

                <div className="mt-6 pt-6 border-t border-border">
                  <h4 className="font-medium text-foreground mb-4 flex items-center">
                    <Mail className="w-4 h-4 mr-2 text-primary" />
                    Contact Information
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <Mail className="w-4 h-4 text-muted-foreground" />
                      <span className="text-foreground">{currentVendor.contact_email}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="w-4 h-4 text-muted-foreground" />
                      <span className="text-foreground">{currentVendor.contact_phone}</span>
                    </div>
                    <div className="flex items-start space-x-3">
                      <MapPin className="w-4 h-4 text-muted-foreground mt-0.5" />
                      <div className="text-foreground">
                        <div>{currentVendor.address.street}</div>
                        <div>
                          {currentVendor.address.city}, {currentVendor.address.state}{' '}
                          {currentVendor.address.zip}
                        </div>
                        <div>{currentVendor.address.country}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Certifications */}
                {currentVendor.certifications && currentVendor.certifications.length > 0 && (
                  <div className="mt-6 pt-6 border-t border-border">
                    <h4 className="font-medium text-foreground mb-4 flex items-center">
                      <Award className="w-4 h-4 mr-2 text-primary" />
                      Certifications
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {currentVendor.certifications.map((cert, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm border border-primary/20"
                        >
                          {cert}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>

              {/* Performance & Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="space-y-6"
              >
                {/* Performance Score */}
                <div className="card-neumorphic p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="w-5 h-5 text-primary" />
                      <h3 className="font-semibold text-foreground">Performance</h3>
                    </div>
                    <span className={`text-2xl font-bold ${getPerformanceColor(currentVendor.performance_score)}`}>
                      {currentVendor.performance_score}%
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-3">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${currentVendor.performance_score}%` }}
                      transition={{ duration: 1, delay: 0.5 }}
                      className={`h-3 rounded-full ${
                        currentVendor.performance_score >= 90
                          ? 'bg-success'
                          : currentVendor.performance_score >= 70
                          ? 'bg-warning'
                          : 'bg-destructive'
                      }`}
                    />
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="card-neumorphic p-6">
                  <h3 className="font-semibold text-foreground mb-4 flex items-center">
                    <Calendar className="w-4 h-4 mr-2 text-primary" />
                    Quick Stats
                  </h3>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Contracts</span>
                      <span className="text-foreground font-medium">
                        {contracts.length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Active Contracts</span>
                      <span className="text-foreground font-medium">
                        {contracts.filter(c => c.status === 'active').length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Value</span>
                      <span className="text-foreground font-medium">
                        ${contracts.reduce((sum, c) => sum + (c.value || 0), 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Added</span>
                      <span className="text-foreground">
                        {formatDistanceToNow(new Date(currentVendor.created_at))}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Last Updated</span>
                      <span className="text-foreground">
                        {formatDistanceToNow(new Date(currentVendor.updated_at))}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status</span>
                      <span className={`font-medium ${
                        currentVendor.status === 'active' ? 'text-success' : 
                        currentVendor.status === 'inactive' ? 'text-warning' : 'text-destructive'
                      }`}>
                        {currentVendor.status.charAt(0).toUpperCase() + currentVendor.status.slice(1)}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="card-neumorphic p-6"
            >
              <div className="flex items-center space-x-2 mb-6">
                <TrendingUp className="w-5 h-5 text-primary" />
                <h3 className="text-lg font-semibold text-foreground">RFQ Performance</h3>
              </div>
              <RFQHistory vendorId={currentVendor.id} />
            </motion.div>
          </TabsContent>

          {/* Contracts Tab */}
          <TabsContent value="contracts">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="card-neumorphic p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-2">
                  <FileText className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-semibold text-foreground">Contracts</h3>
                </div>
                {canEdit() && (
                  <Button
                    onClick={() => navigate(`/contracts/create?vendorId=${id}`)}
                    className="btn-neumorphic gradient-primary text-primary-foreground"
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Create Contract
                  </Button>
                )}
              </div>

              {contracts.length > 0 ? (
                <div className="grid gap-4">
                  {contracts.map((contract) => (
                    <div
                      key={contract.id}
                      className="p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                      onClick={() => navigate(`/contracts/${contract.id}`)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-primary/10 rounded-lg">
                              <FileText className="w-4 h-4 text-primary" />
                            </div>
                            <div>
                              <h4 className="font-medium text-foreground">{contract.title}</h4>
                              <p className="text-sm text-muted-foreground">
                                {new Date(contract.start_date).toLocaleDateString()} - {new Date(contract.end_date).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <span
                            className={`px-3 py-1 rounded-full text-sm font-medium ${
                              contract.status === 'active'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                : contract.status === 'draft'
                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                                : contract.status === 'terminated'
                                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                            }`}
                          >
                            {contract.status === 'active' && <CheckCircle className="w-3 h-3 mr-1 inline" />}
                            {contract.status === 'draft' && <Clock className="w-3 h-3 mr-1 inline" />}
                            {contract.status === 'terminated' && <AlertTriangle className="w-3 h-3 mr-1 inline" />}
                            <span className="capitalize">{contract.status}</span>
                          </span>
                          {contract.value && (
                            <span className="text-sm font-medium text-muted-foreground">
                              ${contract.value.toLocaleString()} {contract.currency}
                            </span>
                          )}
                        </div>
                      </div>
                      {contract.milestones.length > 0 && (
                        <div className="mt-3 pt-3 border-t border-border">
                          <div className="flex items-center justify-between text-sm mb-2">
                            <span className="text-muted-foreground">Progress</span>
                            <span className="font-medium">
                              {contract.milestones.filter(m => m.completed).length}/{contract.milestones.length} milestones completed
                            </span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className="bg-primary h-2 rounded-full transition-all duration-300"
                              style={{
                                width: `${(contract.milestones.filter(m => m.completed).length / contract.milestones.length) * 100}%`
                              }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <FileText className="w-16 h-16 text-muted-foreground mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-semibold text-foreground mb-2">No Contracts</h3>
                  <p className="text-muted-foreground mb-4">
                    This vendor doesn't have any contracts yet.
                  </p>
                  {canEdit() && (
                    <Button
                      onClick={() => navigate(`/contracts/create?vendorId=${id}`)}
                      className="btn-neumorphic gradient-primary text-primary-foreground"
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      Create First Contract
                    </Button>
                  )}
                </div>
              )}
            </motion.div>
          </TabsContent>

          {/* Documents Tab */}
          <TabsContent value="documents">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="card-neumorphic p-8 text-center"
            >
              <FileText className="w-16 h-16 text-muted-foreground mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-semibold text-foreground mb-2">Documents</h3>
              <p className="text-muted-foreground">
                Vendor documents and attachments will be displayed here.
              </p>
            </motion.div>
          </TabsContent>

          {/* Comments Tab */}
          <TabsContent value="comments">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <CommentsSection
                objectType="vendor"
                objectId={currentVendor.id}
                categories={['general', 'performance', 'compliance']}
                showFilters={true}
              />
            </motion.div>
          </TabsContent>

          {/* Audit History Tab */}
          <TabsContent value="audit">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="card-neumorphic p-6"
            >
              <div className="flex items-center space-x-2 mb-6">
                <Clock className="w-5 h-5 text-primary" />
                <h3 className="text-lg font-semibold text-foreground">Audit History</h3>
              </div>
              <AuditTimeline auditEntries={auditHistory} />
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};