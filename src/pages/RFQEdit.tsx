import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Save,
  AlertTriangle,
  Plus,
  Trash2,
  Calendar,
  FileText,
  Users,
  Settings,
  Package,
  CheckCircle2,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Al<PERSON>,
  AlertDescription,
} from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { RFQApiService, RFQ, RFQItem, RFQUpdateData, PartialSelectionConfig } from '@/services/api/rfq';
import { useAuth } from '@/hooks/useAuth';

const DEFAULT_PARTIAL_SELECTION_CONFIG: PartialSelectionConfig = {
  enabled: false,
  requireVendorConfirmation: true,
  confirmationMessage: 'Do you allow individual item purchases at the quoted rates?',
  instructions: 'Please confirm if you allow partial selection of items from your submission.',
  defaultAllowed: false
};

interface ItemFormData extends RFQItem {
  isNew?: boolean;
  isModified?: boolean;
}

const ItemEditor: React.FC<{
  item: ItemFormData;
  onUpdate: (item: ItemFormData) => void;
  onRemove: () => void;
  currencies: string[];
}> = ({ item, onUpdate, onRemove, currencies }) => {
  const [isExpanded, setIsExpanded] = useState(item.isNew || false);

  const handleFieldChange = (field: keyof ItemFormData, value: string | number | Record<string, unknown>) => {
    onUpdate({
      ...item,
      [field]: value,
      isModified: true
    });
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Package className="w-5 h-5 text-primary" />
            <div>
              <CardTitle className="text-base">
                {item.name || 'New Item'}
              </CardTitle>
              {item.category && (
                <CardDescription>{item.category}</CardDescription>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {item.isModified && (
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                Modified
              </Badge>
            )}
            {item.isNew && (
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                New
              </Badge>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? <X className="w-4 h-4" /> : <FileText className="w-4 h-4" />}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onRemove}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor={`item-name-${item.id}`}>Item Name *</Label>
              <Input
                id={`item-name-${item.id}`}
                value={item.name}
                onChange={(e) => handleFieldChange('name', e.target.value)}
                placeholder="Enter item name"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor={`item-category-${item.id}`}>Category</Label>
              <Input
                id={`item-category-${item.id}`}
                value={item.category}
                onChange={(e) => handleFieldChange('category', e.target.value)}
                placeholder="Enter category"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor={`item-quantity-${item.id}`}>Quantity *</Label>
              <Input
                id={`item-quantity-${item.id}`}
                type="number"
                min="1"
                value={item.quantity}
                onChange={(e) => handleFieldChange('quantity', parseInt(e.target.value) || 1)}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor={`item-unit-${item.id}`}>Unit</Label>
              <Input
                id={`item-unit-${item.id}`}
                value={item.unit || ''}
                onChange={(e) => handleFieldChange('unit', e.target.value)}
                placeholder="e.g., pieces, kg, hours"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor={`item-estimated-price-${item.id}`}>Estimated Price</Label>
              <Input
                id={`item-estimated-price-${item.id}`}
                type="number"
                min="0"
                step="0.01"
                value={item.estimatedPrice || ''}
                onChange={(e) => handleFieldChange('estimatedPrice', parseFloat(e.target.value) || undefined)}
                placeholder="0.00"
                className="mt-1"
              />
            </div>

            <div className="md:col-span-2">
              <Label htmlFor={`item-description-${item.id}`}>Description</Label>
              <Textarea
                id={`item-description-${item.id}`}
                value={item.description}
                onChange={(e) => handleFieldChange('description', e.target.value)}
                placeholder="Enter item description"
                rows={3}
                className="mt-1"
              />
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

const PartialSelectionConfigEditor: React.FC<{
  config: PartialSelectionConfig | null | undefined;
  enabled: boolean;
  onConfigChange: (config: PartialSelectionConfig) => void;
  onEnabledChange: (enabled: boolean) => void;
}> = ({ config, enabled, onConfigChange, onEnabledChange }) => {
  const handleConfigChange = (field: keyof PartialSelectionConfig, value: unknown) => {
    const safeConfig = config || {
      enabled: false,
      requireVendorConfirmation: true,
      confirmationMessage: 'Do you allow individual item purchases at the quoted rates?',
      instructions: 'Please confirm if you allow partial selection of items from your submission.',
      defaultAllowed: false
    };
    
    onConfigChange({
      ...safeConfig,
      [field]: value
    });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Partial Selection Configuration
            </CardTitle>
            <CardDescription>
              Allow vendors to opt-in for individual item purchases
            </CardDescription>
          </div>
          <Switch
            checked={enabled}
            onCheckedChange={onEnabledChange}
          />
        </div>
      </CardHeader>

      {enabled && (
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="require-confirmation"
              checked={config?.requireVendorConfirmation || false}
              onCheckedChange={(checked) => handleConfigChange('requireVendorConfirmation', checked)}
            />
            <Label htmlFor="require-confirmation">
              Require vendor confirmation for partial selection
            </Label>
          </div>

          {config?.requireVendorConfirmation && (
            <>
              <div>
                <Label htmlFor="confirmation-message">Confirmation Message</Label>
                <Input
                  id="confirmation-message"
                  value={config?.confirmationMessage || ''}
                  onChange={(e) => handleConfigChange('confirmationMessage', e.target.value)}
                  placeholder="Do you allow individual item purchases at the quoted rates?"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="instructions">Instructions for Vendors</Label>
                <Textarea
                  id="instructions"
                  value={config?.instructions || ''}
                  onChange={(e) => handleConfigChange('instructions', e.target.value)}
                  placeholder="Please confirm if you allow partial selection of items from your submission."
                  rows={3}
                  className="mt-1"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="default-allowed"
                  checked={config?.defaultAllowed || false}
                  onCheckedChange={(checked) => handleConfigChange('defaultAllowed', checked)}
                />
                <Label htmlFor="default-allowed">
                  Default to allowing partial selection
                </Label>
              </div>
            </>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export const RFQEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { canEdit } = useAuth();
  const { toast } = useToast();

  const [rfq, setRfq] = useState<RFQ | null>(null);
  const [formData, setFormData] = useState<RFQUpdateData>({});
  const [items, setItems] = useState<ItemFormData[]>([]);
  const [partialSelectionEnabled, setPartialSelectionEnabled] = useState(false);
  const [partialSelectionConfig, setPartialSelectionConfig] = useState<PartialSelectionConfig>({
    enabled: false,
    requireVendorConfirmation: true,
    confirmationMessage: 'Do you allow individual item purchases at the quoted rates?',
    instructions: 'Please confirm if you allow partial selection of items from your submission.',
    defaultAllowed: false
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showWarningDialog, setShowWarningDialog] = useState(false);
  const [hasActiveSubmissions, setHasActiveSubmissions] = useState(false);

  const currencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'];

  useEffect(() => {
    if (!canEdit()) {
      navigate('/rfqs');
      return;
    }

    if (id) {
      loadRFQ();
    }
  }, [id, canEdit, navigate]);

  const loadRFQ = async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await RFQApiService.getRFQById(parseInt(id));
      if (response.success) {
        const rfqData = response.data;
        setRfq(rfqData);
        
        // Initialize form data
        setFormData({
          title: rfqData.title,
          description: rfqData.description,
          due_date: rfqData.due_date,
          terms: rfqData.terms,
          currency: rfqData.currency,
          allowPartialSelection: rfqData.allow_partial_selection,
          partialSelectionConfig: rfqData.partial_selection_config
        });

        // Initialize items
        setItems(rfqData.items.map(item => ({ ...item, isModified: false })));

        // Initialize partial selection
        setPartialSelectionEnabled(rfqData.allow_partial_selection || false);
        setPartialSelectionConfig(rfqData.partial_selection_config || {
          enabled: false,
          requireVendorConfirmation: true,
          confirmationMessage: 'Do you allow individual item purchases at the quoted rates?',
          instructions: 'Please confirm if you allow partial selection of items from your submission.',
          defaultAllowed: false
        });

        // Check for active submissions
        setHasActiveSubmissions(rfqData.submission_count > 0);
      }
    } catch (err) {
      setError('Failed to load RFQ details');
      console.error('Error loading RFQ:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFormChange = (field: keyof RFQUpdateData, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddItem = () => {
    const newItem: ItemFormData = {
      id: `new-${Date.now()}`,
      name: '',
      description: '',
      quantity: 1,
      specifications: {},
      category: '',
      customFields: {},
      isNew: true,
      isModified: true
    };
    setItems(prev => [...prev, newItem]);
  };

  const handleUpdateItem = (index: number, updatedItem: ItemFormData) => {
    setItems(prev => {
      const newItems = [...prev];
      newItems[index] = updatedItem;
      return newItems;
    });
  };

  const handleRemoveItem = (index: number) => {
    setItems(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = (): string[] => {
    const errors: string[] = [];

    if (!formData.title?.trim()) {
      errors.push('Title is required');
    }

    if (!formData.due_date || new Date(formData.due_date) <= new Date()) {
      errors.push('Due date must be in the future');
    }

    if (items.length === 0) {
      errors.push('At least one item is required');
    }

    items.forEach((item, index) => {
      if (!item.name?.trim()) {
        errors.push(`Item ${index + 1}: Name is required`);
      }
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Item ${index + 1}: Quantity must be greater than 0`);
      }
    });

    if (partialSelectionEnabled && partialSelectionConfig.requireVendorConfirmation) {
      if (!partialSelectionConfig.confirmationMessage?.trim()) {
        errors.push('Confirmation message is required when vendor confirmation is enabled');
      }
    }

    return errors;
  };

  const handleSave = async () => {
    const errors = validateForm();
    if (errors.length > 0) {
      toast({
        title: 'Validation Error',
        description: errors.join(', '),
        variant: 'destructive',
      });
      return;
    }

    if (hasActiveSubmissions) {
      setShowWarningDialog(true);
      return;
    }

    await performSave();
  };

  const performSave = async () => {
    if (!id) return;

    setSaving(true);
    setShowWarningDialog(false);

    try {
      const updateData: RFQUpdateData = {
        ...formData,
        items: items.map(item => ({
          id: item.id,
          name: item.name,
          description: item.description,
          quantity: item.quantity,
          unit: item.unit,
          specifications: item.specifications,
          category: item.category,
          estimatedPrice: item.estimatedPrice,
          customFields: item.customFields
        })),
        allowPartialSelection: partialSelectionEnabled,
        partialSelectionConfig: {
          ...partialSelectionConfig,
          enabled: partialSelectionEnabled
        }
      };

      const response = await RFQApiService.updateRFQ(parseInt(id), updateData);
      if (response.success) {
        toast({
          title: 'Success',
          description: 'RFQ updated successfully',
        });
        navigate(`/rfqs/${id}`);
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to update RFQ',
        variant: 'destructive',
      });
      console.error('Error updating RFQ:', err);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-muted rounded w-1/4 mb-4"></div>
            <div className="h-64 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !rfq) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card>
            <CardContent className="p-8 text-center">
              <AlertTriangle className="w-16 h-16 text-destructive mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">Error Loading RFQ</h2>
              <p className="text-muted-foreground mb-6">
                {error || 'The RFQ could not be loaded for editing.'}
              </p>
              <Button onClick={() => navigate('/rfqs')}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to RFQs
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm border-b border-border">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(`/rfqs/${id}`)}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to RFQ
              </Button>
              <div className="h-6 w-px bg-border" />
              <div>
                <h1 className="text-lg font-semibold">Edit RFQ</h1>
                <p className="text-sm text-muted-foreground">{rfq.title}</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={() => navigate(`/rfqs/${id}`)}>
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={saving}>
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {hasActiveSubmissions && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                This RFQ has active submissions. Changes may affect existing vendor bids.
              </AlertDescription>
            </Alert>
          )}

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title || ''}
                    onChange={(e) => handleFormChange('title', e.target.value)}
                    placeholder="Enter RFQ title"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <Select
                    value={formData.currency || 'USD'}
                    onValueChange={(value) => handleFormChange('currency', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {currencies.map(currency => (
                        <SelectItem key={currency} value={currency}>
                          {currency}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="due-date">Due Date *</Label>
                  <Input
                    id="due-date"
                    type="datetime-local"
                    value={formData.due_date ? new Date(formData.due_date).toISOString().slice(0, 16) : ''}
                    onChange={(e) => handleFormChange('due_date', e.target.value)}
                    className="mt-1"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description || ''}
                  onChange={(e) => handleFormChange('description', e.target.value)}
                  placeholder="Enter RFQ description"
                  rows={4}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="terms">Terms & Conditions</Label>
                <Textarea
                  id="terms"
                  value={formData.terms || ''}
                  onChange={(e) => handleFormChange('terms', e.target.value)}
                  placeholder="Enter terms and conditions"
                  rows={4}
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="w-5 h-5" />
                    Items ({items.length})
                  </CardTitle>
                  <CardDescription>
                    Define the items or services you need quotes for
                  </CardDescription>
                </div>
                <Button onClick={handleAddItem}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Item
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {items.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No items added yet</p>
                  <Button onClick={handleAddItem} className="mt-4">
                    <Plus className="w-4 h-4 mr-2" />
                    Add First Item
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {items.map((item, index) => (
                    <ItemEditor
                      key={item.id}
                      item={item}
                      onUpdate={(updatedItem) => handleUpdateItem(index, updatedItem)}
                      onRemove={() => handleRemoveItem(index)}
                      currencies={currencies}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Partial Selection Configuration */}
          <PartialSelectionConfigEditor
            config={partialSelectionConfig}
            enabled={partialSelectionEnabled}
            onConfigChange={setPartialSelectionConfig}
            onEnabledChange={setPartialSelectionEnabled}
          />
        </div>
      </div>

      {/* Warning Dialog */}
      <Dialog open={showWarningDialog} onOpenChange={setShowWarningDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-amber-500" />
              Confirm Changes
            </DialogTitle>
            <DialogDescription>
              This RFQ has {rfq.submission_count} active submission{rfq.submission_count !== 1 ? 's' : ''}. 
              Making changes may affect existing vendor bids and could require vendors to resubmit their proposals.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowWarningDialog(false)}>
              Cancel
            </Button>
            <Button onClick={performSave} disabled={saving}>
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                'Save Anyway'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RFQEdit;