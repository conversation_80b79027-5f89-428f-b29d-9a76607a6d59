import React, { useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { ArrowLeft, FileText, Building2, Calendar, Users, Save, AlertTriangle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { fetchVendorsAsync } from '@/store/slices/vendorsSlice';
import { createContractAsync, fetchContractTemplatesAsync } from '@/store/slices/contractsSlice';
import { RootState, AppDispatch } from '@/store';
import { useAuth } from '@/hooks/useAuth';
import { toast } from '@/components/ui/sonner';
import { useFieldArray } from 'react-hook-form';

const schema = yup.object({
  title: yup.string().required('Contract title is required'),
  vendor_id: yup.number().required('Vendor selection is required'),
  start_date: yup.date().required('Start date is required'),
  end_date: yup.date()
    .required('End date is required')
    .min(yup.ref('start_date'), 'End date must be after start date'),
  payment_terms: yup.string().required('Payment terms are required'),
  deliverables: yup.string().required('Deliverables description is required'),
  value: yup.number().positive('Contract value must be positive').optional(),
  currency: yup.string().optional(),
  additional_clauses: yup.string().optional(),
  milestones: yup.array().of(
    yup.object().shape({
      name: yup.string().required('Milestone name is required'),
      description: yup.string().optional(),
      due_date: yup.date().required('Due date is required'),
    })
  ).optional(),
});

type FormData = yup.InferType<typeof schema>;



export default function ContractCreate() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [searchParams] = useSearchParams();
  const { canEdit } = useAuth();
  
  const { vendors } = useSelector((state: RootState) => state.vendors);
  const { templates, isCreating } = useSelector((state: RootState) => state.contracts);
  const [selectedTemplate, setSelectedTemplate] = React.useState<string>('');

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      title: '',
      vendor_id: undefined,
      start_date: undefined,
      end_date: undefined,
      payment_terms: '',
      deliverables: '',
      value: undefined,
      currency: 'USD',
      milestones: [],
      additional_clauses: '',
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'milestones',
  });
  
  useEffect(() => {
    dispatch(fetchVendorsAsync({}));
    dispatch(fetchContractTemplatesAsync());
  }, [dispatch]);

  // Pre-fill vendor if provided in URL
  useEffect(() => {
    const vendorId = searchParams.get('vendorId');
    if (vendorId) {
      setValue('vendor_id', parseInt(vendorId), { shouldDirty: true });
    }
  }, [searchParams, setValue]);

  if (!canEdit()) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center">
        <Card className="card-neumorphic max-w-md">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-muted-foreground mb-4">
              You don't have permission to create contracts.
            </p>
            <Button onClick={() => navigate('/contracts/list')}>
              View Contracts
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = templates.find(t => t.id === templateId);
    if (template) {
      console.log('Selected template:', template);
      setValue('title', template.name);
      
      // Handle both defaultFields and default_fields structure
      const defaultFields = template.defaultFields || template.default_fields;
      if (defaultFields) {
        setValue('payment_terms', defaultFields.payment_terms || '');
        setValue('deliverables', defaultFields.deliverables || '');
        setValue('additional_clauses', defaultFields.additional_clauses || '');
      }
    }
  };

  const onSubmit = async (data: FormData, isDraft = false) => {
    try {
      console.log('Form data being submitted:', data);
      
      // Create contract object
      const contractData = {
        vendor_id: data.vendor_id,
        title: data.title,
        status: isDraft ? 'draft' : 'active',
        parties: {
          vendor: vendors.find(v => v.id === data.vendor_id)?.name || '',
          client: 'VMS Corp',
        },
        clauses: {
          payment_terms: data.payment_terms,
          deliverables: data.deliverables,
          additional_clauses: data.additional_clauses || '',
        },
        start_date: data.start_date,
        end_date: data.end_date,
        milestones: data.milestones || [],
      };
      
      const result = await dispatch(createContractAsync(contractData));
      
      if (result.meta.requestStatus === 'fulfilled') {
        const createdContract = result.payload;
        toast.success(isDraft ? 'Draft saved successfully' : 'Contract created successfully');
        
        if (!isDraft) {
          reset();
          // Navigate to the contract detail page
          navigate(`/contracts/${createdContract.id}`);
        }
      } else {
        throw new Error('Failed to create contract');
      }
    } catch (error) {
      toast.error('Failed to create contract');
      console.error('Failed to create contract:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-subtle p-6">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <Button
            variant="ghost"
            onClick={() => navigate('/contracts/list')}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Contracts
          </Button>
          
          <div className="flex items-center gap-3 mb-2">
            <FileText className="w-8 h-8 text-primary" />
            <h1 className="text-3xl font-bold text-foreground">Create New Contract</h1>
          </div>
          <p className="text-muted-foreground">
            Create a new contract using a template or from scratch
          </p>
        </motion.div>

        {/* Template Selection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-6"
        >
          <Card className="card-neumorphic">
            <CardHeader>
              <CardTitle>Choose a Template</CardTitle>
              <CardDescription>
                Select a contract template to get started quickly
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                {templates.map((template) => (
                  <button
                    key={template.id}
                    type="button"
                    onClick={() => handleTemplateSelect(template.id)}
                    className={`p-4 rounded-lg border text-left transition-all ${
                      selectedTemplate === template.id
                        ? 'bg-primary/10 border-primary'
                        : 'bg-background border-border hover:border-primary/50'
                    }`}
                  >
                    <h4 className="font-medium mb-1">{template.name}</h4>
                    <p className="text-sm text-muted-foreground">{template.description}</p>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Contract Details
                </CardTitle>
                <CardDescription>
                  Enter the basic contract information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Contract Title *</Label>
                    <Controller
                      name="title"
                      control={control}
                      render={({ field }) => (
                        <Input
                      {...field}
                      placeholder="Enter contract title"
                      className={errors.title ? 'border-destructive' : ''}
                    />
                      )}
                    />
                    {errors.title && (
                      <p className="text-sm text-destructive mt-1">{errors.title.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="vendor_id">Vendor *</Label>
                    <Controller
                      name="vendor_id"
                      control={control}
                      render={({ field }) => (
                        <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                          <SelectTrigger className={errors.vendor_id ? 'border-destructive' : ''}>
                            <SelectValue placeholder="Select vendor" />
                          </SelectTrigger>
                          <SelectContent>
                            {vendors.map((vendor) => (
                              <SelectItem key={vendor.id} value={vendor.id.toString()}>
                                <div className="flex items-center gap-2">
                                  <Building2 className="w-4 h-4" />
                                  <span>{vendor.name}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.vendor_id && (
                      <p className="text-sm text-destructive mt-1">{errors.vendor_id.message}</p>
                    )}
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start_date">Start Date *</Label>
                    <Controller
                      name="start_date"
                      control={control}
                      render={({ field }) => (
                        <Input
                      {...field}
                      type="date"
                      value={field.value ? new Date(field.value).toISOString().split('T')[0] : ''}
                      onChange={(e) => field.onChange(new Date(e.target.value))}
                      className={errors.start_date ? 'border-destructive' : ''}
                    />
                      )}
                    />
                    {errors.start_date && (
                      <p className="text-sm text-destructive mt-1">{errors.start_date.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="end_date">End Date *</Label>
                    <Controller
                      name="end_date"
                      control={control}
                      render={({ field }) => (
                        <Input
                      {...field}
                      type="date"
                      value={field.value ? new Date(field.value).toISOString().split('T')[0] : ''}
                      onChange={(e) => field.onChange(new Date(e.target.value))}
                      className={errors.end_date ? 'border-destructive' : ''}
                    />
                      )}
                    />
                    {errors.end_date && (
                      <p className="text-sm text-destructive mt-1">{errors.end_date.message}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Terms & Deliverables */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Terms & Deliverables
                </CardTitle>
                <CardDescription>
                  Define payment terms and project deliverables
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="payment_terms">Payment Terms *</Label>
                  <Controller
                    name="payment_terms"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="e.g., 30 days net, Monthly in advance"
                        className={errors.payment_terms ? 'border-destructive' : ''}
                      />
                    )}
                  />
                  {errors.payment_terms && (
                    <p className="text-sm text-destructive mt-1">{errors.payment_terms.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="deliverables">Deliverables *</Label>
                  <Controller
                    name="deliverables"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        {...field}
                        placeholder="Describe the expected deliverables and outcomes"
                        rows={4}
                        className={errors.deliverables ? 'border-destructive' : ''}
                      />
                    )}
                  />
                  {errors.deliverables && (
                    <p className="text-sm text-destructive mt-1">{errors.deliverables.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="additional_clauses">Additional Clauses</Label>
                  <Controller
                    name="additional_clauses"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        {...field}
                        placeholder="Any additional terms, conditions, or special clauses"
                        rows={3}
                      />
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Milestones */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Milestones
                </CardTitle>
                <CardDescription>
                  Add project milestones and deadlines (optional)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className="border-b pb-4 mb-4">
                    <div className="grid md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor={`milestones.${index}.name`}>Name *</Label>
                        <Controller
                          name={`milestones.${index}.name`}
                          control={control}
                          render={({ field }) => (
                            <Input
                              {...field}
                              placeholder="Milestone Name"
                              className={errors.milestones && errors.milestones[index]?.name ? 'border-destructive' : ''}
                            />
                          )}
                        />
                      </div>
                      <div>
                        <Label htmlFor={`milestones.${index}.description`}>Description</Label>
                        <Controller
                          name={`milestones.${index}.description`}
                          control={control}
                          render={({ field }) => (
                            <Input
                              {...field}
                              placeholder="Milestone Description"
                              className={errors.milestones && errors.milestones[index]?.description ? 'border-destructive' : ''}
                            />
                          )}
                        />
                      </div>
                      <div>
                        <Label htmlFor={`milestones.${index}.due_date`}>Due Date *</Label>
                        <Controller
                          name={`milestones.${index}.due_date`}
                          control={control}
                          render={({ field }) => (
                            <Input
                              {...field}
                              type="date"
                              value={field.value ? new Date(field.value).toISOString().split('T')[0] : ''}
                              onChange={(e) => field.onChange(new Date(e.target.value))}
                              className={errors.milestones && errors.milestones[index]?.due_date ? 'border-destructive' : ''}
                            />
                          )}
                        />
                      </div>
                    </div>
                    <Button type="button" variant="destructive" size="sm" onClick={() => remove(index)} className="mt-2">
                      Remove
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => append({ name: '', description: '', due_date: undefined })}
                >
                  Add Milestone
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Submit Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="flex justify-end gap-4"
          >
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/contracts/list')}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="secondary"
              disabled={isSubmitting || !isDirty}
              onClick={handleSubmit(async (data) => {
                await onSubmit(data, true); // true for draft
              })}
              className="min-w-32"
            >
              {isSubmitting ? 'Saving...' : 'Save Draft'}
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !isDirty}
              className="min-w-32"
            >
              {isSubmitting ? 'Creating...' : 'Create Contract'}
            </Button>
          </motion.div>
        </form>
      </div>
    </div>
  );
}