import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, Shield, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { VendorForm } from '../components/vendors';
import { useVendorForm } from '../hooks/useVendorForm';
import { useAuth } from '../hooks/useAuth';
import { toast } from '../components/ui/sonner';

export const VendorCreate: React.FC = () => {
  const navigate = useNavigate();
  const { canEdit } = useAuth();
  
  const { handleSubmit, handleCancel, isSubmitting } = useVendorForm({
    mode: 'create',
    onSuccess: (vendor) => {
      // Show success message with vendor details
      toast.success('Vendor created successfully!', {
        description: `${vendor.name} has been added to your vendor list.`,
        action: {
          label: 'View Vendor',
          onClick: () => navigate(`/vendors/${vendor.id}`),
        },
      });
    },
    redirectTo: '/vendors/list',
  });

  // Check permissions on mount
  useEffect(() => {
    if (!canEdit()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to create vendors.',
      });
      navigate('/vendors/list');
    }
  }, [canEdit, navigate]);

  // Don't render if user doesn't have permission
  if (!canEdit()) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm border-b border-border"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/vendors/list')}
                className="btn-neumorphic"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Vendors
              </Button>
              <div className="h-6 w-px bg-border" />
              <div>
                <h1 className="text-lg font-semibold text-foreground">Create New Vendor</h1>
                <p className="text-sm text-muted-foreground">Add a new vendor to your system</p>
              </div>
            </div>

            {/* Progress Indicator */}
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-4 h-4 text-primary" />
                <span className="text-sm text-muted-foreground">Step 1 of 1</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Security Notice */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="card-neumorphic p-4 bg-primary/5 border-primary/20">
            <div className="flex items-start space-x-3">
              <Shield className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-foreground">Security Notice</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  All vendor information is encrypted and stored securely. Only authorized personnel 
                  can access and modify vendor data. This action will be logged for audit purposes.
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Vendor Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <VendorForm
            mode="create"
            onSubmit={handleSubmit}
            isLoading={isSubmitting}
          />
        </motion.div>

        {/* Help Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8"
        >
          <div className="card-neumorphic p-6">
            <h3 className="text-lg font-semibold text-foreground mb-4">Need Help?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-foreground mb-2">Required Information</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Vendor name and category</li>
                  <li>• Valid email address and phone number</li>
                  <li>• Complete business address</li>
                  <li>• Relevant certifications (optional)</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-foreground mb-2">What Happens Next?</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Vendor will be added to your system</li>
                  <li>• Performance tracking will begin</li>
                  <li>• You can create contracts and invoices</li>
                  <li>• Audit trail will track all changes</li>
                </ul>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};