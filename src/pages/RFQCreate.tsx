import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { motion } from "framer-motion";
import { ArrowLeft, Shield, FileText, AlertCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { RFQCreationForm, RFQCreateData } from "@/components/rfq";
import { RFQDraft } from "@/components/rfq/RFQCreationForm";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "@/components/ui/use-toast";
import { Vendor } from "@/store/slices/vendorsSlice";
import VendorApiService from "@/services/vendorApi";
import { RFQApiService } from "@/services/api/rfq";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";

// Mock vendors data for now - this would come from the API
const mockVendors: Vendor[] = [
  {
    id: 1,
    name: "TechCorp Solutions",
    contact_email: "<EMAIL>",
    contact_phone: "******-0123",
    address: {
      street: "123 Tech Street",
      city: "San Francisco",
      state: "CA",
      zip: "94105",
      country: "USA",
    },
    category: "Technology",
    certifications: ["ISO 9001", "SOC 2"],
    performance_score: 92.5,
    status: "active",
    custom_fields: {},
    created_at: "2024-01-15T10:30:00Z",
    updated_at: "2024-01-20T14:22:00Z",
  },
  {
    id: 2,
    name: "Global Manufacturing Inc",
    contact_email: "<EMAIL>",
    contact_phone: "******-0456",
    address: {
      street: "456 Industrial Blvd",
      city: "Detroit",
      state: "MI",
      zip: "48201",
      country: "USA",
    },
    category: "Manufacturing",
    certifications: ["ISO 14001", "OSHA"],
    performance_score: 87.3,
    status: "active",
    custom_fields: {},
    created_at: "2024-01-10T08:15:00Z",
    updated_at: "2024-01-25T11:45:00Z",
  },
  {
    id: 3,
    name: "Logistics Express",
    contact_email: "<EMAIL>",
    contact_phone: "******-0789",
    address: {
      street: "789 Freight Way",
      city: "Memphis",
      state: "TN",
      zip: "38103",
      country: "USA",
    },
    category: "Logistics",
    certifications: ["C-TPAT", "IATA"],
    performance_score: 78.9,
    status: "active",
    custom_fields: {},
    created_at: "2024-01-05T16:20:00Z",
    updated_at: "2024-01-22T09:30:00Z",
  },
];

// Remove the mockVendors array - we'll fetch real data

const RFQCreate: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user, hasPermission, canEdit } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingDraft, setIsLoadingDraft] = useState(false);
  const [currentDraft, setCurrentDraft] = useState<RFQDraft | null>(null);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [isLoadingVendors, setIsLoadingVendors] = useState(true);

  const draftId = searchParams.get('draft');

  // Check permissions and load draft on mount
  useEffect(() => {
    if (!canEdit()) {
      toast({
        title: "Access Denied",
        description: "You do not have permission to create RFQs.",
        variant: "destructive",
      });
      navigate("/dashboard");
      return;
    }

    // Load draft if draftId is provided
    if (draftId) {
      loadDraft(draftId);
    }
  }, [canEdit, navigate, draftId]);

  const loadDraft = async (id: string) => {
    setIsLoadingDraft(true);
    try {
      // TODO: Replace with actual API call to load draft
      const savedDrafts = JSON.parse(localStorage.getItem('rfq_drafts') || '{}');
      const draft = savedDrafts[id];
      
      if (draft) {
        // Convert date strings back to Date objects
        draft.dueDate = new Date(draft.dueDate);
        draft.lastSaved = new Date(draft.lastSaved);
        setCurrentDraft(draft);
        
        toast({
          title: "Draft Loaded",
          description: `Resumed RFQ draft: "${draft.title}"`,
        });
      } else {
        toast({
          title: "Draft Not Found",
          description: "The requested draft could not be found.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to load draft:', error);
      toast({
        title: "Error Loading Draft",
        description: "There was an error loading your draft.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingDraft(false);
    }
  };

  const saveDraft = async (draft: RFQDraft) => {
    try {
      // TODO: Replace with actual API call to save draft
      const savedDrafts = JSON.parse(localStorage.getItem('rfq_drafts') || '{}');
      savedDrafts[draft.id] = draft;
      localStorage.setItem('rfq_drafts', JSON.stringify(savedDrafts));
      
      setCurrentDraft(draft);
    } catch (error) {
      console.error('Failed to save draft:', error);
      toast({
        title: "Error Saving Draft",
        description: "There was an error saving your draft.",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (data: RFQCreateData) => {
    setIsSubmitting(true);
    try {
      console.log("Creating RFQ:", data);

      // Transform data to match backend API format
      const rfqData = {
        title: data.title,
        description: data.description,
        items: data.items.map(item => ({
          ...item,
          customFields: item.specifications || {}
        })),
        due_date: data.dueDate.toISOString(),
        selectedVendors: data.selectedVendors,
        formConfig: data.formConfig?.map(field => ({
          ...field,
          validation: field.validation?.map(v => v as Record<string, unknown>) || []
        })),
        terms: data.terms,
        currency: 'USD', // Default currency
        aiSettings: data.aiSettings,
        allowPartialSelection: data.allowPartialSelection || false,
        partialSelectionConfig: data.partialSelectionConfig || {
          enabled: false,
          requireVendorConfirmation: true,
          confirmationMessage: "Do you allow individual item purchases at the quoted rates?",
          instructions: "Please confirm if you allow partial selection of items from your submission.",
          defaultAllowed: false
        },
      };

      // Call the actual backend API
      const response = await RFQApiService.createRFQ(rfqData);
      
      if (response.data) {
        // Clear draft after successful submission
        if (currentDraft) {
          const savedDrafts = JSON.parse(localStorage.getItem('rfq_drafts') || '{}');
          delete savedDrafts[currentDraft.id];
          localStorage.setItem('rfq_drafts', JSON.stringify(savedDrafts));
        }

        toast({
          title: "RFQ Created Successfully!",
          description: `"${data.title}" has been created and saved as draft. You can now send it to ${data.selectedVendors.length} vendors.`,
        });

        // Navigate to RFQ detail page
        navigate(`/rfqs/${response.data.id}`);
      } else {
        throw new Error('Failed to create RFQ');
      }
    } catch (error) {
      console.error("RFQ creation error:", error);
      toast({
        title: "Error Creating RFQ",
        description: error instanceof Error ? error.message : "There was an error creating your RFQ. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add vendor loading function
  const loadVendors = async () => {
    setIsLoadingVendors(true);
    try {
      const response = await VendorApiService.getVendors(1, 100, { status: 'active' });
      if (response && response.data) {
        setVendors(response.data);
      } else {
        console.error('Failed to load vendors');
        toast({
          title: "Error",
          description: "Failed to load vendors. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading vendors:', error);
      toast({
        title: "Error",
        description: "Failed to load vendors. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingVendors(false);
    }
  };

  useEffect(() => {
    // Don't render if user doesn't have permission
    if (!canEdit()) {
      return null;
    }

    loadVendors();
  }, []);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm border-b border-border"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate("/dashboard")}
                className="btn-neumorphic"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
              <div className="h-6 w-px bg-border" />
              <div>
                <h1 className="text-lg font-semibold text-foreground">
                  Create New RFQ
                </h1>
                <p className="text-sm text-muted-foreground">
                  Request quotes from multiple vendors
                </p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Security Notice */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="card-neumorphic p-4 bg-primary/5 border-primary/20">
            <div className="flex items-start space-x-3">
              <Shield className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-foreground">
                  RFQ Process
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Your RFQ will be sent to selected vendors via secure email
                  invitations. Vendors can submit bids through public forms
                  without requiring system access. All submissions will be
                  tracked and logged for audit purposes.
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Draft Loading Indicator */}
        {isLoadingDraft && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Alert>
              <FileText className="h-4 w-4" />
              <AlertTitle>Loading Draft</AlertTitle>
              <AlertDescription>
                Loading your saved RFQ draft...
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* Draft Loaded Indicator */}
        {currentDraft && !isLoadingDraft && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Alert>
              <FileText className="h-4 w-4" />
              <AlertTitle>Draft Loaded</AlertTitle>
              <AlertDescription>
                Resumed RFQ draft "{currentDraft.title}" last saved on {currentDraft.lastSaved.toLocaleString()}.
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* RFQ Creation Form */}
        {!isLoadingDraft && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <RFQCreationForm
              onSubmit={handleSubmit}
              vendors={vendors}
              templates={[]}
              isLoading={isSubmitting || isLoadingVendors}
              initialDraft={currentDraft || undefined}
              onSaveDraft={saveDraft}
            />
          </motion.div>
        )}

        {/* Help Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8"
        >
          <div className="card-neumorphic p-6">
            <h3 className="text-lg font-semibold text-foreground mb-4">
              RFQ Best Practices
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-foreground mb-2">
                  Creating Effective RFQs
                </h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Provide clear and detailed item descriptions</li>
                  <li>• Set realistic submission deadlines</li>
                  <li>• Include all necessary specifications</li>
                  <li>• Select vendors with relevant expertise</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-foreground mb-2">
                  What Happens Next?
                </h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Vendors receive email invitations</li>
                  <li>• Submissions are collected and analyzed</li>
                  <li>• AI recommendations help with selection</li>
                  <li>• Generate quotes for client approval</li>
                </ul>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default RFQCreate;
