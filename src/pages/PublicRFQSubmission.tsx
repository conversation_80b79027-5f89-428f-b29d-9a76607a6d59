import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { AlertCircle, Loader2, CheckCircle, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PublicSubmissionForm, PublicRFQData, BidSubmissionData } from '@/components/rfq/PublicSubmissionForm';
import { SubmissionStatusTracker } from '@/components/rfq/SubmissionStatusTracker';
import { toast } from 'sonner';

interface InvitationData {
  id: number;
  rfq_id: number;
  vendor_id: number;
  vendor_name: string;
  vendor_email: string;
  status: string;
  sent_at: string;
  viewed_at?: string;
}

interface TokenValidationResponse {
  success: boolean;
  data?: {
    invitation?: InvitationData;
    has_existing_submission: boolean;
    existing_submission_id?: number;
  };
  message?: string;
}

interface SubmissionStatusResponse {
  token_valid: boolean;
  rfq_title: string;
  rfq_due_date: string;
  rfq_status: string;
  vendor_name: string;
  invitation_status: string;
  has_submission: boolean;
  submission_id?: number;
  can_submit: boolean;
  can_modify: boolean;
}

const PublicRFQSubmission: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rfqData, setRfqData] = useState<PublicRFQData | null>(null);
  const [existingSubmission, setExistingSubmission] = useState<Record<string, unknown> | null>(null);
  const [submissionStatus, setSubmissionStatus] = useState<SubmissionStatusResponse | null>(null);
  const [submitted, setSubmitted] = useState(false);
  const [showStatusTracker, setShowStatusTracker] = useState(false);

  // Validate token and load RFQ data
  useEffect(() => {
    const loadRFQData = async () => {
      if (!token) {
        setError('Invalid submission link');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // First validate the token
        const tokenResponse = await fetch(`/api/public/rfq/${token}/validate`);
        const tokenData: TokenValidationResponse = await tokenResponse.json();

        if (!tokenResponse.ok || !tokenData.success) {
          setError(tokenData.message || 'Invalid or expired submission link');
          setLoading(false);
          return;
        }

        // Get RFQ details for submission
        const rfqResponse = await fetch(`/api/public/rfq/${token}`);
        const rfqResult = await rfqResponse.json();

        if (!rfqResponse.ok) {
          setError(rfqResult.message || 'Failed to load RFQ details');
          setLoading(false);
          return;
        }

        setRfqData(rfqResult.data);

        // Get submission status
        const statusResponse = await fetch(`/api/public/rfq/${token}/status`);
        const statusResult = await statusResponse.json();

        if (statusResponse.ok) {
          setSubmissionStatus(statusResult.data);
        }

        // Load existing submission if available
        if (tokenData.data?.has_existing_submission && tokenData.data?.existing_submission_id) {
          try {
            const submissionResponse = await fetch(`/api/public/rfq/${token}/submission`);
            const submissionResult = await submissionResponse.json();

            if (submissionResponse.ok) {
              setExistingSubmission(submissionResult.data);
            }
          } catch (submissionError) {
            console.warn('Could not load existing submission:', submissionError);
          }
        }

        setLoading(false);
      } catch (err) {
        console.error('Error loading RFQ data:', err);
        setError('Failed to load RFQ information. Please try again.');
        setLoading(false);
      }
    };

    loadRFQData();
  }, [token]);

  const handleSubmission = async (bidData: BidSubmissionData) => {
    if (!token || !rfqData) return;

    try {
      setSubmitting(true);
      setError(null);

      // Create FormData for file upload
      const formData = new FormData();
      
      // Add basic submission data
      formData.append('rfq_id', rfqData.rfq_id.toString());
      formData.append('invitation_token', token);
      formData.append('bidItems', JSON.stringify(bidData.bidItems));
      formData.append('bidData', JSON.stringify(bidData.bidData));
      formData.append('currency', bidData.currency);
      formData.append('payment_terms', bidData.payment_terms);
      formData.append('validity_period', bidData.validity_period.toString());
      formData.append('additional_notes', bidData.additional_notes);

      if (bidData.delivery_days !== null && bidData.delivery_days !== undefined) {
        formData.append('delivery_days', bidData.delivery_days.toString());
      }

      // Add partial selection fields
      if (bidData.allows_partial_selection !== null && bidData.allows_partial_selection !== undefined) {
        formData.append('allows_partial_selection', bidData.allows_partial_selection.toString());
      }
      
      if (bidData.partial_selection_notes) {
        formData.append('partial_selection_notes', bidData.partial_selection_notes);
      }

      // Add file attachments
      bidData.attachments.forEach((file, index) => {
        formData.append('attachments', file);
      });

      // Determine if this is an update or new submission
      const isUpdate = rfqData.has_existing_submission && existingSubmission;
      const url = isUpdate 
        ? `/api/public/rfq/submission/${existingSubmission.id}`
        : '/api/public/rfq/submission';
      
      const method = isUpdate ? 'PUT' : 'POST';

      if (isUpdate) {
        formData.append('token', token);
      }

      const response = await fetch(url, {
        method,
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to submit bid');
      }

      setSubmitted(true);
      toast.success(
        isUpdate 
          ? 'Your bid has been updated successfully!' 
          : 'Your bid has been submitted successfully!'
      );

    } catch (err: unknown) {
      console.error('Submission error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to submit bid. Please try again.';
      setError(errorMessage);
      toast.error('Failed to submit bid. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleBackToStatus = () => {
    setSubmitted(false);
    setShowStatusTracker(true);
  };

  const handleEditSubmission = () => {
    setShowStatusTracker(false);
    setSubmitted(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="card-neumorphic w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">Loading RFQ details...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="card-neumorphic w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="w-5 h-5" />
              <span>Access Error</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-4">
                Please check your submission link or contact the sender for assistance.
              </p>
              <Button
                onClick={() => navigate('/')}
                variant="outline"
                className="btn-neumorphic"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go to Homepage
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="w-full max-w-md"
        >
          <Card className="card-neumorphic">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="w-5 h-5" />
                <span>Submission Successful</span>
              </CardTitle>
              <CardDescription>
                Your bid has been submitted successfully
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  {rfqData?.has_existing_submission 
                    ? 'Your bid has been updated and the procurement team has been notified.'
                    : 'Your bid has been submitted and the procurement team has been notified.'
                  }
                </AlertDescription>
              </Alert>
              
              <div className="space-y-2 text-sm">
                <p><strong>RFQ:</strong> {rfqData?.title}</p>
                <p><strong>Vendor:</strong> {rfqData?.vendor_name}</p>
                <p><strong>Submitted:</strong> {new Date().toLocaleString()}</p>
              </div>

              <div className="text-center space-y-2">
                <Button
                  onClick={handleBackToStatus}
                  className="btn-neumorphic gradient-primary text-primary-foreground w-full"
                >
                  View Submission Status
                </Button>
                <p className="text-xs text-muted-foreground">
                  You can modify your submission until the deadline if needed.
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  if (!rfqData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="card-neumorphic w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertCircle className="w-8 h-8 text-red-500 mb-4" />
            <p className="text-muted-foreground">No RFQ data available</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show status tracker if requested or if there's an existing submission and we're not editing
  if (showStatusTracker || (rfqData.has_existing_submission && !submitted)) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Submission Status
            </h1>
            <p className="text-gray-600">
              Track your bid for: <span className="font-semibold">{rfqData.title}</span>
            </p>
          </motion.div>

          <SubmissionStatusTracker
            token={token!}
            onEditSubmission={handleEditSubmission}
            showEditButton={true}
          />
        </div>
      </div>
    );
  }

  return (
    <PublicSubmissionForm
      rfq={rfqData}
      token={token!}
      onSubmit={handleSubmission}
      isLoading={submitting}
      existingSubmission={existingSubmission}
    />
  );
};

export default PublicRFQSubmission;