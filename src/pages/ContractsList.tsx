import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { 
  Plus, 
  FileText, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Calendar,
  Building2,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  RefreshCw,
  Download,
  SlidersHorizontal,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { fetchContractsAsync, setFilters, clearFilters } from '@/store/slices/contractsSlice';
import { RootState, AppDispatch } from '@/store';
import { useAuth } from '@/hooks/useAuth';
import { toast } from '@/components/ui/sonner';
import { useDebounce } from '@/hooks/useDebounce';

export default function ContractsList() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [searchParams, setSearchParams] = useSearchParams();
  const { canEdit } = useAuth();
  
  const { contracts, isLoading, error, filters, pagination } = useSelector((state: RootState) => state.contracts);
  
  // Debug logging
  console.log('=== CONTRACTS LIST STATE ===');
  console.log('Contracts:', contracts);
  console.log('IsLoading:', isLoading);
  console.log('Error:', error);
  console.log('Pagination:', pagination);
  
  // Local state
  const [searchTerm, setSearchTerm] = useState(filters.search || '');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
  const [selectedVendor, setSelectedVendor] = useState(filters.vendor || '');
  
  // Debounced search term
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Callback functions
  const handleFilterChange = useCallback(
    (filterType: string, value: string) => {
      dispatch(setFilters({ [filterType]: value }));
      
      // Update local state
      if (filterType === 'status') setSelectedStatus(value);
      if (filterType === 'vendor') setSelectedVendor(value);
    },
    [dispatch]
  );

  // Initialize from URL params
  useEffect(() => {
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const vendor = searchParams.get('vendor') || '';

    setSearchTerm(search);
    setSelectedStatus(status);
    setSelectedVendor(vendor);

    dispatch(setFilters({ search, status, vendor }));
  }, [dispatch, searchParams]);

  // Fetch contracts when filters change
  useEffect(() => {
    console.log('=== CONTRACTS LIST: Fetching contracts ===');
    console.log('Filters:', filters);
    dispatch(fetchContractsAsync({ filters }))
      .then((result) => {
        console.log('Contracts fetch result:', result);
      })
      .catch((error) => {
        console.error('Contracts fetch error:', error);
      });
  }, [dispatch, filters]);

  // Update filters when debounced search term changes
  useEffect(() => {
    if (debouncedSearchTerm !== filters.search) {
      handleFilterChange('search', debouncedSearchTerm);
    }
  }, [debouncedSearchTerm, filters.search, handleFilterChange]);

  // Update URL params when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    if (filters.search) params.set('search', filters.search);
    if (filters.status) params.set('status', filters.status);
    if (filters.vendor) params.set('vendor', filters.vendor);

    setSearchParams(params, { replace: true });
  }, [filters, setSearchParams]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'signed':
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      case 'draft':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'terminated':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'signed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'terminated':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const handleClearFilters = useCallback(() => {
    setSearchTerm('');
    setSelectedStatus('');
    setSelectedVendor('');
    dispatch(clearFilters());
  }, [dispatch]);

  const handleRefresh = useCallback(() => {
    dispatch(fetchContractsAsync({ filters }));
    toast.success('Contracts refreshed');
  }, [dispatch, filters]);

  // Memoized filtered contracts count
  const filteredContractsCount = useMemo(() => {
    return contracts.length;
  }, [contracts.length]);

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return !!(filters.search || filters.status || filters.vendor);
  }, [filters]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getTimeRemaining = (endDate: string) => {
    const now = new Date();
    const end = new Date(endDate);
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Expired';
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return '1 day';
    return `${diffDays} days`;
  };

  if (isLoading && contracts.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="card-neumorphic p-8">
          <div className="flex items-center space-x-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="text-muted-foreground">Loading contracts...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold text-foreground">Contracts</h1>
            <p className="text-muted-foreground mt-2">
              {filteredContractsCount} contract{filteredContractsCount !== 1 ? 's' : ''}
              {hasActiveFilters ? ' found' : ' total'}
            </p>
          </div>

          <div className="flex items-center space-x-3">
            {/* Action Buttons */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              className="btn-neumorphic"
            >
              <RefreshCw
                className={`w-4 h-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => toast.info('Export functionality coming soon')}
              className="btn-neumorphic"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>

            {canEdit() && (
              <Button
                onClick={() => navigate('/contracts/create')}
                className="btn-neumorphic gradient-primary text-primary-foreground"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Contract
              </Button>
            )}
          </div>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="card-neumorphic p-6"
        >
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search contracts by title, vendor, or ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-neumorphic pl-10 pr-10"
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchTerm("")}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
                >
                  <X className="w-3 h-3" />
                </Button>
              )}
            </div>

            {/* Quick Filters */}
            <div className="flex items-center space-x-2">
              <Select
                value={selectedStatus || "all-status"}
                onValueChange={(value) =>
                  handleFilterChange(
                    "status",
                    value === "all-status" ? "" : value
                  )
                }
              >
                <SelectTrigger className="w-32 btn-neumorphic">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all-status">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="signed">Signed</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                  <SelectItem value="terminated">Terminated</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className={`btn-neumorphic ${
                  showFilters ? "bg-primary/10 text-primary" : ""
                }`}
              >
                <SlidersHorizontal className="w-4 h-4 mr-2" />
                More
              </Button>

              {hasActiveFilters && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearFilters}
                  className="btn-neumorphic text-muted-foreground hover:text-foreground"
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear
                </Button>
              )}
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              transition={{ duration: 0.3 }}
              className="mt-6 pt-6 border-t border-border"
            >
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Contract Value
                  </label>
                  <Select defaultValue="any-value">
                    <SelectTrigger className="input-neumorphic">
                      <SelectValue placeholder="Any value" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="any-value">Any value</SelectItem>
                      <SelectItem value="0-10000">$0 - $10,000</SelectItem>
                      <SelectItem value="10000-50000">$10,000 - $50,000</SelectItem>
                      <SelectItem value="50000-100000">$50,000 - $100,000</SelectItem>
                      <SelectItem value="100000+">$100,000+</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Date Range
                  </label>
                  <Select defaultValue="any-time">
                    <SelectTrigger className="input-neumorphic">
                      <SelectValue placeholder="Any time" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="any-time">Any time</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">This week</SelectItem>
                      <SelectItem value="month">This month</SelectItem>
                      <SelectItem value="quarter">This quarter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Expiring Soon
                  </label>
                  <Select defaultValue="any-expiry">
                    <SelectTrigger className="input-neumorphic">
                      <SelectValue placeholder="Any" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="any-expiry">Any</SelectItem>
                      <SelectItem value="30-days">Within 30 days</SelectItem>
                      <SelectItem value="60-days">Within 60 days</SelectItem>
                      <SelectItem value="90-days">Within 90 days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button
                    onClick={handleClearFilters}
                    variant="outline"
                    className="btn-neumorphic w-full"
                  >
                    Reset Filters
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Loading Overlay */}
        {isLoading && contracts.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/50 backdrop-blur-sm z-50 flex items-center justify-center"
          >
            <div className="card-neumorphic p-6">
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span className="text-foreground">Updating contracts...</span>
              </div>
            </div>
          </motion.div>
        )}

          {/* Statistics Cards */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"
          >
            <Card className="card-neumorphic">
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Contracts</p>
                    <p className="text-2xl font-bold">{contracts.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-neumorphic">
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Active</p>
                    <p className="text-2xl font-bold">
                      {contracts.filter(c => c.status === 'active').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-neumorphic">
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <Clock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Draft</p>
                    <p className="text-2xl font-bold">
                      {contracts.filter(c => c.status === 'draft').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-neumorphic">
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                    <XCircle className="w-6 h-6 text-red-600 dark:text-red-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Terminated</p>
                    <p className="text-2xl font-bold">
                      {contracts.filter(c => c.status === 'terminated').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Contracts Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle>All Contracts</CardTitle>
                <CardDescription>
                  {filteredContractsCount} contract{filteredContractsCount !== 1 ? 's' : ''} found
                </CardDescription>
              </CardHeader>
              <CardContent>
                {contracts.length > 0 ? (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Contract</TableHead>
                          <TableHead>Vendor</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Start Date</TableHead>
                          <TableHead>End Date</TableHead>
                          <TableHead>Time Remaining</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {contracts.map((contract) => (
                          <TableRow key={contract.id}>
                            <TableCell>
                              <div>
                                <p className="font-medium">{contract.title}</p>
                                <p className="text-sm text-muted-foreground">
                                  ID: {contract.id}
                                </p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Building2 className="w-4 h-4 text-muted-foreground" />
                                <span>{contract.parties.vendor}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={getStatusColor(contract.status)}>
                                {getStatusIcon(contract.status)}
                                <span className="ml-1 capitalize">{contract.status}</span>
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Calendar className="w-4 h-4 text-muted-foreground" />
                                <span>{formatDate(contract.start_date)}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Calendar className="w-4 h-4 text-muted-foreground" />
                                <span>{formatDate(contract.end_date)}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                getTimeRemaining(contract.end_date) === 'Expired' 
                                  ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                              }`}>
                                {getTimeRemaining(contract.end_date)}
                              </span>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => navigate(`/contracts/${contract.id}`)}
                                >
                                  <Eye className="w-4 h-4" />
                                </Button>
                                {canEdit() && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => navigate(`/contracts/${contract.id}/edit`)}
                                  >
                                    <Edit className="w-4 h-4" />
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No contracts found</h3>
                    <p className="text-muted-foreground mb-4">
                      {hasActiveFilters
                        ? 'No contracts match your current filters.' 
                        : 'Get started by creating your first contract.'}
                    </p>
                    <div className="flex items-center justify-center space-x-3">
                      {hasActiveFilters && (
                        <Button
                          variant="outline"
                          onClick={handleClearFilters}
                          className="btn-neumorphic"
                        >
                          <X className="w-4 h-4 mr-2" />
                          Clear Filters
                        </Button>
                      )}
                      {canEdit() && (
                        <Button
                          onClick={() => navigate('/contracts/create')}
                          className="btn-neumorphic gradient-primary text-primary-foreground"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          {hasActiveFilters ? "Create Contract" : "Create First Contract"}
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
    </>
  );
}
