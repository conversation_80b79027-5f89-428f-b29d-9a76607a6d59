import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { RootState, AppDispatch } from '@/store';
import { 
  fetchAIPredictions, 
  fetchAIModels,
  setSelectedVendors,
  clearError 
} from '@/store/slices/analyticsSlice';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Target,
  DollarSign,
  Lightbulb,
  Search,
  Filter,
  Zap,
  Info,
  BarChart3
} from 'lucide-react';
import { format } from 'date-fns';

const AIInsights: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { toast } = useToast();
  
  const { aiInsights } = useSelector((state: RootState) => state.analytics);
  const [searchTerm, setSearchTerm] = useState('');
  const [riskFilter, setRiskFilter] = useState('all');

  useEffect(() => {
    dispatch(fetchAIModels());
    dispatch(fetchAIPredictions([])); // Fetch all predictions initially
  }, [dispatch]);

  const mockVendors = [
    { id: '1', name: 'TechCorp Solutions' },
    { id: '2', name: 'Global Services Ltd' },
    { id: '3', name: 'Innovation Partners' },
    { id: '4', name: 'Digital Solutions Inc' },
    { id: '5', name: 'Enterprise Systems' },
    { id: '6', name: 'Legacy Systems Co' },
  ];

  const filteredPredictions = aiInsights.predictions.filter(prediction => {
    const matchesSearch = prediction.vendorName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRisk = riskFilter === 'all' || 
      (riskFilter === 'high' && prediction.riskScore >= 70) ||
      (riskFilter === 'medium' && prediction.riskScore >= 30 && prediction.riskScore < 70) ||
      (riskFilter === 'low' && prediction.riskScore < 30);
    
    return matchesSearch && matchesRisk;
  });

  const handleVendorSelect = (vendorId: string) => {
    const currentSelected = aiInsights.selectedVendors;
    const newSelected = currentSelected.includes(vendorId)
      ? currentSelected.filter(id => id !== vendorId)
      : [...currentSelected, vendorId];
    
    dispatch(setSelectedVendors(newSelected));
    dispatch(fetchAIPredictions(newSelected));
  };

  const generateInsights = () => {
    dispatch(fetchAIPredictions(aiInsights.selectedVendors));
    toast({
      title: 'AI Analysis Started',
      description: 'Generating insights for selected vendors...',
    });
  };

  const getRiskColor = (riskScore: number) => {
    if (riskScore >= 70) return 'text-red-600';
    if (riskScore >= 30) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getRiskBadgeVariant = (riskScore: number): "default" | "secondary" | "destructive" | "outline" => {
    if (riskScore >= 70) return 'destructive';
    if (riskScore >= 30) return 'secondary';
    return 'default';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold mb-2">AI Insights</h1>
          <p className="text-muted-foreground">
            AI-powered vendor risk assessment and performance predictions.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={generateInsights} disabled={aiInsights.loading}>
            <Brain className="h-4 w-4 mr-2" />
            Generate Insights
          </Button>
        </div>
      </div>

      {/* AI Models Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="h-5 w-5 mr-2" />
            AI Models Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {aiInsights.models.map((model) => (
              <div key={model.type} className="p-3 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">
                    {model.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </h4>
                  <Badge variant="outline" className="text-xs">
                    v{model.version}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Accuracy:</span>
                    <span className="font-medium">{(model.accuracy * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={model.accuracy * 100} className="h-2" />
                  <p className="text-xs text-muted-foreground">
                    Last trained: {format(model.lastTrained, 'PPP')}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Vendor Selection & Filters */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Vendor Selection</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {mockVendors.map((vendor) => (
                <div key={vendor.id} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={vendor.id}
                    checked={aiInsights.selectedVendors.includes(vendor.id)}
                    onChange={() => handleVendorSelect(vendor.id)}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor={vendor.id} className="text-sm cursor-pointer">
                    {vendor.name}
                  </Label>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Filters</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="search">Search Vendors</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search by name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label>Risk Level</Label>
                <Select value={riskFilter} onValueChange={setRiskFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="high">High Risk</SelectItem>
                    <SelectItem value="medium">Medium Risk</SelectItem>
                    <SelectItem value="low">Low Risk</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Insights Content */}
        <div className="lg:col-span-3 space-y-6">
          {aiInsights.loading ? (
            <Card>
              <CardContent className="flex items-center justify-center py-12">
                <div className="text-center">
                  <Brain className="h-12 w-12 text-primary animate-pulse mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Analyzing Vendor Data</h3>
                  <p className="text-muted-foreground">AI models are processing vendor information...</p>
                </div>
              </CardContent>
            </Card>
          ) : filteredPredictions.length > 0 ? (
            <div className="space-y-4">
              {filteredPredictions.map((prediction) => (
                <motion.div
                  key={prediction.vendorId}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="grid grid-cols-1 lg:grid-cols-3 gap-4"
                >
                  {/* Risk Overview Card */}
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">{prediction.vendorName}</CardTitle>
                        <Badge variant={getRiskBadgeVariant(prediction.riskScore)}>
                          {prediction.riskScore >= 70 ? 'High Risk' : 
                           prediction.riskScore >= 30 ? 'Medium Risk' : 'Low Risk'}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Risk Score</span>
                            <span className={`text-lg font-bold ${getRiskColor(prediction.riskScore)}`}>
                              {prediction.riskScore}%
                            </span>
                          </div>
                          <Progress value={prediction.riskScore} className="h-2" />
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Confidence</span>
                            <span className="text-sm text-muted-foreground">
                              {prediction.confidenceLevel}%
                            </span>
                          </div>
                          <Progress value={prediction.confidenceLevel} className="h-1" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Predictions Card */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center">
                        <Target className="h-4 w-4 mr-2" />
                        Predictions
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <TrendingDown className="h-4 w-4 text-orange-500 mr-2" />
                            <span className="text-sm">Performance Decline</span>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">
                              {(prediction.predictions.performanceDecline.probability * 100).toFixed(0)}%
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {prediction.predictions.performanceDecline.timeframe}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                            <span className="text-sm">Contract Renewal</span>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">
                              {(prediction.predictions.contractRenewal.probability * 100).toFixed(0)}%
                            </p>
                            <p className="text-xs text-muted-foreground">Likely</p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <DollarSign className="h-4 w-4 text-blue-500 mr-2" />
                            <span className="text-sm">Cost Savings</span>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">
                              ${prediction.predictions.costOptimization.potentialSavings.toLocaleString()}
                            </p>
                            <p className="text-xs text-muted-foreground">Potential</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Recommendations Card */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center">
                        <Lightbulb className="h-4 w-4 mr-2" />
                        Recommendations
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <Alert>
                          <Info className="h-4 w-4" />
                          <AlertDescription className="text-sm">
                            {prediction.predictions.contractRenewal.recommendation}
                          </AlertDescription>
                        </Alert>
                        
                        {prediction.predictions.costOptimization.suggestions.length > 0 && (
                          <div className="space-y-1">
                            <h4 className="text-sm font-medium">Cost Optimization:</h4>
                            <ul className="text-sm text-muted-foreground space-y-1">
                              {prediction.predictions.costOptimization.suggestions.map((suggestion, index) => (
                                <li key={index} className="flex items-start">
                                  <span className="w-1 h-1 bg-muted-foreground rounded-full mt-2 mr-2 flex-shrink-0" />
                                  {suggestion}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Insights Available</h3>
                <p className="text-muted-foreground text-center max-w-md">
                  {aiInsights.selectedVendors.length === 0 
                    ? 'Select vendors from the sidebar to generate AI insights and predictions.'
                    : 'No vendors match your current filters. Try adjusting the search or risk level filter.'
                  }
                </p>
                {aiInsights.selectedVendors.length > 0 && (
                  <Button 
                    className="mt-4" 
                    onClick={generateInsights}
                    disabled={aiInsights.loading}
                  >
                    <Brain className="h-4 w-4 mr-2" />
                    Generate Insights
                  </Button>
                )}
              </CardContent>
            </Card>
          )}

          {/* Contributing Factors */}
          {filteredPredictions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Contributing Factors Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue={filteredPredictions[0]?.vendorId} className="space-y-4" >
                  <TabsList className="grid w-full grid-cols-auto" style={{display:"flex", justifyContent:"center", flexDirection:"row"}}>
                    {filteredPredictions.slice(0, 4).map((prediction) => (
                      <TabsTrigger key={prediction.vendorId} value={prediction.vendorId} className="text-xs">
                        {prediction.vendorName.split(' ')[0]}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                  
                  {filteredPredictions.map((prediction) => (
                    <TabsContent key={prediction.vendorId} value={prediction.vendorId}>
                      <div className="space-y-3">
                        {prediction.factors.map((factor, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex-1">
                              <h4 className="font-medium text-sm">{factor.factor}</h4>
                              <p className="text-xs text-muted-foreground mt-1">{factor.description}</p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className={`text-sm font-medium ${factor.impact > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {factor.impact > 0 ? '+' : ''}{(factor.impact * 100).toFixed(0)}%
                              </div>
                              {factor.impact > 0 ? (
                                <TrendingUp className="h-4 w-4 text-green-600" />
                              ) : (
                                <TrendingDown className="h-4 w-4 text-red-600" />
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIInsights;