import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { RootState, AppDispatch } from '@/store';
import { 
  fetchSystemSettingsAsync, 
  updateSystemSettingsAsync,
  fetchCustomFieldsAsync,
  createCustomFieldAsync,
  updateCustomFieldAsync,
  deleteCustomFieldAsync,
  fetchAuditLogsAsync,
  SystemSettings,
  CustomField
} from '@/store/slices/systemSlice';
import { Save, Plus, Trash2, Download } from 'lucide-react';

interface GeneralSettingsProps {
  settings: SystemSettings | null;
  onUpdate: (settings: Partial<SystemSettings>) => void;
  isLoading: boolean;
}

const GeneralSettings: React.FC<GeneralSettingsProps> = ({ settings, onUpdate, isLoading }) => {
  const [formData, setFormData] = useState({
    organizationName: '',
    contactEmail: '',
    contactPhone: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: '',
    },
  });

  useEffect(() => {
    if (settings) {
      setFormData({
        organizationName: settings.organizationName,
        contactEmail: settings.contactEmail,
        contactPhone: settings.contactPhone,
        address: settings.address,
      });
    }
  }, [settings]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate(formData);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>General Settings</CardTitle>
        <CardDescription>Update your organization's details and contact information.</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="orgName">Organization Name</Label>
              <Input
                id="orgName"
                value={formData.organizationName}
                onChange={(e) => setFormData({ ...formData, organizationName: e.target.value })}
                placeholder="Your Organization Name"
              />
            </div>
            <div>
              <Label htmlFor="contactEmail">Contact Email</Label>
              <Input
                id="contactEmail"
                type="email"
                value={formData.contactEmail}
                onChange={(e) => setFormData({ ...formData, contactEmail: e.target.value })}
                placeholder="<EMAIL>"
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="contactPhone">Contact Phone</Label>
            <Input
              id="contactPhone"
              value={formData.contactPhone}
              onChange={(e) => setFormData({ ...formData, contactPhone: e.target.value })}
              placeholder="+****************"
            />
          </div>

          <div className="space-y-2">
            <Label>Address</Label>
            <div className="grid grid-cols-1 gap-2">
              <Input
                value={formData.address.street}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  address: { ...formData.address, street: e.target.value }
                })}
                placeholder="Street Address"
              />
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <Input
                  value={formData.address.city}
                  onChange={(e) => setFormData({ 
                    ...formData, 
                    address: { ...formData.address, city: e.target.value }
                  })}
                  placeholder="City"
                />
                <Input
                  value={formData.address.state}
                  onChange={(e) => setFormData({ 
                    ...formData, 
                    address: { ...formData.address, state: e.target.value }
                  })}
                  placeholder="State"
                />
                <Input
                  value={formData.address.zipCode}
                  onChange={(e) => setFormData({ 
                    ...formData, 
                    address: { ...formData.address, zipCode: e.target.value }
                  })}
                  placeholder="ZIP Code"
                />
                <Input
                  value={formData.address.country}
                  onChange={(e) => setFormData({ 
                    ...formData, 
                    address: { ...formData.address, country: e.target.value }
                  })}
                  placeholder="Country"
                />
              </div>
            </div>
          </div>

          <Button type="submit" disabled={isLoading} className="neumorphic-button-primary">
            <Save className="mr-2 h-4 w-4" />
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

interface SecuritySettingsProps {
  settings: SystemSettings | null;
  onUpdate: (settings: Partial<SystemSettings>) => void;
  isLoading: boolean;
}

const SecuritySettings: React.FC<SecuritySettingsProps> = ({ settings, onUpdate, isLoading }) => {
  const [formData, setFormData] = useState({
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      expiryDays: 90,
      preventReuse: 5,
    },
  });

  useEffect(() => {
    if (settings) {
      setFormData({
        sessionTimeout: settings.sessionTimeout,
        maxLoginAttempts: settings.maxLoginAttempts,
        passwordPolicy: settings.passwordPolicy,
      });
    }
  }, [settings]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate(formData);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Security Settings</CardTitle>
        <CardDescription>Configure security policies and authentication settings.</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
              <Input
                id="sessionTimeout"
                type="number"
                min="5"
                max="480"
                value={formData.sessionTimeout}
                onChange={(e) => setFormData({ ...formData, sessionTimeout: parseInt(e.target.value) })}
              />
            </div>
            <div>
              <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
              <Input
                id="maxLoginAttempts"
                type="number"
                min="3"
                max="10"
                value={formData.maxLoginAttempts}
                onChange={(e) => setFormData({ ...formData, maxLoginAttempts: parseInt(e.target.value) })}
              />
            </div>
          </div>

          <div className="space-y-4">
            <Label className="text-base font-semibold">Password Policy</Label>
            
            <div>
              <Label htmlFor="minLength">Minimum Length</Label>
              <Input
                id="minLength"
                type="number"
                min="6"
                max="32"
                value={formData.passwordPolicy.minLength}
                onChange={(e) => setFormData({
                  ...formData,
                  passwordPolicy: { ...formData.passwordPolicy, minLength: parseInt(e.target.value) }
                })}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="requireUppercase"
                  checked={formData.passwordPolicy.requireUppercase}
                  onCheckedChange={(checked) => setFormData({
                    ...formData,
                    passwordPolicy: { ...formData.passwordPolicy, requireUppercase: checked }
                  })}
                />
                <Label htmlFor="requireUppercase">Require Uppercase</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="requireLowercase"
                  checked={formData.passwordPolicy.requireLowercase}
                  onCheckedChange={(checked) => setFormData({
                    ...formData,
                    passwordPolicy: { ...formData.passwordPolicy, requireLowercase: checked }
                  })}
                />
                <Label htmlFor="requireLowercase">Require Lowercase</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="requireNumbers"
                  checked={formData.passwordPolicy.requireNumbers}
                  onCheckedChange={(checked) => setFormData({
                    ...formData,
                    passwordPolicy: { ...formData.passwordPolicy, requireNumbers: checked }
                  })}
                />
                <Label htmlFor="requireNumbers">Require Numbers</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="requireSpecialChars"
                  checked={formData.passwordPolicy.requireSpecialChars}
                  onCheckedChange={(checked) => setFormData({
                    ...formData,
                    passwordPolicy: { ...formData.passwordPolicy, requireSpecialChars: checked }
                  })}
                />
                <Label htmlFor="requireSpecialChars">Require Special Characters</Label>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="expiryDays">Password Expiry (days)</Label>
                <Input
                  id="expiryDays"
                  type="number"
                  min="30"
                  max="365"
                  value={formData.passwordPolicy.expiryDays}
                  onChange={(e) => setFormData({
                    ...formData,
                    passwordPolicy: { ...formData.passwordPolicy, expiryDays: parseInt(e.target.value) }
                  })}
                />
              </div>
              <div>
                <Label htmlFor="preventReuse">Prevent Reuse (last N passwords)</Label>
                <Input
                  id="preventReuse"
                  type="number"
                  min="0"
                  max="24"
                  value={formData.passwordPolicy.preventReuse}
                  onChange={(e) => setFormData({
                    ...formData,
                    passwordPolicy: { ...formData.passwordPolicy, preventReuse: parseInt(e.target.value) }
                  })}
                />
              </div>
            </div>
          </div>

          <Button type="submit" disabled={isLoading} className="neumorphic-button-primary">
            <Save className="mr-2 h-4 w-4" />
            {isLoading ? 'Saving...' : 'Save Security Settings'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

interface CustomFieldsProps {
  customFields: CustomField[];
  onCreateField: (field: Omit<CustomField, 'id' | 'createdAt' | 'createdBy'>) => void;
  onUpdateField: (id: string, field: Partial<CustomField>) => void;
  onDeleteField: (id: string) => void;
  isLoading: boolean;
}

const CustomFields: React.FC<CustomFieldsProps> = ({ 
  customFields, 
  onCreateField, 
  onUpdateField, 
  onDeleteField, 
  isLoading 
}) => {
  const [isCreating, setIsCreating] = useState(false);
  const [newField, setNewField] = useState({
    entityType: 'vendor' as 'vendor' | 'contract' | 'invoice',
    fieldName: '',
    fieldLabel: '',
    fieldType: 'text' as 'text' | 'number' | 'date' | 'select' | 'boolean' | 'textarea',
    required: false,
    helpText: '',
    options: [] as { value: string; label: string }[],
  });

  const handleCreateField = () => {
    if (!newField.fieldName || !newField.fieldLabel) return;
    
    onCreateField({
      ...newField,
      validation: {},
      defaultValue: null,
      order: customFields.length + 1,
      active: true,
    });
    
    setNewField({
      entityType: 'vendor',
      fieldName: '',
      fieldLabel: '',
      fieldType: 'text',
      required: false,
      helpText: '',
      options: [],
    });
    setIsCreating(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Custom Fields</CardTitle>
            <CardDescription>Create additional fields for vendors, contracts, and invoices.</CardDescription>
          </div>
          <Button onClick={() => setIsCreating(true)} className="neumorphic-button">
            <Plus className="mr-2 h-4 w-4" />
            Add Field
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isCreating && (
          <Card className="mb-4 border-dashed">
            <CardContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Entity Type</Label>
                  <Select 
                    value={newField.entityType} 
                    onValueChange={(value: 'vendor' | 'contract' | 'invoice') => 
                      setNewField({ ...newField, entityType: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="vendor">Vendor</SelectItem>
                      <SelectItem value="contract">Contract</SelectItem>
                      <SelectItem value="invoice">Invoice</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Field Type</Label>
                  <Select 
                    value={newField.fieldType} 
                    onValueChange={(value: any) => setNewField({ ...newField, fieldType: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text">Text</SelectItem>
                      <SelectItem value="number">Number</SelectItem>
                      <SelectItem value="date">Date</SelectItem>
                      <SelectItem value="select">Select</SelectItem>
                      <SelectItem value="boolean">Boolean</SelectItem>
                      <SelectItem value="textarea">Textarea</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Field Name (API)</Label>
                  <Input
                    value={newField.fieldName}
                    onChange={(e) => setNewField({ ...newField, fieldName: e.target.value })}
                    placeholder="field_name"
                  />
                </div>
                <div>
                  <Label>Field Label</Label>
                  <Input
                    value={newField.fieldLabel}
                    onChange={(e) => setNewField({ ...newField, fieldLabel: e.target.value })}
                    placeholder="Field Label"
                  />
                </div>
                <div className="md:col-span-2">
                  <Label>Help Text</Label>
                  <Input
                    value={newField.helpText}
                    onChange={(e) => setNewField({ ...newField, helpText: e.target.value })}
                    placeholder="Optional help text for users"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={newField.required}
                    onCheckedChange={(checked) => setNewField({ ...newField, required: checked })}
                  />
                  <Label>Required Field</Label>
                </div>
              </div>
              <div className="flex justify-end space-x-2 mt-4">
                <Button variant="outline" onClick={() => setIsCreating(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateField} disabled={isLoading}>
                  Create Field
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="space-y-4">
          {customFields.map((field) => (
            <Card key={field.id}>
              <CardContent className="pt-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="font-medium">{field.fieldLabel}</span>
                      <Badge variant="outline">{field.entityType}</Badge>
                      <Badge variant="secondary">{field.fieldType}</Badge>
                      {field.required && <Badge variant="destructive">Required</Badge>}
                    </div>
                    <p className="text-sm text-gray-600">API Name: {field.fieldName}</p>
                    {field.helpText && (
                      <p className="text-sm text-gray-500 mt-1">{field.helpText}</p>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDeleteField(field.id)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export const AdminSettings: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { toast } = useToast();
  
  const { 
    settings, 
    customFields, 
    auditLogs,
    isLoading, 
    customFieldsLoading, 
    auditLogsLoading,
    error 
  } = useSelector((state: RootState) => state.system);

  useEffect(() => {
    dispatch(fetchSystemSettingsAsync());
    dispatch(fetchCustomFieldsAsync());
    dispatch(fetchAuditLogsAsync());
  }, [dispatch]);

  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: error,
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  const handleUpdateSettings = async (updatedSettings: Partial<SystemSettings>) => {
    try {
      await dispatch(updateSystemSettingsAsync(updatedSettings)).unwrap();
      toast({
        title: 'Settings updated',
        description: 'System settings have been updated successfully.',
      });
    } catch (error) {
      toast({
        title: 'Failed to update settings',
        description: error as string,
        variant: 'destructive',
      });
    }
  };

  const handleCreateCustomField = async (fieldData: Omit<CustomField, 'id' | 'createdAt' | 'createdBy'>) => {
    try {
      await dispatch(createCustomFieldAsync(fieldData)).unwrap();
      toast({
        title: 'Custom field created',
        description: 'Custom field has been created successfully.',
      });
    } catch (error) {
      toast({
        title: 'Failed to create custom field',
        description: error as string,
        variant: 'destructive',
      });
    }
  };

  const handleUpdateCustomField = async (id: string, fieldData: Partial<CustomField>) => {
    try {
      await dispatch(updateCustomFieldAsync({ id, fieldData })).unwrap();
      toast({
        title: 'Custom field updated',
        description: 'Custom field has been updated successfully.',
      });
    } catch (error) {
      toast({
        title: 'Failed to update custom field',
        description: error as string,
        variant: 'destructive',
      });
    }
  };

  const handleDeleteCustomField = async (id: string) => {
    try {
      await dispatch(deleteCustomFieldAsync(id)).unwrap();
      toast({
        title: 'Custom field deleted',
        description: 'Custom field has been deleted successfully.',
      });
    } catch (error) {
      toast({
        title: 'Failed to delete custom field',
        description: error as string,
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <header className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800">System Settings</h1>
        <p className="text-gray-600 mt-1">Configure system-wide settings and preferences.</p>
      </header>
      
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="custom_fields">Custom Fields</TabsTrigger>
          <TabsTrigger value="audit">Audit Logs</TabsTrigger>
        </TabsList>
        
        <TabsContent value="general" className="mt-4">
          <GeneralSettings 
            settings={settings}
            onUpdate={handleUpdateSettings}
            isLoading={isLoading}
          />
        </TabsContent>
        
        <TabsContent value="security" className="mt-4">
          <SecuritySettings 
            settings={settings}
            onUpdate={handleUpdateSettings}
            isLoading={isLoading}
          />
        </TabsContent>
        
        <TabsContent value="custom_fields" className="mt-4">
          <CustomFields
            customFields={customFields}
            onCreateField={handleCreateCustomField}
            onUpdateField={handleUpdateCustomField}
            onDeleteField={handleDeleteCustomField}
            isLoading={customFieldsLoading}
          />
        </TabsContent>
        
        <TabsContent value="audit" className="mt-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Audit Logs</CardTitle>
                  <CardDescription>View system activity and administrative actions.</CardDescription>
                </div>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export Logs
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {auditLogsLoading ? (
                <div className="text-center py-8">Loading audit logs...</div>
              ) : (
                <div className="space-y-4">
                  {auditLogs.map((log) => (
                    <div key={log.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <span className="font-medium">{log.action.replace(/_/g, ' ')}</span>
                          <span className="text-sm text-gray-500 ml-2">by {log.userName}</span>
                        </div>
                        <span className="text-sm text-gray-500">
                          {new Date(log.timestamp).toLocaleString()}
                        </span>
                      </div>
                      {Object.keys(log.changes).length > 0 && (
                        <div className="text-sm text-gray-600">
                          Changes: {Object.entries(log.changes).map(([key, change]) => (
                            <span key={key} className="mr-4">
                              {key}: {String(change.old)} → {String(change.new)}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};