import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import { AISettings } from '../AISettings';
import aiSlice from '@/store/slices/aiSlice';

// Mock the AI service
jest.mock('@/services/ai/AIService');
jest.mock('@/services/ai/AIProviderFactory');

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      ai: aiSlice
    },
    preloadedState: {
      ai: {
        currentProvider: null,
        configurations: {
          openai: null,
          anthropic: null,
          gemini: null
        },
        isConfiguring: false,
        validationStatus: {
          openai: { isValidating: false, isValid: false, error: null },
          anthropic: { isValidating: false, isValid: false, error: null },
          gemini: { isValidating: false, isValid: false, error: null }
        },
        providerStatuses: {
          openai: { configured: false, available: false },
          anthropic: { configured: false, available: false },
          gemini: { configured: false, available: false }
        },
        usageStats: null,
        settingsOpen: false,
        selectedProviderForConfig: null,
        error: null,
        lastUpdated: null,
        ...initialState
      }
    }
  });
};

const renderWithProviders = (component: React.ReactElement, store = createMockStore()) => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('AISettings', () => {
  it('renders the AI settings page', () => {
    renderWithProviders(<AISettings />);
    
    expect(screen.getByText('AI Settings')).toBeInTheDocument();
    expect(screen.getByText('Configure AI providers for intelligent vendor recommendations and insights')).toBeInTheDocument();
  });

  it('displays provider cards', () => {
    renderWithProviders(<AISettings />);
    
    expect(screen.getByText('OpenAI')).toBeInTheDocument();
    expect(screen.getByText('Anthropic')).toBeInTheDocument();
    expect(screen.getByText('Google Gemini')).toBeInTheDocument();
  });

  it('shows status overview cards', () => {
    renderWithProviders(<AISettings />);
    
    expect(screen.getByText('Active Provider')).toBeInTheDocument();
    expect(screen.getByText('Configured Providers')).toBeInTheDocument();
    expect(screen.getByText('Usage Today')).toBeInTheDocument();
  });

  it('displays getting started guide', () => {
    renderWithProviders(<AISettings />);
    
    expect(screen.getByText('Getting Started')).toBeInTheDocument();
    expect(screen.getByText('Choose an AI Provider')).toBeInTheDocument();
    expect(screen.getByText('Configure API Access')).toBeInTheDocument();
    expect(screen.getByText('Start Using AI Features')).toBeInTheDocument();
  });

  it('shows usage statistics when available', () => {
    const storeWithUsage = createMockStore({
      usageStats: {
        requestsToday: 25,
        tokensUsed: 1500,
        remainingQuota: 8500
      }
    });

    renderWithProviders(<AISettings />, storeWithUsage);
    
    expect(screen.getByText('Usage Statistics')).toBeInTheDocument();
    expect(screen.getByText('25')).toBeInTheDocument(); // requests today
    expect(screen.getByText('1,500')).toBeInTheDocument(); // tokens used
  });

  it('displays error alert when there is an error', () => {
    const storeWithError = createMockStore({
      error: 'Failed to configure provider'
    });

    renderWithProviders(<AISettings />, storeWithError);
    
    expect(screen.getByText('Failed to configure provider')).toBeInTheDocument();
  });
});