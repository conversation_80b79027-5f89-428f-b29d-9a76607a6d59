import * as React from 'react';
import { useF<PERSON>, Controller, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/sonner';
import { Plus, Trash2 } from 'lucide-react';

const templateSchema = z.object({
  name: z.string().min(1),
  defaultCurrency: z.string(),
  defaultTaxRate: z.number().min(0),
});

const automationSchema = z.object({
  autoGenerateOnMilestone: z.boolean(),
  autoApprovalThreshold: z.number().min(0),
});

const formSchema = z.object({
  templates: z.array(templateSchema),
  automation: automationSchema,
});

type FormValues = z.infer<typeof formSchema>;

const Settings: React.FC = () => {
  const { control, handleSubmit, reset } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      templates: [{ name: 'Standard', defaultCurrency: 'USD', defaultTaxRate: 10 }],
      automation: { autoGenerateOnMilestone: true, autoApprovalThreshold: 5000 },
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'templates',
  });

  const onSubmit = (data: FormValues) => {
    // Save settings to backend or local storage
    toast.success('Settings saved successfully');
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">System Settings</h1>
      <Tabs defaultValue="invoices">
        <TabsList>
          <TabsTrigger value="invoices">Invoice Settings</TabsTrigger>
          {/* Add other tabs as needed */}
        </TabsList>
        <TabsContent value="invoices">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Invoice Templates</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className="grid grid-cols-4 gap-4 items-end">
                    <div>
                      <Label htmlFor={`templates.${index}.name`}>Name</Label>
                      <Controller name={`templates.${index}.name`} control={control} render={({ field }) => <Input {...field} />} />
                    </div>
                    <div>
                      <Label htmlFor={`templates.${index}.defaultCurrency`}>Currency</Label>
                      <Controller name={`templates.${index}.defaultCurrency`} control={control} render={({ field }) => <Input {...field} />} />
                    </div>
                    <div>
                      <Label htmlFor={`templates.${index}.defaultTaxRate`}>Tax Rate (%)</Label>
                      <Controller name={`templates.${index}.defaultTaxRate`} control={control} render={({ field }) => <Input type="number" {...field} onChange={e => field.onChange(parseFloat(e.target.value))} />} />
                    </div>
                    <Button type="button" variant="destructive" onClick={() => remove(index)}><Trash2 className="h-4 w-4" /></Button>
                  </div>
                ))}
                <Button type="button" variant="outline" onClick={() => append({ name: '', defaultCurrency: 'USD', defaultTaxRate: 0 })}><Plus className="mr-2 h-4 w-4" /> Add Template</Button>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Automation Rules</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="automation.autoGenerateOnMilestone">Auto-generate invoices on contract milestones</Label>
                  <Controller name="automation.autoGenerateOnMilestone" control={control} render={({ field }) => <Switch checked={field.value} onCheckedChange={field.onChange} />} />
                </div>
                <div>
                  <Label htmlFor="automation.autoApprovalThreshold">Auto-approval threshold ($)</Label>
                  <Controller name="automation.autoApprovalThreshold" control={control} render={({ field }) => <Input type="number" {...field} onChange={e => field.onChange(parseFloat(e.target.value))} />} />
                </div>
              </CardContent>
            </Card>
            <Button type="submit">Save Settings</Button>
          </form>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;