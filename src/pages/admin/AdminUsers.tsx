import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, UserPlus, UserCheck } from 'lucide-react';
import { Dialog, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { RootState, AppDispatch } from '@/store';
import { 
  fetchUsersAsync, 
  inviteUserAsync, 
  updateUserRoleAsync, 
  updateUserStatusAsync,
  createUserAsync,
  setUserPasswordAsync,
  User 
} from '@/store/slices/authSlice';
import { InviteUserModal } from '@/components/admin/InviteUserModal';
import { CreateUserModal } from '@/components/admin/CreateUserModal';
import { UserManagementTable } from '@/components/admin/UserManagementTable';
import { BulkUserActions } from '@/components/admin/BulkUserActions';



export const AdminUsers: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { toast } = useToast();
  
  const { 
    users, 
    invitations, 
    usersLoading, 
    invitationsLoading, 
    error,
    user: currentUser 
  } = useSelector((state: RootState) => state.auth);

  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isCreateUserModalOpen, setIsCreateUserModalOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  useEffect(() => {
    dispatch(fetchUsersAsync());
  }, [dispatch]);

  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: error,
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  const handleInvite = async (email: string, role: 'admin' | 'manager' | 'viewer') => {
    try {
      await dispatch(inviteUserAsync({ email, role })).unwrap();
      toast({
        title: 'Invitation sent',
        description: `Invitation sent to ${email} successfully.`,
      });
      setIsInviteModalOpen(false);
    } catch (error) {
      toast({
        title: 'Failed to send invitation',
        description: error as string,
        variant: 'destructive',
      });
    }
  };

  const handleCreateUser = async (userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    role: 'admin' | 'manager' | 'viewer';
  }) => {
    try {
      await dispatch(createUserAsync(userData)).unwrap();
      toast({
        title: 'User created',
        description: `User ${userData.firstName} ${userData.lastName} created successfully.`,
      });
      setIsCreateUserModalOpen(false);
    } catch (error) {
      toast({
        title: 'Failed to create user',
        description: error as string,
        variant: 'destructive',
      });
    }
  };

  const handleSetPassword = async (userId: string, newPassword: string) => {
    try {
      await dispatch(setUserPasswordAsync({ userId, newPassword })).unwrap();
      toast({
        title: 'Password updated',
        description: 'User password has been updated successfully.',
      });
    } catch (error) {
      toast({
        title: 'Failed to update password',
        description: error as string,
        variant: 'destructive',
      });
    }
  };

  const handleRoleChange = async (userId: string, newRole: 'admin' | 'manager' | 'viewer') => {
    try {
      await dispatch(updateUserRoleAsync({ userId, newRole })).unwrap();
      toast({
        title: 'Role updated',
        description: 'User role has been updated successfully.',
      });
    } catch (error) {
      toast({
        title: 'Failed to update role',
        description: error as string,
        variant: 'destructive',
      });
    }
  };

  const handleStatusChange = async (userId: string, newStatus: 'active' | 'inactive') => {
    try {
      await dispatch(updateUserStatusAsync({ userId, newStatus })).unwrap();
      toast({
        title: 'Status updated',
        description: `User has been ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully.`,
      });
    } catch (error) {
      toast({
        title: 'Failed to update status',
        description: error as string,
        variant: 'destructive',
      });
    }
  };

  const handleBulkRoleChange = async (userIds: string[], newRole: 'admin' | 'manager' | 'viewer') => {
    for (const userId of userIds) {
      await handleRoleChange(userId, newRole);
    }
  };

  const handleBulkStatusChange = async (userIds: string[], newStatus: 'active' | 'inactive') => {
    for (const userId of userIds) {
      await handleStatusChange(userId, newStatus);
    }
  };

  const handleBulkInviteResend = async (userIds: string[]) => {
    // In a real app, this would resend invitations
    const pendingUsers = users.filter(user => userIds.includes(user.id) && user.status === 'pending');
    
    for (const user of pendingUsers) {
      try {
        await dispatch(inviteUserAsync({ email: user.email, role: user.role })).unwrap();
      } catch (error) {
        console.error(`Failed to resend invitation to ${user.email}:`, error);
      }
    }
    
    toast({
      title: 'Invitations resent',
      description: `Resent ${pendingUsers.length} invitation(s) successfully.`,
    });
  };

  const filteredUsers = users.filter((user) => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  if (usersLoading) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading users...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <header className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">User Management</h1>
          <p className="text-gray-600 mt-1">Create, invite, manage, and monitor user access.</p>
        </div>
        <div className="flex gap-3">
          <Dialog open={isCreateUserModalOpen} onOpenChange={setIsCreateUserModalOpen}>
            <DialogTrigger asChild>
              <Button className="neumorphic-button">
                <UserCheck className="mr-2 h-5 w-5" />
                Create User
              </Button>
            </DialogTrigger>
            <CreateUserModal onCreateUser={handleCreateUser} isLoading={usersLoading} />
          </Dialog>
          
          <Dialog open={isInviteModalOpen} onOpenChange={setIsInviteModalOpen}>
            <DialogTrigger asChild>
              <Button className="neumorphic-button">
                <UserPlus className="mr-2 h-5 w-5" />
                Invite User
              </Button>
            </DialogTrigger>
            <InviteUserModal onInvite={handleInvite} isLoading={invitationsLoading} />
          </Dialog>
        </div>
      </header>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="manager">Manager</SelectItem>
                <SelectItem value="viewer">Viewer</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      <div className="mb-4">
        <BulkUserActions
          users={filteredUsers}
          selectedUsers={selectedUsers}
          onSelectionChange={setSelectedUsers}
          onBulkRoleChange={handleBulkRoleChange}
          onBulkStatusChange={handleBulkStatusChange}
          onBulkInviteResend={handleBulkInviteResend}
          currentUser={currentUser}
        />
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>Users ({filteredUsers.length})</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <UserManagementTable 
            users={filteredUsers}
            onRoleChange={handleRoleChange}
            onStatusChange={handleStatusChange}
            onSetPassword={handleSetPassword}
            currentUser={currentUser}
            isPasswordLoading={usersLoading}
          />
        </CardContent>
      </Card>

      {/* Pending Invitations */}
      {invitations.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Pending Invitations ({invitations.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {invitations.map((invitation) => (
                <div key={invitation.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <span className="font-medium">{invitation.email}</span>
                    <span className="ml-2 text-sm text-gray-500">
                      as {invitation.role} • Expires {new Date(invitation.expiresAt).toLocaleDateString()}
                    </span>
                  </div>
                  <Badge variant="outline">Pending</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};