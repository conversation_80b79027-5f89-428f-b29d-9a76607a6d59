import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Brain, 
  Settings, 
  Activity, 
  AlertCircle, 
  CheckCircle, 
  RefreshCw,
  Zap,
  BarChart3
} from 'lucide-react';
import { AppDispatch } from '@/store';
import { AIProvider } from '@/services/ai/types';
import { AIProviderCard } from '@/components/ai/AIProviderCard';
import { AIConfigDialog } from '@/components/ai/AIConfigDialog';
import { ConfigurationBackup } from '@/components/ai/ConfigurationBackup';
import { ConfigurationValidator } from '@/components/ai/ConfigurationValidator';
import {
  fetchProviderStatuses,
  fetchUsageStats,
  switchProvider,
  loadStoredConfigurations,
  selectCurrentProvider,
  selectProviderStatuses,
  selectValidationStatus,
  selectUsageStats,
  selectIsConfiguring,
  selectAIError,
  clearError
} from '@/store/slices/aiSlice';

export const AISettings: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const currentProvider = useSelector(selectCurrentProvider);
  const providerStatuses = useSelector(selectProviderStatuses);
  const validationStatus = useSelector(selectValidationStatus);
  const usageStats = useSelector(selectUsageStats);
  const isConfiguring = useSelector(selectIsConfiguring);
  const error = useSelector(selectAIError);

  const [selectedProvider, setSelectedProvider] = useState<AIProvider | null>(null);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [configProvider, setConfigProvider] = useState<AIProvider | null>(null);

  useEffect(() => {
    // Load stored configurations first
    dispatch(loadStoredConfigurations());
    dispatch(fetchProviderStatuses());
    dispatch(fetchUsageStats());
  }, [dispatch]);

  const handleProviderSelect = async (provider: AIProvider) => {
    if (provider === currentProvider) return;
    
    if (providerStatuses[provider]?.configured) {
      try {
        await dispatch(switchProvider(provider)).unwrap();
        setSelectedProvider(provider);
      } catch (error) {
        // Error handled by slice
      }
    } else {
      // Need to configure first
      handleProviderConfigure(provider);
    }
  };

  const handleProviderConfigure = (provider: AIProvider) => {
    setConfigProvider(provider);
    setConfigDialogOpen(true);
  };

  const handleRefreshStatuses = () => {
    dispatch(fetchProviderStatuses());
    dispatch(fetchUsageStats());
  };

  const getProviderCount = () => {
    return Object.values(providerStatuses).filter(status => status.configured).length;
  };

  const getActiveProviderName = () => {
    if (!currentProvider) return 'None';
    const names = { openai: 'OpenAI', anthropic: 'Anthropic', gemini: 'Google Gemini' };
    return names[currentProvider];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Settings</h1>
          <p className="text-muted-foreground">
            Configure AI providers for intelligent vendor recommendations and insights
          </p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefreshStatuses}
          disabled={isConfiguring}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Status
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => dispatch(clearError())}
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Status Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Provider</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{getActiveProviderName()}</div>
            <p className="text-xs text-muted-foreground">
              Currently selected AI provider
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Configured Providers</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{getProviderCount()}/3</div>
            <p className="text-xs text-muted-foreground">
              Providers ready to use
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Usage Today</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {usageStats?.requestsToday || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              AI requests made today
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Usage Statistics */}
      {usageStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Usage Statistics
            </CardTitle>
            <CardDescription>
              Current usage metrics for your AI provider
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <p className="text-sm font-medium">Requests Today</p>
                <p className="text-2xl font-bold text-blue-600">
                  {usageStats.requestsToday}
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium">Tokens Used</p>
                <p className="text-2xl font-bold text-green-600">
                  {usageStats.tokensUsed.toLocaleString()}
                </p>
              </div>
              {usageStats.remainingQuota && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">Remaining Quota</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {usageStats.remainingQuota.toLocaleString()}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Provider Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            AI Providers
          </CardTitle>
          <CardDescription>
            Configure and manage your AI provider integrations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
            {(['openai', 'anthropic', 'gemini'] as AIProvider[]).map((provider) => (
              <AIProviderCard
                key={provider}
                provider={provider}
                isSelected={currentProvider === provider}
                isConfigured={providerStatuses[provider]?.configured || false}
                isAvailable={providerStatuses[provider]?.available || false}
                isValidating={validationStatus[provider]?.isValidating || false}
                validationError={validationStatus[provider]?.error}
                onSelect={handleProviderSelect}
                onConfigure={handleProviderConfigure}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Configuration Management */}
      <div className="grid gap-6 lg:grid-cols-2">
        <ConfigurationValidator />
        <ConfigurationBackup />
      </div>

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>Getting Started</CardTitle>
          <CardDescription>
            Follow these steps to set up AI-powered features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                1
              </div>
              <div>
                <h4 className="font-medium">Choose an AI Provider</h4>
                <p className="text-sm text-muted-foreground">
                  Select from OpenAI, Anthropic, or Google Gemini based on your needs and budget.
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                2
              </div>
              <div>
                <h4 className="font-medium">Configure API Access</h4>
                <p className="text-sm text-muted-foreground">
                  Enter your API key and configure model settings for optimal performance.
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                3
              </div>
              <div>
                <h4 className="font-medium">Start Using AI Features</h4>
                <p className="text-sm text-muted-foreground">
                  Access AI-powered vendor recommendations, risk analysis, and insights throughout the platform.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Dialog */}
      <AIConfigDialog
        isOpen={configDialogOpen}
        provider={configProvider}
        onClose={() => {
          setConfigDialogOpen(false);
          setConfigProvider(null);
          // Refresh statuses after configuration
          dispatch(fetchProviderStatuses());
          dispatch(fetchUsageStats());
        }}
      />
    </div>
  );
};