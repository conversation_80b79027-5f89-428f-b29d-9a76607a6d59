import * as React from 'react';
import { motion } from 'framer-motion';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  Settings, 
  Receipt, 
  Hash, 
  Percent, 
  Calendar, 
  Mail,
  Users,
  Save,
  RotateCcw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/sonner';
import { useAuth } from '@/hooks/useAuth';

const settingsSchema = z.object({
  // Invoice Numbering
  numberingScheme: z.enum(['sequential', 'date-based', 'custom']),
  numberPrefix: z.string().max(10, 'Prefix must be 10 characters or less'),
  numberSuffix: z.string().max(10, 'Suffix must be 10 characters or less'),
  startingNumber: z.number().min(1, 'Starting number must be at least 1'),
  
  // Tax Settings
  defaultTaxRate: z.number().min(0).max(100, 'Tax rate must be between 0 and 100'),
  taxCalculationMethod: z.enum(['inclusive', 'exclusive']),
  
  // Payment Terms
  defaultPaymentTerms: z.number().min(1, 'Payment terms must be at least 1 day'),
  latePaymentFee: z.number().min(0, 'Late payment fee must be 0 or greater'),
  latePaymentFeeType: z.enum(['fixed', 'percentage']),
  
  // Email Templates
  invoiceEmailSubject: z.string().min(1, 'Email subject is required'),
  invoiceEmailTemplate: z.string().min(1, 'Email template is required'),
  reminderEmailSubject: z.string().min(1, 'Reminder subject is required'),
  reminderEmailTemplate: z.string().min(1, 'Reminder template is required'),
  
  // Approval Workflow
  requireApproval: z.boolean(),
  approvalThreshold: z.number().min(0, 'Approval threshold must be 0 or greater'),
  multiLevelApproval: z.boolean(),
  
  // Branding
  companyLogo: z.string().optional(),
  companyName: z.string().min(1, 'Company name is required'),
  companyAddress: z.string().min(1, 'Company address is required'),
  companyPhone: z.string().optional(),
  companyEmail: z.string().email('Invalid email address'),
});

type SettingsFormData = z.infer<typeof settingsSchema>;

const defaultSettings: SettingsFormData = {
  numberingScheme: 'sequential',
  numberPrefix: 'INV-',
  numberSuffix: '',
  startingNumber: 1000,
  defaultTaxRate: 10,
  taxCalculationMethod: 'exclusive',
  defaultPaymentTerms: 30,
  latePaymentFee: 5,
  latePaymentFeeType: 'percentage',
  invoiceEmailSubject: 'Invoice #{invoiceNumber} from {companyName}',
  invoiceEmailTemplate: 'Dear {vendorName},\n\nPlease find attached invoice #{invoiceNumber} for the amount of {totalAmount}.\n\nPayment is due within {paymentTerms} days.\n\nThank you for your business.\n\nBest regards,\n{companyName}',
  reminderEmailSubject: 'Payment Reminder - Invoice #{invoiceNumber}',
  reminderEmailTemplate: 'Dear {vendorName},\n\nThis is a friendly reminder that invoice #{invoiceNumber} for {totalAmount} is now overdue.\n\nPlease process payment at your earliest convenience.\n\nThank you,\n{companyName}',
  requireApproval: true,
  approvalThreshold: 1000,
  multiLevelApproval: false,
  companyName: 'Your Company Name',
  companyAddress: '123 Business Street, City, State 12345',
  companyPhone: '+****************',
  companyEmail: '<EMAIL>',
};

export default function InvoiceSettings() {
  const { hasPermission } = useAuth();
  const [isLoading, setIsLoading] = React.useState(false);

  const { control, handleSubmit, reset, watch, formState: { errors, isDirty } } = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema),
    defaultValues: defaultSettings,
  });

  const numberingScheme = watch('numberingScheme');
  const requireApproval = watch('requireApproval');
  const latePaymentFeeType = watch('latePaymentFeeType');

  // Check if user has admin permissions
  if (!hasPermission('admin')) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="card-neumorphic p-8 text-center">
          <Settings className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-foreground mb-2">Access Denied</h2>
          <p className="text-muted-foreground">
            You need administrator privileges to access invoice settings.
          </p>
        </div>
      </div>
    );
  }

  const onSubmit = async (data: SettingsFormData) => {
    setIsLoading(true);
    try {
      // Here you would save the settings to your backend
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Invoice settings saved successfully');
    } catch (error) {
      toast.error('Failed to save settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    reset(defaultSettings);
    toast.info('Settings reset to defaults');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground">Invoice Settings</h1>
          <p className="text-muted-foreground mt-2">
            Configure invoice numbering, templates, and approval workflows
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isLoading}
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset to Defaults
          </Button>
          
          <Button
            onClick={handleSubmit(onSubmit)}
            disabled={isLoading || !isDirty}
          >
            <Save className="w-4 h-4 mr-2" />
            {isLoading ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </motion.div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Invoice Numbering */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="card-neumorphic">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Hash className="w-5 h-5" />
                Invoice Numbering
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="numberingScheme">Numbering Scheme</Label>
                  <Controller
                    name="numberingScheme"
                    control={control}
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sequential">Sequential (INV-001, INV-002)</SelectItem>
                          <SelectItem value="date-based">Date-based (INV-20240101-001)</SelectItem>
                          <SelectItem value="custom">Custom Format</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>

                <div>
                  <Label htmlFor="startingNumber">Starting Number</Label>
                  <Controller
                    name="startingNumber"
                    control={control}
                    render={({ field }) => (
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      />
                    )}
                  />
                  {errors.startingNumber && (
                    <p className="text-sm text-red-500 mt-1">{errors.startingNumber.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="numberPrefix">Prefix</Label>
                  <Controller
                    name="numberPrefix"
                    control={control}
                    render={({ field }) => <Input {...field} placeholder="INV-" />}
                  />
                  {errors.numberPrefix && (
                    <p className="text-sm text-red-500 mt-1">{errors.numberPrefix.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="numberSuffix">Suffix</Label>
                  <Controller
                    name="numberSuffix"
                    control={control}
                    render={({ field }) => <Input {...field} placeholder="Optional" />}
                  />
                  {errors.numberSuffix && (
                    <p className="text-sm text-red-500 mt-1">{errors.numberSuffix.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Tax Settings */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="card-neumorphic">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Percent className="w-5 h-5" />
                Tax Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="defaultTaxRate">Default Tax Rate (%)</Label>
                  <Controller
                    name="defaultTaxRate"
                    control={control}
                    render={({ field }) => (
                      <Input
                        type="number"
                        step="0.01"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    )}
                  />
                  {errors.defaultTaxRate && (
                    <p className="text-sm text-red-500 mt-1">{errors.defaultTaxRate.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="taxCalculationMethod">Tax Calculation</Label>
                  <Controller
                    name="taxCalculationMethod"
                    control={control}
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="exclusive">Tax Exclusive (add tax to subtotal)</SelectItem>
                          <SelectItem value="inclusive">Tax Inclusive (tax included in prices)</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Payment Terms */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="card-neumorphic">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Payment Terms
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="defaultPaymentTerms">Default Payment Terms (days)</Label>
                  <Controller
                    name="defaultPaymentTerms"
                    control={control}
                    render={({ field }) => (
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      />
                    )}
                  />
                  {errors.defaultPaymentTerms && (
                    <p className="text-sm text-red-500 mt-1">{errors.defaultPaymentTerms.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="latePaymentFee">Late Payment Fee</Label>
                  <Controller
                    name="latePaymentFee"
                    control={control}
                    render={({ field }) => (
                      <Input
                        type="number"
                        step="0.01"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    )}
                  />
                  {errors.latePaymentFee && (
                    <p className="text-sm text-red-500 mt-1">{errors.latePaymentFee.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="latePaymentFeeType">Fee Type</Label>
                  <Controller
                    name="latePaymentFeeType"
                    control={control}
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="fixed">Fixed Amount</SelectItem>
                          <SelectItem value="percentage">Percentage</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Approval Workflow */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="card-neumorphic">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Approval Workflow
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Controller
                  name="requireApproval"
                  control={control}
                  render={({ field }) => (
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  )}
                />
                <Label htmlFor="requireApproval">Require approval for invoices</Label>
              </div>

              {requireApproval && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="approvalThreshold">Approval Threshold ($)</Label>
                    <Controller
                      name="approvalThreshold"
                      control={control}
                      render={({ field }) => (
                        <Input
                          type="number"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        />
                      )}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Invoices above this amount require approval
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Controller
                      name="multiLevelApproval"
                      control={control}
                      render={({ field }) => (
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      )}
                    />
                    <Label htmlFor="multiLevelApproval">Multi-level approval</Label>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Company Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="card-neumorphic">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Receipt className="w-5 h-5" />
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="companyName">Company Name</Label>
                  <Controller
                    name="companyName"
                    control={control}
                    render={({ field }) => <Input {...field} />}
                  />
                  {errors.companyName && (
                    <p className="text-sm text-red-500 mt-1">{errors.companyName.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="companyEmail">Company Email</Label>
                  <Controller
                    name="companyEmail"
                    control={control}
                    render={({ field }) => <Input type="email" {...field} />}
                  />
                  {errors.companyEmail && (
                    <p className="text-sm text-red-500 mt-1">{errors.companyEmail.message}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="companyAddress">Company Address</Label>
                  <Controller
                    name="companyAddress"
                    control={control}
                    render={({ field }) => <Textarea {...field} rows={3} />}
                  />
                  {errors.companyAddress && (
                    <p className="text-sm text-red-500 mt-1">{errors.companyAddress.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="companyPhone">Company Phone</Label>
                  <Controller
                    name="companyPhone"
                    control={control}
                    render={({ field }) => <Input {...field} />}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </form>
    </div>
  );
}