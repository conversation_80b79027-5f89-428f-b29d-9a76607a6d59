import React, { useEffect } from 'react';
import { useForm, useField<PERSON>rray, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { fetchContractsAsync, updateContractAsync, fetchContractTemplatesAsync, fetchContractByIdAsync } from '@/store/slices/contractsSlice';
import { fetchVendorsAsync } from '@/store/slices/vendorsSlice';
// Remove import { fetchTemplatesAsync } from '@/store/slices/templatesSlice';
import { useToast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Plus, Trash2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

// Validation schema (same as create)
const schema = yup.object().shape({
  title: yup.string().required('Title is required'),
  vendorId: yup.number().required('Vendor is required'),
  startDate: yup.date().required('Start date is required'),
  endDate: yup.date().required('End date is required').min(yup.ref('startDate'), 'End date must be after start date'),
  paymentTerms: yup.string().required('Payment terms are required'),
  deliverables: yup.string().required('Deliverables are required'),
  milestones: yup.array().of(
    yup.object().shape({
      name: yup.string().required('Milestone name is required'),
      description: yup.string().required('Description is required'),
      dueDate: yup.date().required('Due date is required'),
    })
  ),
});

type FormData = yup.InferType<typeof schema>;

export default function ContractEdit() {
  const { id } = useParams<{ id: string }>(); // Get contract ID from URL
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const contract = useSelector((state: RootState) => state.contracts.currentContract);
  const vendors = useSelector((state: RootState) => state.vendors.vendors);
  const templates = useSelector((state: RootState) => state.contracts.templates);
  const isLoading = useSelector((state: RootState) => state.contracts.isLoading);
  const error = useSelector((state: RootState) => state.contracts.error);

  const { control, handleSubmit, reset, formState: { errors } } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      title: '',
      vendorId: undefined,
      startDate: undefined,
      endDate: undefined,
      paymentTerms: '',
      deliverables: '',
      milestones: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'milestones',
  });

  useEffect(() => {
    dispatch(fetchVendorsAsync({}));
    dispatch(fetchContractTemplatesAsync());
    if (id) {
      dispatch(fetchContractByIdAsync(parseInt(id)));
    }
  }, [dispatch, id]);

  useEffect(() => {
    if (contract) {
      reset({
        title: contract.title,
        vendorId: contract.vendor_id,
        startDate: new Date(contract.start_date),
        endDate: new Date(contract.end_date),
        paymentTerms: contract.clauses.payment_terms,
        deliverables: contract.clauses.deliverables,
        milestones: contract.milestones.map(m => ({
          name: m.name,
          description: m.description || '',
          dueDate: new Date(m.due_date)
        })),
      });
    }
  }, [contract, reset]);

  const onSubmit = async (data: FormData, isDraft = false) => {
    if (!id) return;

    const updates = {
      title: data.title,
      vendor_id: data.vendorId,
      start_date: format(data.startDate, 'yyyy-MM-dd'),
      end_date: format(data.endDate, 'yyyy-MM-dd'),
      clauses: {
        ...contract.clauses,
        payment_terms: data.paymentTerms,
        deliverables: data.deliverables,
      },
      milestones: data.milestones.map((m, index) => ({
        id: contract.milestones[index]?.id || index + 1,
        name: m.name,
        description: m.description,
        due_date: format(m.dueDate, 'yyyy-MM-dd'),
        completed: contract.milestones[index]?.completed || false,
        completed_date: contract.milestones[index]?.completed_date,
      })),
      status: (isDraft ? 'draft' : 'active') as 'expired' | 'active' | 'draft' | 'signed' | 'terminated',
    };

    try {
      await dispatch(updateContractAsync({ id: parseInt(id), updates }));
      toast({
        title: isDraft ? 'Draft Saved' : 'Contract Updated',
        description: isDraft ? 'Your changes have been saved as a draft.' : 'The contract has been successfully updated.',
        variant: 'default',
      });
      if (!isDraft) {
        reset();
        navigate('/contracts/list');
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to update contract. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) return <div>Loading...</div>;
  if (error || !contract) return <div>{error || 'Contract not found'}</div>;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-gradient-subtle p-8"
    >
      <Card className="card-neumorphic max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Edit Contract</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit((data) => onSubmit(data))} className="space-y-8">


            {/* Basic Details */}
            <div className="space-y-2">
              <Label htmlFor="title">Contract Title</Label>
              <Controller
                name="title"
                control={control}
                render={({ field }) => <Input {...field} placeholder="Enter contract title" />}
              />
              {errors.title && <p className="text-destructive text-sm">{errors.title.message}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="vendorId">Vendor</Label>
              <Controller
                name="vendorId"
                control={control}
                render={({ field }) => (
                  <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select vendor" />
                    </SelectTrigger>
                    <SelectContent>
                      {vendors.map((vendor) => (
                        <SelectItem key={vendor.id} value={vendor.id.toString()}>
                          {vendor.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.vendorId && <p className="text-destructive text-sm">{errors.vendorId.message}</p>}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date</Label>
                <Controller
                  name="startDate"
                  control={control}
                  render={({ field }) => (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full justify-start text-left font-normal',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  )}
                />
                {errors.startDate && <p className="text-destructive text-sm">{errors.startDate.message}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="endDate">End Date</Label>
                <Controller
                  name="endDate"
                  control={control}
                  render={({ field }) => (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full justify-start text-left font-normal',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  )}
                />
                {errors.endDate && <p className="text-destructive text-sm">{errors.endDate.message}</p>}
              </div>
            </div>

            {/* Terms and Deliverables */}
            <div className="space-y-2">
              <Label htmlFor="paymentTerms">Payment Terms</Label>
              <Controller
                name="paymentTerms"
                control={control}
                render={({ field }) => <Textarea {...field} placeholder="Enter payment terms" />}
              />
              {errors.paymentTerms && <p className="text-destructive text-sm">{errors.paymentTerms.message}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="deliverables">Deliverables</Label>
              <Controller
                name="deliverables"
                control={control}
                render={({ field }) => <Textarea {...field} placeholder="Enter deliverables" />}
              />
              {errors.deliverables && <p className="text-destructive text-sm">{errors.deliverables.message}</p>}
            </div>

            {/* Milestones */}
            <div className="space-y-4">
              <Label>Milestones (Optional)</Label>
              {fields.map((field, index) => (
                <Card key={field.id} className="p-4">
                  <div className="space-y-2">
                    <Input
                      placeholder="Milestone Name"
                      {...control.register(`milestones.${index}.name`)}
                    />
                    {errors.milestones?.[index]?.name && <p className="text-destructive text-sm">{errors.milestones[index]?.name?.message}</p>}

                    <Textarea
                      placeholder="Description"
                      {...control.register(`milestones.${index}.description`)}
                    />
                    {errors.milestones?.[index]?.description && <p className="text-destructive text-sm">{errors.milestones[index]?.description?.message}</p>}

                    <Controller
                      name={`milestones.${index}.dueDate`}
                      control={control}
                      render={({ field }) => (
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full justify-start text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? format(field.value, 'PPP') : <span>Pick a due date</span>}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      )}
                    />
                    {errors.milestones?.[index]?.dueDate && <p className="text-destructive text-sm">{errors.milestones[index]?.dueDate?.message}</p>}
                  </div>
                  <Button type="button" variant="destructive" size="sm" onClick={() => remove(index)} className="mt-2">
                    <Trash2 className="mr-2 h-4 w-4" /> Remove Milestone
                  </Button>
                </Card>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={() => append({ name: '', description: '', dueDate: undefined })}
              >
                <Plus className="mr-2 h-4 w-4" /> Add Milestone
              </Button>
            </div>

            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" onClick={() => navigate('/contracts/list')}>
                Cancel
              </Button>
              <Button type="button" variant="secondary" onClick={handleSubmit((data) => onSubmit(data, true))}>
                Save Draft
              </Button>
              <Button type="submit">
                Update Contract
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
}