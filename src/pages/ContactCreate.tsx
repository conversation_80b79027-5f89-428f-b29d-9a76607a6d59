import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, User, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ContactForm } from '../components/contacts';
import { useAuth } from '../hooks/useAuth';
import { toast } from '../components/ui/sonner';
import { ContactService } from '../services/contactService';
import { AccountService } from '../services/accountService';
import { Contact, ContactCreateRequest } from '../types/contact';
import { Account } from '../types/account';

export const ContactCreate: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { canEdit } = useAuth();
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [availableAccounts, setAvailableAccounts] = useState<Account[]>([]);
  const [availableContacts, setAvailableContacts] = useState<Contact[]>([]);
  const [loadingData, setLoadingData] = useState(true);

  const accountId = searchParams.get('accountId') ? parseInt(searchParams.get('accountId')!) : undefined;

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoadingData(true);
        
        // Load accounts for selection
        const accountsResponse = await AccountService.getAccounts({}, 1, 100);
        setAvailableAccounts(accountsResponse.accounts);

        // If accountId is specified, load contacts from that account for hierarchy
        if (accountId) {
          const contactsResponse = await ContactService.getAccountContacts(accountId, { page: 1, limit: 100 });
          setAvailableContacts(contactsResponse.contacts);
        }
      } catch (error) {
        console.error('Failed to load data:', error);
        toast.error('Failed to load required data');
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, [accountId]);

  // Check permissions on mount
  useEffect(() => {
    if (!canEdit()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to create contacts.',
      });
      navigate('/contacts/list');
    }
  }, [canEdit, navigate]);

  // Handle form submission
  const handleSubmit = async (data: ContactCreateRequest) => {
    try {
      setIsSubmitting(true);
      const contact = await ContactService.createContact(data);
      
      toast.success('Contact created successfully!', {
        description: `${contact.full_name || `${contact.firstName} ${contact.lastName}`} has been added to your contacts.`,
        action: {
          label: 'View Contact',
          onClick: () => navigate(`/contacts/${contact.id}`),
        },
      });

      // Navigate back to contacts list or account view
      if (accountId) {
        navigate(`/accounts/${accountId}?tab=contacts`);
      } else {
        navigate('/contacts/list');
      }
    } catch (error: unknown) {
      console.error('Failed to create contact:', error);
      let message = 'An unexpected error occurred';
      if (error instanceof Error) {
        message = error.message;
      }
      toast.error('Failed to create contact', {
        description: message,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (accountId) {
      navigate(`/accounts/${accountId}?tab=contacts`);
    } else {
      navigate('/contacts/list');
    }
  };

  // Don't render if user doesn't have permission
  if (!canEdit()) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="border-b bg-card"
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancel}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                {accountId ? 'Back to Account' : 'Back to Contacts'}
              </Button>
              <div className="h-6 w-px bg-border" />
              <div>
                <h1 className="text-2xl font-bold flex items-center gap-2">
                  <User className="h-6 w-6 text-primary" />
                  Create New Contact
                </h1>
                <p className="text-muted-foreground">
                  {accountId 
                    ? `Add a new contact to ${availableAccounts.find(a => a.id === accountId)?.name || 'the selected account'}`
                    : 'Add a new contact to your system'
                  }
                </p>
              </div>
            </div>
            
            {/* Success Indicator */}
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4" />
              Ready to create
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="container mx-auto px-4 py-8"
      >
        <div className="max-w-4xl mx-auto">
          {/* Form Card */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="bg-card rounded-lg border shadow-sm"
          >
            <div className="p-6">
              {loadingData ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <p className="mt-4 text-muted-foreground">Loading form data...</p>
                  </div>
                </div>
              ) : (
                <ContactForm
                  mode="create"
                  onSubmit={handleSubmit}
                  isLoading={isSubmitting}
                  accountId={accountId}
                  availableAccounts={availableAccounts}
                  availableContacts={availableContacts}
                />
              )}
            </div>
          </motion.div>

          {/* Help Text */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="mt-6 text-center text-sm text-muted-foreground"
          >
            <p>
              Fields marked with * are required. The contact will be created with Active status by default.
            </p>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};