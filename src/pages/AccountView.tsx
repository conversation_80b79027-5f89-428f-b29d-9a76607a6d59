import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Building2, 
  Loader2, 
  AlertCircle, 
  Edit, 
  Trash2,
  Phone,
  Globe,
  Mail,
  MapPin,
  DollarSign,
  Users,
  Calendar,
  Star,
  Building,
  Briefcase,
  Hash,
  FileText,
  Plus,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AccountApi } from '../services/accountApi';
import { Account } from '../types/account';
import { Opportunity } from '../types/opportunity';
import { opportunityService } from '../services/opportunityService';
import { useAuth } from '../hooks/useAuth';
import { toast } from '../components/ui/sonner';
import { format, parseISO } from 'date-fns';

export const AccountView: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { canEdit } = useAuth();
  
  const [account, setAccount] = useState<Account | null>(null);
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [opportunitiesLoading, setOpportunitiesLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState(false);

  const accountId = id ? parseInt(id) : undefined;

  // Load account data
  useEffect(() => {
    const loadAccount = async () => {
      if (!accountId) {
        setError('Invalid account ID');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const accountData = await AccountApi.getAccount(accountId);
        setAccount(accountData);
        setError(null);
      } catch (err) {
        console.error('Failed to load account:', err);
        setError(err instanceof Error ? err.message : 'Failed to load account');
      } finally {
        setLoading(false);
      }
    };

    loadAccount();
  }, [accountId]);

  // Load opportunities for this account
  const loadOpportunities = async () => {
    if (!accountId) return;

    try {
      setOpportunitiesLoading(true);
      const accountOpportunities = await opportunityService.getOpportunitiesByAccount(accountId);
      setOpportunities(accountOpportunities);
    } catch (err) {
      console.error('Failed to load opportunities:', err);
      toast.error('Failed to load opportunities', {
        description: err instanceof Error ? err.message : 'An error occurred',
      });
    } finally {
      setOpportunitiesLoading(false);
    }
  };

  const handleEdit = () => {
    if (account) {
      navigate(`/accounts/${account.id}/edit`);
    }
  };

  const handleDelete = async () => {
    if (!account) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete "${account.name}"? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      setDeleting(true);
      await AccountApi.deleteAccount(account.id);
      
      toast.success('Account deleted successfully', {
        description: `${account.name} has been removed from your accounts.`,
      });
      
      navigate('/accounts/list');
    } catch (err) {
      console.error('Failed to delete account:', err);
      toast.error('Failed to delete account', {
        description: err instanceof Error ? err.message : 'An error occurred',
      });
    } finally {
      setDeleting(false);
    }
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return 'Not specified';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num?: number) => {
    if (!num) return 'Not specified';
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatDate = (dateString: string | Date | undefined) => {
    if (!dateString) {
      return 'N/A';
    }
    try {
      const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;
      if (isNaN(date.getTime())) {
        throw new Error('Invalid Date');
      }
      return format(date, 'PPP'); // Example format: Oct 25, 2023
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'N/A';
    }
  };

  const getRatingColor = (rating?: string) => {
    switch (rating) {
      case 'Hot': return 'bg-red-500';
      case 'Warm': return 'bg-yellow-500';
      case 'Cold': return 'bg-blue-500';
      default: return 'bg-gray-400';
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="text-muted-foreground">Loading account...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !account) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error || 'Account not found'}
              </AlertDescription>
            </Alert>
            <div className="mt-6">
              <Button onClick={() => navigate('/accounts/list')}>
                Back to Accounts
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="border-b bg-card"
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/accounts/list')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Accounts
              </Button>
              <div className="h-6 w-px bg-border" />
              <div>
                <h1 className="text-2xl font-bold flex items-center gap-2">
                  <Building2 className="h-6 w-6 text-primary" />
                  {account.name}
                </h1>
                <p className="text-muted-foreground">
                  {account.type && `${account.type} • `}
                  {account.industry || 'No industry specified'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Status Badge */}
              <Badge variant={account.status === 'ACTIVE' ? 'default' : 'secondary'}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  account.status === 'ACTIVE' ? 'bg-green-500' : 'bg-gray-400'
                }`} />
                {account.status}
              </Badge>

              {/* Rating Badge */}
              {account.rating && (
                <Badge variant="outline" className="gap-2">
                  <div className={`w-2 h-2 rounded-full ${getRatingColor(account.rating)}`} />
                  {account.rating}
                </Badge>
              )}

              {/* Action Buttons */}
              {canEdit() && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleEdit}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDelete}
                    disabled={deleting}
                    className="text-destructive hover:text-destructive"
                  >
                    {deleting ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4 mr-2" />
                    )}
                    Delete
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="container mx-auto px-4 py-8"
      >
        <div className="max-w-6xl mx-auto">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="opportunities" onClick={loadOpportunities}>
                Opportunities
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Main Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {account.account_number && (
                    <div className="flex items-center gap-3">
                      <Hash className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">Account Number</p>
                        <p className="font-medium">{account.account_number}</p>
                      </div>
                    </div>
                  )}
                  
                  {account.type && (
                    <div className="flex items-center gap-3">
                      <Briefcase className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">Type</p>
                        <p className="font-medium">{account.type}</p>
                      </div>
                    </div>
                  )}
                  
                  {account.industry && (
                    <div className="flex items-center gap-3">
                      <Building className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">Industry</p>
                        <p className="font-medium">{account.industry}</p>
                      </div>
                    </div>
                  )}
                  
                  {account.ownership && (
                    <div className="flex items-center gap-3">
                      <Building className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">Ownership</p>
                        <p className="font-medium">{account.ownership}</p>
                      </div>
                    </div>
                  )}
                </div>

                {account.description && (
                  <>
                    <Separator />
                    <div>
                      <p className="text-sm text-muted-foreground mb-2">Description</p>
                      <p className="text-sm leading-relaxed">{account.description}</p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Financial Information */}
            {(account.annual_revenue || account.number_of_employees) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Financial Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {account.annual_revenue && (
                      <div className="flex items-center gap-3">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Annual Revenue</p>
                          <p className="text-lg font-semibold">{formatCurrency(account.annual_revenue)}</p>
                        </div>
                      </div>
                    )}
                    
                    {account.number_of_employees && (
                      <div className="flex items-center gap-3">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Number of Employees</p>
                          <p className="text-lg font-semibold">{formatNumber(account.number_of_employees)}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Address Information */}
            {(account.billing_address || account.shipping_address) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Address Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {account.billing_address && (
                      <div>
                        <h4 className="font-medium mb-2">Billing Address</h4>
                        <div className="text-sm text-muted-foreground space-y-1">
                          {account.billing_address.street && <p>{account.billing_address.street}</p>}
                          <p>
                            {[
                              account.billing_address.city,
                              account.billing_address.state,
                              account.billing_address.postalCode
                            ].filter(Boolean).join(', ')}
                          </p>
                          {account.billing_address.country && <p>{account.billing_address.country}</p>}
                        </div>
                      </div>
                    )}
                    
                    {account.shipping_address && (
                      <div>
                        <h4 className="font-medium mb-2">Shipping Address</h4>
                        <div className="text-sm text-muted-foreground space-y-1">
                          {account.shipping_address.street && <p>{account.shipping_address.street}</p>}
                          <p>
                            {[
                              account.shipping_address.city,
                              account.shipping_address.state,
                              account.shipping_address.postalCode
                            ].filter(Boolean).join(', ')}
                          </p>
                          {account.shipping_address.country && <p>{account.shipping_address.country}</p>}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Contact & Meta */}
          <div className="space-y-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Phone className="h-5 w-5" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {account.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Phone</p>
                      <a href={`tel:${account.phone}`} className="font-medium hover:text-primary">
                        {account.phone}
                      </a>
                    </div>
                  </div>
                )}
                
                {account.fax && (
                  <div className="flex items-center gap-3">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Fax</p>
                      <p className="font-medium">{account.fax}</p>
                    </div>
                  </div>
                )}
                
                {account.website && (
                  <div className="flex items-center gap-3">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Website</p>
                      <a 
                        href={account.website.startsWith('http') ? account.website : `https://${account.website}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="font-medium hover:text-primary"
                      >
                        {account.website}
                      </a>
                    </div>
                  </div>
                )}

                {account.ticker_symbol && (
                  <div className="flex items-center gap-3">
                    <Hash className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Ticker Symbol</p>
                      <p className="font-medium">{account.ticker_symbol}</p>
                    </div>
                  </div>
                )}

                {account.site && (
                  <div className="flex items-center gap-3">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Site</p>
                      <p className="font-medium">{account.site}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* System Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  System Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground">Created</p>
                  <p className="font-medium">{formatDate(account.created_at)}</p>
                </div>
                
                <div>
                  <p className="text-sm text-muted-foreground">Last Updated</p>
                  <p className="font-medium">{formatDate(account.updated_at)}</p>
                </div>

                {account.owner && (
                  <div>
                    <p className="text-sm text-muted-foreground">Owner</p>
                    <p className="font-medium">{account.owner.email}</p>
                  </div>
                )}

                {account.children_count !== undefined && (
                  <div>
                    <p className="text-sm text-muted-foreground">Child Accounts</p>
                    <p className="font-medium">{account.children_count}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => navigate(`/opportunities/create?accountId=${account.id}`)}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Create New Opportunity
                </Button>
                <Button variant="outline" className="w-full justify-start" disabled>
                  <FileText className="h-4 w-4 mr-2" />
                  View Related Contracts
                </Button>
                <Button variant="outline" className="w-full justify-start" disabled>
                  <FileText className="h-4 w-4 mr-2" />
                  View Related RFQs
                </Button>
                <p className="text-xs text-muted-foreground mt-2">
                  Some features coming soon
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="opportunities">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Account Opportunities</span>
              <Button 
                size="sm"
                onClick={() => navigate(`/opportunities/create?accountId=${account.id}`)}
              >
                <Plus className="h-4 w-4 mr-2" />
                New Opportunity
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {opportunitiesLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Loading opportunities...</span>
              </div>
            ) : opportunities.length === 0 ? (
              <div className="text-center py-8">
                <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No opportunities found</h3>
                <p className="text-muted-foreground mb-4">
                  This account doesn't have any opportunities yet.
                </p>
                <Button onClick={() => navigate(`/opportunities/create?accountId=${account.id}`)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Opportunity
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {opportunities.map((opportunity) => (
                  <div
                    key={opportunity.id}
                    className="border rounded-lg p-4 hover:bg-muted/50 cursor-pointer transition-colors"
                    onClick={() => navigate(`/opportunities/${opportunity.id}`)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-lg">{opportunity.name}</h4>
                        <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <TrendingUp className="h-4 w-4" />
                            {opportunity.stage}
                          </span>
                          {opportunity.amount && (
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />
                              {formatCurrency(opportunity.amount)}
                            </span>
                          )}
                          {opportunity.close_date && (
                            <span className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {formatDate(opportunity.close_date)}
                            </span>
                          )}
                        </div>
                        {opportunity.description && (
                          <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                            {opportunity.description}
                          </p>
                        )}
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        <Badge variant={
                          opportunity.stage === 'Closed Won' ? 'default' :
                          opportunity.stage === 'Closed Lost' ? 'destructive' :
                          'secondary'
                        }>
                          {opportunity.stage}
                        </Badge>
                        {opportunity.probability !== undefined && (
                          <span className="text-sm text-muted-foreground">
                            {opportunity.probability}% probability
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
      </motion.div>
    </div>
  );
};