import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, User, Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ContactForm } from '../components/contacts';
import { useAuth } from '../hooks/useAuth';
import { toast } from '../components/ui/sonner';
import { ContactService } from '../services/contactService';
import { AccountService } from '../services/accountService';
import { Contact, ContactUpdateRequest } from '../types/contact';
import { Account } from '../types/account';

export const ContactEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { canEdit } = useAuth();
  
  const [contact, setContact] = useState<Contact | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [availableAccounts, setAvailableAccounts] = useState<Account[]>([]);
  const [availableContacts, setAvailableContacts] = useState<Contact[]>([]);
  const [loadingData, setLoadingData] = useState(true);

  // Load contact and related data
  useEffect(() => {
    const loadData = async () => {
      if (!id || !canEdit()) return;

      try {
        setLoadingData(true);
        
        // Load contact data
        const contactData = await ContactService.getContact(parseInt(id));
        setContact(contactData);

        // Load accounts for selection
        const accountsResponse = await AccountService.getAccounts({}, 1, 100);
        setAvailableAccounts(accountsResponse.accounts);

        // Load contacts from the same account for hierarchy
        if (contactData.accountId) {
          const contactsResponse = await ContactService.getAccountContacts(contactData.accountId, { page: 1, limit: 100 });
          setAvailableContacts(contactsResponse.contacts);
        }
      } catch (error: any) {
        console.error('Failed to load data:', error);
        toast.error('Failed to load contact data', {
          description: error.message || 'An unexpected error occurred',
        });
        navigate('/contacts/list');
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, [id, canEdit, navigate]);

  // Check permissions on mount
  useEffect(() => {
    if (!canEdit()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to edit contacts.',
      });
      navigate('/contacts/list');
    }
  }, [canEdit, navigate]);

  // Handle form submission
  const handleSubmit = async (data: ContactUpdateRequest) => {
    if (!contact) return;

    try {
      setIsSubmitting(true);
      const updatedContact = await ContactService.updateContact(contact.id, data);
      
      toast.success('Contact updated successfully!', {
        description: `${updatedContact.fullName || `${updatedContact.firstName} ${updatedContact.lastName}`} has been updated.`,
        action: {
          label: 'View Contact',
          onClick: () => navigate(`/contacts/${updatedContact.id}`),
        },
      });

      // Navigate back to contact view
      navigate(`/contacts/${updatedContact.id}`);
    } catch (error: any) {
      console.error('Failed to update contact:', error);
      toast.error('Failed to update contact', {
        description: error.message || 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (contact) {
      navigate(`/contacts/${contact.id}`);
    } else {
      navigate('/contacts/list');
    }
  };

  // Don't render if user doesn't have permission
  if (!canEdit()) {
    return null;
  }

  if (loadingData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading contact data...</p>
        </div>
      </div>
    );
  }

  if (!contact) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <User className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">Contact not found</h3>
          <p className="mt-2 text-muted-foreground">
            The contact you're trying to edit doesn't exist or has been deleted.
          </p>
          <Button className="mt-4" onClick={() => navigate('/contacts/list')}>
            Back to Contacts
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="border-b bg-card"
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancel}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Contact
              </Button>
              <div className="h-6 w-px bg-border" />
              <div>
                <h1 className="text-2xl font-bold flex items-center gap-2">
                  <User className="h-6 w-6 text-primary" />
                  Edit Contact
                </h1>
                <p className="text-muted-foreground">
                  Update {contact.fullName || `${contact.firstName || ''} ${contact.lastName}`.trim()}'s information
                </p>
              </div>
            </div>
            
            {/* Save Indicator */}
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Save className="h-4 w-4" />
              Ready to save changes
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="container mx-auto px-4 py-8"
      >
        <div className="max-w-4xl mx-auto">
          {/* Form Card */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="bg-card rounded-lg border shadow-sm"
          >
            <div className="p-6">
              <ContactForm
                mode="edit"
                initialData={contact}
                onSubmit={handleSubmit}
                isLoading={isSubmitting}
                accountId={contact.accountId}
                availableAccounts={availableAccounts}
                availableContacts={availableContacts}
              />
            </div>
          </motion.div>

          {/* Help Text */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="mt-6 text-center text-sm text-muted-foreground"
          >
            <p>
              Changes will be saved immediately. All modifications are tracked in the audit log.
            </p>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};