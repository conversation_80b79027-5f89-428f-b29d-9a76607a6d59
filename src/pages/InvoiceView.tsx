import * as React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { fetchInvoiceByIdAsync, approveInvoiceAsync, selectInvoiceById } from '@/store/slices/invoicesSlice';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { InvoiceStatusBadge } from '@/components/InvoiceStatusBadge';
import { LineItemsTable } from '@/components/LineItemsTable';
import { InvoiceTotals } from '@/components/InvoiceTotals';
import { PaymentHistory } from '@/components/PaymentHistory';
import { AuditTrail } from '@/components/AuditTrail';
import { ApprovalWorkflow } from '@/components/ApprovalWorkflow';
import { PaymentModal } from '@/components/PaymentModal';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ArrowLeft, Check, CreditCard, Download, Pencil } from 'lucide-react';
import { format } from 'date-fns';
import { useAuth } from '@/hooks/useAuth';
import { toast } from '@/components/ui/sonner';
import { Skeleton } from '@/components/ui/skeleton';

const InvoiceView: React.FC = () => {
  const { id } = useParams<{ id: string }>(); // Corrected
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const invoice = useSelector((state: RootState) => selectInvoiceById(state, id || ''));
  const status = useSelector((state: RootState) => state.invoices.status);
  const [showPaymentModal, setShowPaymentModal] = React.useState(false);

  React.useEffect(() => {
    if (id && !isNaN(Number(id))) {
      dispatch(fetchInvoiceByIdAsync(Number(id)));
    }
  }, [dispatch, id]);

  const handleApprove = async () => {
    if (id && !isNaN(Number(id))) {
      await dispatch(approveInvoiceAsync({ id: Number(id), comments: 'Approved via web interface' }));
      toast.success('Invoice approved');
    }
  };

  const handlePay = () => {
    setShowPaymentModal(true);
  };

  const handlePayment = async (paymentData: any) => {
    if (id && !isNaN(Number(id))) {
      // This would integrate with the payInvoiceAsync thunk
      toast.success('Payment processed successfully');
      setShowPaymentModal(false);
    }
  };

  const handleDownload = () => {
    // Generate and download PDF
    toast.info('Downloading PDF');
  };

  if (!id || isNaN(Number(id))) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">Invalid Invoice ID</h2>
          <p className="text-gray-600 mt-2">The invoice ID provided is not valid.</p>
          <Button onClick={() => navigate('/invoices/list')} className="mt-4">
            Back to Invoices
          </Button>
        </div>
      </div>
    );
  }

  if (status === 'loading' || !invoice) {
    return <Skeleton className="w-full h-[500px]" />;
  }

  return (
    <div className="p-6">
      <Button variant="ghost" onClick={() => navigate('/invoices/list')} className="mb-4">
        <ArrowLeft className="mr-2 h-4 w-4" /> Back to Invoices
      </Button>
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Invoice {invoice.id}</CardTitle>
            <InvoiceStatusBadge status={invoice.status} />
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Vendor</Label>
              <p>{invoice.vendor_name || invoice.vendorId || 'N/A'}</p>
            </div>
            <div>
              <Label>Contract</Label>
              <p>{invoice.contract_title || invoice.contractId || 'N/A'}</p>
            </div>
            <div>
              <Label>Due Date</Label>
              <p>{invoice.due_date ? format(new Date(invoice.due_date), 'PPP') : 'N/A'}</p>
            </div>
            <div>
              <Label>Created</Label>
              <p>{invoice.created_at ? format(new Date(invoice.created_at), 'PPP') : 'N/A'}</p>
            </div>
          </div>
          <LineItemsTable lineItems={invoice.lineItems || []} mode="view" />
          <InvoiceTotals lineItems={invoice.lineItems || []} currency={invoice.currency} mode="view" />
          
          {/* Payment History */}
          <PaymentHistory payments={invoice.paymentHistory || []} />
          
          {/* Approval Workflow */}
          <ApprovalWorkflow 
            approvalLevels={invoice.approvalLevels || []}
            currentLevel={invoice.currentApprovalLevel || 1}
            canApprove={user?.role === 'manager' && invoice.status === 'pending'}
            canReject={user?.role === 'manager' && invoice.status === 'pending'}
            onApprove={(comments) => handleApprove()}
            onReject={(reason) => {
              // Handle rejection
              toast.info('Rejection functionality to be implemented');
            }}
          />
          
          {/* Audit Trail */}
          <AuditTrail auditRecords={invoice.auditTrail || []} />
          <div className="flex space-x-2">
            {user?.role === 'manager' && invoice.status === 'pending' && (
              <Button onClick={handleApprove}><Check className="mr-2 h-4 w-4" /> Approve</Button>
            )}
            {invoice.status === 'approved' && (
              <Button onClick={handlePay}><CreditCard className="mr-2 h-4 w-4" /> Pay</Button>
            )}
            <Button onClick={handleDownload}><Download className="mr-2 h-4 w-4" /> Download PDF</Button>
            {user?.role === 'manager' && invoice.status !== 'paid' && (
              <Button variant="outline" onClick={() => navigate(`/invoices/${id}/edit`)}><Pencil className="mr-2 h-4 w-4" /> Edit</Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        invoiceAmount={invoice.totalAmount}
        currency={invoice.currency}
        invoiceNumber={invoice.invoiceNumber || `INV-${invoice.id}`}
        onPayment={handlePayment}
      />
    </div>
  );
};

export default InvoiceView;