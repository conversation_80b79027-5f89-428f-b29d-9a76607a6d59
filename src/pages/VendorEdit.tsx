import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Shield, 
  AlertTriangle, 
  Save,
  X,
  Clock,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { VendorForm } from '../components/vendors';
import { RootState, AppDispatch } from '@/store';
import { 
  fetchVendorByIdAsync, 
  clearCurrentVendor 
} from '@/store/slices/vendorsSlice';
import { useVendorForm } from '../hooks/useVendorForm';
import { useAuth } from '../hooks/useAuth';
import { toast } from '../components/ui/sonner';
// Simple date formatting utility
const formatDistanceToNow = (date: Date) => {
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) return 'today';
  if (diffInDays === 1) return '1 day ago';
  if (diffInDays < 30) return `${diffInDays} days ago`;
  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
  return `${Math.floor(diffInDays / 365)} years ago`;
};

export const VendorEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { canEdit } = useAuth();
  
  const { currentVendor, isLoading, error } = useSelector(
    (state: RootState) => state.vendors
  );
  
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  const vendorId = id ? parseInt(id, 10) : undefined;
  
  const { handleSubmit, handleCancel, isSubmitting } = useVendorForm({
    mode: 'edit',
    vendorId,
    initialData: currentVendor || undefined,
    onSuccess: (vendor) => {
      setHasUnsavedChanges(false);
      toast.success('Vendor updated successfully!', {
        description: `${vendor.name} information has been updated.`,
        action: {
          label: 'View Vendor',
          onClick: () => navigate(`/vendors/${vendor.id}`),
        },
      });
    },
  });

  useEffect(() => {
    if (!canEdit()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to edit vendors.',
      });
      navigate('/vendors/list');
      return;
    }

    if (vendorId) {
      dispatch(fetchVendorByIdAsync(vendorId));
    }

    return () => {
      dispatch(clearCurrentVendor());
    };
}, [dispatch, vendorId, navigate]); // Removed canEdit from dependencies


  // Handle browser back/refresh with unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  const handleFormChange = () => {
    setHasUnsavedChanges(true);
  };

  const handleCancelWithConfirmation = () => {
    if (hasUnsavedChanges) {
      if (window.confirm('You have unsaved changes. Are you sure you want to leave?')) {
        setHasUnsavedChanges(false);
        handleCancel();
      }
    } else {
      handleCancel();
    }
  };

  // Don't render if user doesn't have permission
  if (!canEdit()) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="card-neumorphic p-8">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-4"></div>
              <span className="text-muted-foreground">Loading vendor details...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !currentVendor) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="card-neumorphic p-8 text-center">
            <AlertTriangle className="w-16 h-16 text-destructive mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-foreground mb-2">Vendor Not Found</h2>
            <p className="text-muted-foreground mb-6">
              The vendor you're trying to edit doesn't exist or has been removed.
            </p>
            <Button
              onClick={() => navigate('/vendors/list')}
              className="btn-neumorphic gradient-primary text-primary-foreground"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Vendors
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm border-b border-border"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancelWithConfirmation}
                className="btn-neumorphic"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {hasUnsavedChanges ? 'Cancel' : 'Back'}
              </Button>
              <div className="h-6 w-px bg-border" />
              <div>
                <h1 className="text-lg font-semibold text-foreground">Edit Vendor</h1>
                <p className="text-sm text-muted-foreground">
                  Updating {currentVendor.name}
                </p>
              </div>
            </div>

            {/* Status Indicators */}
            <div className="flex items-center space-x-4">
              {hasUnsavedChanges && (
                <div className="flex items-center space-x-2 text-warning">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">Unsaved changes</span>
                </div>
              )}
              
              <div className="text-sm text-muted-foreground">
                Last updated {formatDistanceToNow(new Date(currentVendor.updated_at), { addSuffix: true })}
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Security Notice */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="card-neumorphic p-4 bg-primary/5 border-primary/20">
            <div className="flex items-start space-x-3">
              <Shield className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-foreground">Security Notice</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Changes to vendor information are tracked and logged for audit purposes. 
                  Make sure all information is accurate before saving.
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Change Tracking Notice */}
        {hasUnsavedChanges && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="card-neumorphic p-4 bg-warning/5 border-warning/20">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="w-5 h-5 text-warning flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="text-sm font-medium text-foreground">Unsaved Changes</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      You have made changes that haven't been saved yet. Don't forget to save your work.
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setHasUnsavedChanges(false)}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Vendor Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div onChange={handleFormChange}>
            <VendorForm
              mode="edit"
              initialData={currentVendor}
              onSubmit={handleSubmit}
              isLoading={isSubmitting}
            />
          </div>
        </motion.div>

        {/* Help Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8"
        >
          <div className="card-neumorphic p-6">
            <h3 className="text-lg font-semibold text-foreground mb-4">Editing Guidelines</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-foreground mb-2">What You Can Edit</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Basic vendor information</li>
                  <li>• Contact details and address</li>
                  <li>• Category and certifications</li>
                  <li>• Custom fields and notes</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-foreground mb-2">Important Notes</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• All changes are tracked and audited</li>
                  <li>• Email changes may require verification</li>
                  <li>• Performance scores are calculated automatically</li>
                  <li>• Status changes require admin approval</li>
                </ul>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Sticky Save Bar */}
        {hasUnsavedChanges && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50"
          >
            <div className="card-neumorphic p-4 bg-background border-warning/20 shadow-lg">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 text-warning">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm font-medium">You have unsaved changes</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancelWithConfirmation}
                    className="btn-neumorphic"
                  >
                    Discard
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => {
                      // Trigger form submission
                      const form = document.querySelector('form');
                      if (form) {
                        form.requestSubmit();
                      }
                    }}
                    disabled={isSubmitting}
                    className="btn-neumorphic gradient-primary text-primary-foreground"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};