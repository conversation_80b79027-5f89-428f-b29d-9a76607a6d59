import * as React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Search,
  Filter,
  Plus,
  Receipt,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Download,
  Calendar,
  DollarSign,
  Building2,
  TrendingUp,
} from "lucide-react";
import { RootState, AppDispatch } from "@/store";
import { fetchInvoicesAsync, setFilters } from "@/store/slices/invoicesSlice";
import { useAuth } from "@/hooks/useAuth";
import { InvoiceStatusBadge } from "@/components/InvoiceStatusBadge";
import { format } from "date-fns";
import { CSVLink } from "react-csv";

export const InvoicesList: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { canEdit, canDelete } = useAuth();
  const { invoices, isLoading, filters } = useSelector(
    (state: RootState) => state.invoices
  );
  const [searchTerm, setSearchTerm] = React.useState(filters.search || "");
  const [showFilters, setShowFilters] = React.useState(false);

  React.useEffect(() => {
    dispatch(fetchInvoicesAsync({ filters }));
  }, [dispatch, filters]);

  const handleSearch = (search: string) => {
    setSearchTerm(search);
    dispatch(setFilters({ search }));
  };

  const handleFilterChange = (filterType: string, value: string) => {
    dispatch(setFilters({ [filterType]: value }));
  };

  const getAmountColor = (amount: number) => {
    if (amount >= 50000) return "text-success";
    if (amount >= 10000) return "text-warning";
    return "text-foreground";
  };

  const formatSafeDate = (
    dateString: string | undefined | null,
    formatStr: string = "MMM dd, yyyy"
  ) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "N/A";
      return format(date, formatStr);
    } catch {
      return "N/A";
    }
  };

  const csvData = invoices.map((invoice) => ({
    ID: invoice.id,
    InvoiceNumber: invoice.invoiceNumber,
    Vendor: invoice.vendorId,
    Amount: invoice.totalAmount,
    Currency: invoice.currency,
    Status: invoice.status,
    DueDate: formatSafeDate(invoice.dueDate, "PPP"),
    CreatedAt: formatSafeDate(invoice.createdAt, "PPP"),
  }));

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="card-neumorphic p-8">
          <div className="flex items-center space-x-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="text-muted-foreground">Loading invoices...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground">Invoices</h1>
          <p className="text-muted-foreground mt-2">
            Manage your invoice processing and payments
          </p>
        </div>

        {canEdit() && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => navigate("/invoices/create")}
            className="btn-neumorphic gradient-primary text-primary-foreground px-6 py-3 flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Create Invoice</span>
          </motion.button>
        )}
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="card-neumorphic p-6"
      >
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search invoices by number, vendor, or description..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="input-neumorphic w-full pl-10 pr-4"
            />
          </div>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn-neumorphic px-4 py-2 flex items-center space-x-2"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
          </button>

          {/* Export */}
          <CSVLink data={csvData} filename="invoices.csv">
            <button className="btn-neumorphic px-4 py-2 flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Export</span>
            </button>
          </CSVLink>
        </div>

        {/* Filter Options */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            transition={{ duration: 0.3 }}
            className="mt-4 pt-4 border-t border-border"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Status
                </label>
                <select
                  value={filters.status || ""}
                  onChange={(e) => handleFilterChange("status", e.target.value)}
                  className="input-neumorphic w-full"
                >
                  <option value="">All Statuses</option>
                  <option value="draft">Draft</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="sent">Sent</option>
                  <option value="paid">Paid</option>
                  <option value="overdue">Overdue</option>
                  <option value="disputed">Disputed</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Amount Range
                </label>
                <select
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === "") {
                      handleFilterChange("amountRange", "");
                    } else {
                      handleFilterChange("amountRange", value);
                    }
                  }}
                  className="input-neumorphic w-full"
                >
                  <option value="">All Amounts</option>
                  <option value="0-1000">$0 - $1,000</option>
                  <option value="1000-10000">$1,000 - $10,000</option>
                  <option value="10000-50000">$10,000 - $50,000</option>
                  <option value="50000-999999">$50,000+</option>
                </select>
              </div>

              <div className="flex items-end">
                <button
                  onClick={() => {
                    dispatch(
                      setFilters({
                        status: "",
                        search: "",
                        amountRange: undefined,
                      })
                    );
                    setSearchTerm("");
                  }}
                  className="btn-neumorphic px-4 py-2 text-muted-foreground hover:text-foreground"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Statistics Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-4 gap-6"
      >
        <div className="card-neumorphic p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
              <Receipt className="w-6 h-6 text-primary" />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Total Invoices
              </p>
              <p className="text-2xl font-bold text-foreground">
                {invoices.length}
              </p>
            </div>
          </div>
        </div>

        <div className="card-neumorphic p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-warning/10 rounded-xl flex items-center justify-center">
              <Calendar className="w-6 h-6 text-warning" />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Pending
              </p>
              <p className="text-2xl font-bold text-foreground">
                {invoices.filter((i) => i.status === "pending").length}
              </p>
            </div>
          </div>
        </div>

        <div className="card-neumorphic p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-success/10 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-success" />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Paid</p>
              <p className="text-2xl font-bold text-foreground">
                {invoices.filter((i) => i.status === "paid").length}
              </p>
            </div>
          </div>
        </div>

        <div className="card-neumorphic p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-destructive/10 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-destructive" />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Overdue
              </p>
              <p className="text-2xl font-bold text-foreground">
                {invoices.filter((i) => i.status === "overdue").length}
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Invoices Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {invoices.map((invoice, index) => (
          <motion.div
            key={invoice.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 + index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            className="card-neumorphic p-6 cursor-pointer"
            onClick={() => navigate(`/invoices/${invoice.id}`)}
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                  <Receipt className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-foreground">
                    {invoice.invoiceNumber || `INV-${invoice.id}`}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Vendor ID: {invoice.vendorId}
                  </p>
                </div>
              </div>

              <div className="relative group">
                <button className="p-1 rounded-lg hover:bg-accent transition-colors">
                  <MoreHorizontal className="w-4 h-4" />
                </button>

                <div className="absolute right-0 top-full mt-2 w-40 bg-popover border border-border rounded-xl shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/invoices/${invoice.id}`);
                    }}
                    className="w-full px-3 py-2 text-left hover:bg-accent rounded-xl transition-colors flex items-center space-x-2"
                  >
                    <Eye className="w-4 h-4" />
                    <span>View</span>
                  </button>

                  {canEdit() && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/invoices/${invoice.id}/edit`);
                      }}
                      className="w-full px-3 py-2 text-left hover:bg-accent rounded-xl transition-colors flex items-center space-x-2"
                    >
                      <Edit className="w-4 h-4" />
                      <span>Edit</span>
                    </button>
                  )}

                  {canDelete() && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // Handle delete
                      }}
                      className="w-full px-3 py-2 text-left hover:bg-accent rounded-xl transition-colors flex items-center space-x-2 text-destructive"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>Delete</span>
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Status and Amount */}
            <div className="flex items-center justify-between mb-4">
              <InvoiceStatusBadge status={invoice.status} />
              <span
                className={`font-semibold ${getAmountColor(
                  invoice.totalAmount
                )}`}
              >
                {new Intl.NumberFormat("en-US", {
                  style: "currency",
                  currency: invoice.currency || "USD",
                }).format(invoice.totalAmount)}
              </span>
            </div>

            {/* Dates */}
            <div className="space-y-2 mb-4">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Calendar className="w-4 h-4" />
                <span>Due: {formatSafeDate(invoice.dueDate)}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Building2 className="w-4 h-4" />
                <span>Created: {formatSafeDate(invoice.createdAt)}</span>
              </div>
            </div>

            {/* Progress indicator for payment */}
            {invoice.status === "paid" && (
              <div className="w-full bg-success/20 rounded-full h-2">
                <div className="bg-success h-2 rounded-full w-full" />
              </div>
            )}
            {invoice.status === "overdue" && (
              <div className="w-full bg-destructive/20 rounded-full h-2">
                <div className="bg-destructive h-2 rounded-full w-full" />
              </div>
            )}
            {(invoice.status === "pending" ||
              invoice.status === "approved") && (
              <div className="w-full bg-warning/20 rounded-full h-2">
                <div className="bg-warning h-2 rounded-full w-3/4" />
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Empty State */}
      {invoices.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="card-neumorphic p-8 max-w-md mx-auto">
            <Receipt className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              No invoices found
            </h3>
            <p className="text-muted-foreground mb-4">
              {filters.search || filters.status
                ? "Try adjusting your search or filters"
                : "Get started by creating your first invoice"}
            </p>
            {canEdit() && (
              <button
                onClick={() => navigate("/invoices/create")}
                className="btn-neumorphic gradient-primary text-primary-foreground px-6 py-2"
              >
                Create First Invoice
              </button>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default InvoicesList;
