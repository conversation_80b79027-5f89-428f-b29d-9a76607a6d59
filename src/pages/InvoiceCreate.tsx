import * as React from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { createInvoiceAsync, fetchInvoicesAsync } from '@/store/slices/invoicesSlice';
import { fetchVendorsAsync } from '@/store/slices/vendorsSlice';
import { fetchContractsAsync } from '@/store/slices/contractsSlice';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LineItemsTable } from '@/components/LineItemsTable';
import { InvoiceTotals } from '@/components/InvoiceTotals';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/components/ui/sonner';
import { ArrowLeft } from 'lucide-react';
import { LineItem } from '@/store/slices/invoicesSlice';

const formSchema = z.object({
  vendorId: z.string().min(1, 'Vendor is required'),
  contractId: z.string().optional(),
  dueDate: z.string().min(1, 'Due date is required'),
  currency: z.string().default('USD'),
  items: z.array(z.object({
    id: z.string(),
    description: z.string().min(1),
    quantity: z.number().min(0),
    unitPrice: z.number().min(0),
    totalPrice: z.number().min(0),
    taxable: z.boolean(),
  })),
  discount: z.number().min(0).max(100),
  taxRate: z.number().min(0).max(100),
});

type FormValues = z.infer<typeof formSchema>;

const InvoiceCreate: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const vendors = useSelector((state: RootState) => state.vendors.vendors);
  const contracts = useSelector((state: RootState) => state.contracts.contracts);

  const { control, handleSubmit, watch, setValue } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      vendorId: '',
      contractId: '',
      dueDate: '',
      currency: 'USD',
      items: [],
      discount: 0,
      taxRate: 0,
    },
  });

  const [selectedVendorId, setSelectedVendorId] = React.useState<string>('');
  const [vendorSearchTerm, setVendorSearchTerm] = React.useState('');
  const [contractSearchTerm, setContractSearchTerm] = React.useState('');

  React.useEffect(() => {
    dispatch(fetchVendorsAsync({}));
  }, [dispatch]);

  React.useEffect(() => {
    if (selectedVendorId) {
      dispatch(fetchContractsAsync({ 
        filters: { vendor: selectedVendorId }
      }));
    }
  }, [dispatch, selectedVendorId]);

  const items = watch('items');
  const discount = watch('discount');
  const taxRate = watch('taxRate');
  const currency = watch('currency');

  const onSubmit = async (data: FormValues) => {
    try {
      // Calculate the correct amounts
      const itemsWithCorrectTotals = data.items.map(item => ({ 
        ...item, 
        id: item.id || String(Date.now()), 
        totalPrice: item.quantity * item.unitPrice 
      }));
      
      const subtotal = itemsWithCorrectTotals.reduce((sum, item) => sum + item.totalPrice, 0);
      const discountAmount = subtotal * (data.discount / 100);
      const taxableAmount = subtotal - discountAmount;
      const taxAmount = taxableAmount * (data.taxRate / 100);
      const totalAmount = taxableAmount + taxAmount;

      const invoiceData = {
        vendorId: Number(data.vendorId),
        contractId: data.contractId ? Number(data.contractId) : undefined,
        amount: totalAmount, // Use the correctly calculated total
        currency: data.currency,
        status: 'draft',
        items: itemsWithCorrectTotals,
        taxes: taxAmount,
        penalties: 0,
        dueDate: data.dueDate,
      };
      
      console.log('Submitting invoice data:', invoiceData); // Debug log
      await dispatch(createInvoiceAsync(invoiceData));
      toast.success('Invoice created successfully');
      navigate('/invoices/list');
    } catch (error) {
      console.error('Invoice creation error:', error);
      toast.error('Failed to create invoice');
    }
  };

  return (
    <div className="p-6">
      <Button variant="ghost" onClick={() => navigate('/invoices/list')} className="mb-4">
        <ArrowLeft className="mr-2 h-4 w-4" /> Back to Invoices
      </Button>
      <Card>
        <CardHeader>
          <CardTitle>Create New Invoice</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="vendorId">Vendor</Label>
                <Controller
                  name="vendorId"
                  control={control}
                  render={({ field }) => {
                    const filteredVendors = vendors
                      .filter(vendor => vendor.name.toLowerCase().includes(vendorSearchTerm.toLowerCase()))
                      .slice(0, 10); // Show max 10 results
                    
                    return (
                      <Select 
                        onValueChange={(value) => {
                          field.onChange(value);
                          setSelectedVendorId(value);
                          setValue('contractId', ''); // Reset contract when vendor changes
                          setVendorSearchTerm(''); // Clear search after selection
                        }} 
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Search and select vendor..." />
                        </SelectTrigger>
                        <SelectContent>
                          <div className="p-2">
                            <Input
                              placeholder="Type to search vendors..."
                              value={vendorSearchTerm}
                              onChange={(e) => setVendorSearchTerm(e.target.value)}
                              className="mb-2"
                              autoFocus
                            />
                          </div>
                          {filteredVendors.length > 0 ? (
                            filteredVendors.map(vendor => (
                              <SelectItem key={vendor.id} value={String(vendor.id)}>
                                {vendor.name}
                              </SelectItem>
                            ))
                          ) : (
                            <div className="p-2 text-sm text-gray-500">
                              {vendorSearchTerm ? 'No vendors found' : 'Start typing to search vendors...'}
                            </div>
                          )}
                        </SelectContent>
                      </Select>
                    );
                  }}
                />
              </div>
              <div>
                <Label htmlFor="contractId">Contract (Optional)</Label>
                <Controller
                  name="contractId"
                  control={control}
                  render={({ field }) => {
                    const filteredContracts = contracts
                      .filter(contract => 
                        contract.vendor_id === Number(selectedVendorId) &&
                        contract.title.toLowerCase().includes(contractSearchTerm.toLowerCase())
                      )
                      .slice(0, 10); // Show max 10 results
                    
                    return (
                      <Select 
                        onValueChange={(value) => {
                          field.onChange(value);
                          setContractSearchTerm(''); // Clear search after selection
                        }} 
                        value={field.value}
                        disabled={!selectedVendorId}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={selectedVendorId ? "Search and select contract..." : "Select vendor first"} />
                        </SelectTrigger>
                        <SelectContent>
                          {selectedVendorId && (
                            <div className="p-2">
                              <Input
                                placeholder="Type to search contracts..."
                                value={contractSearchTerm}
                                onChange={(e) => setContractSearchTerm(e.target.value)}
                                className="mb-2"
                                autoFocus
                              />
                            </div>
                          )}
                          {selectedVendorId ? (
                            filteredContracts.length > 0 ? (
                              filteredContracts.map(contract => (
                                <SelectItem key={contract.id} value={String(contract.id)}>
                                  {contract.title}
                                </SelectItem>
                              ))
                            ) : (
                              <div className="p-2 text-sm text-gray-500">
                                {contractSearchTerm ? 'No contracts found' : contracts.length === 0 ? 'No contracts available for this vendor' : 'Start typing to search contracts...'}
                              </div>
                            )
                          ) : (
                            <div className="p-2 text-sm text-gray-500">
                              Please select a vendor first
                            </div>
                          )}
                        </SelectContent>
                      </Select>
                    );
                  }}
                />
              </div>
              <div>
                <Label htmlFor="dueDate">Due Date</Label>
                <Controller
                  name="dueDate"
                  control={control}
                  render={({ field }) => <Input type="date" {...field} />}
                />
              </div>
              <div>
                <Label htmlFor="currency">Currency</Label>
                <Controller
                  name="currency"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                        <SelectItem value="GBP">GBP</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
            <LineItemsTable
              lineItems={items}
              onChange={(newItems) => setValue('items', newItems.map(item => ({ ...item, totalPrice: item.quantity * item.unitPrice })))}
              mode="edit"
            />
            <InvoiceTotals
              lineItems={items}
              initialDiscount={discount}
              initialTaxRate={taxRate}
              currency={currency}
              onChange={(totals) => {
                setValue('discount', (totals.discount / totals.subtotal) * 100 || 0);
                setValue('taxRate', (totals.tax / (totals.subtotal - totals.discount)) * 100 || 0);
              }}
              mode="edit"
            />
            <Button type="submit">Create Invoice</Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default InvoiceCreate;