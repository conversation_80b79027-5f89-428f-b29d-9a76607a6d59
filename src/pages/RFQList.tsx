import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, RefreshCw, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/hooks/useAuth';
import { useRFQList } from '@/hooks/useRFQList';
import RFQCard from '@/components/rfq/RFQCard';
import RFQCardSkeleton from '@/components/rfq/RFQCardSkeleton';
import RFQFilters from '@/components/rfq/RFQFilters';
import RFQPagination from '@/components/rfq/RFQPagination';
import RFQBulkActions from '@/components/rfq/RFQBulkActions';

const RFQList: React.FC = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const {
    rfqs,
    loading,
    error,
    filters,
    searchQuery,
    selectedRFQs,
    pagination,
    handleSearch,
    handleFiltersChange,
    handlePage<PERSON>hange,
    handleLimitChange,
    handleRFQSelection,
    handleSelectAll,
    handleViewRFQ,
    handleEditRFQ,
    handleDeleteRFQ,
    handleSendInvitations,
    handleBulkDelete,
    handleBulkStatusUpdate,
    handleExport,
    refresh,
    hasSelection,
    isAllSelected,
    totalResults
  } = useRFQList({
    initialLimit: 12,
    autoLoad: true
  });

  const renderRFQGrid = () => {
    if (loading && rfqs.length === 0) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <RFQCardSkeleton key={index} />
          ))}
        </div>
      );
    }

    if (rfqs.length === 0 && !loading) {
      return (
        <div className="card-neumorphic p-8 text-center">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Plus className="w-8 h-8 text-primary" />
            </div>
            <h3 className="text-lg font-semibold mb-2">
              {searchQuery || Object.keys(filters).length > 0 ? 'No RFQs found' : 'No RFQs yet'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery || Object.keys(filters).length > 0 
                ? 'Try adjusting your search or filters to find what you\'re looking for.'
                : 'Get started by creating your first Request for Quote'
              }
            </p>
            {hasPermission('manager') && (
              <Button 
                onClick={() => navigate('/rfqs/create')}
                className="btn-neumorphic"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First RFQ
              </Button>
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {rfqs.map((rfq) => (
          <div key={rfq.id} className="relative">
            {hasSelection && (
              <div className="absolute top-2 left-2 z-10">
                <input
                  type="checkbox"
                  checked={selectedRFQs.includes(rfq.id)}
                  onChange={(e) => handleRFQSelection(rfq.id, e.target.checked)}
                  className="w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary focus:ring-2"
                />
              </div>
            )}
            <RFQCard
              rfq={rfq}
              onView={handleViewRFQ}
              onEdit={hasPermission('manager') ? handleEditRFQ : undefined}
              onDelete={hasPermission('manager') ? handleDeleteRFQ : undefined}
              onSendInvitations={hasPermission('manager') ? handleSendInvitations : undefined}
              showActions={hasPermission('manager')}
            />
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">RFQs</h1>
          <p className="text-muted-foreground mt-1">
            Manage your Request for Quote processes
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={refresh}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          {hasPermission('manager') && (
            <Button 
              onClick={() => navigate('/rfqs/create')}
              className="btn-neumorphic"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create RFQ
            </Button>
          )}
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <RFQFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onSearch={handleSearch}
        searchQuery={searchQuery}
        totalResults={totalResults}
        loading={loading}
      />

      {/* Bulk Actions */}
      {hasPermission('manager') && (
        <RFQBulkActions
          selectedCount={selectedRFQs.length}
          totalCount={rfqs.length}
          isAllSelected={isAllSelected}
          onSelectAll={handleSelectAll}
          onBulkDelete={handleBulkDelete}
          onBulkStatusUpdate={handleBulkStatusUpdate}
          onExport={handleExport}
          loading={loading}
        />
      )}

      {/* RFQ Grid */}
      {renderRFQGrid()}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <RFQPagination
          currentPage={pagination.page}
          totalPages={pagination.totalPages}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleLimitChange}
          loading={loading}
        />
      )}
    </div>
  );
};

export default RFQList;