import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { Save, X, Play, Settings, Layers, Zap } from 'lucide-react';
import { AppDispatch, RootState } from '@/store';
import { 
  createWorkflow, 
  updateWorkflow, 
  setCurrentWorkflow,
  clearError 
} from '@/store/slices/workflowsSlice';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import TriggerSelector from '@/components/workflows/TriggerSelector';
import StepPalette from '@/components/workflows/StepPalette';
import WorkflowCanvas from '@/components/workflows/WorkflowCanvas';
import ConfigPanel from '@/components/workflows/ConfigPanel';

const WorkflowCreate: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  
  const { currentWorkflow, isLoading, error } = useSelector((state: RootState) => state.workflows);
  
  const [workflowName, setWorkflowName] = useState('');
  const [workflowDescription, setWorkflowDescription] = useState('');
  const [selectedTrigger, setSelectedTrigger] = useState<any>(null);
  const [isTestMode, setIsTestMode] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Check if we're duplicating a workflow
  const duplicateFrom = location.state?.duplicateFrom;

  useEffect(() => {
    if (duplicateFrom) {
      setWorkflowName(`${duplicateFrom.name} (Copy)`);
      setWorkflowDescription(duplicateFrom.description);
      setSelectedTrigger(duplicateFrom.trigger);
      
      // Set up the workflow for duplication
      dispatch(setCurrentWorkflow({
        ...duplicateFrom,
        id: '',
        name: `${duplicateFrom.name} (Copy)`,
        status: 'draft',
        created_at: '',
        updated_at: '',
        success_rate: 0,
        execution_count: 0
      }));
    } else {
      // Initialize empty workflow
      dispatch(setCurrentWorkflow({
        id: '',
        name: '',
        description: '',
        status: 'draft',
        trigger: {
          type: 'manual',
          config: {}
        },
        steps: [],
        created_at: '',
        updated_at: '',
        success_rate: 0,
        execution_count: 0,
        created_by: user?.email || ''
      }));
    }

    return () => {
      dispatch(clearError());
    };
  }, [dispatch, duplicateFrom, user]);

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  const handleSave = async (isDraft = true) => {
    if (!workflowName.trim()) {
      toast.error('Please enter a workflow name');
      return;
    }

    if (!selectedTrigger) {
      toast.error('Please select a trigger for your workflow');
      return;
    }

    const workflowData = {
      name: workflowName.trim(),
      description: workflowDescription.trim(),
      status: isDraft ? 'draft' as const : 'active' as const,
      trigger: selectedTrigger,
      steps: currentWorkflow?.steps || [],
      created_by: user?.email || ''
    };

    try {
      if (currentWorkflow?.id) {
        // Update existing workflow
        await dispatch(updateWorkflow({
          id: currentWorkflow.id,
          updates: workflowData
        })).unwrap();
        toast.success('Workflow updated successfully');
      } else {
        // Create new workflow
        const result = await dispatch(createWorkflow(workflowData)).unwrap();
        toast.success('Workflow created successfully');
        navigate(`/workflows/${result.id}/edit`);
      }
      
      setHasUnsavedChanges(false);
    } catch (error) {
      toast.error('Failed to save workflow');
    }
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      if (window.confirm('You have unsaved changes. Are you sure you want to leave?')) {
        navigate('/workflows');
      }
    } else {
      navigate('/workflows');
    }
  };

  const handleTestWorkflow = () => {
    if (!selectedTrigger || !currentWorkflow?.steps.length) {
      toast.error('Please configure trigger and add steps before testing');
      return;
    }
    
    setIsTestMode(true);
    toast.info('Test mode activated - workflow will run with mock data');
  };

  const validateWorkflow = () => {
    const errors: string[] = [];
    
    if (!workflowName.trim()) errors.push('Workflow name is required');
    if (!selectedTrigger) errors.push('Trigger is required');
    if (!currentWorkflow?.steps.length) errors.push('At least one step is required');
    
    // Check for disconnected steps
    const connectedSteps = new Set<string>();
    currentWorkflow?.steps.forEach(step => {
      step.connections.forEach(conn => connectedSteps.add(conn));
    });
    
    const orphanedSteps = currentWorkflow?.steps.filter(step => 
      step.id !== currentWorkflow.steps[0]?.id && !connectedSteps.has(step.id)
    );
    
    if (orphanedSteps?.length) {
      errors.push(`${orphanedSteps.length} step(s) are not connected to the workflow`);
    }
    
    return errors;
  };

  const validationErrors = validateWorkflow();
  const canSave = validationErrors.length === 0;

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {currentWorkflow?.id ? 'Edit Workflow' : 'Create Workflow'}
              </h1>
              <p className="text-sm text-gray-600">
                Build automated processes with drag-and-drop simplicity
              </p>
            </div>
            {isTestMode && (
              <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                <Play className="h-3 w-3 mr-1" />
                Test Mode
              </Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            
            <Button
              variant="outline"
              onClick={handleTestWorkflow}
              disabled={!canSave || isLoading}
              className="text-blue-600 border-blue-200 hover:bg-blue-50"
            >
              <Play className="h-4 w-4 mr-2" />
              Test
            </Button>
            
            <Button
              onClick={() => handleSave(false)}
              disabled={!canSave || isLoading}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Save className="h-4 w-4 mr-2" />
              Save & Activate
            </Button>
            
            <Button
              onClick={() => handleSave(true)}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Draft
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Configuration */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Workflow Details */}
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Workflow Details</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name *
                </label>
                <Input
                  value={workflowName}
                  onChange={(e) => {
                    setWorkflowName(e.target.value);
                    setHasUnsavedChanges(true);
                  }}
                  placeholder="Enter workflow name"
                  className="w-full"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <Textarea
                  value={workflowDescription}
                  onChange={(e) => {
                    setWorkflowDescription(e.target.value);
                    setHasUnsavedChanges(true);
                  }}
                  placeholder="Describe what this workflow does"
                  className="w-full resize-none"
                  rows={3}
                />
              </div>
            </div>
          </div>

          {/* Trigger Configuration */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-2 mb-4">
              <Zap className="h-5 w-5 text-yellow-500" />
              <h3 className="text-lg font-semibold text-gray-900">Trigger</h3>
            </div>
            
            <TriggerSelector
              selectedTrigger={selectedTrigger}
              onTriggerChange={(trigger) => {
                setSelectedTrigger(trigger);
                setHasUnsavedChanges(true);
              }}
            />
          </div>

          {/* Step Palette */}
          <div className="flex-1 p-4">
            <div className="flex items-center space-x-2 mb-4">
              <Layers className="h-5 w-5 text-blue-500" />
              <h3 className="text-lg font-semibold text-gray-900">Steps</h3>
            </div>
            
            <StepPalette />
          </div>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="p-4 border-t border-gray-200">
              <Card className="border-red-200 bg-red-50">
                <CardContent className="p-3">
                  <h4 className="text-sm font-medium text-red-800 mb-2">
                    Validation Errors:
                  </h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {validationErrors.map((error, index) => (
                      <li key={index} className="flex items-start">
                        <span className="mr-2">•</span>
                        <span>{error}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Main Canvas Area */}
        <div className="flex-1 flex flex-col">
          <WorkflowCanvas 
            isTestMode={isTestMode}
            onStepAdd={() => setHasUnsavedChanges(true)}
            onStepUpdate={() => setHasUnsavedChanges(true)}
            onStepDelete={() => setHasUnsavedChanges(true)}
          />
        </div>

        {/* Right Sidebar - Step Configuration */}
        <div className="w-80 bg-white border-l border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-gray-500" />
              <h3 className="text-lg font-semibold text-gray-900">Configuration</h3>
            </div>
          </div>
          
          <ConfigPanel 
            onConfigChange={() => setHasUnsavedChanges(true)}
          />
        </div>
      </div>
    </div>
  );
};

export default WorkflowCreate;