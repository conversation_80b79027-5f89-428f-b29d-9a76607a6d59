import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Mail, ArrowLeft, Send, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useToast } from '../hooks/use-toast';
import { AuthFormContainer } from '../components/auth/AuthFormContainer';
import { forgotPasswordSchema, ForgotPasswordFormData } from '../lib/authValidation';

export const ForgotPassword: React.FC = () => {
  const [isSuccess, setIsSuccess] = useState(false);
  const [submittedEmail, setSubmittedEmail] = useState('');
  const { forgotPassword, isLoading, error } = useAuth();
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: yupResolver(forgotPasswordSchema),
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    try {
      await forgotPassword(data);
      setSubmittedEmail(data.email);
      setIsSuccess(true);
      toast({
        title: 'Reset email sent!',
        description: 'Please check your inbox for password reset instructions.',
      });
    } catch (err) {
      toast({
        title: 'Failed to send reset email',
        description: error || 'Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (isSuccess) {
    return (
      <AuthFormContainer
        title="Check Your Email"
        subtitle="Password reset instructions sent"
      >
        <div className="text-center space-y-6">
          {/* Success Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-success/10 rounded-full"
          >
            <CheckCircle className="w-10 h-10 text-success" />
          </motion.div>

          {/* Success Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-3"
          >
            <h2 className="text-xl font-semibold text-foreground">
              Reset Link Sent
            </h2>
            <p className="text-muted-foreground">
              We've sent password reset instructions to{' '}
              <span className="font-medium text-foreground">{submittedEmail}</span>
            </p>
            <p className="text-sm text-muted-foreground">
              Click the link in the email to reset your password. The link expires in 1 hour.
            </p>
          </motion.div>

          {/* Instructions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="p-4 bg-info/5 border border-info/20 rounded-xl text-left"
          >
            <h3 className="text-sm font-medium text-info mb-2">What's next?</h3>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>• Check your inbox (and spam folder)</li>
              <li>• Click the reset link in the email</li>
              <li>• Create a new password</li>
              <li>• Sign in with your new password</li>
            </ul>
          </motion.div>

          {/* Back to Login */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="pt-6 border-t border-border"
          >
            <Link
              to="/login"
              className="inline-flex items-center space-x-2 text-primary hover:text-primary-dark transition-colors font-medium"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Sign In</span>
            </Link>
          </motion.div>
        </div>
      </AuthFormContainer>
    );
  }

  return (
    <AuthFormContainer
      title="Reset Password"
      subtitle="Enter your email to receive reset instructions"
    >
      {/* Error Alert */}
      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-6 p-3 bg-destructive/10 border border-destructive/20 rounded-xl flex items-center space-x-2"
        >
          <AlertCircle className="w-4 h-4 text-destructive" />
          <span className="text-sm text-destructive">{error}</span>
        </motion.div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Email Field */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Email Address
          </label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              {...register('email')}
              type="email"
              className={`input-neumorphic w-full pl-10 pr-4 ${
                errors.email ? 'border-destructive' : ''
              }`}
              placeholder="Enter your email address"
              autoFocus
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-destructive">{errors.email.message}</p>
          )}
        </div>

        {/* Info Box */}
        <div className="p-4 bg-info/5 border border-info/20 rounded-xl">
          <p className="text-sm text-muted-foreground">
            We'll send you a secure link to reset your password. Make sure to check your spam folder if you don't see the email.
          </p>
        </div>

        {/* Submit Button */}
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          type="submit"
          disabled={isLoading}
          className="w-full btn-neumorphic gradient-primary text-primary-foreground font-medium py-3 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground"></div>
              <span>Sending...</span>
            </div>
          ) : (
            <div className="flex items-center justify-center space-x-2">
              <Send className="w-4 h-4" />
              <span>Send Reset Link</span>
            </div>
          )}
        </motion.button>
      </form>

      {/* Footer */}
      <div className="mt-8 text-center space-y-4">
        <Link
          to="/login"
          className="inline-flex items-center space-x-2 text-primary hover:text-primary-dark transition-colors font-medium"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Sign In</span>
        </Link>
        
        <div className="pt-4 border-t border-border">
          <p className="text-sm text-muted-foreground">
            Don't have an account?{' '}
            <Link
              to="/register"
              className="text-primary hover:text-primary-dark transition-colors font-medium"
            >
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </AuthFormContainer>
  );
};