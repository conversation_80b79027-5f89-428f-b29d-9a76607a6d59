import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Plus, Search, Filter, Activity, Clock, CheckCircle, XCircle } from 'lucide-react';
import { AppDispatch, RootState } from '@/store';
import { fetchWorkflows, setFilters } from '@/store/slices/workflowsSlice';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import WorkflowCard from '@/components/workflows/WorkflowCard';

const WorkflowsList: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { user, canEdit } = useAuth();
  
  const { 
    workflows, 
    isLoading, 
    error, 
    filters 
  } = useSelector((state: RootState) => state.workflows);

  const [searchTerm, setSearchTerm] = useState(filters.search);

  useEffect(() => {
    dispatch(fetchWorkflows());
  }, [dispatch]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      dispatch(setFilters({ search: searchTerm }));
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, dispatch]);

  const handleStatusFilter = (status: string) => {
    dispatch(setFilters({ status: status as any }));
  };

  const handleTriggerFilter = (trigger: string) => {
    dispatch(setFilters({ trigger }));
  };

  const filteredWorkflows = workflows.filter(workflow => {
    const matchesSearch = workflow.name.toLowerCase().includes(filters.search.toLowerCase()) ||
                         workflow.description.toLowerCase().includes(filters.search.toLowerCase());
    const matchesStatus = filters.status === 'all' || workflow.status === filters.status;
    const matchesTrigger = !filters.trigger || workflow.trigger.type === filters.trigger;
    
    return matchesSearch && matchesStatus && matchesTrigger;
  });

  const getWorkflowStats = () => {
    const total = workflows.length;
    const active = workflows.filter(w => w.status === 'active').length;
    const inactive = workflows.filter(w => w.status === 'inactive').length;
    const draft = workflows.filter(w => w.status === 'draft').length;
    
    return { total, active, inactive, draft };
  };

  const stats = getWorkflowStats();

  const triggerTypes = [
    { value: '', label: 'All Triggers' },
    { value: 'vendor_onboarded', label: 'Vendor Onboarded' },
    { value: 'contract_expiring', label: 'Contract Expiring' },
    { value: 'invoice_approved', label: 'Invoice Approved' },
    { value: 'performance_score_changed', label: 'Performance Score Changed' },
    { value: 'manual', label: 'Manual Trigger' }
  ];

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 text-red-600">
              <XCircle className="h-5 w-5" />
              <span>Error loading workflows: {error}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Workflows & Automation</h1>
          <p className="text-gray-600 mt-1">
            Create and manage automated business processes
          </p>
        </div>
        {canEdit && (
          <Button 
            onClick={() => navigate('/workflows/create')}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Workflow
          </Button>
        )}
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Total Workflows</p>
                <p className="text-2xl font-bold text-blue-900">{stats.total}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Active</p>
                <p className="text-2xl font-bold text-green-900">{stats.active}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600">Inactive</p>
                <p className="text-2xl font-bold text-red-900">{stats.inactive}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600">Draft</p>
                <p className="text-2xl font-bold text-yellow-900">{stats.draft}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="bg-white shadow-sm border border-gray-200">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search workflows by name or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Status Filter */}
            <Select value={filters.status} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>

            {/* Trigger Filter */}
            <Select value={filters.trigger} onValueChange={handleTriggerFilter}>
              <SelectTrigger className="w-full md:w-56">
                <SelectValue placeholder="Filter by trigger" />
              </SelectTrigger>
              <SelectContent>
                {triggerTypes.map(trigger => (
                  <SelectItem key={trigger.value} value={trigger.value}>
                    {trigger.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Workflows Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <Card key={index} className="bg-white shadow-sm border border-gray-200">
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-4 w-2/3" />
                  <div className="flex justify-between">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-20" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredWorkflows.length === 0 ? (
        <Card className="bg-white shadow-sm border border-gray-200">
          <CardContent className="p-12 text-center">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {workflows.length === 0 ? 'No workflows yet' : 'No workflows match your filters'}
            </h3>
            <p className="text-gray-600 mb-6">
              {workflows.length === 0 
                ? 'Get started by creating your first automated workflow'
                : 'Try adjusting your search or filter criteria'
              }
            </p>
            {canEdit && workflows.length === 0 && (
              <Button 
                onClick={() => navigate('/workflows/create')}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Workflow
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWorkflows.map((workflow) => (
            <WorkflowCard 
              key={workflow.id} 
              workflow={workflow}
              canEdit={canEdit}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default WorkflowsList;