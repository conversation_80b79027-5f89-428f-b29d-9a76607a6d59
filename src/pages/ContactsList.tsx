import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Users, Plus, Download, Upload, MoreHorizontal } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { ContactList } from '../components/contacts';
import { useAuth } from '../hooks/useAuth';
import { toast } from '../components/ui/sonner';
import { ContactService } from '../services/contactService';
import { 
  Contact, 
  ContactSearchFilters, 
  PaginatedContacts,
  DEFAULT_PAGINATION_LIMIT 
} from '../types/contact';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

export const ContactsList: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { canEdit, canView } = useAuth();
  
  const [contacts, setContacts] = useState<PaginatedContacts>({
    contacts: [],
    pagination: {
      page: 1,
      limit: DEFAULT_PAGINATION_LIMIT,
      total: 0,
      totalPages: 0
    }
  });
  const [isLoading, setIsLoading] = useState(true);
  const [deleteContactId, setDeleteContactId] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Load contacts
  const loadContacts = async (filters: ContactSearchFilters = {}) => {
    try {
      setIsLoading(true);
      
      // Get filters from URL params
      const urlFilters: ContactSearchFilters = {
        search: searchParams.get('search') || undefined,
        accountId: searchParams.get('accountId') ? parseInt(searchParams.get('accountId')!) : undefined,
        department: searchParams.get('department') ? [searchParams.get('department')!] : undefined,
        level: searchParams.get('level') ? [searchParams.get('level') as any] : undefined,
        status: searchParams.get('status') ? [searchParams.get('status') as any] : undefined,
        page: parseInt(searchParams.get('page') || '1'),
        limit: parseInt(searchParams.get('limit') || DEFAULT_PAGINATION_LIMIT.toString()),
        ...filters
      };

      const response = await ContactService.searchContacts(urlFilters);
      setContacts(response);
    } catch (error: any) {
      console.error('Failed to load contacts:', error);
      toast.error('Failed to load contacts', {
        description: error.message || 'An unexpected error occurred',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    if (canView()) {
      loadContacts();
    }
  }, [searchParams, canView]);

  // Check permissions on mount
  useEffect(() => {
    if (!canView()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to view contacts.',
      });
      navigate('/dashboard');
    }
  }, [canView, navigate]);

  // Handle search and filters
  const handleSearch = (filters: ContactSearchFilters) => {
    const newParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          if (value.length > 0) {
            newParams.set(key, value[0].toString());
          }
        } else {
          newParams.set(key, value.toString());
        }
      }
    });

    setSearchParams(newParams);
  };

  // Handle contact actions
  const handleContactView = (contact: Contact) => {
    navigate(`/contacts/${contact.id}`);
  };

  const handleContactEdit = (contact: Contact) => {
    if (!canEdit()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to edit contacts.',
      });
      return;
    }
    navigate(`/contacts/${contact.id}/edit`);
  };

  const handleContactDelete = (contactId: number) => {
    if (!canEdit()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to delete contacts.',
      });
      return;
    }
    setDeleteContactId(contactId);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!deleteContactId) return;

    try {
      setIsDeleting(true);
      await ContactService.deleteContact(deleteContactId);
      
      toast.success('Contact deleted successfully');
      
      // Reload contacts
      await loadContacts();
    } catch (error: any) {
      console.error('Failed to delete contact:', error);
      toast.error('Failed to delete contact', {
        description: error.message || 'An unexpected error occurred',
      });
    } finally {
      setIsDeleting(false);
      setDeleteContactId(null);
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      // Get current filters for export
      const filters: ContactSearchFilters = {
        search: searchParams.get('search') || undefined,
        accountId: searchParams.get('accountId') ? parseInt(searchParams.get('accountId')!) : undefined,
        department: searchParams.get('department') ? [searchParams.get('department')!] : undefined,
        level: searchParams.get('level') ? [searchParams.get('level') as any] : undefined,
        status: searchParams.get('status') ? [searchParams.get('status') as any] : undefined,
      };

      await ContactService.exportContacts(filters);
      toast.success('Contacts exported successfully');
    } catch (error: any) {
      console.error('Failed to export contacts:', error);
      toast.error('Failed to export contacts', {
        description: error.message || 'An unexpected error occurred',
      });
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action: string, contactIds: number[]) => {
    if (!canEdit()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to perform bulk actions.',
      });
      return;
    }

    try {
      switch (action) {
        case 'export':
          // Export selected contacts
          await ContactService.exportContacts({ contactIds });
          toast.success(`Exported ${contactIds.length} contacts`);
          break;
        case 'delete':
          // Bulk delete contacts
          await ContactService.bulkDeleteContacts(contactIds);
          toast.success(`Deleted ${contactIds.length} contacts`);
          await loadContacts();
          break;
        default:
          toast.error('Unknown bulk action');
      }
    } catch (error: any) {
      console.error('Failed to perform bulk action:', error);
      toast.error('Failed to perform bulk action', {
        description: error.message || 'An unexpected error occurred',
      });
    }
  };

  // Don't render if user doesn't have permission
  if (!canView()) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="border-b bg-card"
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <Users className="h-8 w-8 text-primary" />
                Contacts
              </h1>
              <p className="text-muted-foreground mt-1">
                Manage your client contacts and relationships
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Export Button */}
              <Button variant="outline" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>

              {/* More Actions */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleExport}>
                    <Download className="mr-2 h-4 w-4" />
                    Export All Contacts
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/contacts/import')}>
                    <Upload className="mr-2 h-4 w-4" />
                    Import Contacts
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigate('/contacts/statistics')}>
                    View Statistics
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Create Contact Button */}
              {canEdit() && (
                <Button onClick={() => navigate('/contacts/create')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Contact
                </Button>
              )}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="container mx-auto px-4 py-8"
      >
        <ContactList
          contacts={contacts}
          onContactView={handleContactView}
          onContactEdit={canEdit() ? handleContactEdit : undefined}
          onContactDelete={canEdit() ? handleContactDelete : undefined}
          onSearch={handleSearch}
          onExport={handleExport}
          onBulkAction={canEdit() ? handleBulkAction : undefined}
          isLoading={isLoading}
          showActions={true}
          showBulkActions={canEdit()}
          showFilters={true}
        />
      </motion.div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteContactId !== null} onOpenChange={() => setDeleteContactId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Contact</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this contact? This action cannot be undone.
              The contact will be soft deleted and can be restored by an administrator.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete Contact'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};