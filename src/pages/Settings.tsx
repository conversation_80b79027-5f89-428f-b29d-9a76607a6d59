import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Users, 
  Settings as SettingsIcon, 
  Brain, 
  Database, 
  Shield, 
  UserCog, 
  Palette, 
  Bell,
  Receipt,
  ChevronRight,
  Search,
  Filter,
  Grid3X3,
  List,
  Workflow,
  FileText,
  DollarSign,
  TrendingUp,
  Lock,
  Globe,
  Zap
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '../components/ui/tabs';
import { useAuth } from '../hooks/useAuth';

interface SettingCard {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
  requiredRole?: 'admin' | 'manager' | 'viewer';
  category: 'admin' | 'user' | 'system' | 'modules';
  badge?: string;
  popular?: boolean;
}

const settingsCards: SettingCard[] = [
  // Admin Settings
  {
    title: 'User Management',
    description: 'Manage users, roles, and permissions across the system',
    icon: Users,
    path: '/admin/users',
    requiredRole: 'admin',
    category: 'admin',
    badge: 'Critical',
    popular: true
  },
  {
    title: 'System Settings',
    description: 'Configure system-wide settings and preferences',
    icon: SettingsIcon,
    path: '/admin/settings',
    requiredRole: 'admin',
    category: 'admin'
  },
  {
    title: 'AI Configuration',
    description: 'Configure AI providers, models, and recommendation settings',
    icon: Brain,
    path: '/admin/ai-settings',
    requiredRole: 'admin',
    category: 'admin',
    badge: 'New'
  },
  {
    title: 'Backup & Restore',
    description: 'Manage system backups and data restoration',
    icon: Database,
    path: '/admin/backup',
    requiredRole: 'admin',
    category: 'admin'
  },
  {
    title: 'Security Settings',
    description: 'Configure authentication, encryption, and security policies',
    icon: Lock,
    path: '/admin/security',
    requiredRole: 'admin',
    category: 'admin',
    badge: 'Important'
  },
  
  // User Settings
  {
    title: 'Profile Settings',
    description: 'Update your personal information and preferences',
    icon: UserCog,
    path: '/profile/settings',
    category: 'user',
    popular: true
  },
  {
    title: 'Notification Preferences',
    description: 'Configure your notification and alert settings',
    icon: Bell,
    path: '/profile/notifications',
    category: 'user'
  },
  {
    title: 'Theme & Appearance',
    description: 'Customize the appearance and theme of your dashboard',
    icon: Palette,
    path: '/profile/appearance',
    category: 'user'
  },
  {
    title: 'Language & Region',
    description: 'Set your language, timezone, and regional preferences',
    icon: Globe,
    path: '/profile/localization',
    category: 'user'
  },
  {
    title: 'Privacy & Data',
    description: 'Manage your data privacy and download preferences',
    icon: Shield,
    path: '/profile/privacy',
    category: 'user'
  },
  
  // System Configuration
  {
    title: 'Invoice Settings',
    description: 'Configure invoice templates, numbering, and payment settings',
    icon: Receipt,
    path: '/invoices/settings',
    requiredRole: 'admin',
    category: 'system'
  },
  {
    title: 'Payment Configuration',
    description: 'Set up payment gateways and processing options',
    icon: DollarSign,
    path: '/system/payments',
    requiredRole: 'admin',
    category: 'system'
  },
  {
    title: 'Email Templates',
    description: 'Customize email templates for notifications and communications',
    icon: FileText,
    path: '/system/email-templates',
    requiredRole: 'admin',
    category: 'system'
  },
  {
    title: 'Integration Settings',
    description: 'Configure external integrations and API connections',
    icon: Zap,
    path: '/system/integrations',
    requiredRole: 'admin',
    category: 'system',
    badge: 'Beta'
  },

  // Module Settings
  {
    title: 'Vendor Management',
    description: 'Configure vendor onboarding, scoring, and management settings',
    icon: Users,
    path: '/modules/vendor-settings',
    requiredRole: 'manager',
    category: 'modules'
  },
  {
    title: 'Contract Management',
    description: 'Set up contract templates, approval workflows, and terms',
    icon: FileText,
    path: '/modules/contract-settings',
    requiredRole: 'manager',
    category: 'modules'
  },
  {
    title: 'Workflow Automation',
    description: 'Configure automated workflows and business processes',
    icon: Workflow,
    path: '/modules/workflow-settings',
    requiredRole: 'manager',
    category: 'modules'
  },
  {
    title: 'Analytics & Reports',
    description: 'Customize dashboards, reports, and data visualization',
    icon: TrendingUp,
    path: '/modules/analytics-settings',
    requiredRole: 'manager',
    category: 'modules'
  }
];

export const Settings: React.FC = () => {
  const navigate = useNavigate();
  const { hasPermission, user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeCategory, setActiveCategory] = useState<'all' | 'admin' | 'user' | 'system' | 'modules'>('all');

  const handleCardClick = (path: string) => {
    navigate(path);
  };

  const getVisibleCards = (category?: string) => {
    return settingsCards.filter(card => {
      // Filter by category
      if (category && category !== 'all' && card.category !== category) return false;
      
      // Filter by role permissions
      if (card.requiredRole && !hasPermission(card.requiredRole)) return false;
      
      // Filter by search term
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          card.title.toLowerCase().includes(searchLower) ||
          card.description.toLowerCase().includes(searchLower)
        );
      }
      
      return true;
    });
  };

  const categories = [
    { key: 'all', title: 'All Settings', description: 'View all available settings', count: getVisibleCards().length },
    { key: 'admin', title: 'Administration', description: 'System administration and management', count: getVisibleCards('admin').length },
    { key: 'user', title: 'Personal', description: 'Personal preferences and account settings', count: getVisibleCards('user').length },
    { key: 'system', title: 'System', description: 'System-wide configurations', count: getVisibleCards('system').length },
    { key: 'modules', title: 'Modules', description: 'Module-specific settings', count: getVisibleCards('modules').length }
  ];

  const popularCards = getVisibleCards().filter(card => card.popular);
  const filteredCards = getVisibleCards(activeCategory === 'all' ? undefined : activeCategory);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const renderCard = (card: SettingCard, index: number) => {
    const Icon = card.icon;
    return (
      <motion.div
        key={card.path}
        variants={cardVariants}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Card
          className="group cursor-pointer transition-all duration-300 hover:shadow-lg bg-card border border-border hover:border-primary/50"
          onClick={() => handleCardClick(card.path)}
        >
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                  <Icon className="w-6 h-6 text-primary" />
                </div>
                <div className="flex-1">
                  <CardTitle className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors">
                    {card.title}
                  </CardTitle>
                  {card.badge && (
                    <Badge 
                      variant={card.badge === 'New' ? 'default' : card.badge === 'Critical' ? 'destructive' : 'secondary'}
                      className="mt-1 text-xs"
                    >
                      {card.badge}
                    </Badge>
                  )}
                </div>
              </div>
              <ChevronRight className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors" />
            </div>
          </CardHeader>
          <CardContent>
            <CardDescription className="text-sm text-muted-foreground leading-relaxed">
              {card.description}
            </CardDescription>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className="p-6 space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-foreground">Settings</h1>
            <p className="text-muted-foreground">
              Configure and customize your VendorMS experience
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search settings..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Popular Settings */}
      {!searchTerm && popularCards.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-foreground">Popular Settings</h2>
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {popularCards.map((card, index) => renderCard(card, index))}
          </motion.div>
        </div>
      )}

      {/* Category Tabs */}
      <Tabs value={activeCategory} onValueChange={(value) => setActiveCategory(value as any)}>
        <TabsList className="grid w-full grid-cols-5">
          {categories.map(category => (
            <TabsTrigger key={category.key} value={category.key} className="text-sm">
              {category.title}
              {category.count > 0 && (
                <Badge variant="secondary" className="ml-2 text-xs">
                  {category.count}
                </Badge>
              )}
            </TabsTrigger>
          ))}
        </TabsList>

        {categories.map(category => (
          <TabsContent key={category.key} value={category.key} className="space-y-6">
            {!searchTerm && (
              <div className="space-y-1">
                <h2 className="text-xl font-semibold text-foreground">{category.title}</h2>
                <p className="text-sm text-muted-foreground">{category.description}</p>
              </div>
            )}

            {filteredCards.length > 0 ? (
              <motion.div 
                className={viewMode === 'grid' 
                  ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" 
                  : "space-y-4"
                }
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                {filteredCards.map((card, index) => renderCard(card, index))}
              </motion.div>
            ) : (
              <div className="text-center py-12">
                <Shield className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-foreground mb-2">
                  {searchTerm ? 'No settings found' : 'No settings available'}
                </h3>
                <p className="text-muted-foreground">
                  {searchTerm 
                    ? 'Try adjusting your search terms.'
                    : "You don't have permission to access settings in this category."
                  }
                </p>
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default Settings;