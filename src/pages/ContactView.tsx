import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, User, Edit, Trash2, Mail, Phone, Building2, Users } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '../hooks/useAuth';
import { toast } from '../components/ui/sonner';
import { ContactService } from '../services/contactService';
import { Contact, ContactStatus, ContactLevel } from '../types/contact';

export const ContactView: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { canEdit, canView } = useAuth();
  
  const [contact, setContact] = useState<Contact | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load contact data
  useEffect(() => {
    const loadContact = async () => {
      if (!id || !canView()) return;

      try {
        setIsLoading(true);
        const contactData = await ContactService.getContact(parseInt(id));
        setContact(contactData);
      } catch (error: unknown) {
        console.error('Failed to load contact:', error);
        toast.error('Failed to load contact', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred',
        });
        navigate('/contacts/list');
      } finally {
        setIsLoading(false);
      }
    };

    loadContact();
  }, [id, canView, navigate]);

  // Check permissions
  useEffect(() => {
    if (!canView()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to view contacts.',
      });
      navigate('/contacts/list');
    }
  }, [canView, navigate]);

  // Handle edit
  const handleEdit = () => {
    if (!canEdit()) {
      toast.error('Access Denied', {
        description: 'You do not have permission to edit contacts.',
      });
      return;
    }
    navigate(`/contacts/${id}/edit`);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!canEdit() || !contact) return;

    if (window.confirm('Are you sure you want to delete this contact?')) {
      try {
        await ContactService.deleteContact(contact.id);
        toast.success('Contact deleted successfully');
        navigate('/contacts/list');
      } catch (error: unknown) {
        toast.error('Failed to delete contact', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred',
        });
      }
    }
  };

  // Get contact initials
  const getContactInitials = (contact: Contact) => {
    const firstName = contact.first_name || '';
    const lastName = contact.last_name || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Get status badge variant
  const getStatusVariant = (status: ContactStatus) => {
    switch (status) {
      case ContactStatus.ACTIVE:
        return 'default';
      case ContactStatus.INACTIVE:
        return 'secondary';
      case ContactStatus.ARCHIVED:
        return 'outline';
      case ContactStatus.PENDING:
        return 'destructive';
      default:
        return 'default';
    }
  };

  if (!canView()) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading contact...</p>
        </div>
      </div>
    );
  }

  if (!contact) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <User className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">Contact not found</h3>
          <p className="mt-2 text-muted-foreground">
            The contact you're looking for doesn't exist or has been deleted.
          </p>
          <Button className="mt-4" onClick={() => navigate('/contacts/list')}>
            Back to Contacts
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="border-b bg-card"
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/contacts/list')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Contacts
              </Button>
              <div className="h-6 w-px bg-border" />
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${contact.full_name}`} />
                  <AvatarFallback>{getContactInitials(contact)}</AvatarFallback>
                </Avatar>
                <div>
                  <h1 className="text-2xl font-bold">
                    {contact.full_name || `${contact.first_name || ''} ${contact.last_name}`.trim()}
                  </h1>
                  <p className="text-muted-foreground">
                    {contact.title && `${contact.title} • `}
                    {contact.account?.name}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant={getStatusVariant(contact.status)}>
                {contact.status === ContactStatus.ACTIVE ? 'Active' : 
                 contact.status === ContactStatus.INACTIVE ? 'Inactive' :
                 contact.status === ContactStatus.ARCHIVED ? 'Archived' : 'Pending'}
              </Badge>
              
              {canEdit() && (
                <>
                  <Button variant="outline" onClick={handleEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button variant="outline" onClick={handleDelete}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="container mx-auto px-4 py-8"
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Email</label>
                    <div className="flex items-center gap-2 mt-1">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{contact.email}</span>
                    </div>
                  </div>
                  
                  {contact.phone && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Phone</label>
                      <div className="flex items-center gap-2 mt-1">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{contact.phone}</span>
                      </div>
                    </div>
                  )}
                  
                  {contact.mobile_phone && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Mobile</label>
                      <div className="flex items-center gap-2 mt-1">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{contact.mobile_phone}</span>
                      </div>
                    </div>
                  )}
                  
                  {contact.department && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Department</label>
                      <div className="flex items-center gap-2 mt-1">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span>{contact.department}</span>
                      </div>
                    </div>
                  )}
                </div>
                
                {contact.description && (
                  <>
                    <Separator />
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Description</label>
                      <p className="mt-1 text-sm">{contact.description}</p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Address Information */}
            {(contact.mailing_address || contact.other_address) && (
              <Card>
                <CardHeader>
                  <CardTitle>Address Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {contact.mailing_address && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Mailing Address</label>
                      <div className="mt-1 text-sm">
                        {contact.mailing_address.street && <div>{contact.mailing_address.street}</div>}
                        <div>
                          {contact.mailing_address.city && `${contact.mailing_address.city}, `}
                          {contact.mailing_address.state && `${contact.mailing_address.state} `}
                          {contact.mailing_address.postalCode}
                        </div>
                        {contact.mailing_address.country && <div>{contact.mailing_address.country}</div>}
                      </div>
                    </div>
                  )}
                  
                  {contact.other_address && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Other Address</label>
                      <div className="mt-1 text-sm">
                        {contact.other_address.street && <div>{contact.other_address.street}</div>}
                        <div>
                          {contact.other_address.city && `${contact.other_address.city}, `}
                          {contact.other_address.state && `${contact.other_address.state} `}
                          {contact.other_address.postalCode}
                        </div>
                        {contact.other_address.country && <div>{contact.other_address.country}</div>}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Info */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {contact.level && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Level</span>
                    <Badge variant="outline">{contact.level}</Badge>
                  </div>
                )}
                
                {contact.lead_source && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Lead Source</span>
                    <span className="text-sm">{contact.lead_source}</span>
                  </div>
                )}
                
                {contact.languages && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Languages</span>
                    <span className="text-sm">{contact.languages}</span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Created</span>
                  <span className="text-sm">{new Date(contact.created_at).toLocaleDateString()}</span>
                </div>
              </CardContent>
            </Card>

            {/* Hierarchy */}
            {contact.reportsTo && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Reporting Structure
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Reports To</label>
                    <div className="mt-1">
                      <span className="text-sm">{contact.reportsTo.fullName}</span>
                      {contact.reportsTo.title && (
                        <span className="text-sm text-muted-foreground"> • {contact.reportsTo.title}</span>
                      )}
                    </div>
                  </div>
                  
                  {contact.direct_reports_count && parseInt(contact.direct_reports_count) > 0 && (
                    <div className="mt-3">
                      <label className="text-sm font-medium text-muted-foreground">Direct Reports</label>
                      <div className="mt-1">
                        <span className="text-sm">{contact.direct_reports_count} people</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Communication Preferences */}
            <Card>
              <CardHeader>
                <CardTitle>Communication</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Phone Calls</span>
                  <Badge variant={contact.do_not_call ? "destructive" : "default"}>
                    {contact.do_not_call ? "Do Not Call" : "Allowed"}
                  </Badge>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Email</span>
                  <Badge variant={contact.has_opted_out_of_email ? "destructive" : "default"}>
                    {contact.has_opted_out_of_email ? "Opted Out" : "Allowed"}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </motion.div>
    </div>
  );
};