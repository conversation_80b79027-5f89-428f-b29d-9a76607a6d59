import React from 'react';
import { CollaborationPanel } from '@/components/collaboration';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Building2, MapPin, Phone, Mail, Globe, Star } from 'lucide-react';

const CollaborationDemo: React.FC = () => {
  // Mock current user ID - in real app this would come from auth context
  const currentUserId = '1';
  
  // Mock vendor data for demonstration
  const mockVendor = {
    id: 'vendor-123',
    name: 'TechCorp Solutions',
    status: 'active',
    category: 'Software Development',
    location: 'San Francisco, CA',
    phone: '+****************',
    email: '<EMAIL>',
    website: 'https://techcorp.com',
    rating: 4.8,
    description: 'Leading provider of enterprise software solutions with over 10 years of experience in the industry. Specializing in cloud-based applications and digital transformation services.'
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Collaboration Features Demo
          </h1>
          <p className="text-gray-600">
            Experience real-time collaboration with comments, presence indicators, and activity tracking.
          </p>
        </div>

        <div className="flex gap-6">
          {/* Main Content Area */}
          <div className="flex-1 space-y-6">
            {/* Vendor Information Card */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Building2 className="h-8 w-8 text-blue-600" />
                    <div>
                      <CardTitle className="text-2xl">{mockVendor.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="secondary">{mockVendor.category}</Badge>
                        <Badge 
                          variant={mockVendor.status === 'active' ? 'default' : 'secondary'}
                          className={mockVendor.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                        >
                          {mockVendor.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                    <span className="font-semibold">{mockVendor.rating}</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-600">{mockVendor.description}</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{mockVendor.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{mockVendor.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{mockVendor.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-gray-500" />
                    <a 
                      href={mockVendor.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:underline"
                    >
                      {mockVendor.website}
                    </a>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">98%</div>
                    <div className="text-sm text-gray-600">On-time Delivery</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">$2.4M</div>
                    <div className="text-sm text-gray-600">Total Contract Value</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">24</div>
                    <div className="text-sm text-gray-600">Active Projects</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Updates</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Contract renewed for Q2 2024</p>
                      <p className="text-xs text-gray-500">2 hours ago</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Performance review completed</p>
                      <p className="text-xs text-gray-500">1 day ago</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">New project milestone reached</p>
                      <p className="text-xs text-gray-500">3 days ago</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Demo Instructions */}
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-blue-800">🚀 Try the Collaboration Features</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-blue-700">
                  <p className="font-medium mb-2">What you can test:</p>
                  <ul className="space-y-1 ml-4">
                    <li>• <strong>Real-time Comments:</strong> Add comments with rich text formatting</li>
                    <li>• <strong>Threaded Discussions:</strong> Reply to comments and create nested conversations</li>
                    <li>• <strong>Typing Indicators:</strong> See when others are typing</li>
                    <li>• <strong>Presence Awareness:</strong> View who else is currently viewing this vendor</li>
                    <li>• <strong>Activity Timeline:</strong> Track all collaboration activities</li>
                  </ul>
                </div>
                <div className="flex gap-2 pt-2">
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                    Open Collaboration Panel →
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Collaboration Panel */}
          <CollaborationPanel
            entityType="vendor"
            entityId={mockVendor.id}
            currentUserId={currentUserId}
            defaultOpen={true}
          />
        </div>
      </div>
    </div>
  );
};

export default CollaborationDemo;