import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Building2, TrendingUp, Users, DollarSign } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AccountList } from '../components/accounts/AccountList';


// Define a simple type for the stats for clarity
interface AccountStats {
  total: number;
  active: number;
}

export const AccountsList: React.FC = () => {
  // 1. Add state to hold the total and active counts
  const [stats, setStats] = useState<AccountStats | null>(null);
  
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="border-b bg-card"
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-3">
                <Building2 className="h-8 w-8 text-primary" />
                Account Management
              </h1>
              <p className="text-muted-foreground mt-1">
                Manage your client accounts and relationships
              </p>
            </div>
            
            {/* Quick Stats */}
            <div className="hidden lg:flex items-center gap-4">
              <Card className="w-32">
                <CardContent className="p-3">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-primary" />
                    <div>
                      <p className="text-xs text-muted-foreground ml-2.5">Total</p>
                      <p className="text-lg font-semibold ml-3.5">
                        {stats ? stats.total : '-'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="w-32">
                <CardContent className="p-3">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <div>
                      <p className="text-xs text-muted-foreground ml-2.5">Active</p>
                     <p className="text-lg font-semibold ml-3.5">

                        {stats ? stats.active : '-'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="container mx-auto px-4 py-8"
      >
        <AccountList onStatsChange={setStats}/>
      </motion.div>
    </div>
  );
};