import React, { useState } from 'react';
import { useLocation, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Mail, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useToast } from '../hooks/use-toast';
import { AuthFormContainer } from '../components/auth/AuthFormContainer';

export const VerificationPending: React.FC = () => {
  const [isResending, setIsResending] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const location = useLocation();
  const { resendVerification, error } = useAuth();
  const { toast } = useToast();

  // Get email from navigation state or fallback
  const email = location.state?.email || 'your email';

  const handleResendVerification = async () => {
    if (typeof email !== 'string' || email === 'your email') {
      toast({
        title: 'Error',
        description: 'Email address not found. Please try registering again.',
        variant: 'destructive',
      });
      return;
    }

    setIsResending(true);
    setResendSuccess(false);

    try {
      await resendVerification(email);
      setResendSuccess(true);
      toast({
        title: 'Verification email sent!',
        description: 'Please check your inbox for the verification link.',
      });
    } catch (err) {
      toast({
        title: 'Failed to resend email',
        description: error || 'Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setIsResending(false);
    }
  };

  return (
    <AuthFormContainer
      title="Check Your Email"
      subtitle="We've sent you a verification link"
    >
      <div className="text-center space-y-6">
        {/* Email Icon */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
          className="inline-flex items-center justify-center w-20 h-20 bg-info/10 rounded-full"
        >
          <Mail className="w-10 h-10 text-info" />
        </motion.div>

        {/* Main Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-3"
        >
          <h2 className="text-xl font-semibold text-foreground">
            Verification Email Sent
          </h2>
          <p className="text-muted-foreground">
            We've sent a verification link to{' '}
            <span className="font-medium text-foreground">{email}</span>
          </p>
          <p className="text-sm text-muted-foreground">
            Click the link in the email to verify your account and complete your registration.
          </p>
        </motion.div>

        {/* Instructions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="p-4 bg-info/5 border border-info/20 rounded-xl text-left"
        >
          <h3 className="text-sm font-medium text-info mb-2">What's next?</h3>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• Check your inbox (and spam folder)</li>
            <li>• Click the verification link in the email</li>
            <li>• Return here to sign in to your account</li>
            <li>• The link expires in 24 hours</li>
          </ul>
        </motion.div>

        {/* Error Alert */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="p-3 bg-destructive/10 border border-destructive/20 rounded-xl flex items-center space-x-2"
          >
            <AlertCircle className="w-4 h-4 text-destructive" />
            <span className="text-sm text-destructive">{error}</span>
          </motion.div>
        )}

        {/* Success Message */}
        {resendSuccess && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="p-3 bg-success/10 border border-success/20 rounded-xl flex items-center space-x-2"
          >
            <CheckCircle className="w-4 h-4 text-success" />
            <span className="text-sm text-success">
              Verification email sent successfully!
            </span>
          </motion.div>
        )}

        {/* Resend Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="space-y-4"
        >
          <p className="text-sm text-muted-foreground">
            Didn't receive the email?
          </p>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleResendVerification}
            disabled={isResending || email === 'your email'}
            className="btn-neumorphic text-primary font-medium px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isResending ? (
              <div className="flex items-center space-x-2">
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span>Sending...</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <RefreshCw className="w-4 h-4" />
                <span>Resend Email</span>
              </div>
            )}
          </motion.button>
        </motion.div>

        {/* Footer Links */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="pt-6 border-t border-border space-y-2"
        >
          <p className="text-sm text-muted-foreground">
            Already verified your email?{' '}
            <Link
              to="/login"
              className="text-primary hover:text-primary-dark transition-colors font-medium"
            >
              Sign in
            </Link>
          </p>
          <p className="text-sm text-muted-foreground">
            Need help?{' '}
            <Link
              to="/support"
              className="text-primary hover:text-primary-dark transition-colors font-medium"
            >
              Contact Support
            </Link>
          </p>
        </motion.div>
      </div>
    </AuthFormContainer>
  );
};