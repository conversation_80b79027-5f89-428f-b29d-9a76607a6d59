<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VendorMS - Vendor Management System</title>
    <meta name="description" content="Modern Vendor Management System with neumorphism design, RBAC, real-time features, and multi-language support" />
    <meta name="author" content="VendorMS Team" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <!-- OpenGraph Meta Tags -->
    <meta property="og:title" content="VendorMS - Vendor Management System" />
    <meta property="og:description" content="Modern Vendor Management System with neumorphism design and advanced features" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="VendorMS - Vendor Management System" />
    <meta name="twitter:description" content="Modern Vendor Management System with neumorphism design and advanced features" />
    <meta name="twitter:image" content="/og-image.png" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3B82F6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="VendorMS" />
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- Favicons -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" href="/favicon.png" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
