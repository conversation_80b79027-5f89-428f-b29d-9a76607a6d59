# Comments Integration Specification - Design Document

## 1. System Architecture Overview

### 1.1 High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (React)       │◄──►│   (Node.js)     │◄──►│   (PostgreSQL)  │
│                 │    │                 │    │                 │
│ - Comment UI    │    │ - REST APIs     │    │ - Comments      │
│ - Real-time     │    │ - Socket.io     │    │ - Attachments   │
│ - Notifications │    │ - File Upload   │    │ - Audit Logs    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   File Storage  │
                       │   (Local/Cloud) │
                       └─────────────────┘
```

### 1.2 Component Architecture
- **Frontend Components**: Reusable comment widgets for different object types
- **Backend Services**: Comment management, real-time communication, file handling
- **Database Layer**: Optimized schema for comments, replies, and attachments
- **Real-time Layer**: Socket.io for live updates and presence indicators

## 2. Database Design

### 2.1 Comments Table Schema
```sql
CREATE TABLE comments (
    id SERIAL PRIMARY KEY,
    object_type VARCHAR(50) NOT NULL, -- 'vendor', 'contract', etc.
    object_id INTEGER NOT NULL,
    parent_id INTEGER REFERENCES comments(id), -- For threaded replies
    user_id INTEGER NOT NULL REFERENCES users(id),
    content TEXT NOT NULL,
    content_type VARCHAR(20) DEFAULT 'text', -- 'text', 'html'
    category VARCHAR(50), -- 'general', 'legal', 'financial', 'technical'
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'resolved', 'deleted'
    is_edited BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    metadata JSONB -- For extensibility
);

CREATE INDEX idx_comments_object ON comments(object_type, object_id);
CREATE INDEX idx_comments_parent ON comments(parent_id);
CREATE INDEX idx_comments_user ON comments(user_id);
CREATE INDEX idx_comments_created ON comments(created_at);
```

### 2.2 Comment Attachments Table
```sql
CREATE TABLE comment_attachments (
    id SERIAL PRIMARY KEY,
    comment_id INTEGER NOT NULL REFERENCES comments(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path TEXT NOT NULL,
    uploaded_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_attachments_comment ON comment_attachments(comment_id);
```

### 2.3 Comment Mentions Table
```sql
CREATE TABLE comment_mentions (
    id SERIAL PRIMARY KEY,
    comment_id INTEGER NOT NULL REFERENCES comments(id) ON DELETE CASCADE,
    mentioned_user_id INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_mentions_comment ON comment_mentions(comment_id);
CREATE INDEX idx_mentions_user ON comment_mentions(mentioned_user_id);
```

## 3. API Design

### 3.1 REST API Endpoints

#### Comments CRUD Operations
```
GET    /api/comments/:objectType/:objectId     # Get comments for object
POST   /api/comments                          # Create new comment
PUT    /api/comments/:id                      # Update comment
DELETE /api/comments/:id                      # Delete comment
POST   /api/comments/:id/resolve              # Mark comment as resolved
POST   /api/comments/:id/attachments          # Upload attachment
```

#### Comment Search and Filtering
```
GET /api/comments/search?q=:query&objectType=:type&category=:cat
GET /api/comments/:objectType/:objectId?category=:cat&status=:status
```

### 3.2 Request/Response Schemas

#### Create Comment Request
```json
{
  "objectType": "vendor",
  "objectId": 123,
  "content": "This vendor has excellent response times.",
  "category": "general",
  "parentId": null,
  "mentions": ["user123", "user456"]
}
```

#### Comment Response
```json
{
  "id": 1,
  "objectType": "vendor",
  "objectId": 123,
  "parentId": null,
  "user": {
    "id": 1,
    "name": "John Doe",
    "avatar": "/avatars/john.jpg"
  },
  "content": "This vendor has excellent response times.",
  "category": "general",
  "status": "active",
  "isEdited": false,
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z",
  "attachments": [],
  "mentions": [],
  "replies": []
}
```

## 4. Frontend Component Design

### 4.1 Component Hierarchy
```
CommentsSection
├── CommentsList
│   ├── CommentItem
│   │   ├── CommentHeader
│   │   ├── CommentContent
│   │   ├── CommentActions
│   │   └── CommentReplies
│   └── CommentPagination
├── CommentForm
│   ├── RichTextEditor
│   ├── AttachmentUpload
│   ├── MentionSelector
│   └── CategorySelector
└── CommentFilters
    ├── CategoryFilter
    ├── StatusFilter
    └── SearchInput
```

### 4.2 Key Components

#### CommentsSection Component
```typescript
interface CommentsSectionProps {
  objectType: 'vendor' | 'contract';
  objectId: number;
  permissions: CommentPermissions;
  categories?: string[];
  showFilters?: boolean;
}

const CommentsSection: React.FC<CommentsSectionProps> = ({
  objectType,
  objectId,
  permissions,
  categories = ['general'],
  showFilters = true
}) => {
  // Component implementation
};
```

#### CommentItem Component
```typescript
interface CommentItemProps {
  comment: Comment;
  onReply: (parentId: number) => void;
  onEdit: (commentId: number) => void;
  onDelete: (commentId: number) => void;
  onResolve: (commentId: number) => void;
  permissions: CommentPermissions;
}
```

### 4.3 State Management

#### Redux Slice for Comments
```typescript
interface CommentsState {
  comments: Record<string, Comment[]>; // Keyed by "objectType:objectId"
  loading: boolean;
  error: string | null;
  filters: CommentFilters;
  pagination: PaginationState;
}

const commentsSlice = createSlice({
  name: 'comments',
  initialState,
  reducers: {
    setComments,
    addComment,
    updateComment,
    deleteComment,
    setFilters,
    setLoading,
    setError
  }
});
```

## 5. Real-time Communication Design

### 5.1 Socket.io Events

#### Client to Server Events
```typescript
// Join object room for real-time updates
socket.emit('join-object', { objectType: 'vendor', objectId: 123 });

// Leave object room
socket.emit('leave-object', { objectType: 'vendor', objectId: 123 });

// Typing indicator
socket.emit('typing-start', { objectType: 'vendor', objectId: 123 });
socket.emit('typing-stop', { objectType: 'vendor', objectId: 123 });
```

#### Server to Client Events
```typescript
// New comment added
socket.on('comment-added', (comment: Comment) => {});

// Comment updated
socket.on('comment-updated', (comment: Comment) => {});

// Comment deleted
socket.on('comment-deleted', (commentId: number) => {});

// User typing
socket.on('user-typing', (user: User) => {});

// User stopped typing
socket.on('user-stopped-typing', (userId: number) => {});

// Active users update
socket.on('active-users', (users: User[]) => {});
```

### 5.2 Room Management
```typescript
// Room naming convention: "objectType:objectId"
const roomName = `${objectType}:${objectId}`;

// Join room
socket.join(roomName);

// Broadcast to room
io.to(roomName).emit('comment-added', comment);
```

## 6. Security Design

### 6.1 Authentication & Authorization
- JWT token validation for all API requests
- Role-based access control for comment operations
- Object-level permissions (users can only comment on objects they have access to)

### 6.2 Input Validation & Sanitization
```typescript
// Comment content validation
const commentSchema = Joi.object({
  content: Joi.string().min(1).max(5000).required(),
  objectType: Joi.string().valid('vendor', 'contract').required(),
  objectId: Joi.number().integer().positive().required(),
  category: Joi.string().valid('general', 'legal', 'financial', 'technical'),
  parentId: Joi.number().integer().positive().allow(null)
});

// HTML sanitization for rich text content
const sanitizedContent = DOMPurify.sanitize(content, {
  ALLOWED_TAGS: ['b', 'i', 'u', 'a', 'p', 'br'],
  ALLOWED_ATTR: ['href']
});
```

### 6.3 File Upload Security
- File type validation (whitelist approach)
- File size limits (max 10MB per file)
- Virus scanning for uploaded files
- Secure file storage with access controls

## 7. Performance Optimization

### 7.1 Database Optimization
- Proper indexing on frequently queried columns
- Pagination for large comment sets
- Soft delete for comment history preservation
- Database connection pooling

### 7.2 Frontend Optimization
- Virtual scrolling for large comment lists
- Lazy loading of comment attachments
- Debounced search and typing indicators
- Optimistic UI updates

### 7.3 Caching Strategy
- Redis caching for frequently accessed comments
- Browser caching for static assets
- CDN for file attachments

## 8. Integration Points

### 8.1 Vendor Profile Integration
```typescript
// Add to VendorView.tsx
<CommentsSection 
  objectType="vendor" 
  objectId={vendor.id}
  permissions={userPermissions}
  categories={['general', 'performance', 'compliance']}
/>
```

### 8.2 Contract Management Integration
```typescript
// Add to ContractView.tsx
<CommentsSection 
  objectType="contract" 
  objectId={contract.id}
  permissions={userPermissions}
  categories={['general', 'legal', 'financial', 'technical']}
  showFilters={true}
/>
```

### 8.3 Notification Integration
- Email notifications for mentions and replies
- In-app notification system
- Push notifications for mobile users

## 9. Testing Strategy

### 9.1 Unit Testing
- Component testing with React Testing Library
- API endpoint testing with Jest
- Database query testing

### 9.2 Integration Testing
- Socket.io real-time functionality
- File upload and download
- Permission enforcement

### 9.3 End-to-End Testing
- Complete comment workflows
- Cross-browser compatibility
- Mobile responsiveness

## 10. Deployment Considerations

### 10.1 Database Migration
- Create migration scripts for new tables
- Add indexes without blocking operations
- Backup strategy before deployment

### 10.2 Feature Flags
- Gradual rollout of comments feature
- A/B testing for UI variations
- Quick rollback capability

### 10.3 Monitoring
- Comment creation/update metrics
- Real-time connection monitoring
- Performance monitoring for comment queries
- Error tracking and alerting

## 11. Future Enhancements

### 11.1 Advanced Features
- Comment templates for common scenarios
- AI-powered comment suggestions
- Comment analytics and insights
- Integration with external communication tools

### 11.2 Mobile App Support
- Native mobile comment interface
- Push notifications
- Offline comment drafting

### 11.3 Collaboration Features
- Comment voting/reactions
- Comment moderation workflows
- Advanced mention system with teams
- Comment export to external formats