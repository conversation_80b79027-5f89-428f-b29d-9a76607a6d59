# VendorMS Backend API

Real PostgreSQL backend for the VendorMS application.

## 🚀 Quick Setup

### 1. Install Dependencies
```bash
cd backend
npm install
```

### 2. Database Setup
Make sure your PostgreSQL database is running and the schema is created:

```bash
# Connect to PostgreSQL
psql -U postgres

# Create database
CREATE DATABASE vendorms;

# Run the schema
\i ../database/schema.sql
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your database credentials
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=vendorms
DATABASE_USER=postgres
DATABASE_PASSWORD=your_password
```

### 4. Start the Server
```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

The API will be available at: `http://localhost:3001`

## 📊 API Endpoints

### Vendors
- `GET /api/vendors` - Get all vendors (with filtering & pagination)
- `GET /api/vendors/:id` - Get vendor by ID
- `POST /api/vendors` - Create new vendor
- `PUT /api/vendors/:id` - Update vendor
- `DELETE /api/vendors/:id` - Soft delete vendor
- `GET /api/vendors/categories` - Get vendor categories
- `GET /api/vendors/:id/audit` - Get vendor audit history

### Query Parameters (GET /api/vendors)
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10)
- `search` - Search in name, email, category
- `category` - Filter by category
- `status` - Filter by status (active, inactive, blacklisted)
- `performance_min` - Minimum performance score
- `performance_max` - Maximum performance score

## 🔧 Frontend Integration

Update your frontend `.env` file:
```bash
VITE_API_BASE_URL=http://localhost:3001/api
```

## 🗄️ Database Schema

The backend uses the existing PostgreSQL schema with these main tables:
- `vendors` - Core vendor data
- `audits` - Change tracking
- `users` - User management (for audit trails)

## 🛡️ Security Features

- Input validation with Joi
- SQL injection prevention
- CORS protection
- Helmet security headers
- Request compression
- Error handling

## 📝 Development Notes

- All vendor operations are logged in the `audits` table
- Soft delete is used (status = 'inactive')
- JSON fields are used for flexible data (address, certifications, custom_fields)
- Transactions ensure data consistency
- Connection pooling for performance