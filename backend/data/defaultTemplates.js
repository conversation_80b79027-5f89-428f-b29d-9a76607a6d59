/**
 * Default Quote Templates
 * These templates provide pre-configured layouts and styling options
 */

const defaultTemplates = [
  {
    name: 'Professional Blue',
    description: 'Clean professional template with blue accents',
    is_default: true,
    template_data: {
      layout: {
        header: {
          showLogo: true,
          showCompanyInfo: true,
          backgroundColor: '#ffffff',
          textColor: '#1e293b'
        },
        body: {
          showItemDetails: true,
          showVendorAttribution: false,
          groupByCategory: true,
          showUnitPrices: true,
          showQuantities: true
        },
        footer: {
          showTerms: true,
          showSignature: true,
          showContactInfo: true
        }
      },
      styling: {
        primaryColor: '#2563eb',
        secondaryColor: '#64748b',
        fontFamily: 'Helvetica',
        fontSize: '12px'
      },
      content: {
        defaultTerms: 'Payment terms: Net 30 days. Prices valid for 30 days from quote date. All work subject to our standard terms and conditions.',
        defaultNotes: 'Thank you for your business. Please contact us with any questions about this quote.'
      }
    }
  },
  {
    name: 'Modern Green',
    description: 'Contemporary template with green color scheme',
    is_default: false,
    template_data: {
      layout: {
        header: {
          showLogo: true,
          showCompanyInfo: true,
          backgroundColor: '#f0fdf4',
          textColor: '#14532d'
        },
        body: {
          showItemDetails: true,
          showVendorAttribution: true,
          groupByCategory: false,
          showUnitPrices: true,
          showQuantities: true
        },
        footer: {
          showTerms: true,
          showSignature: false,
          showContactInfo: true
        }
      },
      styling: {
        primaryColor: '#16a34a',
        secondaryColor: '#6b7280',
        fontFamily: 'Helvetica',
        fontSize: '11px'
      },
      content: {
        defaultTerms: 'Payment due within 30 days of invoice date. Quote valid for 45 days. Delivery terms as specified per item.',
        defaultNotes: 'We appreciate the opportunity to provide this quote. All vendor attributions are included for transparency.'
      }
    }
  },
  {
    name: 'Minimal Gray',
    description: 'Clean minimal template with gray tones',
    is_default: false,
    template_data: {
      layout: {
        header: {
          showLogo: false,
          showCompanyInfo: true,
          backgroundColor: '#ffffff',
          textColor: '#374151'
        },
        body: {
          showItemDetails: true,
          showVendorAttribution: false,
          groupByCategory: true,
          showUnitPrices: false,
          showQuantities: false
        },
        footer: {
          showTerms: false,
          showSignature: true,
          showContactInfo: false
        }
      },
      styling: {
        primaryColor: '#6b7280',
        secondaryColor: '#9ca3af',
        fontFamily: 'Helvetica',
        fontSize: '10px'
      },
      content: {
        defaultTerms: 'Standard payment and delivery terms apply.',
        defaultNotes: 'Simplified quote format for quick review.'
      }
    }
  },
  {
    name: 'Corporate Red',
    description: 'Bold corporate template with red accents',
    is_default: false,
    template_data: {
      layout: {
        header: {
          showLogo: true,
          showCompanyInfo: true,
          backgroundColor: '#fef2f2',
          textColor: '#7f1d1d'
        },
        body: {
          showItemDetails: true,
          showVendorAttribution: true,
          groupByCategory: true,
          showUnitPrices: true,
          showQuantities: true
        },
        footer: {
          showTerms: true,
          showSignature: true,
          showContactInfo: true
        }
      },
      styling: {
        primaryColor: '#dc2626',
        secondaryColor: '#6b7280',
        fontFamily: 'Helvetica',
        fontSize: '12px'
      },
      content: {
        defaultTerms: 'Payment terms: Net 15 days. Expedited delivery available upon request. All prices in USD.',
        defaultNotes: 'This quote includes detailed vendor information for full transparency and accountability.'
      }
    }
  },
  {
    name: 'Elegant Purple',
    description: 'Sophisticated template with purple styling',
    is_default: false,
    template_data: {
      layout: {
        header: {
          showLogo: true,
          showCompanyInfo: true,
          backgroundColor: '#faf5ff',
          textColor: '#581c87'
        },
        body: {
          showItemDetails: true,
          showVendorAttribution: false,
          groupByCategory: false,
          showUnitPrices: true,
          showQuantities: true
        },
        footer: {
          showTerms: true,
          showSignature: true,
          showContactInfo: true
        }
      },
      styling: {
        primaryColor: '#7c3aed',
        secondaryColor: '#64748b',
        fontFamily: 'Helvetica',
        fontSize: '11px'
      },
      content: {
        defaultTerms: 'Payment terms negotiable based on project scope. Quote valid for 60 days from issue date.',
        defaultNotes: 'We look forward to working with you on this project. Please reach out with any questions or modifications needed.'
      }
    }
  }
];

module.exports = defaultTemplates;