const { Pool } = require('pg');
require('dotenv').config();

// Database configuration with fallbacks
const dbConfig = {
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT) || 5432,
  database: process.env.DATABASE_NAME || 'vendorms',
  user: process.env.DATABASE_USER || process.env.USER || 'postgres', // Try system user first
  password: process.env.DATABASE_PASSWORD || '', // Empty password for local development
  ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 5000, // Increased timeout
};

console.log('🔗 Attempting database connection with:');
console.log(`   Host: ${dbConfig.host}:${dbConfig.port}`);
console.log(`   Database: ${dbConfig.database}`);
console.log(`   User: ${dbConfig.user}`);
console.log(`   Password: ${dbConfig.password ? '[SET]' : '[EMPTY]'}`);

// Create connection pool
const pool = new Pool(dbConfig);

// Test database connection
pool.on('connect', () => {
  console.log('✅ Connected to PostgreSQL database');
});

pool.on('error', (err) => {
  console.error('❌ Unexpected error on idle client', err);
  process.exit(-1);
});

// Helper function to execute queries
const query = async (text, params) => {
  const start = Date.now();
  try {
    const res = await pool.query(text, params);
    const duration = Date.now() - start;
    console.log('📊 Executed query', { text, duration, rows: res.rowCount });
    return res;
  } catch (error) {
    console.error('❌ Database query error:', error);
    throw error;
  }
};

// Helper function to execute queries with user context
const queryWithUser = async (text, params, userId = 1) => {
  const client = await getClient();
  try {
    // Set user context for audit logging
    await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);
    
    const start = Date.now();
    const res = await client.query(text, params);
    const duration = Date.now() - start;
    console.log('📊 Executed query with user context', { text, duration, rows: res.rowCount, userId });
    
    return res;
  } catch (error) {
    console.error('❌ Database query error:', error);
    throw error;
  } finally {
    client.release();
  }
};

// Helper function to get a client from the pool
const getClient = async () => {
  return await pool.connect();
};

// Helper function to begin transaction
const beginTransaction = async () => {
  const client = await getClient();
  await client.query('BEGIN');
  return client;
};

// Helper function to commit transaction
const commitTransaction = async (client) => {
  await client.query('COMMIT');
  client.release();
};

// Helper function to rollback transaction
const rollbackTransaction = async (client) => {
  await client.query('ROLLBACK');
  client.release();
};

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🔄 Closing database pool...');
  await pool.end();
  console.log('✅ Database pool closed');
  process.exit(0);
});

module.exports = {
  pool,
  query,
  queryWithUser,
  getClient,
  beginTransaction,
  commitTransaction,
  rollbackTransaction,
};