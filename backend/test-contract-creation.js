const { query } = require('./config/database');

async function testContractCreation() {
  console.log('🧪 Testing Contract Creation...\n');

  try {
    // Test 1: Check if contracts table exists
    console.log('1. Checking contracts table structure...');
    const tableInfo = await query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'contracts' 
      ORDER BY ordinal_position
    `);
    
    console.log('✅ Contracts table columns:');
    tableInfo.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    console.log('');

    // Test 2: Check if vendors exist
    console.log('2. Checking available vendors...');
    const vendors = await query('SELECT id, name FROM vendors LIMIT 5');
    console.log(`✅ Found ${vendors.rows.length} vendors:`);
    vendors.rows.forEach(vendor => {
      console.log(`   - ID: ${vendor.id}, Name: ${vendor.name}`);
    });
    console.log('');

    if (vendors.rows.length === 0) {
      console.log('❌ No vendors found! Please add vendors first.');
      return;
    }

    // Test 3: Try creating a test contract
    console.log('3. Creating test contract...');
    const testContractData = {
      vendor_id: vendors.rows[0].id,
      title: 'Test Contract - ' + new Date().toISOString(),
      status: 'draft',
      parties: {
        vendor: vendors.rows[0].name,
        client: 'VMS Corp Test'
      },
      clauses: {
        payment_terms: '30 days net',
        deliverables: 'Test deliverables',
        additional_clauses: 'Test additional clauses'
      },
      milestones: [
        {
          name: 'Test Milestone 1',
          description: 'Test milestone description',
          due_date: '2025-08-01',
          completed: false
        }
      ],
      start_date: '2025-01-01',
      end_date: '2025-12-31'
    };

    const contractQuery = `
      INSERT INTO contracts (
        vendor_id, title, status, parties, clauses, milestones, start_date, end_date
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;

    const contractParams = [
      testContractData.vendor_id,
      testContractData.title,
      testContractData.status,
      JSON.stringify(testContractData.parties),
      JSON.stringify(testContractData.clauses),
      JSON.stringify(testContractData.milestones),
      testContractData.start_date,
      testContractData.end_date
    ];

    const result = await query(contractQuery, contractParams);
    const createdContract = result.rows[0];

    console.log('✅ Contract created successfully!');
    console.log(`   - ID: ${createdContract.id}`);
    console.log(`   - Title: ${createdContract.title}`);
    console.log(`   - Status: ${createdContract.status}`);
    console.log(`   - Vendor ID: ${createdContract.vendor_id}`);
    console.log(`   - Milestones: ${JSON.stringify(createdContract.milestones, null, 2)}`);
    console.log('');

    // Test 4: Verify the contract was stored correctly
    console.log('4. Verifying stored contract...');
    const storedContract = await query('SELECT * FROM contracts WHERE id = $1', [createdContract.id]);
    
    if (storedContract.rows.length > 0) {
      console.log('✅ Contract verification successful!');
      console.log(`   - Parties: ${JSON.stringify(storedContract.rows[0].parties, null, 2)}`);
      console.log(`   - Clauses: ${JSON.stringify(storedContract.rows[0].clauses, null, 2)}`);
    } else {
      console.log('❌ Contract verification failed!');
    }

    console.log('\n🎉 All tests passed! Contract creation should work.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testContractCreation().then(() => {
  console.log('\n✅ Test completed.');
  process.exit(0);
}).catch(error => {
  console.error('❌ Test script failed:', error);
  process.exit(1);
});