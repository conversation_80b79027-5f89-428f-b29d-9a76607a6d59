#!/usr/bin/env node

/**
 * Test script for enhanced RFQ API endpoints
 * This script tests the new functionality added to the RFQ API
 */

const axios = require("axios");

const BASE_URL = "http://localhost:3001/api/rfqs";

// Mock authentication token (in real scenario, this would be obtained from login)
const AUTH_TOKEN = "mock-jwt-token";

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    Authorization: `Bearer ${AUTH_TOKEN}`,
    "Content-Type": "application/json",
  },
});

async function testEnhancedRFQAPI() {
  console.log("🚀 Testing Enhanced RFQ API Endpoints\n");

  try {
    // Test 1: Enhanced filtering and pagination
    console.log("1. Testing enhanced filtering and pagination...");
    const listResponse = await api.get("/", {
      params: {
        page: 1,
        limit: 5,
        search: "test",
        status: "draft",
        sort_by: "created_at",
        sort_order: "desc",
        include_analytics: true,
      },
    });
    console.log("✅ Enhanced filtering works");
    console.log(`   Found ${listResponse.data.data.length} RFQs`);
    console.log(
      `   Pagination: ${JSON.stringify(listResponse.data.pagination)}\n`
    );

    // Test 2: Cursor-based pagination
    console.log("2. Testing cursor-based pagination...");
    const cursorResponse = await api.get("/", {
      params: {
        cursor: Buffer.from(new Date().toISOString()).toString("base64"),
        limit: 3,
      },
    });
    console.log("✅ Cursor-based pagination works");
    console.log(`   Meta: ${JSON.stringify(cursorResponse.data.meta)}\n`);

    // Test 3: Advanced search
    console.log("3. Testing advanced search...");
    const searchResponse = await api.post("/search", {
      query: "office supplies",
      filters: {
        status: ["draft", "sent"],
        date_range: {
          start: "2024-01-01",
          end: "2024-12-31",
        },
      },
      page: 1,
      limit: 10,
      sort_by: "relevance",
    });
    console.log("✅ Advanced search works");
    console.log(`   Search results: ${searchResponse.data.data.length} RFQs`);
    console.log(
      `   Search time: ${searchResponse.data.search_meta.search_time_ms}ms\n`
    );

    // Test 4: Summary statistics
    console.log("4. Testing summary statistics...");
    const statsResponse = await api.get("/summary");
    console.log("✅ Summary statistics work");
    console.log(`   Stats: ${JSON.stringify(statsResponse.data.data)}\n`);

    // Test 5: Bulk operations (mock data)
    console.log("5. Testing bulk operations...");

    // Note: These would fail without actual RFQ IDs, but we can test the validation
    try {
      await api.put("/bulk", {
        rfq_ids: [1, 2, 3],
        updates: {
          status: "sent",
        },
      });
      console.log("✅ Bulk update endpoint accessible");
    } catch (error) {
      if (error.response && error.response.status === 403) {
        console.log(
          "✅ Bulk update validation works (access denied as expected)"
        );
      } else {
        console.log("⚠️  Bulk update error:", error.message);
      }
    }

    try {
      await api.delete("/bulk", {
        data: {
          rfq_ids: [1, 2, 3],
          force: false,
        },
      });
      console.log("✅ Bulk delete endpoint accessible");
    } catch (error) {
      if (error.response && error.response.status === 403) {
        console.log(
          "✅ Bulk delete validation works (access denied as expected)"
        );
      } else {
        console.log("⚠️  Bulk delete error:", error.message);
      }
    }

    // Test 6: Export functionality
    console.log("\n6. Testing export functionality...");
    try {
      const exportResponse = await api.get("/export", {
        params: {
          format: "json",
          include_analytics: true,
        },
      });
      console.log("✅ Export endpoint accessible");
      console.log(`   Export format: JSON`);
    } catch (error) {
      console.log("⚠️  Export error:", error.message);
    }

    console.log("\n🎉 All enhanced RFQ API tests completed!");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    if (error.response) {
      console.error("   Response status:", error.response.status);
      console.error(
        "   Response data:",
        JSON.stringify(error.response.data, null, 2)
      );
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  testEnhancedRFQAPI().catch(console.error);
}

module.exports = { testEnhancedRFQAPI };
