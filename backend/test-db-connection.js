#!/usr/bin/env node

/**
 * Simple database connection test
 */

const { query } = require('./config/database');

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...\n');

  try {
    // Test basic connection
    const result = await query('SELECT NOW() as current_time, version() as db_version');
    console.log('✅ Database connection successful!');
    console.log(`   Current time: ${result.rows[0].current_time}`);
    console.log(`   Database version: ${result.rows[0].db_version.split(' ')[0]}`);

    // Test required tables exist
    const tables = ['users', 'vendors', 'rfqs', 'rfq_invitations', 'rfq_submissions'];
    console.log('\n🔍 Checking required tables...');
    
    for (const table of tables) {
      try {
        const tableCheck = await query(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`   ✅ Table '${table}' exists (${tableCheck.rows[0].count} records)`);
      } catch (error) {
        console.log(`   ❌ Table '${table}' missing or inaccessible`);
        throw new Error(`Required table '${table}' not found`);
      }
    }

    console.log('\n🎉 Database is ready for testing!');
    return true;

  } catch (error) {
    console.error('\n❌ Database connection failed:', error.message);
    console.error('\nTroubleshooting steps:');
    console.error('1. Ensure PostgreSQL is running');
    console.error('2. Check database credentials in .env file');
    console.error('3. Verify database "vendorms" exists');
    console.error('4. Run database migrations to create tables');
    return false;
  }
}

if (require.main === module) {
  testDatabaseConnection().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testDatabaseConnection };