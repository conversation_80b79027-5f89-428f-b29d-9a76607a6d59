const { query } = require('./config/database');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  try {
    console.log('🔄 Running accounts table migration...');
    
    const migrationPath = path.join(__dirname, '../database/migrations/004_create_accounts_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    await query(migrationSQL);
    console.log('✅ Accounts table migration completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
  process.exit(0);
}

runMigration();