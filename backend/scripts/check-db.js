const { exec } = require('child_process');

console.log('🔍 Checking PostgreSQL setup...\n');

// Check if PostgreSQL is running
exec('pg_isready', (error, stdout, stderr) => {
  if (error) {
    console.log('❌ PostgreSQL is not running or not installed');
    console.log('To start PostgreSQL on macOS:');
    console.log('  brew services start postgresql');
    console.log('  or');
    console.log('  pg_ctl -D /usr/local/var/postgres start\n');
    return;
  }
  
  console.log('✅ PostgreSQL is running\n');
  
  // Check current user
  exec('whoami', (error, stdout, stderr) => {
    if (error) return;
    
    const currentUser = stdout.trim();
    console.log(`👤 Current system user: ${currentUser}`);
    console.log(`💡 Try using this as your DATABASE_USER in .env\n`);
    
    // Try to connect with current user
    exec(`psql -U ${currentUser} -d postgres -c "\\du"`, (error, stdout, stderr) => {
      if (!error) {
        console.log('📋 Available PostgreSQL users:');
        console.log(stdout);
      } else {
        console.log('❌ Could not list PostgreSQL users');
        console.log('Try creating a database user:');
        console.log(`  createuser -s ${currentUser}`);
        console.log('  or');
        console.log(`  psql -d postgres -c "CREATE USER ${currentUser} WITH SUPERUSER;"`);
      }
    });
  });
});