-- Insert sample contract templates
INSERT INTO contract_templates (name, description, category, template_content, default_fields, required_fields, active) VALUES
(
  'Software Development Agreement',
  'Standard template for software development projects',
  'Technology',
  'This Software Development Agreement is entered into between {{client_name}} and {{vendor_name}} for the development of {{project_description}}.',
  '{"payment_terms": "30 days net", "deliverables": "Software application with source code, documentation, and testing", "additional_clauses": "All intellectual property rights transfer to client upon final payment. 90-day warranty period included."}',
  '["title", "vendor_id", "start_date", "end_date", "payment_terms", "deliverables"]',
  true
),
(
  'Consulting Services Agreement',
  'Professional consulting and advisory services',
  'Services',
  'This Consulting Services Agreement is entered into between {{client_name}} and {{vendor_name}} for professional consulting services.',
  '{"payment_terms": "15 days net", "deliverables": "Consulting services, reports, and recommendations", "additional_clauses": "Confidentiality agreement applies to all shared information. Reports delivered in digital format."}',
  '["title", "vendor_id", "start_date", "end_date", "payment_terms", "deliverables"]',
  true
),
(
  'Manufacturing Agreement',
  'Product manufacturing and supply contracts',
  'Manufacturing',
  'This Manufacturing Agreement is entered into between {{client_name}} and {{vendor_name}} for the manufacturing of {{product_description}}.',
  '{"payment_terms": "45 days net", "deliverables": "Manufactured products according to specifications", "additional_clauses": "Quality control standards must be met. Delivery schedule as per attached timeline."}',
  '["title", "vendor_id", "start_date", "end_date", "payment_terms", "deliverables"]',
  true
),
(
  'Maintenance & Support Agreement',
  'Ongoing maintenance and support services',
  'Services',
  'This Maintenance & Support Agreement is entered into between {{client_name}} and {{vendor_name}} for ongoing maintenance and support services.',
  '{"payment_terms": "Monthly in advance", "deliverables": "Maintenance services, technical support, and system updates", "additional_clauses": "Service level agreement attached. Emergency support available 24/7."}',
  '["title", "vendor_id", "start_date", "end_date", "payment_terms", "deliverables"]',
  true
);

-- Insert sample contracts for testing
INSERT INTO contracts (vendor_id, title, status, parties, clauses, start_date, end_date, value, currency, created_by, updated_by) VALUES
(
  1,
  'Software Development Agreement - VMS Enhancement',
  'active',
  '{"vendor": "TechCorp Solutions", "client": "VMS Corp", "additional": []}',
  '{"payment_terms": "30 days net", "deliverables": "Web application with responsive design, user authentication, and dashboard", "additional_clauses": "All source code to be delivered upon completion. 90-day warranty period included."}',
  '2024-02-01',
  '2024-05-01',
  50000.00,
  'USD',
  1,
  1
),
(
  2,
  'Marketing Services Agreement - Q2 Campaign',
  'draft',
  '{"vendor": "Creative Marketing Co", "client": "VMS Corp", "additional": []}',
  '{"payment_terms": "15 days net", "deliverables": "Marketing campaign design, social media management, and performance analytics", "additional_clauses": "Monthly performance reports required. Campaign materials subject to approval."}',
  '2024-03-01',
  '2024-08-31',
  25000.00,
  'USD',
  1,
  1
);

-- Insert sample milestones
INSERT INTO contract_milestones (contract_id, name, description, due_date, completed, completed_date) VALUES
(1, 'Phase 1: Design & Planning', 'Complete system design and project planning', '2024-03-01', true, '2024-02-28'),
(1, 'Phase 2: Development', 'Core development and feature implementation', '2024-04-01', false, NULL),
(1, 'Phase 3: Testing & Deployment', 'Quality assurance testing and production deployment', '2024-05-01', false, NULL),
(2, 'Campaign Strategy', 'Develop comprehensive marketing strategy', '2024-03-15', false, NULL),
(2, 'Creative Development', 'Create marketing materials and content', '2024-04-01', false, NULL);