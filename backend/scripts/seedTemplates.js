/**
 * Seed Default Quote Templates
 * This script populates the database with default quote templates
 */

const QuoteTemplate = require('../models/QuoteTemplate');
const defaultTemplates = require('../data/defaultTemplates');
const db = require('../config/database');

async function seedTemplates() {
  try {
    console.log('Starting template seeding...');
    
    // Check if templates already exist
    const existingTemplates = await QuoteTemplate.findAll();
    
    if (existingTemplates.length > 0) {
      console.log(`Found ${existingTemplates.length} existing templates. Skipping seed.`);
      return;
    }
    
    console.log('No existing templates found. Creating default templates...');
    
    // Create each default template
    for (const templateData of defaultTemplates) {
      try {
        const template = await QuoteTemplate.create(templateData);
        console.log(`✓ Created template: ${template.name}`);
      } catch (error) {
        console.error(`✗ Failed to create template: ${templateData.name}`, error.message);
      }
    }
    
    console.log('Template seeding completed!');
    
  } catch (error) {
    console.error('Error seeding templates:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  seedTemplates()
    .then(() => {
      console.log('Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = seedTemplates;