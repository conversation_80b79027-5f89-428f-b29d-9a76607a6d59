-- Clean up duplicate audit entries
-- This script removes duplicate audit entries that might have been created
-- due to both manual logging and database triggers

-- Create a temporary table with unique audit entries
CREATE TEMP TABLE unique_audits AS
SELECT DISTINCT ON (entity_type, entity_id, action, user_id, timestamp)
    id, entity_type, entity_id, action, user_id, old_value, new_value, details, timestamp
FROM audits
ORDER BY entity_type, entity_id, action, user_id, timestamp, id;

-- Show how many duplicates will be removed
SELECT 
    'Total audit entries' as description,
    COUNT(*) as count
FROM audits
UNION ALL
SELECT 
    'Unique audit entries' as description,
    COUNT(*) as count
FROM unique_audits
UNION ALL
SELECT 
    'Duplicates to be removed' as description,
    (SELECT COUNT(*) FROM audits) - (SELECT COUNT(*) FROM unique_audits) as count;

-- Uncomment the following lines to actually remove duplicates
-- (Run this only after reviewing the counts above)

-- DELETE FROM audits;
-- INSERT INTO audits SELECT * FROM unique_audits ORDER BY id;

-- Reset the sequence
-- SELECT setval('audits_id_seq', (SELECT MAX(id) FROM audits));

-- Drop the temporary table
DROP TABLE unique_audits;