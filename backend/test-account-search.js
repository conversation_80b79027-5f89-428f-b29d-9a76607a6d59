const Account = require('./models/Account');

async function testAccountSearch() {
  console.log('🧪 Testing Account Search and Filtering...\n');

  let createdAccounts = [];

  try {
    // Create test data with diverse attributes
    console.log('1. Creating diverse test data...');
    
    const testAccounts = [
      {
        name: 'Tech Solutions Inc',
        type: 'Customer - Direct',
        industry: 'Technology',
        annual_revenue: 5000000,
        number_of_employees: 100,
        ownership: 'Private',
        phone: '******-0001',
        website: 'https://techsolutions.com',
        rating: 'Hot',
        description: 'Leading technology solutions provider',
        billing_address: {
          street: '123 Tech Street',
          city: 'San Francisco',
          state: 'CA',
          postalCode: '94105',
          country: 'USA'
        }
      },
      {
        name: 'Global Manufacturing Corp',
        type: 'Prospect',
        industry: 'Manufacturing',
        annual_revenue: ********,
        number_of_employees: 1000,
        ownership: 'Public',
        phone: '******-0002',
        website: 'https://globalmfg.com',
        rating: 'Warm',
        description: 'International manufacturing company',
        billing_address: {
          street: '456 Industrial Ave',
          city: 'Detroit',
          state: 'MI',
          postalCode: '48201',
          country: 'USA'
        }
      },
      {
        name: 'Healthcare Partners LLC',
        type: 'Customer - Channel',
        industry: 'Healthcare',
        annual_revenue: ********,
        number_of_employees: 250,
        ownership: 'Private',
        phone: '******-0003',
        website: 'https://healthpartners.com',
        rating: 'Cold',
        description: 'Healthcare services and consulting',
        billing_address: {
          street: '789 Medical Center Dr',
          city: 'Boston',
          state: 'MA',
          postalCode: '02101',
          country: 'USA'
        }
      },
      {
        name: 'European Tech Ltd',
        type: 'Customer - Direct',
        industry: 'Technology',
        annual_revenue: 8000000,
        number_of_employees: 150,
        ownership: 'Private',
        phone: '+44-20-7946-0958',
        website: 'https://eurotech.co.uk',
        rating: 'Hot',
        description: 'European technology innovation leader',
        billing_address: {
          street: '10 Downing Street',
          city: 'London',
          state: 'England',
          postalCode: 'SW1A 2AA',
          country: 'UK'
        }
      },
      {
        name: 'Small Startup Co',
        type: 'Prospect',
        industry: 'Technology',
        annual_revenue: 500000,
        number_of_employees: 10,
        ownership: 'Private',
        phone: '******-0004',
        website: 'https://smallstartup.com',
        rating: 'Warm',
        description: 'Innovative startup company',
        billing_address: {
          street: '321 Startup Blvd',
          city: 'Austin',
          state: 'TX',
          postalCode: '73301',
          country: 'USA'
        }
      }
    ];

    for (const accountData of testAccounts) {
      const account = await Account.create(accountData, 1);
      createdAccounts.push(account.id);
      console.log(`✅ Created: ${account.name}`);
    }

    // Test 2: Basic text search
    console.log('\n2. Testing basic text search...');
    
    // Search by name
    const nameSearch = await Account.findAll({ search: 'Tech' }, 1, 10);
    console.log(`✅ Name search for "Tech": ${nameSearch.accounts.length} results`);
    console.log('   Found:', nameSearch.accounts.map(a => a.name).join(', '));

    // Search by description
    const descSearch = await Account.findAll({ search: 'healthcare' }, 1, 10);
    console.log(`✅ Description search for "healthcare": ${descSearch.accounts.length} results`);

    // Test 3: Industry filtering
    console.log('\n3. Testing industry filtering...');
    
    const techIndustry = await Account.findAll({ industry: 'Technology' }, 1, 10);
    console.log(`✅ Technology industry: ${techIndustry.accounts.length} results`);
    
    const healthcareIndustry = await Account.findAll({ industry: 'Healthcare' }, 1, 10);
    console.log(`✅ Healthcare industry: ${healthcareIndustry.accounts.length} results`);

    // Test 4: Type filtering
    console.log('\n4. Testing type filtering...');
    
    const customerDirect = await Account.findAll({ type: 'Customer - Direct' }, 1, 10);
    console.log(`✅ Customer - Direct type: ${customerDirect.accounts.length} results`);
    
    const prospects = await Account.findAll({ type: 'Prospect' }, 1, 10);
    console.log(`✅ Prospect type: ${prospects.accounts.length} results`);

    // Test 5: Rating filtering
    console.log('\n5. Testing rating filtering...');
    
    const hotRating = await Account.findAll({ rating: 'Hot' }, 1, 10);
    console.log(`✅ Hot rating: ${hotRating.accounts.length} results`);
    
    const warmRating = await Account.findAll({ rating: 'Warm' }, 1, 10);
    console.log(`✅ Warm rating: ${warmRating.accounts.length} results`);

    // Test 6: Ownership filtering
    console.log('\n6. Testing ownership filtering...');
    
    const privateOwnership = await Account.findAll({ ownership: 'Private' }, 1, 10);
    console.log(`✅ Private ownership: ${privateOwnership.accounts.length} results`);
    
    const publicOwnership = await Account.findAll({ ownership: 'Public' }, 1, 10);
    console.log(`✅ Public ownership: ${publicOwnership.accounts.length} results`);

    // Test 7: Revenue range filtering
    console.log('\n7. Testing revenue range filtering...');
    
    const highRevenue = await Account.findAll({ 
      annual_revenue_min: ******** 
    }, 1, 10);
    console.log(`✅ Revenue >= $10M: ${highRevenue.accounts.length} results`);
    
    const midRevenue = await Account.findAll({ 
      annual_revenue_min: 1000000,
      annual_revenue_max: ********
    }, 1, 10);
    console.log(`✅ Revenue $1M-$10M: ${midRevenue.accounts.length} results`);

    // Test 8: Employee count filtering
    console.log('\n8. Testing employee count filtering...');
    
    const largeCompanies = await Account.findAll({ 
      number_of_employees_min: 500 
    }, 1, 10);
    console.log(`✅ Employees >= 500: ${largeCompanies.accounts.length} results`);
    
    const smallCompanies = await Account.findAll({ 
      number_of_employees_max: 50 
    }, 1, 10);
    console.log(`✅ Employees <= 50: ${smallCompanies.accounts.length} results`);

    // Test 9: Geographic filtering
    console.log('\n9. Testing geographic filtering...');
    
    const usaAccounts = await Account.findAll({ country: 'USA' }, 1, 10);
    console.log(`✅ USA country: ${usaAccounts.accounts.length} results`);
    
    const californiaAccounts = await Account.findAll({ state: 'CA' }, 1, 10);
    console.log(`✅ California state: ${californiaAccounts.accounts.length} results`);
    
    const sanFranciscoAccounts = await Account.findAll({ city: 'San Francisco' }, 1, 10);
    console.log(`✅ San Francisco city: ${sanFranciscoAccounts.accounts.length} results`);

    // Test 10: Combined filtering
    console.log('\n10. Testing combined filtering...');
    
    const combinedSearch = await Account.findAll({
      industry: 'Technology',
      rating: 'Hot',
      ownership: 'Private',
      annual_revenue_min: 1000000
    }, 1, 10);
    console.log(`✅ Combined filters: ${combinedSearch.accounts.length} results`);
    console.log('   Results:', combinedSearch.accounts.map(a => a.name).join(', '));

    // Test 11: Advanced search method
    console.log('\n11. Testing advanced search method...');
    
    const advancedSearch = await Account.search({
      query: 'technology',
      industry: 'Technology',
      annual_revenue_min: 1000000,
      annual_revenue_max: ********,
      country: 'USA',
      page: 1,
      limit: 5
    });
    console.log(`✅ Advanced search: ${advancedSearch.accounts.length} results`);
    console.log('   Pagination:', advancedSearch.pagination);

    // Test 12: Pagination
    console.log('\n12. Testing pagination...');
    
    const page1 = await Account.findAll({}, 1, 2);
    console.log(`✅ Page 1 (limit 2): ${page1.accounts.length} results`);
    console.log('   Total pages:', page1.pagination.totalPages);
    
    const page2 = await Account.findAll({}, 2, 2);
    console.log(`✅ Page 2 (limit 2): ${page2.accounts.length} results`);

    // Test 13: Sorting and ordering
    console.log('\n13. Testing result ordering...');
    
    const allAccounts = await Account.findAll({}, 1, 10);
    console.log('✅ Default ordering (created_at DESC):');
    allAccounts.accounts.forEach((account, index) => {
      console.log(`   ${index + 1}. ${account.name} (${account.created_at})`);
    });

    // Test 14: Export functionality
    console.log('\n14. Testing export functionality...');
    
    const exportData = await Account.exportToCSV({ industry: 'Technology' });
    console.log(`✅ Export CSV: ${exportData.headers.length} headers, ${exportData.rows.length} rows`);
    console.log('   Headers:', exportData.headers.slice(0, 5).join(', '), '...');

    // Test 15: Performance with large result sets
    console.log('\n15. Testing performance...');
    
    const startTime = Date.now();
    const performanceTest = await Account.findAll({}, 1, 100);
    const endTime = Date.now();
    console.log(`✅ Performance test: ${performanceTest.accounts.length} results in ${endTime - startTime}ms`);

    // Test 16: Edge cases
    console.log('\n16. Testing edge cases...');
    
    // Empty search
    const emptySearch = await Account.findAll({ search: '' }, 1, 10);
    console.log(`✅ Empty search: ${emptySearch.accounts.length} results`);
    
    // Non-existent values
    const nonExistent = await Account.findAll({ industry: 'NonExistentIndustry' }, 1, 10);
    console.log(`✅ Non-existent industry: ${nonExistent.accounts.length} results`);
    
    // Invalid ranges
    const invalidRange = await Account.findAll({ 
      annual_revenue_min: ********,
      annual_revenue_max: 1000000  // max < min
    }, 1, 10);
    console.log(`✅ Invalid range: ${invalidRange.accounts.length} results`);

    // Test 17: URL parameter compatibility
    console.log('\n17. Testing URL parameter compatibility...');
    
    const urlParams = {
      search: 'tech',
      industry: 'Technology',
      type: 'Customer - Direct',
      rating: 'Hot',
      annual_revenue_min: '1000000',  // String numbers (from URL)
      annual_revenue_max: '********',
      page: '1',
      limit: '5'
    };
    
    // Convert string numbers to actual numbers (as validation would do)
    const processedParams = {
      ...urlParams,
      annual_revenue_min: parseInt(urlParams.annual_revenue_min),
      annual_revenue_max: parseInt(urlParams.annual_revenue_max),
      page: parseInt(urlParams.page),
      limit: parseInt(urlParams.limit)
    };
    
    const urlSearch = await Account.findAll(processedParams, processedParams.page, processedParams.limit);
    console.log(`✅ URL parameter search: ${urlSearch.accounts.length} results`);

    console.log('\n🎉 All Account Search and Filtering tests passed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    for (const accountId of createdAccounts) {
      try {
        await Account.delete(accountId, 1);
      } catch (error) {
        console.log(`Note: Could not delete account ${accountId}`);
      }
    }
    console.log('✅ Cleanup completed');
  }
}

// Run the test
testAccountSearch().then(() => {
  console.log('\n✅ Search test completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Search test suite failed:', error);
  process.exit(1);
});