const Account = require('./models/Account');
const AccountHierarchyService = require('./services/accountHierarchyService');

async function testAccountHierarchy() {
  console.log('🧪 Testing Account Hierarchy Management...\n');

  let createdAccounts = [];

  try {
    // Test 1: Create a hierarchy structure
    console.log('1. Creating test hierarchy structure...');
    
    // Create root account
    const rootAccount = await Account.create({
      name: 'Global Corporation',
      type: 'Customer - Direct',
      industry: 'Technology',
      annual_revenue: ********
    }, 1);
    createdAccounts.push(rootAccount.id);
    console.log('✅ Root account created:', rootAccount.name);

    // Create level 1 children
    const usaAccount = await Account.create({
      name: 'USA Division',
      parent_account_id: rootAccount.id,
      type: 'Customer - Direct',
      industry: 'Technology'
    }, 1);
    createdAccounts.push(usaAccount.id);

    const europeAccount = await Account.create({
      name: 'Europe Division',
      parent_account_id: rootAccount.id,
      type: 'Customer - Direct',
      industry: 'Technology'
    }, 1);
    createdAccounts.push(europeAccount.id);

    console.log('✅ Level 1 children created: USA Division, Europe Division');

    // Create level 2 children
    const californiaAccount = await Account.create({
      name: 'California Office',
      parent_account_id: usaAccount.id,
      type: 'Customer - Direct',
      industry: 'Technology'
    }, 1);
    createdAccounts.push(californiaAccount.id);

    const texasAccount = await Account.create({
      name: 'Texas Office',
      parent_account_id: usaAccount.id,
      type: 'Customer - Direct',
      industry: 'Technology'
    }, 1);
    createdAccounts.push(texasAccount.id);

    const ukAccount = await Account.create({
      name: 'UK Office',
      parent_account_id: europeAccount.id,
      type: 'Customer - Direct',
      industry: 'Technology'
    }, 1);
    createdAccounts.push(ukAccount.id);

    console.log('✅ Level 2 children created: California, Texas, UK offices');

    // Test 2: Get hierarchy tree
    console.log('\n2. Testing hierarchy tree retrieval...');
    const tree = await AccountHierarchyService.getHierarchyTree(rootAccount.id);
    console.log('✅ Hierarchy tree retrieved');
    console.log('   Root:', tree.name);
    console.log('   Children count:', tree.children.length);
    console.log('   Grandchildren count:', tree.children.reduce((sum, child) => sum + child.children.length, 0));

    // Test 3: Get all descendants
    console.log('\n3. Testing descendant retrieval...');
    const descendants = await AccountHierarchyService.getAllDescendantIds(rootAccount.id);
    console.log('✅ Descendants retrieved:', descendants.length, 'accounts');

    // Test 4: Get all ancestors
    console.log('\n4. Testing ancestor retrieval...');
    const ancestors = await AccountHierarchyService.getAllAncestorIds(californiaAccount.id);
    console.log('✅ Ancestors retrieved:', ancestors.length, 'accounts');

    // Test 5: Get hierarchy path (breadcrumb)
    console.log('\n5. Testing hierarchy path...');
    const path = await AccountHierarchyService.getHierarchyPath(californiaAccount.id);
    console.log('✅ Hierarchy path retrieved:', path.length, 'levels');
    console.log('   Path:', path.map(p => p.name).join(' > '));

    // Test 6: Get hierarchy statistics
    console.log('\n6. Testing hierarchy statistics...');
    const stats = await AccountHierarchyService.getHierarchyStatistics(rootAccount.id);
    console.log('✅ Hierarchy statistics retrieved');
    console.log('   Total descendants:', stats.total_descendants);
    console.log('   Direct children:', stats.direct_children);
    console.log('   Max depth below:', stats.max_depth_below);
    console.log('   Hierarchy level:', stats.hierarchy_level);

    // Test 7: Validate hierarchy moves
    console.log('\n7. Testing hierarchy move validation...');
    
    // Valid move test
    const validMove = await AccountHierarchyService.validateHierarchyMove(californiaAccount.id, europeAccount.id);
    console.log('✅ Valid move validation:', validMove ? 'PASS' : 'FAIL');

    // Invalid move test (circular reference)
    const invalidMove = await AccountHierarchyService.validateHierarchyMove(rootAccount.id, californiaAccount.id);
    console.log('✅ Invalid move validation:', !invalidMove ? 'PASS' : 'FAIL');

    // Self-parent test
    const selfParent = await AccountHierarchyService.validateHierarchyMove(californiaAccount.id, californiaAccount.id);
    console.log('✅ Self-parent validation:', !selfParent ? 'PASS' : 'FAIL');

    // Test 8: Move account
    console.log('\n8. Testing account move...');
    const movedAccount = await AccountHierarchyService.moveAccount(californiaAccount.id, europeAccount.id, 1);
    console.log('✅ Account moved successfully');
    console.log('   New parent ID:', movedAccount.parent_account_id);

    // Verify the move
    const newPath = await AccountHierarchyService.getHierarchyPath(californiaAccount.id);
    console.log('   New path:', newPath.map(p => p.name).join(' > '));

    // Test 9: Get root accounts
    console.log('\n9. Testing root accounts retrieval...');
    const rootAccounts = await AccountHierarchyService.getRootAccounts();
    console.log('✅ Root accounts retrieved:', rootAccounts.length, 'accounts');

    // Test 10: Get hierarchy summary
    console.log('\n10. Testing hierarchy summary...');
    const summary = await AccountHierarchyService.getHierarchySummary();
    console.log('✅ Hierarchy summary retrieved:', summary.length, 'accounts');

    // Test 11: Flatten hierarchy tree
    console.log('\n11. Testing tree flattening...');
    const updatedTree = await AccountHierarchyService.getHierarchyTree(rootAccount.id);
    const flattened = AccountHierarchyService.flattenHierarchyTree(updatedTree);
    console.log('✅ Tree flattened:', flattened.length, 'accounts');
    console.log('   Levels:', flattened.map(a => `${a.name} (L${a.level})`).join(', '));

    // Test 12: Test circular reference prevention
    console.log('\n12. Testing circular reference prevention...');
    try {
      await AccountHierarchyService.moveAccount(rootAccount.id, californiaAccount.id, 1);
      console.log('❌ Circular reference prevention FAILED - move should have been blocked');
    } catch (error) {
      if (error.message.includes('circular reference')) {
        console.log('✅ Circular reference prevention PASSED - move was blocked');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }

    // Test 13: Test moving to root level
    console.log('\n13. Testing move to root level...');
    const rootMoved = await AccountHierarchyService.moveAccount(texasAccount.id, null, 1);
    console.log('✅ Account moved to root level');
    console.log('   Parent ID:', rootMoved.parent_account_id);

    console.log('\n🎉 All Account Hierarchy tests passed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    // Clean up - delete test accounts in reverse order (children first)
    console.log('\n🧹 Cleaning up test data...');
    for (let i = createdAccounts.length - 1; i >= 0; i--) {
      try {
        await Account.delete(createdAccounts[i], 1);
      } catch (error) {
        console.log('Note: Could not delete account', createdAccounts[i], '(may have children)');
      }
    }
    console.log('✅ Cleanup completed');
  }
}

// Run the test
testAccountHierarchy().then(() => {
  console.log('\n✅ Hierarchy test completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Hierarchy test suite failed:', error);
  process.exit(1);
});