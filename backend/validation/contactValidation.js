const Joi = require('joi');

// Contact validation schemas using Jo<PERSON> (consistent with existing codebase)

// Address schema for mailing and other addresses
const addressSchema = Joi.object({
  street: Joi.string().max(255).optional(),
  city: Joi.string().max(100).optional(),
  state: Joi.string().max(100).optional(),
  postalCode: Joi.string().max(20).optional(),
  country: Joi.string().max(100).optional(),
  latitude: Joi.number().min(-90).max(90).optional(),
  longitude: Joi.number().min(-180).max(180).optional()
});

// Contact salutations (matching database enum)
const salutations = ['Mr.', 'Ms.', 'Mrs.', 'Dr.', 'Prof.', 'Rev.'];

// Lead sources (matching database enum)
const leadSources = ['Web', 'Phone Inquiry', 'Partner Referral', 'Purchased List', 'Other'];

// Contact levels (matching database enum)
const levels = ['Primary', 'Secondary', 'Tertiary'];

// Contact statuses (matching database enum)
const statuses = ['open', 'dismissed', 'resolved', 'escalated'];

// Phone number validation regex (basic international format)
const phoneRegex = /^[\+]?[1-9][\d\s\-\(\)]{0,20}$/;

// Contact creation validation schema
const createContactSchema = Joi.object({
  // Required fields
  account_id: Joi.number().integer().positive().required(),
  last_name: Joi.string().trim().min(1).max(255).required(),
  email: Joi.string().email().max(255).required(),
  
  // Optional personal information
  first_name: Joi.string().max(255).optional(),
  salutation: Joi.string().valid(...salutations).optional(),
  title: Joi.string().max(255).optional(),
  department: Joi.string().max(255).optional(),
  
  // Communication channels
  phone: Joi.string().pattern(phoneRegex).max(50).optional(),
  mobile_phone: Joi.string().pattern(phoneRegex).max(50).optional(),
  home_phone: Joi.string().pattern(phoneRegex).max(50).optional(),
  other_phone: Joi.string().pattern(phoneRegex).max(50).optional(),
  fax: Joi.string().pattern(phoneRegex).max(50).optional(),
  
  // Address information
  mailing_address: addressSchema.optional(),
  other_address: addressSchema.optional(),
  
  // Hierarchy and relationships
  reports_to_id: Joi.number().integer().positive().optional(),
  
  // Assistant information
  assistant_name: Joi.string().max(255).optional(),
  assistant_phone: Joi.string().pattern(phoneRegex).max(50).optional(),
  
  // Additional personal details
  birthdate: Joi.date().max('now').optional(),
  
  // Lead and qualification information
  lead_source: Joi.string().valid(...leadSources).optional(),
  level: Joi.string().valid(...levels).optional(),
  description: Joi.string().max(5000).optional(),
  
  // Language and communication preferences
  languages: Joi.string().max(500).optional(),
  do_not_call: Joi.boolean().default(false),
  has_opted_out_of_email: Joi.boolean().default(false),
  
  // Ownership and assignment
  owner_id: Joi.number().integer().positive().optional(),
  
  // Custom fields
  custom_fields: Joi.object().optional()
});

// Contact update validation schema (all fields optional except constraints)
const updateContactSchema = Joi.object({
  // Optional fields for updates
  account_id: Joi.number().integer().positive().optional(),
  first_name: Joi.string().max(255).optional(),
  last_name: Joi.string().trim().min(1).max(255).optional(),
  salutation: Joi.string().valid(...salutations).optional(),
  title: Joi.string().max(255).optional(),
  department: Joi.string().max(255).optional(),
  
  phone: Joi.string().pattern(phoneRegex).max(50).optional(),
  mobile_phone: Joi.string().pattern(phoneRegex).max(50).optional(),
  home_phone: Joi.string().pattern(phoneRegex).max(50).optional(),
  other_phone: Joi.string().pattern(phoneRegex).max(50).optional(),
  fax: Joi.string().pattern(phoneRegex).max(50).optional(),
  
  email: Joi.string().email().max(255).optional(),
  
  mailing_address: addressSchema.optional(),
  other_address: addressSchema.optional(),
  
  reports_to_id: Joi.number().integer().positive().optional(),
  
  assistant_name: Joi.string().max(255).optional(),
  assistant_phone: Joi.string().pattern(phoneRegex).max(50).optional(),
  
  birthdate: Joi.date().max('now').optional(),
  
  lead_source: Joi.string().valid(...leadSources).optional(),
  level: Joi.string().valid(...levels).optional(),
  description: Joi.string().max(5000).optional(),
  
  languages: Joi.string().max(500).optional(),
  do_not_call: Joi.boolean().optional(),
  has_opted_out_of_email: Joi.boolean().optional(),
  
  owner_id: Joi.number().integer().positive().optional(),
  custom_fields: Joi.object().optional()
});

// Contact search filters validation schema
const searchFiltersSchema = Joi.object({
  search: Joi.string().max(255).optional(),
  account_id: Joi.number().integer().positive().optional(),
  department: Joi.string().max(255).optional(),
  level: Joi.string().valid(...levels).optional(),
  lead_source: Joi.string().valid(...leadSources).optional(),
  status: Joi.string().valid(...statuses).optional(),
  owner_id: Joi.number().integer().positive().optional(),
  reports_to_id: Joi.number().integer().positive().optional(),
  has_direct_reports: Joi.boolean().optional(),
  created_after: Joi.date().optional(),
  created_before: Joi.date().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10)
});

// Validation functions
const validateCreateAccount = (data) => {
  const { error, value } = createContactSchema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });
  
  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      code: detail.type
    }));
    return { isValid: false, errors, data: null };
  }
  
  return { isValid: true, errors: [], data: value };
};

const validateUpdateAccount = (data) => {
  const { error, value } = updateContactSchema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });
  
  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      code: detail.type
    }));
    return { isValid: false, errors, data: null };
  }
  
  return { isValid: true, errors: [], data: value };
};

const validateSearchFilters = (data) => {
  const { error, value } = searchFiltersSchema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });
  
  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      code: detail.type
    }));
    return { isValid: false, errors, data: null };
  }
  
  return { isValid: true, errors: [], data: value };
};

// Export validation functions with consistent naming
const validateContactCreate = validateCreateAccount;
const validateContactUpdate = validateUpdateAccount;
const validateContactSearch = validateSearchFilters;

module.exports = {
  validateContactCreate,
  validateContactUpdate,
  validateContactSearch,
  // Export schemas for direct use if needed
  createContactSchema,
  updateContactSchema,
  searchFiltersSchema,
  // Export constants
  salutations,
  leadSources,
  levels,
  statuses
};