const Joi = require('joi');

// Opportunity validation schemas using Jo<PERSON> (consistent with existing codebase)

// Opportunity stages (matching database enum)
const stages = [
  'Prospecting',
  'Qualification',
  'Needs Analysis',
  'Value Proposition',
  'Id. Decision Makers',
  'Perception Analysis',
  'Proposal/Price Quote',
  'Negotiation/Review',
  'Closed Won',
  'Closed Lost'
];

// Opportunity types (matching database enum)
const types = [
  'Existing Customer - Upgrade',
  'Existing Customer - Replacement',
  'Existing Customer - Downgrade',
  'New Customer'
];

// Lead sources (matching database enum)
const leadSources = [
  'Web',
  'Phone Inquiry',
  'Partner Referral',
  'Purchased List',
  'Other'
];

// Forecast categories (matching database enum)
const forecastCategories = [
  'Pipeline',
  'Best Case',
  'Commit',
  'Closed'
];

// General statuses (matching database enum)
const statuses = ['active', 'inactive', 'archived'];

// Contact role schema for opportunity contacts
const contactRoleSchema = Joi.object({
  contact_id: Joi.number().integer().positive().required(),
  role: Joi.string().max(100).optional(),
  is_primary: Joi.boolean().default(false)
});

// Line item schema for opportunity items
const lineItemSchema = Joi.object({
  product_name: Joi.string().max(255).required(),
  quantity: Joi.number().positive().required(),
  unit_price: Joi.number().min(0).required(),
  total_price: Joi.number().min(0).required(),
  description: Joi.string().max(1000).optional()
});

// Opportunity creation validation schema
const createOpportunitySchema = Joi.object({
  // Required fields
  name: Joi.string().trim().min(1).max(255).required(),
  account_id: Joi.number().integer().positive().required(),
  stage_name: Joi.string().valid(...stages).required(),
  close_date: Joi.date().min('now').required(),
  
  // Financial information
  amount: Joi.number().min(0).precision(2).optional(),
  probability: Joi.number().integer().min(0).max(100).optional(),
  
  // Classification
  type: Joi.string().valid(...types).optional(),
  lead_source: Joi.string().valid(...leadSources).optional(),
  forecast_category: Joi.string().valid(...forecastCategories).optional(),
  
  // Description and details
  description: Joi.string().max(5000).optional(),
  next_step: Joi.string().max(1000).optional(),
  
  // Privacy and status
  is_private: Joi.boolean().default(false),
  status: Joi.string().valid(...statuses).default('active'),
  
  // Ownership and assignment
  owner_id: Joi.number().integer().positive().optional(),
  
  // Campaign association
  campaign_id: Joi.number().integer().positive().optional(),
  
  // Line items
  has_opportunity_line_item: Joi.boolean().default(false),
  items: Joi.when('has_opportunity_line_item', {
    is: true,
    then: Joi.array().items(lineItemSchema).min(1),
    otherwise: Joi.array().items(lineItemSchema).optional()
  }),
  
  // Contacts
  contacts: Joi.array().items(contactRoleSchema).optional(),
  
  // Integration
  integration_id: Joi.string().max(255).optional(),
  
  // Custom fields
  custom_fields: Joi.object().optional()
});

// Opportunity update validation schema (all fields optional except constraints)
const updateOpportunitySchema = Joi.object({
  // Optional fields for updates
  name: Joi.string().trim().min(1).max(255).optional(),
  account_id: Joi.number().integer().positive().optional(),
  stage_name: Joi.string().valid(...stages).optional(),
  close_date: Joi.date().optional(),
  
  amount: Joi.number().min(0).precision(2).optional(),
  probability: Joi.number().integer().min(0).max(100).optional(),
  
  type: Joi.string().valid(...types).optional(),
  lead_source: Joi.string().valid(...leadSources).optional(),
  forecast_category: Joi.string().valid(...forecastCategories).optional(),
  
  description: Joi.string().max(5000).optional(),
  next_step: Joi.string().max(1000).optional(),
  
  is_private: Joi.boolean().optional(),
  status: Joi.string().valid(...statuses).optional(),
  
  owner_id: Joi.number().integer().positive().optional(),
  campaign_id: Joi.number().integer().positive().optional(),
  
  has_opportunity_line_item: Joi.boolean().optional(),
  items: Joi.array().items(lineItemSchema).optional(),
  
  contacts: Joi.array().items(contactRoleSchema).optional(),
  
  integration_id: Joi.string().max(255).optional(),
  custom_fields: Joi.object().optional()
});

// Search filters validation schema
const searchFiltersSchema = Joi.object({
  search: Joi.string().max(255).optional(),
  account_id: Joi.number().integer().positive().optional(),
  stage_name: Joi.string().valid(...stages).optional(),
  owner_id: Joi.number().integer().positive().optional(),
  type: Joi.string().valid(...types).optional(),
  lead_source: Joi.string().valid(...leadSources).optional(),
  forecast_category: Joi.string().valid(...forecastCategories).optional(),
  is_closed: Joi.boolean().optional(),
  is_won: Joi.boolean().optional(),
  min_amount: Joi.number().min(0).optional(),
  max_amount: Joi.number().min(0).optional(),
  close_date_from: Joi.date().optional(),
  close_date_to: Joi.date().optional(),
  created_after: Joi.date().optional(),
  created_before: Joi.date().optional(),
  sort_by: Joi.string().valid(
    'name', 'amount', 'close_date', 'stage_name', 'probability', 
    'created_at', 'updated_at', 'account_name', 'owner_name'
  ).default('created_at'),
  sort_order: Joi.string().valid('asc', 'desc').default('desc'),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10)
});

// Stage advance validation schema
const stageAdvanceSchema = Joi.object({
  stage_name: Joi.string().valid(...stages).required()
});

// Validation functions
const validateCreateOpportunity = (data) => {
  const { error, value } = createOpportunitySchema.validate(data, {
    abortEarly: false,
    stripUnknown: true,
    convert: true
  });
  
  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    };
  }
  
  return {
    isValid: true,
    data: value
  };
};

const validateUpdateOpportunity = (data) => {
  const { error, value } = updateOpportunitySchema.validate(data, {
    abortEarly: false,
    stripUnknown: true,
    convert: true
  });
  
  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    };
  }
  
  return {
    isValid: true,
    data: value
  };
};

const validateOpportunitySearch = (data) => {
  const { error, value } = searchFiltersSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true,
    convert: true
  });
  
  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    };
  }
  
  // Additional business logic validation
  if (value.min_amount && value.max_amount && value.min_amount > value.max_amount) {
    return {
      isValid: false,
      errors: [{
        field: 'amount_range',
        message: 'Minimum amount cannot be greater than maximum amount'
      }]
    };
  }
  
  if (value.close_date_from && value.close_date_to && value.close_date_from > value.close_date_to) {
    return {
      isValid: false,
      errors: [{
        field: 'close_date_range',
        message: 'Close date from cannot be after close date to'
      }]
    };
  }
  
  if (value.created_after && value.created_before && value.created_after > value.created_before) {
    return {
      isValid: false,
      errors: [{
        field: 'created_date_range',
        message: 'Created after date cannot be after created before date'
      }]
    };
  }
  
  return {
    isValid: true,
    data: value
  };
};

const validateStageAdvance = (data) => {
  const { error, value } = stageAdvanceSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true,
    convert: true
  });
  
  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    };
  }
  
  return {
    isValid: true,
    data: value
  };
};

// Custom validation helpers
const validateOpportunityAmount = (amount, items) => {
  if (!items || items.length === 0) {
    return true; // No line items, amount can be anything
  }
  
  const totalItemsAmount = items.reduce((sum, item) => sum + (item.total_price || 0), 0);
  
  // Allow some tolerance for rounding differences
  const tolerance = 0.01;
  return Math.abs(amount - totalItemsAmount) <= tolerance;
};

const validateContactsForAccount = async (contacts, accountId) => {
  // This would typically check if contacts belong to the account
  // Implementation would depend on your Contact model
  return true; // Placeholder
};

const validateStageTransition = (currentStage, newStage) => {
  // Define allowed stage transitions
  const stageOrder = {
    'Prospecting': 0,
    'Qualification': 1,
    'Needs Analysis': 2,
    'Value Proposition': 3,
    'Id. Decision Makers': 4,
    'Perception Analysis': 5,
    'Proposal/Price Quote': 6,
    'Negotiation/Review': 7,
    'Closed Won': 8,
    'Closed Lost': 8
  };
  
  const currentOrder = stageOrder[currentStage];
  const newOrder = stageOrder[newStage];
  
  if (currentOrder === undefined || newOrder === undefined) {
    return false;
  }
  
  // Allow moving to any stage for now (can be made more restrictive)
  return true;
};

module.exports = {
  validateOpportunityCreate: validateCreateOpportunity,
  validateOpportunityUpdate: validateUpdateOpportunity,
  validateOpportunitySearch,
  validateStageAdvance,
  
  // Schemas for direct use
  createOpportunitySchema,
  updateOpportunitySchema,
  searchFiltersSchema,
  stageAdvanceSchema,
  contactRoleSchema,
  lineItemSchema,
  
  // Constants
  stages,
  types,
  leadSources,
  forecastCategories,
  statuses,
  
  // Helper functions
  validateOpportunityAmount,
  validateContactsForAccount,
  validateStageTransition
};