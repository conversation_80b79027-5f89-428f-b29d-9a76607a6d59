const Joi = require('joi');

// Account validation schemas using <PERSON><PERSON> (consistent with existing codebase)

// Address schema for billing and shipping addresses
const addressSchema = Joi.object({
  street: Joi.string().max(255).optional(),
  city: Joi.string().max(100).optional(),
  state: Joi.string().max(100).optional(),
  postalCode: Joi.string().max(20).optional(),
  country: Joi.string().max(100).optional(),
  latitude: Joi.number().min(-90).max(90).optional(),
  longitude: Joi.number().min(-180).max(180).optional()
});

// Account types (matching Salesforce picklist values)
const accountTypes = [
  'Prospect',
  'Customer - Direct',
  'Customer - Channel',
  'Channel Partner / Reseller',
  'Installation Partner',
  'Technology Partner',
  'Other'
];

// Industries (matching Salesforce picklist values)
const industries = [
  'Agriculture',
  'Apparel',
  'Banking',
  'Biotechnology',
  'Chemicals',
  'Communications',
  'Construction',
  'Consulting',
  'Education',
  'Electronics',
  'Energy',
  'Engineering',
  'Entertainment',
  'Environmental',
  'Finance',
  'Food & Beverage',
  'Government',
  'Healthcare',
  'Hospitality',
  'Insurance',
  'Machinery',
  'Manufacturing',
  'Media',
  'Not For Profit',
  'Recreation',
  'Retail',
  'Shipping',
  'Technology',
  'Telecommunications',
  'Transportation',
  'Utilities',
  'Other'
];

// Ownership types
const ownershipTypes = [
  'Public',
  'Private',
  'Subsidiary',
  'Other'
];

// Rating values
const ratings = [
  'Hot',
  'Warm',
  'Cold'
];

// Account status values
const accountStatuses = [
  'ACTIVE',
  'INACTIVE'
];

// Account creation validation schema
const createAccountSchema = Joi.object({
  name: Joi.string()
    .required()
    .min(1)
    .max(255)
    .trim()
    .messages({
      'string.empty': 'Account name is required',
      'string.max': 'Account name must be less than 255 characters',
      'any.required': 'Account name is required'
    }),
  
  account_number: Joi.string()
    .max(100)
    .trim()
    .optional()
    .allow('')
    .messages({
      'string.max': 'Account number must be less than 100 characters'
    }),
  
  type: Joi.string()
    .valid(...accountTypes)
    .optional()
    .messages({
      'any.only': `Account type must be one of: ${accountTypes.join(', ')}`
    }),
  
  industry: Joi.string()
    .valid(...industries)
    .optional()
    .messages({
      'any.only': `Industry must be one of: ${industries.join(', ')}`
    }),
  
  annual_revenue: Joi.number()
    .min(0)
    .precision(2)
    .optional()
    .messages({
      'number.min': 'Annual revenue cannot be negative',
      'number.precision': 'Annual revenue can have at most 2 decimal places'
    }),
  
  number_of_employees: Joi.number()
    .integer()
    .min(0)
    .optional()
    .messages({
      'number.integer': 'Number of employees must be a whole number',
      'number.min': 'Number of employees cannot be negative'
    }),
  
  ownership: Joi.string()
    .valid(...ownershipTypes)
    .optional()
    .messages({
      'any.only': `Ownership must be one of: ${ownershipTypes.join(', ')}`
    }),
  
  phone: Joi.string()
    .max(50)
    .pattern(/^[\+]?[1-9][\d]{0,15}$/)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Phone number must be less than 50 characters',
      'string.pattern.base': 'Phone number format is invalid'
    }),
  
  fax: Joi.string()
    .max(50)
    .pattern(/^[\+]?[1-9][\d]{0,15}$/)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Fax number must be less than 50 characters',
      'string.pattern.base': 'Fax number format is invalid'
    }),
  
  website: Joi.string()
    .uri({ scheme: ['http', 'https'] })
    .max(255)
    .optional()
    .allow('')
    .messages({
      'string.uri': 'Website must be a valid URL',
      'string.max': 'Website URL must be less than 255 characters'
    }),
  
  ticker_symbol: Joi.string()
    .max(10)
    .uppercase()
    .pattern(/^[A-Z]{1,10}$/)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Ticker symbol must be less than 10 characters',
      'string.pattern.base': 'Ticker symbol must contain only uppercase letters'
    }),
  
  site: Joi.string()
    .max(255)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Site must be less than 255 characters'
    }),
  
  rating: Joi.string()
    .valid(...ratings)
    .optional()
    .messages({
      'any.only': `Rating must be one of: ${ratings.join(', ')}`
    }),
  
  description: Joi.string()
    .max(5000)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Description must be less than 5000 characters'
    }),
  
  billing_address: addressSchema.optional(),
  
  shipping_address: addressSchema.optional(),
  
  parent_account_id: Joi.number()
    .integer()
    .positive()
    .optional()
    .messages({
      'number.integer': 'Parent account ID must be a whole number',
      'number.positive': 'Parent account ID must be positive'
    }),
  
  owner_id: Joi.number()
    .integer()
    .positive()
    .optional()
    .messages({
      'number.integer': 'Owner ID must be a whole number',
      'number.positive': 'Owner ID must be positive'
    }),
  
  custom_fields: Joi.object()
    .optional()
    .default({})
    .messages({
      'object.base': 'Custom fields must be an object'
    })
});

// Account update validation schema (all fields optional except constraints)
const updateAccountSchema = Joi.object({
  name: Joi.string()
    .min(1)
    .max(255)
    .trim()
    .optional()
    .messages({
      'string.empty': 'Account name cannot be empty',
      'string.max': 'Account name must be less than 255 characters'
    }),
  
  account_number: Joi.string()
    .max(100)
    .trim()
    .optional()
    .allow('')
    .messages({
      'string.max': 'Account number must be less than 100 characters'
    }),
  
  type: Joi.string()
    .valid(...accountTypes)
    .optional()
    .messages({
      'any.only': `Account type must be one of: ${accountTypes.join(', ')}`
    }),
  
  industry: Joi.string()
    .valid(...industries)
    .optional()
    .messages({
      'any.only': `Industry must be one of: ${industries.join(', ')}`
    }),
  
  annual_revenue: Joi.number()
    .min(0)
    .precision(2)
    .optional()
    .allow(null)
    .messages({
      'number.min': 'Annual revenue cannot be negative',
      'number.precision': 'Annual revenue can have at most 2 decimal places'
    }),
  
  number_of_employees: Joi.number()
    .integer()
    .min(0)
    .optional()
    .allow(null)
    .messages({
      'number.integer': 'Number of employees must be a whole number',
      'number.min': 'Number of employees cannot be negative'
    }),
  
  ownership: Joi.string()
    .valid(...ownershipTypes)
    .optional()
    .messages({
      'any.only': `Ownership must be one of: ${ownershipTypes.join(', ')}`
    }),
  
  phone: Joi.string()
    .max(50)
    .pattern(/^[\+]?[1-9][\d]{0,15}$/)
    .optional()
    .allow('', null)
    .messages({
      'string.max': 'Phone number must be less than 50 characters',
      'string.pattern.base': 'Phone number format is invalid'
    }),
  
  fax: Joi.string()
    .max(50)
    .pattern(/^[\+]?[1-9][\d]{0,15}$/)
    .optional()
    .allow('', null)
    .messages({
      'string.max': 'Fax number must be less than 50 characters',
      'string.pattern.base': 'Fax number format is invalid'
    }),
  
  website: Joi.string()
    .uri({ scheme: ['http', 'https'] })
    .max(255)
    .optional()
    .allow('', null)
    .messages({
      'string.uri': 'Website must be a valid URL',
      'string.max': 'Website URL must be less than 255 characters'
    }),
  
  ticker_symbol: Joi.string()
    .max(10)
    .uppercase()
    .pattern(/^[A-Z]{1,10}$/)
    .optional()
    .allow('', null)
    .messages({
      'string.max': 'Ticker symbol must be less than 10 characters',
      'string.pattern.base': 'Ticker symbol must contain only uppercase letters'
    }),
  
  site: Joi.string()
    .max(255)
    .optional()
    .allow('', null)
    .messages({
      'string.max': 'Site must be less than 255 characters'
    }),
  
  rating: Joi.string()
    .valid(...ratings)
    .optional()
    .messages({
      'any.only': `Rating must be one of: ${ratings.join(', ')}`
    }),
  
  description: Joi.string()
    .max(5000)
    .optional()
    .allow('', null)
    .messages({
      'string.max': 'Description must be less than 5000 characters'
    }),
  
  billing_address: addressSchema.optional().allow(null),
  
  shipping_address: addressSchema.optional().allow(null),
  
  parent_account_id: Joi.number()
    .integer()
    .positive()
    .optional()
    .allow(null)
    .messages({
      'number.integer': 'Parent account ID must be a whole number',
      'number.positive': 'Parent account ID must be positive'
    }),
  
  owner_id: Joi.number()
    .integer()
    .positive()
    .optional()
    .messages({
      'number.integer': 'Owner ID must be a whole number',
      'number.positive': 'Owner ID must be positive'
    }),
  
  status: Joi.string()
    .valid(...accountStatuses)
    .optional()
    .messages({
      'any.only': `Status must be one of: ${accountStatuses.join(', ')}`
    }),
  
  custom_fields: Joi.object()
    .optional()
    .messages({
      'object.base': 'Custom fields must be an object'
    })
});

// Search filters validation schema
const searchFiltersSchema = Joi.object({
  search: Joi.string()
    .max(255)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Search term must be less than 255 characters'
    }),
  
  industry: Joi.alternatives()
    .try(
      Joi.string().valid(...industries),
      Joi.array().items(Joi.string().valid(...industries))
    )
    .optional()
    .messages({
      'any.only': `Industry must be one of: ${industries.join(', ')}`
    }),
  
  type: Joi.alternatives()
    .try(
      Joi.string().valid(...accountTypes),
      Joi.array().items(Joi.string().valid(...accountTypes))
    )
    .optional()
    .messages({
      'any.only': `Type must be one of: ${accountTypes.join(', ')}`
    }),
  
  rating: Joi.alternatives()
    .try(
      Joi.string().valid(...ratings),
      Joi.array().items(Joi.string().valid(...ratings))
    )
    .optional()
    .messages({
      'any.only': `Rating must be one of: ${ratings.join(', ')}`
    }),
  
  ownership: Joi.alternatives()
    .try(
      Joi.string().valid(...ownershipTypes),
      Joi.array().items(Joi.string().valid(...ownershipTypes))
    )
    .optional()
    .messages({
      'any.only': `Ownership must be one of: ${ownershipTypes.join(', ')}`
    }),
  
  status: Joi.alternatives()
    .try(
      Joi.string().valid(...accountStatuses),
      Joi.array().items(Joi.string().valid(...accountStatuses))
    )
    .optional()
    .messages({
      'any.only': `Status must be one of: ${accountStatuses.join(', ')}`
    }),
  
  annual_revenue_min: Joi.number()
    .min(0)
    .optional()
    .messages({
      'number.min': 'Minimum annual revenue cannot be negative'
    }),
  
  annual_revenue_max: Joi.number()
    .min(0)
    .optional()
    .messages({
      'number.min': 'Maximum annual revenue cannot be negative'
    }),
  
  number_of_employees_min: Joi.number()
    .integer()
    .min(0)
    .optional()
    .messages({
      'number.integer': 'Minimum number of employees must be a whole number',
      'number.min': 'Minimum number of employees cannot be negative'
    }),
  
  number_of_employees_max: Joi.number()
    .integer()
    .min(0)
    .optional()
    .messages({
      'number.integer': 'Maximum number of employees must be a whole number',
      'number.min': 'Maximum number of employees cannot be negative'
    }),
  
  country: Joi.alternatives()
    .try(
      Joi.string().max(100),
      Joi.array().items(Joi.string().max(100))
    )
    .optional()
    .messages({
      'string.max': 'Country must be less than 100 characters'
    }),
  
  state: Joi.alternatives()
    .try(
      Joi.string().max(100),
      Joi.array().items(Joi.string().max(100))
    )
    .optional()
    .messages({
      'string.max': 'State must be less than 100 characters'
    }),
  
  city: Joi.alternatives()
    .try(
      Joi.string().max(100),
      Joi.array().items(Joi.string().max(100))
    )
    .optional()
    .messages({
      'string.max': 'City must be less than 100 characters'
    }),
  
  parent_account_id: Joi.number()
    .integer()
    .optional()
    .allow(null)
    .messages({
      'number.integer': 'Parent account ID must be a whole number'
    }),
  
  owner_id: Joi.number()
    .integer()
    .positive()
    .optional()
    .messages({
      'number.integer': 'Owner ID must be a whole number',
      'number.positive': 'Owner ID must be positive'
    }),
  
  has_children: Joi.boolean()
    .optional()
    .messages({
      'boolean.base': 'Has children must be true or false'
    }),
  
  created_after: Joi.date()
    .optional()
    .messages({
      'date.base': 'Created after must be a valid date'
    }),
  
  created_before: Joi.date()
    .optional()
    .messages({
      'date.base': 'Created before must be a valid date'
    }),
  
  page: Joi.number()
    .integer()
    .min(1)
    .optional()
    .default(1)
    .messages({
      'number.integer': 'Page must be a whole number',
      'number.min': 'Page must be at least 1'
    }),
  
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .optional()
    .default(10)
    .messages({
      'number.integer': 'Limit must be a whole number',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    })
});

// Validation middleware functions
const validateCreateAccount = (req, res, next) => {
  const { error, value } = createAccountSchema.validate(req.body, {
    abortEarly: false,
    stripUnknown: true
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      code: detail.type
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      errors
    });
  }

  req.validatedData = value;
  next();
};

const validateUpdateAccount = (req, res, next) => {
  const { error, value } = updateAccountSchema.validate(req.body, {
    abortEarly: false,
    stripUnknown: true
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      code: detail.type
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      errors
    });
  }

  req.validatedData = value;
  next();
};

const validateSearchFilters = (req, res, next) => {
  const { error, value } = searchFiltersSchema.validate(req.query, {
    abortEarly: false,
    stripUnknown: true
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      code: detail.type
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      errors
    });
  }

  req.validatedFilters = value;
  next();
};

module.exports = {
  createAccountSchema,
  updateAccountSchema,
  searchFiltersSchema,
  validateCreateAccount,
  validateUpdateAccount,
  validateSearchFilters,
  accountTypes,
  industries,
  ownershipTypes,
  ratings,
  accountStatuses
};