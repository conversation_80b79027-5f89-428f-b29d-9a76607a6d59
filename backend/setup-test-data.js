#!/usr/bin/env node

/**
 * Setup Test Data Script
 * Creates necessary vendor records and runs comprehensive RFQ tests
 */

const { query } = require('./config/database');
const { runCompleteTestSuite } = require('./test-rfq-crud-complete');

// Sample vendor data matching actual database schema
const sampleVendors = [
  {
    name: "Premium Office Solutions Inc.",
    contact_email: "<EMAIL>",
    contact_phone: "******-0101",
    address: {
      street: "123 Business Park Dr, Suite 100",
      city: "Corporate City",
      state: "CC",
      zip: "12345",
      country: "USA"
    },
    category: "Office Furniture & Equipment",
    certifications: {
      iso9001: true,
      iso14001: false,
      website: "https://www.premiumoffice.com"
    },
    performance_score: 4.8,
    status: "active",
    custom_fields: {
      specialties: ["Executive Furniture", "Ergonomic Solutions"],
      years_in_business: 15,
      employee_count: 50
    }
  },
  {
    name: "TechEquip Pro",
    contact_email: "<EMAIL>",
    contact_phone: "******-0102",
    address: {
      street: "456 Technology Blvd, Tech Tower",
      city: "Innovation City",
      state: "IC",
      zip: "67890",
      country: "USA"
    },
    category: "IT Equipment & Services",
    certifications: {
      iso9001: true,
      iso14001: true,
      website: "https://www.techequippro.com"
    },
    performance_score: 4.6,
    status: "active",
    custom_fields: {
      specialties: ["Business Laptops", "Network Equipment", "IT Support"],
      years_in_business: 12,
      employee_count: 75
    }
  },
  {
    name: "Creative Marketing Solutions",
    contact_email: "<EMAIL>",
    contact_phone: "******-0103",
    address: {
      street: "789 Design Street, Creative Quarter",
      city: "Art City",
      state: "AC",
      zip: "11111",
      country: "USA"
    },
    category: "Marketing & Promotional Materials",
    certifications: {
      iso9001: false,
      iso14001: true,
      website: "https://www.creativemarketingsolutions.com"
    },
    performance_score: 4.7,
    status: "active",
    custom_fields: {
      specialties: ["Print Marketing", "Promotional Items", "Trade Show Materials"],
      years_in_business: 8,
      employee_count: 25
    }
  },
  {
    name: "Global Supply Chain Partners",
    contact_email: "<EMAIL>",
    contact_phone: "******-0104",
    address: {
      street: "321 Supply Chain Ave, Logistics Hub",
      city: "Trade City",
      state: "TC",
      zip: "22222",
      country: "USA"
    },
    category: "General Procurement & Supplies",
    certifications: {
      iso9001: true,
      iso14001: true,
      website: "https://www.globalscp.com"
    },
    performance_score: 4.5,
    status: "active",
    custom_fields: {
      specialties: ["Bulk Procurement", "Supply Chain Management", "Logistics"],
      years_in_business: 20,
      employee_count: 150
    }
  },
  {
    name: "Eco-Friendly Business Solutions",
    contact_email: "<EMAIL>",
    contact_phone: "******-0105",
    address: {
      street: "654 Green Way, Sustainability Park",
      city: "Eco City",
      state: "EC",
      zip: "33333",
      country: "USA"
    },
    category: "Sustainable Office Solutions",
    certifications: {
      iso9001: true,
      iso14001: true,
      website: "https://www.ecobusinesssolutions.com"
    },
    performance_score: 4.9,
    status: "active",
    custom_fields: {
      specialties: ["Sustainable Materials", "Green Certifications", "Eco-Friendly Products"],
      years_in_business: 10,
      employee_count: 30
    }
  }
];

async function createVendorsIfNotExist() {
  console.log('🏢 Setting up vendor records...\n');

  try {
    // Check if vendors table exists and has data
    const existingVendors = await query('SELECT COUNT(*) as count FROM vendors');
    const vendorCount = parseInt(existingVendors.rows[0].count);

    if (vendorCount >= 5) {
      console.log(`✅ Found ${vendorCount} existing vendors - skipping vendor creation`);
      return;
    }

    console.log(`Found ${vendorCount} existing vendors - creating additional vendors...`);

    // Create vendors
    for (let i = 0; i < sampleVendors.length; i++) {
      const vendor = sampleVendors[i];
      
      // Check if vendor already exists
      const existingVendor = await query(
        'SELECT id FROM vendors WHERE contact_email = $1',
        [vendor.contact_email]
      );

      if (existingVendor.rows.length > 0) {
        console.log(`   Vendor "${vendor.name}" already exists - skipping`);
        continue;
      }

      const insertQuery = `
        INSERT INTO vendors (
          name, contact_email, contact_phone, address, category, 
          certifications, performance_score, status, custom_fields, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id, name
      `;

      const result = await query(insertQuery, [
        vendor.name,
        vendor.contact_email,
        vendor.contact_phone,
        JSON.stringify(vendor.address),
        vendor.category,
        JSON.stringify(vendor.certifications),
        vendor.performance_score,
        vendor.status,
        JSON.stringify(vendor.custom_fields)
      ]);

      console.log(`   ✅ Created vendor: ${result.rows[0].name} (ID: ${result.rows[0].id})`);
    }

    console.log('\n✅ Vendor setup completed successfully!\n');

  } catch (error) {
    console.error('❌ Failed to setup vendors:', error.message);
    throw error;
  }
}

async function createUserIfNotExist() {
  console.log('👤 Setting up test user...\n');

  try {
    // Check if users table has data
    const existingUsers = await query('SELECT COUNT(*) as count FROM users');
    const userCount = parseInt(existingUsers.rows[0].count);

    if (userCount > 0) {
      console.log(`✅ Found ${userCount} existing users - skipping user creation`);
      return;
    }

    // Create a test user
    const testUser = {
      email: '<EMAIL>',
      password_hash: '$2b$10$dummy.hash.for.testing.purposes.only',
      first_name: 'Test',
      last_name: 'User',
      role: 'manager',
      status: 'active'
    };

    const insertQuery = `
      INSERT INTO users (
        email, password_hash, first_name, last_name, role, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING id, email, role
    `;

    const result = await query(insertQuery, [
      testUser.email,
      testUser.password_hash,
      testUser.first_name,
      testUser.last_name,
      testUser.role,
      testUser.status
    ]);

    console.log(`✅ Created test user: ${result.rows[0].email} (ID: ${result.rows[0].id}, Role: ${result.rows[0].role})`);
    console.log('');

  } catch (error) {
    console.error('❌ Failed to setup test user:', error.message);
    throw error;
  }
}

async function setupAndRunTests() {
  console.log('🚀 VENDORMS RFQ TEST DATA SETUP & COMPREHENSIVE TESTING');
  console.log('=' .repeat(70));
  console.log('This script will:');
  console.log('• Create sample vendor records if needed');
  console.log('• Create a test user if needed');
  console.log('• Run comprehensive RFQ CRUD tests');
  console.log('• Create sample RFQs with full form data');
  console.log('• Create detailed submissions for testing');
  console.log('• Verify all API endpoints and features');
  console.log('=' .repeat(70));

  try {
    // Step 1: Setup vendors
    await createVendorsIfNotExist();

    // Step 2: Setup test user
    await createUserIfNotExist();

    // Step 3: Run comprehensive tests
    console.log('🧪 Starting comprehensive RFQ testing...\n');
    await runCompleteTestSuite();

  } catch (error) {
    console.error('\n💥 SETUP AND TESTING FAILED:', error.message);
    console.error('\nTroubleshooting steps:');
    console.error('1. Ensure PostgreSQL database is running');
    console.error('2. Ensure backend server is running on port 3001');
    console.error('3. Check database connection settings in config/database.js');
    console.error('4. Verify all required tables exist (run migrations if needed)');
    console.error('5. Check that the backend API is accessible');
    
    if (error.code) {
      console.error(`\nDatabase error code: ${error.code}`);
    }
    
    process.exit(1);
  }
}

// Export for use in other modules
module.exports = {
  createVendorsIfNotExist,
  createUserIfNotExist,
  setupAndRunTests,
  sampleVendors
};

// Run setup and tests if executed directly
if (require.main === module) {
  setupAndRunTests().catch(console.error);
}