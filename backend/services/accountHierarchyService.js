const Account = require('../models/Account');
const { query } = require('../config/database');

class AccountHierarchyService {
  /**
   * Get the complete hierarchy tree starting from a root account
   * @param {number} rootAccountId - The root account ID
   * @returns {Object} Hierarchical tree structure
   */
  static async getHierarchyTree(rootAccountId) {
    try {
      const hierarchy = await Account.getHierarchy(rootAccountId);
      
      if (hierarchy.length === 0) {
        return null;
      }

      // Build tree structure
      const tree = this.buildTreeFromFlat(hierarchy);
      return tree;
    } catch (error) {
      throw new Error(`Failed to get hierarchy tree: ${error.message}`);
    }
  }

  /**
   * Build a tree structure from flat hierarchy data
   * @param {Array} flatData - Flat array of hierarchy nodes
   * @returns {Object} Tree structure
   */
  static buildTreeFromFlat(flatData) {
    const nodeMap = new Map();
    const roots = [];

    // Create node map
    flatData.forEach(node => {
      nodeMap.set(node.id, {
        ...node,
        children: []
      });
    });

    // Build tree structure
    flatData.forEach(node => {
      const currentNode = nodeMap.get(node.id);
      
      if (node.parent_account_id && nodeMap.has(node.parent_account_id)) {
        const parentNode = nodeMap.get(node.parent_account_id);
        parentNode.children.push(currentNode);
      } else {
        roots.push(currentNode);
      }
    });

    return roots.length === 1 ? roots[0] : roots;
  }

  /**
   * Get all descendant account IDs for a given account
   * @param {number} accountId - The parent account ID
   * @returns {Array} Array of descendant account IDs
   */
  static async getAllDescendantIds(accountId) {
    try {
      const result = await query(
        `WITH RECURSIVE descendants AS (
          SELECT id, parent_account_id, 0 as level
          FROM accounts 
          WHERE parent_account_id = $1 AND is_deleted = FALSE
          
          UNION ALL
          
          SELECT a.id, a.parent_account_id, d.level + 1
          FROM accounts a
          INNER JOIN descendants d ON a.parent_account_id = d.id
          WHERE a.is_deleted = FALSE AND d.level < 10
        )
        SELECT id FROM descendants ORDER BY level, id`,
        [accountId]
      );

      return result.rows.map(row => row.id);
    } catch (error) {
      throw new Error(`Failed to get descendant IDs: ${error.message}`);
    }
  }

  /**
   * Get all ancestor account IDs for a given account
   * @param {number} accountId - The child account ID
   * @returns {Array} Array of ancestor account IDs
   */
  static async getAllAncestorIds(accountId) {
    try {
      const result = await query(
        `WITH RECURSIVE ancestors AS (
          SELECT id, parent_account_id, 0 as level
          FROM accounts 
          WHERE id = $1 AND is_deleted = FALSE
          
          UNION ALL
          
          SELECT a.id, a.parent_account_id, anc.level + 1
          FROM accounts a
          INNER JOIN ancestors anc ON a.id = anc.parent_account_id
          WHERE a.is_deleted = FALSE AND anc.level < 10
        )
        SELECT id FROM ancestors WHERE level > 0 ORDER BY level DESC`,
        [accountId]
      );

      return result.rows.map(row => row.id);
    } catch (error) {
      throw new Error(`Failed to get ancestor IDs: ${error.message}`);
    }
  }

  /**
   * Validate that moving an account won't create a circular reference
   * @param {number} accountId - The account to move
   * @param {number} newParentId - The proposed new parent
   * @returns {boolean} True if move is valid, false if it would create a circle
   */
  static async validateHierarchyMove(accountId, newParentId) {
    try {
      if (accountId === newParentId) {
        return false; // Account cannot be its own parent
      }

      // Get all descendants of the account being moved
      const descendants = await this.getAllDescendantIds(accountId);
      
      // Check if the new parent is among the descendants
      return !descendants.includes(newParentId);
    } catch (error) {
      throw new Error(`Failed to validate hierarchy move: ${error.message}`);
    }
  }

  /**
   * Get the hierarchy path (breadcrumb) for an account
   * @param {number} accountId - The account ID
   * @returns {Array} Array of account objects representing the path from root to account
   */
  static async getHierarchyPath(accountId) {
    try {
      const result = await query(
        `WITH RECURSIVE path AS (
          SELECT id, name, parent_account_id, 0 as level
          FROM accounts 
          WHERE id = $1 AND is_deleted = FALSE
          
          UNION ALL
          
          SELECT a.id, a.name, a.parent_account_id, p.level + 1
          FROM accounts a
          INNER JOIN path p ON a.id = p.parent_account_id
          WHERE a.is_deleted = FALSE AND p.level < 10
        )
        SELECT id, name, level FROM path ORDER BY level DESC`,
        [accountId]
      );

      return result.rows;
    } catch (error) {
      throw new Error(`Failed to get hierarchy path: ${error.message}`);
    }
  }

  /**
   * Get hierarchy statistics for an account
   * @param {number} accountId - The account ID
   * @returns {Object} Statistics about the hierarchy
   */
  static async getHierarchyStatistics(accountId) {
    try {
      const [descendants, ancestors, directChildren] = await Promise.all([
        this.getAllDescendantIds(accountId),
        this.getAllAncestorIds(accountId),
        Account.getChildren(accountId)
      ]);

      // Get depth statistics
      const depthResult = await query(
        `WITH RECURSIVE hierarchy_depth AS (
          SELECT id, parent_account_id, 0 as depth
          FROM accounts 
          WHERE id = $1 AND is_deleted = FALSE
          
          UNION ALL
          
          SELECT a.id, a.parent_account_id, hd.depth + 1
          FROM accounts a
          INNER JOIN hierarchy_depth hd ON a.parent_account_id = hd.id
          WHERE a.is_deleted = FALSE AND hd.depth < 10
        )
        SELECT MAX(depth) as max_depth, COUNT(*) - 1 as total_descendants
        FROM hierarchy_depth`,
        [accountId]
      );

      const stats = depthResult.rows[0];

      return {
        account_id: accountId,
        total_descendants: descendants.length,
        total_ancestors: ancestors.length,
        direct_children: directChildren.length,
        max_depth_below: parseInt(stats.max_depth) || 0,
        hierarchy_level: ancestors.length
      };
    } catch (error) {
      throw new Error(`Failed to get hierarchy statistics: ${error.message}`);
    }
  }

  /**
   * Move an account to a new parent (with validation)
   * @param {number} accountId - The account to move
   * @param {number} newParentId - The new parent account ID (null for root level)
   * @param {number} userId - The user performing the operation
   * @returns {Object} Updated account
   */
  static async moveAccount(accountId, newParentId, userId) {
    try {
      // Validate the move
      if (newParentId !== null) {
        const isValidMove = await this.validateHierarchyMove(accountId, newParentId);
        if (!isValidMove) {
          throw new Error('Invalid move: would create circular reference');
        }

        // Verify new parent exists
        const newParent = await Account.findById(newParentId);
        if (!newParent) {
          throw new Error('New parent account not found');
        }
      }

      // Perform the move
      const updatedAccount = await Account.update(accountId, {
        parent_account_id: newParentId
      }, userId);

      return updatedAccount;
    } catch (error) {
      throw new Error(`Failed to move account: ${error.message}`);
    }
  }

  /**
   * Get all root accounts (accounts with no parent)
   * @returns {Array} Array of root account objects
   */
  static async getRootAccounts() {
    try {
      const result = await query(
        `SELECT id, name, account_number, type, industry, status,
                (SELECT COUNT(*) FROM accounts child 
                 WHERE child.parent_account_id = accounts.id AND child.is_deleted = FALSE) as children_count
         FROM accounts 
         WHERE parent_account_id IS NULL AND is_deleted = FALSE
         ORDER BY name`,
        []
      );

      return result.rows;
    } catch (error) {
      throw new Error(`Failed to get root accounts: ${error.message}`);
    }
  }

  /**
   * Get hierarchy summary for all accounts
   * @returns {Array} Array of accounts with hierarchy information
   */
  static async getHierarchySummary() {
    try {
      const result = await query(
        `SELECT 
          a.id,
          a.name,
          a.parent_account_id,
          parent.name as parent_name,
          (SELECT COUNT(*) FROM accounts child 
           WHERE child.parent_account_id = a.id AND child.is_deleted = FALSE) as children_count,
          CASE 
            WHEN a.parent_account_id IS NULL THEN 0
            ELSE (
              WITH RECURSIVE level_calc AS (
                SELECT id, parent_account_id, 0 as level
                FROM accounts 
                WHERE id = a.id AND is_deleted = FALSE
                
                UNION ALL
                
                SELECT acc.id, acc.parent_account_id, lc.level + 1
                FROM accounts acc
                INNER JOIN level_calc lc ON acc.id = lc.parent_account_id
                WHERE acc.is_deleted = FALSE AND lc.level < 10
              )
              SELECT MAX(level) FROM level_calc
            )
          END as hierarchy_level
         FROM accounts a
         LEFT JOIN accounts parent ON a.parent_account_id = parent.id
         WHERE a.is_deleted = FALSE
         ORDER BY a.name`,
        []
      );

      return result.rows;
    } catch (error) {
      throw new Error(`Failed to get hierarchy summary: ${error.message}`);
    }
  }

  /**
   * Flatten a hierarchy tree into a breadth-first ordered array
   * @param {Object} tree - The hierarchy tree
   * @returns {Array} Flattened array of accounts
   */
  static flattenHierarchyTree(tree) {
    const result = [];
    const queue = [{ node: tree, level: 0 }];

    while (queue.length > 0) {
      const { node, level } = queue.shift();
      
      result.push({
        ...node,
        level,
        children: undefined // Remove children from flattened result
      });

      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
          queue.push({ node: child, level: level + 1 });
        });
      }
    }

    return result;
  }
}

module.exports = AccountHierarchyService;