const PDFDocument = require('pdfkit');
const fs = require('fs').promises;
const path = require('path');

/**
 * PDFTemplateRenderer - Advanced PDF generation with template support
 * Handles template-based PDF rendering with custom styling and branding
 */
class PDFTemplateRenderer {
  
  /**
   * Generate PDF from quote using template
   * @param {Object} quote - Quote data
   * @param {Object} template - Quote template configuration
   * @param {Object} options - Rendering options
   * @returns {Promise<Buffer>} PDF buffer
   */
  static async generateQuotePDF(quote, template = null, options = {}) {
    try {
      // Use default template if none provided
      if (!template) {
        template = this.getDefaultTemplate();
      }
      
      const templateData = typeof template.template_data === 'string' 
        ? JSON.parse(template.template_data) 
        : template.template_data;
      
      return new Promise((resolve, reject) => {
        try {
          const doc = new PDFDocument({ 
            margin: 50,
            size: 'A4',
            info: {
              Title: `Quote ${quote.id}`,
              Author: 'VendorMS',
              Subject: `Quote for ${quote.client_name}`,
              Keywords: 'quote, rfq, vendor'
            }
          });
          
          const buffers = [];
          
          doc.on('data', buffers.push.bind(buffers));
          doc.on('end', () => {
            const pdfBuffer = Buffer.concat(buffers);
            resolve(pdfBuffer);
          });
          doc.on('error', reject);
          
          // Render PDF content using template
          this.renderPDFContent(doc, quote, templateData, options);
          
          doc.end();
        } catch (error) {
          reject(error);
        }
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw error;
    }
  }
  
  /**
   * Render PDF content based on template configuration
   * @param {PDFDocument} doc - PDF document instance
   * @param {Object} quote - Quote data
   * @param {Object} templateData - Template configuration
   * @param {Object} options - Rendering options
   */
  static renderPDFContent(doc, quote, templateData, options = {}) {
    const layout = templateData.layout || {};
    const styling = templateData.styling || {};
    const content = templateData.content || {};
    
    let currentY = 50;
    
    // Apply global styling
    const primaryColor = styling.primaryColor || '#2563eb';
    const secondaryColor = styling.secondaryColor || '#64748b';
    const fontSize = parseInt(styling.fontSize?.replace('px', '')) || 12;
    
    // Render header
    if (layout.header?.showLogo !== false) {
      currentY = this.renderHeader(doc, quote, layout.header, styling, currentY);
    }
    
    // Render quote information
    currentY = this.renderQuoteInfo(doc, quote, styling, currentY);
    
    // Render client information
    currentY = this.renderClientInfo(doc, quote, styling, currentY);
    
    // Render line items
    if (layout.body?.showItemDetails !== false) {
      currentY = this.renderLineItems(doc, quote, layout.body, styling, currentY);
    }
    
    // Render totals
    currentY = this.renderTotals(doc, quote, styling, currentY);
    
    // Render footer
    if (layout.footer?.showTerms !== false || layout.footer?.showContactInfo !== false) {
      this.renderFooter(doc, quote, layout.footer, styling, content, currentY);
    }
  }
  
  /**
   * Render PDF header section
   * @param {PDFDocument} doc - PDF document
   * @param {Object} quote - Quote data
   * @param {Object} headerConfig - Header configuration
   * @param {Object} styling - Styling configuration
   * @param {number} startY - Starting Y position
   * @returns {number} Next Y position
   */
  static renderHeader(doc, quote, headerConfig = {}, styling = {}, startY = 50) {
    const primaryColor = styling.primaryColor || '#2563eb';
    const backgroundColor = headerConfig.backgroundColor || '#ffffff';
    const textColor = headerConfig.textColor || '#333333';
    
    let currentY = startY;
    
    // Header background (if not white)
    if (backgroundColor !== '#ffffff') {
      doc.rect(50, currentY, 500, 80)
         .fill(backgroundColor);
    }
    
    // Company logo placeholder (if enabled)
    if (headerConfig.showLogo !== false) {
      doc.rect(60, currentY + 10, 60, 40)
         .stroke(primaryColor);
      doc.fontSize(8)
         .fillColor(primaryColor)
         .text('LOGO', 85, currentY + 27);
    }
    
    // Company information (if enabled)
    if (headerConfig.showCompanyInfo !== false) {
      doc.fontSize(20)
         .fillColor(textColor)
         .text('QUOTE', 400, currentY + 15);
      
      doc.fontSize(10)
         .text('VendorMS Platform', 400, currentY + 40)
         .text('Quote Management System', 400, currentY + 55);
    }
    
    return currentY + 100;
  }
  
  /**
   * Render quote information section
   * @param {PDFDocument} doc - PDF document
   * @param {Object} quote - Quote data
   * @param {Object} styling - Styling configuration
   * @param {number} startY - Starting Y position
   * @returns {number} Next Y position
   */
  static renderQuoteInfo(doc, quote, styling = {}, startY = 150) {
    const primaryColor = styling.primaryColor || '#2563eb';
    const secondaryColor = styling.secondaryColor || '#64748b';
    
    let currentY = startY;
    
    // Quote details section
    doc.fontSize(14)
       .fillColor(primaryColor)
       .text('Quote Information', 50, currentY);
    
    currentY += 25;
    
    // Quote details in two columns
    const leftColumn = 50;
    const rightColumn = 300;
    
    doc.fontSize(10)
       .fillColor(secondaryColor);
    
    // Left column
    doc.text('Quote Number:', leftColumn, currentY)
       .fillColor('#333333')
       .text(`#${quote.id}`, leftColumn + 80, currentY);
    
    doc.fillColor(secondaryColor)
       .text('Quote Date:', leftColumn, currentY + 15)
       .fillColor('#333333')
       .text(new Date(quote.created_at).toLocaleDateString(), leftColumn + 80, currentY + 15);
    
    doc.fillColor(secondaryColor)
       .text('RFQ Reference:', leftColumn, currentY + 30)
       .fillColor('#333333')
       .text(quote.rfq_id ? `RFQ #${quote.rfq_id}` : 'N/A', leftColumn + 80, currentY + 30);
    
    // Right column
    doc.fillColor(secondaryColor)
       .text('Valid Until:', rightColumn, currentY)
       .fillColor('#333333')
       .text(new Date(quote.expires_at || Date.now() + 30*24*60*60*1000).toLocaleDateString(), rightColumn + 80, currentY);
    
    doc.fillColor(secondaryColor)
       .text('Status:', rightColumn, currentY + 15)
       .fillColor('#333333')
       .text(quote.status?.toUpperCase() || 'DRAFT', rightColumn + 80, currentY + 15);
    
    doc.fillColor(secondaryColor)
       .text('Currency:', rightColumn, currentY + 30)
       .fillColor('#333333')
       .text(quote.currency || 'USD', rightColumn + 80, currentY + 30);
    
    return currentY + 60;
  }
  
  /**
   * Render client information section
   * @param {PDFDocument} doc - PDF document
   * @param {Object} quote - Quote data
   * @param {Object} styling - Styling configuration
   * @param {number} startY - Starting Y position
   * @returns {number} Next Y position
   */
  static renderClientInfo(doc, quote, styling = {}, startY = 250) {
    const primaryColor = styling.primaryColor || '#2563eb';
    const secondaryColor = styling.secondaryColor || '#64748b';
    
    let currentY = startY;
    
    // Client information section
    doc.fontSize(14)
       .fillColor(primaryColor)
       .text('Bill To:', 50, currentY);
    
    currentY += 25;
    
    doc.fontSize(12)
       .fillColor('#333333')
       .text(quote.client_name || 'Client Name', 50, currentY);
    
    if (quote.client_email) {
      currentY += 18;
      doc.text(quote.client_email, 50, currentY);
    }
    
    return currentY + 40;
  }
  
  /**
   * Render line items section
   * @param {PDFDocument} doc - PDF document
   * @param {Object} quote - Quote data
   * @param {Object} bodyConfig - Body configuration
   * @param {Object} styling - Styling configuration
   * @param {number} startY - Starting Y position
   * @returns {number} Next Y position
   */
  static renderLineItems(doc, quote, bodyConfig = {}, styling = {}, startY = 350) {
    const primaryColor = styling.primaryColor || '#2563eb';
    const secondaryColor = styling.secondaryColor || '#64748b';
    
    let currentY = startY;
    
    // Items section header
    doc.fontSize(14)
       .fillColor(primaryColor)
       .text('Quote Items', 50, currentY);
    
    currentY += 30;
    
    // Table headers
    const tableTop = currentY;
    const itemX = 50;
    const descriptionX = 150;
    const qtyX = 350;
    const priceX = 400;
    const totalX = 480;
    
    // Header background
    doc.rect(50, currentY - 5, 500, 20)
       .fill('#f8fafc');
    
    // Header text
    doc.fontSize(10)
       .fillColor(secondaryColor)
       .text('Item', itemX, currentY)
       .text('Description', descriptionX, currentY);
    
    if (bodyConfig.showQuantities !== false) {
      doc.text('Qty', qtyX, currentY);
    }
    
    if (bodyConfig.showUnitPrices !== false) {
      doc.text('Unit Price', priceX, currentY);
    }
    
    doc.text('Total', totalX, currentY);
    
    currentY += 25;
    
    // Render items
    const selectedBids = quote.selected_bids || quote.item_selections || {};
    let itemIndex = 1;
    
    for (const [itemId, bid] of Object.entries(selectedBids)) {
      // Check if we need a new page
      if (currentY > 700) {
        doc.addPage();
        currentY = 50;
      }
      
      const itemName = bid.itemName || bid.name || `Item ${itemIndex}`;
      const description = bid.description || bid.specifications || '';
      const quantity = bid.quantity || 1;
      const unitPrice = bid.unitPrice || bid.unit_price || 0;
      const totalPrice = bid.totalPrice || bid.total_price || (unitPrice * quantity);
      
      // Alternate row background
      if (itemIndex % 2 === 0) {
        doc.rect(50, currentY - 3, 500, 18)
           .fill('#fafafa');
      }
      
      doc.fontSize(9)
         .fillColor('#333333')
         .text(itemName, itemX, currentY, { width: 90, ellipsis: true })
         .text(description, descriptionX, currentY, { width: 190, ellipsis: true });
      
      if (bodyConfig.showQuantities !== false) {
        doc.text(quantity.toString(), qtyX, currentY);
      }
      
      if (bodyConfig.showUnitPrices !== false) {
        doc.text(`$${unitPrice.toFixed(2)}`, priceX, currentY);
      }
      
      doc.text(`$${totalPrice.toFixed(2)}`, totalX, currentY);
      
      // Show vendor attribution if enabled
      if (bodyConfig.showVendorAttribution && bid.vendorName) {
        currentY += 12;
        doc.fontSize(8)
           .fillColor(secondaryColor)
           .text(`Vendor: ${bid.vendorName}`, descriptionX, currentY);
      }
      
      currentY += 20;
      itemIndex++;
    }
    
    // If no items, show placeholder
    if (Object.keys(selectedBids).length === 0) {
      doc.fontSize(10)
         .fillColor(secondaryColor)
         .text('No items selected', itemX, currentY);
      currentY += 20;
    }
    
    return currentY + 20;
  }
  
  /**
   * Render totals section
   * @param {PDFDocument} doc - PDF document
   * @param {Object} quote - Quote data
   * @param {Object} styling - Styling configuration
   * @param {number} startY - Starting Y position
   * @returns {number} Next Y position
   */
  static renderTotals(doc, quote, styling = {}, startY = 500) {
    const primaryColor = styling.primaryColor || '#2563eb';
    const secondaryColor = styling.secondaryColor || '#64748b';
    
    let currentY = startY;
    
    // Totals section (right-aligned)
    const totalsX = 350;
    const valuesX = 480;
    
    doc.fontSize(10)
       .fillColor(secondaryColor);
    
    // Subtotal
    const subtotal = this.calculateSubtotal(quote);
    doc.text('Subtotal:', totalsX, currentY)
       .fillColor('#333333')
       .text(`$${subtotal.toFixed(2)}`, valuesX, currentY);
    
    currentY += 15;
    
    // Margin (if applicable)
    if (quote.margin_percentage && quote.margin_percentage > 0) {
      const marginAmount = subtotal * (quote.margin_percentage / 100);
      doc.fillColor(secondaryColor)
         .text(`Margin (${quote.margin_percentage}%):`, totalsX, currentY)
         .fillColor('#333333')
         .text(`$${marginAmount.toFixed(2)}`, valuesX, currentY);
      currentY += 15;
    }
    
    // Taxes (if applicable)
    if (quote.taxes && quote.taxes > 0) {
      doc.fillColor(secondaryColor)
         .text('Taxes:', totalsX, currentY)
         .fillColor('#333333')
         .text(`$${quote.taxes.toFixed(2)}`, valuesX, currentY);
      currentY += 15;
    }
    
    // Total line
    doc.moveTo(totalsX, currentY + 5)
       .lineTo(valuesX + 70, currentY + 5)
       .stroke(primaryColor);
    
    currentY += 15;
    
    // Final total
    doc.fontSize(12)
       .fillColor(primaryColor)
       .text('Total:', totalsX, currentY)
       .text(`$${(quote.total_amount || subtotal).toFixed(2)}`, valuesX, currentY);
    
    return currentY + 30;
  }
  
  /**
   * Render footer section
   * @param {PDFDocument} doc - PDF document
   * @param {Object} quote - Quote data
   * @param {Object} footerConfig - Footer configuration
   * @param {Object} styling - Styling configuration
   * @param {Object} content - Content configuration
   * @param {number} startY - Starting Y position
   */
  static renderFooter(doc, quote, footerConfig = {}, styling = {}, content = {}, startY = 600) {
    const primaryColor = styling.primaryColor || '#2563eb';
    const secondaryColor = styling.secondaryColor || '#64748b';
    
    let currentY = Math.max(startY, 650); // Ensure footer is near bottom
    
    // Terms and conditions
    if (footerConfig.showTerms !== false) {
      doc.fontSize(12)
         .fillColor(primaryColor)
         .text('Terms & Conditions', 50, currentY);
      
      currentY += 20;
      
      const terms = quote.terms || content.defaultTerms || 
        'Payment terms: Net 30 days. Prices valid for 30 days from quote date.';
      
      doc.fontSize(9)
         .fillColor('#333333')
         .text(terms, 50, currentY, { width: 500 });
      
      currentY += 40;
    }
    
    // Notes
    if (quote.notes || content.defaultNotes) {
      doc.fontSize(12)
         .fillColor(primaryColor)
         .text('Notes', 50, currentY);
      
      currentY += 20;
      
      const notes = quote.notes || content.defaultNotes || '';
      
      doc.fontSize(9)
         .fillColor('#333333')
         .text(notes, 50, currentY, { width: 500 });
      
      currentY += 30;
    }
    
    // Contact information
    if (footerConfig.showContactInfo !== false) {
      doc.fontSize(8)
         .fillColor(secondaryColor)
         .text('For questions about this quote, please contact <NAME_EMAIL>', 50, currentY + 20);
    }
    
    // Signature line
    if (footerConfig.showSignature !== false) {
      doc.moveTo(350, currentY + 50)
         .lineTo(500, currentY + 50)
         .stroke(secondaryColor);
      
      doc.fontSize(8)
         .fillColor(secondaryColor)
         .text('Authorized Signature', 350, currentY + 60);
    }
  }
  
  /**
   * Calculate subtotal from quote items
   * @param {Object} quote - Quote data
   * @returns {number} Subtotal amount
   */
  static calculateSubtotal(quote) {
    const selectedBids = quote.selected_bids || quote.item_selections || {};
    let subtotal = 0;
    
    for (const [itemId, bid] of Object.entries(selectedBids)) {
      const totalPrice = bid.totalPrice || bid.total_price || 
        ((bid.unitPrice || bid.unit_price || 0) * (bid.quantity || 1));
      subtotal += totalPrice;
    }
    
    return subtotal;
  }
  
  /**
   * Get default template configuration
   * @returns {Object} Default template
   */
  static getDefaultTemplate() {
    return {
      name: 'Default Template',
      template_data: {
        layout: {
          header: {
            showLogo: true,
            showCompanyInfo: true,
            backgroundColor: '#ffffff',
            textColor: '#333333'
          },
          body: {
            showItemDetails: true,
            showVendorAttribution: false,
            groupByCategory: true,
            showUnitPrices: true,
            showQuantities: true
          },
          footer: {
            showTerms: true,
            showSignature: true,
            showContactInfo: true
          }
        },
        styling: {
          primaryColor: '#2563eb',
          secondaryColor: '#64748b',
          fontFamily: 'Helvetica',
          fontSize: '12px'
        },
        content: {
          defaultTerms: 'Payment terms: Net 30 days. Prices valid for 30 days from quote date.',
          defaultNotes: 'Thank you for your business. Please contact us with any questions.'
        }
      }
    };
  }
}

module.exports = PDFTemplateRenderer;