const Opportunity = require('../models/Opportunity');
const { query } = require('../config/database');

class OpportunityService {
  /**
   * Create a new opportunity with validation
   * @param {Object} opportunityData - Opportunity data
   * @param {number} userId - User performing the operation
   * @returns {Object} Created opportunity
   */
  static async createOpportunity(opportunityData, userId = 1) {
    try {
      // Validate required fields
      const validationErrors = Opportunity.validateOpportunityData(opportunityData);
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.map(e => e.message).join(', ')}`);
      }

      // Validate account exists
      const accountCheck = await query(
        'SELECT id, name FROM accounts WHERE id = $1 AND is_deleted = FALSE',
        [opportunityData.account_id]
      );
      if (accountCheck.rows.length === 0) {
        throw new Error('Account not found or has been deleted');
      }

      // Validate owner exists if provided
      if (opportunityData.owner_id) {
        const ownerCheck = await query(
          'SELECT id, email FROM users WHERE id = $1',
          [opportunityData.owner_id]
        );
        if (ownerCheck.rows.length === 0) {
          throw new Error('Owner not found');
        }
      }

      // Validate contacts exist if provided
      if (opportunityData.contacts && opportunityData.contacts.length > 0) {
        for (const contact of opportunityData.contacts) {
          const contactCheck = await query(
            'SELECT id FROM contacts WHERE id = $1 AND account_id = $2 AND is_deleted = FALSE',
            [contact.contact_id, opportunityData.account_id]
          );
          if (contactCheck.rows.length === 0) {
            throw new Error(`Contact ${contact.contact_id} not found or not associated with the account`);
          }
        }
      }

      // Check for duplicate integration_id if provided
      if (opportunityData.integration_id) {
        const integrationCheck = await query(
          'SELECT id FROM opportunities WHERE integration_id = $1 AND is_deleted = FALSE',
          [opportunityData.integration_id]
        );
        if (integrationCheck.rows.length > 0) {
          throw new Error('Integration ID already exists');
        }
      }

      // Set default owner to current user if not provided
      if (!opportunityData.owner_id) {
        opportunityData.owner_id = userId;
      }

      // Create the opportunity
      const opportunity = await Opportunity.create(opportunityData, userId);
      return opportunity;
    } catch (error) {
      throw new Error(`Failed to create opportunity: ${error.message}`);
    }
  }

  /**
   * Get opportunity by ID with full details
   * @param {number} opportunityId - Opportunity ID
   * @returns {Object} Opportunity details
   */
  static async getOpportunityById(opportunityId) {
    try {
      const opportunity = await Opportunity.findById(opportunityId);
      if (!opportunity) {
        throw new Error('Opportunity not found');
      }

      return opportunity;
    } catch (error) {
      throw new Error(`Failed to get opportunity: ${error.message}`);
    }
  }

  /**
   * Update opportunity with validation
   * @param {number} opportunityId - Opportunity ID
   * @param {Object} updateData - Data to update
   * @param {number} userId - User performing the operation
   * @returns {Object} Updated opportunity
   */
  static async updateOpportunity(opportunityId, updateData, userId = 1) {
    try {
      // Get existing opportunity
      const existingOpportunity = await Opportunity.findById(opportunityId);
      if (!existingOpportunity) {
        throw new Error('Opportunity not found');
      }

      // Validate update data
      const mergedData = { ...existingOpportunity, ...updateData };
      const validationErrors = Opportunity.validateOpportunityData(mergedData);
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.map(e => e.message).join(', ')}`);
      }

      // Validate account if being updated
      if (updateData.account_id && updateData.account_id !== existingOpportunity.account_id) {
        const accountCheck = await query(
          'SELECT id, name FROM accounts WHERE id = $1 AND is_deleted = FALSE',
          [updateData.account_id]
        );
        if (accountCheck.rows.length === 0) {
          throw new Error('Account not found or has been deleted');
        }
      }

      // Validate owner if being updated
      if (updateData.owner_id && updateData.owner_id !== existingOpportunity.owner_id) {
        const ownerCheck = await query(
          'SELECT id, email FROM users WHERE id = $1',
          [updateData.owner_id]
        );
        if (ownerCheck.rows.length === 0) {
          throw new Error('Owner not found');
        }
      }

      // Validate contacts if being updated
      if (updateData.contacts) {
        const accountId = updateData.account_id || existingOpportunity.account_id;
        for (const contact of updateData.contacts) {
          const contactCheck = await query(
            'SELECT id FROM contacts WHERE id = $1 AND account_id = $2 AND is_deleted = FALSE',
            [contact.contact_id, accountId]
          );
          if (contactCheck.rows.length === 0) {
            throw new Error(`Contact ${contact.contact_id} not found or not associated with the account`);
          }
        }
      }

      // Check for duplicate integration_id if being updated
      if (updateData.integration_id && updateData.integration_id !== existingOpportunity.integration_id) {
        const integrationCheck = await query(
          'SELECT id FROM opportunities WHERE integration_id = $1 AND id != $2 AND is_deleted = FALSE',
          [updateData.integration_id, opportunityId]
        );
        if (integrationCheck.rows.length > 0) {
          throw new Error('Integration ID already exists');
        }
      }

      // Validate stage transitions (business rules)
      if (updateData.stage_name && updateData.stage_name !== existingOpportunity.stage_name) {
        const stageValidation = this.validateStageTransition(
          existingOpportunity.stage_name,
          updateData.stage_name
        );
        if (!stageValidation.isValid) {
          throw new Error(`Invalid stage transition: ${stageValidation.error}`);
        }
      }

      // Update the opportunity
      const updatedOpportunity = await Opportunity.update(opportunityId, updateData, userId);
      return updatedOpportunity;
    } catch (error) {
      throw new Error(`Failed to update opportunity: ${error.message}`);
    }
  }

  /**
   * Soft delete an opportunity
   * @param {number} opportunityId - Opportunity ID
   * @param {number} userId - User performing the operation
   * @returns {boolean} Success status
   */
  static async deleteOpportunity(opportunityId, userId = 1) {
    try {
      const opportunity = await Opportunity.findById(opportunityId);
      if (!opportunity) {
        throw new Error('Opportunity not found');
      }

      // Check if opportunity can be deleted (business rules)
      if (opportunity.is_closed && opportunity.is_won) {
        throw new Error('Cannot delete a won opportunity');
      }

      const success = await Opportunity.delete(opportunityId, userId);
      if (!success) {
        throw new Error('Failed to delete opportunity');
      }

      return true;
    } catch (error) {
      throw new Error(`Failed to delete opportunity: ${error.message}`);
    }
  }

  /**
   * Search opportunities with advanced filters
   * @param {Object} searchParams - Search parameters
   * @returns {Array} Matching opportunities
   */
  static async searchOpportunities(searchParams) {
    try {
      const opportunities = await Opportunity.search(searchParams);
      return opportunities;
    } catch (error) {
      throw new Error(`Failed to search opportunities: ${error.message}`);
    }
  }

  /**
   * Get opportunities with pagination and filters
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Records per page
   * @returns {Object} Paginated opportunities
   */
  static async getOpportunities(filters = {}, page = 1, limit = 10) {
    try {
      const result = await Opportunity.findAll(filters, page, limit);
      return result;
    } catch (error) {
      throw new Error(`Failed to get opportunities: ${error.message}`);
    }
  }

  /**
   * Get pipeline data for kanban view
   * @param {Object} filters - Filter criteria
   * @returns {Object} Pipeline data grouped by stage
   */
  static async getPipelineData(filters = {}) {
    try {
      const pipelineData = await Opportunity.getPipelineData(filters);
      return pipelineData;
    } catch (error) {
      throw new Error(`Failed to get pipeline data: ${error.message}`);
    }
  }

  /**
   * Advance opportunity to next stage
   * @param {number} opportunityId - Opportunity ID
   * @param {string} newStage - New stage name
   * @param {number} userId - User performing the operation
   * @returns {Object} Updated opportunity
   */
  static async advanceStage(opportunityId, newStage, userId = 1) {
    try {
      const opportunity = await Opportunity.findById(opportunityId);
      if (!opportunity) {
        throw new Error('Opportunity not found');
      }

      // Validate stage transition
      const stageValidation = this.validateStageTransition(opportunity.stage_name, newStage);
      if (!stageValidation.isValid) {
        throw new Error(`Invalid stage transition: ${stageValidation.error}`);
      }

      // Get stage metadata for probability calculation
      const stages = Opportunity.getStages();
      const stageInfo = stages.find(s => s.name === newStage);
      
      const updateData = {
        stage_name: newStage,
        probability: stageInfo ? stageInfo.probability : opportunity.probability
      };

      // Auto-set forecast category based on stage
      if (newStage === 'Closed Won' || newStage === 'Closed Lost') {
        updateData.forecast_category = 'Closed';
      } else if (newStage === 'Negotiation/Review') {
        updateData.forecast_category = 'Commit';
      } else if (newStage === 'Proposal/Price Quote') {
        updateData.forecast_category = 'Best Case';
      }

      const updatedOpportunity = await Opportunity.update(opportunityId, updateData, userId);
      return updatedOpportunity;
    } catch (error) {
      throw new Error(`Failed to advance stage: ${error.message}`);
    }
  }

  /**
   * Calculate opportunity probability based on stage
   * @param {string} stageName - Stage name
   * @returns {number} Probability percentage
   */
  static calculateProbabilityByStage(stageName) {
    const stages = Opportunity.getStages();
    const stage = stages.find(s => s.name === stageName);
    return stage ? stage.probability : 0;
  }

  /**
   * Validate stage transition
   * @param {string} currentStage - Current stage
   * @param {string} newStage - New stage
   * @returns {Object} Validation result
   */
  static validateStageTransition(currentStage, newStage) {
    const stages = Opportunity.getStages();
    const currentStageInfo = stages.find(s => s.name === currentStage);
    const newStageInfo = stages.find(s => s.name === newStage);

    if (!currentStageInfo) {
      return { isValid: false, error: 'Invalid current stage' };
    }

    if (!newStageInfo) {
      return { isValid: false, error: 'Invalid new stage' };
    }

    // Allow moving to any stage for now (can add business rules later)
    // Business rules could include:
    // - Can't move backwards unless admin
    // - Can't skip stages
    // - Certain stages require specific conditions
    
    return { isValid: true };
  }

  /**
   * Get opportunity analytics
   * @param {Object} filters - Filter criteria
   * @returns {Object} Analytics data
   */
  static async getAnalytics(filters = {}) {
    try {
      const analytics = await Opportunity.getAnalytics(filters);
      return analytics;
    } catch (error) {
      throw new Error(`Failed to get analytics: ${error.message}`);
    }
  }

  /**
   * Export opportunities to CSV
   * @param {Object} filters - Filter criteria
   * @returns {Array} CSV data
   */
  static async exportOpportunities(filters = {}) {
    try {
      const csvData = await Opportunity.exportToCSV(filters);
      return csvData;
    } catch (error) {
      throw new Error(`Failed to export opportunities: ${error.message}`);
    }
  }

  /**
   * Get opportunities by account
   * @param {number} accountId - Account ID
   * @param {Object} options - Additional options
   * @returns {Array} Account opportunities
   */
  static async getAccountOpportunities(accountId, options = {}) {
    try {
      const filters = { account_id: accountId, ...options };
      const result = await Opportunity.findAll(filters, 1, 100);
      return result.opportunities;
    } catch (error) {
      throw new Error(`Failed to get account opportunities: ${error.message}`);
    }
  }

  /**
   * Get opportunities by owner
   * @param {number} ownerId - Owner ID
   * @param {Object} options - Additional options
   * @returns {Array} Owner opportunities
   */
  static async getOwnerOpportunities(ownerId, options = {}) {
    try {
      const filters = { owner_id: ownerId, ...options };
      const result = await Opportunity.findAll(filters, 1, 100);
      return result.opportunities;
    } catch (error) {
      throw new Error(`Failed to get owner opportunities: ${error.message}`);
    }
  }

  /**
   * Bulk update opportunities
   * @param {Array} opportunityUpdates - Array of {id, updateData}
   * @param {number} userId - User performing the operation
   * @returns {Array} Updated opportunities
   */
  static async bulkUpdateOpportunities(opportunityUpdates, userId = 1) {
    try {
      const results = [];
      
      for (const update of opportunityUpdates) {
        try {
          const updatedOpportunity = await this.updateOpportunity(
            update.id,
            update.updateData,
            userId
          );
          results.push({ success: true, opportunity: updatedOpportunity });
        } catch (error) {
          results.push({ success: false, id: update.id, error: error.message });
        }
      }
      
      return results;
    } catch (error) {
      throw new Error(`Failed to bulk update opportunities: ${error.message}`);
    }
  }

  /**
   * Get opportunity statistics
   * @param {Object} filters - Filter criteria
   * @returns {Object} Statistics
   */
  static async getOpportunityStatistics(filters = {}) {
    try {
      const analytics = await Opportunity.getAnalytics(filters);
      const pipelineData = await Opportunity.getPipelineData(filters);
      
      return {
        ...analytics,
        pipeline_breakdown: pipelineData,
        stages_summary: pipelineData.map(stage => ({
          stage: stage.stage_name,
          count: stage.opportunity_count,
          total_amount: stage.total_amount,
          weighted_amount: stage.weighted_amount
        }))
      };
    } catch (error) {
      throw new Error(`Failed to get opportunity statistics: ${error.message}`);
    }
  }

  /**
   * Validate opportunity data
   * @param {Object} opportunityData - Data to validate
   * @returns {Array} Validation errors
   */
  static validateOpportunityData(opportunityData) {
    return Opportunity.validateOpportunityData(opportunityData);
  }

  /**
   * Get available stages
   * @returns {Array} Available stages
   */
  static getStages() {
    return Opportunity.getStages();
  }

  /**
   * Get available types
   * @returns {Array} Available types
   */
  static getTypes() {
    return Opportunity.getTypes();
  }

  /**
   * Get available lead sources
   * @returns {Array} Available lead sources
   */
  static getLeadSources() {
    return Opportunity.getLeadSources();
  }

  /**
   * Get available forecast categories
   * @returns {Array} Available forecast categories
   */
  static getForecastCategories() {
    return Opportunity.getForecastCategories();
  }
}

module.exports = OpportunityService;