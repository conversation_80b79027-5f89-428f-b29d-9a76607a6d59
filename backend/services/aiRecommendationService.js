const { query } = require('../config/database');

class AIRecommendationService {
  constructor() {
    // Default AI settings
    this.defaultWeights = {
      priceWeight: 0.4,
      performanceWeight: 0.3,
      deliveryWeight: 0.2,
      riskWeight: 0.1,
      diversificationPreference: 0.5
    };
  }

  /**
   * Analyze submissions and generate AI recommendations
   * @param {Array} submissions - Array of RFQ submissions
   * @param {Object} rfq - RFQ object with settings
   * @returns {Array} Array of AI recommendations
   */
  async analyzeSubmissions(submissions, rfq) {
    try {
      console.log('Analyzing submissions for RFQ:', rfq.id);
      console.log('Number of submissions:', submissions.length);

      if (submissions.length === 0) {
        return [];
      }

      // Get AI settings from RFQ or use defaults
      const aiSettings = rfq.ai_settings || this.defaultWeights;
      const weights = { ...this.defaultWeights, ...aiSettings };

      // Get vendor performance data
      const vendorIds = submissions.map(s => s.vendor_id);
      const vendorPerformance = await this.getVendorPerformanceData(vendorIds);

      // Get RFQ items for analysis
      const rfqItems = rfq.items || [];
      const recommendations = [];

      // Generate recommendations for each item
      for (const item of rfqItems) {
        const itemRecommendation = await this.generateItemRecommendation(
          item,
          submissions,
          vendorPerformance,
          weights
        );
        
        if (itemRecommendation) {
          recommendations.push(itemRecommendation);
        }
      }

      console.log('Generated recommendations:', recommendations.length);
      return recommendations;
    } catch (error) {
      console.error('Error in analyzeSubmissions:', error);
      throw error;
    }
  }

  /**
   * Generate recommendation for a specific item
   * @param {Object} item - RFQ item
   * @param {Array} submissions - All submissions
   * @param {Object} vendorPerformance - Vendor performance data
   * @param {Object} weights - AI weights
   * @returns {Object} Item recommendation
   */
  async generateItemRecommendation(item, submissions, vendorPerformance, weights) {
    try {
      // Find submissions that have bids for this item
      const itemBids = [];
      
      for (const submission of submissions) {
        const bidItem = submission.bid_items?.find(bi => bi.rfq_item_id === item.id);
        if (bidItem) {
          itemBids.push({
            ...bidItem,
            submission_id: submission.id,
            vendor_id: submission.vendor_id,
            vendor_name: submission.vendor_name,
            vendor_performance: vendorPerformance[submission.vendor_id] || {},
            delivery_days: bidItem.delivery_days || submission.delivery_days || 30
          });
        }
      }

      if (itemBids.length === 0) {
        return null;
      }

      // Calculate scores for each bid
      const scoredBids = itemBids.map(bid => ({
        ...bid,
        scores: this.calculateBidScores(bid, itemBids, vendorPerformance, weights),
        overall_score: 0
      }));

      // Calculate overall scores
      scoredBids.forEach(bid => {
        bid.overall_score = this.calculateOverallScore(bid.scores, weights);
      });

      // Sort by overall score (highest first)
      scoredBids.sort((a, b) => b.overall_score - a.overall_score);

      // Get the best recommendation
      const bestBid = scoredBids[0];
      const confidence = this.calculateConfidence(bestBid, scoredBids);

      // Calculate cost savings compared to highest bid
      const highestPrice = Math.max(...itemBids.map(b => b.total_price));
      const costSavings = highestPrice - bestBid.total_price;

      // Generate rationale
      const rationale = this.generateRationale(bestBid, scoredBids, weights);

      return {
        id: `rec_${item.id}_${bestBid.vendor_id}`,
        rfq_id: null, // Will be set by caller
        item_id: item.id,
        recommended_vendor_id: bestBid.vendor_id,
        recommended_submission_id: bestBid.submission_id,
        confidence: confidence,
        factors: this.formatFactors(bestBid.scores, weights),
        rationale: rationale,
        cost_savings: Math.max(0, costSavings),
        risk_score: bestBid.scores.risk,
        model_version: '1.0.0',
        created_at: new Date()
      };
    } catch (error) {
      console.error('Error generating item recommendation:', error);
      throw error;
    }
  }

  /**
   * Calculate individual scores for a bid
   * @param {Object} bid - Bid to score
   * @param {Array} allBids - All bids for comparison
   * @param {Object} vendorPerformance - Vendor performance data
   * @param {Object} weights - AI weights
   * @returns {Object} Individual scores
   */
  calculateBidScores(bid, allBids, vendorPerformance, weights) {
    // Price score (lower price = higher score)
    const prices = allBids.map(b => b.total_price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;
    const priceScore = priceRange > 0 ? 1 - ((bid.total_price - minPrice) / priceRange) : 1;

    // Performance score (based on vendor performance)
    const vendorPerf = vendorPerformance[bid.vendor_id] || {};
    const performanceScore = (vendorPerf.performance_score || 50) / 100;

    // Delivery score (faster delivery = higher score)
    const deliveryTimes = allBids.map(b => b.delivery_days);
    const minDelivery = Math.min(...deliveryTimes);
    const maxDelivery = Math.max(...deliveryTimes);
    const deliveryRange = maxDelivery - minDelivery;
    const deliveryScore = deliveryRange > 0 ? 1 - ((bid.delivery_days - minDelivery) / deliveryRange) : 1;

    // Risk score (lower risk = higher score)
    const riskScore = this.calculateVendorRiskScore(bid.vendor_id, vendorPerf);

    return {
      price: Math.max(0, Math.min(1, priceScore)),
      performance: Math.max(0, Math.min(1, performanceScore)),
      delivery: Math.max(0, Math.min(1, deliveryScore)),
      risk: Math.max(0, Math.min(1, riskScore))
    };
  }

  /**
   * Calculate overall score using weighted factors
   * @param {Object} scores - Individual scores
   * @param {Object} weights - AI weights
   * @returns {number} Overall score (0-1)
   */
  calculateOverallScore(scores, weights) {
    return (
      scores.price * weights.priceWeight +
      scores.performance * weights.performanceWeight +
      scores.delivery * weights.deliveryWeight +
      scores.risk * weights.riskWeight
    );
  }

  /**
   * Calculate confidence score for recommendation
   * @param {Object} bestBid - Best bid
   * @param {Array} allBids - All scored bids
   * @returns {number} Confidence score (0-1)
   */
  calculateConfidence(bestBid, allBids) {
    if (allBids.length === 1) {
      return 0.5; // Low confidence with only one option
    }

    // Calculate score gap between best and second best
    const sortedScores = allBids.map(b => b.overall_score).sort((a, b) => b - a);
    const scoreGap = sortedScores[0] - (sortedScores[1] || 0);
    
    // Higher gap = higher confidence
    const baseConfidence = Math.min(0.95, 0.5 + scoreGap);
    
    // Adjust based on number of alternatives
    const alternativesFactor = Math.min(1, allBids.length / 5);
    
    return Math.max(0.1, Math.min(0.95, baseConfidence * alternativesFactor));
  }

  /**
   * Calculate vendor risk score
   * @param {number} vendorId - Vendor ID
   * @param {Object} vendorPerf - Vendor performance data
   * @returns {number} Risk score (0-1, higher = less risky)
   */
  calculateVendorRiskScore(vendorId, vendorPerf) {
    // Base risk score from performance
    const performanceRisk = (vendorPerf.performance_score || 50) / 100;
    
    // Adjust based on vendor history
    const contractCount = vendorPerf.total_contracts || 0;
    const experienceFactor = Math.min(1, contractCount / 10); // More contracts = less risk
    
    // Combine factors
    return (performanceRisk * 0.7) + (experienceFactor * 0.3);
  }

  /**
   * Generate human-readable rationale
   * @param {Object} bestBid - Best bid
   * @param {Array} allBids - All bids
   * @param {Object} weights - AI weights
   * @returns {string} Rationale text
   */
  generateRationale(bestBid, allBids, weights) {
    const reasons = [];
    
    // Price analysis
    const prices = allBids.map(b => b.total_price);
    const avgPrice = prices.reduce((sum, p) => sum + p, 0) / prices.length;
    if (bestBid.total_price < avgPrice) {
      const savings = ((avgPrice - bestBid.total_price) / avgPrice * 100).toFixed(1);
      reasons.push(`${savings}% below average price`);
    }
    
    // Performance analysis
    if (bestBid.scores.performance > 0.7) {
      reasons.push('strong vendor performance history');
    }
    
    // Delivery analysis
    const deliveryTimes = allBids.map(b => b.delivery_days);
    const avgDelivery = deliveryTimes.reduce((sum, d) => sum + d, 0) / deliveryTimes.length;
    if (bestBid.delivery_days <= avgDelivery) {
      reasons.push('competitive delivery time');
    }
    
    // Risk analysis
    if (bestBid.scores.risk > 0.6) {
      reasons.push('low risk vendor');
    }
    
    if (reasons.length === 0) {
      return 'Best overall value based on weighted criteria';
    }
    
    return `Recommended due to ${reasons.join(', ')}.`;
  }

  /**
   * Format factors for API response
   * @param {Object} scores - Individual scores
   * @param {Object} weights - AI weights
   * @returns {Array} Formatted factors
   */
  formatFactors(scores, weights) {
    return [
      {
        name: 'price',
        weight: weights.priceWeight,
        score: scores.price,
        description: this.getFactorDescription('price', scores.price)
      },
      {
        name: 'performance',
        weight: weights.performanceWeight,
        score: scores.performance,
        description: this.getFactorDescription('performance', scores.performance)
      },
      {
        name: 'delivery',
        weight: weights.deliveryWeight,
        score: scores.delivery,
        description: this.getFactorDescription('delivery', scores.delivery)
      },
      {
        name: 'risk',
        weight: weights.riskWeight,
        score: scores.risk,
        description: this.getFactorDescription('risk', scores.risk)
      }
    ];
  }

  /**
   * Get description for factor score
   * @param {string} factor - Factor name
   * @param {number} score - Score (0-1)
   * @returns {string} Description
   */
  getFactorDescription(factor, score) {
    const level = score > 0.8 ? 'Excellent' : 
                  score > 0.6 ? 'Good' : 
                  score > 0.4 ? 'Average' : 
                  score > 0.2 ? 'Below Average' : 'Poor';
    
    const descriptions = {
      price: `${level} pricing competitiveness`,
      performance: `${level} vendor performance history`,
      delivery: `${level} delivery time offering`,
      risk: `${level} risk assessment`
    };
    
    return descriptions[factor] || `${level} ${factor} score`;
  }

  /**
   * Get vendor performance data
   * @param {Array} vendorIds - Array of vendor IDs
   * @returns {Object} Vendor performance data
   */
  async getVendorPerformanceData(vendorIds) {
    try {
      if (vendorIds.length === 0) {
        return {};
      }

      const result = await query(`
        SELECT 
          v.id,
          v.performance_score,
          COUNT(DISTINCT c.id) as total_contracts,
          COUNT(DISTINCT CASE WHEN c.status = 'active' THEN c.id END) as active_contracts,
          AVG(k.score) as avg_kpi_score,
          COUNT(DISTINCT ra.id) as risk_assessments_count,
          AVG(ra.risk_score) as avg_risk_score
        FROM vendors v
        LEFT JOIN contracts c ON v.id = c.vendor_id
        LEFT JOIN kpis k ON v.id = k.vendor_id
        LEFT JOIN risk_assessments ra ON v.id = ra.vendor_id
        WHERE v.id = ANY($1)
        GROUP BY v.id, v.performance_score
      `, [vendorIds]);

      const performanceData = {};
      result.rows.forEach(row => {
        performanceData[row.id] = {
          performance_score: parseFloat(row.performance_score) || 0,
          total_contracts: parseInt(row.total_contracts) || 0,
          active_contracts: parseInt(row.active_contracts) || 0,
          avg_kpi_score: parseFloat(row.avg_kpi_score) || 0,
          risk_assessments_count: parseInt(row.risk_assessments_count) || 0,
          avg_risk_score: parseFloat(row.avg_risk_score) || 0
        };
      });

      return performanceData;
    } catch (error) {
      console.error('Error getting vendor performance data:', error);
      return {};
    }
  }

  /**
   * Calculate optimal selections across all items
   * @param {Array} submissions - All submissions
   * @param {Object} criteria - Selection criteria
   * @returns {Array} Optimal selections
   */
  async calculateOptimalSelections(submissions, criteria) {
    try {
      // This is a simplified version - in a real implementation,
      // this would use more sophisticated optimization algorithms
      const recommendations = await this.analyzeSubmissions(submissions, { ai_settings: criteria });
      
      return recommendations.map(rec => ({
        item_id: rec.item_id,
        selected_vendor_id: rec.recommended_vendor_id,
        selected_submission_id: rec.recommended_submission_id,
        rationale: rec.rationale,
        confidence: rec.confidence,
        cost_savings: rec.cost_savings
      }));
    } catch (error) {
      console.error('Error calculating optimal selections:', error);
      throw error;
    }
  }

  /**
   * Get vendor risk assessment
   * @param {number} vendorId - Vendor ID
   * @returns {Object} Risk assessment
   */
  async getVendorRiskAssessment(vendorId) {
    try {
      const result = await query(`
        SELECT 
          v.id,
          v.performance_score,
          v.status,
          COUNT(DISTINCT c.id) as total_contracts,
          COUNT(DISTINCT CASE WHEN c.status = 'terminated' THEN c.id END) as terminated_contracts,
          COUNT(DISTINCT ra.id) as risk_assessments,
          AVG(ra.risk_score) as avg_risk_score,
          COUNT(DISTINCT d.id) as disputes_count
        FROM vendors v
        LEFT JOIN contracts c ON v.id = c.vendor_id
        LEFT JOIN risk_assessments ra ON v.id = ra.vendor_id
        LEFT JOIN disputes d ON d.entity_type = 'contract' AND d.entity_id IN (
          SELECT id FROM contracts WHERE vendor_id = v.id
        )
        WHERE v.id = $1
        GROUP BY v.id, v.performance_score, v.status
      `, [vendorId]);

      if (result.rows.length === 0) {
        return { risk_level: 'unknown', risk_score: 0.5, factors: [] };
      }

      const data = result.rows[0];
      const riskFactors = [];
      let riskScore = 0.5; // Default medium risk

      // Performance-based risk
      const performanceScore = parseFloat(data.performance_score) || 0;
      if (performanceScore > 80) {
        riskScore += 0.2;
        riskFactors.push('High performance score');
      } else if (performanceScore < 50) {
        riskScore -= 0.2;
        riskFactors.push('Low performance score');
      }

      // Contract history risk
      const totalContracts = parseInt(data.total_contracts) || 0;
      const terminatedContracts = parseInt(data.terminated_contracts) || 0;
      if (totalContracts > 0) {
        const terminationRate = terminatedContracts / totalContracts;
        if (terminationRate > 0.2) {
          riskScore -= 0.15;
          riskFactors.push('High contract termination rate');
        } else if (terminationRate === 0) {
          riskScore += 0.1;
          riskFactors.push('No contract terminations');
        }
      }

      // Disputes risk
      const disputesCount = parseInt(data.disputes_count) || 0;
      if (disputesCount > 2) {
        riskScore -= 0.1;
        riskFactors.push('Multiple disputes');
      } else if (disputesCount === 0) {
        riskScore += 0.05;
        riskFactors.push('No disputes');
      }

      // Vendor status risk
      if (data.status === 'blacklisted') {
        riskScore = 0.1;
        riskFactors.push('Vendor is blacklisted');
      } else if (data.status === 'inactive') {
        riskScore -= 0.1;
        riskFactors.push('Vendor is inactive');
      }

      // Normalize risk score
      riskScore = Math.max(0.1, Math.min(0.9, riskScore));

      const riskLevel = riskScore > 0.7 ? 'low' : 
                       riskScore > 0.4 ? 'medium' : 'high';

      return {
        vendor_id: vendorId,
        risk_level: riskLevel,
        risk_score: riskScore,
        factors: riskFactors,
        assessment_date: new Date()
      };
    } catch (error) {
      console.error('Error getting vendor risk assessment:', error);
      throw error;
    }
  }

  /**
   * Update model weights based on feedback
   * @param {Array} feedback - Selection feedback
   * @returns {Promise<void>}
   */
  async updateModelWeights(feedback) {
    try {
      // This is a placeholder for machine learning model updates
      // In a real implementation, this would:
      // 1. Store feedback in a training dataset
      // 2. Retrain the model with new data
      // 3. Update model weights based on performance
      
      console.log('Received feedback for model improvement:', feedback.length, 'items');
      
      // For now, just log the feedback
      feedback.forEach(item => {
        console.log(`Feedback: Item ${item.item_id}, Accepted: ${item.accepted}, Reason: ${item.reason || 'N/A'}`);
      });
      
      // TODO: Implement actual machine learning model updates
      return Promise.resolve();
    } catch (error) {
      console.error('Error updating model weights:', error);
      throw error;
    }
  }
}

module.exports = new AIRecommendationService();