const Contact = require('../models/Contact');
const { query } = require('../config/database');

class ContactService {
  /**
   * Create a new contact with validation
   * @param {Object} contactData - Contact data
   * @param {number} userId - User performing the operation
   * @returns {Object} Created contact
   */
  static async createContact(contactData, userId = 1) {
    try {
      // Validate required fields
      const validationErrors = Contact.validateContactData(contactData);
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.map(e => e.message).join(', ')}`);
      }

      // Check email uniqueness
      const emailCheck = await Contact.checkEmailUniqueness(contactData.email, contactData.account_id);
      if (!emailCheck.isUnique) {
        throw new Error(`Email already exists for another contact: ${emailCheck.existingContact.first_name} ${emailCheck.existingContact.last_name}`);
      }

      // Validate hierarchy if reports_to_id is provided
      if (contactData.reports_to_id) {
        const hierarchyValidation = await Contact.validateHierarchy(null, contactData.reports_to_id);
        if (!hierarchyValidation.isValid) {
          throw new Error(`Hierarchy validation failed: ${hierarchyValidation.error}`);
        }
      }

      // Create the contact
      const contact = await Contact.create(contactData, userId);
      return contact;
    } catch (error) {
      throw new Error(`Failed to create contact: ${error.message}`);
    }
  }

  /**
   * Get contact by ID with full details
   * @param {number} contactId - Contact ID
   * @returns {Object} Contact details
   */
  static async getContactById(contactId) {
    try {
      const contact = await Contact.findById(contactId);
      if (!contact) {
        throw new Error('Contact not found');
      }

      // Get additional hierarchy information
      const [directReports, reportingChain] = await Promise.all([
        Contact.getDirectReports(contactId),
        Contact.getReportingChain(contactId)
      ]);

      return {
        ...contact,
        directReports,
        reportingChain
      };
    } catch (error) {
      throw new Error(`Failed to get contact: ${error.message}`);
    }
  }

  /**
   * Update contact with validation
   * @param {number} contactId - Contact ID
   * @param {Object} updateData - Data to update
   * @param {number} userId - User performing the operation
   * @returns {Object} Updated contact
   */
  static async updateContact(contactId, updateData, userId = 1) {
    try {
      // Get existing contact
      const existingContact = await Contact.findById(contactId);
      if (!existingContact) {
        throw new Error('Contact not found');
      }

      // Validate update data
      const validationErrors = Contact.validateContactData({ ...existingContact, ...updateData });
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.map(e => e.message).join(', ')}`);
      }

      // Check email uniqueness if email is being updated
      if (updateData.email && updateData.email !== existingContact.email) {
        const emailCheck = await Contact.checkEmailUniqueness(
          updateData.email, 
          updateData.account_id || existingContact.account_id, 
          contactId
        );
        if (!emailCheck.isUnique) {
          throw new Error(`Email already exists for another contact: ${emailCheck.existingContact.first_name} ${emailCheck.existingContact.last_name}`);
        }
      }

      // Validate hierarchy if reports_to_id is being updated
      if (updateData.reports_to_id !== undefined) {
        const hierarchyValidation = await Contact.validateHierarchy(contactId, updateData.reports_to_id);
        if (!hierarchyValidation.isValid) {
          throw new Error(`Hierarchy validation failed: ${hierarchyValidation.error}`);
        }
      }

      // Update the contact
      const updatedContact = await Contact.update(contactId, updateData, userId);
      return updatedContact;
    } catch (error) {
      throw new Error(`Failed to update contact: ${error.message}`);
    }
  }

  /**
   * Delete contact with validation
   * @param {number} contactId - Contact ID
   * @param {number} userId - User performing the operation
   * @returns {Object} Deletion result
   */
  static async deleteContact(contactId, userId = 1) {
    try {
      const result = await Contact.delete(contactId, userId);
      return result;
    } catch (error) {
      throw new Error(`Failed to delete contact: ${error.message}`);
    }
  }

  /**
   * Search contacts with advanced filtering
   * @param {Object} searchParams - Search parameters
   * @returns {Object} Paginated search results
   */
  static async searchContacts(searchParams) {
    try {
      const results = await Contact.search(searchParams);
      return results;
    } catch (error) {
      throw new Error(`Failed to search contacts: ${error.message}`);
    }
  }

  /**
   * Get all contacts for an account
   * @param {number} accountId - Account ID
   * @param {Object} options - Additional options (pagination, filters)
   * @returns {Object} Account contacts
   */
  static async getAccountContacts(accountId, options = {}) {
    try {
      const { page = 1, limit = 50, includeHierarchy = false } = options;
      
      const filters = { account_id: accountId, ...options.filters };
      const results = await Contact.findAll(filters, page, limit);

      if (includeHierarchy) {
        // Add hierarchy information
        const orgChart = await Contact.buildOrgChart(accountId);
        results.orgChart = orgChart;
      }

      return results;
    } catch (error) {
      throw new Error(`Failed to get account contacts: ${error.message}`);
    }
  }

  /**
   * Get contact hierarchy for organizational chart
   * @param {number} accountId - Account ID
   * @param {number} rootContactId - Root contact ID (optional)
   * @returns {Object} Organizational chart data
   */
  static async getContactHierarchy(accountId, rootContactId = null) {
    try {
      const orgChart = await Contact.buildOrgChart(accountId, rootContactId);
      
      // Calculate statistics
      const stats = await Contact.getContactStats(accountId);
      
      return {
        orgChart,
        statistics: stats
      };
    } catch (error) {
      throw new Error(`Failed to get contact hierarchy: ${error.message}`);
    }
  }

  /**
   * Update contact hierarchy (move contact in org chart)
   * @param {number} contactId - Contact to move
   * @param {number} newReportsToId - New manager ID (null for top level)
   * @param {number} userId - User performing the operation
   * @returns {Object} Updated contact
   */
  static async updateContactHierarchy(contactId, newReportsToId, userId = 1) {
    try {
      // Validate hierarchy change
      const hierarchyValidation = await Contact.validateHierarchy(contactId, newReportsToId);
      if (!hierarchyValidation.isValid) {
        throw new Error(`Hierarchy validation failed: ${hierarchyValidation.error}`);
      }

      // Update the contact
      const updatedContact = await Contact.update(contactId, { reports_to_id: newReportsToId }, userId);
      return updatedContact;
    } catch (error) {
      throw new Error(`Failed to update contact hierarchy: ${error.message}`);
    }
  }

  /**
   * Bulk create contacts
   * @param {Array} contactsData - Array of contact data
   * @param {number} userId - User performing the operation
   * @returns {Object} Bulk creation results
   */
  static async bulkCreateContacts(contactsData, userId = 1) {
    const results = {
      successful: 0,
      failed: 0,
      results: []
    };

    for (let i = 0; i < contactsData.length; i++) {
      try {
        const contact = await this.createContact(contactsData[i], userId);
        results.successful++;
        results.results.push({
          success: true,
          data: contact,
          index: i
        });
      } catch (error) {
        results.failed++;
        results.results.push({
          success: false,
          error: error.message,
          index: i
        });
      }
    }

    return results;
  }

  /**
   * Bulk update contacts
   * @param {Array} contactsData - Array of contact update data (must include id)
   * @param {number} userId - User performing the operation
   * @returns {Object} Bulk update results
   */
  static async bulkUpdateContacts(contactsData, userId = 1) {
    const results = {
      successful: 0,
      failed: 0,
      results: []
    };

    for (let i = 0; i < contactsData.length; i++) {
      try {
        const { id, ...updateData } = contactsData[i];
        if (!id) {
          throw new Error('Contact ID is required for updates');
        }

        const contact = await this.updateContact(id, updateData, userId);
        results.successful++;
        results.results.push({
          success: true,
          data: contact,
          index: i
        });
      } catch (error) {
        results.failed++;
        results.results.push({
          success: false,
          error: error.message,
          index: i
        });
      }
    }

    return results;
  }

  /**
   * Find potential duplicate contacts
   * @param {Object} searchCriteria - Criteria for finding duplicates
   * @returns {Array} Potential duplicate contacts
   */
  static async findDuplicateContacts(searchCriteria) {
    try {
      const { email, firstName, lastName, accountId } = searchCriteria;
      const duplicates = await Contact.findPotentialDuplicates(email, firstName, lastName, accountId);
      return duplicates;
    } catch (error) {
      throw new Error(`Failed to find duplicate contacts: ${error.message}`);
    }
  }

  /**
   * Merge duplicate contacts
   * @param {number} primaryContactId - Primary contact to keep
   * @param {Array} duplicateContactIds - Duplicate contact IDs to merge
   * @param {number} userId - User performing the operation
   * @returns {Object} Merged contact
   */
  static async mergeContacts(primaryContactId, duplicateContactIds, userId = 1) {
    try {
      const mergedContact = await Contact.mergeContacts(primaryContactId, duplicateContactIds, userId);
      return mergedContact;
    } catch (error) {
      throw new Error(`Failed to merge contacts: ${error.message}`);
    }
  }

  /**
   * Get contact statistics
   * @param {number} accountId - Account ID (optional, for account-specific stats)
   * @returns {Object} Contact statistics
   */
  static async getContactStatistics(accountId = null) {
    try {
      const stats = await Contact.getContactStats(accountId);
      
      // Add additional computed statistics
      const additionalStats = {
        contact_engagement_rate: stats.total_contacts > 0 
          ? ((stats.total_contacts - stats.opted_out_contacts) / stats.total_contacts * 100).toFixed(2)
          : 0,
        hierarchy_coverage: stats.total_contacts > 0
          ? ((stats.total_contacts - stats.top_level_contacts) / stats.total_contacts * 100).toFixed(2)
          : 0
      };

      return {
        ...stats,
        ...additionalStats
      };
    } catch (error) {
      throw new Error(`Failed to get contact statistics: ${error.message}`);
    }
  }

  /**
   * Export contacts to CSV
   * @param {Object} filters - Export filters
   * @returns {Object} CSV export data
   */
  static async exportContacts(filters = {}) {
    try {
      const csvData = await Contact.exportToCSV(filters);
      return csvData;
    } catch (error) {
      throw new Error(`Failed to export contacts: ${error.message}`);
    }
  }

  /**
   * Get contacts by email domain
   * @param {string} domain - Email domain
   * @returns {Array} Contacts with matching email domain
   */
  static async getContactsByDomain(domain) {
    try {
      const contacts = await Contact.findByEmailDomain(domain);
      return contacts;
    } catch (error) {
      throw new Error(`Failed to get contacts by domain: ${error.message}`);
    }
  }

  /**
   * Get contact audit history
   * @param {number} contactId - Contact ID
   * @returns {Array} Audit history entries
   */
  static async getContactAuditHistory(contactId) {
    try {
      const auditHistory = await Contact.getAuditHistory(contactId);
      return auditHistory;
    } catch (error) {
      throw new Error(`Failed to get contact audit history: ${error.message}`);
    }
  }

  /**
   * Validate contact data without creating
   * @param {Object} contactData - Contact data to validate
   * @returns {Object} Validation result
   */
  static async validateContactData(contactData) {
    try {
      const errors = Contact.validateContactData(contactData);
      
      // Additional async validations
      if (contactData.email) {
        const emailCheck = await Contact.checkEmailUniqueness(contactData.email, contactData.account_id);
        if (!emailCheck.isUnique) {
          errors.push({
            field: 'email',
            message: 'Email already exists for another contact',
            code: 'email_not_unique'
          });
        }
      }

      if (contactData.reports_to_id) {
        const hierarchyValidation = await Contact.validateHierarchy(null, contactData.reports_to_id);
        if (!hierarchyValidation.isValid) {
          errors.push({
            field: 'reports_to_id',
            message: hierarchyValidation.error,
            code: 'hierarchy_invalid'
          });
        }
      }

      return {
        isValid: errors.length === 0,
        errors
      };
    } catch (error) {
      throw new Error(`Failed to validate contact data: ${error.message}`);
    }
  }

  /**
   * Get unique departments from contacts
   * @param {number} accountId - Account ID (optional)
   * @returns {Array} List of unique departments
   */
  static async getDepartments(accountId = null) {
    try {
      let departments;
      if (accountId) {
        const result = await query(
          'SELECT DISTINCT department FROM contacts WHERE account_id = $1 AND department IS NOT NULL AND is_deleted = FALSE ORDER BY department',
          [accountId]
        );
        departments = result.rows.map(row => row.department);
      } else {
        departments = await Contact.getDepartments();
      }
      return departments;
    } catch (error) {
      throw new Error(`Failed to get departments: ${error.message}`);
    }
  }

  /**
   * Get unique lead sources from contacts
   * @returns {Array} List of unique lead sources
   */
  static async getLeadSources() {
    try {
      const leadSources = await Contact.getLeadSources();
      return leadSources;
    } catch (error) {
      throw new Error(`Failed to get lead sources: ${error.message}`);
    }
  }

  /**
   * Update communication preferences for a contact
   * @param {number} contactId - Contact ID
   * @param {Object} preferences - Communication preferences
   * @param {number} userId - User performing the operation
   * @returns {Object} Updated contact
   */
  static async updateCommunicationPreferences(contactId, preferences, userId = 1) {
    try {
      const updateData = {
        do_not_call: preferences.doNotCall,
        has_opted_out_of_email: preferences.hasOptedOutOfEmail,
        languages: preferences.preferredLanguage
      };

      // Add custom preferences to custom_fields
      if (preferences.customPreferences) {
        const existingContact = await Contact.findById(contactId);
        const customFields = existingContact.custom_fields || {};
        customFields.communication_preferences = preferences.customPreferences;
        updateData.custom_fields = customFields;
      }

      const updatedContact = await Contact.update(contactId, updateData, userId);
      return updatedContact;
    } catch (error) {
      throw new Error(`Failed to update communication preferences: ${error.message}`);
    }
  }
}

module.exports = ContactService;