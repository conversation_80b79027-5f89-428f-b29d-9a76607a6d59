const { query } = require('../config/database');

/**
 * AuditLogger - Centralized audit logging system for quote operations
 * Tracks all quote-related actions for compliance and monitoring
 */
class AuditLogger {
  
  /**
   * Log quote action
   * @param {Object} params - Audit log parameters
   * @param {string} params.action - Action performed (create, update, send, etc.)
   * @param {string} params.entity_type - Type of entity (quote, template, interaction)
   * @param {number} params.entity_id - ID of the entity
   * @param {number} params.user_id - User performing the action
   * @param {Object} params.old_values - Previous values (for updates)
   * @param {Object} params.new_values - New values
   * @param {string} params.ip_address - User's IP address
   * @param {string} params.user_agent - User's browser/client info
   * @param {Object} params.metadata - Additional context data
   * @returns {Promise<Object>} Audit log entry
   */
  static async logAction({
    action,
    entity_type,
    entity_id,
    user_id,
    old_values = null,
    new_values = null,
    ip_address = null,
    user_agent = null,
    metadata = {}
  }) {
    try {
      const logQuery = `
        INSERT INTO audit_logs (
          action,
          entity_type,
          entity_id,
          user_id,
          old_values,
          new_values,
          ip_address,
          user_agent,
          metadata,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP)
        RETURNING *
      `;
      
      const params = [
        action,
        entity_type,
        entity_id,
        user_id,
        old_values ? JSON.stringify(old_values) : null,
        new_values ? JSON.stringify(new_values) : null,
        ip_address,
        user_agent,
        JSON.stringify(metadata)
      ];
      
      const result = await query(logQuery, params);
      
      console.log(`Audit log created: ${action} on ${entity_type} ${entity_id} by user ${user_id}`);
      
      return result.rows[0];
    } catch (error) {
      console.error('Error creating audit log:', error);
      // Don't throw error to avoid breaking main operations
      return null;
    }
  }
  
  /**
   * Log quote creation
   * @param {Object} quote - Quote object
   * @param {number} user_id - User ID
   * @param {Object} request - Express request object
   * @returns {Promise<Object>} Audit log entry
   */
  static async logQuoteCreation(quote, user_id, request = {}) {
    return this.logAction({
      action: 'create',
      entity_type: 'quote',
      entity_id: quote.id,
      user_id,
      new_values: {
        rfq_id: quote.rfq_id,
        title: quote.title,
        total_amount: quote.total_amount,
        currency: quote.currency,
        status: quote.status
      },
      ip_address: request.ip,
      user_agent: request.get('User-Agent'),
      metadata: {
        rfq_id: quote.rfq_id,
        quote_type: quote.quote_template_id ? 'template_based' : 'custom',
        generation_method: quote.selected_bids ? 'rfq_submission' : 'manual'
      }
    });
  }
  
  /**
   * Log quote update
   * @param {Object} oldQuote - Previous quote state
   * @param {Object} newQuote - Updated quote state
   * @param {number} user_id - User ID
   * @param {Object} request - Express request object
   * @returns {Promise<Object>} Audit log entry
   */
  static async logQuoteUpdate(oldQuote, newQuote, user_id, request = {}) {
    // Identify changed fields
    const changedFields = {};
    const oldValues = {};
    
    const fieldsToTrack = [
      'title', 'total_amount', 'currency', 'status', 'selected_bids',
      'commission_structure', 'margin_percentage', 'taxes', 'terms', 'notes'
    ];
    
    fieldsToTrack.forEach(field => {
      if (JSON.stringify(oldQuote[field]) !== JSON.stringify(newQuote[field])) {
        changedFields[field] = newQuote[field];
        oldValues[field] = oldQuote[field];
      }
    });
    
    if (Object.keys(changedFields).length === 0) {
      return null; // No changes to log
    }
    
    return this.logAction({
      action: 'update',
      entity_type: 'quote',
      entity_id: newQuote.id,
      user_id,
      old_values: oldValues,
      new_values: changedFields,
      ip_address: request.ip,
      user_agent: request.get('User-Agent'),
      metadata: {
        fields_changed: Object.keys(changedFields),
        change_count: Object.keys(changedFields).length
      }
    });
  }
  
  /**
   * Log quote status change
   * @param {Object} quote - Quote object
   * @param {string} oldStatus - Previous status
   * @param {string} newStatus - New status
   * @param {number} user_id - User ID
   * @param {Object} request - Express request object
   * @returns {Promise<Object>} Audit log entry
   */
  static async logQuoteStatusChange(quote, oldStatus, newStatus, user_id, request = {}) {
    return this.logAction({
      action: 'status_change',
      entity_type: 'quote',
      entity_id: quote.id,
      user_id,
      old_values: { status: oldStatus },
      new_values: { status: newStatus },
      ip_address: request.ip,
      user_agent: request.get('User-Agent'),
      metadata: {
        status_transition: `${oldStatus} -> ${newStatus}`,
        quote_amount: quote.total_amount,
        rfq_id: quote.rfq_id
      }
    });
  }
  
  /**
   * Log quote sending
   * @param {Object} quote - Quote object
   * @param {string} recipient_email - Email address
   * @param {number} user_id - User ID
   * @param {Object} request - Express request object
   * @returns {Promise<Object>} Audit log entry
   */
  static async logQuoteSent(quote, recipient_email, user_id, request = {}) {
    return this.logAction({
      action: 'send',
      entity_type: 'quote',
      entity_id: quote.id,
      user_id,
      new_values: {
        recipient_email,
        sent_at: new Date().toISOString()
      },
      ip_address: request.ip,
      user_agent: request.get('User-Agent'),
      metadata: {
        recipient_email,
        quote_amount: quote.total_amount,
        rfq_id: quote.rfq_id,
        delivery_method: 'email'
      }
    });
  }
  
  /**
   * Log quote PDF generation
   * @param {Object} quote - Quote object
   * @param {number} user_id - User ID
   * @param {Object} request - Express request object
   * @returns {Promise<Object>} Audit log entry
   */
  static async logQuotePDFGeneration(quote, user_id, request = {}) {
    return this.logAction({
      action: 'pdf_generate',
      entity_type: 'quote',
      entity_id: quote.id,
      user_id,
      ip_address: request.ip,
      user_agent: request.get('User-Agent'),
      metadata: {
        quote_amount: quote.total_amount,
        rfq_id: quote.rfq_id,
        generation_timestamp: new Date().toISOString()
      }
    });
  }
  
  /**
   * Log quote deletion
   * @param {Object} quote - Quote object being deleted
   * @param {number} user_id - User ID
   * @param {Object} request - Express request object
   * @returns {Promise<Object>} Audit log entry
   */
  static async logQuoteDeletion(quote, user_id, request = {}) {
    return this.logAction({
      action: 'delete',
      entity_type: 'quote',
      entity_id: quote.id,
      user_id,
      old_values: {
        title: quote.title,
        total_amount: quote.total_amount,
        status: quote.status,
        rfq_id: quote.rfq_id
      },
      ip_address: request.ip,
      user_agent: request.get('User-Agent'),
      metadata: {
        quote_amount: quote.total_amount,
        rfq_id: quote.rfq_id,
        deletion_reason: 'user_requested'
      }
    });
  }
  
  /**
   * Get audit history for a quote
   * @param {number} quote_id - Quote ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Audit log entries
   */
  static async getQuoteAuditHistory(quote_id, options = {}) {
    try {
      const {
        limit = 50,
        offset = 0,
        action_filter = null,
        date_from = null,
        date_to = null
      } = options;
      
      let whereClause = 'WHERE entity_type = $1 AND entity_id = $2';
      let params = ['quote', quote_id];
      let paramIndex = 3;
      
      if (action_filter) {
        whereClause += ` AND action = $${paramIndex}`;
        params.push(action_filter);
        paramIndex++;
      }
      
      if (date_from) {
        whereClause += ` AND created_at >= $${paramIndex}`;
        params.push(date_from);
        paramIndex++;
      }
      
      if (date_to) {
        whereClause += ` AND created_at <= $${paramIndex}`;
        params.push(date_to);
        paramIndex++;
      }
      
      const auditQuery = `
        SELECT 
          al.*,
          u.name as user_name,
          u.email as user_email
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        ${whereClause}
        ORDER BY al.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      
      params.push(limit, offset);
      
      const result = await query(auditQuery, params);
      
      return result.rows.map(row => ({
        ...row,
        old_values: row.old_values ? JSON.parse(row.old_values) : null,
        new_values: row.new_values ? JSON.parse(row.new_values) : null,
        metadata: row.metadata ? JSON.parse(row.metadata) : {}
      }));
    } catch (error) {
      console.error('Error getting quote audit history:', error);
      throw error;
    }
  }
  
  /**
   * Get audit statistics
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Audit statistics
   */
  static async getAuditStatistics(options = {}) {
    try {
      const {
        entity_type = 'quote',
        date_from = null,
        date_to = null
      } = options;
      
      let whereClause = 'WHERE entity_type = $1';
      let params = [entity_type];
      let paramIndex = 2;
      
      if (date_from) {
        whereClause += ` AND created_at >= $${paramIndex}`;
        params.push(date_from);
        paramIndex++;
      }
      
      if (date_to) {
        whereClause += ` AND created_at <= $${paramIndex}`;
        params.push(date_to);
        paramIndex++;
      }
      
      const statsQuery = `
        SELECT 
          action,
          COUNT(*) as count,
          COUNT(DISTINCT user_id) as unique_users,
          COUNT(DISTINCT entity_id) as unique_entities,
          MIN(created_at) as first_occurrence,
          MAX(created_at) as last_occurrence
        FROM audit_logs
        ${whereClause}
        GROUP BY action
        ORDER BY count DESC
      `;
      
      const result = await query(statsQuery, params);
      
      return {
        action_statistics: result.rows,
        total_actions: result.rows.reduce((sum, row) => sum + parseInt(row.count), 0),
        unique_users: Math.max(...result.rows.map(row => parseInt(row.unique_users))),
        unique_entities: Math.max(...result.rows.map(row => parseInt(row.unique_entities)))
      };
    } catch (error) {
      console.error('Error getting audit statistics:', error);
      throw error;
    }
  }
}

module.exports = AuditLogger;