const Contact = require('../models/Contact');
const { query } = require('../config/database');

class ContactHierarchyService {
  /**
   * Get the complete organizational chart for an account
   * @param {number} accountId - The account ID
   * @param {number} rootContactId - Optional root contact ID
   * @returns {Object} Organizational chart structure
   */
  static async getOrganizationalChart(accountId, rootContactId = null) {
    try {
      const orgChart = await Contact.buildOrgChart(accountId, rootContactId);
      
      if (orgChart.length === 0) {
        return {
          nodes: [],
          totalNodes: 0,
          maxDepth: 0,
          rootNodes: 0
        };
      }

      // Calculate chart statistics
      const stats = this.calculateChartStatistics(orgChart);
      
      return {
        nodes: orgChart,
        ...stats
      };
    } catch (error) {
      throw new Error(`Failed to get organizational chart: ${error.message}`);
    }
  }

  /**
   * Calculate statistics for an organizational chart
   * @param {Array} orgChart - Organizational chart nodes
   * @returns {Object} Chart statistics
   */
  static calculateChartStatistics(orgChart) {
    let totalNodes = 0;
    let maxDepth = 0;
    const rootNodes = orgChart.length;

    const traverseNodes = (nodes, currentDepth = 0) => {
      nodes.forEach(node => {
        totalNodes++;
        maxDepth = Math.max(maxDepth, currentDepth);
        
        if (node.children && node.children.length > 0) {
          traverseNodes(node.children, currentDepth + 1);
        }
      });
    };

    traverseNodes(orgChart);

    return {
      totalNodes,
      maxDepth,
      rootNodes
    };
  }

  /**
   * Get all subordinates for a contact (recursive)
   * @param {number} contactId - The manager contact ID
   * @returns {Array} Array of all subordinate contacts
   */
  static async getAllSubordinates(contactId) {
    try {
      const result = await query(
        `WITH RECURSIVE subordinates AS (
          SELECT id, first_name, last_name, title, department, email, reports_to_id, 0 as level,
                 CASE 
                   WHEN first_name IS NOT NULL AND first_name != '' 
                   THEN first_name || ' ' || last_name 
                   ELSE last_name 
                 END as full_name
          FROM contacts 
          WHERE reports_to_id = $1 AND is_deleted = FALSE
          
          UNION ALL
          
          SELECT c.id, c.first_name, c.last_name, c.title, c.department, c.email, c.reports_to_id, s.level + 1,
                 CASE 
                   WHEN c.first_name IS NOT NULL AND c.first_name != '' 
                   THEN c.first_name || ' ' || c.last_name 
                   ELSE c.last_name 
                 END as full_name
          FROM contacts c
          INNER JOIN subordinates s ON c.reports_to_id = s.id
          WHERE c.is_deleted = FALSE AND s.level < 10
        )
        SELECT * FROM subordinates ORDER BY level, full_name`,
        [contactId]
      );

      return result.rows;
    } catch (error) {
      throw new Error(`Failed to get subordinates: ${error.message}`);
    }
  }

  /**
   * Get the complete reporting chain for a contact (up to top level)
   * @param {number} contactId - The contact ID
   * @returns {Array} Array of managers up the chain
   */
  static async getReportingChain(contactId) {
    try {
      const reportingChain = await Contact.getReportingChain(contactId);
      return reportingChain;
    } catch (error) {
      throw new Error(`Failed to get reporting chain: ${error.message}`);
    }
  }

  /**
   * Get hierarchy path with breadcrumb information
   * @param {number} contactId - The contact ID
   * @returns {Array} Breadcrumb path from root to contact
   */
  static async getHierarchyPath(contactId) {
    try {
      const path = await Contact.getHierarchyPath(contactId);
      return path;
    } catch (error) {
      throw new Error(`Failed to get hierarchy path: ${error.message}`);
    }
  }

  /**
   * Validate hierarchy move to prevent cycles
   * @param {number} contactId - Contact to move
   * @param {number} newManagerId - New manager ID
   * @returns {Object} Validation result
   */
  static async validateHierarchyMove(contactId, newManagerId) {
    try {
      if (contactId === newManagerId) {
        return {
          isValid: false,
          error: 'Contact cannot report to themselves'
        };
      }

      if (!newManagerId) {
        return { isValid: true, error: null };
      }

      // Check if the new manager is a subordinate of the contact being moved
      const subordinates = await this.getAllSubordinates(contactId);
      const subordinateIds = subordinates.map(s => s.id);
      
      if (subordinateIds.includes(newManagerId)) {
        return {
          isValid: false,
          error: 'Cannot move contact to report to their subordinate'
        };
      }

      // Check if both contacts are in the same account
      const [contact, newManager] = await Promise.all([
        Contact.findById(contactId),
        Contact.findById(newManagerId)
      ]);

      if (!contact || !newManager) {
        return {
          isValid: false,
          error: 'Contact or new manager not found'
        };
      }

      if (contact.account_id !== newManager.account_id) {
        return {
          isValid: false,
          error: 'Contacts must be in the same account'
        };
      }

      return { isValid: true, error: null };
    } catch (error) {
      throw new Error(`Failed to validate hierarchy move: ${error.message}`);
    }
  }

  /**
   * Move a contact in the hierarchy
   * @param {number} contactId - Contact to move
   * @param {number} newManagerId - New manager ID (null for top level)
   * @param {number} userId - User performing the operation
   * @returns {Object} Updated contact
   */
  static async moveContactInHierarchy(contactId, newManagerId, userId = 1) {
    try {
      // Validate the move
      const validation = await this.validateHierarchyMove(contactId, newManagerId);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Perform the move
      const updatedContact = await Contact.update(contactId, {
        reports_to_id: newManagerId
      }, userId);

      return updatedContact;
    } catch (error) {
      throw new Error(`Failed to move contact in hierarchy: ${error.message}`);
    }
  }

  /**
   * Get hierarchy statistics for a contact or account
   * @param {number} contactId - Contact ID (optional)
   * @param {number} accountId - Account ID (optional)
   * @returns {Object} Hierarchy statistics
   */
  static async getHierarchyStatistics(contactId = null, accountId = null) {
    try {
      let stats = {};

      if (contactId) {
        // Get statistics for a specific contact
        const [subordinates, reportingChain, directReports] = await Promise.all([
          this.getAllSubordinates(contactId),
          this.getReportingChain(contactId),
          Contact.getDirectReports(contactId)
        ]);

        // Calculate depth below this contact
        const maxDepthBelow = subordinates.length > 0 
          ? Math.max(...subordinates.map(s => s.level)) + 1 
          : 0;

        stats = {
          contact_id: contactId,
          total_subordinates: subordinates.length,
          direct_reports: directReports.length,
          hierarchy_level: reportingChain.length,
          max_depth_below: maxDepthBelow,
          reporting_chain_length: reportingChain.length
        };
      }

      if (accountId) {
        // Get statistics for the entire account
        const accountStats = await query(
          `SELECT 
            COUNT(*) as total_contacts,
            COUNT(CASE WHEN reports_to_id IS NULL THEN 1 END) as top_level_contacts,
            COUNT(DISTINCT reports_to_id) as managers_count,
            AVG(CASE WHEN reports_to_id IS NOT NULL THEN 1 ELSE 0 END) as hierarchy_coverage
           FROM contacts 
           WHERE account_id = $1 AND is_deleted = FALSE`,
          [accountId]
        );

        // Get maximum hierarchy depth
        const depthResult = await query(
          `WITH RECURSIVE hierarchy_depth AS (
            SELECT id, reports_to_id, 0 as depth
            FROM contacts 
            WHERE account_id = $1 AND reports_to_id IS NULL AND is_deleted = FALSE
            
            UNION ALL
            
            SELECT c.id, c.reports_to_id, hd.depth + 1
            FROM contacts c
            INNER JOIN hierarchy_depth hd ON c.reports_to_id = hd.id
            WHERE c.account_id = $1 AND c.is_deleted = FALSE AND hd.depth < 10
          )
          SELECT MAX(depth) as max_depth FROM hierarchy_depth`,
          [accountId]
        );

        const accountStatsRow = accountStats.rows[0];
        const maxDepth = depthResult.rows[0].max_depth || 0;

        stats.account_stats = {
          account_id: accountId,
          total_contacts: parseInt(accountStatsRow.total_contacts),
          top_level_contacts: parseInt(accountStatsRow.top_level_contacts),
          managers_count: parseInt(accountStatsRow.managers_count),
          max_hierarchy_depth: maxDepth,
          hierarchy_coverage: parseFloat(accountStatsRow.hierarchy_coverage || 0)
        };
      }

      return stats;
    } catch (error) {
      throw new Error(`Failed to get hierarchy statistics: ${error.message}`);
    }
  }

  /**
   * Find contacts without managers (top-level contacts)
   * @param {number} accountId - Account ID
   * @returns {Array} Top-level contacts
   */
  static async getTopLevelContacts(accountId) {
    try {
      const result = await query(
        `SELECT 
          c.id, c.first_name, c.last_name, c.title, c.department, c.email,
          CASE 
            WHEN c.first_name IS NOT NULL AND c.first_name != '' 
            THEN c.first_name || ' ' || c.last_name 
            ELSE c.last_name 
          END as full_name,
          (SELECT COUNT(*) FROM contacts sub WHERE sub.reports_to_id = c.id AND sub.is_deleted = FALSE) as direct_reports_count
         FROM contacts c
         WHERE c.account_id = $1 AND c.reports_to_id IS NULL AND c.is_deleted = FALSE
         ORDER BY c.last_name, c.first_name`,
        [accountId]
      );

      return result.rows;
    } catch (error) {
      throw new Error(`Failed to get top-level contacts: ${error.message}`);
    }
  }

  /**
   * Find contacts who are managers (have direct reports)
   * @param {number} accountId - Account ID
   * @returns {Array} Manager contacts
   */
  static async getManagerContacts(accountId) {
    try {
      const result = await query(
        `SELECT DISTINCT
          c.id, c.first_name, c.last_name, c.title, c.department, c.email,
          CASE 
            WHEN c.first_name IS NOT NULL AND c.first_name != '' 
            THEN c.first_name || ' ' || c.last_name 
            ELSE c.last_name 
          END as full_name,
          (SELECT COUNT(*) FROM contacts sub WHERE sub.reports_to_id = c.id AND sub.is_deleted = FALSE) as direct_reports_count
         FROM contacts c
         WHERE c.account_id = $1 AND c.is_deleted = FALSE
           AND EXISTS (SELECT 1 FROM contacts sub WHERE sub.reports_to_id = c.id AND sub.is_deleted = FALSE)
         ORDER BY c.last_name, c.first_name`,
        [accountId]
      );

      return result.rows;
    } catch (error) {
      throw new Error(`Failed to get manager contacts: ${error.message}`);
    }
  }

  /**
   * Get hierarchy gaps (contacts who should have managers but don't)
   * @param {number} accountId - Account ID
   * @returns {Array} Contacts with potential hierarchy gaps
   */
  static async getHierarchyGaps(accountId) {
    try {
      // This is a business logic function that identifies contacts who might need managers
      // Based on department structure or other criteria
      const result = await query(
        `SELECT 
          c.id, c.first_name, c.last_name, c.title, c.department, c.email,
          CASE 
            WHEN c.first_name IS NOT NULL AND c.first_name != '' 
            THEN c.first_name || ' ' || c.last_name 
            ELSE c.last_name 
          END as full_name,
          'No manager assigned' as gap_reason
         FROM contacts c
         WHERE c.account_id = $1 AND c.reports_to_id IS NULL AND c.is_deleted = FALSE
           AND c.department IS NOT NULL
           AND EXISTS (
             SELECT 1 FROM contacts other 
             WHERE other.account_id = c.account_id 
               AND other.department = c.department 
               AND other.id != c.id 
               AND other.is_deleted = FALSE
           )
         ORDER BY c.department, c.last_name`,
        [accountId]
      );

      return result.rows;
    } catch (error) {
      throw new Error(`Failed to get hierarchy gaps: ${error.message}`);
    }
  }

  /**
   * Bulk update hierarchy relationships
   * @param {Array} hierarchyUpdates - Array of {contactId, newManagerId}
   * @param {number} userId - User performing the operation
   * @returns {Object} Bulk update results
   */
  static async bulkUpdateHierarchy(hierarchyUpdates, userId = 1) {
    const results = {
      successful: 0,
      failed: 0,
      results: []
    };

    for (let i = 0; i < hierarchyUpdates.length; i++) {
      try {
        const { contactId, newManagerId } = hierarchyUpdates[i];
        
        const updatedContact = await this.moveContactInHierarchy(contactId, newManagerId, userId);
        
        results.successful++;
        results.results.push({
          success: true,
          data: updatedContact,
          index: i
        });
      } catch (error) {
        results.failed++;
        results.results.push({
          success: false,
          error: error.message,
          index: i
        });
      }
    }

    return results;
  }

  /**
   * Get suggested managers for a contact based on department and level
   * @param {number} contactId - Contact ID
   * @returns {Array} Suggested manager contacts
   */
  static async getSuggestedManagers(contactId) {
    try {
      const contact = await Contact.findById(contactId);
      if (!contact) {
        throw new Error('Contact not found');
      }

      // Find potential managers in the same department or account
      const result = await query(
        `SELECT 
          c.id, c.first_name, c.last_name, c.title, c.department, c.email,
          CASE 
            WHEN c.first_name IS NOT NULL AND c.first_name != '' 
            THEN c.first_name || ' ' || c.last_name 
            ELSE c.last_name 
          END as full_name,
          CASE 
            WHEN c.department = $2 THEN 1
            ELSE 2
          END as priority_score,
          (SELECT COUNT(*) FROM contacts sub WHERE sub.reports_to_id = c.id AND sub.is_deleted = FALSE) as direct_reports_count
         FROM contacts c
         WHERE c.account_id = $1 AND c.id != $3 AND c.is_deleted = FALSE
           AND (c.title ILIKE '%manager%' OR c.title ILIKE '%director%' OR c.title ILIKE '%head%' OR c.title ILIKE '%lead%')
         ORDER BY priority_score, direct_reports_count, c.last_name
         LIMIT 10`,
        [contact.account_id, contact.department, contactId]
      );

      return result.rows;
    } catch (error) {
      throw new Error(`Failed to get suggested managers: ${error.message}`);
    }
  }

  /**
   * Flatten organizational chart for export or display
   * @param {Array} orgChart - Organizational chart nodes
   * @returns {Array} Flattened hierarchy with level information
   */
  static flattenOrgChart(orgChart) {
    const flattened = [];

    const traverse = (nodes, level = 0) => {
      nodes.forEach(node => {
        flattened.push({
          id: node.id,
          full_name: node.full_name,
          title: node.title,
          department: node.department,
          email: node.email,
          level: level,
          direct_reports_count: node.direct_reports_count || 0
        });

        if (node.children && node.children.length > 0) {
          traverse(node.children, level + 1);
        }
      });
    };

    traverse(orgChart);
    return flattened;
  }

  /**
   * Export organizational chart to CSV
   * @param {number} accountId - Account ID
   * @returns {Object} CSV export data
   */
  static async exportOrgChart(accountId) {
    try {
      const orgChart = await this.getOrganizationalChart(accountId);
      const flattened = this.flattenOrgChart(orgChart.nodes);

      const headers = [
        'ID', 'Full Name', 'Title', 'Department', 'Email', 
        'Hierarchy Level', 'Direct Reports Count'
      ];

      const rows = flattened.map(contact => [
        contact.id,
        contact.full_name,
        contact.title || '',
        contact.department || '',
        contact.email,
        contact.level,
        contact.direct_reports_count
      ]);

      return {
        headers,
        rows
      };
    } catch (error) {
      throw new Error(`Failed to export org chart: ${error.message}`);
    }
  }
}

module.exports = ContactHierarchyService;