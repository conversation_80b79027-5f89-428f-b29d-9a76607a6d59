const { query, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');
const ClientQuote = require('../models/ClientQuote');
const RFQSubmission = require('../models/RFQSubmission');
const Vendor = require('../models/Vendor');

/**
 * QuoteGenerationService - Handles quote generation from RFQ submissions
 * Implements business logic for selection validation, commission calculations, and quote creation
 */
class QuoteGenerationService {
  
  /**
   * Generate a quote from selected RFQ submissions
   * @param {Object} params - Quote generation parameters
   * @param {number} params.rfqId - RFQ ID
   * @param {Object} params.selectedBids - Selected vendor bids
   * @param {Object} params.quoteData - Quote configuration data
   * @param {number} params.userId - User creating the quote
   * @returns {Promise<Object>} Generated quote
   */
  static async generateQuote({ rfqId, selectedBids, quoteData, userId }) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Validate selections
      await this.validateSelections(rfqId, selectedBids, client);
      
      // Calculate commission structure
      const commissionStructure = await this.calculateCommissions(selectedBids, quoteData.commissionConfig);
      
      // Calculate total amounts
      const totals = await this.calculateTotals(selectedBids, commissionStructure, quoteData);
      
      // Prepare quote data
      const finalQuoteData = {
        rfq_id: rfqId,
        title: quoteData.title,
        selectedBids,
        commission_structure: commissionStructure,
        total_amount: totals.finalTotal,
        currency: quoteData.currency || 'USD',
        margin_percentage: quoteData.margin_percentage || 0,
        taxes: totals.taxes,
        terms: quoteData.terms,
        notes: quoteData.notes,
        client_email: quoteData.client_email,
        client_name: quoteData.client_name,
        expires_in_days: quoteData.expires_in_days || 30,
        quote_template_id: quoteData.quote_template_id
      };
      
      // Create quote using existing model
      const quote = await ClientQuote.create(finalQuoteData, userId);
      
      await commitTransaction(client);
      
      // Log successful generation
      console.log(`Quote generated successfully: ID ${quote.id} for RFQ ${rfqId}`);
      
      return quote;
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in QuoteGenerationService.generateQuote:', error);
      throw error;
    }
  }
  
  /**
   * Validate selected submissions
   * @param {number} rfqId - RFQ ID
   * @param {Object} selectedBids - Selected vendor bids
   * @param {Object} client - Database client
   */
  static async validateSelections(rfqId, selectedBids, client) {
    // Verify RFQ exists
    const rfqCheck = await client.query(
      'SELECT id, status, items FROM rfqs WHERE id = $1',
      [rfqId]
    );
    
    if (rfqCheck.rows.length === 0) {
      throw new Error('RFQ not found');
    }
    
    const rfq = rfqCheck.rows[0];
    
    if (rfq.status === 'closed' || rfq.status === 'cancelled') {
      throw new Error('Cannot generate quote for closed or cancelled RFQ');
    }
    
    // Verify all selected submissions exist and belong to the RFQ
    const submissionIds = Object.values(selectedBids).map(bid => bid.submissionId);
    
    if (submissionIds.length === 0) {
      throw new Error('No submissions selected');
    }
    
    const submissionsCheck = await client.query(
      'SELECT id, vendor_id, rfq_id FROM rfq_submissions WHERE id = ANY($1) AND rfq_id = $2',
      [submissionIds, rfqId]
    );
    
    if (submissionsCheck.rows.length !== submissionIds.length) {
      throw new Error('One or more selected submissions are invalid');
    }
    
    // Verify vendor selections match submission vendors
    for (const [itemId, bid] of Object.entries(selectedBids)) {
      const submission = submissionsCheck.rows.find(s => s.id === bid.submissionId);
      if (!submission || submission.vendor_id !== bid.vendorId) {
        throw new Error(`Invalid vendor selection for item ${itemId}`);
      }
    }
    
    return true;
  }
  
  /**
   * Calculate commission structure for selected bids
   * @param {Object} selectedBids - Selected vendor bids
   * @param {Object} commissionConfig - Commission configuration
   * @returns {Promise<Object>} Commission structure
   */
  static async calculateCommissions(selectedBids, commissionConfig = {}) {
    const commissionStructure = {
      type: commissionConfig.type || 'percentage', // 'percentage' or 'fixed'
      default_rate: commissionConfig.default_rate || 0,
      item_overrides: commissionConfig.item_overrides || {},
      vendor_rates: commissionConfig.vendor_rates || {},
      calculations: {}
    };
    
    for (const [itemId, bid] of Object.entries(selectedBids)) {
      const baseAmount = bid.totalPrice || (bid.unitPrice * bid.quantity);
      
      // Determine commission rate for this item/vendor
      let commissionRate = commissionStructure.default_rate;
      
      // Check for item-specific override
      if (commissionStructure.item_overrides[itemId]) {
        commissionRate = commissionStructure.item_overrides[itemId];
      }
      // Check for vendor-specific rate
      else if (commissionStructure.vendor_rates[bid.vendorId]) {
        commissionRate = commissionStructure.vendor_rates[bid.vendorId];
      }
      
      // Calculate commission amount
      let commissionAmount = 0;
      if (commissionStructure.type === 'percentage') {
        commissionAmount = baseAmount * (commissionRate / 100);
      } else if (commissionStructure.type === 'fixed') {
        commissionAmount = commissionRate;
      }
      
      commissionStructure.calculations[itemId] = {
        base_amount: baseAmount,
        commission_rate: commissionRate,
        commission_amount: commissionAmount,
        final_amount: baseAmount + commissionAmount
      };
    }
    
    return commissionStructure;
  }
  
  /**
   * Calculate total amounts including taxes and margins
   * @param {Object} selectedBids - Selected vendor bids
   * @param {Object} commissionStructure - Commission structure
   * @param {Object} quoteData - Quote configuration
   * @returns {Promise<Object>} Total calculations
   */
  static async calculateTotals(selectedBids, commissionStructure, quoteData) {
    let subtotal = 0;
    let totalCommission = 0;
    
    // Sum up all item amounts and commissions
    for (const [itemId, calculation] of Object.entries(commissionStructure.calculations)) {
      subtotal += calculation.base_amount;
      totalCommission += calculation.commission_amount;
    }
    
    // Apply margin
    const marginAmount = subtotal * ((quoteData.margin_percentage || 0) / 100);
    
    // Calculate taxes
    const taxableAmount = subtotal + totalCommission + marginAmount;
    const taxes = taxableAmount * ((quoteData.tax_rate || 0) / 100);
    
    // Final total
    const finalTotal = taxableAmount + taxes;
    
    return {
      subtotal,
      totalCommission,
      marginAmount,
      taxes,
      finalTotal,
      breakdown: {
        vendor_costs: subtotal,
        commission: totalCommission,
        margin: marginAmount,
        taxes: taxes,
        total: finalTotal
      }
    };
  }
  
  /**
   * Get submissions formatted for quote generation
   * @param {number} rfqId - RFQ ID
   * @param {number} userId - User ID for access control
   * @returns {Promise<Object>} Formatted submissions data
   */
  static async getSubmissionsForQuote(rfqId, userId) {
    try {
      // Verify user has access to RFQ
      const rfqCheck = await query(
        'SELECT id, title, items, creator_id FROM rfqs WHERE id = $1 AND creator_id = $2',
        [rfqId, userId]
      );
      
      if (rfqCheck.rows.length === 0) {
        throw new Error('RFQ not found or access denied');
      }
      
      const rfq = rfqCheck.rows[0];
      
      // Get all submissions with vendor details
      const submissionsQuery = `
        SELECT 
          rs.*,
          v.name as vendor_name,
          v.contact_email as vendor_email,
          v.performance_score,
          v.certifications,
          v.address,
          (
            SELECT json_agg(
              json_build_object(
                'id', bi.id,
                'rfq_item_id', bi.rfq_item_id,
                'unit_price', bi.unit_price,
                'total_price', bi.total_price,
                'specifications', bi.specifications,
                'alternatives', bi.alternatives,
                'delivery_days', bi.delivery_days,
                'notes', bi.notes
              )
            )
            FROM bid_items bi 
            WHERE bi.submission_id = rs.id
          ) as bid_items
        FROM rfq_submissions rs
        LEFT JOIN vendors v ON rs.vendor_id = v.id
        WHERE rs.rfq_id = $1
        ORDER BY rs.submitted_at DESC
      `;
      
      const submissionsResult = await query(submissionsQuery, [rfqId]);
      
      // Format data for quote generation
      const formattedData = {
        rfq: {
          id: rfq.id,
          title: rfq.title,
          items: rfq.items
        },
        submissions: submissionsResult.rows.map(submission => ({
          id: submission.id,
          vendor_id: submission.vendor_id,
          vendor_name: submission.vendor_name,
          vendor_email: submission.vendor_email,
          performance_score: submission.performance_score,
          total_amount: submission.total_amount,
          submitted_at: submission.submitted_at,
          bid_items: submission.bid_items || [],
          vendor_details: {
            certifications: submission.certifications,
            address: submission.address,
            performance_score: submission.performance_score
          }
        }))
      };
      
      return formattedData;
    } catch (error) {
      console.error('Error in QuoteGenerationService.getSubmissionsForQuote:', error);
      throw error;
    }
  }
  
  /**
   * Update quote selections
   * @param {number} quoteId - Quote ID
   * @param {Object} newSelections - New selection data
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Updated quote
   */
  static async updateQuoteSelections(quoteId, newSelections, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);
      
      // Get existing quote
      const existingQuote = await client.query(
        'SELECT * FROM client_quotes WHERE id = $1 AND creator_id = $2',
        [quoteId, userId]
      );
      
      if (existingQuote.rows.length === 0) {
        throw new Error('Quote not found or access denied');
      }
      
      const quote = existingQuote.rows[0];
      
      // Only allow updates for draft quotes
      if (quote.status !== 'draft') {
        throw new Error('Can only update draft quotes');
      }
      
      // Validate new selections
      await this.validateSelections(quote.rfq_id, newSelections.selectedBids, client);
      
      // Recalculate commission and totals
      const commissionStructure = await this.calculateCommissions(
        newSelections.selectedBids, 
        newSelections.commissionConfig
      );
      
      const totals = await this.calculateTotals(
        newSelections.selectedBids, 
        commissionStructure, 
        newSelections
      );
      
      // Update quote
      const updateQuery = `
        UPDATE client_quotes 
        SET 
          selected_bids = $1,
          commission_structure = $2,
          total_amount = $3,
          margin_percentage = $4,
          taxes = $5,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $6
        RETURNING *
      `;
      
      const updateParams = [
        JSON.stringify(newSelections.selectedBids),
        JSON.stringify(commissionStructure),
        totals.finalTotal,
        newSelections.margin_percentage || quote.margin_percentage,
        totals.taxes,
        quoteId
      ];
      
      const result = await client.query(updateQuery, updateParams);
      
      await commitTransaction(client);
      
      // Return updated quote with full details
      return await ClientQuote.findById(quoteId, userId);
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in QuoteGenerationService.updateQuoteSelections:', error);
      throw error;
    }
  }
}

module.exports = QuoteGenerationService;