# Enhanced RFQ API Documentation

## Overview

The RFQ API has been enhanced with comprehensive filtering, search capabilities, cursor-based pagination, bulk operations, and improved error handling. This document outlines all the new features and endpoints.

## Enhanced Endpoints

### 1. GET /api/rfqs - Enhanced List RFQs

**Enhanced Query Parameters:**

```typescript
interface EnhancedRFQListParams {
  // Pagination
  page?: number;           // Page number (1-based, default: 1)
  limit?: number;          // Items per page (1-100, default: 10)
  cursor?: string;         // Base64 encoded cursor for cursor-based pagination
  
  // Filtering
  search?: string;         // Search in title, description, and item names
  status?: 'draft' | 'sent' | 'in_progress' | 'closed' | 'cancelled';
  creator_id?: number;     // Filter by creator
  due_date_from?: string;  // ISO date string
  due_date_to?: string;    // ISO date string
  currency?: 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CNY';
  category?: string;       // Filter by item category
  vendor_id?: number;      // Filter RFQs that include specific vendor
  
  // Sorting
  sort_by?: 'created_at' | 'updated_at' | 'due_date' | 'title' | 'response_rate';
  sort_order?: 'asc' | 'desc';
  
  // Include additional data
  include_analytics?: boolean;    // Include analytics data
  include_submissions?: boolean;  // Include submission counts
}
```

**Enhanced Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Office Supplies RFQ",
      "description": "Request for office supplies",
      "status": "sent",
      "due_date": "2024-02-15T00:00:00.000Z",
      "currency": "USD",
      "creator_id": 1,
      "creator_email": "<EMAIL>",
      "item_count": 5,
      "invitation_count": 10,
      "submission_count": 7,
      "response_rate": 70.00,
      "created_at": "2024-01-15T10:00:00.000Z",
      "updated_at": "2024-01-15T10:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3,
    "hasNext": true,
    "hasPrevious": false
  },
  "meta": {
    "total_count": 25,
    "has_next": true,
    "has_previous": false,
    "next_cursor": "eyJjcmVhdGVkX2F0IjoiMjAyNC0wMS0xNVQxMDowMDowMC4wMDBaIn0=",
    "previous_cursor": null
  }
}
```

### 2. POST /api/rfqs/search - Advanced Search

**Request Body:**

```json
{
  "query": "office supplies",
  "filters": {
    "status": ["draft", "sent"],
    "date_range": {
      "start": "2024-01-01T00:00:00.000Z",
      "end": "2024-12-31T23:59:59.999Z"
    },
    "creator_ids": [1, 2, 3],
    "vendor_ids": [10, 20],
    "categories": ["office", "supplies"],
    "currencies": ["USD", "EUR"],
    "amount_range": {
      "min": 1000,
      "max": 50000
    }
  },
  "page": 1,
  "limit": 20,
  "sort_by": "relevance",
  "sort_order": "desc"
}
```

**Response:**

```json
{
  "success": true,
  "data": [...],
  "pagination": {...},
  "search_meta": {
    "query": "office supplies",
    "total_results": 15,
    "search_time_ms": 45
  }
}
```

### 3. PUT /api/rfqs/bulk - Bulk Update RFQs

**Request Body:**

```json
{
  "rfq_ids": [1, 2, 3, 4],
  "updates": {
    "status": "sent",
    "due_date": "2024-03-01T00:00:00.000Z",
    "currency": "USD"
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "updated_count": 4,
    "rfqs": [
      {
        "id": 1,
        "title": "RFQ 1",
        "status": "sent",
        "updated_at": "2024-01-15T12:00:00.000Z"
      }
    ]
  },
  "message": "Successfully updated 4 RFQs"
}
```

### 4. DELETE /api/rfqs/bulk - Bulk Delete RFQs

**Request Body:**

```json
{
  "rfq_ids": [1, 2, 3],
  "force": false
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "deleted_count": 3,
    "rfqs": [
      {
        "id": 1,
        "title": "RFQ 1",
        "status": "cancelled"
      }
    ]
  },
  "message": "Successfully deleted 3 RFQs"
}
```

### 5. GET /api/rfqs/summary - Summary Statistics

**Response:**

```json
{
  "success": true,
  "data": {
    "total_rfqs": 50,
    "draft_count": 10,
    "sent_count": 15,
    "in_progress_count": 20,
    "closed_count": 4,
    "cancelled_count": 1,
    "avg_response_rate": 65.5,
    "overdue_count": 3
  },
  "message": "Summary statistics retrieved successfully"
}
```

### 6. GET /api/rfqs/export - Export RFQs

**Query Parameters:**

```typescript
interface ExportParams {
  format?: 'csv' | 'json' | 'xlsx';
  filters?: {
    status?: string[];
    date_range?: {
      start?: string;
      end?: string;
    };
    creator_ids?: number[];
  };
  include_submissions?: boolean;
  include_analytics?: boolean;
}
```

**Response:**
- Content-Type varies based on format
- Content-Disposition header for file download
- Raw data in requested format

## Enhanced Error Handling

### Validation Errors (400)

```json
{
  "success": false,
  "message": "Invalid query parameters",
  "errors": [
    {
      "field": "limit",
      "message": "\"limit\" must be less than or equal to 100"
    },
    {
      "field": "status",
      "message": "\"status\" must be one of [draft, sent, in_progress, closed, cancelled]"
    }
  ]
}
```

### Access Denied (403)

```json
{
  "success": false,
  "message": "Access denied or some RFQs not found",
  "error": "Some RFQs not found or access denied"
}
```

### Conflict Errors (409)

```json
{
  "success": false,
  "message": "Some RFQs cannot be deleted due to their current status",
  "error": "Cannot delete RFQs with status: in_progress, closed"
}
```

### Server Errors (500)

```json
{
  "success": false,
  "message": "Failed to fetch RFQs",
  "error": "Internal server error"
}
```

## Cursor-Based Pagination

For better performance with large datasets, use cursor-based pagination:

1. **First Request:** `GET /api/rfqs?limit=10`
2. **Next Page:** `GET /api/rfqs?cursor=eyJjcmVhdGVkX2F0IjoiMjAyNC0wMS0xNVQxMDowMDowMC4wMDBaIn0=&limit=10`

The cursor is a base64-encoded timestamp that ensures consistent pagination even when new records are added.

## Performance Optimizations

1. **Database Indexing:** Ensure proper indexes on frequently filtered columns
2. **Query Optimization:** Use EXISTS clauses for JSON array searches
3. **Caching:** Consider implementing Redis caching for frequently accessed data
4. **Pagination:** Use cursor-based pagination for large datasets

## Security Considerations

1. **Input Validation:** All inputs are validated using Joi schemas
2. **SQL Injection Prevention:** Parameterized queries are used throughout
3. **Access Control:** User permissions are checked for all operations
4. **Rate Limiting:** Consider implementing rate limiting for bulk operations
5. **Audit Logging:** All operations are logged for compliance

## Usage Examples

### JavaScript/Node.js

```javascript
const axios = require('axios');

const api = axios.create({
  baseURL: 'http://localhost:3001/api/rfqs',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

// Enhanced filtering
const rfqs = await api.get('/', {
  params: {
    search: 'office supplies',
    status: 'sent',
    sort_by: 'response_rate',
    sort_order: 'desc',
    include_analytics: true
  }
});

// Advanced search
const searchResults = await api.post('/search', {
  query: 'urgent procurement',
  filters: {
    status: ['sent', 'in_progress'],
    date_range: {
      start: '2024-01-01',
      end: '2024-12-31'
    }
  }
});

// Bulk operations
const bulkUpdate = await api.put('/bulk', {
  rfq_ids: [1, 2, 3],
  updates: { status: 'sent' }
});
```

### Frontend Integration

```typescript
// React hook for enhanced RFQ list
const useEnhancedRFQList = (filters: RFQFilters) => {
  const [rfqs, setRfqs] = useState([]);
  const [pagination, setPagination] = useState({});
  const [loading, setLoading] = useState(false);

  const fetchRFQs = useCallback(async () => {
    setLoading(true);
    try {
      const response = await api.get('/rfqs', { params: filters });
      setRfqs(response.data.data);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Failed to fetch RFQs:', error);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchRFQs();
  }, [fetchRFQs]);

  return { rfqs, pagination, loading, refetch: fetchRFQs };
};
```

## Migration Notes

### Breaking Changes
- None - all changes are backward compatible

### New Features
- Enhanced filtering capabilities
- Cursor-based pagination
- Bulk operations
- Advanced search
- Export functionality
- Improved error handling

### Recommended Updates
1. Update frontend components to use new filtering options
2. Implement cursor-based pagination for better performance
3. Add bulk operation capabilities to admin interfaces
4. Integrate advanced search functionality
5. Add export features for reporting