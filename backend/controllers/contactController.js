const ContactService = require('../services/ContactService');
const ContactHierarchyService = require('../services/ContactHierarchyService');
const { 
  validateContactCreate, 
  validateContactUpdate, 
  validateContactSearch 
} = require('../validation/contactValidation');

class ContactController {
  // Get all contacts with filtering and pagination
  static async getAllContacts(req, res) {
    try {
      const searchParams = {
        search: req.query.search,
        account_id: req.query.account_id ? parseInt(req.query.account_id) : undefined,
        department: req.query.department,
        level: req.query.level,
        lead_source: req.query.lead_source,
        status: req.query.status,
        owner_id: req.query.owner_id ? parseInt(req.query.owner_id) : undefined,
        reports_to_id: req.query.reports_to_id ? parseInt(req.query.reports_to_id) : undefined,
        has_direct_reports: req.query.has_direct_reports === 'true',
        created_after: req.query.created_after,
        created_before: req.query.created_before,
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10
      };

      // Validate search parameters
      const validation = await validateContactSearch(searchParams);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Invalid search parameters',
          errors: validation.errors
        });
      }

      const result = await ContactService.searchContacts(validation.data);

      res.json({
        success: true,
        data: result,
        message: `Retrieved ${result.contacts.length} contacts`
      });
    } catch (error) {
      console.error('Error in getAllContacts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve contacts',
        details: error.message
      });
    }
  }

  // Get contact by ID
  static async getContactById(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid contact ID'
        });
      }

      const contact = await ContactService.getContactById(parseInt(id));

      res.json({
        success: true,
        data: contact,
        message: 'Contact retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getContactById:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: 'Contact not found'
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to retrieve contact',
        details: error.message
      });
    }
  }

  // Transform camelCase to snake_case for database compatibility
  static transformContactData(data) {
    const transformed = {};
    
    // Field mappings from camelCase to snake_case
    const fieldMappings = {
      accountId: 'account_id',
      firstName: 'first_name',
      lastName: 'last_name',
      mobilePhone: 'mobile_phone',
      homePhone: 'home_phone',
      otherPhone: 'other_phone',
      mailingAddress: 'mailing_address',
      otherAddress: 'other_address',
      reportsToId: 'reports_to_id',
      assistantName: 'assistant_name',
      assistantPhone: 'assistant_phone',
      leadSource: 'lead_source',
      doNotCall: 'do_not_call',
      hasOptedOutOfEmail: 'has_opted_out_of_email',
      ownerId: 'owner_id',
      customFields: 'custom_fields'
    };

    // Transform fields
    Object.keys(data).forEach(key => {
      const dbField = fieldMappings[key] || key;
      transformed[dbField] = data[key];
    });

    // Handle empty strings for optional fields
    if (transformed.description === '') {
      delete transformed.description;
    }
    if (transformed.languages === '') {
      delete transformed.languages;
    }

    return transformed;
  }

  // Create new contact
  static async createContact(req, res) {
    try {
      const userId = req.user?.id || 1; // Get from auth middleware

      // Transform camelCase to snake_case
      const transformedData = ContactController.transformContactData(req.body);

      // Validate contact data
      const validation = await validateContactCreate(transformedData);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          errors: validation.errors
        });
      }

      const contact = await ContactService.createContact(validation.data, userId);

      res.status(201).json({
        success: true,
        data: contact,
        message: 'Contact created successfully'
      });
    } catch (error) {
      console.error('Error in createContact:', error);
      
      if (error.message.includes('Validation failed') || 
          error.message.includes('already exists') ||
          error.message.includes('Hierarchy validation failed')) {
        return res.status(400).json({
          success: false,
          error: error.message
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to create contact',
        details: error.message
      });
    }
  }

  // Update contact
  static async updateContact(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id || 1;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid contact ID'
        });
      }

      // Transform camelCase to snake_case
      const transformedData = ContactController.transformContactData(req.body);

      // Validate update data
      const validation = await validateContactUpdate(transformedData);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          errors: validation.errors
        });
      }

      const contact = await ContactService.updateContact(parseInt(id), validation.data, userId);

      res.json({
        success: true,
        data: contact,
        message: 'Contact updated successfully'
      });
    } catch (error) {
      console.error('Error in updateContact:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: 'Contact not found'
        });
      }

      if (error.message.includes('Validation failed') || 
          error.message.includes('already exists') ||
          error.message.includes('Hierarchy validation failed')) {
        return res.status(400).json({
          success: false,
          error: error.message
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to update contact',
        details: error.message
      });
    }
  }

  // Delete contact (soft delete)
  static async deleteContact(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id || 1;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid contact ID'
        });
      }

      await ContactService.deleteContact(parseInt(id), userId);

      res.json({
        success: true,
        message: 'Contact deleted successfully'
      });
    } catch (error) {
      console.error('Error in deleteContact:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: 'Contact not found'
        });
      }

      if (error.message.includes('direct reports')) {
        return res.status(400).json({
          success: false,
          error: error.message
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to delete contact',
        details: error.message
      });
    }
  }

  // Get contacts for a specific account
  static async getAccountContacts(req, res) {
    try {
      const { accountId } = req.params;
      const includeHierarchy = req.query.include_hierarchy === 'true';
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 50;

      if (!accountId || isNaN(parseInt(accountId))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const options = {
        page,
        limit,
        includeHierarchy,
        filters: {
          department: req.query.department,
          level: req.query.level,
          status: req.query.status
        }
      };

      const result = await ContactService.getAccountContacts(parseInt(accountId), options);

      res.json({
        success: true,
        data: result,
        message: `Retrieved contacts for account ${accountId}`
      });
    } catch (error) {
      console.error('Error in getAccountContacts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve account contacts',
        details: error.message
      });
    }
  }

  // Get contact hierarchy/org chart
  static async getContactHierarchy(req, res) {
    try {
      const { accountId } = req.params;
      const rootContactId = req.query.root_contact_id ? parseInt(req.query.root_contact_id) : null;

      if (!accountId || isNaN(parseInt(accountId))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const result = await ContactService.getContactHierarchy(parseInt(accountId), rootContactId);

      res.json({
        success: true,
        data: result,
        message: 'Contact hierarchy retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getContactHierarchy:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve contact hierarchy',
        details: error.message
      });
    }
  }

  // Update contact hierarchy position
  static async updateContactHierarchy(req, res) {
    try {
      const { id } = req.params;
      const { reports_to_id } = req.body;
      const userId = req.user?.id || 1;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid contact ID'
        });
      }

      const newReportsToId = reports_to_id ? parseInt(reports_to_id) : null;
      const contact = await ContactService.updateContactHierarchy(parseInt(id), newReportsToId, userId);

      res.json({
        success: true,
        data: contact,
        message: 'Contact hierarchy updated successfully'
      });
    } catch (error) {
      console.error('Error in updateContactHierarchy:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: 'Contact not found'
        });
      }

      if (error.message.includes('Hierarchy validation failed')) {
        return res.status(400).json({
          success: false,
          error: error.message
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to update contact hierarchy',
        details: error.message
      });
    }
  }

  // Bulk create contacts
  static async bulkCreateContacts(req, res) {
    try {
      const { contacts } = req.body;
      const userId = req.user?.id || 1;

      if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Contacts array is required'
        });
      }

      if (contacts.length > 100) {
        return res.status(400).json({
          success: false,
          error: 'Cannot create more than 100 contacts at once'
        });
      }

      const result = await ContactService.bulkCreateContacts(contacts, userId);

      res.status(201).json({
        success: true,
        data: result,
        message: `Bulk create completed: ${result.successful} successful, ${result.failed} failed`
      });
    } catch (error) {
      console.error('Error in bulkCreateContacts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to bulk create contacts',
        details: error.message
      });
    }
  }

  // Bulk update contacts
  static async bulkUpdateContacts(req, res) {
    try {
      const { contacts } = req.body;
      const userId = req.user?.id || 1;

      if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Contacts array is required'
        });
      }

      if (contacts.length > 100) {
        return res.status(400).json({
          success: false,
          error: 'Cannot update more than 100 contacts at once'
        });
      }

      const result = await ContactService.bulkUpdateContacts(contacts, userId);

      res.json({
        success: true,
        data: result,
        message: `Bulk update completed: ${result.successful} successful, ${result.failed} failed`
      });
    } catch (error) {
      console.error('Error in bulkUpdateContacts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to bulk update contacts',
        details: error.message
      });
    }
  }

  // Find duplicate contacts
  static async findDuplicates(req, res) {
    try {
      const { email, first_name, last_name, account_id } = req.query;

      if (!email && (!first_name || !last_name)) {
        return res.status(400).json({
          success: false,
          error: 'Either email or both first_name and last_name are required'
        });
      }

      const searchCriteria = {
        email,
        firstName: first_name,
        lastName: last_name,
        accountId: account_id ? parseInt(account_id) : undefined
      };

      const duplicates = await ContactService.findDuplicateContacts(searchCriteria);

      res.json({
        success: true,
        data: duplicates,
        message: `Found ${duplicates.length} potential duplicates`
      });
    } catch (error) {
      console.error('Error in findDuplicates:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to find duplicate contacts',
        details: error.message
      });
    }
  }

  // Merge contacts
  static async mergeContacts(req, res) {
    try {
      const { primary_contact_id, duplicate_contact_ids } = req.body;
      const userId = req.user?.id || 1;

      if (!primary_contact_id || !duplicate_contact_ids || !Array.isArray(duplicate_contact_ids)) {
        return res.status(400).json({
          success: false,
          error: 'Primary contact ID and array of duplicate contact IDs are required'
        });
      }

      const mergedContact = await ContactService.mergeContacts(
        parseInt(primary_contact_id), 
        duplicate_contact_ids.map(id => parseInt(id)), 
        userId
      );

      res.json({
        success: true,
        data: mergedContact,
        message: 'Contacts merged successfully'
      });
    } catch (error) {
      console.error('Error in mergeContacts:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: error.message
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to merge contacts',
        details: error.message
      });
    }
  }

  // Get contact statistics
  static async getContactStatistics(req, res) {
    try {
      const accountId = req.query.account_id ? parseInt(req.query.account_id) : null;
      const stats = await ContactService.getContactStatistics(accountId);

      res.json({
        success: true,
        data: stats,
        message: 'Contact statistics retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getContactStatistics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve contact statistics',
        details: error.message
      });
    }
  }

  // Export contacts to CSV
  static async exportContacts(req, res) {
    try {
      const filters = {
        search: req.query.search,
        account_id: req.query.account_id ? parseInt(req.query.account_id) : undefined,
        department: req.query.department,
        level: req.query.level,
        status: req.query.status
      };

      const csvData = await ContactService.exportContacts(filters);

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=contacts.csv');

      // Convert to CSV format
      const csvContent = [
        csvData.headers.join(','),
        ...csvData.rows.map(row => row.map(cell => `"${cell}"`).join(','))
      ].join('\n');

      res.send(csvContent);
    } catch (error) {
      console.error('Error in exportContacts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to export contacts',
        details: error.message
      });
    }
  }

  // Get contact audit history
  static async getContactAuditHistory(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid contact ID'
        });
      }

      const auditHistory = await ContactService.getContactAuditHistory(parseInt(id));

      res.json({
        success: true,
        data: auditHistory,
        message: 'Contact audit history retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getContactAuditHistory:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve contact audit history',
        details: error.message
      });
    }
  }

  // Get unique departments
  static async getDepartments(req, res) {
    try {
      const accountId = req.query.account_id ? parseInt(req.query.account_id) : null;
      const departments = await ContactService.getDepartments(accountId);

      res.json({
        success: true,
        data: departments,
        message: 'Departments retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getDepartments:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve departments',
        details: error.message
      });
    }
  }

  // Get unique lead sources
  static async getLeadSources(req, res) {
    try {
      const leadSources = await ContactService.getLeadSources();

      res.json({
        success: true,
        data: leadSources,
        message: 'Lead sources retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getLeadSources:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve lead sources',
        details: error.message
      });
    }
  }

  // Update communication preferences
  static async updateCommunicationPreferences(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id || 1;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid contact ID'
        });
      }

      const contact = await ContactService.updateCommunicationPreferences(
        parseInt(id), 
        req.body, 
        userId
      );

      res.json({
        success: true,
        data: contact,
        message: 'Communication preferences updated successfully'
      });
    } catch (error) {
      console.error('Error in updateCommunicationPreferences:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: 'Contact not found'
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to update communication preferences',
        details: error.message
      });
    }
  }
}

module.exports = ContactController;