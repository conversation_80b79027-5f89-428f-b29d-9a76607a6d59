const Contract = require('../models/Contract');
const Joi = require('joi');

// Validation schemas
const contractCreateSchema = Joi.object({
  vendor_id: Joi.number().integer().positive().required(),
  title: Joi.string().min(3).max(255).required(),
  status: Joi.string().valid('draft', 'ready_for_signature', 'signed', 'active', 'terminated', 'disputed').default('draft'),
  parties: Joi.object({
    vendor: Joi.string().required(),
    client: Joi.string().required(),
    additional: Joi.array().items(Joi.string()).optional()
  }).required(),
  clauses: Joi.object({
    payment_terms: Joi.string().required(),
    deliverables: Joi.string().required(),
    additional_clauses: Joi.string().optional(),
    termination_clause: Joi.string().optional()
  }).required(),
  start_date: Joi.date().required(),
  end_date: Joi.date().greater(Joi.ref('start_date')).required(),
  milestones: Joi.array().items(
    Joi.object({
      name: Joi.string().required(),
      description: Joi.string().optional(),
      due_date: Joi.date().required(),
      completed: Joi.boolean().default(false)
    })
  ).optional()
});

const contractUpdateSchema = Joi.object({
  title: Joi.string().min(3).max(255).optional(),
  status: Joi.string().valid('draft', 'ready_for_signature', 'signed', 'active', 'terminated', 'disputed').optional(),
  parties: Joi.object({
    vendor: Joi.string().required(),
    client: Joi.string().required(),
    additional: Joi.array().items(Joi.string()).optional()
  }).optional(),
  clauses: Joi.object({
    payment_terms: Joi.string().required(),
    deliverables: Joi.string().required(),
    additional_clauses: Joi.string().optional(),
    termination_clause: Joi.string().optional()
  }).optional(),
  start_date: Joi.date().optional(),
  end_date: Joi.date().optional(),
  value: Joi.number().positive().optional(),
  currency: Joi.string().length(3).optional(),
  additional_clauses: Joi.string().optional(),
  docusign_envelope_id: Joi.string().optional(),
  milestones: Joi.array().items(
    Joi.object({
      name: Joi.string().required(),
      description: Joi.string().optional(),
      due_date: Joi.date().required(),
      completed: Joi.boolean().default(false),
      completed_date: Joi.date().optional()
    })
  ).optional()
}).min(1);

const filtersSchema = Joi.object({
  search: Joi.string().optional(),
  status: Joi.string().valid('draft', 'ready_for_signature', 'signed', 'active', 'terminated', 'disputed').optional(),
  vendor_id: Joi.number().integer().positive().optional(),
  start_date: Joi.date().optional(),
  end_date: Joi.date().optional()
});

const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10)
});

// Controller functions
const getContracts = async (req, res) => {
  try {
    // Validate query parameters
    const { error: filtersError, value: filters } = filtersSchema.validate(req.query);
    if (filtersError) {
      return res.status(400).json({
        success: false,
        message: 'Invalid filter parameters',
        errors: filtersError.details
      });
    }

    const { error: paginationError, value: pagination } = paginationSchema.validate({
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 10
    });
    if (paginationError) {
      return res.status(400).json({
        success: false,
        message: 'Invalid pagination parameters',
        errors: paginationError.details
      });
    }

    const result = await Contract.findAll(filters, pagination, req.user.id);

    res.json({
      success: true,
      data: result.contracts,
      pagination: result.pagination,
      message: 'Contracts retrieved successfully'
    });
  } catch (error) {
    console.error('Error in getContracts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve contracts',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

const getContractById = async (req, res) => {
  try {
    const contractId = parseInt(req.params.id);
    
    if (isNaN(contractId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid contract ID'
      });
    }

    const contract = await Contract.findById(contractId, req.user.id);

    if (!contract) {
      return res.status(404).json({
        success: false,
        message: 'Contract not found'
      });
    }

    res.json({
      success: true,
      data: contract,
      message: 'Contract retrieved successfully'
    });
  } catch (error) {
    console.error('Error in getContractById:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve contract',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

const createContract = async (req, res) => {
  try {
    // Validate request body
    const { error, value: contractData } = contractCreateSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.details
      });
    }

    const contract = await Contract.create(contractData, req.user.id);

    res.status(201).json({
      success: true,
      data: contract,
      message: 'Contract created successfully'
    });
  } catch (error) {
    console.error('Error in createContract:', error);
    
    if (error.code === '23503') { // Foreign key violation
      return res.status(400).json({
        success: false,
        message: 'Invalid vendor ID or vendor not found'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create contract',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

const updateContract = async (req, res) => {
  try {
    const contractId = parseInt(req.params.id);
    
    if (isNaN(contractId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid contract ID'
      });
    }

    // Validate request body
    const { error, value: updates } = contractUpdateSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.details
      });
    }

    // Validate date logic if both dates are provided
    if (updates.start_date && updates.end_date && new Date(updates.end_date) <= new Date(updates.start_date)) {
      return res.status(400).json({
        success: false,
        message: 'End date must be after start date'
      });
    }

    const contract = await Contract.update(contractId, updates, req.user.id);

    res.json({
      success: true,
      data: contract,
      message: 'Contract updated successfully'
    });
  } catch (error) {
    console.error('Error in updateContract:', error);
    
    if (error.message === 'Contract not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'Contract not found'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update contract',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

const deleteContract = async (req, res) => {
  try {
    const contractId = parseInt(req.params.id);
    
    if (isNaN(contractId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid contract ID'
      });
    }

    await Contract.delete(contractId, req.user.id);

    res.json({
      success: true,
      message: 'Contract deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteContract:', error);
    
    if (error.message === 'Contract not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'Contract not found'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to delete contract',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

const getContractTemplates = async (req, res) => {
  try {
    const templates = await Contract.getTemplates();

    res.json({
      success: true,
      data: templates,
      message: 'Contract templates retrieved successfully'
    });
  } catch (error) {
    console.error('Error in getContractTemplates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve contract templates',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

const createContractAmendment = async (req, res) => {
  try {
    const contractId = parseInt(req.params.id);
    
    if (isNaN(contractId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid contract ID'
      });
    }

    const amendmentSchema = Joi.object({
      description: Joi.string().required(),
      changes: Joi.object().required()
    });

    const { error, value: amendmentData } = amendmentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.details
      });
    }

    const amendment = await Contract.createAmendment(contractId, amendmentData, req.user.id);

    res.status(201).json({
      success: true,
      data: amendment,
      message: 'Contract amendment created successfully'
    });
  } catch (error) {
    console.error('Error in createContractAmendment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create contract amendment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

const updateMilestone = async (req, res) => {
  try {
    const milestoneId = parseInt(req.params.milestoneId);
    
    if (isNaN(milestoneId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid milestone ID'
      });
    }

    const milestoneSchema = Joi.object({
      completed: Joi.boolean().required(),
      completed_date: Joi.date().optional()
    });

    const { error, value: updates } = milestoneSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.details
      });
    }

    // Set completed_date to current time if marking as completed and no date provided
    if (updates.completed && !updates.completed_date) {
      updates.completed_date = new Date().toISOString();
    }

    const milestone = await Contract.updateMilestone(milestoneId, updates, req.user.id);

    res.json({
      success: true,
      data: milestone,
      message: 'Milestone updated successfully'
    });
  } catch (error) {
    console.error('Error in updateMilestone:', error);
    
    if (error.message === 'Milestone not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'Milestone not found'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update milestone',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// DocuSign integration placeholder
const initiateESignature = async (req, res) => {
  try {
    const contractId = parseInt(req.params.id);
    
    if (isNaN(contractId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid contract ID'
      });
    }

    // TODO: Implement DocuSign integration
    // This is a placeholder for the e-signature functionality
    
    res.status(501).json({
      success: false,
      message: 'E-signature functionality not yet implemented. DocuSign integration required.'
    });
  } catch (error) {
    console.error('Error in initiateESignature:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initiate e-signature',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getContracts,
  getContractById,
  createContract,
  updateContract,
  deleteContract,
  getContractTemplates,
  createContractAmendment,
  updateMilestone,
  initiateESignature
};