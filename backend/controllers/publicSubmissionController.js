const RFQSubmission = require('../models/RFQSubmission');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/rfq-submissions');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const fileFilter = (req, file, cb) => {
  // Allowed file types for RFQ submissions
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/png',
    'image/gif',
    'text/plain',
    'text/csv'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`File type ${file.mimetype} is not allowed`), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 10 // Maximum 10 files per submission
  },
  fileFilter: fileFilter
});

// Validate invitation token and get RFQ details
const validateToken = async (req, res) => {
  try {
    const { token } = req.params;
    console.log('Validating token:', token);

    const validation = await RFQSubmission.validateToken(token);

    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: validation.error,
      });
    }

    res.json({
      success: true,
      data: {
        invitation: validation.invitation,
        has_existing_submission: validation.has_existing_submission,
        existing_submission_id: validation.existing_submission_id,
      },
      message: 'Token is valid',
    });
  } catch (error) {
    console.error('Error validating token:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate token',
      error: error.message,
    });
  }
};

// Get RFQ details for public submission (via token)
const getRFQForSubmission = async (req, res) => {
  try {
    const { token } = req.params;
    console.log('Getting RFQ details for token:', token);

    const validation = await RFQSubmission.validateToken(token);

    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: validation.error,
      });
    }

    const invitation = validation.invitation;

    // Update the invitation viewed_at timestamp if not already set
    if (!invitation.viewed_at) {
      try {
        const { query } = require('../config/database');
        await query(
          'UPDATE rfq_invitations SET viewed_at = CURRENT_TIMESTAMP, status = CASE WHEN status = \'sent\' THEN \'viewed\' ELSE status END WHERE token = $1',
          [token]
        );
        console.log('Updated invitation viewed_at timestamp for token:', token);
      } catch (updateError) {
        console.error('Error updating viewed_at timestamp:', updateError);
        // Don't fail the request if we can't update the timestamp
      }
    }

    // Structure the response for public consumption (hide sensitive data)
    const publicRFQData = {
      rfq_id: invitation.rfq_id,
      title: invitation.rfq_title,
      description: invitation.rfq_description,
      items: invitation.rfq_items,
      due_date: invitation.rfq_due_date,
      form_config: invitation.rfq_form_config,
      terms: invitation.rfq_terms,
      allow_partial_selection: invitation.rfq_allow_partial_selection,
      partial_selection_config: invitation.rfq_partial_selection_config,
      vendor_name: invitation.vendor_name,
      vendor_email: invitation.vendor_email,
      invitation_status: invitation.status,
      has_existing_submission: validation.has_existing_submission,
      existing_submission_id: validation.existing_submission_id,
    };

    res.json({
      success: true,
      data: publicRFQData,
      message: 'RFQ details retrieved successfully',
    });
  } catch (error) {
    console.error('Error getting RFQ for submission:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get RFQ details',
      error: error.message,
    });
  }
};

// Get existing submission by token
const getSubmissionByToken = async (req, res) => {
  try {
    const { token } = req.params;
    console.log('Getting submission by token:', token);

    const submission = await RFQSubmission.findByToken(token);

    if (!submission) {
      return res.status(404).json({
        success: false,
        message: 'Submission not found',
      });
    }

    res.json({
      success: true,
      data: submission,
      message: 'Submission retrieved successfully',
    });
  } catch (error) {
    console.error('Error getting submission by token:', error);
    
    if (error.message === 'Submission token has expired') {
      return res.status(410).json({
        success: false,
        message: 'Submission token has expired',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to get submission',
      error: error.message,
    });
  }
};

// Create new submission (public access)
const createSubmission = async (req, res) => {
  try {
    console.log('Creating submission with data:', req.body);
    console.log('Files uploaded:', req.files?.length || 0);

    // Process uploaded files
    const attachments = [];
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        attachments.push({
          filename: file.filename,
          originalName: file.originalname,
          fileSize: file.size,
          mimeType: file.mimetype,
          filePath: file.path,
          uploadedAt: new Date()
        });
      }
    }

    // Add attachments to submission data
    const submissionData = {
      ...req.body,
      attachments: attachments
    };

    // Parse JSON fields if they're strings
    if (typeof submissionData.bidItems === 'string') {
      submissionData.bidItems = JSON.parse(submissionData.bidItems);
    }
    if (typeof submissionData.bidData === 'string') {
      submissionData.bidData = JSON.parse(submissionData.bidData);
    }

    const submission = await RFQSubmission.create(submissionData);

    res.status(201).json({
      success: true,
      data: submission,
      message: 'Submission created successfully',
    });
  } catch (error) {
    console.error('Error creating submission:', error);

    // Clean up uploaded files if submission failed
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        try {
          await fs.unlink(file.path);
        } catch (unlinkError) {
          console.error('Error cleaning up file:', file.path, unlinkError);
        }
      }
    }

    if (error.message.startsWith('Validation error:')) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        error: error.message,
      });
    }

    if (error.message === 'Invalid invitation token') {
      return res.status(400).json({
        success: false,
        message: 'Invalid invitation token',
      });
    }

    if (error.message === 'Submission deadline has passed') {
      return res.status(410).json({
        success: false,
        message: 'Submission deadline has passed',
      });
    }

    if (error.message === 'Submission already exists for this vendor') {
      return res.status(409).json({
        success: false,
        message: 'Submission already exists for this vendor',
      });
    }

    if (error.message === 'RFQ is no longer accepting submissions') {
      return res.status(410).json({
        success: false,
        message: 'RFQ is no longer accepting submissions',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create submission',
      error: error.message,
    });
  }
};

// Update existing submission (public access)
const updateSubmission = async (req, res) => {
  try {
    const { id } = req.params;
    const { token } = req.body;
    
    console.log('Updating submission', id, 'with token:', token);
    console.log('Update data:', req.body);
    console.log('Files uploaded:', req.files?.length || 0);

    // Process uploaded files
    const newAttachments = [];
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        newAttachments.push({
          filename: file.filename,
          originalName: file.originalname,
          fileSize: file.size,
          mimeType: file.mimetype,
          filePath: file.path,
          uploadedAt: new Date()
        });
      }
    }

    // Prepare update data
    const updateData = { ...req.body };
    delete updateData.token; // Remove token from update data

    // Parse JSON fields if they're strings
    if (typeof updateData.bidItems === 'string') {
      updateData.bidItems = JSON.parse(updateData.bidItems);
    }
    if (typeof updateData.bidData === 'string') {
      updateData.bidData = JSON.parse(updateData.bidData);
    }

    // Handle attachments - merge with existing if specified
    if (newAttachments.length > 0) {
      if (updateData.attachments) {
        // Parse existing attachments if string
        const existingAttachments = typeof updateData.attachments === 'string' 
          ? JSON.parse(updateData.attachments) 
          : updateData.attachments;
        updateData.attachments = [...existingAttachments, ...newAttachments];
      } else {
        updateData.attachments = newAttachments;
      }
    }

    const submission = await RFQSubmission.update(id, updateData, token);

    res.json({
      success: true,
      data: submission,
      message: 'Submission updated successfully',
    });
  } catch (error) {
    console.error('Error updating submission:', error);

    // Clean up uploaded files if update failed
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        try {
          await fs.unlink(file.path);
        } catch (unlinkError) {
          console.error('Error cleaning up file:', file.path, unlinkError);
        }
      }
    }

    if (error.message.startsWith('Validation error:')) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        error: error.message,
      });
    }

    if (error.message === 'Submission not found or invalid token') {
      return res.status(404).json({
        success: false,
        message: 'Submission not found or invalid token',
      });
    }

    if (error.message === 'Submission deadline has passed') {
      return res.status(410).json({
        success: false,
        message: 'Submission deadline has passed',
      });
    }

    if (error.message === 'RFQ is no longer accepting submission updates') {
      return res.status(410).json({
        success: false,
        message: 'RFQ is no longer accepting submission updates',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update submission',
      error: error.message,
    });
  }
};

// Delete submission (public access)
const deleteSubmission = async (req, res) => {
  try {
    const { id } = req.params;
    const { token } = req.body;
    
    console.log('Deleting submission', id, 'with token:', token);

    // Get submission details before deletion to clean up files
    const submission = await RFQSubmission.findByToken(token);
    
    const result = await RFQSubmission.delete(id, token);

    // Clean up uploaded files
    if (submission && submission.attachments) {
      for (const attachment of submission.attachments) {
        try {
          await fs.unlink(attachment.filePath);
        } catch (unlinkError) {
          console.error('Error cleaning up file:', attachment.filePath, unlinkError);
        }
      }
    }

    res.json({
      success: true,
      data: result,
      message: 'Submission deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting submission:', error);

    if (error.message === 'Submission not found or invalid token') {
      return res.status(404).json({
        success: false,
        message: 'Submission not found or invalid token',
      });
    }

    if (error.message === 'Cannot delete submission after deadline') {
      return res.status(410).json({
        success: false,
        message: 'Cannot delete submission after deadline',
      });
    }

    if (error.message === 'Cannot delete submission for closed or cancelled RFQ') {
      return res.status(410).json({
        success: false,
        message: 'Cannot delete submission for closed or cancelled RFQ',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to delete submission',
      error: error.message,
    });
  }
};

// Get submission status (public access)
const getSubmissionStatus = async (req, res) => {
  try {
    const { token } = req.params;
    console.log('Getting submission status for token:', token);

    const validation = await RFQSubmission.validateToken(token);

    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: validation.error,
      });
    }

    const status = {
      token_valid: true,
      rfq_title: validation.invitation.rfq_title,
      rfq_due_date: validation.invitation.rfq_due_date,
      rfq_status: validation.invitation.rfq_status,
      vendor_name: validation.invitation.vendor_name,
      invitation_status: validation.invitation.status,
      has_submission: validation.has_existing_submission,
      submission_id: validation.existing_submission_id,
      can_submit: !validation.has_existing_submission && 
                  validation.invitation.rfq_status !== 'closed' && 
                  validation.invitation.rfq_status !== 'cancelled' &&
                  new Date() <= new Date(validation.invitation.expires_at),
      can_modify: validation.has_existing_submission && 
                  validation.invitation.rfq_status !== 'closed' && 
                  validation.invitation.rfq_status !== 'cancelled' &&
                  new Date() <= new Date(validation.invitation.expires_at),
    };

    res.json({
      success: true,
      data: status,
      message: 'Submission status retrieved successfully',
    });
  } catch (error) {
    console.error('Error getting submission status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get submission status',
      error: error.message,
    });
  }
};

// Download attachment file (public access with token validation)
const downloadAttachment = async (req, res) => {
  try {
    const { token, filename } = req.params;
    console.log('Downloading attachment:', filename, 'for token:', token);

    // Validate token first
    const validation = await RFQSubmission.validateToken(token);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: validation.error,
      });
    }

    // Get submission to verify file belongs to it
    const submission = await RFQSubmission.findByToken(token);
    if (!submission) {
      return res.status(404).json({
        success: false,
        message: 'Submission not found',
      });
    }

    // Find the attachment
    const attachment = submission.attachments.find(att => att.filename === filename);
    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found',
      });
    }

    // Check if file exists
    try {
      await fs.access(attachment.filePath);
    } catch (error) {
      return res.status(404).json({
        success: false,
        message: 'File not found on server',
      });
    }

    // Set appropriate headers
    res.setHeader('Content-Disposition', `attachment; filename="${attachment.originalName}"`);
    res.setHeader('Content-Type', attachment.mimeType);

    // Send file
    res.sendFile(path.resolve(attachment.filePath));
  } catch (error) {
    console.error('Error downloading attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to download attachment',
      error: error.message,
    });
  }
};

// Middleware to handle file uploads
const handleFileUpload = upload.array('attachments', 10);

// Error handling middleware for multer
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File size too large. Maximum size is 10MB per file.',
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Too many files. Maximum is 10 files per submission.',
      });
    }
    return res.status(400).json({
      success: false,
      message: `Upload error: ${error.message}`,
    });
  }
  
  if (error.message.includes('File type') && error.message.includes('not allowed')) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }

  next(error);
};

module.exports = {
  validateToken,
  getRFQForSubmission,
  getSubmissionByToken,
  createSubmission,
  updateSubmission,
  deleteSubmission,
  getSubmissionStatus,
  downloadAttachment,
  handleFileUpload,
  handleUploadError,
};