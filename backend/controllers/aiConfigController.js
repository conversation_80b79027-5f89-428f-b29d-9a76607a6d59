const { query } = require('../config/database');
const crypto = require('crypto');

// Encryption key for API keys (in production, use proper key management)
const ENCRYPTION_KEY = process.env.AI_ENCRYPTION_KEY || crypto.randomBytes(32);
const ALGORITHM = 'aes-256-gcm';

// Encrypt sensitive data
const encrypt = (text) => {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(ALGORITHM, ENCRYPTION_KEY);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  const authTag = cipher.getAuthTag();
  return {
    encrypted,
    iv: iv.toString('hex'),
    authTag: authTag.toString('hex')
  };
};

// Decrypt sensitive data
const decrypt = (encryptedData) => {
  const decipher = crypto.createDecipher(ALGORITHM, ENCRYPTION_KEY);
  decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

// Save AI provider configuration
const saveConfiguration = async (req, res) => {
  try {
    const userId = req.user.id;
    const { provider, apiKey, model, maxTokens, temperature, isActive } = req.body;

    // Validate input
    if (!provider || !apiKey) {
      return res.status(400).json({
        success: false,
        message: 'Provider and API key are required'
      });
    }

    // Validate provider
    const validProviders = ['openai', 'anthropic', 'gemini'];
    if (!validProviders.includes(provider)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid AI provider'
      });
    }

    // Encrypt API key
    const encryptedApiKey = encrypt(apiKey);

    // Check if configuration already exists
    const existingConfig = await query(
      'SELECT id FROM ai_configurations WHERE user_id = $1 AND provider = $2 AND deleted_at IS NULL',
      [userId, provider]
    );

    let result;
    if (existingConfig.rows.length > 0) {
      // Update existing configuration
      result = await query(
        `UPDATE ai_configurations 
         SET api_key_encrypted = $1, api_key_iv = $2, api_key_auth_tag = $3, 
             model = $4, max_tokens = $5, temperature = $6, is_active = $7, 
             updated_at = CURRENT_TIMESTAMP
         WHERE user_id = $8 AND provider = $9 AND deleted_at IS NULL
         RETURNING id, provider, model, max_tokens, temperature, is_active, created_at, updated_at`,
        [
          encryptedApiKey.encrypted,
          encryptedApiKey.iv,
          encryptedApiKey.authTag,
          model || 'default',
          maxTokens || 4000,
          temperature || 0.7,
          isActive !== undefined ? isActive : true,
          userId,
          provider
        ]
      );
    } else {
      // Create new configuration
      result = await query(
        `INSERT INTO ai_configurations 
         (user_id, provider, api_key_encrypted, api_key_iv, api_key_auth_tag, 
          model, max_tokens, temperature, is_active, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
         RETURNING id, provider, model, max_tokens, temperature, is_active, created_at, updated_at`,
        [
          userId,
          provider,
          encryptedApiKey.encrypted,
          encryptedApiKey.iv,
          encryptedApiKey.authTag,
          model || 'default',
          maxTokens || 4000,
          temperature || 0.7,
          isActive !== undefined ? isActive : true
        ]
      );
    }

    const config = result.rows[0];

    res.json({
      success: true,
      message: 'AI configuration saved successfully',
      data: {
        id: config.id,
        provider: config.provider,
        model: config.model,
        maxTokens: config.max_tokens,
        temperature: config.temperature,
        isActive: config.is_active,
        createdAt: config.created_at,
        updatedAt: config.updated_at
      }
    });
  } catch (error) {
    console.error('Save AI configuration error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while saving configuration'
    });
  }
};

// Get AI provider configuration
const getConfiguration = async (req, res) => {
  try {
    const userId = req.user.id;
    const { provider } = req.params;

    // Validate provider
    const validProviders = ['openai', 'anthropic', 'gemini'];
    if (!validProviders.includes(provider)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid AI provider'
      });
    }

    const result = await query(
      `SELECT id, provider, api_key_encrypted, api_key_iv, api_key_auth_tag, 
              model, max_tokens, temperature, is_active, created_at, updated_at
       FROM ai_configurations 
       WHERE user_id = $1 AND provider = $2 AND deleted_at IS NULL`,
      [userId, provider]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Configuration not found'
      });
    }

    const config = result.rows[0];

    // Decrypt API key
    const decryptedApiKey = decrypt({
      encrypted: config.api_key_encrypted,
      iv: config.api_key_iv,
      authTag: config.api_key_auth_tag
    });

    res.json({
      success: true,
      data: {
        id: config.id,
        provider: config.provider,
        apiKey: decryptedApiKey,
        model: config.model,
        maxTokens: config.max_tokens,
        temperature: config.temperature,
        isActive: config.is_active,
        createdAt: config.created_at,
        updatedAt: config.updated_at
      }
    });
  } catch (error) {
    console.error('Get AI configuration error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving configuration'
    });
  }
};

// Get all AI configurations for user
const getAllConfigurations = async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await query(
      `SELECT id, provider, model, max_tokens, temperature, is_active, created_at, updated_at
       FROM ai_configurations 
       WHERE user_id = $1 AND deleted_at IS NULL
       ORDER BY provider`,
      [userId]
    );

    res.json({
      success: true,
      data: result.rows.map(config => ({
        id: config.id,
        provider: config.provider,
        model: config.model,
        maxTokens: config.max_tokens,
        temperature: config.temperature,
        isActive: config.is_active,
        createdAt: config.created_at,
        updatedAt: config.updated_at
      }))
    });
  } catch (error) {
    console.error('Get all AI configurations error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving configurations'
    });
  }
};

// Delete AI provider configuration
const deleteConfiguration = async (req, res) => {
  try {
    const userId = req.user.id;
    const { provider } = req.params;

    // Validate provider
    const validProviders = ['openai', 'anthropic', 'gemini'];
    if (!validProviders.includes(provider)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid AI provider'
      });
    }

    const result = await query(
      `UPDATE ai_configurations 
       SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $1 AND provider = $2 AND deleted_at IS NULL
       RETURNING id`,
      [userId, provider]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Configuration not found'
      });
    }

    res.json({
      success: true,
      message: 'AI configuration deleted successfully'
    });
  } catch (error) {
    console.error('Delete AI configuration error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while deleting configuration'
    });
  }
};

// Set current active provider
const setCurrentProvider = async (req, res) => {
  try {
    const userId = req.user.id;
    const { provider } = req.body;

    // Validate provider
    const validProviders = ['openai', 'anthropic', 'gemini'];
    if (!validProviders.includes(provider)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid AI provider'
      });
    }

    // Check if configuration exists
    const configExists = await query(
      'SELECT id FROM ai_configurations WHERE user_id = $1 AND provider = $2 AND deleted_at IS NULL',
      [userId, provider]
    );

    if (configExists.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Provider configuration not found'
      });
    }

    // Deactivate all providers for user
    await query(
      'UPDATE ai_configurations SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE user_id = $1',
      [userId]
    );

    // Activate selected provider
    await query(
      'UPDATE ai_configurations SET is_active = true, updated_at = CURRENT_TIMESTAMP WHERE user_id = $1 AND provider = $2',
      [userId, provider]
    );

    res.json({
      success: true,
      message: 'Current AI provider updated successfully',
      data: { currentProvider: provider }
    });
  } catch (error) {
    console.error('Set current provider error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while setting current provider'
    });
  }
};

// Get current active provider
const getCurrentProvider = async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await query(
      'SELECT provider FROM ai_configurations WHERE user_id = $1 AND is_active = true AND deleted_at IS NULL',
      [userId]
    );

    const currentProvider = result.rows.length > 0 ? result.rows[0].provider : null;

    res.json({
      success: true,
      data: { currentProvider }
    });
  } catch (error) {
    console.error('Get current provider error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while getting current provider'
    });
  }
};

// Export/backup configurations
const exportConfigurations = async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await query(
      `SELECT provider, api_key_encrypted, api_key_iv, api_key_auth_tag, 
              model, max_tokens, temperature, is_active
       FROM ai_configurations 
       WHERE user_id = $1 AND deleted_at IS NULL`,
      [userId]
    );

    // Create backup data (keep encrypted for security)
    const backupData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      configurations: result.rows.map(config => ({
        provider: config.provider,
        apiKeyEncrypted: config.api_key_encrypted,
        apiKeyIv: config.api_key_iv,
        apiKeyAuthTag: config.api_key_auth_tag,
        model: config.model,
        maxTokens: config.max_tokens,
        temperature: config.temperature,
        isActive: config.is_active
      }))
    };

    res.json({
      success: true,
      data: backupData
    });
  } catch (error) {
    console.error('Export configurations error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while exporting configurations'
    });
  }
};

// Import/restore configurations
const importConfigurations = async (req, res) => {
  try {
    const userId = req.user.id;
    const { backupData } = req.body;

    if (!backupData || !backupData.configurations) {
      return res.status(400).json({
        success: false,
        message: 'Invalid backup data'
      });
    }

    // Begin transaction
    await query('BEGIN');

    try {
      // Delete existing configurations
      await query(
        'UPDATE ai_configurations SET deleted_at = CURRENT_TIMESTAMP WHERE user_id = $1',
        [userId]
      );

      // Import configurations
      for (const config of backupData.configurations) {
        await query(
          `INSERT INTO ai_configurations 
           (user_id, provider, api_key_encrypted, api_key_iv, api_key_auth_tag, 
            model, max_tokens, temperature, is_active, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
          [
            userId,
            config.provider,
            config.apiKeyEncrypted,
            config.apiKeyIv,
            config.apiKeyAuthTag,
            config.model,
            config.maxTokens,
            config.temperature,
            config.isActive
          ]
        );
      }

      await query('COMMIT');

      res.json({
        success: true,
        message: 'Configurations imported successfully',
        data: { imported: backupData.configurations.length }
      });
    } catch (error) {
      await query('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('Import configurations error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while importing configurations'
    });
  }
};

module.exports = {
  saveConfiguration,
  getConfiguration,
  getAllConfigurations,
  deleteConfiguration,
  setCurrentProvider,
  getCurrentProvider,
  exportConfigurations,
  importConfigurations
};