const ClientQuote = require('../models/ClientQuote');
const QuoteTemplate = require('../models/QuoteTemplate');
const QuoteInteraction = require('../models/QuoteInteraction');
const QuoteGenerationService = require('../services/QuoteGenerationService');
const AuditLogger = require('../services/AuditLogger');
const PDFTemplateRenderer = require('../services/PDFTemplateRenderer');
const PDFDocument = require('pdfkit'); // We'll need to install this
const fs = require('fs').promises;
const path = require('path');

// Get all quotes
const getQuotes = async (req, res) => {
  try {
    console.log('=== QUOTE API CALLED ===');
    console.log('User from auth middleware:', req.user);
    console.log('Query params:', req.query);
    
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      search: req.query.search,
      status: req.query.status,
      creator_id: req.query.creator_id ? parseInt(req.query.creator_id) : undefined,
      rfq_id: req.query.rfq_id ? parseInt(req.query.rfq_id) : undefined,
      client_email: req.query.client_email,
      expires_from: req.query.expires_from,
      expires_to: req.query.expires_to,
    };

    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;

    console.log('Calling ClientQuote.findAll with:', { filters, page, limit, userId });
    const result = await ClientQuote.findAll(filters, page, limit, userId);
    console.log('Database result:', result);
    
    res.json({
      success: true,
      data: result.quotes,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error fetching quotes:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch quotes',
      error: error.message,
    });
  }
};

// Get quote by ID
const getQuoteById = async (req, res) => {
  try {
    const { id } = req.params;
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const quote = await ClientQuote.findById(id, userId);
    
    if (!quote) {
      return res.status(404).json({
        success: false,
        message: 'Quote not found',
      });
    }

    res.json({
      success: true,
      data: quote,
    });
  } catch (error) {
    console.error('Error fetching quote:', error);
    
    if (error.message === 'Access denied') {
      return res.status(403).json({
        success: false,
        message: 'Access denied',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to fetch quote',
      error: error.message,
    });
  }
};

// Get quote by public token (for client access)
const getQuoteByToken = async (req, res) => {
  try {
    const { token } = req.params;
    console.log('Getting quote by token:', token);
    
    const quote = await ClientQuote.findByToken(token);
    
    if (!quote) {
      return res.status(404).json({
        success: false,
        message: 'Quote not found',
      });
    }

    // Remove sensitive information for public access
    const publicQuote = {
      ...quote,
      creator_id: undefined,
      creator_email: undefined,
    };

    res.json({
      success: true,
      data: publicQuote,
    });
  } catch (error) {
    console.error('Error fetching quote by token:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch quote',
      error: error.message,
    });
  }
};

// Create new quote
const createQuote = async (req, res) => {
  try {
    console.log('Creating quote with data:', req.body);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const quote = await ClientQuote.create(req.body, userId);
    
    // Log quote creation
    await AuditLogger.logQuoteCreation(quote, userId, req);
    
    res.status(201).json({
      success: true,
      data: quote,
      message: 'Quote created successfully',
    });
  } catch (error) {
    console.error('Error creating quote:', error);
    
    if (error.message.startsWith('Validation error:')) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        error: error.message,
      });
    }

    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    if (error.message === 'One or more selected submissions are invalid') {
      return res.status(400).json({
        success: false,
        message: 'Invalid submission selections',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create quote',
      error: error.message,
    });
  }
};

// Update quote
const updateQuote = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Updating quote', id, 'with data:', req.body);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    // Get old quote for audit logging
    const oldQuote = await ClientQuote.findById(id, userId);
    
    const quote = await ClientQuote.update(id, req.body, userId);
    
    // Log quote update
    await AuditLogger.logQuoteUpdate(oldQuote, quote, userId, req);
    
    res.json({
      success: true,
      data: quote,
      message: 'Quote updated successfully',
    });
  } catch (error) {
    console.error('Error updating quote:', error);
    
    if (error.message === 'Quote not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'Quote not found',
      });
    }

    if (error.message.startsWith('Validation error:')) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        error: error.message,
      });
    }

    if (error.message === 'Cannot update approved or rejected quote') {
      return res.status(409).json({
        success: false,
        message: 'Cannot update approved or rejected quote',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update quote',
      error: error.message,
    });
  }
};

// Send quote to client
const sendQuote = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Sending quote:', id);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const quote = await ClientQuote.send(id, userId);
    
    // Log quote sending
    await AuditLogger.logQuoteSent(quote, quote.client_email, userId, req);
    
    // TODO: Integrate with email service to actually send quote
    console.log('Quote sent to client:', quote.client_email);
    
    res.json({
      success: true,
      data: quote,
      message: `Quote sent successfully to ${quote.client_email}`,
    });
  } catch (error) {
    console.error('Error sending quote:', error);
    
    if (error.message === 'Quote not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'Quote not found',
      });
    }

    if (error.message === 'Quote can only be sent from draft status') {
      return res.status(409).json({
        success: false,
        message: 'Quote can only be sent from draft status',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to send quote',
      error: error.message,
    });
  }
};

// Generate and download quote PDF
const generateQuotePDF = async (req, res) => {
  try {
    const { id } = req.params;
    const { template_id } = req.query;
    console.log('Generating PDF for quote:', id, 'with template:', template_id);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const quote = await ClientQuote.findById(id, userId);
    
    if (!quote) {
      return res.status(404).json({
        success: false,
        message: 'Quote not found',
      });
    }

    // Get template if specified
    let template = null;
    if (template_id) {
      template = await QuoteTemplate.findById(template_id);
    } else if (quote.quote_template_id) {
      template = await QuoteTemplate.findById(quote.quote_template_id);
    }

    // Generate PDF using template renderer
    const pdfBuffer = await PDFTemplateRenderer.generateQuotePDF(quote, template);
    
    // Log PDF generation
    await AuditLogger.logQuoteAction(id, 'pdf_generated', {
      generated_by: userId,
      template_id: template?.id,
      file_size: pdfBuffer.length
    });
    
    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="quote-${quote.id}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);
    
    // Send PDF
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Error generating quote PDF:', error);
    
    if (error.message === 'Access denied') {
      return res.status(403).json({
        success: false,
        message: 'Access denied',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to generate PDF',
      error: error.message,
    });
  }
};

// Generate PDF for public access (via token)
const generatePublicQuotePDF = async (req, res) => {
  try {
    const { token } = req.params;
    console.log('Generating public PDF for token:', token);
    
    const quote = await ClientQuote.findByToken(token);
    
    if (!quote) {
      return res.status(404).json({
        success: false,
        message: 'Quote not found',
      });
    }

    // Generate PDF
    const pdfBuffer = await generatePDFBuffer(quote);
    
    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="quote-${quote.title.replace(/[^a-zA-Z0-9]/g, '-')}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);
    
    // Send PDF
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Error generating public quote PDF:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate PDF',
      error: error.message,
    });
  }
};

// Approve quote (public access via token)
const approveQuote = async (req, res) => {
  try {
    const { token } = req.params;
    console.log('Approving quote with token:', token);
    console.log('Approval data:', req.body);
    
    const result = await ClientQuote.approve(token, req.body);
    
    res.json({
      success: true,
      data: result,
      message: 'Quote approved successfully',
    });
  } catch (error) {
    console.error('Error approving quote:', error);
    
    if (error.message.startsWith('Validation error:')) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        error: error.message,
      });
    }

    if (error.message === 'Quote not found or invalid token') {
      return res.status(404).json({
        success: false,
        message: 'Quote not found or invalid token',
      });
    }

    if (error.message === 'Quote is already approved') {
      return res.status(409).json({
        success: false,
        message: 'Quote is already approved',
      });
    }

    if (error.message === 'Quote has been rejected and cannot be approved') {
      return res.status(409).json({
        success: false,
        message: 'Quote has been rejected and cannot be approved',
      });
    }

    if (error.message === 'Quote has expired and cannot be approved') {
      return res.status(410).json({
        success: false,
        message: 'Quote has expired and cannot be approved',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to approve quote',
      error: error.message,
    });
  }
};

// Reject quote (public access via token)
const rejectQuote = async (req, res) => {
  try {
    const { token } = req.params;
    const { feedback } = req.body;
    console.log('Rejecting quote with token:', token);
    console.log('Feedback:', feedback);
    
    const result = await ClientQuote.reject(token, feedback);
    
    res.json({
      success: true,
      data: result,
      message: 'Quote rejected successfully',
    });
  } catch (error) {
    console.error('Error rejecting quote:', error);
    
    if (error.message === 'Quote not found or invalid token') {
      return res.status(404).json({
        success: false,
        message: 'Quote not found or invalid token',
      });
    }

    if (error.message === 'Quote is already approved and cannot be rejected') {
      return res.status(409).json({
        success: false,
        message: 'Quote is already approved and cannot be rejected',
      });
    }

    if (error.message === 'Quote is already rejected') {
      return res.status(409).json({
        success: false,
        message: 'Quote is already rejected',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to reject quote',
      error: error.message,
    });
  }
};

// Delete quote
const deleteQuote = async (req, res) => {
  try {
    const { id } = req.params;
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    // Get quote for audit logging before deletion
    const quote = await ClientQuote.findById(id, userId);
    
    const result = await ClientQuote.delete(id, userId);
    
    // Log quote deletion
    await AuditLogger.logQuoteDeletion(quote, userId, req);
    
    res.json({
      success: true,
      data: result,
      message: 'Quote deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting quote:', error);
    
    if (error.message === 'Quote not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'Quote not found',
      });
    }

    if (error.message === 'Cannot delete approved quote') {
      return res.status(409).json({
        success: false,
        message: 'Cannot delete approved quote',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to delete quote',
      error: error.message,
    });
  }
};

// Get quote statistics
const getQuoteStatistics = async (req, res) => {
  try {
    console.log('Fetching quote statistics');
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const statistics = await ClientQuote.getStatistics(userId);
    
    res.json({
      success: true,
      data: statistics,
      message: 'Statistics retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching quote statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch statistics',
      error: error.message,
    });
  }
};

// Validate quote token (for public access)
const validateQuoteToken = async (req, res) => {
  try {
    const { token } = req.params;
    console.log('Validating quote token:', token);

    const validation = await ClientQuote.validateToken(token);

    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: validation.error,
      });
    }

    res.json({
      success: true,
      data: {
        quote: validation.quote,
        can_approve: validation.can_approve,
        can_reject: validation.can_reject,
      },
      message: 'Token is valid',
    });
  } catch (error) {
    console.error('Error validating quote token:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate token',
      error: error.message,
    });
  }
};

// Get submissions for quote generation
const getSubmissionsForQuote = async (req, res) => {
  try {
    const { rfqId } = req.params;
    const userId = req.user?.id || 1;
    
    const submissionsData = await QuoteGenerationService.getSubmissionsForQuote(
      parseInt(rfqId), 
      userId
    );
    
    res.json({
      success: true,
      data: submissionsData
    });
  } catch (error) {
    console.error('Error getting submissions for quote:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get submissions for quote'
    });
  }
};

// Generate quote from RFQ submissions
const generateQuoteFromSubmissions = async (req, res) => {
  try {
    const { rfqId } = req.params;
    const userId = req.user?.id || 1;
    const quoteData = req.body;
    
    // Validate required fields
    if (!quoteData.selectedBids || Object.keys(quoteData.selectedBids).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Selected bids are required'
      });
    }
    
    const quote = await QuoteGenerationService.generateQuote({
      rfqId: parseInt(rfqId),
      selectedBids: quoteData.selectedBids,
      quoteData,
      userId
    });
    
    // Log quote generation from submissions
    await AuditLogger.logQuoteCreation(quote, userId, req);
    
    res.status(201).json({
      success: true,
      data: quote,
      message: 'Quote generated successfully'
    });
  } catch (error) {
    console.error('Error generating quote from submissions:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to generate quote'
    });
  }
};

// Update quote selections
const updateQuoteSelections = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id || 1;
    const updateData = req.body;
    
    const updatedQuote = await QuoteGenerationService.updateQuoteSelections(
      parseInt(id),
      updateData,
      userId
    );
    
    res.json({
      success: true,
      data: updatedQuote,
      message: 'Quote selections updated successfully'
    });
  } catch (error) {
    console.error('Error updating quote selections:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update quote selections'
    });
  }
};

// Get quote audit history
const getQuoteAuditHistory = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Fetching audit history for quote:', id);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    const { limit, offset, action_filter, date_from, date_to } = req.query;
    
    // Verify user has access to the quote
    const quote = await ClientQuote.findById(id, userId);
    if (!quote) {
      return res.status(404).json({
        success: false,
        message: 'Quote not found'
      });
    }
    
    // Get audit history using AuditLogger
    const auditHistory = await AuditLogger.getQuoteAuditHistory(parseInt(id), {
      limit: limit ? parseInt(limit) : 50,
      offset: offset ? parseInt(offset) : 0,
      action_filter,
      date_from,
      date_to
    });
    
    res.json({
      success: true,
      data: auditHistory,
      message: 'Audit history retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching quote audit history:', error);
    
    if (error.message === 'Quote not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'Quote not found',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to fetch audit history',
      error: error.message,
    });
  }
};

// Helper function to generate PDF buffer
const generatePDFBuffer = async (quote) => {
  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument({ margin: 50 });
      const buffers = [];

      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(buffers);
        resolve(pdfBuffer);
      });

      // Header
      doc.fontSize(20).text('QUOTE', 50, 50);
      doc.fontSize(12).text(`Quote #${quote.id}`, 50, 80);
      doc.text(`Date: ${new Date(quote.created_at).toLocaleDateString()}`, 50, 100);
      doc.text(`Valid Until: ${new Date(quote.expires_at).toLocaleDateString()}`, 50, 120);

      // Client Information
      doc.fontSize(14).text('Bill To:', 50, 160);
      doc.fontSize(12).text(quote.client_name, 50, 180);
      doc.text(quote.client_email, 50, 200);

      // Quote Details
      doc.fontSize(14).text('Quote Details:', 50, 240);
      doc.fontSize(12).text(`Title: ${quote.title}`, 50, 260);
      doc.text(`RFQ: ${quote.rfq_title}`, 50, 280);

      // Line Items
      let yPosition = 320;
      doc.fontSize(14).text('Items:', 50, yPosition);
      yPosition += 30;

      // Table headers
      doc.fontSize(10);
      doc.text('Item', 50, yPosition);
      doc.text('Vendor', 200, yPosition);
      doc.text('Unit Price', 350, yPosition);
      doc.text('Total', 450, yPosition);
      yPosition += 20;

      // Draw line under headers
      doc.moveTo(50, yPosition).lineTo(550, yPosition).stroke();
      yPosition += 10;

      // Line items
      const selectedBids = quote.selected_bids || {};
      const rfqItems = quote.rfq_items || [];
      
      Object.entries(selectedBids).forEach(([itemId, bid]) => {
        const rfqItem = rfqItems.find(item => item.id === itemId);
        const vendorName = quote.vendor_details?.[bid.vendorId]?.name || 'Unknown Vendor';
        
        doc.text(rfqItem?.name || itemId, 50, yPosition);
        doc.text(vendorName, 200, yPosition);
        doc.text(`$${bid.unitPrice.toFixed(2)}`, 350, yPosition);
        doc.text(`$${bid.totalPrice.toFixed(2)}`, 450, yPosition);
        yPosition += 20;
      });

      // Totals
      yPosition += 20;
      doc.moveTo(350, yPosition).lineTo(550, yPosition).stroke();
      yPosition += 10;

      const subtotal = Object.values(selectedBids).reduce((sum, bid) => sum + bid.totalPrice, 0);
      const marginAmount = subtotal * (quote.margin_percentage / 100);
      const total = quote.total_amount;

      doc.text('Subtotal:', 350, yPosition);
      doc.text(`$${subtotal.toFixed(2)}`, 450, yPosition);
      yPosition += 20;

      if (quote.margin_percentage > 0) {
        doc.text(`Margin (${quote.margin_percentage}%):`, 350, yPosition);
        doc.text(`$${marginAmount.toFixed(2)}`, 450, yPosition);
        yPosition += 20;
      }

      if (quote.taxes > 0) {
        doc.text('Taxes:', 350, yPosition);
        doc.text(`$${quote.taxes.toFixed(2)}`, 450, yPosition);
        yPosition += 20;
      }

      doc.fontSize(12).text('Total:', 350, yPosition);
      doc.text(`$${total.toFixed(2)}`, 450, yPosition);

      // Terms and Notes
      if (quote.terms) {
        yPosition += 40;
        doc.fontSize(14).text('Terms & Conditions:', 50, yPosition);
        yPosition += 20;
        doc.fontSize(10).text(quote.terms, 50, yPosition, { width: 500 });
      }

      if (quote.notes) {
        yPosition += 60;
        doc.fontSize(14).text('Notes:', 50, yPosition);
        yPosition += 20;
        doc.fontSize(10).text(quote.notes, 50, yPosition, { width: 500 });
      }

      doc.end();
    } catch (error) {
      reject(error);
    }
  });
};

// Quote Template Controllers
const getQuoteTemplates = async (req, res) => {
  try {
    const filters = {
      search: req.query.search,
      created_by: req.query.created_by ? parseInt(req.query.created_by) : undefined,
      is_default: req.query.is_default !== undefined ? req.query.is_default === 'true' : undefined
    };

    const userId = req.user?.id || 1;
    const templates = await QuoteTemplate.findAll(filters, userId);
    
    res.json({
      success: true,
      data: templates
    });
  } catch (error) {
    console.error('Error fetching quote templates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch quote templates',
      error: error.message
    });
  }
};

const getQuoteTemplateById = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id || 1;
    
    const template = await QuoteTemplate.findById(id, userId);
    
    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Quote template not found'
      });
    }
    
    res.json({
      success: true,
      data: template
    });
  } catch (error) {
    console.error('Error fetching quote template:', error);
    
    if (error.message === 'Access denied') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to fetch quote template',
      error: error.message
    });
  }
};

const createQuoteTemplate = async (req, res) => {
  try {
    const userId = req.user?.id || 1;
    const template = await QuoteTemplate.create(req.body, userId);
    
    res.status(201).json({
      success: true,
      data: template,
      message: 'Quote template created successfully'
    });
  } catch (error) {
    console.error('Error creating quote template:', error);
    
    if (error.message.includes('Validation error')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to create quote template',
      error: error.message
    });
  }
};

const updateQuoteTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id || 1;
    
    const template = await QuoteTemplate.update(id, req.body, userId);
    
    res.json({
      success: true,
      data: template,
      message: 'Quote template updated successfully'
    });
  } catch (error) {
    console.error('Error updating quote template:', error);
    
    if (error.message === 'Template not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'Quote template not found'
      });
    }
    
    if (error.message.includes('Validation error')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to update quote template',
      error: error.message
    });
  }
};

const deleteQuoteTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id || 1;
    
    await QuoteTemplate.delete(id, userId);
    
    res.json({
      success: true,
      message: 'Quote template deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting quote template:', error);
    
    if (error.message === 'Template not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'Quote template not found'
      });
    }
    
    if (error.message === 'Cannot delete default template' || 
        error.message === 'Cannot delete template that is being used by quotes') {
      return res.status(409).json({
        success: false,
        message: error.message
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to delete quote template',
      error: error.message
    });
  }
};

const cloneQuoteTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;
    const userId = req.user?.id || 1;
    
    const template = await QuoteTemplate.clone(id, name, userId);
    
    res.status(201).json({
      success: true,
      data: template,
      message: 'Quote template cloned successfully'
    });
  } catch (error) {
    console.error('Error cloning quote template:', error);
    
    if (error.message === 'Template not found') {
      return res.status(404).json({
        success: false,
        message: 'Quote template not found'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to clone quote template',
      error: error.message
    });
  }
};

// Preview quote template
const previewQuoteTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const template = await QuoteTemplate.findById(id);
    
    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    // Create sample quote data for preview
    const sampleQuote = {
      id: 'SAMPLE-001',
      client_name: 'Sample Client Corp',
      client_email: '<EMAIL>',
      rfq_id: 'RFQ-001',
      status: 'draft',
      currency: 'USD',
      created_at: new Date(),
      expires_at: new Date(Date.now() + 30*24*60*60*1000),
      selected_bids: {
        '1': {
          itemName: 'Sample Product A',
          description: 'High-quality sample product with specifications',
          quantity: 10,
          unitPrice: 150.00,
          totalPrice: 1500.00,
          vendorName: 'Sample Vendor Inc'
        },
        '2': {
          itemName: 'Sample Service B',
          description: 'Professional consulting service',
          quantity: 5,
          unitPrice: 200.00,
          totalPrice: 1000.00,
          vendorName: 'Expert Services LLC'
        }
      },
      total_amount: 2500.00,
      margin_percentage: 10,
      taxes: 250.00,
      terms: 'Sample terms and conditions for preview purposes.',
      notes: 'This is a sample quote generated for template preview.'
    };
    
    // Generate preview PDF
    const pdfBuffer = await PDFTemplateRenderer.generateQuotePDF(sampleQuote, template);
    
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename="template-preview-${id}.pdf"`);
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Error generating template preview:', error);
    res.status(500).json({ error: 'Failed to generate template preview' });
  }
};

// Quote Interaction Controllers
const getQuoteInteractions = async (req, res) => {
  try {
    const { id } = req.params;
    const filters = {
      interaction_type: req.query.interaction_type,
      client_email: req.query.client_email,
      date_from: req.query.date_from,
      date_to: req.query.date_to
    };
    
    const interactions = await QuoteInteraction.findByQuoteId(id, filters);
    
    res.json({
      success: true,
      data: interactions
    });
  } catch (error) {
    console.error('Error fetching quote interactions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch quote interactions',
      error: error.message
    });
  }
};

const createQuoteInteraction = async (req, res) => {
  try {
    const { id } = req.params;
    const interactionData = {
      ...req.body,
      quote_id: parseInt(id),
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    };
    
    const interaction = await QuoteInteraction.create(interactionData);
    
    res.status(201).json({
      success: true,
      data: interaction,
      message: 'Quote interaction recorded successfully'
    });
  } catch (error) {
    console.error('Error creating quote interaction:', error);
    
    if (error.message.includes('Validation error') || error.message === 'Quote not found') {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to record quote interaction',
      error: error.message
    });
  }
};

const getQuoteStats = async (req, res) => {
  try {
    const { id } = req.params;
    const stats = await QuoteInteraction.getQuoteStats(id);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching quote stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch quote statistics',
      error: error.message
    });
  }
};

const getQuoteTimeline = async (req, res) => {
  try {
    const { id } = req.params;
    const limit = parseInt(req.query.limit) || 50;
    
    const timeline = await QuoteInteraction.getQuoteTimeline(id, limit);
    
    res.json({
      success: true,
      data: timeline
    });
  } catch (error) {
    console.error('Error fetching quote timeline:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch quote timeline',
      error: error.message
    });
  }
};

module.exports = {
  getQuotes,
  getQuoteById,
  getQuoteByToken,
  createQuote,
  updateQuote,
  sendQuote,
  generateQuotePDF,
  generatePublicQuotePDF,
  approveQuote,
  rejectQuote,
  deleteQuote,
  getQuoteStatistics,
  validateQuoteToken,
  getQuoteAuditHistory,
  // Quote Generation
  getSubmissionsForQuote,
  generateQuoteFromSubmissions,
  updateQuoteSelections,
  // Quote Templates
  getQuoteTemplates,
  getQuoteTemplateById,
  createQuoteTemplate,
  updateQuoteTemplate,
  deleteQuoteTemplate,
  cloneQuoteTemplate,
  previewQuoteTemplate,
  // Quote Interactions
  getQuoteInteractions,
  createQuoteInteraction,
  getQuoteStats,
  getQuoteTimeline
};