const RFQ = require('../models/RFQ');
const RFQSubmission = require('../models/RFQSubmission');
const aiRecommendationService = require('../services/aiRecommendationService');
const Joi = require('joi');

// Validation schemas for query parameters
const getRFQsQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  cursor: Joi.string().optional(),
  search: Joi.string().max(255).optional(),
  status: Joi.string().valid('draft', 'sent', 'in_progress', 'closed', 'cancelled').optional(),
  creator_id: Joi.number().integer().positive().optional(),
  due_date_from: Joi.date().iso().optional(),
  due_date_to: Joi.date().iso().optional(),
  currency: Joi.string().valid('USD', 'EUR', 'GBP', 'JPY', 'CNY').optional(),
  category: Joi.string().max(100).optional(),
  vendor_id: Joi.number().integer().positive().optional(),
  sort_by: Joi.string().valid('created_at', 'updated_at', 'due_date', 'title', 'response_rate').default('created_at'),
  sort_order: Joi.string().valid('asc', 'desc').default('desc'),
  include_analytics: Joi.boolean().default(false),
  include_submissions: Joi.boolean().default(false)
});

// Get all RFQs with enhanced filtering and pagination
const getRFQs = async (req, res) => {
  try {
    console.log('=== Enhanced RFQ API CALLED ===');
    console.log('User from auth middleware:', req.user);
    console.log('Query params:', req.query);
    
    // Validate query parameters
    const { error, value } = getRFQsQuerySchema.validate(req.query);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Invalid query parameters',
        errors: error.details.map(d => ({
          field: d.path.join('.'),
          message: d.message
        }))
      });
    }

    const {
      page,
      limit,
      cursor,
      search,
      status,
      creator_id,
      due_date_from,
      due_date_to,
      currency,
      category,
      vendor_id,
      sort_by,
      sort_order,
      include_analytics,
      include_submissions
    } = value;

    const filters = {
      search,
      status,
      creator_id,
      due_date_from,
      due_date_to,
      currency,
      category,
      vendor_id,
      sort_by,
      sort_order,
      include_analytics,
      include_submissions
    };

    const userId = req.user?.id || 1;

    console.log('Calling RFQ.findAll with enhanced filters:', { filters, page, limit, cursor, userId });
    
    let result;
    if (cursor) {
      // Use cursor-based pagination
      result = await RFQ.findAllWithCursor(filters, cursor, limit, userId);
    } else {
      // Use offset-based pagination
      result = await RFQ.findAll(filters, page, limit, userId);
    }
    
    console.log('Database result:', result);
    
    res.json({
      success: true,
      data: result.rfqs,
      pagination: result.pagination,
      meta: {
        total_count: result.pagination.total,
        has_next: result.pagination.hasNext,
        has_previous: result.pagination.hasPrevious,
        next_cursor: result.pagination.nextCursor,
        previous_cursor: result.pagination.previousCursor
      }
    });
  } catch (error) {
    console.error('Error fetching RFQs:', error);
    console.error('Error stack:', error.stack);
    
    // Enhanced error handling
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        error: error.message,
      });
    }
    
    if (error.message.includes('Invalid cursor')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid pagination cursor',
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to fetch RFQs',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
};

// Get RFQ by ID
const getRFQById = async (req, res) => {
  try {
    const { id } = req.params;
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const rfq = await RFQ.findById(id, userId);
    
    if (!rfq) {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    res.json({
      success: true,
      data: rfq,
    });
  } catch (error) {
    console.error('Error fetching RFQ:', error);
    
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to fetch RFQ',
      error: error.message,
    });
  }
};

// Create new RFQ
const createRFQ = async (req, res) => {
  try {
    console.log('Creating RFQ with data:', req.body);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const rfq = await RFQ.create(req.body, userId);
    
    res.status(201).json({
      success: true,
      data: rfq,
      message: 'RFQ created successfully',
    });
  } catch (error) {
    console.error('Error creating RFQ:', error);
    
    // Handle validation errors
    if (error.message.startsWith('Validation error:')) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create RFQ',
      error: error.message,
    });
  }
};

// Update RFQ
const updateRFQ = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Updating RFQ', id, 'with data:', req.body);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const rfq = await RFQ.update(id, req.body, userId);
    
    res.json({
      success: true,
      data: rfq,
      message: 'RFQ updated successfully',
    });
  } catch (error) {
    console.error('Error updating RFQ:', error);
    
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    if (error.message.startsWith('Validation error:')) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        error: error.message,
      });
    }

    if (error.message.includes('Cannot update')) {
      return res.status(409).json({
        success: false,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update RFQ',
      error: error.message,
    });
  }
};

// Delete RFQ (soft delete by setting status to cancelled)
const deleteRFQ = async (req, res) => {
  try {
    const { id } = req.params;
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const result = await RFQ.delete(id, userId);
    
    res.json({
      success: true,
      data: result,
      message: 'RFQ cancelled successfully',
    });
  } catch (error) {
    console.error('Error deleting RFQ:', error);
    
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    if (error.message.includes('Cannot delete')) {
      return res.status(409).json({
        success: false,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to cancel RFQ',
      error: error.message,
    });
  }
};

// Send RFQ invitations
const sendRFQInvitations = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Sending invitations for RFQ:', id);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const result = await RFQ.sendInvitations(id, userId);
    
    // TODO: Integrate with email service to actually send invitation emails
    console.log('RFQ invitations sent:', result.invitations.length);
    
    res.json({
      success: true,
      data: result,
      message: `RFQ sent successfully to ${result.invitations.length} vendors`,
    });
  } catch (error) {
    console.error('Error sending RFQ invitations:', error);
    
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    if (error.message.includes('can only be sent')) {
      return res.status(409).json({
        success: false,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to send RFQ invitations',
      error: error.message,
    });
  }
};

// Get RFQ invitations
const getRFQInvitations = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Fetching invitations for RFQ:', id);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    // Get RFQ with invitations
    const rfq = await RFQ.findById(id, userId);
    
    if (!rfq) {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }
    
    res.json({
      success: true,
      data: rfq.invitations || [],
      message: `Found ${rfq.invitations?.length || 0} invitations`,
    });
  } catch (error) {
    console.error('Error fetching RFQ invitations:', error);
    
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to fetch RFQ invitations',
      error: error.message,
    });
  }
};

// Get RFQ submissions
const getRFQSubmissions = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Fetching submissions for RFQ:', id);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const submissions = await RFQSubmission.findByRFQ(id, userId);
    
    res.json({
      success: true,
      data: submissions,
      message: `Found ${submissions.length} submissions`,
    });
  } catch (error) {
    console.error('Error fetching RFQ submissions:', error);
    
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to fetch RFQ submissions',
      error: error.message,
    });
  }
};

// Get RFQ submissions comparison data
const getRFQSubmissionsComparison = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Fetching submission comparison data for RFQ:', id);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const comparisonData = await RFQSubmission.getComparisonData(id, userId);
    
    res.json({
      success: true,
      data: comparisonData,
      message: 'Comparison data retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching RFQ submission comparison:', error);
    
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to fetch submission comparison data',
      error: error.message,
    });
  }
};

// Generate AI recommendations for RFQ
const generateAIRecommendations = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Generating AI recommendations for RFQ:', id);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    // Get RFQ details
    const rfq = await RFQ.findById(id, userId);
    if (!rfq) {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }
    
    // Get submissions for this RFQ
    const submissions = await RFQSubmission.findByRFQ(id, userId);
    
    if (submissions.length === 0) {
      return res.json({
        success: true,
        data: [],
        message: 'No submissions available for AI analysis',
      });
    }
    
    // Generate AI recommendations
    const recommendations = await aiRecommendationService.analyzeSubmissions(submissions, rfq);
    
    // Add RFQ ID to recommendations
    recommendations.forEach(rec => {
      rec.rfq_id = parseInt(id);
    });
    
    res.json({
      success: true,
      data: recommendations,
      message: `Generated ${recommendations.length} AI recommendations`,
    });
  } catch (error) {
    console.error('Error generating AI recommendations:', error);
    
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to generate AI recommendations',
      error: error.message,
    });
  }
};

// Get RFQ analytics
const getRFQAnalytics = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Fetching analytics for RFQ:', id);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const analytics = await RFQ.getAnalytics(id, userId);
    
    res.json({
      success: true,
      data: analytics,
      message: 'Analytics retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching RFQ analytics:', error);
    
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to fetch RFQ analytics',
      error: error.message,
    });
  }
};

// Get RFQ status statistics
const getRFQStatusStatistics = async (req, res) => {
  try {
    console.log('Fetching RFQ status statistics');
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const statistics = await RFQ.getStatusStatistics(userId);
    
    res.json({
      success: true,
      data: statistics,
      message: 'Status statistics retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching RFQ status statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch status statistics',
      error: error.message,
    });
  }
};

// Get RFQ audit history
const getRFQAuditHistory = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Fetching audit history for RFQ:', id);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    const auditHistory = await RFQ.getAuditHistory(id, userId);
    
    res.json({
      success: true,
      data: auditHistory,
      message: 'Audit history retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching RFQ audit history:', error);
    
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to fetch audit history',
      error: error.message,
    });
  }
};

// Recalculate RFQ analytics
const recalculateRFQAnalytics = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Recalculating analytics for RFQ:', id);
    
    // TODO: Extract user ID from JWT token
    const userId = req.user?.id || 1;
    
    // Check if user has access to this RFQ
    const rfq = await RFQ.findById(id, userId);
    if (!rfq) {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }
    
    const analytics = await RFQ.calculateAnalytics(id);
    
    res.json({
      success: true,
      data: analytics,
      message: 'Analytics recalculated successfully',
    });
  } catch (error) {
    console.error('Error recalculating RFQ analytics:', error);
    
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to recalculate analytics',
      error: error.message,
    });
  }
};

// Bulk operations validation schemas
const bulkUpdateSchema = Joi.object({
  rfq_ids: Joi.array().items(Joi.number().integer().positive()).min(1).max(50).required(),
  updates: Joi.object({
    status: Joi.string().valid('draft', 'sent', 'in_progress', 'closed', 'cancelled').optional(),
    due_date: Joi.date().iso().optional(),
    currency: Joi.string().valid('USD', 'EUR', 'GBP', 'JPY', 'CNY').optional()
  }).min(1).required()
});

const bulkDeleteSchema = Joi.object({
  rfq_ids: Joi.array().items(Joi.number().integer().positive()).min(1).max(50).required(),
  force: Joi.boolean().default(false)
});

// Bulk update RFQs
const bulkUpdateRFQs = async (req, res) => {
  try {
    console.log('Bulk updating RFQs:', req.body);
    
    // Validate request body
    const { error, value } = bulkUpdateSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request data',
        errors: error.details.map(d => ({
          field: d.path.join('.'),
          message: d.message
        }))
      });
    }

    const { rfq_ids, updates } = value;
    const userId = req.user?.id || 1;

    const result = await RFQ.bulkUpdate(rfq_ids, updates, userId);
    
    res.json({
      success: true,
      data: result,
      message: `Successfully updated ${result.updated_count} RFQs`,
    });
  } catch (error) {
    console.error('Error bulk updating RFQs:', error);
    
    if (error.message.includes('access denied') || error.message.includes('not found')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied or some RFQs not found',
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to bulk update RFQs',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
};

// Bulk delete RFQs
const bulkDeleteRFQs = async (req, res) => {
  try {
    console.log('Bulk deleting RFQs:', req.body);
    
    // Validate request body
    const { error, value } = bulkDeleteSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request data',
        errors: error.details.map(d => ({
          field: d.path.join('.'),
          message: d.message
        }))
      });
    }

    const { rfq_ids, force } = value;
    const userId = req.user?.id || 1;

    const result = await RFQ.bulkDelete(rfq_ids, force, userId);
    
    res.json({
      success: true,
      data: result,
      message: `Successfully deleted ${result.deleted_count} RFQs`,
    });
  } catch (error) {
    console.error('Error bulk deleting RFQs:', error);
    
    if (error.message.includes('access denied') || error.message.includes('not found')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied or some RFQs not found',
        error: error.message,
      });
    }

    if (error.message.includes('Cannot delete')) {
      return res.status(409).json({
        success: false,
        message: 'Some RFQs cannot be deleted due to their current status',
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to bulk delete RFQs',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
};

// Advanced search with full-text search capabilities
const searchRFQs = async (req, res) => {
  try {
    const searchSchema = Joi.object({
      query: Joi.string().min(1).max(255).required(),
      filters: Joi.object({
        status: Joi.array().items(Joi.string().valid('draft', 'sent', 'in_progress', 'closed', 'cancelled')).optional(),
        date_range: Joi.object({
          start: Joi.date().iso().optional(),
          end: Joi.date().iso().optional()
        }).optional(),
        creator_ids: Joi.array().items(Joi.number().integer().positive()).optional(),
        vendor_ids: Joi.array().items(Joi.number().integer().positive()).optional(),
        categories: Joi.array().items(Joi.string().max(100)).optional(),
        currencies: Joi.array().items(Joi.string().valid('USD', 'EUR', 'GBP', 'JPY', 'CNY')).optional(),
        amount_range: Joi.object({
          min: Joi.number().min(0).optional(),
          max: Joi.number().min(0).optional()
        }).optional()
      }).optional(),
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      sort_by: Joi.string().valid('relevance', 'created_at', 'updated_at', 'due_date', 'title').default('relevance'),
      sort_order: Joi.string().valid('asc', 'desc').default('desc')
    });

    const { error, value } = searchSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Invalid search parameters',
        errors: error.details.map(d => ({
          field: d.path.join('.'),
          message: d.message
        }))
      });
    }

    const userId = req.user?.id || 1;
    const result = await RFQ.advancedSearch(value, userId);
    
    res.json({
      success: true,
      data: result.rfqs,
      pagination: result.pagination,
      search_meta: {
        query: value.query,
        total_results: result.pagination.total,
        search_time_ms: result.search_time_ms
      }
    });
  } catch (error) {
    console.error('Error searching RFQs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search RFQs',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
};

// Get RFQ summary statistics
const getRFQSummaryStats = async (req, res) => {
  try {
    const userId = req.user?.id || 1;
    const stats = await RFQ.getSummaryStatistics(userId);
    
    res.json({
      success: true,
      data: stats,
      message: 'Summary statistics retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching RFQ summary stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch summary statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
};

// Export RFQs data
const exportRFQs = async (req, res) => {
  try {
    const exportSchema = Joi.object({
      format: Joi.string().valid('csv', 'json', 'xlsx').default('csv'),
      filters: Joi.object({
        status: Joi.array().items(Joi.string().valid('draft', 'sent', 'in_progress', 'closed', 'cancelled')).optional(),
        date_range: Joi.object({
          start: Joi.date().iso().optional(),
          end: Joi.date().iso().optional()
        }).optional(),
        creator_ids: Joi.array().items(Joi.number().integer().positive()).optional()
      }).optional(),
      include_submissions: Joi.boolean().default(false),
      include_analytics: Joi.boolean().default(false)
    });

    const { error, value } = exportSchema.validate(req.query);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Invalid export parameters',
        errors: error.details.map(d => ({
          field: d.path.join('.'),
          message: d.message
        }))
      });
    }

    const userId = req.user?.id || 1;
    const exportData = await RFQ.exportData(value, userId);
    
    // Set appropriate headers based on format
    const { format } = value;
    const timestamp = new Date().toISOString().split('T')[0];
    
    if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="rfqs_export_${timestamp}.csv"`);
    } else if (format === 'xlsx') {
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="rfqs_export_${timestamp}.xlsx"`);
    } else {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="rfqs_export_${timestamp}.json"`);
    }
    
    res.send(exportData);
  } catch (error) {
    console.error('Error exporting RFQs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export RFQs',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
};

module.exports = {
  getRFQs,
  getRFQById,
  createRFQ,
  updateRFQ,
  deleteRFQ,
  sendRFQInvitations,
  getRFQInvitations,
  getRFQSubmissions,
  getRFQSubmissionsComparison,
  generateAIRecommendations,
  getRFQAnalytics,
  getRFQStatusStatistics,
  getRFQAuditHistory,
  recalculateRFQAnalytics,
  // New enhanced endpoints
  bulkUpdateRFQs,
  bulkDeleteRFQs,
  searchRFQs,
  getRFQSummaryStats,
  exportRFQs,
};