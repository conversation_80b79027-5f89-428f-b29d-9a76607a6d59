const OpportunityService = require('../services/OpportunityService');
const { 
  validateOpportunityCreate, 
  validateOpportunityUpdate, 
  validateOpportunitySearch 
} = require('../validation/opportunityValidation');

class OpportunityController {
  // Get all opportunities with filtering and pagination
  static async getAllOpportunities(req, res) {
    try {
      const searchParams = {
        search: req.query.search,
        account_id: req.query.account_id ? parseInt(req.query.account_id) : undefined,
        stage_name: req.query.stage_name,
        owner_id: req.query.owner_id ? parseInt(req.query.owner_id) : undefined,
        type: req.query.type,
        lead_source: req.query.lead_source,
        forecast_category: req.query.forecast_category,
        is_closed: req.query.is_closed === 'true' ? true : req.query.is_closed === 'false' ? false : undefined,
        is_won: req.query.is_won === 'true' ? true : req.query.is_won === 'false' ? false : undefined,
        min_amount: req.query.min_amount ? parseFloat(req.query.min_amount) : undefined,
        max_amount: req.query.max_amount ? parseFloat(req.query.max_amount) : undefined,
        close_date_from: req.query.close_date_from,
        close_date_to: req.query.close_date_to,
        created_after: req.query.created_after,
        created_before: req.query.created_before,
        sort_by: req.query.sort_by || 'created_at',
        sort_order: req.query.sort_order || 'desc',
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10
      };

      // Validate search parameters
      const validation = await validateOpportunitySearch(searchParams);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Invalid search parameters',
          errors: validation.errors
        });
      }

      const result = await OpportunityService.getOpportunities(validation.data, searchParams.page, searchParams.limit);

      res.json({
        success: true,
        data: result,
        message: `Retrieved ${result.opportunities.length} opportunities`
      });
    } catch (error) {
      console.error('Error in getAllOpportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunities',
        details: error.message
      });
    }
  }

  // Get opportunity by ID
  static async getOpportunityById(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid opportunity ID'
        });
      }

      const opportunity = await OpportunityService.getOpportunityById(parseInt(id));

      res.json({
        success: true,
        data: opportunity,
        message: 'Opportunity retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getOpportunityById:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: 'Opportunity not found'
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunity',
        details: error.message
      });
    }
  }

  // Transform camelCase to snake_case for database compatibility
  static transformOpportunityData(data) {
    const transformed = {};
    
    // Field mappings from camelCase to snake_case
    const fieldMappings = {
      accountId: 'account_id',
      stageName: 'stage_name',
      closeDate: 'close_date',
      leadSource: 'lead_source',
      isPrivate: 'is_private',
      forecastCategory: 'forecast_category',
      nextStep: 'next_step',
      isClosed: 'is_closed',
      isWon: 'is_won',
      ownerId: 'owner_id',
      campaignId: 'campaign_id',
      hasOpportunityLineItem: 'has_opportunity_line_item',
      customFields: 'custom_fields',
      integrationId: 'integration_id'
    };

    // Transform fields
    Object.keys(data).forEach(key => {
      const dbField = fieldMappings[key] || key;
      transformed[dbField] = data[key];
    });

    return transformed;
  }

  // Create new opportunity
  static async createOpportunity(req, res) {
    try {
      // Validate input data
      const validation = await validateOpportunityCreate(req.body);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          errors: validation.errors
        });
      }

      // Transform data for database
      const opportunityData = OpportunityController.transformOpportunityData(validation.data);
      
      // Get user ID from request (assuming it's set by auth middleware)
      const userId = req.user?.id || 1;

      const opportunity = await OpportunityService.createOpportunity(opportunityData, userId);

      res.status(201).json({
        success: true,
        data: opportunity,
        message: 'Opportunity created successfully'
      });
    } catch (error) {
      console.error('Error in createOpportunity:', error);
      
      if (error.message.includes('Validation failed') || 
          error.message.includes('not found') ||
          error.message.includes('already exists')) {
        return res.status(400).json({
          success: false,
          error: error.message
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to create opportunity',
        details: error.message
      });
    }
  }

  // Update opportunity
  static async updateOpportunity(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid opportunity ID'
        });
      }

      // Validate input data
      const validation = await validateOpportunityUpdate(req.body);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          errors: validation.errors
        });
      }

      // Transform data for database
      const updateData = OpportunityController.transformOpportunityData(validation.data);
      
      // Get user ID from request
      const userId = req.user?.id || 1;

      const opportunity = await OpportunityService.updateOpportunity(parseInt(id), updateData, userId);

      res.json({
        success: true,
        data: opportunity,
        message: 'Opportunity updated successfully'
      });
    } catch (error) {
      console.error('Error in updateOpportunity:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: 'Opportunity not found'
        });
      }

      if (error.message.includes('Validation failed') ||
          error.message.includes('Invalid stage transition') ||
          error.message.includes('already exists')) {
        return res.status(400).json({
          success: false,
          error: error.message
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to update opportunity',
        details: error.message
      });
    }
  }

  // Delete opportunity (soft delete)
  static async deleteOpportunity(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid opportunity ID'
        });
      }

      // Get user ID from request
      const userId = req.user?.id || 1;

      const success = await OpportunityService.deleteOpportunity(parseInt(id), userId);

      if (success) {
        res.json({
          success: true,
          message: 'Opportunity deleted successfully'
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to delete opportunity'
        });
      }
    } catch (error) {
      console.error('Error in deleteOpportunity:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: 'Opportunity not found'
        });
      }

      if (error.message.includes('Cannot delete')) {
        return res.status(400).json({
          success: false,
          error: error.message
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to delete opportunity',
        details: error.message
      });
    }
  }

  // Get opportunities by account
  static async getAccountOpportunities(req, res) {
    try {
      const { accountId } = req.params;
      const includeClosed = req.query.include_closed === 'true';

      if (!accountId || isNaN(parseInt(accountId))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const options = {
        include_closed: includeClosed
      };

      const opportunities = await OpportunityService.getAccountOpportunities(parseInt(accountId), options);

      res.json({
        success: true,
        data: opportunities,
        message: `Retrieved ${opportunities.length} opportunities for account`
      });
    } catch (error) {
      console.error('Error in getAccountOpportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve account opportunities',
        details: error.message
      });
    }
  }

  // Get opportunities by owner
  static async getOwnerOpportunities(req, res) {
    try {
      const { ownerId } = req.params;
      const includeClosed = req.query.include_closed === 'true';

      if (!ownerId || isNaN(parseInt(ownerId))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid owner ID'
        });
      }

      const options = {
        include_closed: includeClosed
      };

      const opportunities = await OpportunityService.getOwnerOpportunities(parseInt(ownerId), options);

      res.json({
        success: true,
        data: opportunities,
        message: `Retrieved ${opportunities.length} opportunities for owner`
      });
    } catch (error) {
      console.error('Error in getOwnerOpportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve owner opportunities',
        details: error.message
      });
    }
  }

  // Bulk create opportunities
  static async bulkCreateOpportunities(req, res) {
    try {
      const { opportunities } = req.body;

      if (!opportunities || !Array.isArray(opportunities) || opportunities.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid opportunities data. Expected non-empty array.'
        });
      }

      // Get user ID from request
      const userId = req.user?.id || 1;

      const results = [];
      const errors = [];

      for (let i = 0; i < opportunities.length; i++) {
        try {
          // Validate each opportunity
          const validation = await validateOpportunityCreate(opportunities[i]);
          if (!validation.isValid) {
            errors.push({
              index: i,
              errors: validation.errors
            });
            continue;
          }

          // Transform and create
          const opportunityData = OpportunityController.transformOpportunityData(validation.data);
          const opportunity = await OpportunityService.createOpportunity(opportunityData, userId);
          results.push(opportunity);
        } catch (error) {
          errors.push({
            index: i,
            error: error.message
          });
        }
      }

      res.status(201).json({
        success: true,
        data: {
          created: results,
          errors: errors
        },
        message: `Created ${results.length} opportunities, ${errors.length} errors`
      });
    } catch (error) {
      console.error('Error in bulkCreateOpportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to bulk create opportunities',
        details: error.message
      });
    }
  }

  // Bulk update opportunities
  static async bulkUpdateOpportunities(req, res) {
    try {
      const { updates } = req.body;

      if (!updates || !Array.isArray(updates) || updates.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid updates data. Expected non-empty array.'
        });
      }

      // Get user ID from request
      const userId = req.user?.id || 1;

      // Transform updates for service
      const transformedUpdates = updates.map(update => ({
        id: update.id,
        updateData: OpportunityController.transformOpportunityData(update.updateData)
      }));

      const results = await OpportunityService.bulkUpdateOpportunities(transformedUpdates, userId);

      const successCount = results.filter(r => r.success).length;
      const errorCount = results.filter(r => !r.success).length;

      res.json({
        success: true,
        data: results,
        message: `Updated ${successCount} opportunities, ${errorCount} errors`
      });
    } catch (error) {
      console.error('Error in bulkUpdateOpportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to bulk update opportunities',
        details: error.message
      });
    }
  }

  // Get pipeline data for kanban view
  static async getPipelineData(req, res) {
    try {
      const filters = {
        owner_id: req.query.owner_id ? parseInt(req.query.owner_id) : undefined,
        account_id: req.query.account_id ? parseInt(req.query.account_id) : undefined,
        type: req.query.type
      };

      const pipelineData = await OpportunityService.getPipelineData(filters);

      res.json({
        success: true,
        data: pipelineData,
        message: 'Pipeline data retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getPipelineData:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve pipeline data',
        details: error.message
      });
    }
  }

  // Advance opportunity stage
  static async advanceStage(req, res) {
    try {
      const { id } = req.params;
      const { stage_name } = req.body;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid opportunity ID'
        });
      }

      if (!stage_name) {
        return res.status(400).json({
          success: false,
          error: 'Stage name is required'
        });
      }

      // Get user ID from request
      const userId = req.user?.id || 1;

      const opportunity = await OpportunityService.advanceStage(parseInt(id), stage_name, userId);

      res.json({
        success: true,
        data: opportunity,
        message: 'Stage advanced successfully'
      });
    } catch (error) {
      console.error('Error in advanceStage:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: 'Opportunity not found'
        });
      }

      if (error.message.includes('Invalid stage transition')) {
        return res.status(400).json({
          success: false,
          error: error.message
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to advance stage',
        details: error.message
      });
    }
  }

  // Get opportunity analytics
  static async getAnalytics(req, res) {
    try {
      const filters = {
        owner_id: req.query.owner_id ? parseInt(req.query.owner_id) : undefined,
        date_from: req.query.date_from,
        date_to: req.query.date_to
      };

      const analytics = await OpportunityService.getAnalytics(filters);

      res.json({
        success: true,
        data: analytics,
        message: 'Analytics retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getAnalytics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve analytics',
        details: error.message
      });
    }
  }

  // Get opportunity statistics
  static async getOpportunityStatistics(req, res) {
    try {
      const filters = {
        owner_id: req.query.owner_id ? parseInt(req.query.owner_id) : undefined,
        account_id: req.query.account_id ? parseInt(req.query.account_id) : undefined,
        date_from: req.query.date_from,
        date_to: req.query.date_to
      };

      const statistics = await OpportunityService.getOpportunityStatistics(filters);

      res.json({
        success: true,
        data: statistics,
        message: 'Statistics retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getOpportunityStatistics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve statistics',
        details: error.message
      });
    }
  }

  // Export opportunities
  static async exportOpportunities(req, res) {
    try {
      const format = req.query.format || 'csv';
      const filters = {
        search: req.query.search,
        account_id: req.query.account_id ? parseInt(req.query.account_id) : undefined,
        stage_name: req.query.stage_name,
        owner_id: req.query.owner_id ? parseInt(req.query.owner_id) : undefined,
        type: req.query.type,
        lead_source: req.query.lead_source,
        forecast_category: req.query.forecast_category,
        is_closed: req.query.is_closed === 'true' ? true : req.query.is_closed === 'false' ? false : undefined,
        is_won: req.query.is_won === 'true' ? true : req.query.is_won === 'false' ? false : undefined
      };

      const csvData = await OpportunityService.exportOpportunities(filters);

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=opportunities.csv');
      res.send(csvData);
    } catch (error) {
      console.error('Error in exportOpportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to export opportunities',
        details: error.message
      });
    }
  }

  // Get available stages
  static async getStages(req, res) {
    try {
      const stages = OpportunityService.getStages();
      res.json({
        success: true,
        data: stages,
        message: 'Stages retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getStages:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve stages',
        details: error.message
      });
    }
  }

  // Get available types
  static async getTypes(req, res) {
    try {
      const types = OpportunityService.getTypes();
      res.json({
        success: true,
        data: types,
        message: 'Types retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getTypes:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve types',
        details: error.message
      });
    }
  }

  // Get available lead sources
  static async getLeadSources(req, res) {
    try {
      const leadSources = OpportunityService.getLeadSources();
      res.json({
        success: true,
        data: leadSources,
        message: 'Lead sources retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getLeadSources:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve lead sources',
        details: error.message
      });
    }
  }

  // Get available forecast categories
  static async getForecastCategories(req, res) {
    try {
      const forecastCategories = OpportunityService.getForecastCategories();
      res.json({
        success: true,
        data: forecastCategories,
        message: 'Forecast categories retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getForecastCategories:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve forecast categories',
        details: error.message
      });
    }
  }
}

module.exports = OpportunityController;