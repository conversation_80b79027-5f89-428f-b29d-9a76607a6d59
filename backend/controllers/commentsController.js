const Comment = require('../models/Comment');
const { validationResult } = require('express-validator');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/comments');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 5 // Max 5 files per comment
  },
  fileFilter: (req, file, cb) => {
    // Allow common file types
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|txt|csv|xlsx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only images, PDFs, and documents are allowed.'));
    }
  }
});

class CommentsController {
  // Get comments for an object (with threading)
  static async getComments(req, res) {
    try {
      const { objectType, objectId } = req.params;
      const { category, includeDeleted = false } = req.query;
      
      const options = {
        category: category || null,
        includeDeleted: includeDeleted === 'true'
      };
      
      const comments = await Comment.getThread(objectType, parseInt(objectId), options);
      
      // Build threaded structure
      const threaded = CommentsController.buildThreadStructure(comments);
      
      res.json({
        success: true,
        data: threaded,
        meta: {
          total: comments.length,
          objectType,
          objectId: parseInt(objectId)
        }
      });
    } catch (error) {
      console.error('Error getting comments:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve comments',
        error: error.message
      });
    }
  }

  // Create a new comment
  static async createComment(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const {
        object_type,
        object_id,
        parent_id,
        content,
        content_type = 'text',
        category,
        metadata
      } = req.body;

      const commentData = {
        object_type,
        object_id: parseInt(object_id),
        parent_id: parent_id ? parseInt(parent_id) : null,
        user_id: req.user.id,
        content,
        content_type,
        category,
        metadata
      };

      const comment = await Comment.create(commentData);
      
      // Process mentions if any
      const mentions = CommentsController.extractMentions(content);
      if (mentions.length > 0) {
        for (const mentionedUserId of mentions) {
          await comment.addMention(mentionedUserId);
        }
      }

      // Emit real-time event
      if (req.io) {
        req.io.to(`${object_type}-${object_id}`).emit('comment:created', {
          comment: comment.toJSON(),
          mentions
        });
      }

      res.status(201).json({
        success: true,
        data: comment.toJSON(),
        message: 'Comment created successfully'
      });
    } catch (error) {
      console.error('Error creating comment:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create comment',
        error: error.message
      });
    }
  }

  // Update a comment
  static async updateComment(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { id } = req.params;
      const updateData = req.body;
      
      const comment = await Comment.update(parseInt(id), updateData, req.user.id);
      
      // Process new mentions if content was updated
      if (updateData.content) {
        const mentions = CommentsController.extractMentions(updateData.content);
        if (mentions.length > 0) {
          for (const mentionedUserId of mentions) {
            await comment.addMention(mentionedUserId);
          }
        }
      }

      // Emit real-time event
      if (req.io) {
        req.io.to(`${comment.entity_type}-${comment.entity_id}`).emit('comment:updated', {
          comment: comment.toJSON()
        });
      }

      res.json({
        success: true,
        data: comment.toJSON(),
        message: 'Comment updated successfully'
      });
    } catch (error) {
      console.error('Error updating comment:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update comment',
        error: error.message
      });
    }
  }

  // Delete a comment
  static async deleteComment(req, res) {
    try {
      const { id } = req.params;
      const success = await Comment.delete(parseInt(id), req.user.id);
      
      if (!success) {
        return res.status(404).json({
          success: false,
          message: 'Comment not found or unauthorized'
        });
      }

      // Emit real-time event
      if (req.io) {
        req.io.emit('comment:deleted', {
          commentId: parseInt(id),
          userId: req.user.id
        });
      }

      res.json({
        success: true,
        message: 'Comment deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting comment:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete comment',
        error: error.message
      });
    }
  }

  // Get a single comment
  static async getComment(req, res) {
    try {
      const { id } = req.params;
      const comment = await Comment.findById(parseInt(id));
      
      if (!comment) {
        return res.status(404).json({
          success: false,
          message: 'Comment not found'
        });
      }

      // Get attachments and mentions
      const [attachments, mentions] = await Promise.all([
        comment.getAttachments(),
        comment.getMentions()
      ]);

      const commentData = comment.toJSON();
      commentData.attachments = attachments;
      commentData.mentions = mentions;

      res.json({
        success: true,
        data: commentData
      });
    } catch (error) {
      console.error('Error getting comment:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve comment',
        error: error.message
      });
    }
  }

  // Upload attachments to a comment
  static async uploadAttachments(req, res) {
    const uploadMiddleware = upload.array('attachments', 5);
    
    uploadMiddleware(req, res, async (err) => {
      if (err) {
        return res.status(400).json({
          success: false,
          message: 'File upload failed',
          error: err.message
        });
      }

      try {
        const { id } = req.params;
        const comment = await Comment.findById(parseInt(id));
        
        if (!comment) {
          return res.status(404).json({
            success: false,
            message: 'Comment not found'
          });
        }

        // Check if user owns the comment
        if (comment.user_id !== req.user.id) {
          return res.status(403).json({
            success: false,
            message: 'Unauthorized to add attachments to this comment'
          });
        }

        const attachments = [];
        
        for (const file of req.files) {
          const attachmentData = {
            filename: file.filename,
            original_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            file_path: file.path,
            uploaded_by: req.user.id
          };
          
          const attachment = await comment.addAttachment(attachmentData);
          attachments.push(attachment);
        }

        // Emit real-time event
        if (req.io) {
          req.io.to(`${comment.entity_type}-${comment.entity_id}`).emit('comment:attachments_added', {
            commentId: comment.id,
            attachments
          });
        }

        res.json({
          success: true,
          data: attachments,
          message: 'Attachments uploaded successfully'
        });
      } catch (error) {
        console.error('Error uploading attachments:', error);
        res.status(500).json({
          success: false,
          message: 'Failed to upload attachments',
          error: error.message
        });
      }
    });
  }

  // Search comments
  static async searchComments(req, res) {
    try {
      const { q: searchTerm, objectType, category, limit = 50, offset = 0 } = req.query;
      
      if (!searchTerm || searchTerm.trim().length < 2) {
        return res.status(400).json({
          success: false,
          message: 'Search term must be at least 2 characters long'
        });
      }

      const options = {
        objectType: objectType || null,
        category: category || null,
        limit: parseInt(limit),
        offset: parseInt(offset)
      };

      const comments = await Comment.search(searchTerm.trim(), options);

      res.json({
        success: true,
        data: comments.map(comment => comment.toJSON()),
        meta: {
          searchTerm,
          total: comments.length,
          limit: options.limit,
          offset: options.offset
        }
      });
    } catch (error) {
      console.error('Error searching comments:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to search comments',
        error: error.message
      });
    }
  }

  // Get comment statistics
  static async getStats(req, res) {
    try {
      const { objectType, objectId } = req.query;
      
      const stats = await Comment.getStats(
        objectType || null,
        objectId ? parseInt(objectId) : null
      );

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Error getting comment stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve comment statistics',
        error: error.message
      });
    }
  }

  // Get user's comments
  static async getUserComments(req, res) {
    try {
      const { userId } = req.params;
      const { objectType, limit = 50, offset = 0 } = req.query;
      
      const options = {
        objectType: objectType || null,
        limit: parseInt(limit),
        offset: parseInt(offset)
      };

      const comments = await Comment.getByUser(parseInt(userId), options);

      res.json({
        success: true,
        data: comments.map(comment => comment.toJSON()),
        meta: {
          userId: parseInt(userId),
          total: comments.length,
          limit: options.limit,
          offset: options.offset
        }
      });
    } catch (error) {
      console.error('Error getting user comments:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve user comments',
        error: error.message
      });
    }
  }

  // Helper method to build threaded comment structure
  static buildThreadStructure(comments) {
    const commentMap = new Map();
    const rootComments = [];

    // First pass: create comment objects and map them
    comments.forEach(comment => {
      const commentObj = comment.toJSON ? comment.toJSON() : comment;
      commentObj.replies = [];
      commentMap.set(commentObj.id, commentObj);
    });

    // Second pass: build the tree structure
    comments.forEach(comment => {
      const commentObj = commentMap.get(comment.id);
      if (comment.parent_id) {
        const parent = commentMap.get(comment.parent_id);
        if (parent) {
          parent.replies.push(commentObj);
        }
      } else {
        rootComments.push(commentObj);
      }
    });

    return rootComments;
  }

  // Helper method to extract user mentions from content
  static extractMentions(content) {
    const mentionRegex = /@\[(\d+)\]/g;
    const mentions = [];
    let match;
    
    while ((match = mentionRegex.exec(content)) !== null) {
      const userId = parseInt(match[1]);
      if (!mentions.includes(userId)) {
        mentions.push(userId);
      }
    }
    
    return mentions;
  }

  // Real-time typing indicator
  static async handleTyping(req, res) {
    try {
      const { objectType, objectId } = req.params;
      const { isTyping } = req.body;
      
      if (req.io) {
        req.io.to(`${objectType}-${objectId}`).emit('user:typing', {
          userId: req.user.id,
          userName: req.user.name,
          isTyping,
          timestamp: new Date().toISOString()
        });
      }

      res.json({
        success: true,
        message: 'Typing status updated'
      });
    } catch (error) {
      console.error('Error handling typing:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update typing status',
        error: error.message
      });
    }
  }
}

module.exports = CommentsController;