const Account = require('../models/Account');
const { 
  validateCreateAccount, 
  validateUpdateAccount, 
  validateSearchFilters 
} = require('../validation/accountValidation');

class AccountController {
  // Get all accounts with filtering and pagination
  static async getAllAccounts(req, res) {
    try {
      // Validation is handled by middleware
      const filters = req.validatedFilters || {};
      const { page, limit, ...searchFilters } = filters;

      const result = await Account.findAll(searchFilters, page, limit);

      res.json({
        success: true,
        data: result,
        message: `Retrieved ${result.accounts.length} accounts`
      });
    } catch (error) {
      console.error('Error in getAllAccounts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve accounts',
        details: error.message
      });
    }
  }

  // Get account by ID
  static async getAccountById(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const account = await Account.findById(parseInt(id));

      if (!account) {
        return res.status(404).json({
          success: false,
          error: 'Account not found'
        });
      }

      res.json({
        success: true,
        data: account,
        message: 'Account retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getAccountById:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve account',
        details: error.message
      });
    }
  }

  // Create new account
  static async createAccount(req, res) {
    try {
      // Validation is handled by middleware
      const accountData = req.validatedData;
      const userId = req.user?.id || 1; // Get from auth middleware

      // Additional business logic validation
      if (accountData.parent_account_id) {
        const parentAccount = await Account.findById(accountData.parent_account_id);
        if (!parentAccount) {
          return res.status(400).json({
            success: false,
            error: 'Parent account not found'
          });
        }
      }

      // Check for duplicate account names (case-insensitive)
      const existingAccounts = await Account.findAll({ search: accountData.name }, 1, 1);
      const duplicateAccount = existingAccounts.accounts.find(
        acc => acc.name.toLowerCase() === accountData.name.toLowerCase()
      );

      if (duplicateAccount) {
        return res.status(400).json({
          success: false,
          error: 'Account with this name already exists',
          details: `An account named "${duplicateAccount.name}" already exists`
        });
      }

      const account = await Account.create(accountData, userId);

      res.status(201).json({
        success: true,
        data: account,
        message: 'Account created successfully'
      });
    } catch (error) {
      console.error('Error in createAccount:', error);
      
      if (error.message.includes('duplicate key') || error.message.includes('already exists')) {
        return res.status(400).json({
          success: false,
          error: 'Account with this name already exists'
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to create account',
        details: error.message
      });
    }
  }

  // Update account
  static async updateAccount(req, res) {
    try {
      const { id } = req.params;
      const accountData = req.validatedData;
      const userId = req.user?.id || 1;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      // Check if account exists
      const existingAccount = await Account.findById(parseInt(id));
      if (!existingAccount) {
        return res.status(404).json({
          success: false,
          error: 'Account not found'
        });
      }

      // Additional business logic validation
      if (accountData.parent_account_id && accountData.parent_account_id !== existingAccount.parent_account_id) {
        if (accountData.parent_account_id === parseInt(id)) {
          return res.status(400).json({
            success: false,
            error: 'Account cannot be its own parent'
          });
        }

        const parentAccount = await Account.findById(accountData.parent_account_id);
        if (!parentAccount) {
          return res.status(400).json({
            success: false,
            error: 'Parent account not found'
          });
        }
      }

      // Check for duplicate names if name is being changed
      if (accountData.name && accountData.name.toLowerCase() !== existingAccount.name.toLowerCase()) {
        const existingAccounts = await Account.findAll({ search: accountData.name }, 1, 1);
        const duplicateAccount = existingAccounts.accounts.find(
          acc => acc.name.toLowerCase() === accountData.name.toLowerCase() && acc.id !== parseInt(id)
        );

        if (duplicateAccount) {
          return res.status(400).json({
            success: false,
            error: 'Account with this name already exists',
            details: `An account named "${duplicateAccount.name}" already exists`
          });
        }
      }

      const updatedAccount = await Account.update(parseInt(id), accountData, userId);

      res.json({
        success: true,
        data: updatedAccount,
        message: 'Account updated successfully'
      });
    } catch (error) {
      console.error('Error in updateAccount:', error);
      
      if (error.message.includes('Circular hierarchy')) {
        return res.status(400).json({
          success: false,
          error: 'Invalid hierarchy: Circular reference detected'
        });
      }

      if (error.message.includes('duplicate key') || error.message.includes('already exists')) {
        return res.status(400).json({
          success: false,
          error: 'Account with this name already exists'
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to update account',
        details: error.message
      });
    }
  }

  // Delete account (soft delete)
  static async deleteAccount(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id || 1;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const result = await Account.delete(parseInt(id), userId);

      if (!result) {
        return res.status(404).json({
          success: false,
          error: 'Account not found'
        });
      }

      res.json({
        success: true,
        message: 'Account deleted successfully'
      });
    } catch (error) {
      console.error('Error in deleteAccount:', error);
      
      if (error.message.includes('child accounts')) {
        return res.status(400).json({
          success: false,
          error: 'Cannot delete account with child accounts',
          details: 'Please delete or reassign child accounts first'
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to delete account',
        details: error.message
      });
    }
  }

  // Get account hierarchy
  static async getAccountHierarchy(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const hierarchy = await Account.getHierarchy(parseInt(id));

      if (hierarchy.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'Account not found'
        });
      }

      res.json({
        success: true,
        data: hierarchy,
        message: 'Account hierarchy retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getAccountHierarchy:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve account hierarchy',
        details: error.message
      });
    }
  }

  // Get account children
  static async getAccountChildren(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const children = await Account.getChildren(parseInt(id));

      res.json({
        success: true,
        data: children,
        message: `Retrieved ${children.length} child accounts`
      });
    } catch (error) {
      console.error('Error in getAccountChildren:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve child accounts',
        details: error.message
      });
    }
  }

  // Get account ancestors
  static async getAccountAncestors(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const ancestors = await Account.getAncestors(parseInt(id));

      res.json({
        success: true,
        data: ancestors,
        message: `Retrieved ${ancestors.length} ancestor accounts`
      });
    } catch (error) {
      console.error('Error in getAccountAncestors:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve ancestor accounts',
        details: error.message
      });
    }
  }

  // Search accounts
  static async searchAccounts(req, res) {
    try {
      // Validation is handled by middleware
      const searchParams = req.validatedFilters || {};

      const result = await Account.search(searchParams);

      res.json({
        success: true,
        data: result,
        message: `Found ${result.accounts.length} accounts matching search criteria`
      });
    } catch (error) {
      console.error('Error in searchAccounts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to search accounts',
        details: error.message
      });
    }
  }

  // Export accounts
  static async exportAccounts(req, res) {
    try {
      const { format = 'csv' } = req.query;
      const filters = req.validatedFilters || {};

      if (!['csv', 'json'].includes(format)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid export format. Supported formats: csv, json'
        });
      }

      if (format === 'csv') {
        const csvData = await Account.exportToCSV(filters);
        
        // Convert to CSV string
        const csvContent = [
          csvData.headers.join(','),
          ...csvData.rows.map(row => 
            row.map(cell => 
              typeof cell === 'string' && cell.includes(',') 
                ? `"${cell.replace(/"/g, '""')}"` 
                : cell
            ).join(',')
          )
        ].join('\n');

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=accounts.csv');
        res.send(csvContent);
      } else if (format === 'json') {
        const { accounts } = await Account.findAll(filters, 1, 10000);
        
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', 'attachment; filename=accounts.json');
        res.json({
          success: true,
          data: accounts,
          exported_at: new Date().toISOString(),
          total_records: accounts.length
        });
      }
    } catch (error) {
      console.error('Error in exportAccounts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to export accounts',
        details: error.message
      });
    }
  }

  // Get account audit history
  static async getAccountAuditHistory(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const auditHistory = await Account.getAuditHistory(parseInt(id));

      res.json({
        success: true,
        data: auditHistory,
        message: `Retrieved ${auditHistory.length} audit entries`
      });
    } catch (error) {
      console.error('Error in getAccountAuditHistory:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve audit history',
        details: error.message
      });
    }
  }

  // Get account types (for dropdowns)
  static async getAccountTypes(req, res) {
    try {
      const types = await Account.getTypes();

      res.json({
        success: true,
        data: types,
        message: 'Account types retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getAccountTypes:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve account types',
        details: error.message
      });
    }
  }

  // Get account industries (for dropdowns)
  static async getAccountIndustries(req, res) {
    try {
      const industries = await Account.getIndustries();

      res.json({
        success: true,
        data: industries,
        message: 'Account industries retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getAccountIndustries:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve account industries',
        details: error.message
      });
    }
  }

  // Get account statistics
  static async getAccountStatistics(req, res) {
    try {
      const { accounts } = await Account.findAll({}, 1, 10000); // Get all for stats
      
      const stats = {
        total_accounts: accounts.length,
        active_accounts: accounts.filter(a => a.status === 'ACTIVE').length,
        inactive_accounts: accounts.filter(a => a.status === 'INACTIVE').length,
        accounts_with_children: accounts.filter(a => a.children_count > 0).length,
        accounts_by_industry: {},
        accounts_by_type: {},
        accounts_by_rating: {},
        average_annual_revenue: 0,
        total_annual_revenue: 0,
        average_employees: 0
      };

      // Calculate industry distribution
      accounts.forEach(account => {
        if (account.industry) {
          stats.accounts_by_industry[account.industry] = 
            (stats.accounts_by_industry[account.industry] || 0) + 1;
        }
      });

      // Calculate type distribution
      accounts.forEach(account => {
        if (account.type) {
          stats.accounts_by_type[account.type] = 
            (stats.accounts_by_type[account.type] || 0) + 1;
        }
      });

      // Calculate rating distribution
      accounts.forEach(account => {
        if (account.rating) {
          stats.accounts_by_rating[account.rating] = 
            (stats.accounts_by_rating[account.rating] || 0) + 1;
        }
      });

      // Calculate financial averages
      const accountsWithRevenue = accounts.filter(a => a.annual_revenue);
      if (accountsWithRevenue.length > 0) {
        stats.total_annual_revenue = accountsWithRevenue.reduce((sum, a) => sum + parseFloat(a.annual_revenue), 0);
        stats.average_annual_revenue = stats.total_annual_revenue / accountsWithRevenue.length;
      }

      // Calculate employee averages
      const accountsWithEmployees = accounts.filter(a => a.number_of_employees);
      if (accountsWithEmployees.length > 0) {
        stats.average_employees = accountsWithEmployees.reduce((sum, a) => sum + a.number_of_employees, 0) / accountsWithEmployees.length;
      }

      res.json({
        success: true,
        data: stats,
        message: 'Account statistics retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getAccountStatistics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve account statistics',
        details: error.message
      });
    }
  }
  // Hierarchy-specific methods
  
  // Get hierarchy tree
  static async getHierarchyTree(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const AccountHierarchyService = require('../services/accountHierarchyService');
      const tree = await AccountHierarchyService.getHierarchyTree(parseInt(id));

      if (!tree) {
        return res.status(404).json({
          success: false,
          error: 'Account not found'
        });
      }

      res.json({
        success: true,
        data: tree,
        message: 'Hierarchy tree retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getHierarchyTree:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve hierarchy tree',
        details: error.message
      });
    }
  }

  // Get hierarchy path (breadcrumb)
  static async getHierarchyPath(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const AccountHierarchyService = require('../services/accountHierarchyService');
      const path = await AccountHierarchyService.getHierarchyPath(parseInt(id));

      res.json({
        success: true,
        data: path,
        message: 'Hierarchy path retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getHierarchyPath:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve hierarchy path',
        details: error.message
      });
    }
  }

  // Get hierarchy statistics
  static async getHierarchyStatistics(req, res) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      const AccountHierarchyService = require('../services/accountHierarchyService');
      const stats = await AccountHierarchyService.getHierarchyStatistics(parseInt(id));

      res.json({
        success: true,
        data: stats,
        message: 'Hierarchy statistics retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getHierarchyStatistics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve hierarchy statistics',
        details: error.message
      });
    }
  }

  // Move account in hierarchy
  static async moveAccount(req, res) {
    try {
      const { id } = req.params;
      const { parent_account_id } = req.body;
      const userId = req.user?.id || 1;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Invalid account ID'
        });
      }

      if (parent_account_id !== null && (isNaN(parseInt(parent_account_id)) || parseInt(parent_account_id) <= 0)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid parent account ID'
        });
      }

      const AccountHierarchyService = require('../services/accountHierarchyService');
      const updatedAccount = await AccountHierarchyService.moveAccount(
        parseInt(id), 
        parent_account_id ? parseInt(parent_account_id) : null, 
        userId
      );

      res.json({
        success: true,
        data: updatedAccount,
        message: 'Account moved successfully'
      });
    } catch (error) {
      console.error('Error in moveAccount:', error);
      
      if (error.message.includes('circular reference')) {
        return res.status(400).json({
          success: false,
          error: 'Invalid move: would create circular reference'
        });
      }

      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: error.message
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to move account',
        details: error.message
      });
    }
  }

  // Get root accounts
  static async getRootAccounts(req, res) {
    try {
      const AccountHierarchyService = require('../services/accountHierarchyService');
      const rootAccounts = await AccountHierarchyService.getRootAccounts();

      res.json({
        success: true,
        data: rootAccounts,
        message: `Retrieved ${rootAccounts.length} root accounts`
      });
    } catch (error) {
      console.error('Error in getRootAccounts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve root accounts',
        details: error.message
      });
    }
  }

  // Get hierarchy summary
  static async getHierarchySummary(req, res) {
    try {
      const AccountHierarchyService = require('../services/accountHierarchyService');
      const summary = await AccountHierarchyService.getHierarchySummary();

      res.json({
        success: true,
        data: summary,
        message: `Retrieved hierarchy summary for ${summary.length} accounts`
      });
    } catch (error) {
      console.error('Error in getHierarchySummary:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve hierarchy summary',
        details: error.message
      });
    }
  }
}

module.exports = AccountController;