const Invoice = require('../models/Invoice');
const Joi = require('joi');

// Validation schemas
const createInvoiceSchema = Joi.object({
  contractId: Joi.number().integer().optional(),
  vendorId: Joi.number().integer().required(),
  amount: Joi.number().positive().required(),
  currency: Joi.string().valid('USD', 'EUR', 'GBP', 'JPY', 'CNY').default('USD'),
  status: Joi.string().valid('draft', 'approved', 'paid', 'partially_paid', 'disputed').default('draft'),
  items: Joi.array().items(
    Joi.object({
      id: Joi.string().optional(),
      description: Joi.string().required(),
      quantity: Joi.number().positive().required(),
      unitPrice: Joi.number().min(0).required(),
      totalPrice: Joi.number().min(0).optional(),
      taxable: Joi.boolean().default(true)
    })
  ).min(1).required(),
  taxes: Joi.number().min(0).default(0),
  penalties: Joi.number().min(0).default(0),
  dueDate: Joi.date().optional(),
  approvedBy: Joi.number().integer().optional()
});

const updateInvoiceSchema = Joi.object({
  contractId: Joi.number().integer().optional(),
  vendorId: Joi.number().integer().optional(),
  amount: Joi.number().positive().optional(),
  currency: Joi.string().valid('USD', 'EUR', 'GBP', 'JPY', 'CNY').optional(),
  status: Joi.string().valid('draft', 'approved', 'paid', 'partially_paid', 'disputed').optional(),
  items: Joi.array().items(
    Joi.object({
      id: Joi.string().optional(),
      description: Joi.string().required(),
      quantity: Joi.number().positive().required(),
      unitPrice: Joi.number().min(0).required(),
      totalPrice: Joi.number().min(0).optional(),
      taxable: Joi.boolean().default(true)
    })
  ).min(1).optional(),
  taxes: Joi.number().min(0).optional(),
  penalties: Joi.number().min(0).optional(),
  dueDate: Joi.date().optional(),
  approvedBy: Joi.number().integer().optional(),
  allowPaidEdit: Joi.boolean().default(false)
});

const approveInvoiceSchema = Joi.object({
  comments: Joi.string().optional()
});

// Get all invoices with filtering and pagination
const getInvoices = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      vendorId,
      contractId,
      startDate,
      endDate,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    // Validate pagination parameters
    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(100, Math.max(1, parseInt(limit)));

    const filters = {
      page: pageNum,
      limit: limitNum,
      search,
      status,
      vendorId: vendorId ? parseInt(vendorId) : undefined,
      contractId: contractId ? parseInt(contractId) : undefined,
      startDate,
      endDate,
      sortBy,
      sortOrder
    };

    const result = await Invoice.findAll(filters);

    res.json({
      success: true,
      data: result.invoices,
      pagination: result.pagination
    });
  } catch (error) {
    console.error('Error fetching invoices:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch invoices',
      error: error.message
    });
  }
};

// Get invoice by ID
const getInvoiceById = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    if (!id || isNaN(parseInt(id))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invoice ID'
      });
    }

    const invoice = await Invoice.findById(parseInt(id), userId);

    res.json({
      success: true,
      data: invoice
    });
  } catch (error) {
    console.error('Error fetching invoice:', error);
    if (error.message === 'Invoice not found') {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to fetch invoice',
      error: error.message
    });
  }
};

// Create new invoice
const createInvoice = async (req, res) => {
  try {
    const { error, value } = createInvoiceSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details
      });
    }

    const userId = req.user.id;

    // Add IDs to items if not present
    const itemsWithIds = value.items.map(item => ({
      ...item,
      id: item.id || require('crypto').randomUUID(),
      totalPrice: item.quantity * item.unitPrice
    }));

    const invoiceData = {
      ...value,
      items: itemsWithIds
    };

    const invoice = await Invoice.create(invoiceData, userId);

    res.status(201).json({
      success: true,
      message: 'Invoice created successfully',
      data: invoice
    });
  } catch (error) {
    console.error('Error creating invoice:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create invoice',
      error: error.message
    });
  }
};

// Update invoice
const updateInvoice = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    if (!id || isNaN(parseInt(id))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invoice ID'
      });
    }

    const { error, value } = updateInvoiceSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details
      });
    }

    // Add IDs to items if not present and calculate total prices
    if (value.items) {
      value.items = value.items.map(item => ({
        ...item,
        id: item.id || require('crypto').randomUUID(),
        totalPrice: item.quantity * item.unitPrice
      }));
    }

    const invoice = await Invoice.update(parseInt(id), value, userId);

    res.json({
      success: true,
      message: 'Invoice updated successfully',
      data: invoice
    });
  } catch (error) {
    console.error('Error updating invoice:', error);
    if (error.message === 'Invoice not found') {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }
    if (error.message.includes('Cannot update')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to update invoice',
      error: error.message
    });
  }
};

// Approve invoice
const approveInvoice = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    if (!id || isNaN(parseInt(id))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invoice ID'
      });
    }

    // Check if user has permission to approve
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to approve invoices'
      });
    }

    const { error, value } = approveInvoiceSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details
      });
    }

    const invoice = await Invoice.approve(parseInt(id), userId, value.comments);

    res.json({
      success: true,
      message: 'Invoice approved successfully',
      data: invoice
    });
  } catch (error) {
    console.error('Error approving invoice:', error);
    if (error.message === 'Invoice not found') {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }
    if (error.message.includes('cannot be approved')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to approve invoice',
      error: error.message
    });
  }
};

// Delete invoice
const deleteInvoice = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    if (!id || isNaN(parseInt(id))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invoice ID'
      });
    }

    // Check if user has permission to delete
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to delete invoices'
      });
    }

    const invoice = await Invoice.delete(parseInt(id), userId);

    res.json({
      success: true,
      message: 'Invoice deleted successfully',
      data: invoice
    });
  } catch (error) {
    console.error('Error deleting invoice:', error);
    if (error.message === 'Invoice not found') {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }
    if (error.message.includes('Cannot delete')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to delete invoice',
      error: error.message
    });
  }
};

// Get invoice statistics
const getInvoiceStatistics = async (req, res) => {
  try {
    const { vendorId, startDate, endDate } = req.query;

    const filters = {
      vendorId: vendorId ? parseInt(vendorId) : undefined,
      startDate,
      endDate
    };

    const statistics = await Invoice.getStatistics(filters);

    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('Error fetching invoice statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch invoice statistics',
      error: error.message
    });
  }
};

// Generate invoice from contract
const generateInvoiceFromContract = async (req, res) => {
  try {
    const { contractId } = req.params;
    const userId = req.user.id;

    if (!contractId || isNaN(parseInt(contractId))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid contract ID'
      });
    }

    // Check if user has permission to create invoices
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to create invoices'
      });
    }

    const additionalData = req.body || {};
    const invoice = await Invoice.generateFromContract(parseInt(contractId), userId, additionalData);

    res.status(201).json({
      success: true,
      message: 'Invoice generated from contract successfully',
      data: invoice
    });
  } catch (error) {
    console.error('Error generating invoice from contract:', error);
    if (error.message === 'Contract not found') {
      return res.status(404).json({
        success: false,
        message: 'Contract not found'
      });
    }
    if (error.message.includes('No billable items')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to generate invoice from contract',
      error: error.message
    });
  }
};

// Reject invoice (change status back to draft)
const rejectInvoice = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { reason } = req.body;

    if (!id || isNaN(parseInt(id))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invoice ID'
      });
    }

    // Check if user has permission to reject
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to reject invoices'
      });
    }

    if (!reason) {
      return res.status(400).json({
        success: false,
        message: 'Rejection reason is required'
      });
    }

    const invoice = await Invoice.update(parseInt(id), { 
      status: 'draft',
      approvedBy: null 
    }, userId);

    // Add rejection comment
    await require('../config/database').query(`
      INSERT INTO comments (entity_type, entity_id, user_id, text, created_at)
      VALUES ('invoices', $1, $2, $3, CURRENT_TIMESTAMP)
    `, [id, userId, `Rejection: ${reason}`]);

    res.json({
      success: true,
      message: 'Invoice rejected successfully',
      data: invoice
    });
  } catch (error) {
    console.error('Error rejecting invoice:', error);
    if (error.message === 'Invoice not found') {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to reject invoice',
      error: error.message
    });
  }
};

// Submit invoice for approval
const submitForApproval = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    if (!id || isNaN(parseInt(id))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invoice ID'
      });
    }

    const invoice = await Invoice.update(parseInt(id), { 
      status: 'pending' 
    }, userId);

    res.json({
      success: true,
      message: 'Invoice submitted for approval successfully',
      data: invoice
    });
  } catch (error) {
    console.error('Error submitting invoice for approval:', error);
    if (error.message === 'Invoice not found') {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to submit invoice for approval',
      error: error.message
    });
  }
};

// Generate invoice from RFQ quote
const generateInvoiceFromRFQQuote = async (req, res) => {
  try {
    const { quoteId } = req.params;
    const userId = req.user.id;

    if (!quoteId || isNaN(parseInt(quoteId))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid quote ID'
      });
    }

    // Check if user has permission to create invoices
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to create invoices'
      });
    }

    const additionalData = req.body || {};
    const invoices = await Invoice.generateFromRFQQuote(parseInt(quoteId), userId, additionalData);

    res.status(201).json({
      success: true,
      message: 'Invoices generated from RFQ quote successfully',
      data: invoices
    });
  } catch (error) {
    console.error('Error generating invoice from RFQ quote:', error);
    if (error.message === 'Approved quote not found') {
      return res.status(404).json({
        success: false,
        message: 'Approved quote not found'
      });
    }
    if (error.message.includes('already exists')) {
      return res.status(409).json({
        success: false,
        message: error.message
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to generate invoice from RFQ quote',
      error: error.message
    });
  }
};

// Get invoices linked to RFQ
const getInvoicesByRFQ = async (req, res) => {
  try {
    const { rfqId } = req.params;
    const userId = req.user.id;

    if (!rfqId || isNaN(parseInt(rfqId))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid RFQ ID'
      });
    }

    const invoices = await Invoice.findByRFQ(parseInt(rfqId), userId);

    res.json({
      success: true,
      data: invoices
    });
  } catch (error) {
    console.error('Error fetching invoices by RFQ:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch invoices by RFQ',
      error: error.message
    });
  }
};

// Get RFQ invoice status tracking
const getRFQInvoiceStatus = async (req, res) => {
  try {
    const { rfqId } = req.params;
    const userId = req.user.id;

    if (!rfqId || isNaN(parseInt(rfqId))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid RFQ ID'
      });
    }

    const status = await Invoice.getRFQInvoiceStatus(parseInt(rfqId), userId);

    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('Error fetching RFQ invoice status:', error);
    if (error.message === 'RFQ not found or access denied') {
      return res.status(404).json({
        success: false,
        message: 'RFQ not found or access denied'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to fetch RFQ invoice status',
      error: error.message
    });
  }
};

// Update invoice status with RFQ tracking
const updateInvoiceStatusWithRFQTracking = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, reason } = req.body;
    const userId = req.user.id;

    if (!id || isNaN(parseInt(id))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invoice ID'
      });
    }

    if (!status) {
      return res.status(400).json({
        success: false,
        message: 'Status is required'
      });
    }

    // Check if user has permission to update status
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to update invoice status'
      });
    }

    const invoice = await Invoice.updateStatusWithRFQTracking(
      parseInt(id), 
      status, 
      userId, 
      reason || ''
    );

    res.json({
      success: true,
      message: 'Invoice status updated successfully',
      data: invoice
    });
  } catch (error) {
    console.error('Error updating invoice status:', error);
    if (error.message === 'Invoice not found') {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to update invoice status',
      error: error.message
    });
  }
};

// Get RFQ-to-Invoice analytics
const getRFQInvoiceAnalytics = async (req, res) => {
  try {
    const { startDate, endDate, vendorId, rfqId } = req.query;

    const filters = {
      startDate,
      endDate,
      vendorId: vendorId ? parseInt(vendorId) : undefined,
      rfqId: rfqId ? parseInt(rfqId) : undefined
    };

    const analytics = await Invoice.getRFQInvoiceAnalytics(filters);

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching RFQ invoice analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch RFQ invoice analytics',
      error: error.message
    });
  }
};

module.exports = {
  getInvoices,
  getInvoiceById,
  createInvoice,
  updateInvoice,
  approveInvoice,
  deleteInvoice,
  getInvoiceStatistics,
  generateInvoiceFromContract,
  rejectInvoice,
  submitForApproval,
  generateInvoiceFromRFQQuote,
  getInvoicesByRFQ,
  getRFQInvoiceStatus,
  updateInvoiceStatusWithRFQTracking,
  getRFQInvoiceAnalytics
};