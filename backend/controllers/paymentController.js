const Payment = require('../models/Payment');
const Joi = require('joi');

// Validation schemas
const createPaymentSchema = Joi.object({
  invoiceId: Joi.number().integer().required(),
  amount: Joi.number().positive().required(),
  currency: Joi.string().valid('USD', 'EUR', 'GBP', 'JPY', 'CNY').default('USD'),
  gateway: Joi.string().valid('stripe', 'paypal').required(),
  transactionId: Joi.string().required(),
  status: Joi.string().valid('pending', 'completed', 'failed', 'refunded').default('pending'),
  partialAmounts: Joi.array().items(
    Joi.object({
      date: Joi.date().required(),
      amount: Joi.number().positive().required()
    })
  ).optional(),
  conversionRate: Joi.number().positive().optional()
});

const updatePaymentStatusSchema = Joi.object({
  status: Joi.string().valid('pending', 'completed', 'failed', 'refunded').required(),
  conversionRate: Joi.number().positive().optional(),
  partialAmounts: Joi.array().items(
    Joi.object({
      date: Joi.date().required(),
      amount: Joi.number().positive().required()
    })
  ).optional()
});

const processRefundSchema = Joi.object({
  refundAmount: Joi.number().positive().required(),
  reason: Joi.string().required()
});

// Get all payments with filtering and pagination
const getPayments = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      gateway,
      invoiceId,
      startDate,
      endDate,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    // Validate pagination parameters
    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(100, Math.max(1, parseInt(limit)));

    const filters = {
      page: pageNum,
      limit: limitNum,
      search,
      status,
      gateway,
      invoiceId: invoiceId ? parseInt(invoiceId) : undefined,
      startDate,
      endDate,
      sortBy,
      sortOrder
    };

    const result = await Payment.findAll(filters);

    res.json({
      success: true,
      data: result.payments,
      pagination: result.pagination
    });
  } catch (error) {
    console.error('Error fetching payments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payments',
      error: error.message
    });
  }
};

// Get payments for a specific invoice
const getPaymentsByInvoiceId = async (req, res) => {
  try {
    const { invoiceId } = req.params;

    if (!invoiceId || isNaN(parseInt(invoiceId))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invoice ID'
      });
    }

    const payments = await Payment.findByInvoiceId(parseInt(invoiceId));

    res.json({
      success: true,
      data: payments
    });
  } catch (error) {
    console.error('Error fetching payments for invoice:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payments for invoice',
      error: error.message
    });
  }
};

// Get payment by transaction ID
const getPaymentByTransactionId = async (req, res) => {
  try {
    const { transactionId } = req.params;

    if (!transactionId) {
      return res.status(400).json({
        success: false,
        message: 'Transaction ID is required'
      });
    }

    const payment = await Payment.findByTransactionId(transactionId);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    res.json({
      success: true,
      data: payment
    });
  } catch (error) {
    console.error('Error fetching payment by transaction ID:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payment',
      error: error.message
    });
  }
};

// Create new payment record
const createPayment = async (req, res) => {
  try {
    const { error, value } = createPaymentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details
      });
    }

    const userId = req.user.id;
    const payment = await Payment.create(value, userId);

    res.status(201).json({
      success: true,
      message: 'Payment record created successfully',
      data: payment
    });
  } catch (error) {
    console.error('Error creating payment:', error);
    if (error.message === 'Invoice not found') {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }
    if (error.message.includes('exceeds remaining')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to create payment record',
      error: error.message
    });
  }
};

// Update payment status (typically called by webhooks)
const updatePaymentStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id; // May be null for webhook calls

    if (!id || isNaN(parseInt(id))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid payment ID'
      });
    }

    const { error, value } = updatePaymentStatusSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details
      });
    }

    const payment = await Payment.updateStatus(parseInt(id), value.status, {
      conversionRate: value.conversionRate,
      partialAmounts: value.partialAmounts
    }, userId);

    res.json({
      success: true,
      message: 'Payment status updated successfully',
      data: payment
    });
  } catch (error) {
    console.error('Error updating payment status:', error);
    if (error.message === 'Payment not found') {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to update payment status',
      error: error.message
    });
  }
};

// Process refund
const processRefund = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    if (!id || isNaN(parseInt(id))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid payment ID'
      });
    }

    // Check if user has permission to process refunds
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to process refunds'
      });
    }

    const { error, value } = processRefundSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details
      });
    }

    const refund = await Payment.processRefund(
      parseInt(id), 
      value.refundAmount, 
      value.reason, 
      userId
    );

    res.json({
      success: true,
      message: 'Refund processed successfully',
      data: refund
    });
  } catch (error) {
    console.error('Error processing refund:', error);
    if (error.message === 'Payment not found') {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }
    if (error.message.includes('Can only refund') || error.message.includes('cannot exceed')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to process refund',
      error: error.message
    });
  }
};

// Get payment statistics
const getPaymentStatistics = async (req, res) => {
  try {
    const { startDate, endDate, gateway, status } = req.query;

    const filters = {
      startDate,
      endDate,
      gateway,
      status
    };

    const statistics = await Payment.getStatistics(filters);

    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('Error fetching payment statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payment statistics',
      error: error.message
    });
  }
};

// Webhook handler for Stripe
const handleStripeWebhook = async (req, res) => {
  try {
    const { type, data } = req.body;
    
    // Basic webhook handling - in production, verify webhook signature
    switch (type) {
      case 'payment_intent.succeeded':
        const paymentIntent = data.object;
        const payment = await Payment.findByTransactionId(paymentIntent.id);
        
        if (payment) {
          await Payment.updateStatus(payment.id, 'completed', {
            conversionRate: paymentIntent.amount_received / paymentIntent.amount
          });
        }
        break;
        
      case 'payment_intent.payment_failed':
        const failedPayment = data.object;
        const failedPaymentRecord = await Payment.findByTransactionId(failedPayment.id);
        
        if (failedPaymentRecord) {
          await Payment.updateStatus(failedPaymentRecord.id, 'failed');
        }
        break;
        
      default:
        console.log(`Unhandled Stripe webhook event type: ${type}`);
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Error handling Stripe webhook:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process webhook',
      error: error.message
    });
  }
};

// Webhook handler for PayPal
const handlePayPalWebhook = async (req, res) => {
  try {
    const { event_type, resource } = req.body;
    
    // Basic webhook handling - in production, verify webhook signature
    switch (event_type) {
      case 'PAYMENT.CAPTURE.COMPLETED':
        const payment = await Payment.findByTransactionId(resource.id);
        
        if (payment) {
          await Payment.updateStatus(payment.id, 'completed');
        }
        break;
        
      case 'PAYMENT.CAPTURE.DENIED':
        const deniedPayment = await Payment.findByTransactionId(resource.id);
        
        if (deniedPayment) {
          await Payment.updateStatus(deniedPayment.id, 'failed');
        }
        break;
        
      default:
        console.log(`Unhandled PayPal webhook event type: ${event_type}`);
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Error handling PayPal webhook:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process webhook',
      error: error.message
    });
  }
};

module.exports = {
  getPayments,
  getPaymentsByInvoiceId,
  getPaymentByTransactionId,
  createPayment,
  updatePaymentStatus,
  processRefund,
  getPaymentStatistics,
  handleStripeWebhook,
  handlePayPalWebhook
};