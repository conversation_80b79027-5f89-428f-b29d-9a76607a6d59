const express = require('express');
const router = express.Router();
const AccountController = require('../controllers/accountController');
const AccountHierarchyService = require('../services/accountHierarchyService');
const { 
  validateCreateAccount, 
  validateUpdateAccount, 
  validateSearchFilters 
} = require('../validation/accountValidation');

// Middleware for authentication (assuming it exists)
// const { authenticateToken, requireRole } = require('../middleware/auth');

// Apply authentication middleware to all routes
// router.use(authenticateToken);

/**
 * @swagger
 * components:
 *   schemas:
 *     Account:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: integer
 *           description: Unique account identifier
 *         name:
 *           type: string
 *           maxLength: 255
 *           description: Account name (required)
 *         account_number:
 *           type: string
 *           maxLength: 100
 *           description: Optional account number
 *         type:
 *           type: string
 *           enum: [Prospect, Customer - Direct, Customer - Channel, Channel Partner / Reseller, Installation Partner, Technology Partner, Other]
 *           description: Account type
 *         industry:
 *           type: string
 *           description: Industry classification
 *         annual_revenue:
 *           type: number
 *           minimum: 0
 *           description: Annual revenue
 *         number_of_employees:
 *           type: integer
 *           minimum: 0
 *           description: Number of employees
 *         ownership:
 *           type: string
 *           enum: [Public, Private, Subsidiary, Other]
 *           description: Ownership type
 *         phone:
 *           type: string
 *           maxLength: 50
 *           description: Phone number
 *         fax:
 *           type: string
 *           maxLength: 50
 *           description: Fax number
 *         website:
 *           type: string
 *           maxLength: 255
 *           description: Website URL
 *         ticker_symbol:
 *           type: string
 *           maxLength: 10
 *           description: Stock ticker symbol
 *         site:
 *           type: string
 *           maxLength: 255
 *           description: Site identifier
 *         rating:
 *           type: string
 *           enum: [Hot, Warm, Cold]
 *           description: Account rating
 *         description:
 *           type: string
 *           maxLength: 5000
 *           description: Account description
 *         billing_address:
 *           $ref: '#/components/schemas/Address'
 *         shipping_address:
 *           $ref: '#/components/schemas/Address'
 *         parent_account_id:
 *           type: integer
 *           description: Parent account ID for hierarchy
 *         owner_id:
 *           type: integer
 *           description: Account owner user ID
 *         integration_id:
 *           type: string
 *           maxLength: 100
 *           description: External system integration ID
 *         custom_fields:
 *           type: object
 *           description: Custom fields in JSON format
 *         status:
 *           type: string
 *           enum: [ACTIVE, INACTIVE]
 *           description: Account status
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *     
 *     Address:
 *       type: object
 *       properties:
 *         street:
 *           type: string
 *           maxLength: 255
 *           description: Street address
 *         city:
 *           type: string
 *           maxLength: 100
 *           description: City
 *         state:
 *           type: string
 *           maxLength: 100
 *           description: State or province
 *         postalCode:
 *           type: string
 *           maxLength: 20
 *           description: Postal or ZIP code
 *         country:
 *           type: string
 *           maxLength: 100
 *           description: Country
 *         latitude:
 *           type: number
 *           minimum: -90
 *           maximum: 90
 *           description: Latitude coordinate
 *         longitude:
 *           type: number
 *           minimum: -180
 *           maximum: 180
 *           description: Longitude coordinate
 *     
 *     AccountResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         data:
 *           $ref: '#/components/schemas/Account'
 *         message:
 *           type: string
 *     
 *     AccountListResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         data:
 *           type: object
 *           properties:
 *             accounts:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Account'
 *             pagination:
 *               type: object
 *               properties:
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 total:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *         message:
 *           type: string
 */

/**
 * @swagger
 * /api/accounts:
 *   get:
 *     summary: Get all accounts with filtering and pagination
 *     tags: [Accounts]
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for account name or description
 *       - in: query
 *         name: industry
 *         schema:
 *           type: string
 *         description: Filter by industry
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by account type
 *       - in: query
 *         name: rating
 *         schema:
 *           type: string
 *         description: Filter by rating
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Accounts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AccountListResponse'
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Internal server error
 */
router.get('/', validateSearchFilters, AccountController.getAllAccounts);

/**
 * @swagger
 * /api/accounts/search:
 *   get:
 *     summary: Advanced search for accounts
 *     tags: [Accounts]
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: annual_revenue_min
 *         schema:
 *           type: number
 *         description: Minimum annual revenue
 *       - in: query
 *         name: annual_revenue_max
 *         schema:
 *           type: number
 *         description: Maximum annual revenue
 *       - in: query
 *         name: country
 *         schema:
 *           type: string
 *         description: Filter by country
 *       - in: query
 *         name: has_children
 *         schema:
 *           type: boolean
 *         description: Filter accounts with/without children
 *     responses:
 *       200:
 *         description: Search results retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AccountListResponse'
 */
router.get('/search', validateSearchFilters, AccountController.searchAccounts);

/**
 * @swagger
 * /api/accounts/export:
 *   get:
 *     summary: Export accounts data
 *     tags: [Accounts]
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, json]
 *           default: csv
 *         description: Export format
 *     responses:
 *       200:
 *         description: Export file generated successfully
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *           application/json:
 *             schema:
 *               type: object
 */
router.get('/export', validateSearchFilters, AccountController.exportAccounts);

/**
 * @swagger
 * /api/accounts/statistics:
 *   get:
 *     summary: Get account statistics
 *     tags: [Accounts]
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     total_accounts:
 *                       type: integer
 *                     active_accounts:
 *                       type: integer
 *                     accounts_by_industry:
 *                       type: object
 *                     average_annual_revenue:
 *                       type: number
 */
router.get('/statistics', AccountController.getAccountStatistics);

/**
 * @swagger
 * /api/accounts/types:
 *   get:
 *     summary: Get available account types
 *     tags: [Accounts]
 *     responses:
 *       200:
 *         description: Account types retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: string
 */
router.get('/types', AccountController.getAccountTypes);

/**
 * @swagger
 * /api/accounts/industries:
 *   get:
 *     summary: Get available account industries
 *     tags: [Accounts]
 *     responses:
 *       200:
 *         description: Account industries retrieved successfully
 */
router.get('/industries', AccountController.getAccountIndustries);

/**
 * @swagger
 * /api/accounts/roots:
 *   get:
 *     summary: Get all root accounts (accounts with no parent)
 *     tags: [Accounts]
 *     responses:
 *       200:
 *         description: Root accounts retrieved successfully
 */
router.get('/roots', AccountController.getRootAccounts);

/**
 * @swagger
 * /api/accounts/hierarchy-summary:
 *   get:
 *     summary: Get hierarchy summary for all accounts
 *     tags: [Accounts]
 *     responses:
 *       200:
 *         description: Hierarchy summary retrieved successfully
 */
router.get('/hierarchy-summary', AccountController.getHierarchySummary);

/**
 * @swagger
 * /api/accounts/{id}:
 *   get:
 *     summary: Get account by ID
 *     tags: [Accounts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *     responses:
 *       200:
 *         description: Account retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AccountResponse'
 *       404:
 *         description: Account not found
 *       500:
 *         description: Internal server error
 */
router.get('/:id', AccountController.getAccountById);

/**
 * @swagger
 * /api/accounts/{id}/hierarchy:
 *   get:
 *     summary: Get account hierarchy (all descendants)
 *     tags: [Accounts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *     responses:
 *       200:
 *         description: Account hierarchy retrieved successfully
 */
router.get('/:id/hierarchy', AccountController.getAccountHierarchy);

/**
 * @swagger
 * /api/accounts/{id}/children:
 *   get:
 *     summary: Get direct child accounts
 *     tags: [Accounts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *     responses:
 *       200:
 *         description: Child accounts retrieved successfully
 */
router.get('/:id/children', AccountController.getAccountChildren);

/**
 * @swagger
 * /api/accounts/{id}/ancestors:
 *   get:
 *     summary: Get account ancestors (parent chain)
 *     tags: [Accounts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *     responses:
 *       200:
 *         description: Account ancestors retrieved successfully
 */
router.get('/:id/ancestors', AccountController.getAccountAncestors);

/**
 * @swagger
 * /api/accounts/{id}/audit:
 *   get:
 *     summary: Get account audit history
 *     tags: [Accounts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *     responses:
 *       200:
 *         description: Audit history retrieved successfully
 */
router.get('/:id/audit', AccountController.getAccountAuditHistory);

/**
 * @swagger
 * /api/accounts/{id}/tree:
 *   get:
 *     summary: Get account hierarchy tree
 *     tags: [Accounts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *     responses:
 *       200:
 *         description: Hierarchy tree retrieved successfully
 */
router.get('/:id/tree', AccountController.getHierarchyTree);

/**
 * @swagger
 * /api/accounts/{id}/path:
 *   get:
 *     summary: Get account hierarchy path (breadcrumb)
 *     tags: [Accounts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *     responses:
 *       200:
 *         description: Hierarchy path retrieved successfully
 */
router.get('/:id/path', AccountController.getHierarchyPath);

/**
 * @swagger
 * /api/accounts/{id}/hierarchy-stats:
 *   get:
 *     summary: Get account hierarchy statistics
 *     tags: [Accounts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *     responses:
 *       200:
 *         description: Hierarchy statistics retrieved successfully
 */
router.get('/:id/hierarchy-stats', AccountController.getHierarchyStatistics);

/**
 * @swagger
 * /api/accounts:
 *   post:
 *     summary: Create a new account
 *     tags: [Accounts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 255
 *               account_number:
 *                 type: string
 *                 maxLength: 100
 *               type:
 *                 type: string
 *               industry:
 *                 type: string
 *               annual_revenue:
 *                 type: number
 *                 minimum: 0
 *               number_of_employees:
 *                 type: integer
 *                 minimum: 0
 *               ownership:
 *                 type: string
 *               phone:
 *                 type: string
 *               website:
 *                 type: string
 *               rating:
 *                 type: string
 *               description:
 *                 type: string
 *               billing_address:
 *                 $ref: '#/components/schemas/Address'
 *               shipping_address:
 *                 $ref: '#/components/schemas/Address'
 *               parent_account_id:
 *                 type: integer
 *               custom_fields:
 *                 type: object
 *     responses:
 *       201:
 *         description: Account created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AccountResponse'
 *       400:
 *         description: Validation error or duplicate account name
 *       500:
 *         description: Internal server error
 */
router.post('/', validateCreateAccount, AccountController.createAccount);

/**
 * @swagger
 * /api/accounts/{id}:
 *   put:
 *     summary: Update an account
 *     tags: [Accounts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 255
 *               account_number:
 *                 type: string
 *                 maxLength: 100
 *               type:
 *                 type: string
 *               industry:
 *                 type: string
 *               annual_revenue:
 *                 type: number
 *                 minimum: 0
 *               number_of_employees:
 *                 type: integer
 *                 minimum: 0
 *               ownership:
 *                 type: string
 *               phone:
 *                 type: string
 *               website:
 *                 type: string
 *               rating:
 *                 type: string
 *               description:
 *                 type: string
 *               billing_address:
 *                 $ref: '#/components/schemas/Address'
 *               shipping_address:
 *                 $ref: '#/components/schemas/Address'
 *               parent_account_id:
 *                 type: integer
 *               status:
 *                 type: string
 *                 enum: [ACTIVE, INACTIVE]
 *               custom_fields:
 *                 type: object
 *     responses:
 *       200:
 *         description: Account updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AccountResponse'
 *       400:
 *         description: Validation error or circular hierarchy
 *       404:
 *         description: Account not found
 *       500:
 *         description: Internal server error
 */
router.put('/:id', validateUpdateAccount, AccountController.updateAccount);

/**
 * @swagger
 * /api/accounts/{id}:
 *   delete:
 *     summary: Delete an account (soft delete)
 *     tags: [Accounts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *     responses:
 *       200:
 *         description: Account deleted successfully
 *       400:
 *         description: Cannot delete account with child accounts
 *       404:
 *         description: Account not found
 *       500:
 *         description: Internal server error
 */
router.delete('/:id', AccountController.deleteAccount);

/**
 * @swagger
 * /api/accounts/{id}/move:
 *   put:
 *     summary: Move account to a new parent in hierarchy
 *     tags: [Accounts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID to move
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               parent_account_id:
 *                 type: integer
 *                 nullable: true
 *                 description: New parent account ID (null for root level)
 *     responses:
 *       200:
 *         description: Account moved successfully
 *       400:
 *         description: Invalid move or circular reference
 *       404:
 *         description: Account not found
 *       500:
 *         description: Internal server error
 */
router.put('/:id/move', AccountController.moveAccount);

module.exports = router;