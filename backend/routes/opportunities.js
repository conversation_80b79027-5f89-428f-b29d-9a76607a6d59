const express = require('express');
const router = express.Router();
const OpportunityController = require('../controllers/opportunityController');

// Middleware for authentication (assuming it exists)
// const { authenticateToken, requireRole } = require('../middleware/auth');

// Apply authentication middleware to all routes
// router.use(authenticateToken);

/**
 * @swagger
 * components:
 *   schemas:
 *     Opportunity:
 *       type: object
 *       required:
 *         - name
 *         - account_id
 *         - stage_name
 *         - close_date
 *       properties:
 *         id:
 *           type: integer
 *           description: Unique opportunity identifier
 *         name:
 *           type: string
 *           maxLength: 255
 *           description: Opportunity name (required)
 *         account_id:
 *           type: integer
 *           description: Associated account ID (required)
 *         amount:
 *           type: number
 *           format: decimal
 *           description: Opportunity amount
 *         close_date:
 *           type: string
 *           format: date
 *           description: Expected close date (required)
 *         stage_name:
 *           type: string
 *           enum: [Prospecting, Qualification, Needs Analysis, Value Proposition, Id. Decision Makers, Perception Analysis, Proposal/Price Quote, Negotiation/Review, Closed Won, Closed Lost]
 *           description: Current stage (required)
 *         probability:
 *           type: integer
 *           minimum: 0
 *           maximum: 100
 *           description: Win probability percentage
 *         type:
 *           type: string
 *           enum: [Existing Customer - Upgrade, Existing Customer - Replacement, Existing Customer - Downgrade, New Customer]
 *           description: Opportunity type
 *         lead_source:
 *           type: string
 *           enum: [Web, Phone Inquiry, Partner Referral, Purchased List, Other]
 *           description: Lead source
 *         is_private:
 *           type: boolean
 *           description: Whether opportunity is private
 *         description:
 *           type: string
 *           description: Opportunity description
 *         forecast_category:
 *           type: string
 *           enum: [Pipeline, Best Case, Commit, Closed]
 *           description: Forecast category
 *         next_step:
 *           type: string
 *           description: Next step in the sales process
 *         is_closed:
 *           type: boolean
 *           description: Whether opportunity is closed
 *         is_won:
 *           type: boolean
 *           description: Whether opportunity is won
 *         owner_id:
 *           type: integer
 *           description: Opportunity owner user ID
 *         campaign_id:
 *           type: integer
 *           description: Associated campaign ID
 *         has_opportunity_line_item:
 *           type: boolean
 *           description: Whether opportunity has line items
 *         items:
 *           type: object
 *           description: Opportunity line items (JSONB)
 *         custom_fields:
 *           type: object
 *           description: Custom fields (JSONB)
 *         integration_id:
 *           type: string
 *           description: External system integration ID
 *         status:
 *           type: string
 *           enum: [active, inactive, archived]
 *           description: Opportunity status
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *         created_by:
 *           type: integer
 *           description: User who created the opportunity
 *         updated_by:
 *           type: integer
 *           description: User who last updated the opportunity
 */

/**
 * @swagger
 * /api/opportunities:
 *   get:
 *     summary: Get all opportunities with filtering and pagination
 *     tags: [Opportunities]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of opportunities per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for opportunity name or description
 *       - in: query
 *         name: account_id
 *         schema:
 *           type: integer
 *         description: Filter by account ID
 *       - in: query
 *         name: stage_name
 *         schema:
 *           type: string
 *         description: Filter by stage
 *       - in: query
 *         name: owner_id
 *         schema:
 *           type: integer
 *         description: Filter by owner
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by opportunity type
 *       - in: query
 *         name: lead_source
 *         schema:
 *           type: string
 *         description: Filter by lead source
 *       - in: query
 *         name: forecast_category
 *         schema:
 *           type: string
 *         description: Filter by forecast category
 *       - in: query
 *         name: is_closed
 *         schema:
 *           type: boolean
 *         description: Filter by closed status
 *       - in: query
 *         name: is_won
 *         schema:
 *           type: boolean
 *         description: Filter by won status
 *       - in: query
 *         name: min_amount
 *         schema:
 *           type: number
 *         description: Minimum opportunity amount
 *       - in: query
 *         name: max_amount
 *         schema:
 *           type: number
 *         description: Maximum opportunity amount
 *       - in: query
 *         name: close_date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter opportunities closing from this date
 *       - in: query
 *         name: close_date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter opportunities closing until this date
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *           default: created_at
 *         description: Sort field
 *       - in: query
 *         name: sort_order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *     responses:
 *       200:
 *         description: List of opportunities
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 opportunities:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Opportunity'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     pages:
 *                       type: integer
 */
router.get('/', OpportunityController.getAllOpportunities);

/**
 * @swagger
 * /api/opportunities:
 *   post:
 *     summary: Create a new opportunity
 *     tags: [Opportunities]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Opportunity'
 *     responses:
 *       201:
 *         description: Opportunity created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Opportunity'
 *       400:
 *         description: Validation error
 *       404:
 *         description: Account not found
 */
router.post('/', OpportunityController.createOpportunity);

/**
 * @swagger
 * /api/opportunities/bulk:
 *   post:
 *     summary: Create multiple opportunities
 *     tags: [Opportunities]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               opportunities:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/Opportunity'
 *     responses:
 *       201:
 *         description: Opportunities created successfully
 *       400:
 *         description: Validation error
 */
router.post('/bulk', OpportunityController.bulkCreateOpportunities);

/**
 * @swagger
 * /api/opportunities/bulk:
 *   put:
 *     summary: Update multiple opportunities
 *     tags: [Opportunities]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               updates:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     updateData:
 *                       $ref: '#/components/schemas/Opportunity'
 *     responses:
 *       200:
 *         description: Opportunities updated successfully
 *       400:
 *         description: Validation error
 */
router.put('/bulk', OpportunityController.bulkUpdateOpportunities);

/**
 * @swagger
 * /api/opportunities/pipeline:
 *   get:
 *     summary: Get pipeline data for kanban view
 *     tags: [Opportunities]
 *     parameters:
 *       - in: query
 *         name: owner_id
 *         schema:
 *           type: integer
 *         description: Filter by owner
 *       - in: query
 *         name: account_id
 *         schema:
 *           type: integer
 *         description: Filter by account
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by opportunity type
 *     responses:
 *       200:
 *         description: Pipeline data grouped by stage
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   stage_name:
 *                     type: string
 *                   opportunity_count:
 *                     type: integer
 *                   total_amount:
 *                     type: number
 *                   weighted_amount:
 *                     type: number
 *                   opportunities:
 *                     type: array
 *                     items:
 *                       $ref: '#/components/schemas/Opportunity'
 */
router.get('/pipeline', OpportunityController.getPipelineData);

/**
 * @swagger
 * /api/opportunities/analytics:
 *   get:
 *     summary: Get opportunity analytics
 *     tags: [Opportunities]
 *     parameters:
 *       - in: query
 *         name: owner_id
 *         schema:
 *           type: integer
 *         description: Filter by owner
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Analytics from date
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: Analytics to date
 *     responses:
 *       200:
 *         description: Analytics data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total_opportunities:
 *                   type: integer
 *                 total_amount:
 *                   type: number
 *                 weighted_amount:
 *                   type: number
 *                 won_opportunities:
 *                   type: integer
 *                 won_amount:
 *                   type: number
 *                 lost_opportunities:
 *                   type: integer
 *                 lost_amount:
 *                   type: number
 *                 win_rate:
 *                   type: number
 *                 average_deal_size:
 *                   type: number
 *                 average_sales_cycle:
 *                   type: number
 */
router.get('/analytics', OpportunityController.getAnalytics);

/**
 * @swagger
 * /api/opportunities/statistics:
 *   get:
 *     summary: Get opportunity statistics
 *     tags: [Opportunities]
 *     responses:
 *       200:
 *         description: Opportunity statistics
 */
router.get('/statistics', OpportunityController.getOpportunityStatistics);

/**
 * @swagger
 * /api/opportunities/export:
 *   get:
 *     summary: Export opportunities to CSV
 *     tags: [Opportunities]
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, xlsx]
 *           default: csv
 *         description: Export format
 *     responses:
 *       200:
 *         description: CSV file
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 */
router.get('/export', OpportunityController.exportOpportunities);

/**
 * @swagger
 * /api/opportunities/stages:
 *   get:
 *     summary: Get available opportunity stages
 *     tags: [Opportunities]
 *     responses:
 *       200:
 *         description: List of available stages
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                   probability:
 *                     type: integer
 *                   is_closed:
 *                     type: boolean
 *                   is_won:
 *                     type: boolean
 */
router.get('/stages', OpportunityController.getStages);

/**
 * @swagger
 * /api/opportunities/types:
 *   get:
 *     summary: Get available opportunity types
 *     tags: [Opportunities]
 *     responses:
 *       200:
 *         description: List of available types
 */
router.get('/types', OpportunityController.getTypes);

/**
 * @swagger
 * /api/opportunities/lead-sources:
 *   get:
 *     summary: Get available lead sources
 *     tags: [Opportunities]
 *     responses:
 *       200:
 *         description: List of available lead sources
 */
router.get('/lead-sources', OpportunityController.getLeadSources);

/**
 * @swagger
 * /api/opportunities/forecast-categories:
 *   get:
 *     summary: Get available forecast categories
 *     tags: [Opportunities]
 *     responses:
 *       200:
 *         description: List of available forecast categories
 */
router.get('/forecast-categories', OpportunityController.getForecastCategories);

/**
 * @swagger
 * /api/opportunities/{id}:
 *   get:
 *     summary: Get opportunity by ID
 *     tags: [Opportunities]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Opportunity ID
 *     responses:
 *       200:
 *         description: Opportunity details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Opportunity'
 *       404:
 *         description: Opportunity not found
 */
router.get('/:id', OpportunityController.getOpportunityById);

/**
 * @swagger
 * /api/opportunities/{id}:
 *   put:
 *     summary: Update opportunity
 *     tags: [Opportunities]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Opportunity ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Opportunity'
 *     responses:
 *       200:
 *         description: Opportunity updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Opportunity'
 *       400:
 *         description: Validation error
 *       404:
 *         description: Opportunity not found
 */
router.put('/:id', OpportunityController.updateOpportunity);

/**
 * @swagger
 * /api/opportunities/{id}:
 *   delete:
 *     summary: Delete opportunity (soft delete)
 *     tags: [Opportunities]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Opportunity ID
 *     responses:
 *       200:
 *         description: Opportunity deleted successfully
 *       404:
 *         description: Opportunity not found
 */
router.delete('/:id', OpportunityController.deleteOpportunity);

/**
 * @swagger
 * /api/opportunities/{id}/advance-stage:
 *   put:
 *     summary: Advance opportunity to next stage
 *     tags: [Opportunities]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Opportunity ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               stage_name:
 *                 type: string
 *                 description: New stage name
 *     responses:
 *       200:
 *         description: Stage advanced successfully
 *       400:
 *         description: Invalid stage transition
 *       404:
 *         description: Opportunity not found
 */
router.put('/:id/advance-stage', OpportunityController.advanceStage);

/**
 * @swagger
 * /api/opportunities/account/{accountId}:
 *   get:
 *     summary: Get opportunities by account
 *     tags: [Opportunities]
 *     parameters:
 *       - in: path
 *         name: accountId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *       - in: query
 *         name: include_closed
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include closed opportunities
 *     responses:
 *       200:
 *         description: Account opportunities
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Opportunity'
 */
router.get('/account/:accountId', OpportunityController.getAccountOpportunities);

/**
 * @swagger
 * /api/opportunities/owner/{ownerId}:
 *   get:
 *     summary: Get opportunities by owner
 *     tags: [Opportunities]
 *     parameters:
 *       - in: path
 *         name: ownerId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Owner ID
 *       - in: query
 *         name: include_closed
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include closed opportunities
 *     responses:
 *       200:
 *         description: Owner opportunities
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Opportunity'
 */
router.get('/owner/:ownerId', OpportunityController.getOwnerOpportunities);

module.exports = router;