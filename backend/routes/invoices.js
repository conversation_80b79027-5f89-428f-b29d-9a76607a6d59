const express = require('express');
const router = express.Router();
const {
  getInvoices,
  getInvoiceById,
  createInvoice,
  updateInvoice,
  approveInvoice,
  deleteInvoice,
  getInvoiceStatistics,
  generateInvoiceFromContract,
  rejectInvoice,
  submitForApproval,
  generateInvoiceFromRFQQuote,
  getInvoicesByRFQ,
  getRFQInvoiceStatus,
  updateInvoiceStatusWithRFQTracking,
  getRFQInvoiceAnalytics
} = require('../controllers/invoiceController');
const { authenticateToken } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Invoice CRUD routes
router.get('/', getInvoices);
router.get('/statistics', getInvoiceStatistics);
router.get('/:id', getInvoiceById);
router.post('/', createInvoice);
router.put('/:id', updateInvoice);
router.delete('/:id', deleteInvoice);

// Invoice workflow routes
router.post('/:id/approve', approveInvoice);
router.post('/:id/reject', rejectInvoice);
router.post('/:id/submit', submitForApproval);

// Contract integration routes
router.post('/generate/contract/:contractId', generateInvoiceFromContract);

// RFQ integration routes
router.post('/generate/rfq-quote/:quoteId', generateInvoiceFromRFQQuote);
router.get('/rfq/:rfqId', getInvoicesByRFQ);
router.get('/rfq/:rfqId/status', getRFQInvoiceStatus);
router.put('/:id/status-rfq', updateInvoiceStatusWithRFQTracking);
router.get('/analytics/rfq', getRFQInvoiceAnalytics);

module.exports = router;