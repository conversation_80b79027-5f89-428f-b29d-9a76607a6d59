const express = require('express');
const { body, param, query } = require('express-validator');
const CommentsController = require('../controllers/commentsController');
const { authenticateToken } = require('../middleware/auth');
const router = express.Router();

// Validation middleware
const validateCreateComment = [
  body('object_type')
    .isIn(['vendor', 'contract', 'document', 'user', 'project'])
    .withMessage('Invalid object type'),
  body('object_id')
    .isInt({ min: 1 })
    .withMessage('Object ID must be a positive integer'),
  body('content')
    .isLength({ min: 1, max: 5000 })
    .withMessage('Content must be between 1 and 5000 characters'),
  body('content_type')
    .optional()
    .isIn(['text', 'html'])
    .withMessage('Content type must be text or html'),
  body('category')
    .optional()
    .isIn(['general', 'legal', 'financial', 'technical', 'performance', 'compliance'])
    .withMessage('Invalid category'),
  body('parent_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Parent ID must be a positive integer')
];

const validateUpdateComment = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Comment ID must be a positive integer'),
  body('content')
    .optional()
    .isLength({ min: 1, max: 5000 })
    .withMessage('Content must be between 1 and 5000 characters'),
  body('content_type')
    .optional()
    .isIn(['text', 'html'])
    .withMessage('Content type must be text or html'),
  body('category')
    .optional()
    .isIn(['general', 'legal', 'financial', 'technical', 'performance', 'compliance'])
    .withMessage('Invalid category'),
  body('status')
    .optional()
    .isIn(['active', 'resolved', 'deleted'])
    .withMessage('Invalid status')
];

const validateObjectParams = [
  param('objectType')
    .isIn(['vendor', 'contract', 'document', 'user', 'project'])
    .withMessage('Invalid object type'),
  param('objectId')
    .isInt({ min: 1 })
    .withMessage('Object ID must be a positive integer')
];

const validateCommentId = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Comment ID must be a positive integer')
];

const validateUserId = [
  param('userId')
    .isInt({ min: 1 })
    .withMessage('User ID must be a positive integer')
];

const validateSearchQuery = [
  query('q')
    .isLength({ min: 2, max: 100 })
    .withMessage('Search query must be between 2 and 100 characters'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Offset must be a non-negative integer')
];

// Apply authentication middleware to all routes
router.use(authenticateToken);

// GET /api/comments/:objectType/:objectId - Get comments for an object
router.get(
  '/:objectType/:objectId',
  validateObjectParams,
  CommentsController.getComments
);

// POST /api/comments - Create a new comment
router.post(
  '/',
  validateCreateComment,
  CommentsController.createComment
);

// GET /api/comments/comment/:id - Get a specific comment
router.get(
  '/comment/:id',
  validateCommentId,
  CommentsController.getComment
);

// PUT /api/comments/:id - Update a comment
router.put(
  '/:id',
  validateUpdateComment,
  CommentsController.updateComment
);

// DELETE /api/comments/:id - Delete a comment
router.delete(
  '/:id',
  validateCommentId,
  CommentsController.deleteComment
);

// POST /api/comments/:id/attachments - Upload attachments to a comment
router.post(
  '/:id/attachments',
  validateCommentId,
  CommentsController.uploadAttachments
);

// GET /api/comments/search - Search comments
router.get(
  '/search',
  validateSearchQuery,
  CommentsController.searchComments
);

// GET /api/comments/stats - Get comment statistics
router.get(
  '/stats',
  CommentsController.getStats
);

// GET /api/comments/user/:userId - Get comments by user
router.get(
  '/user/:userId',
  validateUserId,
  CommentsController.getUserComments
);

// POST /api/comments/typing/:objectType/:objectId - Handle typing indicators
router.post(
  '/typing/:objectType/:objectId',
  validateObjectParams,
  body('isTyping')
    .isBoolean()
    .withMessage('isTyping must be a boolean'),
  CommentsController.handleTyping
);

// Error handling middleware for this router
router.use((error, req, res, next) => {
  console.error('Comments route error:', error);
  
  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(400).json({
      success: false,
      message: 'File too large. Maximum size is 10MB.',
      error: 'FILE_TOO_LARGE'
    });
  }
  
  if (error.code === 'LIMIT_FILE_COUNT') {
    return res.status(400).json({
      success: false,
      message: 'Too many files. Maximum is 5 files per comment.',
      error: 'TOO_MANY_FILES'
    });
  }
  
  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.status(400).json({
      success: false,
      message: 'Unexpected file field.',
      error: 'UNEXPECTED_FILE'
    });
  }
  
  res.status(500).json({
    success: false,
    message: 'Internal server error in comments module',
    error: process.env.NODE_ENV === 'development' ? error.message : 'INTERNAL_ERROR'
  });
});

module.exports = router;