const express = require('express');
const router = express.Router();
const {
  getQuotes,
  getQuoteById,
  getQuoteByToken,
  createQuote,
  updateQuote,
  sendQuote,
  generateQuotePDF,
  generatePublicQuotePDF,
  approveQuote,
  rejectQuote,
  deleteQuote,
  getQuoteStatistics,
  validateQuoteToken,
  getQuoteAuditHistory,
  // Quote Generation
  getSubmissionsForQuote,
  generateQuoteFromSubmissions,
  updateQuoteSelections,
  // Quote Templates
  getQuoteTemplates,
  getQuoteTemplateById,
  createQuoteTemplate,
  updateQuoteTemplate,
  deleteQuoteTemplate,
  cloneQuoteTemplate,
  previewQuoteTemplate,
  // Quote Interactions
  getQuoteInteractions,
  createQuoteInteraction,
  getQuoteStats,
  getQuoteTimeline
} = require('../controllers/quoteController');

// Quote CRUD routes (authenticated)
router.get('/', getQuotes);
router.get('/statistics', getQuoteStatistics);
router.get('/:id', getQuoteById);
router.post('/', createQuote);
router.put('/:id', updateQuote);
router.delete('/:id', deleteQuote);

// Quote workflow routes (authenticated)
router.post('/:id/send', sendQuote);

// PDF generation (authenticated)
router.get('/:id/pdf', generateQuotePDF);

// Audit routes (authenticated)
router.get('/:id/audit', getQuoteAuditHistory);

// Quote Generation from RFQ Submissions
router.get('/rfq/:rfqId/submissions', getSubmissionsForQuote);
router.post('/rfq/:rfqId/generate', generateQuoteFromSubmissions);
router.put('/:id/selections', updateQuoteSelections);

// Quote Template routes (authenticated)
router.get('/templates', getQuoteTemplates);
router.get('/templates/:id', getQuoteTemplateById);
router.post('/templates', createQuoteTemplate);
router.put('/templates/:id', updateQuoteTemplate);
router.delete('/templates/:id', deleteQuoteTemplate);
router.post('/templates/:id/clone', cloneQuoteTemplate);
router.get('/templates/:id/preview', previewQuoteTemplate);

// Quote Interaction routes (authenticated)
router.get('/:id/interactions', getQuoteInteractions);
router.post('/:id/interactions', createQuoteInteraction);
router.get('/:id/stats', getQuoteStats);
router.get('/:id/timeline', getQuoteTimeline);

// Public routes (token-based access)
router.get('/public/validate/:token', validateQuoteToken);
router.get('/public/:token', getQuoteByToken);
router.get('/public/:token/pdf', generatePublicQuotePDF);
router.post('/public/:token/approve', approveQuote);
router.post('/public/:token/reject', rejectQuote);

module.exports = router;