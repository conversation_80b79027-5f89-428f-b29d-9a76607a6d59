const express = require('express');
const router = express.Router();
const {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  updateUserRole,
  setUserPassword
} = require('../controllers/userController');
const { authenticateToken, requireRole } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(authenticateToken);

// User management routes (admin only)
router.get('/', requireRole('admin'), getUsers);
router.get('/:id', requireRole('admin'), getUserById);
router.post('/', requireRole('admin'), createUser);
router.put('/:id', requireRole('admin'), updateUser);
router.delete('/:id', requireRole('admin'), deleteUser);
router.put('/:id/role', requireRole('admin'), updateUserRole);
router.put('/:id/password', requireRole('admin'), setUserPassword);

module.exports = router;