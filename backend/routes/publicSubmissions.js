const express = require('express');
const router = express.Router();
const {
  validateToken,
  getRFQForSubmission,
  getSubmissionByToken,
  createSubmission,
  updateSubmission,
  deleteSubmission,
  getSubmissionStatus,
  downloadAttachment,
  handleFileUpload,
  handleUploadError,
} = require('../controllers/publicSubmissionController');

// Token validation
router.get('/:token/validate', validateToken);

// RFQ details for submission (public access)
router.get('/:token', getRFQForSubmission);

// Submission status
router.get('/:token/status', getSubmissionStatus);

// Submission CRUD (public access via token)
router.get('/:token/submission', getSubmissionByToken);
router.post('/submission', handleFileUpload, handleUploadError, createSubmission);
router.put('/submission/:id', handleFileUpload, handleUploadError, updateSubmission);
router.delete('/submission/:id', deleteSubmission);

// File download
router.get('/:token/attachment/:filename', downloadAttachment);

module.exports = router;