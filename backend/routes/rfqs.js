const express = require('express');
const router = express.Router();
const {
  getRFQs,
  getRFQById,
  createRFQ,
  updateRFQ,
  deleteRFQ,
  sendRFQInvitations,
  getRFQInvitations,
  getRFQSubmissions,
  getRFQSubmissionsComparison,
  generateAIRecommendations,
  getRFQAnalytics,
  getRFQStatusStatistics,
  getRFQAuditHistory,
  recalculateRFQAnalytics,
  // New enhanced endpoints
  bulkUpdateRFQs,
  bulkDeleteRFQs,
  searchRFQs,
  getRFQSummaryStats,
  exportRFQs,
} = require('../controllers/rfqController');

// RFQ CRUD routes
router.get('/', getRFQs);
router.get('/statistics', getRFQStatusStatistics);
router.get('/summary', getRFQSummaryStats);
router.get('/export', exportRFQs);
router.post('/search', searchRFQs);
router.put('/bulk', bulkUpdateRFQs);
router.delete('/bulk', bulkDeleteRFQs);
router.get('/:id', getRFQById);
router.post('/', createRFQ);
router.put('/:id', updateRFQ);
router.delete('/:id', deleteRFQ);

// RFQ workflow routes
router.post('/:id/send', sendRFQInvitations);

// RFQ invitations routes
router.get('/:id/invitations', getRFQInvitations);

// RFQ submissions routes
router.get('/:id/submissions', getRFQSubmissions);
router.get('/:id/submissions/comparison', getRFQSubmissionsComparison);

// AI recommendations
router.post('/:id/ai-recommend', generateAIRecommendations);
router.get('/:id/ai-recommendations', generateAIRecommendations);

// Analytics routes
router.get('/:id/analytics', getRFQAnalytics);
router.post('/:id/analytics/recalculate', recalculateRFQAnalytics);

// Audit routes
router.get('/:id/audit', getRFQAuditHistory);

module.exports = router;