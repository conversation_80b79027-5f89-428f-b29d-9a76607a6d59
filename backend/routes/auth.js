const express = require('express');
const router = express.Router();
const {
  register,
  login,
  verifyToken,
  forgotPassword,
  resetPassword,
  refreshToken
} = require('../controllers/authController');
const { authenticateToken } = require('../middleware/auth');

// Public routes
router.post('/register', register);
router.post('/login', login);
router.post('/forgot-password', forgotPassword);
router.post('/reset-password', resetPassword);
router.post('/refresh-token', refreshToken);

// Protected routes
router.get('/verify', authenticateToken, verifyToken);

module.exports = router;