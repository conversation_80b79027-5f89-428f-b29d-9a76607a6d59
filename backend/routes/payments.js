const express = require('express');
const router = express.Router();
const {
  getPayments,
  getPaymentsByInvoiceId,
  getPaymentByTransactionId,
  createPayment,
  updatePaymentStatus,
  processRefund,
  getPaymentStatistics,
  handleStripeWebhook,
  handlePayPalWebhook
} = require('../controllers/paymentController');
const { authenticateToken } = require('../middleware/auth');

// Webhook routes (no authentication required)
router.post('/webhooks/stripe', handleStripeWebhook);
router.post('/webhooks/paypal', handlePayPalWebhook);

// Apply authentication middleware to all other routes
router.use(authenticateToken);

// Payment CRUD routes
router.get('/', getPayments);
router.get('/statistics', getPaymentStatistics);
router.get('/invoice/:invoiceId', getPaymentsByInvoiceId);
router.get('/transaction/:transactionId', getPaymentByTransactionId);
router.post('/', createPayment);
router.put('/:id/status', updatePaymentStatus);

// Payment operations
router.post('/:id/refund', processRefund);

module.exports = router;