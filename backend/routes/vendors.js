const express = require('express');
const router = express.Router();
const {
  getVendors,
  getVendorById,
  createVendor,
  updateVendor,
  deleteVendor,
  getVendorCategories,
  getVendorAudit,
  getVendorRFQHistory,
  getVendorRFQStatistics,
  getVendorEngagementAnalytics,
  updateVendorPerformanceFromRFQ,
} = require('../controllers/vendorController');

// Routes
router.get('/', getVendors);
router.get('/categories', getVendorCategories);
router.get('/:id', getVendorById);
router.get('/:id/audit', getVendorAudit);
router.get('/:id/rfq-history', getVendorRFQHistory);
router.get('/:id/rfq-statistics', getVendorRFQStatistics);
router.get('/:id/engagement-analytics', getVendorEngagementAnalytics);
router.post('/', createVendor);
router.put('/:id', updateVendor);
router.put('/:id/performance-rfq', updateVendorPerformanceFromRFQ);
router.delete('/:id', deleteVendor);

module.exports = router;