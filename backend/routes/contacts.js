const express = require('express');
const router = express.Router();
const ContactController = require('../controllers/contactController');
const ContactHierarchyService = require('../services/ContactHierarchyService');

// Middleware for authentication (assuming it exists)
// const { authenticateToken, requireRole } = require('../middleware/auth');

// Apply authentication middleware to all routes
// router.use(authenticateToken);

/**
 * @swagger
 * components:
 *   schemas:
 *     Contact:
 *       type: object
 *       required:
 *         - account_id
 *         - last_name
 *         - email
 *       properties:
 *         id:
 *           type: integer
 *           description: Unique contact identifier
 *         account_id:
 *           type: integer
 *           description: Associated account ID (required)
 *         first_name:
 *           type: string
 *           maxLength: 255
 *           description: Contact first name
 *         last_name:
 *           type: string
 *           maxLength: 255
 *           description: Contact last name (required)
 *         salutation:
 *           type: string
 *           enum: [Mr., Ms., Mrs., Dr., Prof., Rev.]
 *           description: Contact salutation
 *         title:
 *           type: string
 *           maxLength: 255
 *           description: Job title
 *         department:
 *           type: string
 *           maxLength: 255
 *           description: Department
 *         phone:
 *           type: string
 *           maxLength: 50
 *           description: Primary phone number
 *         mobile_phone:
 *           type: string
 *           maxLength: 50
 *           description: Mobile phone number
 *         email:
 *           type: string
 *           format: email
 *           maxLength: 255
 *           description: Email address (required, unique)
 *         mailing_address:
 *           type: object
 *           properties:
 *             street:
 *               type: string
 *             city:
 *               type: string
 *             state:
 *               type: string
 *             postal_code:
 *               type: string
 *             country:
 *               type: string
 *         reports_to_id:
 *           type: integer
 *           description: Manager contact ID
 *         lead_source:
 *           type: string
 *           enum: [Web, Phone Inquiry, Partner Referral, Purchased List, Other]
 *           description: Lead source
 *         level:
 *           type: string
 *           enum: [Primary, Secondary, Tertiary]
 *           description: Contact priority level
 *         do_not_call:
 *           type: boolean
 *           default: false
 *           description: Do not call preference
 *         has_opted_out_of_email:
 *           type: boolean
 *           default: false
 *           description: Email opt-out preference
 *         status:
 *           type: string
 *           enum: [open, dismissed, resolved, escalated]
 *           default: open
 *           description: Contact status
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 */

/**
 * @swagger
 * /api/contacts:
 *   get:
 *     summary: Get all contacts with filtering and pagination
 *     tags: [Contacts]
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for name, email, title, department
 *       - in: query
 *         name: account_id
 *         schema:
 *           type: integer
 *         description: Filter by account ID
 *       - in: query
 *         name: department
 *         schema:
 *           type: string
 *         description: Filter by department
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [Primary, Secondary, Tertiary]
 *         description: Filter by contact level
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [open, dismissed, resolved, escalated]
 *         description: Filter by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Contacts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     contacts:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Contact'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Server error
 */
router.get('/', ContactController.getAllContacts);

/**
 * @swagger
 * /api/contacts:
 *   post:
 *     summary: Create a new contact
 *     tags: [Contacts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - account_id
 *               - last_name
 *               - email
 *             properties:
 *               account_id:
 *                 type: integer
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               salutation:
 *                 type: string
 *                 enum: [Mr., Ms., Mrs., Dr., Prof., Rev.]
 *               title:
 *                 type: string
 *               department:
 *                 type: string
 *               phone:
 *                 type: string
 *               mobile_phone:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               mailing_address:
 *                 type: object
 *               reports_to_id:
 *                 type: integer
 *               lead_source:
 *                 type: string
 *                 enum: [Web, Phone Inquiry, Partner Referral, Purchased List, Other]
 *               level:
 *                 type: string
 *                 enum: [Primary, Secondary, Tertiary]
 *               do_not_call:
 *                 type: boolean
 *               has_opted_out_of_email:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Contact created successfully
 *       400:
 *         description: Validation error
 *       500:
 *         description: Server error
 */
router.post('/', ContactController.createContact);

/**
 * @swagger
 * /api/contacts/bulk:
 *   post:
 *     summary: Bulk create contacts
 *     tags: [Contacts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               contacts:
 *                 type: array
 *                 maxItems: 100
 *                 items:
 *                   type: object
 *                   required:
 *                     - account_id
 *                     - last_name
 *                     - email
 *     responses:
 *       201:
 *         description: Bulk create completed
 *       400:
 *         description: Validation error
 *       500:
 *         description: Server error
 */
router.post('/bulk', ContactController.bulkCreateContacts);

/**
 * @swagger
 * /api/contacts/bulk:
 *   put:
 *     summary: Bulk update contacts
 *     tags: [Contacts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               contacts:
 *                 type: array
 *                 maxItems: 100
 *                 items:
 *                   type: object
 *                   required:
 *                     - id
 *     responses:
 *       200:
 *         description: Bulk update completed
 *       400:
 *         description: Validation error
 *       500:
 *         description: Server error
 */
router.put('/bulk', ContactController.bulkUpdateContacts);

/**
 * @swagger
 * /api/contacts/duplicates:
 *   get:
 *     summary: Find potential duplicate contacts
 *     tags: [Contacts]
 *     parameters:
 *       - in: query
 *         name: email
 *         schema:
 *           type: string
 *         description: Email to search for duplicates
 *       - in: query
 *         name: first_name
 *         schema:
 *           type: string
 *         description: First name to search for duplicates
 *       - in: query
 *         name: last_name
 *         schema:
 *           type: string
 *         description: Last name to search for duplicates
 *       - in: query
 *         name: account_id
 *         schema:
 *           type: integer
 *         description: Limit search to specific account
 *     responses:
 *       200:
 *         description: Potential duplicates found
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Server error
 */
router.get('/duplicates', ContactController.findDuplicates);

/**
 * @swagger
 * /api/contacts/merge:
 *   post:
 *     summary: Merge duplicate contacts
 *     tags: [Contacts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - primary_contact_id
 *               - duplicate_contact_ids
 *             properties:
 *               primary_contact_id:
 *                 type: integer
 *               duplicate_contact_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *     responses:
 *       200:
 *         description: Contacts merged successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Contact not found
 *       500:
 *         description: Server error
 */
router.post('/merge', ContactController.mergeContacts);

/**
 * @swagger
 * /api/contacts/statistics:
 *   get:
 *     summary: Get contact statistics
 *     tags: [Contacts]
 *     parameters:
 *       - in: query
 *         name: account_id
 *         schema:
 *           type: integer
 *         description: Get statistics for specific account
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/statistics', ContactController.getContactStatistics);

/**
 * @swagger
 * /api/contacts/export:
 *   get:
 *     summary: Export contacts to CSV
 *     tags: [Contacts]
 *     parameters:
 *       - in: query
 *         name: account_id
 *         schema:
 *           type: integer
 *         description: Filter by account ID
 *       - in: query
 *         name: department
 *         schema:
 *           type: string
 *         description: Filter by department
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: CSV file
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *       500:
 *         description: Server error
 */
router.get('/export', ContactController.exportContacts);

/**
 * @swagger
 * /api/contacts/departments:
 *   get:
 *     summary: Get unique departments
 *     tags: [Contacts]
 *     parameters:
 *       - in: query
 *         name: account_id
 *         schema:
 *           type: integer
 *         description: Filter by account ID
 *     responses:
 *       200:
 *         description: Departments retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/departments', ContactController.getDepartments);

/**
 * @swagger
 * /api/contacts/lead-sources:
 *   get:
 *     summary: Get unique lead sources
 *     tags: [Contacts]
 *     responses:
 *       200:
 *         description: Lead sources retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/lead-sources', ContactController.getLeadSources);

/**
 * @swagger
 * /api/contacts/{id}:
 *   get:
 *     summary: Get contact by ID
 *     tags: [Contacts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     responses:
 *       200:
 *         description: Contact retrieved successfully
 *       404:
 *         description: Contact not found
 *       500:
 *         description: Server error
 */
router.get('/:id', ContactController.getContactById);

/**
 * @swagger
 * /api/contacts/{id}:
 *   put:
 *     summary: Update contact
 *     tags: [Contacts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               title:
 *                 type: string
 *               department:
 *                 type: string
 *               phone:
 *                 type: string
 *               email:
 *                 type: string
 *               reports_to_id:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Contact updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Contact not found
 *       500:
 *         description: Server error
 */
router.put('/:id', ContactController.updateContact);

/**
 * @swagger
 * /api/contacts/{id}:
 *   delete:
 *     summary: Delete contact (soft delete)
 *     tags: [Contacts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     responses:
 *       200:
 *         description: Contact deleted successfully
 *       400:
 *         description: Cannot delete contact with direct reports
 *       404:
 *         description: Contact not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', ContactController.deleteContact);

/**
 * @swagger
 * /api/contacts/{id}/hierarchy:
 *   put:
 *     summary: Update contact hierarchy position
 *     tags: [Contacts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reports_to_id:
 *                 type: integer
 *                 nullable: true
 *                 description: New manager ID (null for top level)
 *     responses:
 *       200:
 *         description: Hierarchy updated successfully
 *       400:
 *         description: Hierarchy validation failed
 *       404:
 *         description: Contact not found
 *       500:
 *         description: Server error
 */
router.put('/:id/hierarchy', ContactController.updateContactHierarchy);

/**
 * @swagger
 * /api/contacts/{id}/audit:
 *   get:
 *     summary: Get contact audit history
 *     tags: [Contacts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     responses:
 *       200:
 *         description: Audit history retrieved successfully
 *       400:
 *         description: Invalid contact ID
 *       500:
 *         description: Server error
 */
router.get('/:id/audit', ContactController.getContactAuditHistory);

/**
 * @swagger
 * /api/contacts/{id}/communication-preferences:
 *   put:
 *     summary: Update communication preferences
 *     tags: [Contacts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               doNotCall:
 *                 type: boolean
 *               hasOptedOutOfEmail:
 *                 type: boolean
 *               preferredLanguage:
 *                 type: string
 *               customPreferences:
 *                 type: object
 *     responses:
 *       200:
 *         description: Preferences updated successfully
 *       404:
 *         description: Contact not found
 *       500:
 *         description: Server error
 */
router.put('/:id/communication-preferences', ContactController.updateCommunicationPreferences);

// Account-specific contact routes
/**
 * @swagger
 * /api/contacts/account/{accountId}:
 *   get:
 *     summary: Get all contacts for a specific account
 *     tags: [Contacts]
 *     parameters:
 *       - in: path
 *         name: accountId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *       - in: query
 *         name: include_hierarchy
 *         schema:
 *           type: boolean
 *         description: Include organizational chart data
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *     responses:
 *       200:
 *         description: Account contacts retrieved successfully
 *       400:
 *         description: Invalid account ID
 *       500:
 *         description: Server error
 */
router.get('/account/:accountId', ContactController.getAccountContacts);

/**
 * @swagger
 * /api/contacts/account/{accountId}/hierarchy:
 *   get:
 *     summary: Get organizational chart for account
 *     tags: [Contacts]
 *     parameters:
 *       - in: path
 *         name: accountId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Account ID
 *       - in: query
 *         name: root_contact_id
 *         schema:
 *           type: integer
 *         description: Root contact ID for hierarchy
 *     responses:
 *       200:
 *         description: Organizational chart retrieved successfully
 *       400:
 *         description: Invalid account ID
 *       500:
 *         description: Server error
 */
router.get('/account/:accountId/hierarchy', ContactController.getContactHierarchy);

module.exports = router;