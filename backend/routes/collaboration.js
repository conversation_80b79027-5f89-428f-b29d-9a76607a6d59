const express = require('express');
const { Pool } = require('pg');
const { authenticateToken } = require('../middleware/auth');
const router = express.Router();

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

// Get comments for an entity
router.get('/comments/:entityType/:entityId', authenticateToken, async (req, res) => {
  try {
    const { entityType, entityId } = req.params;
    
    const query = `
      SELECT 
        c.id,
        c.text,
        c.parent_comment_id,
        c.created_at,
        c.updated_at,
        u.email as author_email
      FROM comments c
      JOIN users u ON c.user_id = u.id
      WHERE c.entity_type = $1 AND c.entity_id = $2
      ORDER BY c.created_at ASC
    `;
    
    const result = await pool.query(query, [entityType, entityId]);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching comments:', error);
    res.status(500).json({ error: 'Failed to fetch comments' });
  }
});

// Create a new comment
router.post('/comments', authenticateToken, async (req, res) => {
  try {
    const { entityType, entityId, text, parentCommentId } = req.body;
    const userId = req.user.id;
    
    const query = `
      INSERT INTO comments (entity_type, entity_id, user_id, text, parent_comment_id)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id, created_at
    `;
    
    const result = await pool.query(query, [entityType, entityId, userId, text, parentCommentId || null]);
    const comment = result.rows[0];
    
    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`${entityType}_${entityId}`).emit('new_comment', {
        id: comment.id,
        text,
        parentCommentId,
        authorEmail: req.user.email,
        createdAt: comment.created_at
      });
    }
    
    res.status(201).json({
      id: comment.id,
      text,
      parentCommentId,
      authorEmail: req.user.email,
      createdAt: comment.created_at
    });
  } catch (error) {
    console.error('Error creating comment:', error);
    res.status(500).json({ error: 'Failed to create comment' });
  }
});

// Update a comment
router.put('/comments/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { text } = req.body;
    const userId = req.user.id;
    
    // Check if user owns the comment
    const checkQuery = 'SELECT user_id, entity_type, entity_id FROM comments WHERE id = $1';
    const checkResult = await pool.query(checkQuery, [id]);
    
    if (checkResult.rows.length === 0) {
      return res.status(404).json({ error: 'Comment not found' });
    }
    
    if (checkResult.rows[0].user_id !== userId) {
      return res.status(403).json({ error: 'Not authorized to edit this comment' });
    }
    
    const updateQuery = `
      UPDATE comments 
      SET text = $1, updated_at = CURRENT_TIMESTAMP 
      WHERE id = $2 
      RETURNING updated_at
    `;
    
    const result = await pool.query(updateQuery, [text, id]);
    
    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      const { entity_type, entity_id } = checkResult.rows[0];
      io.to(`${entity_type}_${entity_id}`).emit('comment_updated', {
        id: parseInt(id),
        text,
        updatedAt: result.rows[0].updated_at
      });
    }
    
    res.json({ 
      id: parseInt(id), 
      text, 
      updatedAt: result.rows[0].updated_at 
    });
  } catch (error) {
    console.error('Error updating comment:', error);
    res.status(500).json({ error: 'Failed to update comment' });
  }
});

// Delete a comment
router.delete('/comments/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    // Check if user owns the comment
    const checkQuery = 'SELECT user_id, entity_type, entity_id FROM comments WHERE id = $1';
    const checkResult = await pool.query(checkQuery, [id]);
    
    if (checkResult.rows.length === 0) {
      return res.status(404).json({ error: 'Comment not found' });
    }
    
    if (checkResult.rows[0].user_id !== userId) {
      return res.status(403).json({ error: 'Not authorized to delete this comment' });
    }
    
    const deleteQuery = 'DELETE FROM comments WHERE id = $1';
    await pool.query(deleteQuery, [id]);
    
    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      const { entity_type, entity_id } = checkResult.rows[0];
      io.to(`${entity_type}_${entity_id}`).emit('comment_deleted', {
        id: parseInt(id)
      });
    }
    
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting comment:', error);
    res.status(500).json({ error: 'Failed to delete comment' });
  }
});

// Get active users for an entity (for presence indicators)
router.get('/presence/:entityType/:entityId', authenticateToken, async (req, res) => {
  try {
    const { entityType, entityId } = req.params;
    const io = req.app.get('io');
    
    if (!io) {
      return res.json([]);
    }
    
    const roomName = `${entityType}_${entityId}`;
    const room = io.sockets.adapter.rooms.get(roomName);
    
    if (!room) {
      return res.json([]);
    }
    
    const activeUsers = [];
    for (const socketId of room) {
      const socket = io.sockets.sockets.get(socketId);
      if (socket && socket.user) {
        activeUsers.push({
          id: socket.user.id,
          email: socket.user.email
        });
      }
    }
    
    res.json(activeUsers);
  } catch (error) {
    console.error('Error fetching presence:', error);
    res.status(500).json({ error: 'Failed to fetch presence' });
  }
});

module.exports = router;