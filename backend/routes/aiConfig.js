const express = require('express');
const router = express.Router();
const {
  saveConfiguration,
  getConfiguration,
  getAllConfigurations,
  deleteConfiguration,
  setCurrentProvider,
  getCurrentProvider,
  exportConfigurations,
  importConfigurations
} = require('../controllers/aiConfigController');
const { authenticateToken } = require('../middleware/auth');

// All AI configuration routes require authentication
router.use(authenticateToken);

// Configuration management routes
router.post('/configurations', saveConfiguration);
router.get('/configurations', getAllConfigurations);
router.get('/configurations/:provider', getConfiguration);
router.delete('/configurations/:provider', deleteConfiguration);

// Current provider management
router.post('/current-provider', setCurrentProvider);
router.get('/current-provider', getCurrentProvider);

// Backup and restore
router.get('/export', exportConfigurations);
router.post('/import', importConfigurations);

module.exports = router;