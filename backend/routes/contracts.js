const express = require('express');
const router = express.Router();
const {
  getContracts,
  getContractById,
  createContract,
  updateContract,
  deleteContract,
  getContractTemplates,
  createContractAmendment,
  updateMilestone,
  initiateESignature
} = require('../controllers/contractController');
const { authenticateToken } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Contract CRUD routes
router.get('/', getContracts);
router.get('/templates', getContractTemplates);
router.get('/:id', getContractById);
router.post('/', createContract);
router.put('/:id', updateContract);
router.delete('/:id', deleteContract);

// Contract-specific actions
router.post('/:id/amendments', createContractAmendment);
router.post('/:id/esign', initiateESignature);
router.put('/milestones/:milestoneId', updateMilestone);

module.exports = router;