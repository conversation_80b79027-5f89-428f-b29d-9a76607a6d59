#!/usr/bin/env node

/**
 * Comprehensive RFQ CRUD Test Suite with Full Form Data
 * Tests all RFQ operations with realistic data and creates sample records
 */

const axios = require('axios');
const { query } = require('./config/database');

const BASE_URL = 'http://localhost:3001/api';

// Mock authentication token (in real scenario, this would be obtained from login)
const AUTH_TOKEN = 'mock-jwt-token';

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Sample RFQ data with full form configuration
const sampleRFQData1 = {
  title: "Office Supplies and Equipment RFQ",
  description: "Request for quotation for office supplies including furniture, stationery, and IT equipment for our new branch office. We need competitive pricing and reliable delivery schedules.",
  items: [
    {
      id: "item-1",
      name: "Executive Office Desk",
      description: "Solid wood executive desk with drawers, minimum 60 inches wide",
      quantity: 5,
      specifications: {
        material: "Solid Oak or equivalent",
        dimensions: "60\" x 30\" x 30\"",
        finish: "Natural wood stain",
        features: ["Built-in cable management", "Locking drawers", "Scratch-resistant surface"]
      },
      category: "Furniture",
      estimatedPrice: 800,
      customFields: {
        warranty: "5 years",
        assembly: "Required"
      }
    },
    {
      id: "item-2",
      name: "Ergonomic Office Chairs",
      description: "High-back ergonomic office chairs with lumbar support",
      quantity: 15,
      specifications: {
        type: "Executive chair",
        material: "Mesh back with fabric seat",
        adjustability: "Height, armrest, tilt",
        weight_capacity: "300 lbs"
      },
      category: "Furniture",
      estimatedPrice: 350,
      customFields: {
        warranty: "3 years",
        color_options: ["Black", "Gray", "Navy"]
      }
    },
    {
      id: "item-3",
      name: "Laptop Computers",
      description: "Business laptops for office work and presentations",
      quantity: 10,
      specifications: {
        processor: "Intel i7 or AMD Ryzen 7",
        ram: "16GB DDR4",
        storage: "512GB SSD",
        display: "15.6 inch Full HD",
        os: "Windows 11 Pro"
      },
      category: "IT Equipment",
      estimatedPrice: 1200,
      customFields: {
        warranty: "3 years on-site",
        software: "MS Office included"
      }
    },
    {
      id: "item-4",
      name: "Printer Paper (A4)",
      description: "High-quality white printer paper for office use",
      quantity: 100,
      specifications: {
        size: "A4 (210 x 297 mm)",
        weight: "80 GSM",
        brightness: "96%",
        packaging: "500 sheets per ream"
      },
      category: "Stationery",
      estimatedPrice: 8,
      customFields: {
        eco_friendly: "FSC certified",
        bulk_discount: "Available for 50+ reams"
      }
    }
  ],
  due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
  selectedVendors: [1, 2, 3], // Assuming vendor IDs 1, 2, 3 exist
  formConfig: [
    {
      id: "delivery_timeline",
      type: "select",
      label: "Preferred Delivery Timeline",
      required: true,
      options: ["Within 2 weeks", "Within 1 month", "Within 2 months", "Flexible"],
      itemSpecific: false
    },
    {
      id: "payment_terms",
      type: "select",
      label: "Payment Terms",
      required: true,
      options: ["Net 30", "Net 60", "2/10 Net 30", "Cash on Delivery"],
      itemSpecific: false
    },
    {
      id: "installation_service",
      type: "select",
      label: "Installation/Setup Service Required",
      required: false,
      options: ["Yes - Full service", "Yes - Basic setup", "No - Self installation"],
      itemSpecific: true
    },
    {
      id: "warranty_extension",
      type: "select",
      label: "Extended Warranty Options",
      required: false,
      options: ["1 year extension", "2 year extension", "3 year extension", "No extension"],
      itemSpecific: true
    },
    {
      id: "bulk_discount",
      type: "number",
      label: "Bulk Discount Percentage (if applicable)",
      required: false,
      validation: [
        { type: "min", value: 0 },
        { type: "max", value: 50 }
      ],
      itemSpecific: false
    },
    {
      id: "references",
      type: "textarea",
      label: "Previous Client References",
      required: false,
      itemSpecific: false
    },
    {
      id: "certifications",
      type: "file",
      label: "Quality Certifications (PDF)",
      required: false,
      itemSpecific: true
    }
  ],
  terms: `Terms and Conditions:

1. DELIVERY: All items must be delivered to our office address within the specified timeline.

2. QUALITY: All products must meet or exceed the specifications mentioned in this RFQ.

3. WARRANTY: Standard manufacturer warranty applies unless otherwise specified.

4. PAYMENT: Payment will be made according to agreed terms after satisfactory delivery and inspection.

5. CANCELLATION: We reserve the right to cancel this RFQ at any time before contract signing.

6. COMPLIANCE: All vendors must comply with local business regulations and tax requirements.

7. EVALUATION: Proposals will be evaluated based on price (40%), quality (30%), delivery timeline (20%), and vendor reputation (10%).

8. CONFIDENTIALITY: All information shared in this RFQ process is confidential.`,
  currency: "USD",
  aiSettings: {
    priceWeight: 0.4,
    performanceWeight: 0.3,
    deliveryWeight: 0.2,
    riskWeight: 0.1,
    diversificationPreference: 0.6
  }
};

const sampleRFQData2 = {
  title: "Marketing Materials and Promotional Items RFQ",
  description: "Request for quotation for marketing materials including brochures, business cards, promotional items, and trade show materials for Q2 marketing campaign.",
  items: [
    {
      id: "item-1",
      name: "Corporate Brochures",
      description: "Tri-fold corporate brochures with company information and services",
      quantity: 5000,
      specifications: {
        size: "8.5\" x 11\" tri-fold",
        paper: "Glossy 100lb cover stock",
        colors: "Full color (CMYK)",
        finish: "UV coating"
      },
      category: "Marketing Materials",
      estimatedPrice: 0.75,
      customFields: {
        design_service: "Layout design included",
        proofing: "Digital proof required"
      }
    },
    {
      id: "item-2",
      name: "Business Cards",
      description: "Premium business cards for executives and sales team",
      quantity: 2000,
      specifications: {
        size: "3.5\" x 2\"",
        paper: "16pt cardstock",
        finish: "Matte with spot UV logo",
        colors: "Full color both sides"
      },
      category: "Marketing Materials",
      estimatedPrice: 0.25,
      customFields: {
        variable_data: "Names and titles vary",
        rush_order: "2-week delivery needed"
      }
    },
    {
      id: "item-3",
      name: "Promotional USB Drives",
      description: "Custom branded USB drives for trade shows and client gifts",
      quantity: 1000,
      specifications: {
        capacity: "8GB minimum",
        material: "Metal or high-quality plastic",
        branding: "Laser engraving or full color print",
        packaging: "Individual gift boxes"
      },
      category: "Promotional Items",
      estimatedPrice: 12,
      customFields: {
        data_preload: "Company presentation files",
        custom_shape: "Logo-shaped preferred"
      }
    }
  ],
  due_date: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString(), // 21 days from now
  selectedVendors: [2, 3, 4], // Assuming vendor IDs 2, 3, 4 exist
  formConfig: [
    {
      id: "design_services",
      type: "select",
      label: "Design Services Required",
      required: true,
      options: ["Full design service", "Layout only", "No design needed"],
      itemSpecific: true
    },
    {
      id: "rush_delivery",
      type: "select",
      label: "Rush Delivery Available",
      required: false,
      options: ["Yes - 1 week", "Yes - 2 weeks", "Standard delivery only"],
      itemSpecific: false
    },
    {
      id: "sample_approval",
      type: "select",
      label: "Sample/Proof Approval Process",
      required: true,
      options: ["Digital proof only", "Physical sample required", "Both digital and physical"],
      itemSpecific: true
    },
    {
      id: "quantity_breaks",
      type: "textarea",
      label: "Quantity Break Pricing",
      required: false,
      itemSpecific: true
    }
  ],
  terms: `Marketing Materials RFQ Terms:

1. QUALITY: All materials must be print-ready quality with color accuracy.

2. TIMELINE: Strict adherence to delivery dates is critical for campaign launch.

3. SAMPLES: Physical samples or digital proofs must be approved before production.

4. BRANDING: All items must follow brand guidelines (will be provided to selected vendor).

5. SHIPPING: Items may need to be shipped to multiple locations.

6. STORAGE: Vendor may need to store materials and ship in batches as needed.`,
  currency: "USD",
  aiSettings: {
    priceWeight: 0.35,
    performanceWeight: 0.25,
    deliveryWeight: 0.3,
    riskWeight: 0.1,
    diversificationPreference: 0.4
  }
};

// Sample submission data
const sampleSubmissions = [
  {
    rfq_id: null, // Will be set after RFQ creation
    vendor_id: 1,
    total_amount: 15750.00,
    currency: "USD",
    delivery_days: "Within 1 month",
    additional_notes: "We can provide all items with premium quality. Bulk discount of 5% applied for quantities over specified minimums. Free installation service included for furniture items.",
    bid_data: [
      {
        item_id: "item-1",
        unit_price: 750.00,
        total_price: 3750.00,
        delivery_days: 14,
        notes: "Premium oak desk with 5-year warranty",
        custom_fields: {
          delivery_days: "Within 2 weeks",
          installation_service: "Yes - Full service",
          warranty_extension: "1 year extension"
        }
      },
      {
        item_id: "item-2",
        unit_price: 320.00,
        total_price: 4800.00,
        delivery_days: 10,
        notes: "Ergonomic chairs with 3-year warranty, available in all requested colors",
        custom_fields: {
          delivery_days: "Within 2 weeks",
          installation_service: "Yes - Basic setup",
          warranty_extension: "No extension"
        }
      },
      {
        item_id: "item-3",
        unit_price: 1150.00,
        total_price: 11500.00,
        delivery_days: 7,
        notes: "Latest generation laptops with Windows 11 Pro and MS Office",
        custom_fields: {
          delivery_days: "Within 2 weeks",
          warranty_extension: "2 year extension"
        }
      },
      {
        item_id: "item-4",
        unit_price: 7.50,
        total_price: 750.00,
        delivery_days: 3,
        notes: "FSC certified paper with bulk discount applied",
        custom_fields: {
          delivery_days: "Within 2 weeks"
        }
      }
    ],
    form_responses: {
      delivery_days: "Within 1 month",
      payment_terms: "Net 30",
      bulk_discount: 5,
      references: "ABC Corp (2023), XYZ Ltd (2022), DEF Inc (2023) - References available upon request"
    }
  },
  {
    rfq_id: null, // Will be set after RFQ creation
    vendor_id: 2,
    total_amount: 16200.00,
    currency: "USD",
    delivery_days: "Within 2 months",
    additional_notes: "Competitive pricing with extended warranty options. We specialize in office furniture and IT equipment with 15+ years experience.",
    bid_data: [
      {
        item_id: "item-1",
        unit_price: 780.00,
        total_price: 3900.00,
        delivery_days: 21,
        notes: "Solid mahogany desk with premium finish",
        custom_fields: {
          delivery_days: "Within 1 month",
          installation_service: "Yes - Full service",
          warranty_extension: "2 year extension"
        }
      },
      {
        item_id: "item-2",
        unit_price: 340.00,
        total_price: 5100.00,
        delivery_days: 14,
        notes: "High-end ergonomic chairs with advanced lumbar support",
        custom_fields: {
          delivery_days: "Within 1 month",
          installation_service: "Yes - Full service",
          warranty_extension: "1 year extension"
        }
      },
      {
        item_id: "item-3",
        unit_price: 1180.00,
        total_price: 11800.00,
        delivery_days: 10,
        notes: "Business-grade laptops with enhanced security features",
        custom_fields: {
          delivery_days: "Within 1 month",
          warranty_extension: "3 year extension"
        }
      },
      {
        item_id: "item-4",
        unit_price: 8.00,
        total_price: 800.00,
        delivery_days: 5,
        notes: "Premium quality paper with eco-friendly certification",
        custom_fields: {
          delivery_days: "Within 2 weeks"
        }
      }
    ],
    form_responses: {
      delivery_days: "Within 2 months",
      payment_terms: "2/10 Net 30",
      bulk_discount: 3,
      references: "We have served 200+ corporate clients. Reference list available.",
      certifications: "ISO 9001, ISO 14001 certified"
    }
  }
];

// Test functions
async function testRFQCRUD() {
  console.log('🚀 Starting Comprehensive RFQ CRUD Tests\n');

  let createdRFQs = [];

  try {
    // Test 1: Create RFQ with full form data
    console.log('1. Testing RFQ Creation with Full Form Data...');
    
    const createResponse1 = await api.post('/rfqs', sampleRFQData1);
    console.log('✅ RFQ 1 created successfully');
    console.log(`   ID: ${createResponse1.data.data.id}`);
    console.log(`   Title: ${createResponse1.data.data.title}`);
    console.log(`   Items: ${createResponse1.data.data.items.length}`);
    console.log(`   Form Config: ${createResponse1.data.data.form_config.length} fields`);
    createdRFQs.push(createResponse1.data.data);

    const createResponse2 = await api.post('/rfqs', sampleRFQData2);
    console.log('✅ RFQ 2 created successfully');
    console.log(`   ID: ${createResponse2.data.data.id}`);
    console.log(`   Title: ${createResponse2.data.data.title}`);
    console.log(`   Items: ${createResponse2.data.data.items.length}`);
    createdRFQs.push(createResponse2.data.data);

    // Test 2: Read RFQ with full details
    console.log('\n2. Testing RFQ Read Operations...');
    
    const readResponse = await api.get(`/rfqs/${createdRFQs[0].id}`);
    console.log('✅ RFQ read successfully');
    console.log(`   Full details retrieved for RFQ ${readResponse.data.data.id}`);
    console.log(`   AI Settings: ${JSON.stringify(readResponse.data.data.ai_settings)}`);
    console.log(`   Terms length: ${readResponse.data.data.terms.length} characters`);

    // Test 3: Update RFQ
    console.log('\n3. Testing RFQ Update Operations...');
    
    const updateData = {
      title: "Updated: " + createdRFQs[0].title,
      description: "UPDATED: " + createdRFQs[0].description,
      currency: "EUR",
      aiSettings: {
        priceWeight: 0.5,
        performanceWeight: 0.3,
        deliveryWeight: 0.15,
        riskWeight: 0.05,
        diversificationPreference: 0.7
      }
    };

    const updateResponse = await api.put(`/rfqs/${createdRFQs[0].id}`, updateData);
    console.log('✅ RFQ updated successfully');
    console.log(`   New title: ${updateResponse.data.data.title}`);
    console.log(`   New currency: ${updateResponse.data.data.currency}`);

    // Test 4: Send RFQ invitations
    console.log('\n4. Testing RFQ Send Operations...');
    
    const sendResponse = await api.post(`/rfqs/${createdRFQs[0].id}/send`);
    console.log('✅ RFQ invitations sent successfully');
    console.log(`   Status: ${sendResponse.data.data.rfq.status}`);
    console.log(`   Invitations sent: ${sendResponse.data.data.invitations.length}`);

    const sendResponse2 = await api.post(`/rfqs/${createdRFQs[1].id}/send`);
    console.log('✅ RFQ 2 invitations sent successfully');

    // Test 5: List RFQs with enhanced filtering
    console.log('\n5. Testing Enhanced RFQ Listing...');
    
    const listResponse = await api.get('/rfqs', {
      params: {
        search: 'office',
        status: 'sent',
        currency: 'USD',
        sort_by: 'created_at',
        sort_order: 'desc',
        include_analytics: true
      }
    });
    console.log('✅ Enhanced RFQ listing works');
    console.log(`   Found ${listResponse.data.data.length} RFQs`);
    console.log(`   Pagination: Page ${listResponse.data.pagination.page} of ${listResponse.data.pagination.totalPages}`);

    // Test 6: Advanced search
    console.log('\n6. Testing Advanced Search...');
    
    const searchResponse = await api.post('/rfqs/search', {
      query: 'office supplies',
      filters: {
        status: ['sent'],
        currencies: ['USD', 'EUR']
      },
      page: 1,
      limit: 10
    });
    console.log('✅ Advanced search works');
    console.log(`   Search results: ${searchResponse.data.data.length} RFQs`);

    console.log('\n🎉 All RFQ CRUD tests passed successfully!');
    return createdRFQs;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  }
}

async function createSampleSubmissions(rfqIds) {
  console.log('\n📝 Creating Sample RFQ Submissions...');

  try {
    // Update submission data with actual RFQ IDs
    sampleSubmissions[0].rfq_id = rfqIds[0];
    sampleSubmissions[1].rfq_id = rfqIds[0]; // Both submissions for first RFQ

    // Create submissions for first RFQ
    for (let i = 0; i < 2; i++) {
      const submissionData = sampleSubmissions[i];
      
      // Insert submission directly into database since we don't have submission API endpoint
      // Get invitation token from database
       const invitationResult = await query(
         'SELECT token FROM rfq_invitations WHERE rfq_id = $1 AND vendor_id = $2',
         [submissionData.rfq_id, submissionData.vendor_id]
       );
       
       const invitationToken = invitationResult.rows[0]?.token;
       if (!invitationToken) {
         console.log(`⚠️  No invitation found for RFQ ${submissionData.rfq_id}, vendor ${submissionData.vendor_id}`);
         continue;
       }

      const submissionQuery = `
        INSERT INTO rfq_submissions (
          rfq_id, vendor_id, invitation_token, total_amount, currency, delivery_days, 
          additional_notes, bid_data, submitted_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
        RETURNING *
      `;

      const result = await query(submissionQuery, [
        submissionData.rfq_id,
        submissionData.vendor_id,
        invitationToken,
        submissionData.total_amount,
        submissionData.currency,
        submissionData.delivery_days,
        submissionData.additional_notes,
        JSON.stringify(submissionData.bid_data)
      ]);

      console.log(`✅ Submission ${i + 1} created for RFQ ${submissionData.rfq_id}`);
      console.log(`   Vendor ID: ${submissionData.vendor_id}`);
      console.log(`   Total Amount: $${submissionData.total_amount}`);
      console.log(`   Items: ${submissionData.bid_data.length}`);

      // Update invitation status
      await query(
        'UPDATE rfq_invitations SET status = $1 WHERE rfq_id = $2 AND vendor_id = $3',
        ['submitted', submissionData.rfq_id, submissionData.vendor_id]
      );
    }

    // Create submissions for second RFQ (marketing materials)
    const marketingSubmissions = [
      {
        rfq_id: rfqIds[1],
        vendor_id: 2,
        total_amount: 4250.00,
        currency: "USD",
        delivery_days: "Within 2 weeks",
        additional_notes: "We specialize in marketing materials with fast turnaround times. All items include design services and rush delivery options.",
        bid_data: [
          {
            item_id: "item-1",
            unit_price: 0.70,
            total_price: 3500.00,
            delivery_days: 10,
            notes: "High-quality tri-fold brochures with UV coating",
            custom_fields: {
              design_services: "Full design service",
              sample_approval: "Both digital and physical"
            }
          },
          {
            item_id: "item-2",
            unit_price: 0.22,
            total_price: 440.00,
            delivery_days: 7,
            notes: "Premium business cards with spot UV finish",
            custom_fields: {
              design_services: "Layout only",
              sample_approval: "Digital proof only"
            }
          },
          {
            item_id: "item-3",
            unit_price: 11.50,
            total_price: 11500.00,
            delivery_days: 14,
            notes: "Custom metal USB drives with laser engraving",
            custom_fields: {
              design_services: "No design needed",
              sample_approval: "Physical sample required"
            }
          }
        ],
        form_responses: {
          design_services: "Full design service",
          rush_delivery: "Yes - 2 weeks",
          sample_approval: "Both digital and physical",
          quantity_breaks: "5000+ brochures: 10% discount, 10000+: 15% discount"
        }
      },
      {
        rfq_id: rfqIds[1],
        vendor_id: 3,
        total_amount: 4100.00,
        currency: "USD",
        delivery_timeline: "Within 3 weeks",
        notes: "Competitive pricing with excellent quality. We have extensive experience in promotional items and marketing materials.",
        bid_data: [
          {
            item_id: "item-1",
            unit_price: 0.68,
            total_price: 3400.00,
            delivery_days: 12,
            notes: "Premium brochures with eco-friendly paper options",
            custom_fields: {
              design_services: "Full design service",
              sample_approval: "Digital proof only"
            }
          },
          {
            item_id: "item-2",
            unit_price: 0.20,
            total_price: 400.00,
            delivery_days: 8,
            notes: "High-quality business cards with multiple finish options",
            custom_fields: {
              design_services: "Layout only",
              sample_approval: "Physical sample required"
            }
          },
          {
            item_id: "item-3",
            unit_price: 10.50,
            total_price: 10500.00,
            delivery_days: 18,
            notes: "Custom plastic USB drives with full-color printing",
            custom_fields: {
              design_services: "Full design service",
              sample_approval: "Both digital and physical"
            }
          }
        ],
        form_responses: {
          design_services: "Full design service",
          rush_delivery: "Standard delivery only",
          sample_approval: "Both digital and physical",
          quantity_breaks: "Bulk pricing available for orders over $5000"
        }
      }
    ];

    for (let i = 0; i < marketingSubmissions.length; i++) {
      const submissionData = marketingSubmissions[i];
      
      // Get invitation token from database
       const invitationResult = await query(
         'SELECT token FROM rfq_invitations WHERE rfq_id = $1 AND vendor_id = $2',
         [submissionData.rfq_id, submissionData.vendor_id]
       );
       
       const invitationToken = invitationResult.rows[0]?.token;
       if (!invitationToken) {
         console.log(`⚠️  No invitation found for RFQ ${submissionData.rfq_id}, vendor ${submissionData.vendor_id}`);
         continue;
       }

      const submissionQuery = `
        INSERT INTO rfq_submissions (
          rfq_id, vendor_id, invitation_token, total_amount, currency, delivery_days, 
          additional_notes, bid_data, submitted_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
        RETURNING *
      `;

      await query(submissionQuery, [
        submissionData.rfq_id,
        submissionData.vendor_id,
        invitationToken,
        submissionData.total_amount,
        submissionData.currency,
        submissionData.delivery_days,
        submissionData.additional_notes,
        JSON.stringify(submissionData.bid_data)
      ]);

      console.log(`✅ Marketing submission ${i + 1} created for RFQ ${submissionData.rfq_id}`);
      console.log(`   Vendor ID: ${submissionData.vendor_id}`);
      console.log(`   Total Amount: $${submissionData.total_amount}`);

      // Update invitation status
      await query(
        'UPDATE rfq_invitations SET status = $1 WHERE rfq_id = $2 AND vendor_id = $3',
        ['submitted', submissionData.rfq_id, submissionData.vendor_id]
      );
    }

    console.log('\n✅ All sample submissions created successfully!');

  } catch (error) {
    console.error('❌ Failed to create submissions:', error.message);
    throw error;
  }
}

async function testSubmissionViews(rfqIds) {
  console.log('\n👀 Testing RFQ Submission Views...');

  try {
    // Test submissions for first RFQ
    const submissionsResponse = await api.get(`/rfqs/${rfqIds[0]}/submissions`);
    console.log('✅ RFQ submissions retrieved successfully');
    console.log(`   RFQ ${rfqIds[0]} has ${submissionsResponse.data.data.length} submissions`);

    submissionsResponse.data.data.forEach((submission, index) => {
      console.log(`   Submission ${index + 1}:`);
      console.log(`     Vendor ID: ${submission.vendor_id}`);
      console.log(`     Total: $${submission.total_amount}`);
      console.log(`     Items: ${submission.bid_data.length}`);
      console.log(`     Delivery: ${submission.delivery_days}`);
    });

    // Test comparison view
    const comparisonResponse = await api.get(`/rfqs/${rfqIds[0]}/submissions/comparison`);
    console.log('\n✅ RFQ submission comparison retrieved successfully');
    console.log(`   Comparison data includes ${comparisonResponse.data.data.submissions?.length || 0} submissions`);

    // Test analytics
    const analyticsResponse = await api.get(`/rfqs/${rfqIds[0]}/analytics`);
    console.log('\n✅ RFQ analytics retrieved successfully');
    console.log(`   Response rate: ${analyticsResponse.data.data.response_rate}%`);
    console.log(`   Average bid: $${analyticsResponse.data.data.average_bid_amount || 'N/A'}`);

    // Test AI recommendations
    const aiResponse = await api.post(`/rfqs/${rfqIds[0]}/ai-recommend`);
    console.log('\n✅ AI recommendations generated successfully');
    console.log(`   Generated ${aiResponse.data.data.length} recommendations`);

    console.log('\n🎉 All submission view tests passed!');

  } catch (error) {
    console.error('❌ Submission view test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function runFullTestSuite() {
  console.log('🧪 Starting Full RFQ Test Suite with Sample Data Creation\n');
  console.log('=' .repeat(60));

  try {
    // Run CRUD tests and get created RFQ IDs
    const createdRFQs = await testRFQCRUD();
    const rfqIds = createdRFQs.map(rfq => rfq.id);

    console.log('\n' + '=' .repeat(60));

    // Create sample submissions
    await createSampleSubmissions(rfqIds);

    console.log('\n' + '=' .repeat(60));

    // Test submission views
    await testSubmissionViews(rfqIds);

    console.log('\n' + '=' .repeat(60));
    console.log('🎊 FULL TEST SUITE COMPLETED SUCCESSFULLY! 🎊');
    console.log('\nSample data created:');
    console.log(`- 2 RFQs with full form data (IDs: ${rfqIds.join(', ')})`);
    console.log('- 4 submissions (2 per RFQ) with detailed responses');
    console.log('- All CRUD operations tested and working');
    console.log('- Enhanced API features verified');
    console.log('\nYou can now test the UI with this sample data!');

  } catch (error) {
    console.error('\n💥 TEST SUITE FAILED:', error.message);
    process.exit(1);
  }
}

// Export functions for use in other modules
module.exports = {
  testRFQCRUD,
  createSampleSubmissions,
  testSubmissionViews,
  runFullTestSuite,
  sampleRFQData1,
  sampleRFQData2
};

// Run the full test suite if this script is executed directly
if (require.main === module) {
  runFullTestSuite().catch(console.error);
}