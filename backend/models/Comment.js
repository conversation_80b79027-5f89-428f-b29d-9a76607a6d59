const { Pool } = require('pg');
const pool = require('../config/database');

class Comment {
  constructor(data = {}) {
    this.id = data.id;
    this.entity_type = data.entity_type;
    this.entity_id = data.entity_id;
      this.parent_comment_id = data.parent_comment_id;
    this.user_id = data.user_id;
      this.text = data.text;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
    this.user_email = data.user_email;
  }

  // Create a new comment
  static async create(commentData) {
    const {
      object_type,
      object_id,
      parent_id,
      user_id,
      content,
      content_type = 'text',
      category,
      metadata
    } = commentData;

    const query = `
      INSERT INTO comments (
        entity_type, entity_id, parent_comment_id, user_id, text
      )
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `;

    const values = [
      object_type,
      object_id,
      parent_id,
      user_id,
      content
    ];

    try {
      const result = await pool.query(query, values);
      return new Comment(result.rows[0]);
    } catch (error) {
      throw new Error(`Failed to create comment: ${error.message}`);
    }
  }

  // Get comment by ID
  static async findById(id) {
    const query = `
      SELECT c.*, u.email as user_email
      FROM comments c
      JOIN users u ON c.user_id = u.id
      WHERE c.id = $1
    `;

    try {
      const result = await pool.query(query, [id]);
      if (result.rows.length === 0) {
        return null;
      }
      return new Comment(result.rows[0]);
    } catch (error) {
      throw new Error(`Failed to find comment: ${error.message}`);
    }
  }

  // Get comments for an object with threading
  static async getThread(objectType, objectId, options = {}) {
    const { includeDeleted = false, category = null } = options;
    
    let query = `
      SELECT c.*, u.email as user_email
      FROM comments c
      JOIN users u ON c.user_id = u.id
      WHERE c.entity_type = $1 AND c.entity_id = $2
    `;
    
    const values = [objectType, objectId];
    let paramCount = 3;

    // Note: No deleted_at column in actual schema

    if (category) {
      query += ` AND c.category = $${paramCount}`;
      values.push(category);
      paramCount++;
    }

    query += ` ORDER BY c.created_at ASC`;

    try {
      const result = await pool.query(query, values);
      return result.rows.map(row => new Comment(row));
    } catch (error) {
      throw new Error(`Failed to get comment thread: ${error.message}`);
    }
  }

  // Update comment
  static async update(id, updateData, userId) {
    const allowedFields = ['text'];
    const updates = [];
    const values = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        updates.push(`${key} = $${paramCount}`);
        values.push(updateData[key]);
        paramCount++;
      }
    });

    if (updates.length === 0) {
      throw new Error('No valid fields to update');
    }

    values.push(id, userId);
    const query = `
      UPDATE comments 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount} AND user_id = $${paramCount + 1}
      RETURNING *
    `;

    try {
      const result = await pool.query(query, values);
      if (result.rows.length === 0) {
        throw new Error('Comment not found or unauthorized');
      }
      return new Comment(result.rows[0]);
    } catch (error) {
      throw new Error(`Failed to update comment: ${error.message}`);
    }
  }

  // Soft delete comment
  static async delete(id, userId) {
    const query = `
      DELETE FROM comments 
      WHERE id = $1 AND user_id = $2
      RETURNING id
    `;

    try {
      const result = await pool.query(query, [id, userId]);
      return result.rows.length > 0;
    } catch (error) {
      throw new Error(`Failed to delete comment: ${error.message}`);
    }
  }

  // Get comments by user
  static async getByUser(userId, options = {}) {
    const { limit = 50, offset = 0, objectType = null } = options;
    
    let query = `
      SELECT c.*, u.email as user_email
      FROM comments c
      JOIN users u ON c.user_id = u.id
      WHERE c.user_id = $1
    `;
    
    const values = [userId];
    let paramCount = 2;

    if (objectType) {
      query += ` AND c.entity_type = $${paramCount}`;
      values.push(objectType);
      paramCount++;
    }

    query += ` ORDER BY c.created_at DESC LIMIT $${paramCount} OFFSET $${paramCount + 1}`;
    values.push(limit, offset);

    try {
      const result = await pool.query(query, values);
      return result.rows.map(row => new Comment(row));
    } catch (error) {
      throw new Error(`Failed to get user comments: ${error.message}`);
    }
  }

  // Search comments
  static async search(searchTerm, options = {}) {
    const { objectType = null, category = null, limit = 50, offset = 0 } = options;
    
    let query = `
      SELECT c.*, u.email as user_email
      FROM comments c
      JOIN users u ON c.user_id = u.id
      WHERE c.text ILIKE $1
    `;
    
    const values = [`%${searchTerm}%`];
    let paramCount = 2;

    if (objectType) {
      query += ` AND c.entity_type = $${paramCount}`;
      values.push(objectType);
      paramCount++;
    }

    if (category) {
      query += ` AND c.category = $${paramCount}`;
      values.push(category);
      paramCount++;
    }

    query += ` ORDER BY c.created_at DESC LIMIT $${paramCount} OFFSET $${paramCount + 1}`;
    values.push(limit, offset);

    try {
      const result = await pool.query(query, values);
      return result.rows.map(row => new Comment(row));
    } catch (error) {
      throw new Error(`Failed to search comments: ${error.message}`);
    }
  }

  // Get comment statistics
  static async getStats(objectType = null, objectId = null) {
    let query = `
      SELECT 
        COUNT(*) as total_comments,
        COUNT(CASE WHEN parent_comment_id IS NOT NULL THEN 1 END) as replies,
        COUNT(DISTINCT user_id) as unique_commenters
      FROM comments
      WHERE 1=1
    `;
    
    const values = [];
    let paramCount = 1;

    if (objectType) {
      query += ` AND entity_type = $${paramCount}`;
      values.push(objectType);
      paramCount++;
    }

    if (objectId) {
      query += ` AND entity_id = $${paramCount}`;
      values.push(objectId);
      paramCount++;
    }

    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw new Error(`Failed to get comment stats: ${error.message}`);
    }
  }

  // Instance method to add attachment
  async addAttachment(attachmentData) {
    const { filename, original_name, file_size, mime_type, file_path, uploaded_by } = attachmentData;
    
    const query = `
      INSERT INTO comment_attachments (
        comment_id, filename, original_name, file_size, mime_type, file_path, uploaded_by
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    try {
      const result = await pool.query(query, [
        this.id, filename, original_name, file_size, mime_type, file_path, uploaded_by
      ]);
      return result.rows[0];
    } catch (error) {
      throw new Error(`Failed to add attachment: ${error.message}`);
    }
  }

  // Instance method to add mention
  async addMention(mentionedUserId) {
    const query = `
      INSERT INTO comment_mentions (comment_id, mentioned_user_id)
      VALUES ($1, $2)
      ON CONFLICT (comment_id, mentioned_user_id) DO NOTHING
      RETURNING *
    `;

    try {
      const result = await pool.query(query, [this.id, mentionedUserId]);
      return result.rows[0];
    } catch (error) {
      throw new Error(`Failed to add mention: ${error.message}`);
    }
  }

  // Get attachments for this comment
  async getAttachments() {
    const query = `
      SELECT * FROM comment_attachments
      WHERE comment_id = $1
      ORDER BY created_at ASC
    `;

    try {
      const result = await pool.query(query, [this.id]);
      return result.rows;
    } catch (error) {
      throw new Error(`Failed to get attachments: ${error.message}`);
    }
  }

  // Get mentions for this comment
  async getMentions() {
    const query = `
      SELECT cm.*, u.email as user_email
      FROM comment_mentions cm
      JOIN users u ON cm.mentioned_user_id = u.id
      WHERE cm.comment_id = $1
      ORDER BY cm.created_at ASC
    `;

    try {
      const result = await pool.query(query, [this.id]);
      return result.rows;
    } catch (error) {
      throw new Error(`Failed to get mentions: ${error.message}`);
    }
  }

  // Convert to JSON for API responses
  toJSON() {
    return {
      id: this.id,
      entity_type: this.entity_type,
      entity_id: this.entity_id,
      parent_comment_id: this.parent_comment_id,
      user_id: this.user_id,
      user_email: this.user_email,
      text: this.text,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}

module.exports = Comment;