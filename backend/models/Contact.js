const {
  query,
  queryWithUser,
  beginTransaction,
  commitTransaction,
  rollbackTransaction,
} = require("../config/database");

class Contact {
  // Get all contacts with filtering and pagination
  static async findAll(filters = {}, page = 1, limit = 10) {
    let whereClause = "WHERE c.is_deleted = FALSE";
    const params = [];
    let paramCount = 0;

    // Apply filters
    if (filters.search) {
      paramCount++;
      whereClause += ` AND (c.first_name ILIKE $${paramCount} OR c.last_name ILIKE $${paramCount} OR c.email ILIKE $${paramCount} OR c.title ILIKE $${paramCount} OR c.department ILIKE $${paramCount})`;
      params.push(`%${filters.search}%`);
    }

    if (filters.account_id) {
      paramCount++;
      whereClause += ` AND c.account_id = $${paramCount}`;
      params.push(filters.account_id);
    }

    if (filters.department) {
      paramCount++;
      whereClause += ` AND c.department = $${paramCount}`;
      params.push(filters.department);
    }

    if (filters.level) {
      paramCount++;
      whereClause += ` AND c.level = $${paramCount}`;
      params.push(filters.level);
    }

    if (filters.lead_source) {
      paramCount++;
      whereClause += ` AND c.lead_source = $${paramCount}`;
      params.push(filters.lead_source);
    }

    if (filters.status) {
      paramCount++;
      whereClause += ` AND c.status = $${paramCount}`;
      params.push(filters.status);
    }

    if (filters.owner_id) {
      paramCount++;
      whereClause += ` AND c.owner_id = $${paramCount}`;
      params.push(filters.owner_id);
    }

    if (filters.reports_to_id !== undefined) {
      paramCount++;
      if (filters.reports_to_id === null) {
        whereClause += ` AND c.reports_to_id IS NULL`;
      } else {
        whereClause += ` AND c.reports_to_id = $${paramCount}`;
        params.push(filters.reports_to_id);
      }
    }

    if (filters.has_direct_reports !== undefined) {
      if (filters.has_direct_reports) {
        whereClause += ` AND EXISTS (SELECT 1 FROM contacts sub WHERE sub.reports_to_id = c.id AND sub.is_deleted = FALSE)`;
      } else {
        whereClause += ` AND NOT EXISTS (SELECT 1 FROM contacts sub WHERE sub.reports_to_id = c.id AND sub.is_deleted = FALSE)`;
      }
    }

    if (filters.created_after) {
      paramCount++;
      whereClause += ` AND c.created_at >= $${paramCount}`;
      params.push(filters.created_after);
    }

    if (filters.created_before) {
      paramCount++;
      whereClause += ` AND c.created_at <= $${paramCount}`;
      params.push(filters.created_before);
    }

    // Count total records
    const countQuery = `
      SELECT COUNT(*) 
      FROM contacts c
      LEFT JOIN accounts a ON c.account_id = a.id
      ${whereClause}
    `;
    const countResult = await query(countQuery, params);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const offset = (page - 1) * limit;
    paramCount++;
    const limitParam = paramCount;
    paramCount++;
    const offsetParam = paramCount;

    const selectQuery = `
      SELECT 
        c.id, c.account_id, c.first_name, c.last_name, c.salutation, c.title,
        c.department, c.phone, c.mobile_phone, c.home_phone, c.other_phone,
        c.fax, c.email, c.mailing_address, c.other_address, c.reports_to_id,
        c.assistant_name, c.assistant_phone, c.birthdate, c.lead_source,
        c.description, c.languages, c.level, c.do_not_call, c.has_opted_out_of_email,
        c.owner_id, c.integration_id, c.custom_fields, c.status, c.created_at,
        c.updated_at, c.created_by_id, c.last_modified_by_id, c.is_deleted,
        -- Computed full name
        CASE 
          WHEN c.first_name IS NOT NULL AND c.first_name != '' 
          THEN c.first_name || ' ' || c.last_name 
          ELSE c.last_name 
        END as full_name,
        -- Account information
        a.name as account_name,
        -- Reports to information
        rt.first_name || ' ' || rt.last_name as reports_to_name,
        -- Owner information
        u.email as owner_email,
        -- Direct reports count
        (SELECT COUNT(*) FROM contacts sub WHERE sub.reports_to_id = c.id AND sub.is_deleted = FALSE) as direct_reports_count
      FROM contacts c
      LEFT JOIN accounts a ON c.account_id = a.id
      LEFT JOIN contacts rt ON c.reports_to_id = rt.id AND rt.is_deleted = FALSE
      LEFT JOIN users u ON c.owner_id = u.id
      ${whereClause}
      ORDER BY c.created_at DESC
      LIMIT $${limitParam} OFFSET $${offsetParam}
    `;

    params.push(limit, offset);
    const result = await query(selectQuery, params);

    return {
      contacts: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  // Find contact by ID
  static async findById(id) {
    const result = await query(
      `SELECT 
        c.id, c.account_id, c.first_name, c.last_name, c.salutation, c.title,
        c.department, c.phone, c.mobile_phone, c.home_phone, c.other_phone,
        c.fax, c.email, c.mailing_address, c.other_address, c.reports_to_id,
        c.assistant_name, c.assistant_phone, c.birthdate, c.lead_source,
        c.description, c.languages, c.level, c.do_not_call, c.has_opted_out_of_email,
        c.owner_id, c.integration_id, c.custom_fields, c.status, c.created_at,
        c.updated_at, c.created_by_id, c.last_modified_by_id, c.is_deleted,
        -- Computed full name
        CASE 
          WHEN c.first_name IS NOT NULL AND c.first_name != '' 
          THEN c.first_name || ' ' || c.last_name 
          ELSE c.last_name 
        END as full_name,
        -- Account information
        a.name as account_name,
        -- Reports to information
        rt.first_name || ' ' || rt.last_name as reports_to_name,
        -- Owner information
        u.email as owner_email,
        -- Direct reports count
        (SELECT COUNT(*) FROM contacts sub WHERE sub.reports_to_id = c.id AND sub.is_deleted = FALSE) as direct_reports_count
      FROM contacts c
      LEFT JOIN accounts a ON c.account_id = a.id
      LEFT JOIN contacts rt ON c.reports_to_id = rt.id AND rt.is_deleted = FALSE
      LEFT JOIN users u ON c.owner_id = u.id
      WHERE c.id = $1 AND c.is_deleted = FALSE`,
      [id]
    );
    return result.rows[0];
  }

  // Get contact hierarchy (subordinates)
  static async getHierarchy(id) {
    const result = await query(
      `WITH RECURSIVE contact_hierarchy AS (
        SELECT id, first_name, last_name, reports_to_id, 0 as level,
               CASE 
                 WHEN first_name IS NOT NULL AND first_name != '' 
                 THEN first_name || ' ' || last_name 
                 ELSE last_name 
               END as full_name
        FROM contacts 
        WHERE id = $1 AND is_deleted = FALSE
        
        UNION ALL
        
        SELECT c.id, c.first_name, c.last_name, c.reports_to_id, ch.level + 1,
               CASE 
                 WHEN c.first_name IS NOT NULL AND c.first_name != '' 
                 THEN c.first_name || ' ' || c.last_name 
                 ELSE c.last_name 
               END as full_name
        FROM contacts c
        INNER JOIN contact_hierarchy ch ON c.reports_to_id = ch.id
        WHERE c.is_deleted = FALSE AND ch.level < 5
      )
      SELECT * FROM contact_hierarchy ORDER BY level, full_name`,
      [id]
    );
    return result.rows;
  }

  // Get direct reports
  static async getDirectReports(id) {
    const result = await query(
      `SELECT 
        c.id, c.first_name, c.last_name, c.title, c.department, c.email, c.phone,
        CASE 
          WHEN c.first_name IS NOT NULL AND c.first_name != '' 
          THEN c.first_name || ' ' || c.last_name 
          ELSE c.last_name 
        END as full_name
      FROM contacts c
      WHERE c.reports_to_id = $1 AND c.is_deleted = FALSE
      ORDER BY c.last_name, c.first_name`,
      [id]
    );
    return result.rows;
  }

  // Get contact ancestors (reporting chain)
  static async getReportingChain(id) {
    const result = await query(
      `WITH RECURSIVE reporting_chain AS (
        SELECT id, first_name, last_name, reports_to_id, 0 as level,
               CASE 
                 WHEN first_name IS NOT NULL AND first_name != '' 
                 THEN first_name || ' ' || last_name 
                 ELSE last_name 
               END as full_name
        FROM contacts 
        WHERE id = $1 AND is_deleted = FALSE
        
        UNION ALL
        
        SELECT c.id, c.first_name, c.last_name, c.reports_to_id, rc.level + 1,
               CASE 
                 WHEN c.first_name IS NOT NULL AND c.first_name != '' 
                 THEN c.first_name || ' ' || c.last_name 
                 ELSE c.last_name 
               END as full_name
        FROM contacts c
        INNER JOIN reporting_chain rc ON c.id = rc.reports_to_id
        WHERE c.is_deleted = FALSE AND rc.level < 5
      )
      SELECT * FROM reporting_chain WHERE level > 0 ORDER BY level DESC`,
      [id]
    );
    return result.rows;
  }

  // Create new contact
  static async create(contactData, userId = 1) {
    const {
      account_id,
      first_name,
      last_name,
      salutation,
      title,
      department,
      phone,
      mobile_phone,
      home_phone,
      other_phone,
      fax,
      email,
      mailing_address,
      other_address,
      reports_to_id,
      assistant_name,
      assistant_phone,
      birthdate,
      lead_source,
      description,
      languages,
      level,
      do_not_call = false,
      has_opted_out_of_email = false,
      owner_id,
      custom_fields = {},
    } = contactData;

    const client = await beginTransaction();

    try {
      // Set user context for audit logging
      await client.query("SELECT set_config($1, $2, true)", [
        "app.current_user_id",
        userId.toString(),
      ]);

      const result = await client.query(
        `INSERT INTO contacts (
          account_id, first_name, last_name, salutation, title, department,
          phone, mobile_phone, home_phone, other_phone, fax, email,
          mailing_address, other_address, reports_to_id, assistant_name,
          assistant_phone, birthdate, lead_source, description, languages,
          level, do_not_call, has_opted_out_of_email, owner_id, custom_fields,
          created_by_id, last_modified_by_id
        )
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $27)
         RETURNING *`,
        [
          account_id,
          first_name,
          last_name,
          salutation,
          title,
          department,
          phone,
          mobile_phone,
          home_phone,
          other_phone,
          fax,
          email,
          mailing_address ? JSON.stringify(mailing_address) : null,
          other_address ? JSON.stringify(other_address) : null,
          reports_to_id,
          assistant_name,
          assistant_phone,
          birthdate,
          lead_source,
          description,
          languages,
          level,
          do_not_call,
          has_opted_out_of_email,
          owner_id || userId,
          JSON.stringify(custom_fields),
          userId,
        ]
      );

      await commitTransaction(client);

      // Return the created contact with computed fields
      return await this.findById(result.rows[0].id);
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Update contact
  static async update(id, contactData, userId = 1) {
    const client = await beginTransaction();

    try {
      // Set user context for audit logging
      await client.query("SELECT set_config($1, $2, true)", [
        "app.current_user_id",
        userId.toString(),
      ]);

      // Get current contact data for validation
      const currentResult = await client.query(
        "SELECT * FROM contacts WHERE id = $1 AND is_deleted = FALSE",
        [id]
      );
      if (currentResult.rows.length === 0) {
        throw new Error("Contact not found");
      }

      // Build update query dynamically
      const updateFields = [];
      const params = [id];
      let paramCount = 1;

      Object.keys(contactData).forEach((key) => {
        if (contactData[key] !== undefined && key !== "id") {
          paramCount++;
          if (
            key === "mailing_address" ||
            key === "other_address" ||
            key === "custom_fields"
          ) {
            updateFields.push(`${key} = $${paramCount}`);
            params.push(JSON.stringify(contactData[key]));
          } else {
            updateFields.push(`${key} = $${paramCount}`);
            params.push(contactData[key]);
          }
        }
      });

      if (updateFields.length === 0) {
        await rollbackTransaction(client);
        return await this.findById(id);
      }

      // Add last_modified_by_id and updated_at
      paramCount++;
      updateFields.push(`last_modified_by_id = $${paramCount}`);
      params.push(userId);

      paramCount++;
      updateFields.push(`updated_at = $${paramCount}`);
      params.push(new Date());

      const updateQuery = `
        UPDATE contacts 
        SET ${updateFields.join(", ")}
        WHERE id = $1 AND is_deleted = FALSE
        RETURNING *
      `;

      const result = await client.query(updateQuery, params);

      await commitTransaction(client);

      // Return the updated contact with computed fields
      return await this.findById(result.rows[0].id);
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Soft delete contact
  static async delete(id, userId = 1) {
    const client = await beginTransaction();

    try {
      // Set user context for audit logging
      await client.query("SELECT set_config($1, $2, true)", [
        "app.current_user_id",
        userId.toString(),
      ]);

      // Get current contact data
      const currentResult = await client.query(
        "SELECT * FROM contacts WHERE id = $1 AND is_deleted = FALSE",
        [id]
      );
      if (currentResult.rows.length === 0) {
        throw new Error("Contact not found");
      }

      // Check for direct reports
      const reportsResult = await client.query(
        "SELECT COUNT(*) FROM contacts WHERE reports_to_id = $1 AND is_deleted = FALSE",
        [id]
      );
      const reportsCount = parseInt(reportsResult.rows[0].count);

      if (reportsCount > 0) {
        throw new Error(
          "Cannot delete contact with direct reports. Please reassign direct reports first."
        );
      }

      // Soft delete
      const result = await client.query(
        `UPDATE contacts 
         SET is_deleted = TRUE, status = 'dismissed', last_modified_by_id = $2, updated_at = $3
         WHERE id = $1 AND is_deleted = FALSE
         RETURNING id`,
        [id, userId, new Date()]
      );

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Search contacts with advanced filters
  static async search(searchParams) {
    const {
      query: searchQuery,
      account_id,
      department,
      level,
      lead_source,
      status,
      owner_id,
      reports_to_id,
      has_direct_reports,
      created_after,
      created_before,
      page = 1,
      limit = 10,
    } = searchParams;

    return this.findAll(
      {
        search: searchQuery,
        account_id,
        department,
        level,
        lead_source,
        status,
        owner_id,
        reports_to_id,
        has_direct_reports,
        created_after,
        created_before,
      },
      page,
      limit
    );
  }

  // Get contact audit history
  static async getAuditHistory(id) {
    const result = await query(
      `SELECT a.id, a.entity_type, a.entity_id, a.action, a.user_id, 
              a.old_value, a.new_value, a.details, a.timestamp,
              u.email as user_name
       FROM audits a
       LEFT JOIN users u ON a.user_id = u.id
       WHERE a.entity_type = 'contacts' AND a.entity_id = $1
       ORDER BY a.timestamp DESC`,
      [id]
    );
    return result.rows;
  }

  // Get unique departments
  static async getDepartments() {
    const result = await query(
      "SELECT DISTINCT department FROM contacts WHERE department IS NOT NULL AND is_deleted = FALSE ORDER BY department"
    );
    return result.rows.map((row) => row.department);
  }

  // Get unique lead sources
  static async getLeadSources() {
    const result = await query(
      "SELECT DISTINCT lead_source FROM contacts WHERE lead_source IS NOT NULL AND is_deleted = FALSE ORDER BY lead_source"
    );
    return result.rows.map((row) => row.lead_source);
  }

  // Export contacts to CSV format
  static async exportToCSV(filters = {}) {
    const { contacts } = await this.findAll(filters, 1, 10000); // Large limit for export

    const csvHeaders = [
      "ID",
      "Full Name",
      "First Name",
      "Last Name",
      "Title",
      "Department",
      "Email",
      "Phone",
      "Mobile Phone",
      "Account Name",
      "Reports To",
      "Lead Source",
      "Level",
      "Status",
      "Created At",
    ];

    const csvRows = contacts.map((contact) => [
      contact.id,
      contact.full_name,
      contact.first_name || "",
      contact.last_name,
      contact.title || "",
      contact.department || "",
      contact.email,
      contact.phone || "",
      contact.mobile_phone || "",
      contact.account_name || "",
      contact.reports_to_name || "",
      contact.lead_source || "",
      contact.level || "",
      contact.status,
      contact.created_at,
    ]);

    return {
      headers: csvHeaders,
      rows: csvRows,
    };
  }

  // Validate contact data
  static validateContactData(contactData) {
    const errors = [];

    // Required fields
    if (!contactData.account_id) {
      errors.push({ field: "account_id", message: "Account is required" });
    }

    if (!contactData.last_name || contactData.last_name.trim().length === 0) {
      errors.push({ field: "last_name", message: "Last name is required" });
    }

    if (!contactData.email || contactData.email.trim().length === 0) {
      errors.push({ field: "email", message: "Email is required" });
    }

    // Email validation
    if (contactData.email && !this.isValidEmail(contactData.email)) {
      errors.push({ field: "email", message: "Invalid email format" });
    }

    // Length validations
    if (contactData.first_name && contactData.first_name.length > 255) {
      errors.push({
        field: "first_name",
        message: "First name must be less than 255 characters",
      });
    }

    if (contactData.last_name && contactData.last_name.length > 255) {
      errors.push({
        field: "last_name",
        message: "Last name must be less than 255 characters",
      });
    }

    if (contactData.title && contactData.title.length > 255) {
      errors.push({
        field: "title",
        message: "Title must be less than 255 characters",
      });
    }

    if (contactData.department && contactData.department.length > 255) {
      errors.push({
        field: "department",
        message: "Department must be less than 255 characters",
      });
    }

    // Phone number validation (basic)
    const phoneFields = [
      "phone",
      "mobile_phone",
      "home_phone",
      "other_phone",
      "fax",
      "assistant_phone",
    ];
    phoneFields.forEach((field) => {
      if (contactData[field] && contactData[field].length > 50) {
        errors.push({
          field,
          message: `${field.replace("_", " ")} must be less than 50 characters`,
        });
      }
    });

    // Date validation
    if (contactData.birthdate && !this.isValidDate(contactData.birthdate)) {
      errors.push({ field: "birthdate", message: "Invalid birthdate format" });
    }

    return errors;
  }

  // Helper method to validate email
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Helper method to validate dates
  static isValidDate(dateString) {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  }

  // Computed field methods
  static getFullName(firstName, lastName) {
    if (firstName && firstName.trim() !== "") {
      return `${firstName} ${lastName}`;
    }
    return lastName;
  }

  // Hierarchy validation methods
  static async validateHierarchy(contactId, reportsToId) {
    if (!reportsToId || contactId === reportsToId) {
      return {
        isValid: contactId !== reportsToId,
        error:
          contactId === reportsToId
            ? "Contact cannot report to themselves"
            : null,
      };
    }

    // Check for circular references by traversing up the hierarchy
    let currentId = reportsToId;
    let depth = 0;
    const maxDepth = 10;

    while (currentId && depth < maxDepth) {
      if (currentId === contactId) {
        return {
          isValid: false,
          error: "Circular hierarchy reference detected",
        };
      }

      const result = await query(
        "SELECT reports_to_id FROM contacts WHERE id = $1 AND is_deleted = FALSE",
        [currentId]
      );

      if (result.rows.length === 0) {
        break;
      }

      currentId = result.rows[0].reports_to_id;
      depth++;
    }

    if (depth >= maxDepth) {
      return {
        isValid: false,
        error: "Hierarchy depth exceeds maximum allowed levels",
      };
    }

    return { isValid: true, error: null };
  }

  // Get hierarchy path (from root to contact)
  static async getHierarchyPath(contactId) {
    const result = await query(
      `WITH RECURSIVE hierarchy_path AS (
        SELECT id, first_name, last_name, reports_to_id, 0 as level,
               ARRAY[id] as path,
               CASE 
                 WHEN first_name IS NOT NULL AND first_name != '' 
                 THEN first_name || ' ' || last_name 
                 ELSE last_name 
               END as full_name
        FROM contacts 
        WHERE id = $1 AND is_deleted = FALSE
        
        UNION ALL
        
        SELECT c.id, c.first_name, c.last_name, c.reports_to_id, hp.level + 1,
               c.id || hp.path,
               CASE 
                 WHEN c.first_name IS NOT NULL AND c.first_name != '' 
                 THEN c.first_name || ' ' || c.last_name 
                 ELSE c.last_name 
               END as full_name
        FROM contacts c
        INNER JOIN hierarchy_path hp ON c.id = hp.reports_to_id
        WHERE c.is_deleted = FALSE AND hp.level < 5
      )
      SELECT * FROM hierarchy_path ORDER BY level DESC`,
      [contactId]
    );
    return result.rows;
  }

  // Get all contacts in account with hierarchy information
  static async getAccountContacts(accountId) {
    const result = await query(
      `SELECT 
        c.id, c.first_name, c.last_name, c.title, c.department, c.email, c.phone,
        c.reports_to_id,
        CASE 
          WHEN c.first_name IS NOT NULL AND c.first_name != '' 
          THEN c.first_name || ' ' || c.last_name 
          ELSE c.last_name 
        END as full_name,
        (SELECT COUNT(*) FROM contacts sub WHERE sub.reports_to_id = c.id AND sub.is_deleted = FALSE) as direct_reports_count
      FROM contacts c
      WHERE c.account_id = $1 AND c.is_deleted = FALSE
      ORDER BY c.last_name, c.first_name`,
      [accountId]
    );
    return result.rows;
  }

  // Build organizational chart data structure
  static async buildOrgChart(accountId, rootContactId = null) {
    const contacts = await this.getAccountContacts(accountId);

    // If no root specified, find contacts with no manager
    const rootContacts = rootContactId
      ? contacts.filter((c) => c.id === rootContactId)
      : contacts.filter((c) => !c.reports_to_id);

    const buildTree = (parentId = null, level = 0) => {
      return contacts
        .filter((c) => c.reports_to_id === parentId)
        .map((contact) => ({
          ...contact,
          level,
          children: buildTree(contact.id, level + 1),
        }));
    };

    return rootContacts.map((root) => ({
      ...root,
      level: 0,
      children: buildTree(root.id, 1),
    }));
  }

  // Get contact statistics
  static async getContactStats(accountId = null) {
    let whereClause = "WHERE c.is_deleted = FALSE";
    const params = [];

    if (accountId) {
      whereClause += " AND c.account_id = $1";
      params.push(accountId);
    }

    const result = await query(
      `SELECT 
        COUNT(*) as total_contacts,
        COUNT(CASE WHEN c.status = 'open' THEN 1 END) as active_contacts,
        COUNT(CASE WHEN c.reports_to_id IS NULL THEN 1 END) as top_level_contacts,
        COUNT(CASE WHEN c.do_not_call = true THEN 1 END) as do_not_call_contacts,
        COUNT(CASE WHEN c.has_opted_out_of_email = true THEN 1 END) as opted_out_contacts,
        COUNT(DISTINCT c.department) as unique_departments,
        COUNT(DISTINCT c.account_id) as unique_accounts
      FROM contacts c
      ${whereClause}`,
      params
    );

    return result.rows[0];
  }

  // Find contacts by email domain
  static async findByEmailDomain(domain) {
    const result = await query(
      `SELECT 
        c.id, c.first_name, c.last_name, c.email, c.title, c.department,
        a.name as account_name,
        CASE 
          WHEN c.first_name IS NOT NULL AND c.first_name != '' 
          THEN c.first_name || ' ' || c.last_name 
          ELSE c.last_name 
        END as full_name
      FROM contacts c
      LEFT JOIN accounts a ON c.account_id = a.id
      WHERE c.email ILIKE $1 AND c.is_deleted = FALSE
      ORDER BY c.last_name, c.first_name`,
      [`%@${domain}`]
    );
    return result.rows;
  }

  // Find potential duplicate contacts
  static async findPotentialDuplicates(
    email = null,
    firstName = null,
    lastName = null,
    accountId = null
  ) {
    let whereConditions = ["c.is_deleted = FALSE"];
    const params = [];
    let paramCount = 0;

    if (email) {
      paramCount++;
      whereConditions.push(`c.email = $${paramCount}`);
      params.push(email);
    }

    if (firstName && lastName) {
      paramCount++;
      whereConditions.push(
        `(c.first_name ILIKE $${paramCount} AND c.last_name ILIKE $${
          paramCount + 1
        })`
      );
      params.push(firstName, lastName);
      paramCount++;
    }

    if (accountId) {
      paramCount++;
      whereConditions.push(`c.account_id = $${paramCount}`);
      params.push(accountId);
    }

    const whereClause = whereConditions.join(" AND ");

    const result = await query(
      `SELECT 
        c.id, c.first_name, c.last_name, c.email, c.title, c.department,
        c.account_id, a.name as account_name,
        CASE 
          WHEN c.first_name IS NOT NULL AND c.first_name != '' 
          THEN c.first_name || ' ' || c.last_name 
          ELSE c.last_name 
        END as full_name,
        c.created_at
      FROM contacts c
      LEFT JOIN accounts a ON c.account_id = a.id
      WHERE ${whereClause}
      ORDER BY c.created_at DESC`,
      params
    );

    return result.rows;
  }

  // Merge contacts (combine data from duplicate contacts)
  static async mergeContacts(
    primaryContactId,
    duplicateContactIds,
    userId = 1
  ) {
    const client = await beginTransaction();

    try {
      // Set user context for audit logging
      await client.query("SELECT set_config($1, $2, true)", [
        "app.current_user_id",
        userId.toString(),
      ]);

      // Get primary contact
      const primaryResult = await client.query(
        "SELECT * FROM contacts WHERE id = $1 AND is_deleted = FALSE",
        [primaryContactId]
      );
      if (primaryResult.rows.length === 0) {
        throw new Error("Primary contact not found");
      }

      // Update any contacts that report to the duplicates to report to primary
      for (const duplicateId of duplicateContactIds) {
        await client.query(
          "UPDATE contacts SET reports_to_id = $1 WHERE reports_to_id = $2 AND is_deleted = FALSE",
          [primaryContactId, duplicateId]
        );

        // Soft delete the duplicate
        await client.query(
          "UPDATE contacts SET is_deleted = TRUE, last_modified_by_id = $2, updated_at = $3 WHERE id = $1",
          [duplicateId, userId, new Date()]
        );
      }

      await commitTransaction(client);
      return await this.findById(primaryContactId);
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Check email uniqueness within tenant/account scope
  static async checkEmailUniqueness(
    email,
    accountId = null,
    excludeContactId = null
  ) {
    let whereClause = "WHERE email = $1 AND is_deleted = FALSE";
    const params = [email];
    let paramCount = 1;

    if (accountId) {
      paramCount++;
      whereClause += ` AND account_id = $${paramCount}`;
      params.push(accountId);
    }

    if (excludeContactId) {
      paramCount++;
      whereClause += ` AND id != $${paramCount}`;
      params.push(excludeContactId);
    }

    const result = await query(
      `SELECT id, first_name, last_name, email FROM contacts ${whereClause} LIMIT 1`,
      params
    );

    return {
      isUnique: result.rows.length === 0,
      existingContact: result.rows[0] || null,
    };
  }
}

module.exports = Contact;
