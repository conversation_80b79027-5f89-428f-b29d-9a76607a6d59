const { query, queryWithUser, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');

class Payment {
  // Create a new payment record
  static async create(paymentData, userId) {
    const client = await beginTransaction();
    try {
      const {
        invoiceId,
        amount,
        currency = 'USD',
        gateway,
        transactionId,
        status = 'pending',
        partialAmounts = null,
        conversionRate = null
      } = paymentData;

      // Validate required fields
      if (!invoiceId || !amount || !gateway || !transactionId) {
        throw new Error('Missing required fields: invoiceId, amount, gateway, and transactionId are required');
      }

      // Check if invoice exists
      const invoiceResult = await queryWithUser(client, 'SELECT * FROM invoices WHERE id = $1', [invoiceId], userId);
      if (invoiceResult.rows.length === 0) {
        throw new Error('Invoice not found');
      }

      const invoice = invoiceResult.rows[0];

      // Validate payment amount doesn't exceed invoice amount
      const existingPaymentsResult = await queryWithUser(client, `
        SELECT COALESCE(SUM(amount), 0) as total_paid
        FROM payments 
        WHERE invoice_id = $1 AND status = 'completed'
      `, [invoiceId], userId);

      const totalPaid = parseFloat(existingPaymentsResult.rows[0].total_paid);
      const invoiceAmount = parseFloat(invoice.amount);
      const newPaymentAmount = parseFloat(amount);

      if (totalPaid + newPaymentAmount > invoiceAmount + 0.01) { // Allow small rounding differences
        throw new Error('Payment amount exceeds remaining invoice balance');
      }

      const result = await queryWithUser(client, `
        INSERT INTO payments (
          invoice_id, amount, currency, gateway, transaction_id, status, 
          partial_amounts, conversion_rate, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
        RETURNING *
      `, [invoiceId, amount, currency, gateway, transactionId, status, 
          partialAmounts ? JSON.stringify(partialAmounts) : null, conversionRate], userId);

      // Log audit trail
      await queryWithUser(client, `
        INSERT INTO audits (entity_type, entity_id, action, user_id, new_value, timestamp)
        VALUES ('payments', $1, 'create', $2, $3, CURRENT_TIMESTAMP)
      `, [result.rows[0].id, userId, JSON.stringify(result.rows[0])]);

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Update payment status (typically called by webhooks)
  static async updateStatus(paymentId, status, additionalData = {}, userId = null) {
    const client = await beginTransaction();
    try {
      // Get current payment
      const currentResult = await query('SELECT * FROM payments WHERE id = $1', [paymentId]);
      if (currentResult.rows.length === 0) {
        throw new Error('Payment not found');
      }

      const currentPayment = currentResult.rows[0];

      // Update payment status
      const updateFields = ['status = $2', 'updated_at = CURRENT_TIMESTAMP'];
      const updateValues = [paymentId, status];
      let paramIndex = 3;

      if (status === 'completed' && !currentPayment.paid_at) {
        updateFields.push(`paid_at = $${paramIndex}`);
        updateValues.push(new Date());
        paramIndex++;
      }

      if (additionalData.conversionRate) {
        updateFields.push(`conversion_rate = $${paramIndex}`);
        updateValues.push(additionalData.conversionRate);
        paramIndex++;
      }

      if (additionalData.partialAmounts) {
        updateFields.push(`partial_amounts = $${paramIndex}`);
        updateValues.push(JSON.stringify(additionalData.partialAmounts));
        paramIndex++;
      }

      const result = await query(`
        UPDATE payments 
        SET ${updateFields.join(', ')}
        WHERE id = $1
        RETURNING *
      `, updateValues);

      const updatedPayment = result.rows[0];

      // Update invoice status based on payment status
      if (status === 'completed') {
        await this.updateInvoiceStatus(updatedPayment.invoice_id, userId);
      }

      // Log audit trail
      if (userId) {
        await query(`
          INSERT INTO audits (entity_type, entity_id, action, user_id, old_value, new_value, timestamp)
          VALUES ('payments', $1, 'update', $2, $3, $4, CURRENT_TIMESTAMP)
        `, [paymentId, userId, JSON.stringify(currentPayment), JSON.stringify(updatedPayment)]);
      }

      await commitTransaction(client);
      return updatedPayment;
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Update invoice status based on payment completion
  static async updateInvoiceStatus(invoiceId, userId = null) {
    try {
      // Get invoice and total payments
      const invoiceResult = await query('SELECT * FROM invoices WHERE id = $1', [invoiceId]);
      if (invoiceResult.rows.length === 0) {
        throw new Error('Invoice not found');
      }

      const invoice = invoiceResult.rows[0];
      const invoiceAmount = parseFloat(invoice.amount);

      // Calculate total completed payments
      const paymentsResult = await query(`
        SELECT COALESCE(SUM(amount), 0) as total_paid
        FROM payments 
        WHERE invoice_id = $1 AND status = 'completed'
      `, [invoiceId]);

      const totalPaid = parseFloat(paymentsResult.rows[0].total_paid);

      // Determine new invoice status
      let newStatus;
      if (totalPaid >= invoiceAmount - 0.01) { // Allow small rounding differences
        newStatus = 'paid';
      } else if (totalPaid > 0) {
        newStatus = 'partially_paid';
      } else {
        return; // No status change needed
      }

      // Update invoice status
      await query(`
        UPDATE invoices 
        SET status = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [newStatus, invoiceId]);

      // Log audit trail if userId provided
      if (userId) {
        await query(`
          INSERT INTO audits (entity_type, entity_id, action, user_id, new_value, timestamp)
          VALUES ('invoices', $1, 'update', $2, $3, CURRENT_TIMESTAMP)
        `, [invoiceId, userId, JSON.stringify({ status: newStatus, reason: 'Payment completed' })]);
      }

    } catch (error) {
      console.error('Error updating invoice status:', error);
      throw error;
    }
  }

  // Get payments for an invoice
  static async findByInvoiceId(invoiceId) {
    const result = await query(`
      SELECT p.*, i.amount as invoice_amount
      FROM payments p
      LEFT JOIN invoices i ON p.invoice_id = i.id
      WHERE p.invoice_id = $1
      ORDER BY p.created_at DESC
    `, [invoiceId]);

    return result.rows;
  }

  // Get payment by transaction ID
  static async findByTransactionId(transactionId) {
    const result = await query(`
      SELECT p.*, i.amount as invoice_amount, i.vendor_id
      FROM payments p
      LEFT JOIN invoices i ON p.invoice_id = i.id
      WHERE p.transaction_id = $1
    `, [transactionId]);

    return result.rows[0] || null;
  }

  // Get payment statistics
  static async getStatistics(filters = {}) {
    const { startDate, endDate, gateway, status } = filters;

    let whereConditions = [];
    let queryParams = [];
    let paramIndex = 1;

    if (startDate) {
      whereConditions.push(`created_at >= $${paramIndex}`);
      queryParams.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      whereConditions.push(`created_at <= $${paramIndex}`);
      queryParams.push(endDate);
      paramIndex++;
    }

    if (gateway) {
      whereConditions.push(`gateway = $${paramIndex}`);
      queryParams.push(gateway);
      paramIndex++;
    }

    if (status) {
      whereConditions.push(`status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    const result = await query(`
      SELECT 
        COUNT(*) as total_payments,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
        COUNT(CASE WHEN status = 'refunded' THEN 1 END) as refunded_count,
        COALESCE(SUM(amount), 0) as total_amount,
        COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as completed_amount,
        COALESCE(AVG(amount), 0) as average_amount,
        COUNT(CASE WHEN gateway = 'stripe' THEN 1 END) as stripe_count,
        COUNT(CASE WHEN gateway = 'paypal' THEN 1 END) as paypal_count
      FROM payments
      ${whereClause}
    `, queryParams);

    return result.rows[0];
  }

  // Process refund
  static async processRefund(paymentId, refundAmount, reason, userId) {
    const client = await beginTransaction();
    try {
      // Get payment details
      const paymentResult = await queryWithUser(client, 'SELECT * FROM payments WHERE id = $1', [paymentId], userId);
      if (paymentResult.rows.length === 0) {
        throw new Error('Payment not found');
      }

      const payment = paymentResult.rows[0];

      if (payment.status !== 'completed') {
        throw new Error('Can only refund completed payments');
      }

      if (refundAmount > payment.amount) {
        throw new Error('Refund amount cannot exceed payment amount');
      }

      // Create refund record
      const refundResult = await queryWithUser(client, `
        INSERT INTO payments (
          invoice_id, amount, currency, gateway, transaction_id, status, created_at
        ) VALUES ($1, $2, $3, $4, $5, 'refunded', CURRENT_TIMESTAMP)
        RETURNING *
      `, [payment.invoice_id, -refundAmount, payment.currency, payment.gateway, 
          `refund_${payment.transaction_id}_${Date.now()}`, userId]);

      // Update original payment status if full refund
      if (refundAmount >= payment.amount) {
        await queryWithUser(client, `
          UPDATE payments 
          SET status = 'refunded', updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
        `, [paymentId], userId);
      }

      // Update invoice status
      await this.updateInvoiceStatus(payment.invoice_id, userId);

      // Log audit trail
      await queryWithUser(client, `
        INSERT INTO audits (entity_type, entity_id, action, user_id, new_value, timestamp)
        VALUES ('payments', $1, 'refund', $2, $3, CURRENT_TIMESTAMP)
      `, [paymentId, userId, JSON.stringify({ 
        refund_amount: refundAmount, 
        reason: reason,
        refund_payment_id: refundResult.rows[0].id 
      })]);

      await commitTransaction(client);
      return refundResult.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Get all payments with filtering
  static async findAll(filters = {}, pagination = {}) {
    const { page = 1, limit = 10, search, status, gateway, invoiceId, startDate, endDate } = filters;
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let queryParams = [];
    let paramIndex = 1;

    if (search) {
      whereConditions.push(`(
        p.transaction_id ILIKE $${paramIndex} OR 
        v.name ILIKE $${paramIndex} OR
        p.id::text ILIKE $${paramIndex}
      )`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (status) {
      whereConditions.push(`p.status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    if (gateway) {
      whereConditions.push(`p.gateway = $${paramIndex}`);
      queryParams.push(gateway);
      paramIndex++;
    }

    if (invoiceId) {
      whereConditions.push(`p.invoice_id = $${paramIndex}`);
      queryParams.push(invoiceId);
      paramIndex++;
    }

    if (startDate) {
      whereConditions.push(`p.created_at >= $${paramIndex}`);
      queryParams.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      whereConditions.push(`p.created_at <= $${paramIndex}`);
      queryParams.push(endDate);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM payments p
      LEFT JOIN invoices i ON p.invoice_id = i.id
      LEFT JOIN vendors v ON i.vendor_id = v.id
      ${whereClause}
    `, queryParams);

    // Get payments
    const result = await query(`
      SELECT 
        p.*,
        i.amount as invoice_amount,
        v.name as vendor_name,
        v.contact_email as vendor_email,
        v.contact_phone as vendor_phone
      FROM payments p
      LEFT JOIN invoices i ON p.invoice_id = i.id
      LEFT JOIN vendors v ON i.vendor_id = v.id
      ${whereClause}
      ORDER BY p.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `, [...queryParams, limit, offset]);

    return {
      payments: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      }
    };
  }
}

module.exports = Payment;