const { query, queryWithUser, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');
const Joi = require('joi');

class QuoteInteraction {
  // Validation schemas
  static getValidationSchema() {
    return {
      create: Joi.object({
        quote_id: Joi.number().integer().positive().required(),
        interaction_type: Joi.string().valid(
          'viewed', 'downloaded', 'shared', 'approved', 'rejected', 
          'requested_changes', 'commented', 'email_opened', 'link_clicked'
        ).required(),
        client_email: Joi.string().email().allow(null),
        client_name: Joi.string().max(255).allow(null),
        ip_address: Joi.string().ip().allow(null),
        user_agent: Joi.string().max(1000).allow(null),
        metadata: Joi.object().allow(null),
        notes: Joi.string().max(2000).allow('')
      }),

      update: Joi.object({
        notes: Joi.string().max(2000).allow(''),
        metadata: Joi.object().allow(null)
      }).min(1)
    };
  }

  // Validate interaction data
  static validateCreate(data) {
    return this.getValidationSchema().create.validate(data, { abortEarly: false });
  }

  static validateUpdate(data) {
    return this.getValidationSchema().update.validate(data, { abortEarly: false });
  }

  // Get interactions for a quote
  static async findByQuoteId(quoteId, filters = {}) {
    try {
      let whereClause = 'WHERE qi.quote_id = $1';
      const params = [quoteId];
      let paramCount = 1;

      // Apply filters
      if (filters.interaction_type) {
        paramCount++;
        whereClause += ` AND qi.interaction_type = $${paramCount}`;
        params.push(filters.interaction_type);
      }

      if (filters.client_email) {
        paramCount++;
        whereClause += ` AND qi.client_email = $${paramCount}`;
        params.push(filters.client_email);
      }

      if (filters.date_from) {
        paramCount++;
        whereClause += ` AND qi.created_at >= $${paramCount}`;
        params.push(filters.date_from);
      }

      if (filters.date_to) {
        paramCount++;
        whereClause += ` AND qi.created_at <= $${paramCount}`;
        params.push(filters.date_to);
      }

      const interactionsQuery = `
        SELECT 
          qi.*,
          cq.title as quote_title,
          cq.public_token as quote_token
        FROM quote_interactions qi
        LEFT JOIN client_quotes cq ON qi.quote_id = cq.id
        ${whereClause}
        ORDER BY qi.created_at DESC
      `;

      const result = await query(interactionsQuery, params);
      return result.rows;
    } catch (error) {
      console.error('Error in QuoteInteraction.findByQuoteId:', error);
      throw error;
    }
  }

  // Get interaction by ID
  static async findById(id) {
    try {
      const interactionQuery = `
        SELECT 
          qi.*,
          cq.title as quote_title,
          cq.public_token as quote_token,
          cq.rfq_id
        FROM quote_interactions qi
        LEFT JOIN client_quotes cq ON qi.quote_id = cq.id
        WHERE qi.id = $1
      `;

      const result = await query(interactionQuery, [id]);
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error in QuoteInteraction.findById:', error);
      throw error;
    }
  }

  // Create new interaction
  static async create(interactionData) {
    try {
      // Validate input data
      const { error, value } = this.validateCreate(interactionData);
      if (error) {
        throw new Error(`Validation error: ${error.details.map(d => d.message).join(', ')}`);
      }

      const {
        quote_id,
        interaction_type,
        client_email,
        client_name,
        ip_address,
        user_agent,
        metadata,
        notes
      } = value;

      // Verify quote exists
      const quoteCheck = await query(
        'SELECT id FROM client_quotes WHERE id = $1',
        [quote_id]
      );

      if (quoteCheck.rows.length === 0) {
        throw new Error('Quote not found');
      }

      // Create interaction
      const interactionQuery = `
        INSERT INTO quote_interactions (
          quote_id, interaction_type, client_email, client_name,
          ip_address, user_agent, metadata, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;

      const interactionParams = [
        quote_id,
        interaction_type,
        client_email,
        client_name,
        ip_address,
        user_agent,
        metadata ? JSON.stringify(metadata) : null,
        notes || ''
      ];

      const result = await query(interactionQuery, interactionParams);
      const interaction = result.rows[0];

      // Update quote's last interaction timestamp
      await query(
        'UPDATE client_quotes SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
        [quote_id]
      );

      return await this.findById(interaction.id);
    } catch (error) {
      console.error('Error in QuoteInteraction.create:', error);
      throw error;
    }
  }

  // Update interaction
  static async update(id, updates) {
    try {
      // Validate input data
      const { error, value } = this.validateUpdate(updates);
      if (error) {
        throw new Error(`Validation error: ${error.details.map(d => d.message).join(', ')}`);
      }

      // Check if interaction exists
      const existingInteraction = await query(
        'SELECT * FROM quote_interactions WHERE id = $1',
        [id]
      );

      if (existingInteraction.rows.length === 0) {
        throw new Error('Interaction not found');
      }

      // Build dynamic update query
      const updateFields = [];
      const params = [id];
      let paramCount = 1;

      const allowedFields = ['notes', 'metadata'];

      for (const field of allowedFields) {
        if (value[field] !== undefined) {
          paramCount++;
          if (field === 'metadata') {
            updateFields.push(`${field} = $${paramCount}`);
            params.push(value[field] ? JSON.stringify(value[field]) : null);
          } else {
            updateFields.push(`${field} = $${paramCount}`);
            params.push(value[field]);
          }
        }
      }

      if (updateFields.length === 0) {
        return existingInteraction.rows[0];
      }

      // Add updated_at
      updateFields.push('updated_at = CURRENT_TIMESTAMP');

      const updateQuery = `
        UPDATE quote_interactions 
        SET ${updateFields.join(', ')}
        WHERE id = $1
        RETURNING *
      `;

      const result = await query(updateQuery, params);
      return await this.findById(id);
    } catch (error) {
      console.error('Error in QuoteInteraction.update:', error);
      throw error;
    }
  }

  // Get interaction statistics for a quote
  static async getQuoteStats(quoteId) {
    try {
      const statsQuery = `
        SELECT 
          interaction_type,
          COUNT(*) as count,
          COUNT(DISTINCT client_email) as unique_clients,
          MIN(created_at) as first_interaction,
          MAX(created_at) as last_interaction
        FROM quote_interactions 
        WHERE quote_id = $1
        GROUP BY interaction_type
        ORDER BY interaction_type
      `;

      const result = await query(statsQuery, [quoteId]);
      
      // Get total stats
      const totalQuery = `
        SELECT 
          COUNT(*) as total_interactions,
          COUNT(DISTINCT client_email) as total_unique_clients,
          MIN(created_at) as first_interaction,
          MAX(created_at) as last_interaction
        FROM quote_interactions 
        WHERE quote_id = $1
      `;

      const totalResult = await query(totalQuery, [quoteId]);

      return {
        by_type: result.rows,
        totals: totalResult.rows[0]
      };
    } catch (error) {
      console.error('Error in QuoteInteraction.getQuoteStats:', error);
      throw error;
    }
  }

  // Get interaction timeline for a quote
  static async getQuoteTimeline(quoteId, limit = 50) {
    try {
      const timelineQuery = `
        SELECT 
          qi.*,
          CASE 
            WHEN qi.client_email IS NOT NULL THEN qi.client_email
            WHEN qi.client_name IS NOT NULL THEN qi.client_name
            ELSE 'Anonymous'
          END as actor_name
        FROM quote_interactions qi
        WHERE qi.quote_id = $1
        ORDER BY qi.created_at DESC
        LIMIT $2
      `;

      const result = await query(timelineQuery, [quoteId, limit]);
      return result.rows;
    } catch (error) {
      console.error('Error in QuoteInteraction.getQuoteTimeline:', error);
      throw error;
    }
  }

  // Track quote view (convenience method)
  static async trackView(quoteId, clientInfo = {}) {
    return await this.create({
      quote_id: quoteId,
      interaction_type: 'viewed',
      client_email: clientInfo.email || null,
      client_name: clientInfo.name || null,
      ip_address: clientInfo.ip_address || null,
      user_agent: clientInfo.user_agent || null,
      metadata: clientInfo.metadata || null
    });
  }

  // Track quote download (convenience method)
  static async trackDownload(quoteId, clientInfo = {}) {
    return await this.create({
      quote_id: quoteId,
      interaction_type: 'downloaded',
      client_email: clientInfo.email || null,
      client_name: clientInfo.name || null,
      ip_address: clientInfo.ip_address || null,
      user_agent: clientInfo.user_agent || null,
      metadata: clientInfo.metadata || null
    });
  }

  // Track quote approval (convenience method)
  static async trackApproval(quoteId, clientInfo = {}, notes = '') {
    return await this.create({
      quote_id: quoteId,
      interaction_type: 'approved',
      client_email: clientInfo.email || null,
      client_name: clientInfo.name || null,
      ip_address: clientInfo.ip_address || null,
      user_agent: clientInfo.user_agent || null,
      metadata: clientInfo.metadata || null,
      notes: notes
    });
  }

  // Track quote rejection (convenience method)
  static async trackRejection(quoteId, clientInfo = {}, notes = '') {
    return await this.create({
      quote_id: quoteId,
      interaction_type: 'rejected',
      client_email: clientInfo.email || null,
      client_name: clientInfo.name || null,
      ip_address: clientInfo.ip_address || null,
      user_agent: clientInfo.user_agent || null,
      metadata: clientInfo.metadata || null,
      notes: notes
    });
  }

  // Delete interaction
  static async delete(id) {
    try {
      const result = await query(
        'DELETE FROM quote_interactions WHERE id = $1 RETURNING id',
        [id]
      );

      if (result.rows.length === 0) {
        throw new Error('Interaction not found');
      }

      return result.rows[0];
    } catch (error) {
      console.error('Error in QuoteInteraction.delete:', error);
      throw error;
    }
  }
}

module.exports = QuoteInteraction;