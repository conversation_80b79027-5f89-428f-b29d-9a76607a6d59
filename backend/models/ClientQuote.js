const { query, queryWithUser, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');
const Joi = require('joi');

class ClientQuote {
  // Validation schemas
  static getValidationSchema() {
    return {
      create: Joi.object({
        rfq_id: Joi.number().integer().positive().required(),
        title: Joi.string().min(3).max(255).required(),
        selectedBids: Joi.object().pattern(
          Joi.string(), // item_id
          Joi.object({
            submissionId: Joi.number().integer().positive().required(),
            vendorId: Joi.number().integer().positive().required(),
            unitPrice: Joi.number().min(0).precision(2).required(),
            totalPrice: Joi.number().min(0).precision(2).required(),
            rationale: Joi.string().max(1000).allow(''),
            overrideAI: Joi.boolean().default(false)
          })
        ).min(1).required(),
        // New enhanced selection structure
        item_selections: Joi.object().pattern(
          Joi.string(), // item_id
          Joi.object({
            submissionId: Joi.number().integer().positive().required(),
            vendorId: Joi.number().integer().positive().required(),
            itemData: Joi.object().required(),
            pricing: Joi.object({
              unitPrice: Joi.number().min(0).precision(2).required(),
              quantity: Joi.number().min(0).required(),
              totalPrice: Joi.number().min(0).precision(2).required(),
              originalPrice: Joi.number().min(0).precision(2).required()
            }).required(),
            rationale: Joi.string().max(1000).allow(''),
            overrideAI: Joi.boolean().default(false)
          })
        ).optional(),
        // Commission structure
        commission_structure: Joi.object({
          global_rate: Joi.number().min(0).max(100).precision(2).default(0),
          item_overrides: Joi.object().pattern(
            Joi.string(), // item_id
            Joi.object({
              rate: Joi.number().min(0).max(100).precision(2).required(),
              type: Joi.string().valid('percentage', 'fixed').default('percentage'),
              amount: Joi.number().min(0).precision(2).optional()
            })
          ).optional()
        }).optional(),
        quote_template_id: Joi.number().integer().positive().optional(),
        parent_quote_id: Joi.number().integer().positive().optional(),
        currency: Joi.string().valid('USD', 'EUR', 'GBP', 'JPY', 'CNY').default('USD'),
        margin_percentage: Joi.number().min(0).max(100).precision(2).default(0),
        taxes: Joi.number().min(0).precision(2).default(0),
        terms: Joi.string().max(10000).allow(''),
        notes: Joi.string().max(5000).allow(''),
        client_email: Joi.string().email().required(),
        client_name: Joi.string().min(1).max(255).required(),
        expires_in_days: Joi.number().integer().min(1).max(365).default(30)
      }),

      update: Joi.object({
        title: Joi.string().min(3).max(255),
        selectedBids: Joi.object().pattern(
          Joi.string(),
          Joi.object({
            submissionId: Joi.number().integer().positive().required(),
            vendorId: Joi.number().integer().positive().required(),
            unitPrice: Joi.number().min(0).precision(2).required(),
            totalPrice: Joi.number().min(0).precision(2).required(),
            rationale: Joi.string().max(1000).allow(''),
            overrideAI: Joi.boolean().default(false)
          })
        ).min(1),
        // New enhanced selection structure
        item_selections: Joi.object().pattern(
          Joi.string(),
          Joi.object({
            submissionId: Joi.number().integer().positive().required(),
            vendorId: Joi.number().integer().positive().required(),
            itemData: Joi.object().required(),
            pricing: Joi.object({
              unitPrice: Joi.number().min(0).precision(2).required(),
              quantity: Joi.number().min(0).required(),
              totalPrice: Joi.number().min(0).precision(2).required(),
              originalPrice: Joi.number().min(0).precision(2).required()
            }).required(),
            rationale: Joi.string().max(1000).allow(''),
            overrideAI: Joi.boolean().default(false)
          })
        ),
        // Commission structure
        commission_structure: Joi.object({
          global_rate: Joi.number().min(0).max(100).precision(2),
          item_overrides: Joi.object().pattern(
            Joi.string(),
            Joi.object({
              rate: Joi.number().min(0).max(100).precision(2).required(),
              type: Joi.string().valid('percentage', 'fixed').default('percentage'),
              amount: Joi.number().min(0).precision(2).optional()
            })
          )
        }),
        quote_template_id: Joi.number().integer().positive(),
        margin_percentage: Joi.number().min(0).max(100).precision(2),
        taxes: Joi.number().min(0).precision(2),
        terms: Joi.string().max(10000).allow(''),
        notes: Joi.string().max(5000).allow(''),
        client_email: Joi.string().email(),
        client_name: Joi.string().min(1).max(255),
        expires_in_days: Joi.number().integer().min(1).max(365),
        status: Joi.string().valid('draft', 'sent', 'viewed', 'approved', 'rejected', 'expired')
      }).min(1),

      approve: Joi.object({
        approved_by_name: Joi.string().min(1).max(255).required(),
        approved_by_email: Joi.string().email().required(),
        approval_signature: Joi.string().max(10000).allow(''),
        feedback: Joi.string().max(5000).allow('')
      })
    };
  }

  // Validate quote data
  static validateCreate(data) {
    return this.getValidationSchema().create.validate(data, { abortEarly: false });
  }

  static validateUpdate(data) {
    return this.getValidationSchema().update.validate(data, { abortEarly: false });
  }

  static validateApprove(data) {
    return this.getValidationSchema().approve.validate(data, { abortEarly: false });
  }

  // Get all quotes with filtering and pagination
  static async findAll(filters = {}, page = 1, limit = 10, userId) {
    try {
      let whereClause = 'WHERE 1=1';
      const params = [];
      let paramCount = 0;

      // Apply filters
      if (filters.search) {
        paramCount++;
        whereClause += ` AND (cq.title ILIKE $${paramCount} OR cq.client_name ILIKE $${paramCount} OR cq.client_email ILIKE $${paramCount})`;
        params.push(`%${filters.search}%`);
      }

      if (filters.status) {
        paramCount++;
        whereClause += ` AND cq.status = $${paramCount}`;
        params.push(filters.status);
      }

      if (filters.creator_id) {
        paramCount++;
        whereClause += ` AND cq.creator_id = $${paramCount}`;
        params.push(filters.creator_id);
      }

      if (filters.rfq_id) {
        paramCount++;
        whereClause += ` AND cq.rfq_id = $${paramCount}`;
        params.push(filters.rfq_id);
      }

      if (filters.client_email) {
        paramCount++;
        whereClause += ` AND cq.client_email ILIKE $${paramCount}`;
        params.push(`%${filters.client_email}%`);
      }

      if (filters.expires_from) {
        paramCount++;
        whereClause += ` AND cq.expires_at >= $${paramCount}`;
        params.push(filters.expires_from);
      }

      if (filters.expires_to) {
        paramCount++;
        whereClause += ` AND cq.expires_at <= $${paramCount}`;
        params.push(filters.expires_to);
      }

      // Count total records
      const countQuery = `
        SELECT COUNT(*) as total
        FROM client_quotes cq
        LEFT JOIN users u ON cq.creator_id = u.id
        LEFT JOIN rfqs r ON cq.rfq_id = r.id
        ${whereClause}
      `;
      const countResult = await query(countQuery, params);
      const total = parseInt(countResult.rows[0].total);

      // Calculate pagination
      const offset = (page - 1) * limit;
      const totalPages = Math.ceil(total / limit);

      // Fetch quotes with related information
      paramCount++;
      const limitParam = paramCount;
      paramCount++;
      const offsetParam = paramCount;
      
      const quotesQuery = `
        SELECT 
          cq.*,
          u.email as creator_email,
          r.title as rfq_title,
          r.due_date as rfq_due_date,
          CASE 
            WHEN cq.expires_at < CURRENT_TIMESTAMP AND cq.status NOT IN ('approved', 'rejected')
            THEN true 
            ELSE false 
          END as is_expired
        FROM client_quotes cq
        LEFT JOIN users u ON cq.creator_id = u.id
        LEFT JOIN rfqs r ON cq.rfq_id = r.id
        ${whereClause}
        ORDER BY cq.created_at DESC
        LIMIT $${limitParam} OFFSET $${offsetParam}
      `;

      params.push(limit, offset);
      const result = await query(quotesQuery, params);

      return {
        quotes: result.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      };
    } catch (error) {
      console.error('Error in ClientQuote.findAll:', error);
      throw error;
    }
  }

  // Find quote by ID with detailed information
  static async findById(id, userId = null) {
    try {
      const quoteQuery = `
        SELECT 
          cq.*,
          u.email as creator_email,
          r.title as rfq_title,
          r.description as rfq_description,
          r.items as rfq_items,
          r.due_date as rfq_due_date,
          r.terms as rfq_terms,
          CASE 
            WHEN cq.expires_at < CURRENT_TIMESTAMP AND cq.status NOT IN ('approved', 'rejected')
            THEN true 
            ELSE false 
          END as is_expired
        FROM client_quotes cq
        LEFT JOIN users u ON cq.creator_id = u.id
        LEFT JOIN rfqs r ON cq.rfq_id = r.id
        WHERE cq.id = $1
      `;

      const result = await query(quoteQuery, [id]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const quote = result.rows[0];

      // Check access permissions if userId provided
      if (userId && quote.creator_id !== userId) {
        throw new Error('Access denied');
      }

      // Get selected vendor details for each bid
      const selectedBids = quote.selected_bids || {};
      const vendorIds = Object.values(selectedBids).map(bid => bid.vendorId);
      
      if (vendorIds.length > 0) {
        const vendorsQuery = `
          SELECT id, name, contact_email, performance_score
          FROM vendors 
          WHERE id = ANY($1)
        `;
        const vendorsResult = await query(vendorsQuery, [vendorIds]);
        
        // Add vendor details to selected bids
        quote.vendor_details = vendorsResult.rows.reduce((acc, vendor) => {
          acc[vendor.id] = vendor;
          return acc;
        }, {});
      }

      return quote;
    } catch (error) {
      console.error('Error in ClientQuote.findById:', error);
      throw error;
    }
  }

  // Find quote by public token (for client access)
  static async findByToken(token) {
    try {
      const quoteQuery = `
        SELECT 
          cq.*,
          u.email as creator_email,
          r.title as rfq_title,
          r.description as rfq_description,
          r.items as rfq_items,
          r.terms as rfq_terms,
          CASE 
            WHEN cq.expires_at < CURRENT_TIMESTAMP AND cq.status NOT IN ('approved', 'rejected')
            THEN true 
            ELSE false 
          END as is_expired
        FROM client_quotes cq
        LEFT JOIN users u ON cq.creator_id = u.id
        LEFT JOIN rfqs r ON cq.rfq_id = r.id
        WHERE cq.public_token = $1
      `;

      const result = await query(quoteQuery, [token]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const quote = result.rows[0];

      // Update viewed_at if not already viewed
      if (!quote.viewed_at && quote.status === 'sent') {
        await query(
          `UPDATE client_quotes 
           SET viewed_at = CURRENT_TIMESTAMP, status = 'viewed', updated_at = CURRENT_TIMESTAMP
           WHERE id = $1`,
          [quote.id]
        );
        quote.viewed_at = new Date();
        quote.status = 'viewed';
      }

      // Get selected vendor details
      const selectedBids = quote.selected_bids || {};
      const vendorIds = Object.values(selectedBids).map(bid => bid.vendorId);
      
      if (vendorIds.length > 0) {
        const vendorsQuery = `
          SELECT id, name, contact_email, performance_score
          FROM vendors 
          WHERE id = ANY($1)
        `;
        const vendorsResult = await query(vendorsQuery, [vendorIds]);
        
        quote.vendor_details = vendorsResult.rows.reduce((acc, vendor) => {
          acc[vendor.id] = vendor;
          return acc;
        }, {});
      }

      return quote;
    } catch (error) {
      console.error('Error in ClientQuote.findByToken:', error);
      throw error;
    }
  }

  // Create new quote
  static async create(quoteData, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Validate input data
      const { error, value } = this.validateCreate(quoteData);
      if (error) {
        throw new Error(`Validation error: ${error.details.map(d => d.message).join(', ')}`);
      }

      const {
        rfq_id,
        title,
        selectedBids,
        item_selections,
        commission_structure,
        quote_template_id,
        parent_quote_id,
        currency,
        margin_percentage,
        taxes,
        terms,
        notes,
        client_email,
        client_name,
        expires_in_days
      } = value;

      // Verify RFQ exists and user has permission
      const rfqCheck = await client.query(
        'SELECT id, status FROM rfqs WHERE id = $1 AND creator_id = $2',
        [rfq_id, userId]
      );

      if (rfqCheck.rows.length === 0) {
        throw new Error('RFQ not found or access denied');
      }

      // Verify all selected submissions exist and belong to the RFQ
      const submissionIds = Object.values(selectedBids).map(bid => bid.submissionId);
      const submissionsCheck = await client.query(
        'SELECT id FROM rfq_submissions WHERE id = ANY($1) AND rfq_id = $2',
        [submissionIds, rfq_id]
      );

      if (submissionsCheck.rows.length !== submissionIds.length) {
        throw new Error('One or more selected submissions are invalid');
      }

      // Calculate total amount from selected bids
      const selectedTotal = Object.values(selectedBids).reduce((sum, bid) => {
        return sum + bid.totalPrice;
      }, 0);

      // Apply margin and taxes
      const finalTotal = selectedTotal * (1 + margin_percentage / 100) + taxes;

      // Set expiration date
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expires_in_days);

      // Create quote
      const quoteQuery = `
        INSERT INTO client_quotes (
          rfq_id, creator_id, title, selected_bids, item_selections, commission_structure,
          quote_template_id, parent_quote_id, total_amount, currency,
          margin_percentage, taxes, terms, notes, client_email, client_name,
          expires_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        RETURNING *
      `;

      const quoteParams = [
        rfq_id,
        userId,
        title,
        JSON.stringify(selectedBids),
        JSON.stringify(item_selections || {}),
        JSON.stringify(commission_structure || {}),
        quote_template_id || null,
        parent_quote_id || null,
        finalTotal,
        currency,
        margin_percentage,
        taxes,
        terms || '',
        notes || '',
        client_email,
        client_name,
        expiresAt
      ];

      const quoteResult = await client.query(quoteQuery, quoteParams);
      const quote = quoteResult.rows[0];

      await commitTransaction(client);

      // Return quote with full details
      return await this.findById(quote.id, userId);
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in ClientQuote.create:', error);
      throw error;
    }
  }

  // Update quote
  static async update(id, updates, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Validate input data
      const { error, value } = this.validateUpdate(updates);
      if (error) {
        throw new Error(`Validation error: ${error.details.map(d => d.message).join(', ')}`);
      }

      // Check if quote exists and user has permission
      const existingQuote = await client.query(
        'SELECT * FROM client_quotes WHERE id = $1 AND creator_id = $2',
        [id, userId]
      );

      if (existingQuote.rows.length === 0) {
        throw new Error('Quote not found or access denied');
      }

      const currentQuote = existingQuote.rows[0];

      // Check if quote can be updated
      if (currentQuote.status === 'approved' || currentQuote.status === 'rejected') {
        throw new Error('Cannot update approved or rejected quote');
      }

      // Build dynamic update query
      const updateFields = [];
      const params = [id];
      let paramCount = 1;

      const allowedFields = [
        'title', 'selected_bids', 'item_selections', 'commission_structure',
        'quote_template_id', 'margin_percentage', 'taxes', 'terms', 
        'notes', 'client_email', 'client_name', 'status'
      ];

      let recalculateTotal = false;

      for (const field of allowedFields) {
        if (value[field] !== undefined) {
          paramCount++;
          if (field === 'selected_bids' || field === 'item_selections' || field === 'commission_structure') {
            updateFields.push(`${field} = $${paramCount}`);
            params.push(JSON.stringify(value[field]));
            if (field === 'selected_bids' || field === 'item_selections') {
              recalculateTotal = true;
            }
          } else {
            updateFields.push(`${field} = $${paramCount}`);
            params.push(value[field]);
            if (field === 'margin_percentage' || field === 'taxes') {
              recalculateTotal = true;
            }
          }
        }
      }

      // Handle expiration date update
      if (value.expires_in_days) {
        const newExpiresAt = new Date();
        newExpiresAt.setDate(newExpiresAt.getDate() + value.expires_in_days);
        paramCount++;
        updateFields.push(`expires_at = $${paramCount}`);
        params.push(newExpiresAt);
      }

      // Recalculate total if needed
      if (recalculateTotal) {
        const selectedBids = value.selected_bids || currentQuote.selected_bids;
        const marginPercentage = value.margin_percentage !== undefined ? value.margin_percentage : currentQuote.margin_percentage;
        const taxes = value.taxes !== undefined ? value.taxes : currentQuote.taxes;

        const selectedTotal = Object.values(selectedBids).reduce((sum, bid) => {
          return sum + bid.totalPrice;
        }, 0);

        const finalTotal = selectedTotal * (1 + marginPercentage / 100) + taxes;
        
        paramCount++;
        updateFields.push(`total_amount = $${paramCount}`);
        params.push(finalTotal);
      }

      if (updateFields.length === 0) {
        await rollbackTransaction(client);
        return currentQuote;
      }

      // Add updated_at and version increment
      updateFields.push('updated_at = CURRENT_TIMESTAMP');
      updateFields.push('version = version + 1');

      const updateQuery = `
        UPDATE client_quotes 
        SET ${updateFields.join(', ')}
        WHERE id = $1
        RETURNING *
      `;

      const result = await client.query(updateQuery, params);
      const updatedQuote = result.rows[0];

      await commitTransaction(client);

      // Return updated quote with full details
      return await this.findById(id, userId);
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in ClientQuote.update:', error);
      throw error;
    }
  }

  // Send quote to client
  static async send(id, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Check if quote exists and user has permission
      const existingQuote = await client.query(
        'SELECT * FROM client_quotes WHERE id = $1 AND creator_id = $2',
        [id, userId]
      );

      if (existingQuote.rows.length === 0) {
        throw new Error('Quote not found or access denied');
      }

      const currentQuote = existingQuote.rows[0];

      // Check if quote can be sent
      if (currentQuote.status !== 'draft') {
        throw new Error('Quote can only be sent from draft status');
      }

      // Update quote status to sent
      const result = await client.query(
        `UPDATE client_quotes 
         SET status = 'sent', sent_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
         WHERE id = $1
         RETURNING *`,
        [id]
      );

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in ClientQuote.send:', error);
      throw error;
    }
  }

  // Approve quote (public access via token)
  static async approve(token, approvalData) {
    const client = await beginTransaction();
    
    try {
      // Validate approval data
      const { error, value } = this.validateApprove(approvalData);
      if (error) {
        throw new Error(`Validation error: ${error.details.map(d => d.message).join(', ')}`);
      }

      const {
        approved_by_name,
        approved_by_email,
        approval_signature,
        feedback
      } = value;

      // Find quote by token
      const quoteCheck = await client.query(
        'SELECT * FROM client_quotes WHERE public_token = $1',
        [token]
      );

      if (quoteCheck.rows.length === 0) {
        throw new Error('Quote not found or invalid token');
      }

      const quote = quoteCheck.rows[0];

      // Check if quote can be approved
      if (quote.status === 'approved') {
        throw new Error('Quote is already approved');
      }

      if (quote.status === 'rejected') {
        throw new Error('Quote has been rejected and cannot be approved');
      }

      // Check if quote is expired
      if (new Date() > new Date(quote.expires_at)) {
        throw new Error('Quote has expired and cannot be approved');
      }

      // Update quote with approval
      const approvalResult = await client.query(
        `UPDATE client_quotes 
         SET status = 'approved', 
             approved_at = CURRENT_TIMESTAMP,
             approved_by_name = $1,
             approved_by_email = $2,
             approval_signature = $3,
             updated_at = CURRENT_TIMESTAMP
         WHERE id = $4
         RETURNING *`,
        [approved_by_name, approved_by_email, approval_signature || '', quote.id]
      );

      const approvedQuote = approvalResult.rows[0];

      // Create invoices from approved quote using Invoice model
      const Invoice = require('./Invoice');
      const invoiceResults = await Invoice.generateFromRFQQuote(client, approvedQuote);

      // Update quote with invoice reference (use first invoice if multiple)
      if (invoiceResults.length > 0) {
        await client.query(
          'UPDATE client_quotes SET invoice_id = $1 WHERE id = $2',
          [invoiceResults[0].id, approvedQuote.id]
        );
      }

      await commitTransaction(client);

      return {
        quote: approvedQuote,
        invoices: invoiceResults,
        feedback: feedback || ''
      };
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in ClientQuote.approve:', error);
      throw error;
    }
  }

  // Reject quote (public access via token)
  static async reject(token, feedback = '') {
    const client = await beginTransaction();
    
    try {
      // Find quote by token
      const quoteCheck = await client.query(
        'SELECT * FROM client_quotes WHERE public_token = $1',
        [token]
      );

      if (quoteCheck.rows.length === 0) {
        throw new Error('Quote not found or invalid token');
      }

      const quote = quoteCheck.rows[0];

      // Check if quote can be rejected
      if (quote.status === 'approved') {
        throw new Error('Quote is already approved and cannot be rejected');
      }

      if (quote.status === 'rejected') {
        throw new Error('Quote is already rejected');
      }

      // Update quote status to rejected
      const result = await client.query(
        `UPDATE client_quotes 
         SET status = 'rejected', updated_at = CURRENT_TIMESTAMP
         WHERE id = $1
         RETURNING *`,
        [quote.id]
      );

      await commitTransaction(client);

      return {
        quote: result.rows[0],
        feedback: feedback
      };
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in ClientQuote.reject:', error);
      throw error;
    }
  }



  // Delete quote
  static async delete(id, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Check if quote exists and user has permission
      const existingQuote = await client.query(
        'SELECT * FROM client_quotes WHERE id = $1 AND creator_id = $2',
        [id, userId]
      );

      if (existingQuote.rows.length === 0) {
        throw new Error('Quote not found or access denied');
      }

      const currentQuote = existingQuote.rows[0];

      // Check if quote can be deleted
      if (currentQuote.status === 'approved') {
        throw new Error('Cannot delete approved quote');
      }

      // Delete quote
      const result = await client.query(
        'DELETE FROM client_quotes WHERE id = $1 RETURNING id',
        [id]
      );

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in ClientQuote.delete:', error);
      throw error;
    }
  }

  // Get quote statistics
  static async getStatistics(userId) {
    try {
      const result = await query(`
        SELECT 
          status,
          COUNT(*) as count,
          SUM(total_amount) as total_value,
          AVG(total_amount) as average_value
        FROM client_quotes
        WHERE creator_id = $1
        GROUP BY status
        ORDER BY status
      `, [userId]);

      return result.rows;
    } catch (error) {
      console.error('Error in ClientQuote.getStatistics:', error);
      throw error;
    }
  }

  // Validate quote token (for public access)
  static async validateToken(token) {
    try {
      const result = await query(`
        SELECT 
          cq.*,
          r.title as rfq_title,
          CASE 
            WHEN cq.expires_at < CURRENT_TIMESTAMP AND cq.status NOT IN ('approved', 'rejected')
            THEN true 
            ELSE false 
          END as is_expired
        FROM client_quotes cq
        LEFT JOIN rfqs r ON cq.rfq_id = r.id
        WHERE cq.public_token = $1
      `, [token]);

      if (result.rows.length === 0) {
        return { valid: false, error: 'Invalid token' };
      }

      const quote = result.rows[0];

      // Check if quote is expired
      if (quote.is_expired) {
        return { valid: false, error: 'Quote has expired' };
      }

      return {
        valid: true,
        quote,
        can_approve: quote.status === 'sent' || quote.status === 'viewed',
        can_reject: quote.status === 'sent' || quote.status === 'viewed'
      };
    } catch (error) {
      console.error('Error in ClientQuote.validateToken:', error);
      throw error;
    }
  }

  // Get audit history for quote
  static async getAuditHistory(id, userId) {
    try {
      // Check if quote exists and user has permission
      const quoteCheck = await query(
        'SELECT id FROM client_quotes WHERE id = $1 AND creator_id = $2',
        [id, userId]
      );

      if (quoteCheck.rows.length === 0) {
        throw new Error('Quote not found or access denied');
      }

      const result = await query(`
        SELECT 
          a.id, a.entity_type, a.entity_id, a.action, a.user_id,
          a.old_value, a.new_value, a.details, a.timestamp,
          u.email as user_email
        FROM audits a
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.entity_type = 'client_quotes' AND a.entity_id = $1
        ORDER BY a.timestamp DESC
      `, [id]);

      return result.rows;
    } catch (error) {
      console.error('Error in ClientQuote.getAuditHistory:', error);
      throw error;
    }
  }

  // Helper method to calculate total with commission structure
  static calculateTotalWithCommission(itemSelections, commissionStructure = {}) {
    const globalRate = commissionStructure.global_rate || 0;
    const itemOverrides = commissionStructure.item_overrides || {};
    
    let subtotal = 0;
    let totalCommission = 0;
    
    Object.entries(itemSelections).forEach(([itemId, selection]) => {
      const itemTotal = selection.pricing.totalPrice;
      subtotal += itemTotal;
      
      // Check for item-specific commission override
      const override = itemOverrides[itemId];
      let commissionAmount = 0;
      
      if (override) {
        if (override.type === 'fixed') {
          commissionAmount = override.amount || 0;
        } else {
          commissionAmount = itemTotal * (override.rate / 100);
        }
      } else {
        commissionAmount = itemTotal * (globalRate / 100);
      }
      
      totalCommission += commissionAmount;
    });
    
    return {
      subtotal,
      commission: totalCommission,
      total: subtotal + totalCommission
    };
  }

  // Helper method to validate item selections against RFQ submissions
  static async validateItemSelections(rfqId, itemSelections) {
    try {
      const submissionIds = Object.values(itemSelections).map(sel => sel.submissionId);
      const vendorIds = Object.values(itemSelections).map(sel => sel.vendorId);
      
      // Verify all submissions belong to the RFQ
      const submissionCheck = await query(
        'SELECT id, vendor_id FROM rfq_submissions WHERE id = ANY($1) AND rfq_id = $2',
        [submissionIds, rfqId]
      );
      
      if (submissionCheck.rows.length !== submissionIds.length) {
        throw new Error('One or more selected submissions do not belong to this RFQ');
      }
      
      // Verify vendor IDs match submission vendor IDs
      const submissionVendorMap = submissionCheck.rows.reduce((acc, row) => {
        acc[row.id] = row.vendor_id;
        return acc;
      }, {});
      
      for (const [itemId, selection] of Object.entries(itemSelections)) {
        const expectedVendorId = submissionVendorMap[selection.submissionId];
        if (expectedVendorId !== selection.vendorId) {
          throw new Error(`Vendor ID mismatch for item ${itemId}`);
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error in validateItemSelections:', error);
      throw error;
    }
  }

  // Helper method to create quote revision
  static async createRevision(parentQuoteId, updates, userId) {
    const client = await beginTransaction();
    
    try {
      // Get parent quote
      const parentQuote = await this.findById(parentQuoteId, userId);
      if (!parentQuote) {
        throw new Error('Parent quote not found');
      }
      
      // Create revision data by merging parent data with updates
      const revisionData = {
        rfq_id: parentQuote.rfq_id,
        title: updates.title || `${parentQuote.title} (Rev ${parentQuote.revision_number + 1})`,
        selectedBids: updates.selectedBids || parentQuote.selected_bids,
        item_selections: updates.item_selections || parentQuote.item_selections,
        commission_structure: updates.commission_structure || parentQuote.commission_structure,
        quote_template_id: updates.quote_template_id || parentQuote.quote_template_id,
        parent_quote_id: parentQuoteId,
        currency: updates.currency || parentQuote.currency,
        margin_percentage: updates.margin_percentage !== undefined ? updates.margin_percentage : parentQuote.margin_percentage,
        taxes: updates.taxes !== undefined ? updates.taxes : parentQuote.taxes,
        terms: updates.terms || parentQuote.terms,
        notes: updates.notes || parentQuote.notes,
        client_email: updates.client_email || parentQuote.client_email,
        client_name: updates.client_name || parentQuote.client_name,
        expires_in_days: updates.expires_in_days || 30
      };
      
      // Create the revision
      const revision = await this.create(revisionData, userId);
      
      await commitTransaction(client);
      return revision;
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in createRevision:', error);
      throw error;
    }
  }

  // Helper method to get quote template
  static async getQuoteTemplate(templateId) {
    try {
      const result = await query(
        'SELECT * FROM quote_templates WHERE id = $1 AND is_active = true',
        [templateId]
      );
      
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error in getQuoteTemplate:', error);
      throw error;
    }
  }

  // Helper method to get default quote template
  static async getDefaultQuoteTemplate() {
    try {
      const result = await query(
        'SELECT * FROM quote_templates WHERE is_default = true AND is_active = true LIMIT 1'
      );
      
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error in getDefaultQuoteTemplate:', error);
      throw error;
    }
  }
}

module.exports = ClientQuote;