const { query, queryWithUser, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');

class Contract {
  static async findAll(filters = {}, pagination = { page: 1, limit: 10 }, userId) {
    try {
      let whereClause = 'WHERE 1=1';
      let params = [];
      let paramIndex = 1;

      // Build dynamic WHERE clause based on filters
      if (filters.search) {
        whereClause += ` AND (c.title ILIKE $${paramIndex} OR v.name ILIKE $${paramIndex})`;
        params.push(`%${filters.search}%`);
        paramIndex++;
      }

      if (filters.status) {
        whereClause += ` AND c.status = $${paramIndex}`;
        params.push(filters.status);
        paramIndex++;
      }

      if (filters.vendor_id) {
        whereClause += ` AND c.vendor_id = $${paramIndex}`;
        params.push(filters.vendor_id);
        paramIndex++;
      }

      if (filters.start_date && filters.end_date) {
        whereClause += ` AND c.start_date >= $${paramIndex} AND c.start_date <= $${paramIndex + 1}`;
        params.push(filters.start_date, filters.end_date);
        paramIndex += 2;
      }

      // Count total records
      const countQuery = `
        SELECT COUNT(*) as total
        FROM contracts c
        LEFT JOIN vendors v ON c.vendor_id = v.id
        ${whereClause}
      `;
      const countResult = await query(countQuery, params);
      const total = parseInt(countResult.rows[0].total);

      // Calculate pagination
      const offset = (pagination.page - 1) * pagination.limit;
      const totalPages = Math.ceil(total / pagination.limit);

      // Fetch contracts with vendor information
      const contractsQuery = `
        SELECT 
          c.*,
          v.name as vendor_name,
          v.contact_email as vendor_email,
          v.contact_phone as vendor_phone
        FROM contracts c
        LEFT JOIN vendors v ON c.vendor_id = v.id
        ${whereClause}
        ORDER BY c.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      params.push(pagination.limit, offset);
      const result = await query(contractsQuery, params);

      return {
        contracts: result.rows,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages
        }
      };
    } catch (error) {
      console.error('Error in Contract.findAll:', error);
      throw error;
    }
  }

  static async findById(id, userId) {
    try {
      const contractQuery = `
        SELECT 
          c.*,
          v.name as vendor_name,
          v.contact_email as vendor_email,
          v.contact_phone as vendor_phone,
          v.address as vendor_address
        FROM contracts c
        LEFT JOIN vendors v ON c.vendor_id = v.id
        WHERE c.id = $1
      `;

      const result = await query(contractQuery, [id]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      console.error('Error in Contract.findById:', error);
      throw error;
    }
  }

  static async create(contractData, userId) {
    try {
      // Insert contract using simple query (no transaction needed for single insert)
      const contractQuery = `
        INSERT INTO contracts (
          vendor_id, title, status, parties, clauses, milestones, start_date, end_date
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;

      const contractParams = [
        contractData.vendor_id,
        contractData.title,
        contractData.status || 'draft',
        JSON.stringify(contractData.parties || {}),
        JSON.stringify(contractData.clauses || {}),
        JSON.stringify(contractData.milestones || []),
        contractData.start_date,
        contractData.end_date
      ];

      const contractResult = await query(contractQuery, contractParams);
      const contract = contractResult.rows[0];

      return contract;
    } catch (error) {
      console.error('Error in Contract.create:', error);
      throw error;
    }
  }

  static async update(id, updates, userId) {
    const client = await beginTransaction();
    
    try {
      // Build dynamic update query
      const updateFields = [];
      const params = [];
      let paramIndex = 1;

      const allowedFields = [
        'title', 'status', 'parties', 'clauses', 'start_date', 'end_date',
        'value', 'currency', 'additional_clauses', 'docusign_envelope_id'
      ];

      for (const field of allowedFields) {
        if (updates[field] !== undefined) {
          if (field === 'parties' || field === 'clauses') {
            updateFields.push(`${field} = $${paramIndex}`);
            params.push(JSON.stringify(updates[field]));
          } else {
            updateFields.push(`${field} = $${paramIndex}`);
            params.push(updates[field]);
          }
          paramIndex++;
        }
      }

      if (updateFields.length === 0) {
        throw new Error('No valid fields to update');
      }

      updateFields.push(`updated_by = $${paramIndex}`);
      params.push(userId);
      paramIndex++;

      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

      params.push(id, userId);

      const updateQuery = `
        UPDATE contracts 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex} AND created_by = $${paramIndex + 1}
        RETURNING *
      `;

      const result = await client.query(updateQuery, params);

      if (result.rows.length === 0) {
        throw new Error('Contract not found or access denied');
      }

      // Handle milestone updates if provided
      if (updates.milestones) {
        // Delete existing milestones
        await client.query(
          'DELETE FROM contract_milestones WHERE contract_id = $1',
          [id]
        );

        // Insert new milestones
        for (const milestone of updates.milestones) {
          const milestoneQuery = `
            INSERT INTO contract_milestones (
              contract_id, name, description, due_date, completed, completed_date
            ) VALUES ($1, $2, $3, $4, $5, $6)
          `;
          
          await client.query(milestoneQuery, [
            id,
            milestone.name,
            milestone.description || null,
            milestone.due_date,
            milestone.completed || false,
            milestone.completed_date || null
          ]);
        }
      }

      await commitTransaction(client);

      // Return updated contract with related data
      return await this.findById(id, userId);
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in Contract.update:', error);
      throw error;
    }
  }

  static async delete(id, userId) {
    try {
      const deleteQuery = `
        DELETE FROM contracts 
        WHERE id = $1 AND created_by = $2
        RETURNING id
      `;

      const result = await queryWithUser(deleteQuery, [id, userId], userId);

      if (result.rows.length === 0) {
        throw new Error('Contract not found or access denied');
      }

      return { id: result.rows[0].id };
    } catch (error) {
      console.error('Error in Contract.delete:', error);
      throw error;
    }
  }

  static async getTemplates() {
    try {
      const templateQuery = `
        SELECT * FROM contract_templates 
        WHERE active = true 
        ORDER BY category, name
      `;

      const result = await query(templateQuery);
      return result.rows;
    } catch (error) {
      console.error('Error in Contract.getTemplates:', error);
      
      // Return mock templates if table doesn't exist yet
      return [
        {
          id: 'software-dev',
          name: 'Software Development Agreement',
          description: 'Standard template for software development projects',
          category: 'Technology',
          default_fields: {
            payment_terms: '30 days net',
            deliverables: 'Software application with source code, documentation, and testing',
            additional_clauses: 'All intellectual property rights transfer to client upon final payment. 90-day warranty period included.'
          },
          required_fields: ['title', 'vendor_id', 'start_date', 'end_date', 'payment_terms', 'deliverables'],
          active: true
        },
        {
          id: 'consulting',
          name: 'Consulting Services Agreement',
          description: 'Professional consulting and advisory services',
          category: 'Services',
          default_fields: {
            payment_terms: '15 days net',
            deliverables: 'Consulting services, reports, and recommendations',
            additional_clauses: 'Confidentiality agreement applies to all shared information. Reports delivered in digital format.'
          },
          required_fields: ['title', 'vendor_id', 'start_date', 'end_date', 'payment_terms', 'deliverables'],
          active: true
        }
      ];
    }
  }

  static async createAmendment(contractId, amendmentData, userId) {
    try {
      const query = `
        INSERT INTO contract_amendments (
          contract_id, description, changes, created_by
        ) VALUES ($1, $2, $3, $4)
        RETURNING *
      `;

      const result = await queryWithUser(query, [
        contractId,
        amendmentData.description,
        JSON.stringify(amendmentData.changes),
        userId
      ], userId);

      return result.rows[0];
    } catch (error) {
      console.error('Error in Contract.createAmendment:', error);
      throw error;
    }
  }

  static async updateMilestone(milestoneId, updates, userId) {
    try {
      const query = `
        UPDATE contract_milestones 
        SET completed = $1, completed_date = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $3 AND contract_id IN (
          SELECT id FROM contracts WHERE created_by = $4
        )
        RETURNING *
      `;

      const result = await queryWithUser(query, [
        updates.completed,
        updates.completed_date,
        milestoneId,
        userId
      ], userId);

      if (result.rows.length === 0) {
        throw new Error('Milestone not found or access denied');
      }

      return result.rows[0];
    } catch (error) {
      console.error('Error in Contract.updateMilestone:', error);
      throw error;
    }
  }
}

module.exports = Contract;