const { query, queryWithUser, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');

class Vendor {
  // Get all vendors with filtering and pagination
  static async findAll(filters = {}, page = 1, limit = 10) {
    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    // Apply filters
    if (filters.search) {
      paramCount++;
      whereClause += ` AND (name ILIKE $${paramCount} OR contact_email ILIKE $${paramCount} OR category ILIKE $${paramCount})`;
      params.push(`%${filters.search}%`);
    }

    if (filters.category) {
      paramCount++;
      whereClause += ` AND category = $${paramCount}`;
      params.push(filters.category);
    }

    if (filters.status) {
      paramCount++;
      whereClause += ` AND status = $${paramCount}`;
      params.push(filters.status);
    }

    if (filters.performance_min !== undefined) {
      paramCount++;
      whereClause += ` AND performance_score >= $${paramCount}`;
      params.push(filters.performance_min);
    }

    if (filters.performance_max !== undefined) {
      paramCount++;
      whereClause += ` AND performance_score <= $${paramCount}`;
      params.push(filters.performance_max);
    }

    // Count total records
    const countQuery = `SELECT COUNT(*) FROM vendors ${whereClause}`;
    const countResult = await query(countQuery, params);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const offset = (page - 1) * limit;
    paramCount++;
    const limitParam = paramCount;
    paramCount++;
    const offsetParam = paramCount;
    
    const selectQuery = `
      SELECT id, name, contact_email, contact_phone, address, category, 
             certifications, performance_score, status, custom_fields,
             created_at, updated_at, deactivated_at, blacklisted_reason
      FROM vendors 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${limitParam} OFFSET $${offsetParam}
    `;
    
    params.push(limit, offset);
    const result = await query(selectQuery, params);

    return {
      vendors: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  // Find vendor by ID
  static async findById(id) {
    const result = await query(
      `SELECT id, name, contact_email, contact_phone, address, category, 
              certifications, performance_score, status, custom_fields,
              created_at, updated_at, deactivated_at, blacklisted_reason
       FROM vendors WHERE id = $1`,
      [id]
    );
    return result.rows[0];
  }

  // Create new vendor
  static async create(vendorData, userId = 1) {
    const {
      name,
      contact_email,
      contact_phone,
      address,
      category,
      certifications = [],
      custom_fields = {}
    } = vendorData;

    const result = await queryWithUser(
      `INSERT INTO vendors (name, contact_email, contact_phone, address, category, certifications, custom_fields)
       VALUES ($1, $2, $3, $4, $5, $6, $7)
       RETURNING id, name, contact_email, contact_phone, address, category, 
                 certifications, performance_score, status, custom_fields,
                 created_at, updated_at, deactivated_at, blacklisted_reason`,
      [name, contact_email, contact_phone, JSON.stringify(address), category, JSON.stringify(certifications), JSON.stringify(custom_fields)],
      userId
    );

    return result.rows[0];
  }

  // Update vendor
  static async update(id, vendorData, userId = 1) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);
      
      // Get current vendor data for audit
      const currentResult = await client.query('SELECT * FROM vendors WHERE id = $1', [id]);
      if (currentResult.rows.length === 0) {
        throw new Error('Vendor not found');
      }
      const currentVendor = currentResult.rows[0];

      // Build update query dynamically
      const updateFields = [];
      const params = [id];
      let paramCount = 1;

      Object.keys(vendorData).forEach(key => {
        if (vendorData[key] !== undefined && key !== 'id') {
          paramCount++;
          if (key === 'address' || key === 'certifications' || key === 'custom_fields') {
            updateFields.push(`${key} = $${paramCount}`);
            params.push(JSON.stringify(vendorData[key]));
          } else {
            updateFields.push(`${key} = $${paramCount}`);
            params.push(vendorData[key]);
          }
        }
      });

      if (updateFields.length === 0) {
        await rollbackTransaction(client);
        return currentVendor;
      }

      // Add updated_at
      paramCount++;
      updateFields.push(`updated_at = $${paramCount}`);
      params.push(new Date());

      const updateQuery = `
        UPDATE vendors 
        SET ${updateFields.join(', ')}
        WHERE id = $1
        RETURNING id, name, contact_email, contact_phone, address, category, 
                  certifications, performance_score, status, custom_fields,
                  created_at, updated_at, deactivated_at, blacklisted_reason
      `;

      const result = await client.query(updateQuery, params);
      const updatedVendor = result.rows[0];

      // Audit logging is handled by database trigger

      await commitTransaction(client);
      return updatedVendor;
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Soft delete vendor
  static async delete(id, userId = 1) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);
      
      // Get current vendor data
      const currentResult = await client.query('SELECT * FROM vendors WHERE id = $1', [id]);
      if (currentResult.rows.length === 0) {
        throw new Error('Vendor not found');
      }

      // Soft delete
      const result = await client.query(
        `UPDATE vendors 
         SET status = 'inactive', deactivated_at = $1, updated_at = $1
         WHERE id = $2
         RETURNING id`,
        [new Date(), id]
      );

      // Audit logging is handled by database trigger

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Get vendor categories
  static async getCategories() {
    const result = await query(
      'SELECT DISTINCT category FROM vendors WHERE category IS NOT NULL ORDER BY category'
    );
    return result.rows.map(row => row.category);
  }

  // Get vendor audit history
  static async getAuditHistory(id) {
    const result = await query(
      `SELECT a.id, a.entity_type, a.entity_id, a.action, a.user_id, 
              a.old_value, a.new_value, a.details, a.timestamp,
              u.email as user_name
       FROM audits a
       LEFT JOIN users u ON a.user_id = u.id
       WHERE a.entity_type = 'vendors' AND a.entity_id = $1
       ORDER BY a.timestamp DESC`,
      [id]
    );
    return result.rows;
  }

  // Get RFQ participation history for vendor
  static async getRFQHistory(id) {
    const result = await query(
      `SELECT 
         r.id as rfq_id,
         r.title as rfq_title,
         r.status as rfq_status,
         r.due_date,
         r.created_at as rfq_created_at,
         ri.status as invitation_status,
         ri.sent_at as invitation_sent_at,
         ri.viewed_at as invitation_viewed_at,
         ri.submitted_at as invitation_submitted_at,
         rs.id as submission_id,
         rs.total_amount as bid_amount,
         rs.currency as bid_currency,
         rs.submitted_at as bid_submitted_at,
         cq.id as quote_id,
         cq.status as quote_status,
         cq.approved_at as quote_approved_at,
         -- Check if vendor was selected in any quote
         CASE 
           WHEN cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' 
           THEN true 
           ELSE false 
         END as was_selected
       FROM vendors v
       LEFT JOIN rfq_invitations ri ON v.id = ri.vendor_id
       LEFT JOIN rfqs r ON ri.rfq_id = r.id
       LEFT JOIN rfq_submissions rs ON ri.rfq_id = rs.rfq_id AND ri.vendor_id = rs.vendor_id
       LEFT JOIN client_quotes cq ON r.id = cq.rfq_id
       WHERE v.id = $1 AND r.id IS NOT NULL
       ORDER BY r.created_at DESC`,
      [id]
    );
    return result.rows;
  }

  // Get RFQ participation statistics for vendor
  static async getRFQStatistics(id) {
    const result = await query(
      `SELECT 
         COUNT(DISTINCT ri.rfq_id) as total_invitations,
         COUNT(DISTINCT rs.rfq_id) as total_submissions,
         COUNT(DISTINCT CASE WHEN cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.rfq_id END) as total_selections,
         COUNT(DISTINCT CASE WHEN cq.status = 'approved' AND cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.rfq_id END) as total_wins,
         COALESCE(AVG(rs.total_amount), 0) as average_bid_amount,
         COALESCE(SUM(CASE WHEN cq.status = 'approved' AND cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.total_amount END), 0) as total_won_value,
         CASE 
           WHEN COUNT(DISTINCT ri.rfq_id) > 0 
           THEN ROUND((COUNT(DISTINCT rs.rfq_id)::DECIMAL / COUNT(DISTINCT ri.rfq_id)) * 100, 2)
           ELSE 0 
         END as response_rate,
         CASE 
           WHEN COUNT(DISTINCT rs.rfq_id) > 0 
           THEN ROUND((COUNT(DISTINCT CASE WHEN cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.rfq_id END)::DECIMAL / COUNT(DISTINCT rs.rfq_id)) * 100, 2)
           ELSE 0 
         END as selection_rate,
         CASE 
           WHEN COUNT(DISTINCT CASE WHEN cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.rfq_id END) > 0 
           THEN ROUND((COUNT(DISTINCT CASE WHEN cq.status = 'approved' AND cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.rfq_id END)::DECIMAL / COUNT(DISTINCT CASE WHEN cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.rfq_id END)) * 100, 2)
           ELSE 0 
         END as win_rate
       FROM vendors v
       LEFT JOIN rfq_invitations ri ON v.id = ri.vendor_id
       LEFT JOIN rfqs r ON ri.rfq_id = r.id
       LEFT JOIN rfq_submissions rs ON ri.rfq_id = rs.rfq_id AND ri.vendor_id = rs.vendor_id
       LEFT JOIN client_quotes cq ON r.id = cq.rfq_id
       WHERE v.id = $1
       GROUP BY v.id`,
      [id]
    );
    return result.rows[0] || {
      total_invitations: 0,
      total_submissions: 0,
      total_selections: 0,
      total_wins: 0,
      average_bid_amount: 0,
      total_won_value: 0,
      response_rate: 0,
      selection_rate: 0,
      win_rate: 0
    };
  }

  // Update vendor performance score based on RFQ outcomes
  static async updatePerformanceFromRFQ(vendorId, rfqOutcome, userId = 1) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Get current vendor data
      const vendorResult = await client.query('SELECT * FROM vendors WHERE id = $1', [vendorId]);
      if (vendorResult.rows.length === 0) {
        throw new Error('Vendor not found');
      }

      const vendor = vendorResult.rows[0];
      const currentScore = vendor.performance_score || 0;

      // Calculate performance adjustment based on RFQ outcome
      let scoreAdjustment = 0;
      let adjustmentReason = '';

      switch (rfqOutcome.type) {
        case 'selected_for_quote':
          scoreAdjustment = 2; // Small positive adjustment for being selected
          adjustmentReason = `Selected for quote in RFQ: ${rfqOutcome.rfqTitle}`;
          break;
        case 'quote_approved':
          scoreAdjustment = 5; // Larger positive adjustment for winning
          adjustmentReason = `Won RFQ: ${rfqOutcome.rfqTitle}`;
          break;
        case 'invoice_paid':
          scoreAdjustment = 3; // Positive adjustment for timely payment
          adjustmentReason = `Invoice paid for RFQ: ${rfqOutcome.rfqTitle} (${rfqOutcome.currency} ${rfqOutcome.amount})`;
          break;
        case 'invoice_overdue':
          scoreAdjustment = -2; // Small penalty for overdue invoices (may not be vendor's fault)
          adjustmentReason = `Invoice overdue for RFQ: ${rfqOutcome.rfqTitle}`;
          break;
        case 'late_submission':
          scoreAdjustment = -3; // Penalty for late submission
          adjustmentReason = `Late submission for RFQ: ${rfqOutcome.rfqTitle}`;
          break;
        case 'no_submission':
          scoreAdjustment = -5; // Penalty for not responding
          adjustmentReason = `No response to RFQ: ${rfqOutcome.rfqTitle}`;
          break;
        case 'quality_issue':
          scoreAdjustment = -10; // Penalty for quality issues
          adjustmentReason = `Quality issue reported for RFQ: ${rfqOutcome.rfqTitle}`;
          break;
        default:
          scoreAdjustment = 0;
      }

      // Apply adjustment with bounds checking
      const newScore = Math.max(0, Math.min(100, currentScore + scoreAdjustment));

      // Update vendor performance score
      const updateResult = await client.query(
        `UPDATE vendors 
         SET performance_score = $1, updated_at = CURRENT_TIMESTAMP
         WHERE id = $2
         RETURNING *`,
        [newScore, vendorId]
      );

      // Log the performance adjustment
      await client.query(
        `INSERT INTO vendor_histories (vendor_id, change_type, old_value, new_value, changed_by)
         VALUES ($1, $2, $3, $4, $5)`,
        [
          vendorId,
          'performance_adjustment',
          JSON.stringify({ score: currentScore, reason: 'RFQ outcome' }),
          JSON.stringify({ score: newScore, reason: adjustmentReason, adjustment: scoreAdjustment }),
          userId
        ]
      );

      await commitTransaction(client);
      return updateResult.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in Vendor.updatePerformanceFromRFQ:', error);
      throw error;
    }
  }

  // Get vendor engagement analytics
  static async getEngagementAnalytics(id) {
    const result = await query(
      `SELECT 
         -- RFQ Engagement Metrics
         COUNT(DISTINCT ri.rfq_id) as rfq_invitations_received,
         COUNT(DISTINCT rs.rfq_id) as rfq_responses_submitted,
         COUNT(DISTINCT CASE WHEN ri.viewed_at IS NOT NULL THEN ri.rfq_id END) as rfq_invitations_viewed,
         
         -- Response Time Metrics
         AVG(EXTRACT(EPOCH FROM (rs.submitted_at - ri.sent_at))/3600) as avg_response_time_hours,
         MIN(EXTRACT(EPOCH FROM (rs.submitted_at - ri.sent_at))/3600) as fastest_response_time_hours,
         MAX(EXTRACT(EPOCH FROM (rs.submitted_at - ri.sent_at))/3600) as slowest_response_time_hours,
         
         -- Success Metrics
         COUNT(DISTINCT CASE WHEN cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.rfq_id END) as times_selected,
         COUNT(DISTINCT CASE WHEN cq.status = 'approved' AND cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.rfq_id END) as times_won,
         
         -- Financial Metrics
         AVG(rs.total_amount) as average_bid_amount,
         MIN(rs.total_amount) as lowest_bid_amount,
         MAX(rs.total_amount) as highest_bid_amount,
         SUM(CASE WHEN cq.status = 'approved' AND cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.total_amount END) as total_contract_value,
         
         -- Recent Activity (last 90 days)
         COUNT(DISTINCT CASE WHEN ri.sent_at > CURRENT_DATE - INTERVAL '90 days' THEN ri.rfq_id END) as recent_invitations,
         COUNT(DISTINCT CASE WHEN rs.submitted_at > CURRENT_DATE - INTERVAL '90 days' THEN rs.rfq_id END) as recent_submissions,
         
         -- Engagement Score (0-100)
         CASE 
           WHEN COUNT(DISTINCT ri.rfq_id) = 0 THEN 0
           ELSE LEAST(100, 
             (COUNT(DISTINCT rs.rfq_id)::DECIMAL / COUNT(DISTINCT ri.rfq_id) * 40) + -- Response rate (40%)
             (COUNT(DISTINCT CASE WHEN cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.rfq_id END)::DECIMAL / GREATEST(COUNT(DISTINCT rs.rfq_id), 1) * 30) + -- Selection rate (30%)
             (COUNT(DISTINCT CASE WHEN cq.status = 'approved' AND cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.rfq_id END)::DECIMAL / GREATEST(COUNT(DISTINCT CASE WHEN cq.selected_bids::text LIKE '%"vendorId":' || v.id || '%' THEN cq.rfq_id END), 1) * 20) + -- Win rate (20%)
             (CASE WHEN COUNT(DISTINCT CASE WHEN ri.sent_at > CURRENT_DATE - INTERVAL '90 days' THEN ri.rfq_id END) > 0 THEN 10 ELSE 0 END) -- Recent activity (10%)
           )
         END as engagement_score
       FROM vendors v
       LEFT JOIN rfq_invitations ri ON v.id = ri.vendor_id
       LEFT JOIN rfqs r ON ri.rfq_id = r.id
       LEFT JOIN rfq_submissions rs ON ri.rfq_id = rs.rfq_id AND ri.vendor_id = rs.vendor_id
       LEFT JOIN client_quotes cq ON r.id = cq.rfq_id
       WHERE v.id = $1
       GROUP BY v.id`,
      [id]
    );
    
    return result.rows[0] || {
      rfq_invitations_received: 0,
      rfq_responses_submitted: 0,
      rfq_invitations_viewed: 0,
      avg_response_time_hours: null,
      fastest_response_time_hours: null,
      slowest_response_time_hours: null,
      times_selected: 0,
      times_won: 0,
      average_bid_amount: null,
      lowest_bid_amount: null,
      highest_bid_amount: null,
      total_contract_value: null,
      recent_invitations: 0,
      recent_submissions: 0,
      engagement_score: 0
    };
  }
}

module.exports = Vendor;