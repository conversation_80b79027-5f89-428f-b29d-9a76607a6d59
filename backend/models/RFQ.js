const { query, queryWithUser, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');
const Joi = require('joi');

class RFQ {
  // Validation schemas
  static getValidationSchema() {
    return {
      create: Joi.object({
        title: Joi.string().min(3).max(255).required(),
        description: Joi.string().max(5000).allow(''),
        items: Joi.array().min(1).max(100).items(
          Joi.object({
            id: Joi.string().required(),
            name: Joi.string().min(1).max(255).required(),
            description: Joi.string().max(1000).allow(''),
            quantity: Joi.number().integer().min(1).max(1000000).required(),
            specifications: Joi.object().default({}),
            category: Joi.string().max(100).allow(''),
            estimatedPrice: Joi.number().min(0).allow(null),
            customFields: Joi.object().default({})
          })
        ).required(),
        due_date: Joi.date().greater('now').required(),
        selectedVendors: Joi.array().min(1).items(Joi.number().integer().positive()).required(),
        formConfig: Joi.array().items(
          Joi.object({
            id: Joi.string().required(),
            type: Joi.string().valid('text', 'number', 'date', 'file', 'select', 'textarea').required(),
            label: Joi.string().min(1).max(255).required(),
            required: Joi.boolean().default(false),
            options: Joi.array().items(Joi.string()).when('type', { is: 'select', then: Joi.required() }),
            validation: Joi.array().items(Joi.object()).default([]),
            itemSpecific: Joi.boolean().default(false)
          })
        ).default([]),
        terms: Joi.string().max(10000).allow(''),
        currency: Joi.string().valid('USD', 'EUR', 'GBP', 'JPY', 'CNY').default('USD'),
        aiSettings: Joi.object({
          priceWeight: Joi.number().min(0).max(1).default(0.4),
          performanceWeight: Joi.number().min(0).max(1).default(0.3),
          deliveryWeight: Joi.number().min(0).max(1).default(0.2),
          riskWeight: Joi.number().min(0).max(1).default(0.1),
          diversificationPreference: Joi.number().min(0).max(1).default(0.5)
        }).default({}),
        allow_partial_selection: Joi.boolean().default(false),
        partial_selection_config: Joi.object({
          enabled: Joi.boolean().default(false),
          requireVendorConfirmation: Joi.boolean().default(false),
          confirmationMessage: Joi.string().max(1000).allow(''),
          instructions: Joi.string().max(1000).allow(''),
          defaultAllowed: Joi.boolean().default(false)
        }).default({})
      }),

      update: Joi.object({
        title: Joi.string().min(3).max(255),
        description: Joi.string().max(5000).allow(''),
        items: Joi.array().min(1).max(100).items(
          Joi.object({
            id: Joi.string().required(),
            name: Joi.string().min(1).max(255).required(),
            description: Joi.string().max(1000).allow(''),
            quantity: Joi.number().integer().min(1).max(1000000).required(),
            specifications: Joi.object().default({}),
            category: Joi.string().max(100).allow(''),
            estimatedPrice: Joi.number().min(0).allow(null),
            customFields: Joi.object().default({})
          })
        ),
        due_date: Joi.date().greater('now'),
        formConfig: Joi.array().items(
          Joi.object({
            id: Joi.string().required(),
            type: Joi.string().valid('text', 'number', 'date', 'file', 'select', 'textarea').required(),
            label: Joi.string().min(1).max(255).required(),
            required: Joi.boolean().default(false),
            options: Joi.array().items(Joi.string()).when('type', { is: 'select', then: Joi.required() }),
            validation: Joi.array().items(Joi.object()).default([]),
            itemSpecific: Joi.boolean().default(false)
          })
        ),
        terms: Joi.string().max(10000).allow(''),
        currency: Joi.string().valid('USD', 'EUR', 'GBP', 'JPY', 'CNY'),
        aiSettings: Joi.object({
          priceWeight: Joi.number().min(0).max(1),
          performanceWeight: Joi.number().min(0).max(1),
          deliveryWeight: Joi.number().min(0).max(1),
          riskWeight: Joi.number().min(0).max(1),
          diversificationPreference: Joi.number().min(0).max(1)
        }),
        allow_partial_selection: Joi.boolean(),
        partial_selection_config: Joi.object({
            enabled: Joi.boolean().default(false),
          requireVendorConfirmation: Joi.boolean().default(false),
          confirmationMessage: Joi.string().max(1000).allow(''),
          instructions: Joi.string().max(1000).allow(''),
          defaultAllowed: Joi.boolean().default(false)
        }),
        status: Joi.string().valid('draft', 'sent', 'in_progress', 'closed', 'cancelled')
      }).min(1)
    };
  }

  // Validate RFQ data
  static validateCreate(data) {
    return this.getValidationSchema().create.validate(data, { abortEarly: false });
  }

  static validateUpdate(data) {
    return this.getValidationSchema().update.validate(data, { abortEarly: false });
  }

  // Get all RFQs with filtering and pagination
  static async findAll(filters = {}, page = 1, limit = 10, userId) {
    try {
      let whereClause = 'WHERE 1=1';
      const params = [];
      let paramCount = 0;

      // Apply filters
      if (filters.search) {
        paramCount++;
        whereClause += ` AND (r.title ILIKE $${paramCount} OR r.description ILIKE $${paramCount} OR EXISTS (
          SELECT 1 FROM jsonb_array_elements(r.items) AS item 
          WHERE item->>'name' ILIKE $${paramCount} OR item->>'description' ILIKE $${paramCount}
        ))`;
        params.push(`%${filters.search}%`);
      }

      if (filters.status) {
        paramCount++;
        whereClause += ` AND r.status = $${paramCount}`;
        params.push(filters.status);
      }

      if (filters.creator_id) {
        paramCount++;
        whereClause += ` AND r.creator_id = $${paramCount}`;
        params.push(filters.creator_id);
      }

      if (filters.due_date_from) {
        paramCount++;
        whereClause += ` AND r.due_date >= $${paramCount}`;
        params.push(filters.due_date_from);
      }

      if (filters.due_date_to) {
        paramCount++;
        whereClause += ` AND r.due_date <= $${paramCount}`;
        params.push(filters.due_date_to);
      }

      if (filters.currency) {
        paramCount++;
        whereClause += ` AND r.currency = $${paramCount}`;
        params.push(filters.currency);
      }

      if (filters.category) {
        paramCount++;
        whereClause += ` AND EXISTS (
          SELECT 1 FROM jsonb_array_elements(r.items) AS item 
          WHERE item->>'category' = $${paramCount}
        )`;
        params.push(filters.category);
      }

      if (filters.vendor_id) {
        paramCount++;
        whereClause += ` AND EXISTS (
          SELECT 1 FROM rfq_invitations ri 
          WHERE ri.rfq_id = r.id AND ri.vendor_id = $${paramCount}
        )`;
        params.push(filters.vendor_id);
      }

      // Count total records
      const countQuery = `
        SELECT COUNT(*) as total
        FROM rfqs r
        LEFT JOIN users u ON r.creator_id = u.id
        ${whereClause}
      `;
      const countResult = await query(countQuery, params);
      const total = parseInt(countResult.rows[0].total);

      // Calculate pagination
      const offset = (page - 1) * limit;
      const totalPages = Math.ceil(total / limit);

      // Build ORDER BY clause
      let orderBy = 'ORDER BY ';
      switch (filters.sort_by) {
        case 'title':
          orderBy += 'r.title';
          break;
        case 'due_date':
          orderBy += 'r.due_date';
          break;
        case 'updated_at':
          orderBy += 'r.updated_at';
          break;
        case 'response_rate':
          orderBy += 'response_rate';
          break;
        default:
          orderBy += 'r.created_at';
      }
      orderBy += ` ${filters.sort_order || 'DESC'}`;

      // Fetch RFQs with creator information and summary statistics
      paramCount++;
      const limitParam = paramCount;
      paramCount++;
      const offsetParam = paramCount;
      
      const rfqsQuery = `
        SELECT 
          r.id,
          r.title,
          r.description,
          r.status,
          r.due_date,
          r.currency,
          r.creator_id,
          u.email as creator_email,
          jsonb_array_length(r.items) as item_count,
          r.created_at,
          r.updated_at,
          -- Get invitation and submission counts
          COALESCE(inv_stats.invitation_count, 0) as invitation_count,
          COALESCE(sub_stats.submission_count, 0) as submission_count,
          CASE 
            WHEN COALESCE(inv_stats.invitation_count, 0) > 0 
            THEN ROUND((COALESCE(sub_stats.submission_count, 0)::DECIMAL / inv_stats.invitation_count) * 100, 2)
            ELSE 0 
          END as response_rate
        FROM rfqs r
        LEFT JOIN users u ON r.creator_id = u.id
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as invitation_count
          FROM rfq_invitations
          GROUP BY rfq_id
        ) inv_stats ON r.id = inv_stats.rfq_id
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as submission_count
          FROM rfq_submissions
          GROUP BY rfq_id
        ) sub_stats ON r.id = sub_stats.rfq_id
        ${whereClause}
        ${orderBy}
        LIMIT $${limitParam} OFFSET $${offsetParam}
      `;

      params.push(limit, offset);
      const result = await query(rfqsQuery, params);

      return {
        rfqs: result.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrevious: page > 1
        }
      };
    } catch (error) {
      console.error('Error in RFQ.findAll:', error);
      throw error;
    }
  }

  // Find RFQ by ID with detailed information
  static async findById(id, userId) {
    try {
      const rfqQuery = `
        SELECT 
          r.*,
          u.email as creator_email,
          -- Get invitation and submission statistics
          COALESCE(inv_stats.invitation_count, 0) as invitation_count,
          COALESCE(sub_stats.submission_count, 0) as submission_count,
          CASE 
            WHEN COALESCE(inv_stats.invitation_count, 0) > 0 
            THEN ROUND((COALESCE(sub_stats.submission_count, 0)::DECIMAL / inv_stats.invitation_count) * 100, 2)
            ELSE 0 
          END as response_rate
        FROM rfqs r
        LEFT JOIN users u ON r.creator_id = u.id
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as invitation_count
          FROM rfq_invitations
          GROUP BY rfq_id
        ) inv_stats ON r.id = inv_stats.rfq_id
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as submission_count
          FROM rfq_submissions
          GROUP BY rfq_id
        ) sub_stats ON r.id = sub_stats.rfq_id
        WHERE r.id = $1
      `;

      const result = await query(rfqQuery, [id]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const rfq = result.rows[0];

      // Get invitations with vendor details
      const invitationsQuery = `
        SELECT 
          ri.*,
          v.name as vendor_name,
          v.contact_email as vendor_email,
          v.performance_score
        FROM rfq_invitations ri
        LEFT JOIN vendors v ON ri.vendor_id = v.id
        WHERE ri.rfq_id = $1
        ORDER BY ri.created_at ASC
      `;

      const invitationsResult = await query(invitationsQuery, [id]);
      rfq.invitations = invitationsResult.rows;

      // Get submissions with vendor details
      const submissionsQuery = `
        SELECT 
          rs.*,
          v.name as vendor_name,
          v.contact_email as vendor_email,
          v.performance_score
        FROM rfq_submissions rs
        LEFT JOIN vendors v ON rs.vendor_id = v.id
        WHERE rs.rfq_id = $1
        ORDER BY rs.submitted_at ASC
      `;

      const submissionsResult = await query(submissionsQuery, [id]);
      rfq.submissions = submissionsResult.rows;

      return rfq;
    } catch (error) {
      console.error('Error in RFQ.findById:', error);
      throw error;
    }
  }

  // Create new RFQ
  static async create(rfqData, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Validate input data
      const { error, value } = this.validateCreate(rfqData);
      if (error) {
        throw new Error(`Validation error: ${error.details.map(d => d.message).join(', ')}`);
      }

      const {
        title,
        description,
        items,
        due_date,
        selectedVendors,
        formConfig,
        terms,
        currency,
        aiSettings
      } = value;

      // Create RFQ
      const rfqQuery = `
        INSERT INTO rfqs (
          creator_id, title, description, items, due_date, form_config, 
          ai_settings, terms, currency, status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `;

      const rfqParams = [
        userId,
        title,
        description || '',
        JSON.stringify(items),
        due_date,
        JSON.stringify(formConfig),
        JSON.stringify(aiSettings),
        terms || '',
        currency,
        'active'
      ];

      const rfqResult = await client.query(rfqQuery, rfqParams);
      const rfq = rfqResult.rows[0];

      // Create invitations for selected vendors
      const { v4: uuidv4 } = require('uuid');
      const invitationPromises = selectedVendors.map(async (vendorId) => {
        const token = uuidv4(); // Generate unique token for each invitation
        const invitationQuery = `
          INSERT INTO rfq_invitations (rfq_id, vendor_id, token, expires_at, status, created_at)
          VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
          RETURNING *
        `;
        
        return await client.query(invitationQuery, [rfq.id, vendorId, token, due_date, 'pending']);
      });

      const invitationResults = await Promise.all(invitationPromises);
      const invitations = invitationResults.map(result => result.rows[0]);

      await commitTransaction(client);

      // Return RFQ with invitations
      return {
        ...rfq,
        invitations
      };
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in RFQ.create:', error);
      throw error;
    }
  }

  // Update RFQ
  static async update(id, updates, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Validate input data
      const { error, value } = this.validateUpdate(updates);
      if (error) {
        throw new Error(`Validation error: ${error.details.map(d => d.message).join(', ')}`);
      }

      // Check if RFQ exists and user has permission
      const existingRfq = await client.query(
        'SELECT * FROM rfqs WHERE id = $1 AND creator_id = $2',
        [id, userId]
      );

      if (existingRfq.rows.length === 0) {
        throw new Error('RFQ not found or access denied');
      }

      const currentRfq = existingRfq.rows[0];

      // Check if RFQ can be updated (not sent yet or still in draft)
      if (currentRfq.status === 'closed' || currentRfq.status === 'cancelled') {
        throw new Error('Cannot update closed or cancelled RFQ');
      }

      // Build dynamic update query
      const updateFields = [];
      const params = [id];
      let paramCount = 1;

      const allowedFields = [
        'title', 'description', 'items', 'due_date', 'form_config', 
        'ai_settings', 'terms', 'currency', 'status', 'allow_partial_selection', 'partial_selection_config'
      ];

      for (const field of allowedFields) {
        if (value[field] !== undefined) {
          paramCount++;
          if (field === 'items' || field === 'form_config' || field === 'ai_settings' || field === 'partial_selection_config') {
            updateFields.push(`${field} = $${paramCount}`);
            params.push(JSON.stringify(value[field]));
          } else {
            updateFields.push(`${field} = $${paramCount}`);
            params.push(value[field]);
          }
        }
      }

      if (updateFields.length === 0) {
        await rollbackTransaction(client);
        return currentRfq;
      }

      // Add updated_at
      updateFields.push('updated_at = CURRENT_TIMESTAMP');

      const updateQuery = `
        UPDATE rfqs 
        SET ${updateFields.join(', ')}
        WHERE id = $1
        RETURNING *
      `;

      const result = await client.query(updateQuery, params);
      const updatedRfq = result.rows[0];

      await commitTransaction(client);

      // Return updated RFQ with full details
      return await this.findById(id, userId);
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in RFQ.update:', error);
      throw error;
    }
  }

  // Delete RFQ (soft delete by setting status to cancelled)
  static async delete(id, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Check if RFQ exists and user has permission
      const existingRfq = await client.query(
        'SELECT * FROM rfqs WHERE id = $1 AND creator_id = $2',
        [id, userId]
      );

      if (existingRfq.rows.length === 0) {
        throw new Error('RFQ not found or access denied');
      }

      const currentRfq = existingRfq.rows[0];

      // Check if RFQ can be deleted
      if (currentRfq.status === 'in_progress' || currentRfq.status === 'closed') {
        throw new Error('Cannot delete RFQ with active submissions or that is already closed');
      }

      // Soft delete by updating status
      const result = await client.query(
        `UPDATE rfqs 
         SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
         WHERE id = $1
         RETURNING id, status`,
        [id]
      );

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in RFQ.delete:', error);
      throw error;
    }
  }

  // Send invitations (update RFQ status to 'sent')
  static async sendInvitations(id, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Check if RFQ exists and user has permission
      const existingRfq = await client.query(
        'SELECT * FROM rfqs WHERE id = $1 AND creator_id = $2',
        [id, userId]
      );

      if (existingRfq.rows.length === 0) {
        throw new Error('RFQ not found or access denied');
      }

      const currentRfq = existingRfq.rows[0];

      // Check if RFQ can be sent
      if (currentRfq.status !== 'draft') {
        throw new Error('RFQ can only be sent from draft status');
      }

      // Update RFQ status to sent
      const rfqResult = await client.query(
        `UPDATE rfqs 
         SET status = 'sent', updated_at = CURRENT_TIMESTAMP
         WHERE id = $1
         RETURNING *`,
        [id]
      );

      // Update invitation status to sent
      const invitationsResult = await client.query(
        `UPDATE rfq_invitations 
         SET status = 'sent', sent_at = CURRENT_TIMESTAMP
         WHERE rfq_id = $1
         RETURNING *`,
        [id]
      );

      await commitTransaction(client);

      return {
        rfq: rfqResult.rows[0],
        invitations: invitationsResult.rows
      };
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in RFQ.sendInvitations:', error);
      throw error;
    }
  }

  // Get RFQ analytics
  static async getAnalytics(id, userId) {
    try {
      // Check if RFQ exists and user has permission
      const rfqCheck = await query(
        'SELECT id FROM rfqs WHERE id = $1 AND creator_id = $2',
        [id, userId]
      );

      if (rfqCheck.rows.length === 0) {
        throw new Error('RFQ not found or access denied');
      }

      // Get or create analytics record
      let analyticsResult = await query(
        'SELECT * FROM rfq_analytics WHERE rfq_id = $1',
        [id]
      );

      if (analyticsResult.rows.length === 0) {
        // Calculate and insert analytics
        await this.calculateAnalytics(id);
        analyticsResult = await query(
          'SELECT * FROM rfq_analytics WHERE rfq_id = $1',
          [id]
        );
      }

      return analyticsResult.rows[0];
    } catch (error) {
      console.error('Error in RFQ.getAnalytics:', error);
      throw error;
    }
  }

  // Calculate and update analytics
  static async calculateAnalytics(rfqId) {
    const client = await beginTransaction();
    
    try {
      // Get RFQ details
      const rfqResult = await client.query('SELECT * FROM rfqs WHERE id = $1', [rfqId]);
      if (rfqResult.rows.length === 0) {
        throw new Error('RFQ not found');
      }

      const rfq = rfqResult.rows[0];

      // Get invitation statistics
      const invitationStats = await client.query(`
        SELECT 
          COUNT(*) as total_invitations,
          COUNT(CASE WHEN status = 'submitted' THEN 1 END) as total_submissions
        FROM rfq_invitations 
        WHERE rfq_id = $1
      `, [rfqId]);

      const { total_invitations, total_submissions } = invitationStats.rows[0];
      const response_rate = total_invitations > 0 ? (total_submissions / total_invitations) * 100 : 0;

      // Get submission statistics
      const submissionStats = await client.query(`
        SELECT 
          MIN(total_amount) as lowest_bid,
          MAX(total_amount) as highest_bid,
          AVG(total_amount) as average_bid,
          AVG(EXTRACT(EPOCH FROM (submitted_at - (
            SELECT sent_at FROM rfq_invitations ri 
            WHERE ri.rfq_id = rs.rfq_id AND ri.vendor_id = rs.vendor_id
          )))/3600) as avg_response_time_hours
        FROM rfq_submissions rs
        WHERE rfq_id = $1
      `, [rfqId]);

      const submissionData = submissionStats.rows[0];

      // Upsert analytics record
      const analyticsQuery = `
        INSERT INTO rfq_analytics (
          rfq_id, total_invitations, total_submissions, response_rate,
          average_response_time_hours, lowest_bid_amount, highest_bid_amount,
          average_bid_amount, calculated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
        ON CONFLICT (rfq_id) 
        DO UPDATE SET
          total_invitations = EXCLUDED.total_invitations,
          total_submissions = EXCLUDED.total_submissions,
          response_rate = EXCLUDED.response_rate,
          average_response_time_hours = EXCLUDED.average_response_time_hours,
          lowest_bid_amount = EXCLUDED.lowest_bid_amount,
          highest_bid_amount = EXCLUDED.highest_bid_amount,
          average_bid_amount = EXCLUDED.average_bid_amount,
          calculated_at = CURRENT_TIMESTAMP
        RETURNING *
      `;

      const result = await client.query(analyticsQuery, [
        rfqId,
        parseInt(total_invitations) || 0,
        parseInt(total_submissions) || 0,
        parseFloat(response_rate.toFixed(2)),
        submissionData.avg_response_time_hours ? parseFloat(submissionData.avg_response_time_hours.toFixed(2)) : null,
        submissionData.lowest_bid ? parseFloat(submissionData.lowest_bid) : null,
        submissionData.highest_bid ? parseFloat(submissionData.highest_bid) : null,
        submissionData.average_bid ? parseFloat(submissionData.average_bid) : null
      ]);

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in RFQ.calculateAnalytics:', error);
      throw error;
    }
  }

  // Get RFQ status statistics
  static async getStatusStatistics(userId) {
    try {
      const result = await query(`
        SELECT 
          status,
          COUNT(*) as count
        FROM rfqs
        WHERE creator_id = $1
        GROUP BY status
        ORDER BY status
      `, [userId]);

      return result.rows;
    } catch (error) {
      console.error('Error in RFQ.getStatusStatistics:', error);
      throw error;
    }
  }

  // Get audit history for RFQ
  static async getAuditHistory(id, userId) {
    try {
      // Check if RFQ exists and user has permission
      const rfqCheck = await query(
        'SELECT id FROM rfqs WHERE id = $1 AND creator_id = $2',
        [id, userId]
      );

      if (rfqCheck.rows.length === 0) {
        throw new Error('RFQ not found or access denied');
      }

      const result = await query(`
        SELECT 
          a.id, a.entity_type, a.entity_id, a.action, a.user_id,
          a.old_value, a.new_value, a.details, a.timestamp,
          u.email as user_email
        FROM audits a
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.entity_type IN ('rfqs', 'rfq_invitations', 'rfq_submissions') 
        AND (
          (a.entity_type = 'rfqs' AND a.entity_id = $1) OR
          (a.entity_type = 'rfq_invitations' AND a.entity_id IN (
            SELECT id FROM rfq_invitations WHERE rfq_id = $1
          )) OR
          (a.entity_type = 'rfq_submissions' AND a.entity_id IN (
            SELECT id FROM rfq_submissions WHERE rfq_id = $1
          ))
        )
        ORDER BY a.timestamp DESC
      `, [id]);

      return result.rows;
    } catch (error) {
      console.error('Error in RFQ.getAuditHistory:', error);
      throw error;
    }
  }

  // Cursor-based pagination for better performance with large datasets
  static async findAllWithCursor(filters = {}, cursor, limit = 10, userId) {
    try {
      let whereClause = 'WHERE 1=1';
      const params = [];
      let paramCount = 0;

      // Decode cursor (base64 encoded timestamp)
      let cursorDate = null;
      if (cursor) {
        try {
          const decodedCursor = Buffer.from(cursor, 'base64').toString('utf-8');
          cursorDate = new Date(decodedCursor);
          if (isNaN(cursorDate.getTime())) {
            throw new Error('Invalid cursor format');
          }
        } catch (err) {
          throw new Error('Invalid cursor');
        }
      }

      // Apply cursor condition
      if (cursorDate) {
        paramCount++;
        whereClause += ` AND r.created_at < $${paramCount}`;
        params.push(cursorDate.toISOString());
      }

      // Apply same filters as findAll
      if (filters.search) {
        paramCount++;
        whereClause += ` AND (r.title ILIKE $${paramCount} OR r.description ILIKE $${paramCount} OR EXISTS (
          SELECT 1 FROM jsonb_array_elements(r.items) AS item 
          WHERE item->>'name' ILIKE $${paramCount} OR item->>'description' ILIKE $${paramCount}
        ))`;
        params.push(`%${filters.search}%`);
      }

      if (filters.status) {
        paramCount++;
        whereClause += ` AND r.status = $${paramCount}`;
        params.push(filters.status);
      }

      if (filters.creator_id) {
        paramCount++;
        whereClause += ` AND r.creator_id = $${paramCount}`;
        params.push(filters.creator_id);
      }

      if (filters.due_date_from) {
        paramCount++;
        whereClause += ` AND r.due_date >= $${paramCount}`;
        params.push(filters.due_date_from);
      }

      if (filters.due_date_to) {
        paramCount++;
        whereClause += ` AND r.due_date <= $${paramCount}`;
        params.push(filters.due_date_to);
      }

      if (filters.currency) {
        paramCount++;
        whereClause += ` AND r.currency = $${paramCount}`;
        params.push(filters.currency);
      }

      if (filters.category) {
        paramCount++;
        whereClause += ` AND EXISTS (
          SELECT 1 FROM jsonb_array_elements(r.items) AS item 
          WHERE item->>'category' = $${paramCount}
        )`;
        params.push(filters.category);
      }

      if (filters.vendor_id) {
        paramCount++;
        whereClause += ` AND EXISTS (
          SELECT 1 FROM rfq_invitations ri 
          WHERE ri.rfq_id = r.id AND ri.vendor_id = $${paramCount}
        )`;
        params.push(filters.vendor_id);
      }

      // Fetch one extra record to determine if there's a next page
      paramCount++;
      const limitParam = paramCount;
      
      const rfqsQuery = `
        SELECT 
          r.id,
          r.title,
          r.description,
          r.status,
          r.due_date,
          r.currency,
          r.creator_id,
          u.email as creator_email,
          jsonb_array_length(r.items) as item_count,
          r.created_at,
          r.updated_at,
          COALESCE(inv_stats.invitation_count, 0) as invitation_count,
          COALESCE(sub_stats.submission_count, 0) as submission_count,
          CASE 
            WHEN COALESCE(inv_stats.invitation_count, 0) > 0 
            THEN ROUND((COALESCE(sub_stats.submission_count, 0)::DECIMAL / inv_stats.invitation_count) * 100, 2)
            ELSE 0 
          END as response_rate
        FROM rfqs r
        LEFT JOIN users u ON r.creator_id = u.id
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as invitation_count
          FROM rfq_invitations
          GROUP BY rfq_id
        ) inv_stats ON r.id = inv_stats.rfq_id
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as submission_count
          FROM rfq_submissions
          GROUP BY rfq_id
        ) sub_stats ON r.id = sub_stats.rfq_id
        ${whereClause}
        ORDER BY r.created_at DESC
        LIMIT $${limitParam}
      `;

      params.push(limit + 1);
      const result = await query(rfqsQuery, params);

      const hasNext = result.rows.length > limit;
      const rfqs = hasNext ? result.rows.slice(0, limit) : result.rows;

      // Generate next cursor from the last item
      let nextCursor = null;
      let previousCursor = null;
      
      if (hasNext && rfqs.length > 0) {
        const lastItem = rfqs[rfqs.length - 1];
        nextCursor = Buffer.from(lastItem.created_at).toString('base64');
      }

      if (cursor) {
        // For previous cursor, we'd need to implement reverse pagination
        // This is a simplified version
        previousCursor = null;
      }

      return {
        rfqs,
        pagination: {
          limit,
          hasNext,
          hasPrevious: !!cursor,
          nextCursor,
          previousCursor
        }
      };
    } catch (error) {
      console.error('Error in RFQ.findAllWithCursor:', error);
      throw error;
    }
  }

  // Bulk update RFQs
  static async bulkUpdate(rfqIds, updates, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Check if all RFQs exist and user has permission
      const existingRfqs = await client.query(
        'SELECT id, status FROM rfqs WHERE id = ANY($1) AND creator_id = $2',
        [rfqIds, userId]
      );

      if (existingRfqs.rows.length !== rfqIds.length) {
        throw new Error('Some RFQs not found or access denied');
      }

      // Check if any RFQs cannot be updated
      const invalidStatuses = existingRfqs.rows.filter(rfq => 
        rfq.status === 'closed' || rfq.status === 'cancelled'
      );

      if (invalidStatuses.length > 0) {
        throw new Error(`Cannot update RFQs with status: ${invalidStatuses.map(r => r.status).join(', ')}`);
      }

      // Build dynamic update query
      const updateFields = [];
      const params = [rfqIds];
      let paramCount = 1;

      const allowedFields = ['status', 'due_date', 'currency'];

      for (const field of allowedFields) {
        if (updates[field] !== undefined) {
          paramCount++;
          updateFields.push(`${field} = $${paramCount}`);
          params.push(updates[field]);
        }
      }

      if (updateFields.length === 0) {
        await rollbackTransaction(client);
        return { updated_count: 0, rfqs: [] };
      }

      // Add updated_at
      updateFields.push('updated_at = CURRENT_TIMESTAMP');
      paramCount++;
      params.push(userId);

      const updateQuery = `
        UPDATE rfqs 
        SET ${updateFields.join(', ')}
        WHERE id = ANY($1) AND creator_id = $${paramCount}
        RETURNING id, title, status, updated_at
      `;

      const result = await client.query(updateQuery, params);

      await commitTransaction(client);

      return {
        updated_count: result.rows.length,
        rfqs: result.rows
      };
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in RFQ.bulkUpdate:', error);
      throw error;
    }
  }

  // Bulk delete RFQs
  static async bulkDelete(rfqIds, force, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Check if all RFQs exist and user has permission
      const existingRfqs = await client.query(
        'SELECT id, status FROM rfqs WHERE id = ANY($1) AND creator_id = $2',
        [rfqIds, userId]
      );

      if (existingRfqs.rows.length !== rfqIds.length) {
        throw new Error('Some RFQs not found or access denied');
      }

      // Check if any RFQs cannot be deleted (unless force is true)
      if (!force) {
        const invalidStatuses = existingRfqs.rows.filter(rfq => 
          rfq.status === 'in_progress' || rfq.status === 'closed'
        );

        if (invalidStatuses.length > 0) {
          throw new Error(`Cannot delete RFQs with status: ${invalidStatuses.map(r => r.status).join(', ')}`);
        }
      }

      // Soft delete by updating status to cancelled
      const result = await client.query(
        `UPDATE rfqs 
         SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
         WHERE id = ANY($1) AND creator_id = $2
         RETURNING id, title, status`,
        [rfqIds, userId]
      );

      await commitTransaction(client);

      return {
        deleted_count: result.rows.length,
        rfqs: result.rows
      };
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in RFQ.bulkDelete:', error);
      throw error;
    }
  }

  // Advanced search with full-text search capabilities
  static async advancedSearch(searchParams, userId) {
    try {
      const startTime = Date.now();
      const { query: searchQuery, filters = {}, page = 1, limit = 20, sort_by = 'relevance', sort_order = 'desc' } = searchParams;

      let whereClause = 'WHERE 1=1';
      const params = [];
      let paramCount = 0;

      // Full-text search
      paramCount++;
      whereClause += ` AND (
        r.title ILIKE $${paramCount} OR 
        r.description ILIKE $${paramCount} OR
        EXISTS (
          SELECT 1 FROM jsonb_array_elements(r.items) AS item 
          WHERE item->>'name' ILIKE $${paramCount} OR item->>'description' ILIKE $${paramCount}
        )
      )`;
      params.push(`%${searchQuery}%`);

      // Apply filters
      if (filters.status && filters.status.length > 0) {
        paramCount++;
        whereClause += ` AND r.status = ANY($${paramCount})`;
        params.push(filters.status);
      }

      if (filters.date_range) {
        if (filters.date_range.start) {
          paramCount++;
          whereClause += ` AND r.created_at >= $${paramCount}`;
          params.push(filters.date_range.start);
        }
        if (filters.date_range.end) {
          paramCount++;
          whereClause += ` AND r.created_at <= $${paramCount}`;
          params.push(filters.date_range.end);
        }
      }

      if (filters.creator_ids && filters.creator_ids.length > 0) {
        paramCount++;
        whereClause += ` AND r.creator_id = ANY($${paramCount})`;
        params.push(filters.creator_ids);
      }

      if (filters.vendor_ids && filters.vendor_ids.length > 0) {
        paramCount++;
        whereClause += ` AND EXISTS (
          SELECT 1 FROM rfq_invitations ri 
          WHERE ri.rfq_id = r.id AND ri.vendor_id = ANY($${paramCount})
        )`;
        params.push(filters.vendor_ids);
      }

      if (filters.categories && filters.categories.length > 0) {
        paramCount++;
        whereClause += ` AND EXISTS (
          SELECT 1 FROM jsonb_array_elements(r.items) AS item 
          WHERE item->>'category' = ANY($${paramCount})
        )`;
        params.push(filters.categories);
      }

      if (filters.currencies && filters.currencies.length > 0) {
        paramCount++;
        whereClause += ` AND r.currency = ANY($${paramCount})`;
        params.push(filters.currencies);
      }

      // Count total records
      const countQuery = `
        SELECT COUNT(*) as total
        FROM rfqs r
        ${whereClause}
      `;
      const countResult = await query(countQuery, params);
      const total = parseInt(countResult.rows[0].total);

      // Calculate pagination
      const offset = (page - 1) * limit;
      const totalPages = Math.ceil(total / limit);

      // Build ORDER BY clause
      let orderBy = 'ORDER BY ';
      if (sort_by === 'relevance') {
        // Simple relevance scoring based on title match
        orderBy += `
          CASE 
            WHEN r.title ILIKE $1 THEN 3
            WHEN r.description ILIKE $1 THEN 2
            ELSE 1
          END DESC, r.created_at DESC
        `;
      } else {
        switch (sort_by) {
          case 'title':
            orderBy += 'r.title';
            break;
          case 'due_date':
            orderBy += 'r.due_date';
            break;
          case 'updated_at':
            orderBy += 'r.updated_at';
            break;
          default:
            orderBy += 'r.created_at';
        }
        orderBy += ` ${sort_order.toUpperCase()}`;
      }

      // Fetch RFQs
      paramCount++;
      const limitParam = paramCount;
      paramCount++;
      const offsetParam = paramCount;
      
      const rfqsQuery = `
        SELECT 
          r.id,
          r.title,
          r.description,
          r.status,
          r.due_date,
          r.currency,
          r.creator_id,
          u.email as creator_email,
          jsonb_array_length(r.items) as item_count,
          r.created_at,
          r.updated_at,
          COALESCE(inv_stats.invitation_count, 0) as invitation_count,
          COALESCE(sub_stats.submission_count, 0) as submission_count,
          CASE 
            WHEN COALESCE(inv_stats.invitation_count, 0) > 0 
            THEN ROUND((COALESCE(sub_stats.submission_count, 0)::DECIMAL / inv_stats.invitation_count) * 100, 2)
            ELSE 0 
          END as response_rate
        FROM rfqs r
        LEFT JOIN users u ON r.creator_id = u.id
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as invitation_count
          FROM rfq_invitations
          GROUP BY rfq_id
        ) inv_stats ON r.id = inv_stats.rfq_id
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as submission_count
          FROM rfq_submissions
          GROUP BY rfq_id
        ) sub_stats ON r.id = sub_stats.rfq_id
        ${whereClause}
        ${orderBy}
        LIMIT $${limitParam} OFFSET $${offsetParam}
      `;

      params.push(limit, offset);
      const result = await query(rfqsQuery, params);

      const endTime = Date.now();

      return {
        rfqs: result.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrevious: page > 1
        },
        search_time_ms: endTime - startTime
      };
    } catch (error) {
      console.error('Error in RFQ.advancedSearch:', error);
      throw error;
    }
  }

  // Get summary statistics
  static async getSummaryStatistics(userId) {
    try {
      const result = await query(`
        SELECT 
          COUNT(*) as total_rfqs,
          COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_count,
          COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count,
          COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_count,
          COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_count,
          COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_count,
          AVG(CASE 
            WHEN inv_stats.invitation_count > 0 
            THEN (sub_stats.submission_count::DECIMAL / inv_stats.invitation_count) * 100
            ELSE 0 
          END) as avg_response_rate,
          COUNT(CASE WHEN due_date < CURRENT_DATE AND status NOT IN ('closed', 'cancelled') THEN 1 END) as overdue_count
        FROM rfqs r
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as invitation_count
          FROM rfq_invitations
          GROUP BY rfq_id
        ) inv_stats ON r.id = inv_stats.rfq_id
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as submission_count
          FROM rfq_submissions
          GROUP BY rfq_id
        ) sub_stats ON r.id = sub_stats.rfq_id
        WHERE r.creator_id = $1
      `, [userId]);

      return result.rows[0];
    } catch (error) {
      console.error('Error in RFQ.getSummaryStatistics:', error);
      throw error;
    }
  }

  // Export RFQs data
  static async exportData(exportParams, userId) {
    try {
      const { format = 'csv', filters = {}, include_submissions = false, include_analytics = false } = exportParams;

      let whereClause = 'WHERE r.creator_id = $1';
      const params = [userId];
      let paramCount = 1;

      // Apply filters
      if (filters.status && filters.status.length > 0) {
        paramCount++;
        whereClause += ` AND r.status = ANY($${paramCount})`;
        params.push(filters.status);
      }

      if (filters.date_range) {
        if (filters.date_range.start) {
          paramCount++;
          whereClause += ` AND r.created_at >= $${paramCount}`;
          params.push(filters.date_range.start);
        }
        if (filters.date_range.end) {
          paramCount++;
          whereClause += ` AND r.created_at <= $${paramCount}`;
          params.push(filters.date_range.end);
        }
      }

      if (filters.creator_ids && filters.creator_ids.length > 0) {
        paramCount++;
        whereClause += ` AND r.creator_id = ANY($${paramCount})`;
        params.push(filters.creator_ids);
      }

      // Base query
      let selectClause = `
        SELECT 
          r.id,
          r.title,
          r.description,
          r.status,
          r.due_date,
          r.currency,
          u.email as creator_email,
          jsonb_array_length(r.items) as item_count,
          r.created_at,
          r.updated_at,
          COALESCE(inv_stats.invitation_count, 0) as invitation_count,
          COALESCE(sub_stats.submission_count, 0) as submission_count,
          CASE 
            WHEN COALESCE(inv_stats.invitation_count, 0) > 0 
            THEN ROUND((COALESCE(sub_stats.submission_count, 0)::DECIMAL / inv_stats.invitation_count) * 100, 2)
            ELSE 0 
          END as response_rate
      `;

      if (include_analytics) {
        selectClause += `,
          ra.average_response_time_hours,
          ra.lowest_bid_amount,
          ra.highest_bid_amount,
          ra.average_bid_amount
        `;
      }

      const exportQuery = `
        ${selectClause}
        FROM rfqs r
        LEFT JOIN users u ON r.creator_id = u.id
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as invitation_count
          FROM rfq_invitations
          GROUP BY rfq_id
        ) inv_stats ON r.id = inv_stats.rfq_id
        LEFT JOIN (
          SELECT rfq_id, COUNT(*) as submission_count
          FROM rfq_submissions
          GROUP BY rfq_id
        ) sub_stats ON r.id = sub_stats.rfq_id
        ${include_analytics ? 'LEFT JOIN rfq_analytics ra ON r.id = ra.rfq_id' : ''}
        ${whereClause}
        ORDER BY r.created_at DESC
      `;

      const result = await query(exportQuery, params);

      // Format data based on requested format
      if (format === 'json') {
        return JSON.stringify(result.rows, null, 2);
      } else if (format === 'csv') {
        // Simple CSV conversion
        if (result.rows.length === 0) {
          return '';
        }

        const headers = Object.keys(result.rows[0]);
        const csvRows = [headers.join(',')];
        
        result.rows.forEach(row => {
          const values = headers.map(header => {
            const value = row[header];
            if (value === null || value === undefined) {
              return '';
            }
            // Escape commas and quotes
            const stringValue = String(value);
            if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
              return `"${stringValue.replace(/"/g, '""')}"`;
            }
            return stringValue;
          });
          csvRows.push(values.join(','));
        });

        return csvRows.join('\n');
      } else {
        // For XLSX, we'd need a library like xlsx
        // For now, return JSON format
        return JSON.stringify(result.rows, null, 2);
      }
    } catch (error) {
      console.error('Error in RFQ.exportData:', error);
      throw error;
    }
  }
}

module.exports = RFQ;