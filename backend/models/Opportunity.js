const {
  query,
  queryWithUser,
  beginTransaction,
  commitTransaction,
  rollbackTransaction,
} = require("../config/database");

class Opportunity {
  /**
   * Get all opportunities with filtering and pagination
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Records per page
   * @returns {Object} Paginated opportunities
   */
  static async findAll(filters = {}, page = 1, limit = 10) {
    let whereClause = "WHERE o.is_deleted = FALSE";
    const params = [];
    let paramCount = 0;

    // Apply filters
    if (filters.search) {
      paramCount++;
      whereClause += ` AND (o.name ILIKE $${paramCount} OR o.description ILIKE $${paramCount} OR o.next_step ILIKE $${paramCount})`;
      params.push(`%${filters.search}%`);
    }

    if (filters.account_id) {
      paramCount++;
      whereClause += ` AND o.account_id = $${paramCount}`;
      params.push(filters.account_id);
    }

    if (filters.stage_name) {
      paramCount++;
      whereClause += ` AND o.stage_name = $${paramCount}`;
      params.push(filters.stage_name);
    }

    if (filters.type) {
      paramCount++;
      whereClause += ` AND o.type = $${paramCount}`;
      params.push(filters.type);
    }

    if (filters.lead_source) {
      paramCount++;
      whereClause += ` AND o.lead_source = $${paramCount}`;
      params.push(filters.lead_source);
    }

    if (filters.forecast_category) {
      paramCount++;
      whereClause += ` AND o.forecast_category = $${paramCount}`;
      params.push(filters.forecast_category);
    }

    if (filters.owner_id) {
      paramCount++;
      whereClause += ` AND o.owner_id = $${paramCount}`;
      params.push(filters.owner_id);
    }

    if (filters.is_closed !== undefined) {
      paramCount++;
      whereClause += ` AND o.is_closed = $${paramCount}`;
      params.push(filters.is_closed);
    }

    if (filters.is_won !== undefined) {
      paramCount++;
      whereClause += ` AND o.is_won = $${paramCount}`;
      params.push(filters.is_won);
    }

    if (filters.is_private !== undefined) {
      paramCount++;
      whereClause += ` AND o.is_private = $${paramCount}`;
      params.push(filters.is_private);
    }

    if (filters.amount_min) {
      paramCount++;
      whereClause += ` AND o.amount >= $${paramCount}`;
      params.push(filters.amount_min);
    }

    if (filters.amount_max) {
      paramCount++;
      whereClause += ` AND o.amount <= $${paramCount}`;
      params.push(filters.amount_max);
    }

    if (filters.close_date_from) {
      paramCount++;
      whereClause += ` AND o.close_date >= $${paramCount}`;
      params.push(filters.close_date_from);
    }

    if (filters.close_date_to) {
      paramCount++;
      whereClause += ` AND o.close_date <= $${paramCount}`;
      params.push(filters.close_date_to);
    }

    if (filters.probability_min) {
      paramCount++;
      whereClause += ` AND o.probability >= $${paramCount}`;
      params.push(filters.probability_min);
    }

    if (filters.probability_max) {
      paramCount++;
      whereClause += ` AND o.probability <= $${paramCount}`;
      params.push(filters.probability_max);
    }

    if (filters.created_after) {
      paramCount++;
      whereClause += ` AND o.created_at >= $${paramCount}`;
      params.push(filters.created_after);
    }

    if (filters.created_before) {
      paramCount++;
      whereClause += ` AND o.created_at <= $${paramCount}`;
      params.push(filters.created_before);
    }

    // Count total records
    const countQuery = `
      SELECT COUNT(*) 
      FROM opportunities o
      LEFT JOIN accounts a ON o.account_id = a.id
      LEFT JOIN users u ON o.owner_id = u.id
      ${whereClause}
    `;
    const countResult = await query(countQuery, params);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const offset = (page - 1) * limit;
    paramCount++;
    const offsetParam = paramCount;
    paramCount++;
    const limitParam = paramCount;
    params.push(offset, limit);

    const selectQuery = `
      SELECT 
        o.*,
        a.name as account_name,
        a.type as account_type,
        u.email as owner_email,
        CONCAT(u.email) as owner_name
      FROM opportunities o
      LEFT JOIN accounts a ON o.account_id = a.id
      LEFT JOIN users u ON o.owner_id = u.id
      ${whereClause}
      ORDER BY o.created_at DESC
      OFFSET $${offsetParam} LIMIT $${limitParam}
    `;

    const result = await query(selectQuery, params);

    return {
      opportunities: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Find opportunity by ID with related data
   * @param {number} id - Opportunity ID
   * @returns {Object|null} Opportunity with related data
   */
  static async findById(id) {
    const opportunityQuery = `
      SELECT 
        o.*,
        a.name as account_name,
        a.type as account_type,
        a.industry as account_industry,
        u.email as owner_email,
        cb.email as created_by_email,
        mb.email as last_modified_by_email
      FROM opportunities o
      LEFT JOIN accounts a ON o.account_id = a.id
      LEFT JOIN users u ON o.owner_id = u.id
      LEFT JOIN users cb ON o.created_by_id = cb.id
      LEFT JOIN users mb ON o.last_modified_by_id = mb.id
      WHERE o.id = $1 AND o.is_deleted = FALSE
    `;

    const result = await query(opportunityQuery, [id]);
    
    if (result.rows.length === 0) {
      return null;
    }

    const opportunity = result.rows[0];

    // Get associated contacts
    const contactsQuery = `
      SELECT 
        c.*,
        oc.role,
        oc.is_primary
      FROM opportunity_contacts oc
      JOIN contacts c ON oc.contact_id = c.id
      WHERE oc.opportunity_id = $1 AND c.is_deleted = FALSE
      ORDER BY oc.is_primary DESC, c.last_name, c.first_name
    `;
    
    const contactsResult = await query(contactsQuery, [id]);
    opportunity.contacts = contactsResult.rows;

    return opportunity;
  }

  /**
   * Create a new opportunity
   * @param {Object} opportunityData - Opportunity data
   * @param {number} userId - User creating the opportunity
   * @returns {Object} Created opportunity
   */
  static async create(opportunityData, userId = 1) {
    const client = await beginTransaction();
    
    try {
      const {
        name,
        account_id,
        amount,
        currency = 'USD',
        close_date,
        stage_name = 'Prospecting',
        probability,
        type,
        lead_source,
        forecast_category = 'Pipeline',
        is_private = false,
        description,
        next_step,
        owner_id,
        campaign_id,
        has_opportunity_line_item = false,
        items = [],
        integration_id,
        custom_fields = {},
        status = 'OPEN',
        contacts = []
      } = opportunityData;

      // Insert opportunity
      const insertQuery = `
        INSERT INTO opportunities (
          name, account_id, amount, currency, close_date, stage_name, probability,
          type, lead_source, forecast_category, is_private, description, next_step,
          owner_id, campaign_id, has_opportunity_line_item, items, integration_id,
          custom_fields, status, created_by_id, last_modified_by_id
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $21
        ) RETURNING *
      `;

      const values = [
        name, account_id, amount, currency, close_date, stage_name, probability,
        type, lead_source, forecast_category, is_private, description, next_step,
        owner_id, campaign_id, has_opportunity_line_item, JSON.stringify(items),
        integration_id, JSON.stringify(custom_fields), status, userId
      ];

      const result = await client.query(insertQuery, values);
      const opportunity = result.rows[0];

      // Add contacts if provided
      if (contacts && contacts.length > 0) {
        for (const contact of contacts) {
          await client.query(
            `INSERT INTO opportunity_contacts (opportunity_id, contact_id, role, is_primary, created_by_id) 
             VALUES ($1, $2, $3, $4, $5)`,
            [opportunity.id, contact.contact_id, contact.role, contact.is_primary || false, userId]
          );
        }
      }

      await commitTransaction(client);
      return await this.findById(opportunity.id);
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  /**
   * Update an opportunity
   * @param {number} id - Opportunity ID
   * @param {Object} opportunityData - Updated data
   * @param {number} userId - User updating the opportunity
   * @returns {Object} Updated opportunity
   */
  static async update(id, opportunityData, userId = 1) {
    const client = await beginTransaction();
    
    try {
      const updateFields = [];
      const values = [];
      let paramCount = 0;

      // Build dynamic update query
      Object.keys(opportunityData).forEach(key => {
        if (key !== 'contacts' && opportunityData[key] !== undefined) {
          paramCount++;
          updateFields.push(`${key} = $${paramCount}`);
          
          if (key === 'items' || key === 'custom_fields') {
            values.push(JSON.stringify(opportunityData[key]));
          } else {
            values.push(opportunityData[key]);
          }
        }
      });

      if (updateFields.length > 0) {
        paramCount++;
        updateFields.push(`last_modified_by_id = $${paramCount}`);
        values.push(userId);

        paramCount++;
        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
        values.push(id);

        const updateQuery = `
          UPDATE opportunities 
          SET ${updateFields.join(', ')}
          WHERE id = $${paramCount} AND is_deleted = FALSE
          RETURNING *
        `;

        await client.query(updateQuery, values);
      }

      // Update contacts if provided
      if (opportunityData.contacts !== undefined) {
        // Remove existing contacts
        await client.query(
          'DELETE FROM opportunity_contacts WHERE opportunity_id = $1',
          [id]
        );

        // Add new contacts
        if (opportunityData.contacts.length > 0) {
          for (const contact of opportunityData.contacts) {
            await client.query(
              `INSERT INTO opportunity_contacts (opportunity_id, contact_id, role, is_primary, created_by_id) 
               VALUES ($1, $2, $3, $4, $5)`,
              [id, contact.contact_id, contact.role, contact.is_primary || false, userId]
            );
          }
        }
      }

      await commitTransaction(client);
      return await this.findById(id);
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  /**
   * Soft delete an opportunity
   * @param {number} id - Opportunity ID
   * @param {number} userId - User deleting the opportunity
   * @returns {boolean} Success status
   */
  static async delete(id, userId = 1) {
    const deleteQuery = `
      UPDATE opportunities 
      SET is_deleted = TRUE, last_modified_by_id = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND is_deleted = FALSE
      RETURNING id
    `;

    const result = await query(deleteQuery, [id, userId]);
    return result.rows.length > 0;
  }

  /**
   * Search opportunities with advanced filters
   * @param {Object} searchParams - Search parameters
   * @returns {Array} Matching opportunities
   */
  static async search(searchParams) {
    const {
      searchTerm,
      stage_name,
      type,
      lead_source,
      forecast_category,
      owner_id,
      account_id,
      amount_min,
      amount_max,
      close_date_from,
      close_date_to,
      is_closed,
      is_won,
      limit = 50
    } = searchParams;

    let whereClause = "WHERE o.is_deleted = FALSE";
    const params = [];
    let paramCount = 0;

    if (searchTerm) {
      paramCount++;
      whereClause += ` AND (o.name ILIKE $${paramCount} OR o.description ILIKE $${paramCount} OR a.name ILIKE $${paramCount})`;
      params.push(`%${searchTerm}%`);
    }

    // Add other filters similar to findAll method
    if (stage_name) {
      paramCount++;
      whereClause += ` AND o.stage_name = $${paramCount}`;
      params.push(stage_name);
    }

    if (type) {
      paramCount++;
      whereClause += ` AND o.type = $${paramCount}`;
      params.push(type);
    }

    if (account_id) {
      paramCount++;
      whereClause += ` AND o.account_id = $${paramCount}`;
      params.push(account_id);
    }

    if (owner_id) {
      paramCount++;
      whereClause += ` AND o.owner_id = $${paramCount}`;
      params.push(owner_id);
    }

    if (amount_min) {
      paramCount++;
      whereClause += ` AND o.amount >= $${paramCount}`;
      params.push(amount_min);
    }

    if (amount_max) {
      paramCount++;
      whereClause += ` AND o.amount <= $${paramCount}`;
      params.push(amount_max);
    }

    if (close_date_from) {
      paramCount++;
      whereClause += ` AND o.close_date >= $${paramCount}`;
      params.push(close_date_from);
    }

    if (close_date_to) {
      paramCount++;
      whereClause += ` AND o.close_date <= $${paramCount}`;
      params.push(close_date_to);
    }

    if (is_closed !== undefined) {
      paramCount++;
      whereClause += ` AND o.is_closed = $${paramCount}`;
      params.push(is_closed);
    }

    if (is_won !== undefined) {
      paramCount++;
      whereClause += ` AND o.is_won = $${paramCount}`;
      params.push(is_won);
    }

    paramCount++;
    params.push(limit);

    const searchQuery = `
      SELECT 
        o.*,
        a.name as account_name,
        a.type as account_type,
        u.email as owner_email
      FROM opportunities o
      LEFT JOIN accounts a ON o.account_id = a.id
      LEFT JOIN users u ON o.owner_id = u.id
      ${whereClause}
      ORDER BY o.updated_at DESC
      LIMIT $${paramCount}
    `;

    const result = await query(searchQuery, params);
    return result.rows;
  }

  /**
   * Get pipeline data for kanban view
   * @param {Object} filters - Filter criteria
   * @returns {Object} Pipeline data grouped by stage
   */
  static async getPipelineData(filters = {}) {
    let whereClause = "WHERE o.is_deleted = FALSE AND o.is_closed = FALSE";
    const params = [];
    let paramCount = 0;

    // Apply filters
    if (filters.owner_id) {
      paramCount++;
      whereClause += ` AND o.owner_id = $${paramCount}`;
      params.push(filters.owner_id);
    }

    if (filters.account_id) {
      paramCount++;
      whereClause += ` AND o.account_id = $${paramCount}`;
      params.push(filters.account_id);
    }

    if (filters.type) {
      paramCount++;
      whereClause += ` AND o.type = $${paramCount}`;
      params.push(filters.type);
    }

    const pipelineQuery = `
      SELECT 
        o.stage_name,
        COUNT(*) as opportunity_count,
        COALESCE(SUM(o.amount), 0) as total_amount,
        COALESCE(AVG(o.amount), 0) as avg_amount,
        COALESCE(SUM(o.amount * o.probability / 100), 0) as weighted_amount,
        json_agg(
          json_build_object(
            'id', o.id,
            'name', o.name,
            'amount', o.amount,
            'probability', o.probability,
            'close_date', o.close_date,
            'account_name', a.name,
            'owner_email', u.email
          ) ORDER BY o.amount DESC NULLS LAST
        ) as opportunities
      FROM opportunities o
      LEFT JOIN accounts a ON o.account_id = a.id
      LEFT JOIN users u ON o.owner_id = u.id
      ${whereClause}
      GROUP BY o.stage_name
      ORDER BY 
        CASE o.stage_name
          WHEN 'Prospecting' THEN 1
          WHEN 'Qualification' THEN 2
          WHEN 'Needs Analysis' THEN 3
          WHEN 'Value Proposition' THEN 4
          WHEN 'Id. Decision Makers' THEN 5
          WHEN 'Perception Analysis' THEN 6
          WHEN 'Proposal/Price Quote' THEN 7
          WHEN 'Negotiation/Review' THEN 8
          ELSE 9
        END
    `;

    const result = await query(pipelineQuery, params);
    return result.rows;
  }

  /**
   * Get opportunity analytics
   * @param {Object} filters - Filter criteria
   * @returns {Object} Analytics data
   */
  static async getAnalytics(filters = {}) {
    let whereClause = "WHERE o.is_deleted = FALSE";
    const params = [];
    let paramCount = 0;

    // Apply date filters
    if (filters.date_from) {
      paramCount++;
      whereClause += ` AND o.created_at >= $${paramCount}`;
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      paramCount++;
      whereClause += ` AND o.created_at <= $${paramCount}`;
      params.push(filters.date_to);
    }

    if (filters.owner_id) {
      paramCount++;
      whereClause += ` AND o.owner_id = $${paramCount}`;
      params.push(filters.owner_id);
    }

    const analyticsQuery = `
      SELECT 
        COUNT(*) as total_opportunities,
        COUNT(CASE WHEN o.is_closed = TRUE THEN 1 END) as closed_opportunities,
        COUNT(CASE WHEN o.is_won = TRUE THEN 1 END) as won_opportunities,
        COUNT(CASE WHEN o.is_closed = TRUE AND o.is_won = FALSE THEN 1 END) as lost_opportunities,
        COALESCE(SUM(o.amount), 0) as total_pipeline_value,
        COALESCE(SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END), 0) as won_amount,
        COALESCE(SUM(o.amount * o.probability / 100), 0) as weighted_pipeline,
        COALESCE(AVG(o.amount), 0) as avg_opportunity_size,
        COALESCE(AVG(CASE WHEN o.is_closed = TRUE THEN o.probability END), 0) as avg_close_probability,
        ROUND(
          CASE 
            WHEN COUNT(CASE WHEN o.is_closed = TRUE THEN 1 END) > 0 
            THEN (COUNT(CASE WHEN o.is_won = TRUE THEN 1 END)::numeric / COUNT(CASE WHEN o.is_closed = TRUE THEN 1 END)::numeric) * 100
            ELSE 0 
          END, 2
        ) as win_rate
      FROM opportunities o
      ${whereClause}
    `;

    const result = await query(analyticsQuery, params);
    return result.rows[0];
  }

  /**
   * Validate opportunity data
   * @param {Object} opportunityData - Data to validate
   * @returns {Array} Validation errors
   */
  static validateOpportunityData(opportunityData) {
    const errors = [];

    // Required fields
    if (!opportunityData.name || opportunityData.name.trim() === '') {
      errors.push({ field: 'name', message: 'Opportunity name is required' });
    }

    if (!opportunityData.account_id) {
      errors.push({ field: 'account_id', message: 'Account is required' });
    }

    // Validate amount
    if (opportunityData.amount !== null && opportunityData.amount !== undefined) {
      if (isNaN(opportunityData.amount) || opportunityData.amount < 0) {
        errors.push({ field: 'amount', message: 'Amount must be a positive number' });
      }
    }

    // Validate probability
    if (opportunityData.probability !== null && opportunityData.probability !== undefined) {
      if (isNaN(opportunityData.probability) || opportunityData.probability < 0 || opportunityData.probability > 100) {
        errors.push({ field: 'probability', message: 'Probability must be between 0 and 100' });
      }
    }

    // Validate close date
    if (opportunityData.close_date) {
      const closeDate = new Date(opportunityData.close_date);
      if (isNaN(closeDate.getTime())) {
        errors.push({ field: 'close_date', message: 'Invalid close date format' });
      }
    }

    // Validate stage consistency
    if (opportunityData.stage_name) {
      const validStages = [
        'Prospecting', 'Qualification', 'Needs Analysis', 'Value Proposition',
        'Id. Decision Makers', 'Perception Analysis', 'Proposal/Price Quote',
        'Negotiation/Review', 'Closed Won', 'Closed Lost'
      ];
      
      if (!validStages.includes(opportunityData.stage_name)) {
        errors.push({ field: 'stage_name', message: 'Invalid stage name' });
      }
    }

    return errors;
  }

  /**
   * Get opportunity stages with metadata
   * @returns {Array} Available stages
   */
  static getStages() {
    return [
      { name: 'Prospecting', probability: 10, order: 1 },
      { name: 'Qualification', probability: 20, order: 2 },
      { name: 'Needs Analysis', probability: 30, order: 3 },
      { name: 'Value Proposition', probability: 40, order: 4 },
      { name: 'Id. Decision Makers', probability: 50, order: 5 },
      { name: 'Perception Analysis', probability: 60, order: 6 },
      { name: 'Proposal/Price Quote', probability: 70, order: 7 },
      { name: 'Negotiation/Review', probability: 80, order: 8 },
      { name: 'Closed Won', probability: 100, order: 9 },
      { name: 'Closed Lost', probability: 0, order: 10 }
    ];
  }

  /**
   * Get opportunity types
   * @returns {Array} Available types
   */
  static getTypes() {
    return [
      'New Customer',
      'Existing Customer - Upgrade',
      'Existing Customer - Replacement',
      'Existing Customer - Downgrade',
      'Renewal - Upsell',
      'Renewal - Renewal'
    ];
  }

  /**
   * Get lead sources
   * @returns {Array} Available lead sources
   */
  static getLeadSources() {
    return [
      'Web',
      'Phone Inquiry',
      'Partner Referral',
      'Purchased List',
      'Trade Show',
      'Word of mouth',
      'Email',
      'Other'
    ];
  }

  /**
   * Get forecast categories
   * @returns {Array} Available forecast categories
   */
  static getForecastCategories() {
    return [
      'Omitted',
      'Pipeline',
      'Best Case',
      'Commit',
      'Closed'
    ];
  }

  /**
   * Export opportunities to CSV format
   * @param {Object} filters - Filter criteria
   * @returns {Array} CSV data
   */
  static async exportToCSV(filters = {}) {
    const { opportunities } = await this.findAll(filters, 1, 10000);
    
    return opportunities.map(opp => ({
      'Opportunity Name': opp.name,
      'Account': opp.account_name,
      'Amount': opp.amount,
      'Currency': opp.currency,
      'Stage': opp.stage_name,
      'Probability': opp.probability,
      'Type': opp.type,
      'Lead Source': opp.lead_source,
      'Forecast Category': opp.forecast_category,
      'Close Date': opp.close_date,
      'Owner': opp.owner_email,
      'Is Closed': opp.is_closed,
      'Is Won': opp.is_won,
      'Created Date': opp.created_at,
      'Last Modified': opp.updated_at
    }));
  }
}

module.exports = Opportunity;