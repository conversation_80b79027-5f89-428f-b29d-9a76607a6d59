const { query, queryWithUser, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');
const Joi = require('joi');

class QuoteTemplate {
  // Validation schemas
  static getValidationSchema() {
    return {
      create: Joi.object({
        name: Joi.string().min(3).max(255).required(),
        description: Joi.string().max(1000).allow(''),
        template_data: Joi.object({
          layout: Joi.object({
            header: Joi.object({
              showLogo: Joi.boolean().default(true),
              showCompanyInfo: Joi.boolean().default(true),
              backgroundColor: Joi.string().default('#ffffff'),
              textColor: Joi.string().default('#333333')
            }).optional(),
            body: Joi.object({
              showItemDetails: Joi.boolean().default(true),
              showVendorAttribution: Joi.boolean().default(false),
              groupByCategory: Joi.boolean().default(true),
              showUnitPrices: Joi.boolean().default(true),
              showQuantities: Joi.boolean().default(true)
            }).optional(),
            footer: Joi.object({
              showTerms: Joi.boolean().default(true),
              showSignature: Joi.boolean().default(true),
              showContactInfo: Joi.boolean().default(true)
            }).optional()
          }).optional(),
          styling: Joi.object({
            primaryColor: Joi.string().default('#2563eb'),
            secondaryColor: Joi.string().default('#64748b'),
            fontFamily: Joi.string().default('Inter, sans-serif'),
            fontSize: Joi.string().default('14px')
          }).optional(),
          content: Joi.object({
            defaultTerms: Joi.string().allow(''),
            defaultNotes: Joi.string().allow('')
          }).optional()
        }).required(),
        is_default: Joi.boolean().default(false)
      }),

      update: Joi.object({
        name: Joi.string().min(3).max(255),
        description: Joi.string().max(1000).allow(''),
        template_data: Joi.object(),
        is_default: Joi.boolean(),
        is_active: Joi.boolean()
      }).min(1)
    };
  }

  // Validate template data
  static validateCreate(data) {
    return this.getValidationSchema().create.validate(data, { abortEarly: false });
  }

  static validateUpdate(data) {
    return this.getValidationSchema().update.validate(data, { abortEarly: false });
  }

  // Get all templates with filtering
  static async findAll(filters = {}, userId) {
    try {
      let whereClause = 'WHERE is_active = true';
      const params = [];
      let paramCount = 0;

      // Apply filters
      if (filters.search) {
        paramCount++;
        whereClause += ` AND (name ILIKE $${paramCount} OR description ILIKE $${paramCount})`;
        params.push(`%${filters.search}%`);
      }

      if (filters.created_by) {
        paramCount++;
        whereClause += ` AND created_by = $${paramCount}`;
        params.push(filters.created_by);
      }

      if (filters.is_default !== undefined) {
        paramCount++;
        whereClause += ` AND is_default = $${paramCount}`;
        params.push(filters.is_default);
      }

      const templatesQuery = `
        SELECT 
          qt.*,
          u.email as creator_email
        FROM quote_templates qt
        LEFT JOIN users u ON qt.created_by = u.id
        ${whereClause}
        ORDER BY qt.is_default DESC, qt.created_at DESC
      `;

      const result = await query(templatesQuery, params);
      return result.rows;
    } catch (error) {
      console.error('Error in QuoteTemplate.findAll:', error);
      throw error;
    }
  }

  // Find template by ID
  static async findById(id, userId = null) {
    try {
      const templateQuery = `
        SELECT 
          qt.*,
          u.email as creator_email
        FROM quote_templates qt
        LEFT JOIN users u ON qt.created_by = u.id
        WHERE qt.id = $1 AND qt.is_active = true
      `;

      const result = await query(templateQuery, [id]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const template = result.rows[0];

      // Check access permissions if userId provided
      if (userId && template.created_by !== userId) {
        // Allow access to default templates for all users
        if (!template.is_default) {
          throw new Error('Access denied');
        }
      }

      return template;
    } catch (error) {
      console.error('Error in QuoteTemplate.findById:', error);
      throw error;
    }
  }

  // Get default template
  static async getDefault() {
    try {
      const result = await query(
        'SELECT * FROM quote_templates WHERE is_default = true AND is_active = true LIMIT 1'
      );
      
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error in QuoteTemplate.getDefault:', error);
      throw error;
    }
  }

  // Create new template
  static async create(templateData, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Validate input data
      const { error, value } = this.validateCreate(templateData);
      if (error) {
        throw new Error(`Validation error: ${error.details.map(d => d.message).join(', ')}`);
      }

      const {
        name,
        description,
        template_data,
        is_default
      } = value;

      // If setting as default, unset other defaults
      if (is_default) {
        await client.query(
          'UPDATE quote_templates SET is_default = false WHERE is_default = true'
        );
      }

      // Create template
      const templateQuery = `
        INSERT INTO quote_templates (
          name, description, template_data, is_default, created_by
        ) VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `;

      const templateParams = [
        name,
        description || '',
        JSON.stringify(template_data),
        is_default,
        userId
      ];

      const templateResult = await client.query(templateQuery, templateParams);
      const template = templateResult.rows[0];

      await commitTransaction(client);

      // Return template with full details
      return await this.findById(template.id, userId);
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in QuoteTemplate.create:', error);
      throw error;
    }
  }

  // Update template
  static async update(id, updates, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Validate input data
      const { error, value } = this.validateUpdate(updates);
      if (error) {
        throw new Error(`Validation error: ${error.details.map(d => d.message).join(', ')}`);
      }

      // Check if template exists and user has permission
      const existingTemplate = await client.query(
        'SELECT * FROM quote_templates WHERE id = $1 AND created_by = $2 AND is_active = true',
        [id, userId]
      );

      if (existingTemplate.rows.length === 0) {
        throw new Error('Template not found or access denied');
      }

      // Build dynamic update query
      const updateFields = [];
      const params = [id];
      let paramCount = 1;

      const allowedFields = [
        'name', 'description', 'template_data', 'is_default', 'is_active'
      ];

      for (const field of allowedFields) {
        if (value[field] !== undefined) {
          paramCount++;
          if (field === 'template_data') {
            updateFields.push(`${field} = $${paramCount}`);
            params.push(JSON.stringify(value[field]));
          } else {
            updateFields.push(`${field} = $${paramCount}`);
            params.push(value[field]);
          }
        }
      }

      if (updateFields.length === 0) {
        await rollbackTransaction(client);
        return existingTemplate.rows[0];
      }

      // If setting as default, unset other defaults
      if (value.is_default === true) {
        await client.query(
          'UPDATE quote_templates SET is_default = false WHERE is_default = true AND id != $1',
          [id]
        );
      }

      // Add updated_at
      updateFields.push('updated_at = CURRENT_TIMESTAMP');

      const updateQuery = `
        UPDATE quote_templates 
        SET ${updateFields.join(', ')}
        WHERE id = $1
        RETURNING *
      `;

      const result = await client.query(updateQuery, params);
      const updatedTemplate = result.rows[0];

      await commitTransaction(client);

      // Return updated template with full details
      return await this.findById(id, userId);
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in QuoteTemplate.update:', error);
      throw error;
    }
  }

  // Delete template (soft delete)
  static async delete(id, userId) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Check if template exists and user has permission
      const existingTemplate = await client.query(
        'SELECT * FROM quote_templates WHERE id = $1 AND created_by = $2 AND is_active = true',
        [id, userId]
      );

      if (existingTemplate.rows.length === 0) {
        throw new Error('Template not found or access denied');
      }

      const currentTemplate = existingTemplate.rows[0];

      // Check if template is default
      if (currentTemplate.is_default) {
        throw new Error('Cannot delete default template');
      }

      // Check if template is being used by quotes
      const usageCheck = await client.query(
        'SELECT COUNT(*) as count FROM client_quotes WHERE quote_template_id = $1',
        [id]
      );

      if (parseInt(usageCheck.rows[0].count) > 0) {
        throw new Error('Cannot delete template that is being used by quotes');
      }

      // Soft delete template
      const result = await client.query(
        'UPDATE quote_templates SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING id',
        [id]
      );

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in QuoteTemplate.delete:', error);
      throw error;
    }
  }

  // Clone template
  static async clone(id, newName, userId) {
    try {
      const originalTemplate = await this.findById(id, userId);
      if (!originalTemplate) {
        throw new Error('Template not found');
      }

      const cloneData = {
        name: newName || `${originalTemplate.name} (Copy)`,
        description: originalTemplate.description,
        template_data: originalTemplate.template_data,
        is_default: false
      };

      return await this.create(cloneData, userId);
    } catch (error) {
      console.error('Error in QuoteTemplate.clone:', error);
      throw error;
    }
  }
}

module.exports = QuoteTemplate;