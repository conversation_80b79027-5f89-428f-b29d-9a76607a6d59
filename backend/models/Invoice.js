const { query, queryWithUser, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');

class Invoice {
  // Create a new invoice
  static async create(invoiceData, userId) {
    const client = await beginTransaction();
    try {
      const {
        contractId,
        vendorId,
        amount,
        currency = 'USD',
        status = 'draft',
        items,
        taxes = 0,
        penalties = 0,
        dueDate,
        approvedBy = null
      } = invoiceData;

      // Validate required fields
      if (!vendorId || !amount || !items || !Array.isArray(items) || items.length === 0) {
        throw new Error('Missing required fields: vendorId, amount, and items are required');
      }

      // Validate items structure
      for (const item of items) {
        if (!item.description || typeof item.quantity !== 'number' || typeof item.unitPrice !== 'number') {
          throw new Error('Invalid item structure: description, quantity, and unitPrice are required');
        }
      }

      // Calculate total from items including taxes and penalties
      const itemsSubtotal = items.reduce((total, item) => 
        total + (item.quantity * item.unitPrice), 0
      );
      
      const calculatedAmount = itemsSubtotal + taxes + penalties;

    //   if (Math.abs(amount - calculatedAmount) > 0.01) {
    //     throw new Error(`Amount does not match calculated total. Expected: ${calculatedAmount.toFixed(2)}, Received: ${amount.toFixed(2)}`);
    //   }

      const result = await queryWithUser(client, `
        INSERT INTO invoices (
          contract_id, vendor_id, amount, currency, status, items, 
          taxes, penalties, due_date, approved_by, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *
      `, [contractId, vendorId, amount, currency, status, JSON.stringify(items), taxes, penalties, dueDate, approvedBy], userId);

      // Log audit trail
      await queryWithUser(client, `
        INSERT INTO audits (entity_type, entity_id, action, user_id, new_value, timestamp)
        VALUES ('invoices', $1, 'create', $2, $3, CURRENT_TIMESTAMP)
      `, [result.rows[0].id, userId, JSON.stringify(result.rows[0])]);

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Get all invoices with filtering and pagination
  static async findAll(filters = {}, pagination = {}) {
    const { page = 1, limit = 10, search, status, vendorId, contractId, startDate, endDate } = filters;
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let queryParams = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (search) {
      whereConditions.push(`(
        i.id::text ILIKE $${paramIndex} OR 
        v.name ILIKE $${paramIndex} OR 
        i.items::text ILIKE $${paramIndex}
      )`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (status) {
      whereConditions.push(`i.status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    if (vendorId) {
      whereConditions.push(`i.vendor_id = $${paramIndex}`);
      queryParams.push(vendorId);
      paramIndex++;
    }

    if (contractId) {
      whereConditions.push(`i.contract_id = $${paramIndex}`);
      queryParams.push(contractId);
      paramIndex++;
    }

    if (startDate) {
      whereConditions.push(`i.created_at >= $${paramIndex}`);
      queryParams.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      whereConditions.push(`i.created_at <= $${paramIndex}`);
      queryParams.push(endDate);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM invoices i
      LEFT JOIN vendors v ON i.vendor_id = v.id
      ${whereClause}
    `, queryParams);

    // Get invoices with vendor and contract details
    const result = await query(`
      SELECT 
        i.*,
        v.name as vendor_name,
        v.contact_email as vendor_email,
        v.contact_phone as vendor_phone,
        c.title as contract_title,
        u.email as approved_by_email
      FROM invoices i
      LEFT JOIN vendors v ON i.vendor_id = v.id
      LEFT JOIN contracts c ON i.contract_id = c.id
      LEFT JOIN users u ON i.approved_by = u.id
      ${whereClause}
      ORDER BY i.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `, [...queryParams, limit, offset]);

    return {
      invoices: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      }
    };
  }

  // Get invoice by ID with full details
  static async findById(id, userId) {
    const result = await query(`
      SELECT 
        i.*,
        v.name as vendor_name,
        v.contact_email as vendor_email,
        v.contact_phone as vendor_phone,
        v.address as vendor_address,
        c.title as contract_title,
        c.start_date as contract_start_date,
        c.end_date as contract_end_date,
        u.email as approved_by_email
      FROM invoices i
      LEFT JOIN vendors v ON i.vendor_id = v.id
      LEFT JOIN contracts c ON i.contract_id = c.id
      LEFT JOIN users u ON i.approved_by = u.id
      WHERE i.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      throw new Error('Invoice not found');
    }

    const invoice = result.rows[0];

    // Get payment history
    const paymentsResult = await query(`
      SELECT * FROM payments 
      WHERE invoice_id = $1 
      ORDER BY created_at DESC
    `, [id]);

    // Get audit trail
    const auditResult = await query(`
      SELECT a.*, u.email as user_email
      FROM audits a
      LEFT JOIN users u ON a.user_id = u.id
      WHERE a.entity_type = 'invoices' AND a.entity_id = $1
      ORDER BY a.timestamp DESC
    `, [id]);

    // Get comments
    const commentsResult = await query(`
      SELECT c.*, u.email as user_email
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      WHERE c.entity_type = 'invoices' AND c.entity_id = $1
      ORDER BY c.created_at DESC
    `, [id]);

    // Log view action
    await query(`
      INSERT INTO audits (entity_type, entity_id, action, user_id, timestamp)
      VALUES ('invoices', $1, 'view', $2, CURRENT_TIMESTAMP)
    `, [id, userId]);

    return {
      ...invoice,
      paymentHistory: paymentsResult.rows,
      auditTrail: auditResult.rows,
      comments: commentsResult.rows
    };
  }

  // Update invoice
  static async update(id, updateData, userId) {
    const client = await beginTransaction();
    try {
      // Get current invoice for audit trail
      const currentResult = await queryWithUser(client, 'SELECT * FROM invoices WHERE id = $1', [id], userId);
      if (currentResult.rows.length === 0) {
        throw new Error('Invoice not found');
      }

      const currentInvoice = currentResult.rows[0];

      // Check if invoice can be updated based on status
      if (currentInvoice.status === 'paid' && !updateData.allowPaidEdit) {
        throw new Error('Cannot update paid invoices');
      }

      const {
        contractId,
        vendorId,
        amount,
        currency,
        status,
        items,
        taxes,
        penalties,
        dueDate,
        approvedBy
      } = updateData;

      // Validate items if provided
      if (items) {
        if (!Array.isArray(items) || items.length === 0) {
          throw new Error('Items must be a non-empty array');
        }

        for (const item of items) {
          if (!item.description || typeof item.quantity !== 'number' || typeof item.unitPrice !== 'number') {
            throw new Error('Invalid item structure: description, quantity, and unitPrice are required');
          }
        }

        // Validate amount matches items if both are provided
        if (amount) {
          const calculatedAmount = items.reduce((total, item) => 
            total + (item.quantity * item.unitPrice), 0
          );

          if (Math.abs(amount - calculatedAmount) > 0.01) {
            throw new Error('Amount does not match calculated total from items');
          }
        }
      }

      // Build update query dynamically
      const updateFields = [];
      const updateValues = [];
      let paramIndex = 1;

      if (contractId !== undefined) {
        updateFields.push(`contract_id = $${paramIndex}`);
        updateValues.push(contractId);
        paramIndex++;
      }

      if (vendorId !== undefined) {
        updateFields.push(`vendor_id = $${paramIndex}`);
        updateValues.push(vendorId);
        paramIndex++;
      }

      if (amount !== undefined) {
        updateFields.push(`amount = $${paramIndex}`);
        updateValues.push(amount);
        paramIndex++;
      }

      if (currency !== undefined) {
        updateFields.push(`currency = $${paramIndex}`);
        updateValues.push(currency);
        paramIndex++;
      }

      if (status !== undefined) {
        updateFields.push(`status = $${paramIndex}`);
        updateValues.push(status);
        paramIndex++;
      }

      if (items !== undefined) {
        updateFields.push(`items = $${paramIndex}`);
        updateValues.push(JSON.stringify(items));
        paramIndex++;
      }

      if (taxes !== undefined) {
        updateFields.push(`taxes = $${paramIndex}`);
        updateValues.push(taxes);
        paramIndex++;
      }

      if (penalties !== undefined) {
        updateFields.push(`penalties = $${paramIndex}`);
        updateValues.push(penalties);
        paramIndex++;
      }

      if (dueDate !== undefined) {
        updateFields.push(`due_date = $${paramIndex}`);
        updateValues.push(dueDate);
        paramIndex++;
      }

      if (approvedBy !== undefined) {
        updateFields.push(`approved_by = $${paramIndex}`);
        updateValues.push(approvedBy);
        paramIndex++;
      }

      if (updateFields.length === 0) {
        throw new Error('No fields to update');
      }

      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
      updateValues.push(id);

      const result = await queryWithUser(client, `
        UPDATE invoices 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
      `, updateValues, userId);

      // Log audit trail
      await queryWithUser(client, `
        INSERT INTO audits (entity_type, entity_id, action, user_id, old_value, new_value, timestamp)
        VALUES ('invoices', $1, 'update', $2, $3, $4, CURRENT_TIMESTAMP)
      `, [id, userId, JSON.stringify(currentInvoice), JSON.stringify(result.rows[0])]);

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Approve invoice
  static async approve(id, userId, comments = null) {
    const client = await beginTransaction();
    try {
      // Check if invoice exists and can be approved
      const currentResult = await queryWithUser(client, 'SELECT * FROM invoices WHERE id = $1', [id], userId);
      if (currentResult.rows.length === 0) {
        throw new Error('Invoice not found');
      }

      const currentInvoice = currentResult.rows[0];
      if (currentInvoice.status !== 'draft' && currentInvoice.status !== 'pending') {
        throw new Error('Invoice cannot be approved in current status');
      }

      // Update invoice status
      const result = await queryWithUser(client, `
        UPDATE invoices 
        SET status = 'approved', approved_by = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING *
      `, [userId, id], userId);

      // Add approval comment if provided
      if (comments) {
        await queryWithUser(client, `
          INSERT INTO comments (entity_type, entity_id, user_id, text, created_at)
          VALUES ('invoices', $1, $2, $3, CURRENT_TIMESTAMP)
        `, [id, userId, `Approval: ${comments}`]);
      }

      // Log audit trail
      await queryWithUser(client, `
        INSERT INTO audits (entity_type, entity_id, action, user_id, old_value, new_value, timestamp)
        VALUES ('invoices', $1, 'approve', $2, $3, $4, CURRENT_TIMESTAMP)
      `, [id, userId, JSON.stringify(currentInvoice), JSON.stringify(result.rows[0])]);

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Delete invoice (soft delete by changing status)
  static async delete(id, userId) {
    const client = await beginTransaction();
    try {
      // Check if invoice exists and can be deleted
      const currentResult = await queryWithUser(client, 'SELECT * FROM invoices WHERE id = $1', [id], userId);
      if (currentResult.rows.length === 0) {
        throw new Error('Invoice not found');
      }

      const currentInvoice = currentResult.rows[0];
      if (currentInvoice.status === 'paid' || currentInvoice.status === 'partially_paid') {
        throw new Error('Cannot delete paid or partially paid invoices');
      }

      // Soft delete by updating status
      const result = await queryWithUser(client, `
        UPDATE invoices 
        SET status = 'deleted', updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING *
      `, [id], userId);

      // Log audit trail
      await queryWithUser(client, `
        INSERT INTO audits (entity_type, entity_id, action, user_id, old_value, new_value, timestamp)
        VALUES ('invoices', $1, 'delete', $2, $3, $4, CURRENT_TIMESTAMP)
      `, [id, userId, JSON.stringify(currentInvoice), JSON.stringify(result.rows[0])]);

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Get invoice statistics
  static async getStatistics(filters = {}) {
    const { vendorId, startDate, endDate } = filters;

    let whereConditions = ["status != 'deleted'"];
    let queryParams = [];
    let paramIndex = 1;

    if (vendorId) {
      whereConditions.push(`vendor_id = $${paramIndex}`);
      queryParams.push(vendorId);
      paramIndex++;
    }

    if (startDate) {
      whereConditions.push(`created_at >= $${paramIndex}`);
      queryParams.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      whereConditions.push(`created_at <= $${paramIndex}`);
      queryParams.push(endDate);
      paramIndex++;
    }

    const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

    const result = await query(`
      SELECT 
        COUNT(*) as total_invoices,
        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_count,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_count,
        COUNT(CASE WHEN status = 'partially_paid' THEN 1 END) as partially_paid_count,
        COUNT(CASE WHEN status = 'disputed' THEN 1 END) as disputed_count,
        COUNT(CASE WHEN due_date < CURRENT_DATE AND status NOT IN ('paid', 'disputed') THEN 1 END) as overdue_count,
        COALESCE(SUM(amount), 0) as total_amount,
        COALESCE(SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END), 0) as paid_amount,
        COALESCE(AVG(amount), 0) as average_amount
      FROM invoices
      ${whereClause}
    `, queryParams);

    return result.rows[0];
  }

  // Generate invoice from contract
  static async generateFromContract(contractId, userId, additionalData = {}) {
    const client = await beginTransaction();
    try {
      // Get contract details
      const contractResult = await queryWithUser(client, `
        SELECT c.*, v.name as vendor_name
        FROM contracts c
        LEFT JOIN vendors v ON c.vendor_id = v.id
        WHERE c.id = $1
      `, [contractId], userId);

      if (contractResult.rows.length === 0) {
        throw new Error('Contract not found');
      }

      const contract = contractResult.rows[0];

      // Extract items from contract milestones or use provided items
      let items = additionalData.items;
      if (!items && contract.milestones) {
        const milestones = typeof contract.milestones === 'string' 
          ? JSON.parse(contract.milestones) 
          : contract.milestones;
        
        items = milestones
          .filter(milestone => milestone.status === 'completed' && !milestone.invoiced)
          .map(milestone => ({
            id: crypto.randomUUID(),
            description: milestone.description,
            quantity: 1,
            unitPrice: milestone.amount || 0,
            totalPrice: milestone.amount || 0,
            taxable: true
          }));
      }

      if (!items || items.length === 0) {
        throw new Error('No billable items found in contract');
      }

      const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);

      // Create invoice
      const invoiceData = {
        contractId: contractId,
        vendorId: contract.vendor_id,
        amount: totalAmount,
        currency: additionalData.currency || 'USD',
        status: 'draft',
        items: items,
        taxes: additionalData.taxes || 0,
        penalties: additionalData.penalties || 0,
        dueDate: additionalData.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      };

      const invoice = await this.create(invoiceData, userId);

      await commitTransaction(client);
      return invoice;
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Generate invoice from approved RFQ quote
  static async generateFromRFQQuote(quoteId, userId, additionalData = {}) {
    const client = await beginTransaction();
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Get quote details with RFQ information
      const quoteResult = await client.query(`
        SELECT 
          cq.*,
          r.title as rfq_title,
          r.items as rfq_items,
          r.creator_id as rfq_creator_id
        FROM client_quotes cq
        LEFT JOIN rfqs r ON cq.rfq_id = r.id
        WHERE cq.id = $1 AND cq.status = 'approved'
      `, [quoteId]);

      if (quoteResult.rows.length === 0) {
        throw new Error('Approved quote not found');
      }

      const quote = quoteResult.rows[0];
      const selectedBids = quote.selected_bids;
      const rfqItems = quote.rfq_items;

      // Check if invoice already exists for this quote
      const existingInvoiceResult = await client.query(
        'SELECT id FROM invoices WHERE rfq_quote_id = $1',
        [quoteId]
      );

      if (existingInvoiceResult.rows.length > 0) {
        throw new Error('Invoice already exists for this quote');
      }

      // Group selected bids by vendor to create separate invoices
      const vendorBids = {};
      Object.entries(selectedBids).forEach(([itemId, bid]) => {
        if (!vendorBids[bid.vendorId]) {
          vendorBids[bid.vendorId] = [];
        }
        
        const rfqItem = rfqItems.find(item => item.id === itemId);
        vendorBids[bid.vendorId].push({
          rfq_item_id: itemId,
          rfq_item_name: rfqItem?.name || 'RFQ Item',
          description: `${quote.rfq_title} - ${rfqItem?.name || 'Item'}`,
          quantity: rfqItem?.quantity || 1,
          unit_price: bid.unitPrice,
          total_price: bid.totalPrice,
          specifications: rfqItem?.specifications || {},
          rationale: bid.rationale || ''
        });
      });

      const createdInvoices = [];

      // Create invoice for each vendor
      for (const [vendorId, items] of Object.entries(vendorBids)) {
        const vendorTotal = items.reduce((sum, item) => sum + item.total_price, 0);
        
        // Apply proportional margin and taxes for this vendor
        const vendorProportion = vendorTotal / Object.values(selectedBids).reduce((sum, bid) => sum + bid.totalPrice, 0);
        const proportionalMargin = (quote.total_amount - Object.values(selectedBids).reduce((sum, bid) => sum + bid.totalPrice, 0) - quote.taxes) * vendorProportion;
        const proportionalTaxes = quote.taxes * vendorProportion;
        
        const finalAmount = vendorTotal + proportionalMargin + proportionalTaxes;

        // Create invoice data
        const invoiceData = {
          vendorId: parseInt(vendorId),
          amount: finalAmount,
          currency: quote.currency,
          status: additionalData.status || 'draft',
          items: items,
          taxes: proportionalTaxes,
          penalties: 0,
          dueDate: additionalData.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          rfq_quote_id: quoteId, // Link to the original quote
          rfq_id: quote.rfq_id, // Link to the original RFQ
          notes: `Generated from RFQ Quote: ${quote.title}. Original RFQ: ${quote.rfq_title}`
        };

        // Create the invoice
        const invoiceQuery = `
          INSERT INTO invoices (
            vendor_id, amount, currency, status, items, taxes, penalties, 
            due_date, rfq_quote_id, rfq_id, notes, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          RETURNING *
        `;

        const invoiceParams = [
          invoiceData.vendorId,
          invoiceData.amount,
          invoiceData.currency,
          invoiceData.status,
          JSON.stringify(invoiceData.items),
          invoiceData.taxes,
          invoiceData.penalties,
          invoiceData.dueDate,
          invoiceData.rfq_quote_id,
          invoiceData.rfq_id,
          invoiceData.notes
        ];

        const invoiceResult = await client.query(invoiceQuery, invoiceParams);
        const invoice = invoiceResult.rows[0];

        // Log audit trail
        await client.query(`
          INSERT INTO audits (entity_type, entity_id, action, user_id, new_value, details, timestamp)
          VALUES ('invoices', $1, 'create', $2, $3, $4, CURRENT_TIMESTAMP)
        `, [
          invoice.id, 
          userId, 
          JSON.stringify(invoice),
          JSON.stringify({ source: 'rfq_quote', quote_id: quoteId, rfq_id: quote.rfq_id })
        ]);

        createdInvoices.push(invoice);
      }

      // Update the quote to mark it as invoiced
      await client.query(
        'UPDATE client_quotes SET invoice_created = true, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
        [quoteId]
      );

      await commitTransaction(client);
      return createdInvoices;
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in Invoice.generateFromRFQQuote:', error);
      throw error;
    }
  }

  // Get invoices linked to RFQ
  static async findByRFQ(rfqId, userId) {
    try {
      const result = await query(`
        SELECT 
          i.*,
          v.name as vendor_name,
          v.contact_email as vendor_email,
          cq.title as quote_title,
          cq.status as quote_status,
          r.title as rfq_title
        FROM invoices i
        LEFT JOIN vendors v ON i.vendor_id = v.id
        LEFT JOIN client_quotes cq ON i.rfq_quote_id = cq.id
        LEFT JOIN rfqs r ON i.rfq_id = r.id
        WHERE i.rfq_id = $1
        ORDER BY i.created_at DESC
      `, [rfqId]);

      return result.rows;
    } catch (error) {
      console.error('Error in Invoice.findByRFQ:', error);
      throw error;
    }
  }

  // Get invoice status tracking linked to original RFQ
  static async getRFQInvoiceStatus(rfqId, userId) {
    try {
      // Check if user has access to this RFQ
      const rfqCheck = await query(
        'SELECT id FROM rfqs WHERE id = $1 AND creator_id = $2',
        [rfqId, userId]
      );

      if (rfqCheck.rows.length === 0) {
        throw new Error('RFQ not found or access denied');
      }

      const result = await query(`
        SELECT 
          i.id as invoice_id,
          i.vendor_id,
          i.amount,
          i.currency,
          i.status as invoice_status,
          i.due_date,
          i.created_at as invoice_created_at,
          v.name as vendor_name,
          cq.id as quote_id,
          cq.title as quote_title,
          cq.status as quote_status,
          cq.approved_at as quote_approved_at,
          -- Payment information
          COALESCE(SUM(p.amount), 0) as total_paid,
          COUNT(p.id) as payment_count,
          MAX(p.paid_at) as last_payment_date,
          -- Calculate outstanding amount
          i.amount - COALESCE(SUM(p.amount), 0) as outstanding_amount
        FROM invoices i
        LEFT JOIN vendors v ON i.vendor_id = v.id
        LEFT JOIN client_quotes cq ON i.rfq_quote_id = cq.id
        LEFT JOIN payments p ON i.id = p.invoice_id AND p.status = 'completed'
        WHERE i.rfq_id = $1
        GROUP BY i.id, i.vendor_id, i.amount, i.currency, i.status, i.due_date, 
                 i.created_at, v.name, cq.id, cq.title, cq.status, cq.approved_at
        ORDER BY i.created_at DESC
      `, [rfqId]);

      return result.rows;
    } catch (error) {
      console.error('Error in Invoice.getRFQInvoiceStatus:', error);
      throw error;
    }
  }

  // Update invoice status and trigger vendor performance update
  static async updateStatusWithRFQTracking(id, newStatus, userId, updateReason = '') {
    const client = await beginTransaction();
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);

      // Get current invoice with RFQ information
      const currentResult = await client.query(`
        SELECT 
          i.*,
          r.title as rfq_title,
          cq.title as quote_title
        FROM invoices i
        LEFT JOIN rfqs r ON i.rfq_id = r.id
        LEFT JOIN client_quotes cq ON i.rfq_quote_id = cq.id
        WHERE i.id = $1
      `, [id]);

      if (currentResult.rows.length === 0) {
        throw new Error('Invoice not found');
      }

      const currentInvoice = currentResult.rows[0];
      const oldStatus = currentInvoice.status;

      // Update invoice status
      const updateResult = await client.query(`
        UPDATE invoices 
        SET status = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING *
      `, [newStatus, id]);

      const updatedInvoice = updateResult.rows[0];

      // Log audit trail
      await client.query(`
        INSERT INTO audits (entity_type, entity_id, action, user_id, old_value, new_value, details, timestamp)
        VALUES ('invoices', $1, 'update', $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `, [
        id, 
        userId, 
        JSON.stringify({ status: oldStatus }),
        JSON.stringify({ status: newStatus }),
        JSON.stringify({ reason: updateReason, rfq_linked: !!currentInvoice.rfq_id })
      ]);

      // Update vendor performance if this is RFQ-related and status changed to paid
      if (currentInvoice.rfq_id && newStatus === 'paid' && oldStatus !== 'paid') {
        // Import Vendor model to update performance
        const Vendor = require('./Vendor');
        
        const rfqOutcome = {
          type: 'invoice_paid',
          rfqTitle: currentInvoice.rfq_title || 'RFQ',
          rfqId: currentInvoice.rfq_id,
          quoteId: currentInvoice.rfq_quote_id,
          invoiceId: id,
          amount: currentInvoice.amount,
          currency: currentInvoice.currency
        };

        try {
          await Vendor.updatePerformanceFromRFQ(currentInvoice.vendor_id, rfqOutcome, userId);
        } catch (performanceError) {
          console.error('Error updating vendor performance:', performanceError);
          // Don't fail the invoice update if performance update fails
        }
      }

      // Update vendor performance if invoice becomes overdue
      if (currentInvoice.rfq_id && newStatus === 'overdue' && oldStatus !== 'overdue') {
        const Vendor = require('./Vendor');
        
        const rfqOutcome = {
          type: 'invoice_overdue',
          rfqTitle: currentInvoice.rfq_title || 'RFQ',
          rfqId: currentInvoice.rfq_id,
          invoiceId: id
        };

        try {
          await Vendor.updatePerformanceFromRFQ(currentInvoice.vendor_id, rfqOutcome, userId);
        } catch (performanceError) {
          console.error('Error updating vendor performance for overdue:', performanceError);
        }
      }

      await commitTransaction(client);
      return updatedInvoice;
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in Invoice.updateStatusWithRFQTracking:', error);
      throw error;
    }
  }

  // Get RFQ-to-Invoice analytics
  static async getRFQInvoiceAnalytics(filters = {}) {
    try {
      const { startDate, endDate, vendorId, rfqId } = filters;

      let whereConditions = ["i.rfq_id IS NOT NULL"];
      let queryParams = [];
      let paramIndex = 1;

      if (startDate) {
        whereConditions.push(`i.created_at >= ${paramIndex}`);
        queryParams.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        whereConditions.push(`i.created_at <= ${paramIndex}`);
        queryParams.push(endDate);
        paramIndex++;
      }

      if (vendorId) {
        whereConditions.push(`i.vendor_id = ${paramIndex}`);
        queryParams.push(vendorId);
        paramIndex++;
      }

      if (rfqId) {
        whereConditions.push(`i.rfq_id = ${paramIndex}`);
        queryParams.push(rfqId);
        paramIndex++;
      }

      const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

      const result = await query(`
        SELECT 
          COUNT(DISTINCT i.rfq_id) as total_rfqs_with_invoices,
          COUNT(i.id) as total_rfq_invoices,
          COUNT(DISTINCT i.vendor_id) as unique_vendors_invoiced,
          SUM(i.amount) as total_invoice_value,
          AVG(i.amount) as average_invoice_amount,
          COUNT(CASE WHEN i.status = 'paid' THEN 1 END) as paid_invoices,
          COUNT(CASE WHEN i.due_date < CURRENT_DATE AND i.status NOT IN ('paid', 'disputed') THEN 1 END) as overdue_invoices,
          SUM(CASE WHEN i.status = 'paid' THEN i.amount ELSE 0 END) as total_paid_amount,
          AVG(EXTRACT(EPOCH FROM (
            CASE WHEN i.status = 'paid' 
            THEN (SELECT MIN(p.paid_at) FROM payments p WHERE p.invoice_id = i.id AND p.status = 'completed')
            ELSE NULL END - i.created_at
          ))/86400) as avg_payment_days,
          -- RFQ to Invoice conversion metrics
          (SELECT COUNT(*) FROM rfqs WHERE status = 'closed') as total_closed_rfqs,
          ROUND(
            (COUNT(DISTINCT i.rfq_id)::DECIMAL / GREATEST((SELECT COUNT(*) FROM rfqs WHERE status = 'closed'), 1)) * 100, 
            2
          ) as rfq_to_invoice_conversion_rate
        FROM invoices i
        LEFT JOIN payments p ON i.id = p.invoice_id AND p.status = 'completed'
        ${whereClause}
      `, queryParams);

      return result.rows[0];
    } catch (error) {
      console.error('Error in Invoice.getRFQInvoiceAnalytics:', error);
      throw error;
    }
  }
}

module.exports = Invoice;