const { query, queryWithUser, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');

class Account {
  // Get all accounts with filtering and pagination
  static async findAll(filters = {}, page = 1, limit = 10) {
    let whereClause = 'WHERE is_deleted = FALSE';
    const params = [];
    let paramCount = 0;

    // Apply filters
    if (filters.search) {
      paramCount++;
      whereClause += ` AND (name ILIKE $${paramCount} OR description ILIKE $${paramCount})`;
      params.push(`%${filters.search}%`);
    }

    if (filters.industry) {
      paramCount++;
      whereClause += ` AND industry = $${paramCount}`;
      params.push(filters.industry);
    }

    if (filters.type) {
      paramCount++;
      whereClause += ` AND type = $${paramCount}`;
      params.push(filters.type);
    }

    if (filters.rating) {
      paramCount++;
      whereClause += ` AND rating = $${paramCount}`;
      params.push(filters.rating);
    }

    if (filters.ownership) {
      paramCount++;
      whereClause += ` AND ownership = $${paramCount}`;
      params.push(filters.ownership);
    }

    if (filters.status) {
      paramCount++;
      whereClause += ` AND status = $${paramCount}`;
      params.push(filters.status);
    }

    if (filters.annual_revenue_min !== undefined) {
      paramCount++;
      whereClause += ` AND annual_revenue >= $${paramCount}`;
      params.push(filters.annual_revenue_min);
    }

    if (filters.annual_revenue_max !== undefined) {
      paramCount++;
      whereClause += ` AND annual_revenue <= $${paramCount}`;
      params.push(filters.annual_revenue_max);
    }

    if (filters.number_of_employees_min !== undefined) {
      paramCount++;
      whereClause += ` AND number_of_employees >= $${paramCount}`;
      params.push(filters.number_of_employees_min);
    }

    if (filters.number_of_employees_max !== undefined) {
      paramCount++;
      whereClause += ` AND number_of_employees <= $${paramCount}`;
      params.push(filters.number_of_employees_max);
    }

    if (filters.country) {
      paramCount++;
      whereClause += ` AND (billing_address->>'country' = $${paramCount} OR shipping_address->>'country' = $${paramCount})`;
      params.push(filters.country);
    }

    if (filters.state) {
      paramCount++;
      whereClause += ` AND (billing_address->>'state' = $${paramCount} OR shipping_address->>'state' = $${paramCount})`;
      params.push(filters.state);
    }

    if (filters.city) {
      paramCount++;
      whereClause += ` AND (billing_address->>'city' = $${paramCount} OR shipping_address->>'city' = $${paramCount})`;
      params.push(filters.city);
    }

    if (filters.parent_account_id !== undefined) {
      paramCount++;
      if (filters.parent_account_id === null) {
        whereClause += ` AND parent_account_id IS NULL`;
      } else {
        whereClause += ` AND parent_account_id = $${paramCount}`;
        params.push(filters.parent_account_id);
      }
    }

    if (filters.owner_id) {
      paramCount++;
      whereClause += ` AND owner_id = $${paramCount}`;
      params.push(filters.owner_id);
    }

    if (filters.has_children !== undefined) {
      if (filters.has_children) {
        whereClause += ` AND EXISTS (SELECT 1 FROM accounts child WHERE child.parent_account_id = accounts.id AND child.is_deleted = FALSE)`;
      } else {
        whereClause += ` AND NOT EXISTS (SELECT 1 FROM accounts child WHERE child.parent_account_id = accounts.id AND child.is_deleted = FALSE)`;
      }
    }

    if (filters.created_after) {
      paramCount++;
      whereClause += ` AND created_at >= $${paramCount}`;
      params.push(filters.created_after);
    }

    if (filters.created_before) {
      paramCount++;
      whereClause += ` AND created_at <= $${paramCount}`;
      params.push(filters.created_before);
    }

    // Count total records
    const countQuery = `SELECT COUNT(*) FROM accounts ${whereClause}`;
    const countResult = await query(countQuery, params);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const offset = (page - 1) * limit;
    paramCount++;
    const limitParam = paramCount;
    paramCount++;
    const offsetParam = paramCount;
    
    const selectQuery = `
      SELECT id, name, account_number, type, industry, annual_revenue, 
             number_of_employees, ownership, phone, fax, website, 
             ticker_symbol, site, rating, description, billing_address, 
             shipping_address, parent_account_id, owner_id, integration_id,
             custom_fields, status, created_at, updated_at, created_by_id,
             last_modified_by_id, is_deleted,
             (SELECT COUNT(*) FROM accounts child WHERE child.parent_account_id = accounts.id AND child.is_deleted = FALSE) as children_count
      FROM accounts 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${limitParam} OFFSET $${offsetParam}
    `;
    
    params.push(limit, offset);
    const result = await query(selectQuery, params);

    return {
      accounts: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  // Find account by ID
  static async findById(id) {
    const result = await query(
      `SELECT id, name, account_number, type, industry, annual_revenue, 
              number_of_employees, ownership, phone, fax, website, 
              ticker_symbol, site, rating, description, billing_address, 
              shipping_address, parent_account_id, owner_id, integration_id,
              custom_fields, status, created_at, updated_at, created_by_id,
              last_modified_by_id, is_deleted,
              (SELECT COUNT(*) FROM accounts child WHERE child.parent_account_id = accounts.id AND child.is_deleted = FALSE) as children_count
       FROM accounts WHERE id = $1 AND is_deleted = FALSE`,
      [id]
    );
    return result.rows[0];
  }

  // Get account hierarchy (children)
  static async getHierarchy(id) {
    const result = await query(
      `WITH RECURSIVE account_hierarchy AS (
        SELECT id, name, parent_account_id, 0 as level
        FROM accounts 
        WHERE id = $1 AND is_deleted = FALSE
        
        UNION ALL
        
        SELECT a.id, a.name, a.parent_account_id, ah.level + 1
        FROM accounts a
        INNER JOIN account_hierarchy ah ON a.parent_account_id = ah.id
        WHERE a.is_deleted = FALSE AND ah.level < 5
      )
      SELECT * FROM account_hierarchy ORDER BY level, name`,
      [id]
    );
    return result.rows;
  }

  // Get account children (direct children only)
  static async getChildren(id) {
    const result = await query(
      `SELECT id, name, account_number, type, industry, annual_revenue, 
              number_of_employees, ownership, phone, fax, website, 
              ticker_symbol, site, rating, description, billing_address, 
              shipping_address, parent_account_id, owner_id, integration_id,
              custom_fields, status, created_at, updated_at, created_by_id,
              last_modified_by_id, is_deleted
       FROM accounts 
       WHERE parent_account_id = $1 AND is_deleted = FALSE
       ORDER BY name`,
      [id]
    );
    return result.rows;
  }

  // Get account ancestors (parent chain)
  static async getAncestors(id) {
    const result = await query(
      `WITH RECURSIVE account_ancestors AS (
        SELECT id, name, parent_account_id, 0 as level
        FROM accounts 
        WHERE id = $1 AND is_deleted = FALSE
        
        UNION ALL
        
        SELECT a.id, a.name, a.parent_account_id, aa.level + 1
        FROM accounts a
        INNER JOIN account_ancestors aa ON a.id = aa.parent_account_id
        WHERE a.is_deleted = FALSE AND aa.level < 5
      )
      SELECT * FROM account_ancestors WHERE level > 0 ORDER BY level DESC`,
      [id]
    );
    return result.rows;
  }

  // Create new account
  static async create(accountData, userId = 1) {
    const {
      name,
      account_number,
      type,
      industry,
      annual_revenue,
      number_of_employees,
      ownership,
      phone,
      fax,
      website,
      ticker_symbol,
      site,
      rating,
      description,
      billing_address,
      shipping_address,
      parent_account_id,
      owner_id,
      custom_fields = {}
    } = accountData;

    // Validate hierarchy to prevent circular references
    if (parent_account_id) {
      const ancestors = await this.getAncestors(parent_account_id);
      // This will be validated after creation, but we can add basic check here
    }

    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);
      
      const result = await client.query(
        `INSERT INTO accounts (
          name, account_number, type, industry, annual_revenue, 
          number_of_employees, ownership, phone, fax, website, 
          ticker_symbol, site, rating, description, billing_address, 
          shipping_address, parent_account_id, owner_id, custom_fields,
          created_by_id, last_modified_by_id
        )
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $20)
         RETURNING id, name, account_number, type, industry, annual_revenue, 
                   number_of_employees, ownership, phone, fax, website, 
                   ticker_symbol, site, rating, description, billing_address, 
                   shipping_address, parent_account_id, owner_id, integration_id,
                   custom_fields, status, created_at, updated_at, created_by_id,
                   last_modified_by_id, is_deleted`,
        [
          name, account_number, type, industry, annual_revenue,
          number_of_employees, ownership, phone, fax, website,
          ticker_symbol, site, rating, description, 
          billing_address ? JSON.stringify(billing_address) : null,
          shipping_address ? JSON.stringify(shipping_address) : null,
          parent_account_id, owner_id || userId, JSON.stringify(custom_fields),
          userId
        ]
      );

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }

    return result.rows[0];
  }

  // Update account
  static async update(id, accountData, userId = 1) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);
      
      // Get current account data for audit
      const currentResult = await client.query('SELECT * FROM accounts WHERE id = $1 AND is_deleted = FALSE', [id]);
      if (currentResult.rows.length === 0) {
        throw new Error('Account not found');
      }
      const currentAccount = currentResult.rows[0];

      // Validate hierarchy changes to prevent circular references
      if (accountData.parent_account_id !== undefined && accountData.parent_account_id !== currentAccount.parent_account_id) {
        if (accountData.parent_account_id) {
          // Check if the new parent would create a circular reference
          const descendants = await this.getHierarchy(id);
          const descendantIds = descendants.map(d => d.id);
          if (descendantIds.includes(accountData.parent_account_id)) {
            throw new Error('Circular hierarchy reference detected');
          }
        }
      }

      // Build update query dynamically
      const updateFields = [];
      const params = [id];
      let paramCount = 1;

      Object.keys(accountData).forEach(key => {
        if (accountData[key] !== undefined && key !== 'id') {
          paramCount++;
          if (key === 'billing_address' || key === 'shipping_address' || key === 'custom_fields') {
            updateFields.push(`${key} = $${paramCount}`);
            params.push(JSON.stringify(accountData[key]));
          } else {
            updateFields.push(`${key} = $${paramCount}`);
            params.push(accountData[key]);
          }
        }
      });

      if (updateFields.length === 0) {
        await rollbackTransaction(client);
        return currentAccount;
      }

      // Add last_modified_by_id and updated_at
      paramCount++;
      updateFields.push(`last_modified_by_id = $${paramCount}`);
      params.push(userId);

      paramCount++;
      updateFields.push(`updated_at = $${paramCount}`);
      params.push(new Date());

      const updateQuery = `
        UPDATE accounts 
        SET ${updateFields.join(', ')}
        WHERE id = $1 AND is_deleted = FALSE
        RETURNING id, name, account_number, type, industry, annual_revenue, 
                  number_of_employees, ownership, phone, fax, website, 
                  ticker_symbol, site, rating, description, billing_address, 
                  shipping_address, parent_account_id, owner_id, integration_id,
                  custom_fields, status, created_at, updated_at, created_by_id,
                  last_modified_by_id, is_deleted
      `;

      const result = await client.query(updateQuery, params);
      const updatedAccount = result.rows[0];

      // Audit logging is handled by database trigger

      await commitTransaction(client);
      return updatedAccount;
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Soft delete account
  static async delete(id, userId = 1) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);
      
      // Get current account data
      const currentResult = await client.query('SELECT * FROM accounts WHERE id = $1 AND is_deleted = FALSE', [id]);
      if (currentResult.rows.length === 0) {
        throw new Error('Account not found');
      }

      // Check for child accounts
      const childrenResult = await client.query('SELECT COUNT(*) FROM accounts WHERE parent_account_id = $1 AND is_deleted = FALSE', [id]);
      const childrenCount = parseInt(childrenResult.rows[0].count);
      
      if (childrenCount > 0) {
        throw new Error('Cannot delete account with child accounts. Please delete or reassign child accounts first.');
      }

      // Check for linked entities (this would be expanded when other modules are implemented)
      // For now, we'll just do a soft delete

      // Soft delete
      const result = await client.query(
        `UPDATE accounts 
         SET is_deleted = TRUE, status = 'INACTIVE', last_modified_by_id = $2, updated_at = $3
         WHERE id = $1 AND is_deleted = FALSE
         RETURNING id`,
        [id, userId, new Date()]
      );

      // Audit logging is handled by database trigger

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Get account industries
  static async getIndustries() {
    const result = await query(
      'SELECT DISTINCT industry FROM accounts WHERE industry IS NOT NULL AND is_deleted = FALSE ORDER BY industry'
    );
    return result.rows.map(row => row.industry);
  }

  // Get account types
  static async getTypes() {
    const result = await query(
      'SELECT DISTINCT type FROM accounts WHERE type IS NOT NULL AND is_deleted = FALSE ORDER BY type'
    );
    return result.rows.map(row => row.type);
  }

  // Get account audit history
  static async getAuditHistory(id) {
    const result = await query(
      `SELECT a.id, a.entity_type, a.entity_id, a.action, a.user_id, 
              a.old_value, a.new_value, a.details, a.timestamp,
              u.email as user_name
       FROM audits a
       LEFT JOIN users u ON a.user_id = u.id
       WHERE a.entity_type = 'accounts' AND a.entity_id = $1
       ORDER BY a.timestamp DESC`,
      [id]
    );
    return result.rows;
  }

  // Search accounts with advanced filters
  static async search(searchParams) {
    const {
      query: searchQuery,
      industry,
      type,
      rating,
      ownership,
      annual_revenue_min,
      annual_revenue_max,
      number_of_employees_min,
      number_of_employees_max,
      country,
      state,
      city,
      parent_account_id,
      owner_id,
      has_children,
      created_after,
      created_before,
      page = 1,
      limit = 10
    } = searchParams;

    return this.findAll({
      search: searchQuery,
      industry,
      type,
      rating,
      ownership,
      annual_revenue_min,
      annual_revenue_max,
      number_of_employees_min,
      number_of_employees_max,
      country,
      state,
      city,
      parent_account_id,
      owner_id,
      has_children,
      created_after,
      created_before
    }, page, limit);
  }

  // Export accounts to CSV format
  static async exportToCSV(filters = {}) {
    const { accounts } = await this.findAll(filters, 1, 10000); // Large limit for export
    
    const csvHeaders = [
      'ID', 'Name', 'Account Number', 'Type', 'Industry', 'Annual Revenue',
      'Number of Employees', 'Ownership', 'Phone', 'Website', 'Rating',
      'Billing Country', 'Billing State', 'Billing City', 'Status', 'Created At'
    ];

    const csvRows = accounts.map(account => [
      account.id,
      account.name,
      account.account_number || '',
      account.type || '',
      account.industry || '',
      account.annual_revenue || '',
      account.number_of_employees || '',
      account.ownership || '',
      account.phone || '',
      account.website || '',
      account.rating || '',
      account.billing_address?.country || '',
      account.billing_address?.state || '',
      account.billing_address?.city || '',
      account.status,
      account.created_at
    ]);

    return {
      headers: csvHeaders,
      rows: csvRows
    };
  }

  // Validate account data
  static validateAccountData(accountData) {
    const errors = [];

    // Required fields
    if (!accountData.name || accountData.name.trim().length === 0) {
      errors.push({ field: 'name', message: 'Account name is required' });
    }

    if (accountData.name && accountData.name.length > 255) {
      errors.push({ field: 'name', message: 'Account name must be less than 255 characters' });
    }

    // Email validation for contact fields would go here if needed
    
    // Revenue validation
    if (accountData.annual_revenue !== undefined && accountData.annual_revenue < 0) {
      errors.push({ field: 'annual_revenue', message: 'Annual revenue cannot be negative' });
    }

    // Employee count validation
    if (accountData.number_of_employees !== undefined && accountData.number_of_employees < 0) {
      errors.push({ field: 'number_of_employees', message: 'Number of employees cannot be negative' });
    }

    // URL validation
    if (accountData.website && !this.isValidUrl(accountData.website)) {
      errors.push({ field: 'website', message: 'Invalid website URL format' });
    }

    return errors;
  }

  // Helper method to validate URLs
  static isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }
}

module.exports = Account;