const { query, queryWithUser, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');
const Joi = require('joi');

class RFQSubmission {
  // Validation schemas
  static getValidationSchema() {
    return {
      create: Joi.object({
        rfq_id: Joi.number().integer().positive().required(),
        vendor_id: Joi.number().integer().positive().required(),
        invitation_token: Joi.string().length(64).required(),
        bidItems: Joi.array().min(1).items(
          Joi.object({
            rfq_item_id: Joi.string().required(),
            item_name: Joi.string().min(1).max(255).required(),
            unit_price: Joi.number().min(0).precision(2).required(),
            quantity: Joi.number().integer().min(1).required(),
            delivery_days: Joi.number().integer().min(0).allow(null),
            specifications: Joi.object().default({}),
            alternatives: Joi.array().items(
              Joi.object({
                description: Joi.string().max(500).required(),
                unitPrice: Joi.number().min(0).precision(2).required(),
                totalPrice: Joi.number().min(0).precision(2).required(),
                notes: Joi.string().max(1000).allow('')
              })
            ).default([]),
            notes: Joi.string().max(1000).allow('')
          })
        ).required(),
        bidData: Joi.object({
          generalTerms: Joi.alternatives().try(
            Joi.object(),
            Joi.string().max(1000)
          ).default({}),
          deliverySchedule: Joi.alternatives().try(
            Joi.object(),
            Joi.string().max(1000)
          ).default({}),
          paymentTerms: Joi.string().max(1000).allow(''),
          validityPeriod: Joi.number().integer().min(1).max(365).default(30),
          additionalNotes: Joi.string().max(5000).allow('')
        }).default({}),
        attachments: Joi.array().items(
          Joi.object({
            filename: Joi.string().required(),
            originalName: Joi.string().required(),
            fileSize: Joi.number().integer().min(1).max(10485760).required(), // 10MB limit
            mimeType: Joi.string().required(),
            filePath: Joi.string().required(),
            uploadedAt: Joi.date().default(() => new Date())
          })
        ).default([]),
        currency: Joi.string().valid('USD', 'EUR', 'GBP', 'JPY', 'CNY').default('USD'),
        delivery_days: Joi.number().integer().min(0).allow(null),
        payment_terms: Joi.string().max(1000).allow(''),
        validity_period: Joi.number().integer().min(1).max(365).default(30),
        additional_notes: Joi.string().max(5000).allow('')
      }),

      update: Joi.object({
        bidItems: Joi.array().min(1).items(
          Joi.object({
            rfq_item_id: Joi.string().required(),
            item_name: Joi.string().min(1).max(255).required(),
            unit_price: Joi.number().min(0).precision(2).required(),
            quantity: Joi.number().integer().min(1).required(),
            delivery_days: Joi.number().integer().min(0).allow(null),
            specifications: Joi.object().default({}),
            alternatives: Joi.array().items(
              Joi.object({
                description: Joi.string().max(500).required(),
                unitPrice: Joi.number().min(0).precision(2).required(),
                totalPrice: Joi.number().min(0).precision(2).required(),
                notes: Joi.string().max(1000).allow('')
              })
            ).default([]),
            notes: Joi.string().max(1000).allow('')
          })
        ),
        bidData: Joi.object({
          generalTerms: Joi.object(),
          deliverySchedule: Joi.object(),
          paymentTerms: Joi.string().max(1000).allow(''),
          validityPeriod: Joi.number().integer().min(1).max(365),
          additionalNotes: Joi.string().max(5000).allow('')
        }),
        attachments: Joi.array().items(
          Joi.object({
            filename: Joi.string().required(),
            originalName: Joi.string().required(),
            fileSize: Joi.number().integer().min(1).max(10485760).required(),
            mimeType: Joi.string().required(),
            filePath: Joi.string().required(),
            uploadedAt: Joi.date()
          })
        ),
        delivery_days: Joi.number().integer().min(0).allow(null),
        payment_terms: Joi.string().max(1000).allow(''),
        validity_period: Joi.number().integer().min(1).max(365),
        additional_notes: Joi.string().max(5000).allow('')
      }).min(1)
    };
  }

  // Validate submission data
  static validateCreate(data) {
    return this.getValidationSchema().create.validate(data, { abortEarly: false });
  }

  static validateUpdate(data) {
    return this.getValidationSchema().update.validate(data, { abortEarly: false });
  }

  // Get all submissions for an RFQ
  static async findByRFQ(rfqId, userId) {
    try {
      // Check if user has access to this RFQ
      const rfqCheck = await query(
        'SELECT id FROM rfqs WHERE id = $1 AND creator_id = $2',
        [rfqId, userId]
      );

      if (rfqCheck.rows.length === 0) {
        throw new Error('RFQ not found or access denied');
      }

      const submissionsQuery = `
        SELECT 
          rs.*,
          v.name as vendor_name,
          v.contact_email as vendor_email,
          v.performance_score,
          v.category as vendor_category,
          ri.sent_at as invitation_sent_at,
          ri.viewed_at as invitation_viewed_at
        FROM rfq_submissions rs
        LEFT JOIN vendors v ON rs.vendor_id = v.id
        LEFT JOIN rfq_invitations ri ON rs.rfq_id = ri.rfq_id AND rs.vendor_id = ri.vendor_id
        WHERE rs.rfq_id = $1
        ORDER BY rs.submitted_at ASC
      `;

      const result = await query(submissionsQuery, [rfqId]);

      // Get bid items for each submission
      for (let submission of result.rows) {
        const bidItemsQuery = `
          SELECT * FROM bid_items 
          WHERE submission_id = $1 
          ORDER BY rfq_item_id
        `;
        const bidItemsResult = await query(bidItemsQuery, [submission.id]);
        submission.bid_items = bidItemsResult.rows;
      }

      return result.rows;
    } catch (error) {
      console.error('Error in RFQSubmission.findByRFQ:', error);
      throw error;
    }
  }

  // Find submission by ID
  static async findById(id, userId = null) {
    try {
      const submissionQuery = `
        SELECT 
          rs.*,
          v.name as vendor_name,
          v.contact_email as vendor_email,
          v.performance_score,
          v.category as vendor_category,
          r.title as rfq_title,
          r.due_date as rfq_due_date,
          r.creator_id as rfq_creator_id,
          ri.sent_at as invitation_sent_at,
          ri.viewed_at as invitation_viewed_at
        FROM rfq_submissions rs
        LEFT JOIN vendors v ON rs.vendor_id = v.id
        LEFT JOIN rfqs r ON rs.rfq_id = r.id
        LEFT JOIN rfq_invitations ri ON rs.rfq_id = ri.rfq_id AND rs.vendor_id = ri.vendor_id
        WHERE rs.id = $1
      `;

      const result = await query(submissionQuery, [id]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const submission = result.rows[0];

      // Check access permissions if userId provided
      if (userId && submission.rfq_creator_id !== userId) {
        throw new Error('Access denied');
      }

      // Get bid items
      const bidItemsQuery = `
        SELECT * FROM bid_items 
        WHERE submission_id = $1 
        ORDER BY rfq_item_id
      `;
      const bidItemsResult = await query(bidItemsQuery, [id]);
      submission.bid_items = bidItemsResult.rows;

      return submission;
    } catch (error) {
      console.error('Error in RFQSubmission.findById:', error);
      throw error;
    }
  }

  // Find submission by token (for public access)
  static async findByToken(token) {
    try {
      const submissionQuery = `
        SELECT 
          rs.*,
          v.name as vendor_name,
          v.contact_email as vendor_email,
          r.title as rfq_title,
          r.description as rfq_description,
          r.items as rfq_items,
          r.due_date as rfq_due_date,
          r.form_config as rfq_form_config,
          r.terms as rfq_terms,
          ri.expires_at as token_expires_at
        FROM rfq_submissions rs
        LEFT JOIN vendors v ON rs.vendor_id = v.id
        LEFT JOIN rfqs r ON rs.rfq_id = r.id
        LEFT JOIN rfq_invitations ri ON rs.invitation_token = ri.token
        WHERE rs.invitation_token = $1
      `;

      const result = await query(submissionQuery, [token]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const submission = result.rows[0];

      // Check if token is expired
      if (new Date() > new Date(submission.token_expires_at)) {
        throw new Error('Submission token has expired');
      }

      // Get bid items
      const bidItemsQuery = `
        SELECT * FROM bid_items 
        WHERE submission_id = $1 
        ORDER BY rfq_item_id
      `;
      const bidItemsResult = await query(bidItemsQuery, [submission.id]);
      submission.bid_items = bidItemsResult.rows;

      return submission;
    } catch (error) {
      console.error('Error in RFQSubmission.findByToken:', error);
      throw error;
    }
  }

  // Create new submission (public access via token)
  static async create(submissionData) {
    const client = await beginTransaction();
    
    try {
      // For public submissions, validate token first to get vendor_id
      if (submissionData.invitation_token && !submissionData.vendor_id) {
        const tokenValidation = await this.validateToken(submissionData.invitation_token);
        if (!tokenValidation.valid) {
          throw new Error(tokenValidation.error);
        }
        // Add vendor_id from token validation
        submissionData.vendor_id = tokenValidation.invitation.vendor_id;
      }

      // Create a modified validation schema for public submissions
      const baseSchema = this.getValidationSchema().create;
      const publicValidationSchema = baseSchema.keys({
        invitation_token: Joi.string().min(1).required() // Allow any length for invitation_token
      });

      // Validate input data
      const { error, value } = publicValidationSchema.validate(submissionData, { abortEarly: false });
      if (error) {
        throw new Error(`Validation error: ${error.details.map(d => d.message).join(', ')}`);
      }

      const {
        rfq_id,
        vendor_id,
        invitation_token,
        bidItems,
        bidData,
        attachments,
        currency,
        delivery_days,
        payment_terms,
        validity_period,
        additional_notes
      } = value;

      // Verify invitation token and check if it's valid
      const invitationCheck = await client.query(`
        SELECT ri.*, r.due_date, r.status as rfq_status
        FROM rfq_invitations ri
        LEFT JOIN rfqs r ON ri.rfq_id = r.id
        WHERE ri.token = $1 AND ri.rfq_id = $2 AND ri.vendor_id = $3
      `, [invitation_token, rfq_id, vendor_id]);

      if (invitationCheck.rows.length === 0) {
        throw new Error('Invalid invitation token');
      }

      const invitation = invitationCheck.rows[0];

      // Check if RFQ is still accepting submissions
      if (invitation.rfq_status === 'closed' || invitation.rfq_status === 'cancelled') {
        throw new Error('RFQ is no longer accepting submissions');
      }

      // Check if deadline has passed
      if (new Date() > new Date(invitation.due_date)) {
        throw new Error('Submission deadline has passed');
      }

      // Check if submission already exists
      const existingSubmission = await client.query(
        'SELECT id FROM rfq_submissions WHERE rfq_id = $1 AND vendor_id = $2',
        [rfq_id, vendor_id]
      );

      if (existingSubmission.rows.length > 0) {
        throw new Error('Submission already exists for this vendor');
      }

      // Calculate total amount from bid items
      const totalAmount = bidItems.reduce((sum, item) => {
        return sum + (item.unit_price * item.quantity);
      }, 0);

      // Create submission
      const submissionQuery = `
        INSERT INTO rfq_submissions (
          rfq_id, vendor_id, invitation_token, bid_data, attachments,
          total_amount, currency, delivery_days, payment_terms,
          validity_period, additional_notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING *
      `;

      const submissionParams = [
        rfq_id,
        vendor_id,
        invitation_token,
        JSON.stringify(bidData),
        JSON.stringify(attachments),
        totalAmount,
        currency,
        delivery_days,
        payment_terms || '',
        validity_period,
        additional_notes || ''
      ];

      const submissionResult = await client.query(submissionQuery, submissionParams);
      const submission = submissionResult.rows[0];

      // Create bid items
      const bidItemPromises = bidItems.map(async (item) => {
        const totalPrice = item.unit_price * item.quantity;
        
        const bidItemQuery = `
          INSERT INTO bid_items (
            submission_id, rfq_item_id, item_name, unit_price, quantity,
            total_price, delivery_days, specifications, alternatives, notes
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
          RETURNING *
        `;

        return await client.query(bidItemQuery, [
          submission.id,
          item.rfq_item_id,
          item.item_name,
          item.unit_price,
          item.quantity,
          totalPrice,
          item.delivery_days,
          JSON.stringify(item.specifications),
          JSON.stringify(item.alternatives),
          item.notes || ''
        ]);
      });

      const bidItemResults = await Promise.all(bidItemPromises);
      const createdBidItems = bidItemResults.map(result => result.rows[0]);

      // Update invitation status
      await client.query(
        `UPDATE rfq_invitations 
         SET status = 'submitted', submitted_at = CURRENT_TIMESTAMP
         WHERE token = $1`,
        [invitation_token]
      );

      await commitTransaction(client);

      // Return submission with bid items
      return {
        ...submission,
        bid_items: createdBidItems
      };
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in RFQSubmission.create:', error);
      throw error;
    }
  }

  // Update existing submission (public access via token)
  static async update(id, updates, token) {
    const client = await beginTransaction();
    
    try {
      // Validate input data
      const { error, value } = this.validateUpdate(updates);
      if (error) {
        throw new Error(`Validation error: ${error.details.map(d => d.message).join(', ')}`);
      }

      // Verify submission exists and token is valid
      const submissionCheck = await client.query(`
        SELECT rs.*, ri.expires_at, r.due_date, r.status as rfq_status
        FROM rfq_submissions rs
        LEFT JOIN rfq_invitations ri ON rs.invitation_token = ri.token
        LEFT JOIN rfqs r ON rs.rfq_id = r.id
        WHERE rs.id = $1 AND rs.invitation_token = $2
      `, [id, token]);

      if (submissionCheck.rows.length === 0) {
        throw new Error('Submission not found or invalid token');
      }

      const currentSubmission = submissionCheck.rows[0];

      // Check if RFQ is still accepting updates
      if (currentSubmission.rfq_status === 'closed' || currentSubmission.rfq_status === 'cancelled') {
        throw new Error('RFQ is no longer accepting submission updates');
      }

      // Check if deadline has passed
      if (new Date() > new Date(currentSubmission.due_date)) {
        throw new Error('Submission deadline has passed');
      }

      // Build dynamic update query
      const updateFields = [];
      const params = [id];
      let paramCount = 1;

      const allowedFields = [
        'bid_data', 'attachments', 'delivery_days', 'payment_terms',
        'validity_period', 'additional_notes'
      ];

      for (const field of allowedFields) {
        if (value[field] !== undefined) {
          paramCount++;
          if (field === 'bid_data' || field === 'attachments') {
            updateFields.push(`${field} = $${paramCount}`);
            params.push(JSON.stringify(value[field]));
          } else {
            updateFields.push(`${field} = $${paramCount}`);
            params.push(value[field]);
          }
        }
      }

      // Handle bid items update
      if (value.bidItems) {
        // Delete existing bid items
        await client.query('DELETE FROM bid_items WHERE submission_id = $1', [id]);

        // Calculate new total amount
        const totalAmount = value.bidItems.reduce((sum, item) => {
          return sum + (item.unit_price * item.quantity);
        }, 0);

        // Update total amount
        paramCount++;
        updateFields.push(`total_amount = $${paramCount}`);
        params.push(totalAmount);

        // Create new bid items
        const bidItemPromises = value.bidItems.map(async (item) => {
          const totalPrice = item.unit_price * item.quantity;
          
          const bidItemQuery = `
            INSERT INTO bid_items (
              submission_id, rfq_item_id, item_name, unit_price, quantity,
              total_price, delivery_days, specifications, alternatives, notes
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
          `;

          return await client.query(bidItemQuery, [
            id,
            item.rfq_item_id,
            item.item_name,
            item.unit_price,
            item.quantity,
            totalPrice,
            item.delivery_days,
            JSON.stringify(item.specifications),
            JSON.stringify(item.alternatives),
            item.notes || ''
          ]);
        });

        await Promise.all(bidItemPromises);
      }

      if (updateFields.length === 0) {
        await rollbackTransaction(client);
        return currentSubmission;
      }

      // Add updated_at
      updateFields.push('updated_at = CURRENT_TIMESTAMP');

      const updateQuery = `
        UPDATE rfq_submissions 
        SET ${updateFields.join(', ')}
        WHERE id = $1
        RETURNING *
      `;

      const result = await client.query(updateQuery, params);
      const updatedSubmission = result.rows[0];

      await commitTransaction(client);

      // Return updated submission with full details
      return await this.findById(id);
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in RFQSubmission.update:', error);
      throw error;
    }
  }

  // Delete submission (only before RFQ deadline)
  static async delete(id, token) {
    const client = await beginTransaction();
    
    try {
      // Verify submission exists and token is valid
      const submissionCheck = await client.query(`
        SELECT rs.*, r.due_date, r.status as rfq_status
        FROM rfq_submissions rs
        LEFT JOIN rfqs r ON rs.rfq_id = r.id
        WHERE rs.id = $1 AND rs.invitation_token = $2
      `, [id, token]);

      if (submissionCheck.rows.length === 0) {
        throw new Error('Submission not found or invalid token');
      }

      const submission = submissionCheck.rows[0];

      // Check if RFQ allows deletion
      if (submission.rfq_status === 'closed' || submission.rfq_status === 'cancelled') {
        throw new Error('Cannot delete submission for closed or cancelled RFQ');
      }

      // Check if deadline has passed
      if (new Date() > new Date(submission.due_date)) {
        throw new Error('Cannot delete submission after deadline');
      }

      // Delete bid items first (cascade should handle this, but being explicit)
      await client.query('DELETE FROM bid_items WHERE submission_id = $1', [id]);

      // Delete submission
      const result = await client.query(
        'DELETE FROM rfq_submissions WHERE id = $1 RETURNING id',
        [id]
      );

      // Update invitation status back to sent
      await client.query(
        `UPDATE rfq_invitations 
         SET status = 'sent', submitted_at = NULL
         WHERE token = $1`,
        [token]
      );

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      console.error('Error in RFQSubmission.delete:', error);
      throw error;
    }
  }

  // Get submission comparison data for RFQ analysis
  static async getComparisonData(rfqId, userId) {
    try {
      // Check if user has access to this RFQ
      const rfqCheck = await query(
        'SELECT id, items FROM rfqs WHERE id = $1 AND creator_id = $2',
        [rfqId, userId]
      );

      if (rfqCheck.rows.length === 0) {
        throw new Error('RFQ not found or access denied');
      }

      const rfqItems = rfqCheck.rows[0].items;

      // Get all submissions with bid items
      const submissions = await this.findByRFQ(rfqId, userId);

      // Structure data for comparison
      const comparisonData = {
        rfq_items: rfqItems,
        submissions: submissions.map(submission => ({
          id: submission.id,
          vendor_id: submission.vendor_id,
          vendor_name: submission.vendor_name,
          vendor_performance_score: submission.performance_score,
          total_amount: submission.total_amount,
          currency: submission.currency,
          delivery_days: submission.delivery_days,
          payment_terms: submission.payment_terms,
          validity_period: submission.validity_period,
          submitted_at: submission.submitted_at,
          bid_items: submission.bid_items.reduce((acc, item) => {
            acc[item.rfq_item_id] = {
              unit_price: item.unit_price,
              total_price: item.total_price,
              delivery_days: item.delivery_days,
              specifications: item.specifications,
              alternatives: item.alternatives,
              notes: item.notes
            };
            return acc;
          }, {})
        })),
        statistics: {
          total_submissions: submissions.length,
          lowest_total_bid: submissions.length > 0 ? Math.min(...submissions.map(s => s.total_amount)) : 0,
          highest_total_bid: submissions.length > 0 ? Math.max(...submissions.map(s => s.total_amount)) : 0,
          average_total_bid: submissions.length > 0 ? submissions.reduce((sum, s) => sum + s.total_amount, 0) / submissions.length : 0
        }
      };

      return comparisonData;
    } catch (error) {
      console.error('Error in RFQSubmission.getComparisonData:', error);
      throw error;
    }
  }

  // Get submission analytics for a specific submission
  static async getSubmissionAnalytics(id, userId) {
    try {
      const submission = await this.findById(id, userId);
      if (!submission) {
        throw new Error('Submission not found');
      }

      // Get comparison with other submissions for the same RFQ
      const allSubmissions = await this.findByRFQ(submission.rfq_id, userId);
      
      const analytics = {
        submission_id: id,
        vendor_name: submission.vendor_name,
        total_amount: submission.total_amount,
        rank_by_price: allSubmissions
          .sort((a, b) => a.total_amount - b.total_amount)
          .findIndex(s => s.id === id) + 1,
        total_competitors: allSubmissions.length,
        price_difference_from_lowest: submission.total_amount - Math.min(...allSubmissions.map(s => s.total_amount)),
        price_difference_from_average: submission.total_amount - (allSubmissions.reduce((sum, s) => sum + s.total_amount, 0) / allSubmissions.length),
        delivery_days: submission.delivery_days,
        response_time_hours: submission.invitation_sent_at ? 
          Math.round((new Date(submission.submitted_at) - new Date(submission.invitation_sent_at)) / (1000 * 60 * 60)) : null,
        item_count: submission.bid_items.length,
        has_alternatives: submission.bid_items.some(item => item.alternatives && item.alternatives.length > 0),
        has_attachments: submission.attachments && submission.attachments.length > 0
      };

      return analytics;
    } catch (error) {
      console.error('Error in RFQSubmission.getSubmissionAnalytics:', error);
      throw error;
    }
  }

  // Validate invitation token (for public access)
  static async validateToken(token) {
    try {
      const result = await query(`
        SELECT 
          ri.*,
          r.title as rfq_title,
          r.description as rfq_description,
          r.items as rfq_items,
          r.due_date as rfq_due_date,
          r.form_config as rfq_form_config,
          r.terms as rfq_terms,
          r.status as rfq_status,
          r.allow_partial_selection as rfq_allow_partial_selection,
          r.partial_selection_config as rfq_partial_selection_config,
          v.name as vendor_name,
          v.contact_email as vendor_email
        FROM rfq_invitations ri
        LEFT JOIN rfqs r ON ri.rfq_id = r.id
        LEFT JOIN vendors v ON ri.vendor_id = v.id
        WHERE ri.token = $1
      `, [token]);

      if (result.rows.length === 0) {
        return { valid: false, error: 'Invalid token' };
      }

      const invitation = result.rows[0];

      // Check if token is expired
      if (new Date() > new Date(invitation.expires_at)) {
        return { valid: false, error: 'Token has expired' };
      }

      // Check if RFQ is still accepting submissions
      if (invitation.rfq_status === 'closed' || invitation.rfq_status === 'cancelled') {
        return { valid: false, error: 'RFQ is no longer accepting submissions' };
      }

      // Check if submission already exists
      const existingSubmission = await query(
        'SELECT id FROM rfq_submissions WHERE rfq_id = $1 AND vendor_id = $2',
        [invitation.rfq_id, invitation.vendor_id]
      );

      return {
        valid: true,
        invitation,
        has_existing_submission: existingSubmission.rows.length > 0,
        existing_submission_id: existingSubmission.rows.length > 0 ? existingSubmission.rows[0].id : null
      };
    } catch (error) {
      console.error('Error in RFQSubmission.validateToken:', error);
      throw error;
    }
  }
}

module.exports = RFQSubmission;