# RFQ CRUD Testing Guide

This guide explains how to test the enhanced RFQ API with comprehensive CRUD operations and sample data.

## Overview

The RFQ testing suite includes:

- **Complete CRUD operations** with full form data validation
- **2 sample RFQs** with realistic, comprehensive data
- **4 detailed submissions** (2 per RFQ) with complete form responses
- **Enhanced API features** testing (filtering, search, pagination, bulk operations)
- **Analytics and AI recommendations** verification

## Prerequisites

1. **Database Setup**: Ensure PostgreSQL is running and the VendorMS database exists
2. **Backend Server**: The backend server should be running on port 3001
3. **Required Tables**: All database tables should be created (run migrations if needed)
4. **Dependencies**: Install required npm packages

```bash
cd backend
npm install
```

## Quick Start

### Option 1: Complete Setup and Testing (Recommended)

Run the comprehensive setup script that creates sample data and runs all tests:

```bash
node setup-test-data.js
```

This script will:

- Create 5 sample vendor records (if they don't exist)
- Create a test user (if needed)
- Run comprehensive RFQ CRUD tests
- Create 2 RFQs with full form configurations
- Create 4 detailed submissions
- Test all API endpoints and features

### Option 2: Run Tests Only

If you already have vendors and users set up:

```bash
node test-rfq-crud-complete.js
```

### Option 3: Individual Test Components

Run specific test components:

```bash
# Test enhanced API features only
node test-enhanced-rfq-api.js

# Run the original comprehensive test
node test-rfq-crud-full.js
```

## Sample Data Created

### RFQ 1: Office Equipment & Furniture Procurement

- **Items**: 5 items (desks, chairs, laptops, printers, supplies)
- **Form Fields**: 10 comprehensive form fields
- **Vendors**: 4 invited vendors
- **Submissions**: 2 detailed submissions with full responses
- **Features**: Complex specifications, custom fields, AI settings

### RFQ 2: Marketing Campaign Materials & Promotional Items

- **Items**: 5 items (brochures, business cards, banners, USB drives, apparel)
- **Form Fields**: 8 marketing-specific form fields
- **Vendors**: 4 invited vendors
- **Submissions**: 2 detailed submissions with creative requirements
- **Features**: Design services, proofing processes, rush delivery options

## Test Coverage

### CRUD Operations

- ✅ **Create**: RFQs with complete form configurations
- ✅ **Read**: Full RFQ details with all relationships
- ✅ **Update**: Complex updates with validation
- ✅ **Delete**: Soft delete with status management

### Enhanced Features

- ✅ **Advanced Filtering**: Search, status, category, vendor filtering
- ✅ **Cursor-based Pagination**: Performance optimization for large datasets
- ✅ **Bulk Operations**: Bulk update and delete with validation
- ✅ **Advanced Search**: Full-text search with relevance scoring
- ✅ **Export Functionality**: CSV, JSON export with filtering
- ✅ **Analytics**: Response rates, bid analysis, performance metrics

### Submission Features

- ✅ **Submission Views**: Detailed submission listings
- ✅ **Comparison Views**: Side-by-side submission comparison
- ✅ **Analytics**: Response rates, bid statistics, timing analysis
- ✅ **AI Recommendations**: Vendor scoring and recommendations
- ✅ **Audit History**: Complete audit trail for all operations

## API Endpoints Tested

### Core RFQ Endpoints

```
GET    /api/rfqs                    # Enhanced listing with filtering
GET    /api/rfqs/:id               # Detailed RFQ view
POST   /api/rfqs                   # Create RFQ with full form data
PUT    /api/rfqs/:id               # Update RFQ
DELETE /api/rfqs/:id               # Soft delete RFQ
POST   /api/rfqs/:id/send          # Send invitations
```

### Enhanced Endpoints

```
POST   /api/rfqs/search            # Advanced search
GET    /api/rfqs/summary           # Summary statistics
GET    /api/rfqs/export            # Export functionality
PUT    /api/rfqs/bulk              # Bulk update
DELETE /api/rfqs/bulk              # Bulk delete
```

### Submission Endpoints

```
GET    /api/rfqs/:id/submissions           # Get submissions
GET    /api/rfqs/:id/submissions/comparison # Comparison view
GET    /api/rfqs/:id/analytics             # Analytics
POST   /api/rfqs/:id/ai-recommend          # AI recommendations
GET    /api/rfqs/:id/audit                 # Audit history
```

## Expected Test Output

When tests run successfully, you should see:

```
🚀 VENDORMS RFQ TEST DATA SETUP & COMPREHENSIVE TESTING
======================================================================
🏢 Setting up vendor records...
✅ Created vendor: Premium Office Solutions Inc. (ID: 1)
✅ Created vendor: TechEquip Pro (ID: 2)
...

🧪 Starting comprehensive RFQ testing...
✅ Office Equipment RFQ created successfully
   ID: 1
   Items: 5
   Form Fields: 10
   Vendors: 4

✅ Marketing Materials RFQ created successfully
   ID: 2
   Items: 5
   Form Fields: 8

📝 Creating Detailed RFQ Submissions with Full Form Data...
✅ Submission 1 created successfully
   Total Amount: $28,750
   Items: 5
   Form Responses: 6 fields

🎊 COMPREHENSIVE TEST SUITE COMPLETED SUCCESSFULLY! 🎊
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**

   ```
   Error: connect ECONNREFUSED 127.0.0.1:5432
   ```

   - Ensure PostgreSQL is running
   - Check database connection settings in `config/database.js`

2. **Table Does Not Exist**

   ```
   Error: relation "rfqs" does not exist
   ```

   - Run database migrations to create required tables
   - Check that all tables are properly created

3. **Backend Server Not Running**

   ```
   Error: connect ECONNREFUSED 127.0.0.1:3001
   ```

   - Start the backend server: `npm start` or `node server.js`
   - Verify the server is running on port 3001

4. **Authentication Issues**
   ```
   Error: 401 Unauthorized
   ```
   - The tests use mock authentication
   - Ensure auth middleware is properly configured

### Verification Steps

After successful test completion, verify the data in your database:

```sql
-- Check created RFQs
SELECT id, title, status, created_at FROM rfqs ORDER BY created_at DESC LIMIT 5;

-- Check submissions
SELECT rfq_id, vendor_id, total_amount, submitted_at FROM rfq_submissions ORDER BY submitted_at DESC;

-- Check invitations
SELECT rfq_id, vendor_id, status FROM rfq_invitations WHERE rfq_id IN (SELECT id FROM rfqs ORDER BY created_at DESC LIMIT 2);

-- Check vendors
SELECT id, name, contact_email, performance_score FROM vendors ORDER BY id;
```

## Using the Sample Data

Once the tests complete successfully, you can:

1. **Test the Frontend**: Use the created RFQs and submissions to test UI components
2. **API Development**: Use the sample data for further API development
3. **Performance Testing**: The data provides a good baseline for performance testing
4. **Feature Validation**: Verify that all RFQ features work with realistic data

## Next Steps

After running the tests:

1. **Frontend Integration**: Connect your React components to the tested API endpoints
2. **UI Testing**: Use the sample RFQs to test the complete user interface
3. **Performance Optimization**: Monitor API response times with the sample data
4. **Feature Enhancement**: Build upon the tested foundation to add new features

## Support

If you encounter issues:

1. Check the console output for detailed error messages
2. Verify all prerequisites are met
3. Review the database logs for connection issues
4. Ensure all required environment variables are set

The test suite provides comprehensive coverage of the RFQ system and creates realistic sample data for thorough testing of your VendorMS application.
