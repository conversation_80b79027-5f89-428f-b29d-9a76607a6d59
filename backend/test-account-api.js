const Account = require('./models/Account');

async function testAccountAPI() {
  console.log('🧪 Testing Account API...\n');

  try {
    // Test 1: Create a test account
    console.log('1. Testing account creation...');
    const testAccount = {
      name: 'Test Corporation',
      account_number: 'ACC-001',
      type: 'Prospect',
      industry: 'Technology',
      annual_revenue: 1000000,
      number_of_employees: 50,
      ownership: 'Private',
      phone: '******-0123',
      website: 'https://testcorp.com',
      rating: 'Hot',
      description: 'A test corporation for API testing',
      billing_address: {
        street: '123 Test Street',
        city: 'Test City',
        state: 'CA',
        postalCode: '12345',
        country: 'USA'
      },
      custom_fields: {
        test_field: 'test_value'
      }
    };

    const createdAccount = await Account.create(testAccount, 1);
    console.log('✅ Account created successfully:', createdAccount.name);
    console.log('   ID:', createdAccount.id);

    // Test 2: Find account by ID
    console.log('\n2. Testing account retrieval...');
    const foundAccount = await Account.findById(createdAccount.id);
    console.log('✅ Account found:', foundAccount.name);

    // Test 3: Update account
    console.log('\n3. Testing account update...');
    const updateData = {
      rating: 'Warm',
      annual_revenue: 1500000,
      description: 'Updated test corporation'
    };
    const updatedAccount = await Account.update(createdAccount.id, updateData, 1);
    console.log('✅ Account updated successfully');
    console.log('   New rating:', updatedAccount.rating);
    console.log('   New revenue:', updatedAccount.annual_revenue);

    // Test 4: Search accounts
    console.log('\n4. Testing account search...');
    const searchResults = await Account.findAll({ search: 'Test' }, 1, 10);
    console.log('✅ Search completed');
    console.log('   Found', searchResults.accounts.length, 'accounts');

    // Test 5: Create child account for hierarchy testing
    console.log('\n5. Testing account hierarchy...');
    const childAccount = {
      name: 'Test Subsidiary',
      parent_account_id: createdAccount.id,
      type: 'Customer - Direct',
      industry: 'Technology'
    };
    const createdChild = await Account.create(childAccount, 1);
    console.log('✅ Child account created:', createdChild.name);

    // Test hierarchy retrieval
    const hierarchy = await Account.getHierarchy(createdAccount.id);
    console.log('✅ Hierarchy retrieved:', hierarchy.length, 'levels');

    const children = await Account.getChildren(createdAccount.id);
    console.log('✅ Children retrieved:', children.length, 'child accounts');

    // Test 6: Export functionality
    console.log('\n6. Testing export functionality...');
    const exportData = await Account.exportToCSV({});
    console.log('✅ Export completed');
    console.log('   Headers:', exportData.headers.length);
    console.log('   Rows:', exportData.rows.length);

    // Test 7: Get audit history
    console.log('\n7. Testing audit history...');
    const auditHistory = await Account.getAuditHistory(createdAccount.id);
    console.log('✅ Audit history retrieved:', auditHistory.length, 'entries');

    // Test 8: Get industries and types
    console.log('\n8. Testing metadata retrieval...');
    const industries = await Account.getIndustries();
    const types = await Account.getTypes();
    console.log('✅ Industries retrieved:', industries.length);
    console.log('✅ Types retrieved:', types.length);

    // Test 9: Validation
    console.log('\n9. Testing validation...');
    const validationErrors = Account.validateAccountData({
      name: '', // Should fail - empty name
      annual_revenue: -1000, // Should fail - negative revenue
      website: 'invalid-url' // Should fail - invalid URL
    });
    console.log('✅ Validation working:', validationErrors.length, 'errors found');

    // Clean up - soft delete test accounts
    console.log('\n10. Cleaning up test data...');
    await Account.delete(createdChild.id, 1);
    await Account.delete(createdAccount.id, 1);
    console.log('✅ Test accounts deleted');

    console.log('\n🎉 All Account API tests passed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the test
testAccountAPI().then(() => {
  console.log('\n✅ Test completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});