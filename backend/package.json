{"name": "vendorms-backend", "version": "1.0.0", "description": "VendorMS Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:rfq": "node test-rfq-crud-complete.js", "test:rfq-enhanced": "node test-enhanced-rfq-api.js", "setup:test-data": "node setup-test-data.js"}, "dependencies": {"aws-sdk": "^2.1490.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "docusign-esign": "^6.4.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.2.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pdfkit": "^0.17.1", "pg": "^8.11.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "socket.io": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}, "keywords": ["vendor", "management", "api", "postgresql", "contracts", "docusign"], "author": "VendorMS Team", "license": "MIT"}