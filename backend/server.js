const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const vendorRoutes = require('./routes/vendors');
const contractRoutes = require('./routes/contracts');
const invoiceRoutes = require('./routes/invoices');
const paymentRoutes = require('./routes/payments');
const aiConfigRoutes = require('./routes/aiConfig');
const collaborationRoutes = require('./routes/collaboration');
const commentsRoutes = require('./routes/comments');
const rfqRoutes = require('./routes/rfqs');
const publicSubmissionRoutes = require('./routes/publicSubmissions');
const quoteRoutes = require('./routes/quotes');
const accountRoutes = require('./routes/accounts');
const contactRoutes = require('./routes/contacts');
const opportunityRoutes = require('./routes/opportunities');

// Create Express app and HTTP server
const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 3001;

// Create Socket.IO instance
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || ['http://localhost:8080', 'http://localhost:8081'],
    credentials: true,
  },
});

// Make Socket.IO available to routes
app.set('io', io);

// Middleware
app.use(helmet());
app.use(compression());
app.use(morgan('combined'));
app.use(cors({
  origin: process.env.FRONTEND_URL || ['http://localhost:8080', 'http://localhost:8081'],
  credentials: true,
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/vendors', vendorRoutes);
app.use('/api/contracts', contractRoutes);
app.use('/api/invoices', invoiceRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/ai', aiConfigRoutes);
app.use('/api/collaboration', collaborationRoutes);
app.use('/api/comments', commentsRoutes);
app.use('/api/rfqs', rfqRoutes);
app.use('/api/quotes', quoteRoutes);
app.use('/api/accounts', accountRoutes);
app.use('/api/contacts', contactRoutes);
app.use('/api/opportunities', opportunityRoutes);
app.use('/api/public/rfq', publicSubmissionRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Global error handler:', err);
  
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
  });
});

// Socket.IO authentication middleware
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  
  if (!token) {
    return next(new Error('Authentication error'));
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    socket.userId = decoded.id;
    socket.userRole = decoded.role;
    next();
  } catch (err) {
    next(new Error('Authentication error'));
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`👤 User ${socket.userId} connected`);
  
  // Join user to their personal room
  socket.join(`user_${socket.userId}`);
  
  // Handle joining entity-specific rooms
  socket.on('join_room', (data) => {
    const { entityType, entityId } = data;
    const roomName = `${entityType}_${entityId}`;
    socket.join(roomName);
    
    // Broadcast user presence to room
    socket.to(roomName).emit('user_joined', {
      userId: socket.userId,
      timestamp: new Date().toISOString(),
    });
    
    console.log(`👤 User ${socket.userId} joined room: ${roomName}`);
  });
  
  // Handle leaving rooms
  socket.on('leave_room', (data) => {
    const { entityType, entityId } = data;
    const roomName = `${entityType}_${entityId}`;
    socket.leave(roomName);
    
    // Broadcast user left to room
    socket.to(roomName).emit('user_left', {
      userId: socket.userId,
      timestamp: new Date().toISOString(),
    });
    
    console.log(`👤 User ${socket.userId} left room: ${roomName}`);
  });
  
  // Handle new comments
  socket.on('new_comment', (data) => {
    const { entityType, entityId, comment } = data;
    const roomName = `${entityType}_${entityId}`;
    
    // Broadcast comment to all users in the room
    io.to(roomName).emit('comment_added', {
      ...comment,
      userId: socket.userId,
      timestamp: new Date().toISOString(),
    });
    
    console.log(`💬 New comment in room: ${roomName}`);
  });
  
  // Handle typing indicators
  socket.on('typing_start', (data) => {
    const { entityType, entityId } = data;
    const roomName = `${entityType}_${entityId}`;
    
    socket.to(roomName).emit('user_typing', {
      userId: socket.userId,
      isTyping: true,
    });
  });
  
  socket.on('typing_stop', (data) => {
    const { entityType, entityId } = data;
    const roomName = `${entityType}_${entityId}`;
    
    socket.to(roomName).emit('user_typing', {
      userId: socket.userId,
      isTyping: false,
    });
  });
  
  // Handle disconnection
  socket.on('disconnect', () => {
    console.log(`👤 User ${socket.userId} disconnected`);
  });
});



// Start server
server.listen(PORT, () => {
  console.log(`🚀 VendorMS Backend API running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🔌 WebSocket server ready`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🔄 SIGTERM received, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🔄 SIGINT received, shutting down gracefully...');
  process.exit(0);
});