#!/usr/bin/env node

/**
 * Complete RFQ CRUD Test Suite with Full Form Data and Sample Records
 * Tests all RFQ operations and creates realistic sample data for UI testing
 */

const axios = require('axios');
const { query } = require('./config/database');

const BASE_URL = 'http://localhost:3001/api';

// Mock authentication token
const AUTH_TOKEN = 'mock-jwt-token';

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Complete RFQ test data with full form configuration
const completeRFQData1 = {
  title: "Office Equipment & Furniture Procurement RFQ",
  description: "Comprehensive request for quotation covering office furniture, IT equipment, and supplies for our new headquarters expansion. We require high-quality products with competitive pricing and reliable delivery schedules.",
  items: [
    {
      id: "item-001",
      name: "Executive Office Desks",
      description: "Premium executive desks with built-in storage and cable management",
      quantity: 8,
      specifications: {
        material: "Solid oak or equivalent hardwood",
        dimensions: "72\" W x 36\" D x 30\" H",
        finish: "Natural wood stain with protective coating",
        features: ["Built-in cable management", "Locking file drawer", "Keyboard tray", "CPU holder"]
      },
      category: "Office Furniture",
      estimatedPrice: 1200,
      customFields: {
        warranty_period: "5 years",
        assembly_required: true,
        delivery_method: "White glove delivery"
      }
    },
    {
      id: "item-002",
      name: "Ergonomic Task Chairs",
      description: "High-back ergonomic office chairs with lumbar support and adjustable features",
      quantity: 25,
      specifications: {
        type: "Executive task chair",
        back_material: "Breathable mesh",
        seat_material: "High-density foam with fabric upholstery",
        adjustments: ["Height", "Armrest", "Tilt tension", "Lumbar support"],
        weight_capacity: "300 lbs"
      },
      category: "Office Furniture",
      estimatedPrice: 450,
      customFields: {
        warranty_period: "3 years",
        color_options: ["Black", "Charcoal", "Navy Blue"],
        caster_type: "Carpet casters"
      }
    },
    {
      id: "item-003",
      name: "Business Laptops",
      description: "High-performance business laptops for professional use",
      quantity: 15,
      specifications: {
        processor: "Intel Core i7 12th Gen or AMD Ryzen 7",
        memory: "16GB DDR4 RAM",
        storage: "512GB NVMe SSD",
        display: "15.6\" Full HD IPS",
        graphics: "Integrated graphics",
        os: "Windows 11 Pro",
        connectivity: ["WiFi 6", "Bluetooth 5.2", "USB-C", "HDMI"]
      },
      category: "IT Equipment",
      estimatedPrice: 1400,
      customFields: {
        warranty_period: "3 years on-site",
        software_bundle: "Microsoft Office 365",
        security_features: "TPM 2.0, Fingerprint reader"
      }
    },
    {
      id: "item-004",
      name: "Multi-Function Printers",
      description: "Color laser multi-function printers for office use",
      quantity: 3,
      specifications: {
        type: "Color laser MFP",
        functions: ["Print", "Copy", "Scan", "Fax"],
        print_speed: "30 ppm color, 35 ppm mono",
        paper_capacity: "550 sheet tray + 100 sheet bypass",
        connectivity: ["Ethernet", "WiFi", "USB", "Mobile printing"]
      },
      category: "IT Equipment",
      estimatedPrice: 800,
      customFields: {
        warranty_period: "2 years",
        maintenance_contract: "Available",
        toner_yield: "High capacity cartridges"
      }
    },
    {
      id: "item-005",
      name: "Office Supplies Bundle",
      description: "Complete office supplies package including stationery and consumables",
      quantity: 1,
      specifications: {
        contents: [
          "Copy paper (50 reams)",
          "Pens and pencils (assorted)",
          "Notebooks and notepads",
          "Folders and binders",
          "Staplers and hole punchers",
          "Desk organizers"
        ]
      },
      category: "Office Supplies",
      estimatedPrice: 500,
      customFields: {
        eco_friendly: "Recycled materials preferred",
        bulk_packaging: "Individual item packaging not required"
      }
    }
  ],
  due_date: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(), // 45 days from now
  selectedVendors: [6, 7, 8, 9], // Using newly created vendor IDs
  formConfig: [
    {
      id: "delivery_schedule",
      type: "select",
      label: "Preferred Delivery Schedule",
      required: true,
      options: ["All items together", "Furniture first, then IT", "Phased delivery over 2 months", "As available"],
      itemSpecific: false
    },
    {
      id: "payment_terms",
      type: "select",
      label: "Payment Terms",
      required: true,
      options: ["Net 30", "Net 45", "Net 60", "2/10 Net 30", "Progress payments"],
      itemSpecific: false
    },
    {
      id: "installation_setup",
      type: "select",
      label: "Installation and Setup Services",
      required: true,
      options: ["Full installation included", "Basic setup only", "Delivery only", "Custom arrangement"],
      itemSpecific: true
    },
    {
      id: "warranty_support",
      type: "select",
      label: "Warranty and Support Level",
      required: false,
      options: ["Standard warranty", "Extended warranty +1 year", "Extended warranty +2 years", "Premium support package"],
      itemSpecific: true
    },
    {
      id: "volume_discount",
      type: "number",
      label: "Volume Discount Percentage",
      required: false,
      validation: [
        { type: "min", value: 0 },
        { type: "max", value: 25 }
      ],
      itemSpecific: false
    },
    {
      id: "training_services",
      type: "select",
      label: "Training Services Required",
      required: false,
      options: ["No training needed", "Basic user training", "Administrator training", "Comprehensive training package"],
      itemSpecific: true
    },
    {
      id: "maintenance_contract",
      type: "select",
      label: "Maintenance Contract Options",
      required: false,
      options: ["No maintenance contract", "1 year maintenance", "2 year maintenance", "3 year maintenance"],
      itemSpecific: true
    },
    {
      id: "references_portfolio",
      type: "textarea",
      label: "Client References and Portfolio",
      required: true,
      itemSpecific: false
    },
    {
      id: "certifications_docs",
      type: "file",
      label: "Certifications and Quality Documents",
      required: false,
      itemSpecific: false
    },
    {
      id: "special_requirements",
      type: "textarea",
      label: "Special Requirements or Notes",
      required: false,
      itemSpecific: true
    }
  ],
  terms: `TERMS AND CONDITIONS FOR OFFICE EQUIPMENT & FURNITURE PROCUREMENT

1. SCOPE OF WORK
   - Supply and delivery of all specified items as per RFQ requirements
   - Installation and setup services as specified in vendor response
   - Warranty and support services as outlined in proposal

2. DELIVERY REQUIREMENTS
   - All items must be delivered to our headquarters address
   - Delivery must be coordinated with our facilities team
   - White glove delivery service required for furniture items
   - IT equipment must be delivered in original manufacturer packaging

3. QUALITY STANDARDS
   - All products must meet or exceed specified requirements
   - Items must be new, unused, and in original packaging
   - Furniture must comply with GREENGUARD certification standards
   - IT equipment must have current manufacturer warranty

4. INSTALLATION AND SETUP
   - Professional installation required for furniture items
   - IT equipment setup and configuration as specified
   - All packaging materials to be removed by vendor
   - Final inspection and sign-off required

5. PAYMENT TERMS
   - Payment upon satisfactory delivery and installation
   - Progress payments available for large orders
   - All invoices must include detailed item breakdown
   - Payment terms as agreed in vendor proposal

6. WARRANTY AND SUPPORT
   - Minimum manufacturer warranty required for all items
   - Extended warranty options to be clearly specified
   - Local service and support availability required
   - Response time commitments for service calls

7. EVALUATION CRITERIA
   - Price competitiveness (35%)
   - Product quality and specifications (25%)
   - Delivery timeline and logistics (20%)
   - Vendor experience and references (10%)
   - Warranty and support services (10%)

8. COMPLIANCE REQUIREMENTS
   - All vendors must be properly licensed and insured
   - Compliance with local safety and building codes
   - Environmental compliance for disposal of packaging
   - Data security requirements for IT equipment

9. CANCELLATION AND CHANGES
   - Right to cancel or modify order before delivery
   - Change orders must be approved in writing
   - Cancellation fees to be clearly specified
   - Force majeure provisions apply

10. CONFIDENTIALITY
    - All RFQ information is confidential and proprietary
    - Vendor proposals become property of requesting organization
    - Non-disclosure of pricing and technical information`,
  currency: "USD",
  aiSettings: {
    priceWeight: 0.35,
    performanceWeight: 0.25,
    deliveryWeight: 0.20,
    riskWeight: 0.10,
    diversificationPreference: 0.10
  }
};

const completeRFQData2 = {
  title: "Marketing Campaign Materials & Promotional Items RFQ",
  description: "Request for quotation for comprehensive marketing materials including print collateral, promotional items, and digital assets for our Q2 product launch campaign.",
  items: [
    {
      id: "item-101",
      name: "Product Launch Brochures",
      description: "High-quality tri-fold brochures showcasing new product features",
      quantity: 10000,
      specifications: {
        size: "8.5\" x 11\" tri-fold",
        paper_stock: "100lb gloss cover",
        printing: "4/4 full color CMYK",
        finish: "Aqueous coating",
        folding: "Letter fold (tri-fold)"
      },
      category: "Print Marketing",
      estimatedPrice: 0.85,
      customFields: {
        design_service: "Layout design included",
        proofing_rounds: "Up to 3 revisions",
        rush_option: "Available if needed"
      }
    },
    {
      id: "item-102",
      name: "Executive Business Cards",
      description: "Premium business cards for sales team and executives",
      quantity: 5000,
      specifications: {
        size: "3.5\" x 2\" standard",
        paper_stock: "18pt uncoated cardstock",
        printing: "4/4 full color with spot UV logo",
        finish: "Soft touch coating",
        corners: "Rounded corners optional"
      },
      category: "Print Marketing",
      estimatedPrice: 0.35,
      customFields: {
        variable_data: "Names and titles will vary",
        quantity_per_person: "250 cards per person",
        storage_boxes: "Individual storage boxes included"
      }
    },
    {
      id: "item-103",
      name: "Trade Show Banners",
      description: "Large format banners for trade show booth display",
      quantity: 6,
      specifications: {
        size: "8' x 10' retractable banner",
        material: "Premium vinyl with UV protection",
        printing: "Large format digital printing",
        hardware: "Retractable banner stand included",
        graphics: "High resolution artwork required"
      },
      category: "Display Marketing",
      estimatedPrice: 250,
      customFields: {
        design_service: "Graphic design included",
        setup_instructions: "Easy setup required",
        carrying_case: "Padded carrying case included"
      }
    },
    {
      id: "item-104",
      name: "Promotional USB Drives",
      description: "Custom branded USB drives for client gifts and trade shows",
      quantity: 2500,
      specifications: {
        capacity: "16GB minimum",
        material: "Metal housing preferred",
        branding: "Laser engraving with company logo",
        packaging: "Individual gift boxes",
        data_transfer: "USB 3.0 minimum speed"
      },
      category: "Promotional Items",
      estimatedPrice: 15,
      customFields: {
        preloaded_content: "Company presentation and brochures",
        custom_shape: "Logo-shaped design preferred",
        lanyard_included: "Branded lanyard with each drive"
      }
    },
    {
      id: "item-105",
      name: "Corporate Apparel",
      description: "Branded polo shirts and jackets for trade show staff",
      quantity: 50,
      specifications: {
        polo_shirts: "25 pieces - various sizes",
        jackets: "25 pieces - various sizes",
        material: "High-quality cotton blend",
        branding: "Embroidered company logo",
        colors: "Navy blue and charcoal gray"
      },
      category: "Promotional Items",
      estimatedPrice: 45,
      customFields: {
        size_breakdown: "S(5), M(15), L(20), XL(8), XXL(2)",
        care_instructions: "Machine washable",
        rush_delivery: "2-week delivery if possible"
      }
    }
  ],
  due_date: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString(), // 28 days from now
  selectedVendors: [7, 8, 9, 10], // Different vendor set
  formConfig: [
    {
      id: "design_services_level",
      type: "select",
      label: "Design Services Required",
      required: true,
      options: ["Complete design service", "Layout and typesetting only", "Minor revisions only", "No design service needed"],
      itemSpecific: true
    },
    {
      id: "proofing_process",
      type: "select",
      label: "Proofing and Approval Process",
      required: true,
      options: ["Digital proofs only", "Physical samples required", "Both digital and physical", "Press check required"],
      itemSpecific: true
    },
    {
      id: "delivery_timeline",
      type: "select",
      label: "Delivery Timeline Requirements",
      required: true,
      options: ["Standard delivery (3-4 weeks)", "Rush delivery (2 weeks)", "Express delivery (1 week)", "Flexible timeline"],
      itemSpecific: false
    },
    {
      id: "quantity_breaks",
      type: "textarea",
      label: "Quantity Break Pricing",
      required: false,
      itemSpecific: true
    },
    {
      id: "shipping_method",
      type: "select",
      label: "Preferred Shipping Method",
      required: true,
      options: ["Ground shipping", "Express shipping", "Freight delivery", "Local pickup available"],
      itemSpecific: false
    },
    {
      id: "quality_samples",
      type: "select",
      label: "Quality Samples Required",
      required: false,
      options: ["No samples needed", "Paper/material samples", "Printed samples", "Complete prototype"],
      itemSpecific: true
    },
    {
      id: "storage_fulfillment",
      type: "select",
      label: "Storage and Fulfillment Services",
      required: false,
      options: ["Not required", "Short-term storage (30 days)", "Long-term storage (90+ days)", "Drop shipping services"],
      itemSpecific: false
    },
    {
      id: "environmental_requirements",
      type: "select",
      label: "Environmental/Sustainability Requirements",
      required: false,
      options: ["No specific requirements", "Recycled materials preferred", "FSC certified paper required", "Carbon neutral shipping"],
      itemSpecific: true
    }
  ],
  terms: `MARKETING MATERIALS RFQ TERMS AND CONDITIONS

1. PROJECT SCOPE
   - Design, production, and delivery of all specified marketing materials
   - Quality control and proofing services as specified
   - Packaging and shipping to designated locations

2. DESIGN AND CREATIVE SERVICES
   - Original design work must be provided in editable formats
   - All fonts and images must be properly licensed
   - Brand guidelines compliance is mandatory
   - Unlimited revisions during initial design phase

3. QUALITY STANDARDS
   - All printed materials must meet commercial printing standards
   - Color accuracy within industry standard tolerances
   - Promotional items must pass quality inspection
   - Defective items will be replaced at no charge

4. DELIVERY AND LOGISTICS
   - On-time delivery is critical for campaign launch
   - Partial shipments acceptable with prior approval
   - Packaging must protect items during shipping
   - Delivery confirmation and tracking required

5. INTELLECTUAL PROPERTY
   - All designs become property of requesting organization
   - Vendor may not use designs for other clients
   - Copyright and trademark compliance required
   - Confidentiality of campaign information mandatory

6. PAYMENT AND PRICING
   - 50% deposit required for orders over $10,000
   - Final payment upon satisfactory delivery
   - Quantity break pricing encouraged
   - No hidden fees or surcharges

7. EVALUATION CRITERIA
   - Creative capabilities and design quality (30%)
   - Pricing competitiveness (25%)
   - Production timeline and reliability (25%)
   - Previous experience and portfolio (20%)`,
  currency: "USD",
  aiSettings: {
    priceWeight: 0.25,
    performanceWeight: 0.30,
    deliveryWeight: 0.30,
    riskWeight: 0.10,
    diversificationPreference: 0.05
  }
};

// Comprehensive submission data
const detailedSubmissions = [
  // Submissions for Office Equipment RFQ (RFQ 1)
  {
    rfq_id: null, // Will be set after RFQ creation
    vendor_id: 6,
    total_amount: 28750.00,
    currency: "USD",
    delivery_timeline: "Phased delivery over 2 months",
    notes: "We are pleased to submit our comprehensive proposal for your office equipment needs. Our company has 15+ years of experience in corporate furnishing and IT solutions. We offer competitive pricing with premium quality products and full-service installation.",
    bid_data: [
      {
        item_id: "item-001",
        unit_price: 1150.00,
        total_price: 9200.00,
        delivery_days: 21,
        notes: "Premium solid oak executive desks with lifetime structural warranty. Includes professional assembly and cable management setup.",
        custom_fields: {
          installation_setup: "Full installation included",
          warranty_support: "Extended warranty +1 year",
          special_requirements: "Custom cable management configuration available"
        }
      },
      {
        item_id: "item-002",
        unit_price: 420.00,
        total_price: 10500.00,
        delivery_days: 14,
        notes: "Ergonomic task chairs with advanced lumbar support. Available in all requested colors with carpet casters included.",
        custom_fields: {
          installation_setup: "Basic setup only",
          warranty_support: "Standard warranty",
          special_requirements: "Color coordination with office design"
        }
      },
      {
        item_id: "item-003",
        unit_price: 1350.00,
        total_price: 20250.00,
        delivery_days: 10,
        notes: "Latest generation business laptops with enhanced security features. Includes Windows 11 Pro and Office 365 setup.",
        custom_fields: {
          installation_setup: "Custom arrangement",
          warranty_support: "Premium support package",
          training_services: "Administrator training",
          special_requirements: "Domain joining and security policy configuration"
        }
      },
      {
        item_id: "item-004",
        unit_price: 750.00,
        total_price: 2250.00,
        delivery_days: 7,
        notes: "High-performance color laser MFPs with mobile printing capabilities. Includes 2-year maintenance contract.",
        custom_fields: {
          installation_setup: "Full installation included",
          warranty_support: "Extended warranty +1 year",
          maintenance_contract: "2 year maintenance",
          training_services: "Basic user training"
        }
      },
      {
        item_id: "item-005",
        unit_price: 450.00,
        total_price: 450.00,
        delivery_days: 5,
        notes: "Complete office supplies bundle with eco-friendly options. Bulk packaging to reduce waste.",
        custom_fields: {
          installation_setup: "Delivery only",
          special_requirements: "Eco-friendly materials where possible"
        }
      }
    ],
    form_responses: {
      delivery_schedule: "Phased delivery over 2 months",
      payment_terms: "Net 30",
      volume_discount: 8,
      references_portfolio: "Major clients include Fortune 500 companies: TechCorp (2023), GlobalManufacturing (2022), ServicesPro (2023). Portfolio available upon request with detailed case studies and client testimonials.",
      training_services: "Comprehensive training package",
      maintenance_contract: "2 year maintenance"
    }
  },
  {
    rfq_id: null, // Will be set after RFQ creation
    vendor_id: 2,
    total_amount: 31200.00,
    currency: "USD",
    delivery_days: "All items together",
    notes: "Thank you for considering our proposal. We specialize in premium office solutions with a focus on ergonomics and productivity. Our proposal includes extended warranties and comprehensive support services.",
    bid_data: [
      {
        item_id: "item-001",
        unit_price: 1280.00,
        total_price: 10240.00,
        delivery_days: 28,
        notes: "Handcrafted executive desks with premium finish. Each desk is individually inspected for quality assurance.",
        custom_fields: {
          installation_setup: "Full installation included",
          warranty_support: "Extended warranty +2 years",
          special_requirements: "Custom wood stain matching available"
        }
      },
      {
        item_id: "item-002",
        unit_price: 480.00,
        total_price: 12000.00,
        delivery_days: 21,
        notes: "Premium ergonomic chairs with advanced adjustment mechanisms. Includes ergonomic assessment service.",
        custom_fields: {
          installation_setup: "Full installation included",
          warranty_support: "Extended warranty +1 year",
          training_services: "Basic user training",
          special_requirements: "Ergonomic assessment for each user"
        }
      },
      {
        item_id: "item-003",
        unit_price: 1420.00,
        total_price: 21300.00,
        delivery_days: 14,
        notes: "High-end business laptops with premium support package. Includes data migration services.",
        custom_fields: {
          installation_setup: "Custom arrangement",
          warranty_support: "Premium support package",
          training_services: "Comprehensive training package",
          special_requirements: "Data migration and user profile setup"
        }
      },
      {
        item_id: "item-004",
        unit_price: 920.00,
        total_price: 2760.00,
        delivery_days: 12,
        notes: "Enterprise-grade MFPs with advanced security features. Includes managed print services option.",
        custom_fields: {
          installation_setup: "Full installation included",
          warranty_support: "Premium support package",
          maintenance_contract: "3 year maintenance",
          training_services: "Administrator training"
        }
      },
      {
        item_id: "item-005",
        unit_price: 520.00,
        total_price: 520.00,
        delivery_days: 7,
        notes: "Premium office supplies with sustainable packaging. Includes quarterly restocking service.",
        custom_fields: {
          installation_setup: "Delivery only",
          special_requirements: "Quarterly restocking service available"
        }
      }
    ],
    form_responses: {
      delivery_schedule: "All items together",
      payment_terms: "2/10 Net 30",
      volume_discount: 12,
      references_portfolio: "Established 1995, serving 500+ corporate clients. Recent projects: MegaCorp HQ (2023), Innovation Center (2022). Full portfolio and references available. ISO 9001 certified.",
      training_services: "Comprehensive training package",
      maintenance_contract: "3 year maintenance"
    }
  },
  // Submissions for Marketing Materials RFQ (RFQ 2)
  {
    rfq_id: null, // Will be set after RFQ creation
    vendor_id: 2,
    total_amount: 18750.00,
    currency: "USD",
    delivery_days: "Rush delivery (2 weeks)",
    notes: "We are excited to partner with you on your product launch campaign. Our team specializes in high-impact marketing materials with fast turnaround times. We guarantee on-time delivery for your campaign launch.",
    bid_data: [
      {
        item_id: "item-101",
        unit_price: 0.78,
        total_price: 7800.00,
        delivery_days: 12,
        notes: "Premium tri-fold brochures with aqueous coating for durability. Includes complete design service and 3 proof rounds.",
        custom_fields: {
          design_services_level: "Complete design service",
          proofing_process: "Both digital and physical",
          quality_samples: "Printed samples",
          environmental_requirements: "FSC certified paper required"
        }
      },
      {
        item_id: "item-102",
        unit_price: 0.32,
        total_price: 1600.00,
        delivery_days: 8,
        notes: "Premium business cards with soft touch coating and spot UV. Variable data printing included.",
        custom_fields: {
          design_services_level: "Layout and typesetting only",
          proofing_process: "Digital proofs only",
          quality_samples: "Paper/material samples"
        }
      },
      {
        item_id: "item-103",
        unit_price: 220.00,
        total_price: 1320.00,
        delivery_days: 10,
        notes: "High-quality retractable banners with premium vinyl and UV protection. Includes graphic design and setup instructions.",
        custom_fields: {
          design_services_level: "Complete design service",
          proofing_process: "Digital proofs only",
          quality_samples: "Complete prototype"
        }
      },
      {
        item_id: "item-104",
        unit_price: 13.50,
        total_price: 33750.00,
        delivery_days: 14,
        notes: "Custom metal USB drives with laser engraving. Includes preloaded content and branded lanyards.",
        custom_fields: {
          design_services_level: "Minor revisions only",
          proofing_process: "Physical samples required",
          quality_samples: "Complete prototype"
        }
      },
      {
        item_id: "item-105",
        unit_price: 42.00,
        total_price: 2100.00,
        delivery_days: 14,
        notes: "High-quality corporate apparel with embroidered logos. Includes size consultation and fitting service.",
        custom_fields: {
          design_services_level: "Minor revisions only",
          proofing_process: "Physical samples required",
          quality_samples: "Material samples"
        }
      }
    ],
    form_responses: {
      delivery_days: "Rush delivery (2 weeks)",
      quantity_breaks: "Brochures: 15,000+ = 10% discount, 25,000+ = 15% discount. Business cards: 10,000+ = 8% discount. USB drives: 5,000+ = 12% discount.",
      shipping_method: "Express shipping",
      storage_fulfillment: "Short-term storage (30 days)",
      environmental_requirements: "FSC certified paper required"
    }
  },
  {
    rfq_id: null, // Will be set after RFQ creation
    vendor_id: 3,
    total_amount: 17950.00,
    currency: "USD",
    delivery_days: "Standard delivery (3-4 weeks)",
    notes: "Our comprehensive marketing solutions combine creative excellence with competitive pricing. We have extensive experience in product launch campaigns and can provide additional marketing support services.",
    bid_data: [
      {
        item_id: "item-101",
        unit_price: 0.72,
        total_price: 7200.00,
        delivery_days: 18,
        notes: "High-quality brochures with eco-friendly paper options. Complete design service with unlimited revisions during design phase.",
        custom_fields: {
          design_services_level: "Complete design service",
          proofing_process: "Both digital and physical",
          quality_samples: "Printed samples",
          environmental_requirements: "Recycled materials preferred"
        }
      },
      {
        item_id: "item-102",
        unit_price: 0.28,
        total_price: 1400.00,
        delivery_days: 12,
        notes: "Premium business cards with multiple finish options. Variable data setup included at no extra charge.",
        custom_fields: {
          design_services_level: "Complete design service",
          proofing_process: "Digital proofs only",
          quality_samples: "Paper/material samples"
        }
      },
      {
        item_id: "item-103",
        unit_price: 195.00,
        total_price: 1170.00,
        delivery_days: 15,
        notes: "Professional trade show banners with premium hardware. Includes design consultation and setup training.",
        custom_fields: {
          design_services_level: "Complete design service",
          proofing_process: "Both digital and physical",
          quality_samples: "Complete prototype"
        }
      },
      {
        item_id: "item-104",
        unit_price: 12.80,
        total_price: 32000.00,
        delivery_days: 21,
        notes: "Premium USB drives with custom logo-shaped design. Includes data preloading and quality testing.",
        custom_fields: {
          design_services_level: "Complete design service",
          proofing_process: "Physical samples required",
          quality_samples: "Complete prototype"
        }
      },
      {
        item_id: "item-105",
        unit_price: 38.00,
        total_price: 1900.00,
        delivery_days: 18,
        notes: "Quality corporate apparel with professional embroidery. Includes size consultation and care instructions.",
        custom_fields: {
          design_services_level: "Layout and typesetting only",
          proofing_process: "Physical samples required",
          quality_samples: "Material samples"
        }
      }
    ],
    form_responses: {
      delivery_days: "Standard delivery (3-4 weeks)",
      quantity_breaks: "Volume discounts available: 10% for orders over $15,000, 15% for orders over $25,000. Additional discounts for repeat customers.",
      shipping_method: "Ground shipping",
      storage_fulfillment: "Long-term storage (90+ days)",
      environmental_requirements: "Recycled materials preferred"
    }
  }
];

// Test functions
async function testComprehensiveRFQCRUD() {
  console.log('🚀 Starting Comprehensive RFQ CRUD Tests with Full Form Data\n');
  console.log('=' .repeat(80));

  let createdRFQs = [];

  try {
    // Test 1: Create RFQs with complete form data
    console.log('1. Creating RFQs with Complete Form Data...\n');
    
    console.log('   Creating Office Equipment RFQ...');
    const createResponse1 = await api.post('/rfqs', completeRFQData1);
    console.log('   ✅ Office Equipment RFQ created successfully');
    console.log(`      ID: ${createResponse1.data.data.id}`);
    console.log(`      Title: ${createResponse1.data.data.title}`);
    console.log(`      Items: ${createResponse1.data.data.items.length}`);
    console.log(`      Form Fields: ${createResponse1.data.data.form_config.length}`);
    console.log(`      Vendors: ${createResponse1.data.data.invitations.length}`);
    createdRFQs.push(createResponse1.data.data);

    console.log('\n   Creating Marketing Materials RFQ...');
    const createResponse2 = await api.post('/rfqs', completeRFQData2);
    console.log('   ✅ Marketing Materials RFQ created successfully');
    console.log(`      ID: ${createResponse2.data.data.id}`);
    console.log(`      Title: ${createResponse2.data.data.title}`);
    console.log(`      Items: ${createResponse2.data.data.items.length}`);
    console.log(`      Form Fields: ${createResponse2.data.data.form_config.length}`);
    createdRFQs.push(createResponse2.data.data);

    // Test 2: Read RFQs with full details
    console.log('\n' + '=' .repeat(80));
    console.log('2. Testing RFQ Read Operations with Full Details...\n');
    
    for (let i = 0; i < createdRFQs.length; i++) {
      const rfq = createdRFQs[i];
      console.log(`   Reading RFQ ${rfq.id}...`);
      const readResponse = await api.get(`/rfqs/${rfq.id}`);
      console.log('   ✅ RFQ read successfully');
      console.log(`      Title: ${readResponse.data.data.title}`);
      console.log(`      Status: ${readResponse.data.data.status}`);
      console.log(`      Items: ${readResponse.data.data.items.length}`);
      console.log(`      Terms length: ${readResponse.data.data.terms.length} characters`);
      console.log(`      AI Settings: Price(${readResponse.data.data.ai_settings.priceWeight}), Performance(${readResponse.data.data.ai_settings.performanceWeight})`);
      console.log(`      Invitations: ${readResponse.data.data.invitations.length}`);
      console.log('');
    }

    // Test 3: Update RFQ with complex data
    console.log('=' .repeat(80));
    console.log('3. Testing RFQ Update Operations...\n');
    
    const updateData = {
      title: "[UPDATED] " + createdRFQs[0].title,
      description: "[UPDATED] " + createdRFQs[0].description + "\n\nUPDATE: Added additional requirements and specifications.",
      currency: "EUR",
      aiSettings: {
        priceWeight: 0.40,
        performanceWeight: 0.30,
        deliveryWeight: 0.20,
        riskWeight: 0.05,
        diversificationPreference: 0.05
      },
      terms: createdRFQs[0].terms + "\n\nADDENDUM: Updated terms and conditions as of " + new Date().toISOString()
    };

    console.log(`   Updating RFQ ${createdRFQs[0].id}...`);
    const updateResponse = await api.put(`/rfqs/${createdRFQs[0].id}`, updateData);
    console.log('   ✅ RFQ updated successfully');
    console.log(`      New title: ${updateResponse.data.data.title}`);
    console.log(`      New currency: ${updateResponse.data.data.currency}`);
    console.log(`      Updated AI settings: ${JSON.stringify(updateResponse.data.data.ai_settings)}`);

    // Test 4: Send RFQ invitations
    console.log('\n' + '=' .repeat(80));
    console.log('4. Testing RFQ Send Operations...\n');
    
    for (let i = 0; i < createdRFQs.length; i++) {
      const rfq = createdRFQs[i];
      console.log(`   Sending invitations for RFQ ${rfq.id}...`);
      const sendResponse = await api.post(`/rfqs/${rfq.id}/send`);
      console.log('   ✅ RFQ invitations sent successfully');
      console.log(`      Status changed to: ${sendResponse.data.data.rfq.status}`);
      console.log(`      Invitations sent: ${sendResponse.data.data.invitations.length}`);
      console.log('');
    }

    // Test 5: Enhanced listing and filtering
    console.log('=' .repeat(80));
    console.log('5. Testing Enhanced RFQ Listing and Filtering...\n');
    
    const listTests = [
      {
        name: "Basic listing with analytics",
        params: { include_analytics: true, limit: 10 }
      },
      {
        name: "Search by keyword",
        params: { search: "office", include_analytics: true }
      },
      {
        name: "Filter by status and currency",
        params: { status: "sent", currency: "USD", sort_by: "created_at", sort_order: "desc" }
      },
      {
        name: "Category-based filtering",
        params: { category: "Office Furniture", include_submissions: true }
      }
    ];

    for (const test of listTests) {
      console.log(`   Testing: ${test.name}...`);
      const listResponse = await api.get('/rfqs', { params: test.params });
      console.log(`   ✅ ${test.name} successful`);
      console.log(`      Found: ${listResponse.data.data.length} RFQs`);
      console.log(`      Pagination: Page ${listResponse.data.pagination.page} of ${listResponse.data.pagination.totalPages}`);
      console.log('');
    }

    console.log('🎉 All CRUD tests completed successfully!');
    return createdRFQs;

  } catch (error) {
    console.error('❌ CRUD test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  }
}

async function createDetailedSubmissions(rfqIds) {
  console.log('\n' + '=' .repeat(80));
  console.log('📝 Creating Detailed RFQ Submissions with Full Form Data...\n');

  try {
    // Update submission data with actual RFQ IDs
    detailedSubmissions[0].rfq_id = rfqIds[0]; // Office Equipment - Vendor 1
    detailedSubmissions[1].rfq_id = rfqIds[0]; // Office Equipment - Vendor 2
    detailedSubmissions[2].rfq_id = rfqIds[1]; // Marketing Materials - Vendor 2
    detailedSubmissions[3].rfq_id = rfqIds[1]; // Marketing Materials - Vendor 3

    for (let i = 0; i < detailedSubmissions.length; i++) {
      const submissionData = detailedSubmissions[i];
      
      console.log(`   Creating submission ${i + 1} for RFQ ${submissionData.rfq_id}...`);
      
      // Get the actual invitation token from database
      const tokenQuery = `
        SELECT token FROM rfq_invitations 
        WHERE rfq_id = $1 AND vendor_id = $2
      `;
      const tokenResult = await query(tokenQuery, [submissionData.rfq_id, submissionData.vendor_id]);
      const invitationToken = tokenResult.rows[0]?.token;
      
      if (!invitationToken) {
        console.log(`⚠️  No invitation found for RFQ ${submissionData.rfq_id}, vendor ${submissionData.vendor_id}`);
        continue;
      }

      // Insert submission into database
        const submissionQuery = `
          INSERT INTO rfq_submissions (
            rfq_id, vendor_id, invitation_token, total_amount, currency, delivery_days, 
            additional_notes, bid_data, submitted_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
          RETURNING *
        `;

      const submissionResult = await query(submissionQuery, [
          submissionData.rfq_id,
          submissionData.vendor_id,
          invitationToken,
          submissionData.total_amount,
          submissionData.currency,
          submissionData.delivery_days,
          submissionData.additional_notes,
          JSON.stringify(submissionData.bid_data)
        ]);

      console.log(`   ✅ Submission ${i + 1} created successfully`);
      console.log(`      RFQ ID: ${submissionData.rfq_id}`);
      console.log(`      Vendor ID: ${submissionData.vendor_id}`);
      console.log(`      Total Amount: $${submissionData.total_amount.toLocaleString()}`);
      console.log(`      Items: ${submissionData.bid_data.length}`);
      console.log(`      Delivery: ${submissionData.delivery_days}`);
      console.log(`      Form Responses: ${Object.keys(submissionData.form_responses).length} fields`);

      // Update invitation status to submitted
      await query(
        'UPDATE rfq_invitations SET status = $1 WHERE rfq_id = $2 AND vendor_id = $3',
        ['submitted', submissionData.rfq_id, submissionData.vendor_id]
      );
      console.log(`      Updated invitation status to 'submitted'`);
      console.log('');
    }

    console.log('✅ All detailed submissions created successfully!');

  } catch (error) {
    console.error('❌ Failed to create submissions:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
    throw error;
  }
}

async function testSubmissionViews(rfqIds) {
  console.log('\n' + '=' .repeat(80));
  console.log('👀 Testing RFQ Submission Views and Analytics...\n');

  try {
    for (let i = 0; i < rfqIds.length; i++) {
      const rfqId = rfqIds[i];
      console.log(`Testing submission views for RFQ ${rfqId}...\n`);

      // Test 1: Get submissions
      console.log(`   1. Getting submissions for RFQ ${rfqId}...`);
      const submissionsResponse = await api.get(`/rfqs/${rfqId}/submissions`);
      console.log('   ✅ Submissions retrieved successfully');
      console.log(`      Found: ${submissionsResponse.data.data.length} submissions`);
      
      submissionsResponse.data.data.forEach((submission, index) => {
        console.log(`      Submission ${index + 1}:`);
        console.log(`        Vendor ID: ${submission.vendor_id}`);
        console.log(`        Total: $${parseFloat(submission.total_amount).toLocaleString()}`);
        console.log(`        Items: ${submission.bid_data.length}`);
        console.log(`        Delivery: ${submission.delivery_days}`);
        console.log(`        Form Fields: ${Object.keys(submission.form_responses).length}`);
      });

      // Test 2: Get comparison view
      console.log(`\n   2. Getting comparison view for RFQ ${rfqId}...`);
      const comparisonResponse = await api.get(`/rfqs/${rfqId}/submissions/comparison`);
      console.log('   ✅ Comparison view retrieved successfully');
      console.log(`      Comparison includes: ${comparisonResponse.data.data.submissions?.length || 0} submissions`);

      // Test 3: Get analytics
      console.log(`\n   3. Getting analytics for RFQ ${rfqId}...`);
      const analyticsResponse = await api.get(`/rfqs/${rfqId}/analytics`);
      console.log('   ✅ Analytics retrieved successfully');
      console.log(`      Response Rate: ${analyticsResponse.data.data.response_rate}%`);
      console.log(`      Total Invitations: ${analyticsResponse.data.data.total_invitations}`);
      console.log(`      Total Submissions: ${analyticsResponse.data.data.total_submissions}`);
      if (analyticsResponse.data.data.average_bid_amount) {
        console.log(`      Average Bid: $${parseFloat(analyticsResponse.data.data.average_bid_amount).toLocaleString()}`);
        console.log(`      Lowest Bid: $${parseFloat(analyticsResponse.data.data.lowest_bid_amount).toLocaleString()}`);
        console.log(`      Highest Bid: $${parseFloat(analyticsResponse.data.data.highest_bid_amount).toLocaleString()}`);
      }

      // Test 4: Generate AI recommendations
      console.log(`\n   4. Generating AI recommendations for RFQ ${rfqId}...`);
      const aiResponse = await api.post(`/rfqs/${rfqId}/ai-recommend`);
      console.log('   ✅ AI recommendations generated successfully');
      console.log(`      Generated: ${aiResponse.data.data.length} recommendations`);
      
      aiResponse.data.data.forEach((recommendation, index) => {
        console.log(`      Recommendation ${index + 1}:`);
        console.log(`        Vendor ID: ${recommendation.vendor_id}`);
        console.log(`        Score: ${recommendation.overall_score}`);
        console.log(`        Reason: ${recommendation.recommendation_reason}`);
      });

      // Test 5: Get audit history
      console.log(`\n   5. Getting audit history for RFQ ${rfqId}...`);
      const auditResponse = await api.get(`/rfqs/${rfqId}/audit`);
      console.log('   ✅ Audit history retrieved successfully');
      console.log(`      Audit entries: ${auditResponse.data.data.length}`);

      console.log('\n' + '-'.repeat(60) + '\n');
    }

    console.log('🎉 All submission view tests completed successfully!');

  } catch (error) {
    console.error('❌ Submission view test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function testEnhancedFeatures() {
  console.log('\n' + '=' .repeat(80));
  console.log('🔧 Testing Enhanced API Features...\n');

  try {
    // Test 1: Advanced search
    console.log('1. Testing Advanced Search...');
    const searchResponse = await api.post('/rfqs/search', {
      query: 'office equipment furniture',
      filters: {
        status: ['sent'],
        currencies: ['USD', 'EUR'],
        date_range: {
          start: '2024-01-01',
          end: '2024-12-31'
        }
      },
      page: 1,
      limit: 10,
      sort_by: 'relevance'
    });
    console.log('✅ Advanced search successful');
    console.log(`   Results: ${searchResponse.data.data.length} RFQs`);
    console.log(`   Search time: ${searchResponse.data.search_meta.search_time_ms}ms`);

    // Test 2: Summary statistics
    console.log('\n2. Testing Summary Statistics...');
    const statsResponse = await api.get('/rfqs/summary');
    console.log('✅ Summary statistics retrieved');
    console.log(`   Total RFQs: ${statsResponse.data.data.total_rfqs}`);
    console.log(`   Draft: ${statsResponse.data.data.draft_count}`);
    console.log(`   Sent: ${statsResponse.data.data.sent_count}`);
    console.log(`   Average Response Rate: ${statsResponse.data.data.avg_response_rate}%`);

    // Test 3: Cursor-based pagination
    console.log('\n3. Testing Cursor-based Pagination...');
    const cursorResponse = await api.get('/rfqs', {
      params: {
        cursor: Buffer.from(new Date().toISOString()).toString('base64'),
        limit: 5
      }
    });
    console.log('✅ Cursor-based pagination works');
    console.log(`   Results: ${cursorResponse.data.data.length} RFQs`);
    console.log(`   Has Next: ${cursorResponse.data.meta.has_next}`);

    console.log('\n🎉 Enhanced features testing completed!');

  } catch (error) {
    console.error('❌ Enhanced features test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function runCompleteTestSuite() {
  console.log('🧪 COMPREHENSIVE RFQ CRUD TEST SUITE WITH FULL FORM DATA');
  console.log('=' .repeat(80));
  console.log('This test suite will:');
  console.log('• Create 2 RFQs with complete form configurations');
  console.log('• Test all CRUD operations with full data validation');
  console.log('• Create 4 detailed submissions (2 per RFQ)');
  console.log('• Test all submission views and analytics');
  console.log('• Verify enhanced API features');
  console.log('=' .repeat(80));

  try {
    // Step 1: Run comprehensive CRUD tests
    const createdRFQs = await testComprehensiveRFQCRUD();
    const rfqIds = createdRFQs.map(rfq => rfq.id);

    // Step 2: Create detailed submissions
    await createDetailedSubmissions(rfqIds);

    // Step 3: Test submission views
    await testSubmissionViews(rfqIds);

    // Step 4: Test enhanced features
    await testEnhancedFeatures();

    // Final summary
    console.log('\n' + '=' .repeat(80));
    console.log('🎊 COMPREHENSIVE TEST SUITE COMPLETED SUCCESSFULLY! 🎊');
    console.log('=' .repeat(80));
    console.log('\n📊 SAMPLE DATA CREATED:');
    console.log(`• 2 RFQs with full form configurations (IDs: ${rfqIds.join(', ')})`);
    console.log('• 4 detailed submissions with complete form responses');
    console.log('• All CRUD operations tested and verified');
    console.log('• Enhanced API features validated');
    console.log('• Analytics and AI recommendations generated');
    console.log('\n🎯 READY FOR UI TESTING:');
    console.log('• RFQ listing with filtering and search');
    console.log('• RFQ detail views with full form data');
    console.log('• Submission comparison and analytics');
    console.log('• AI recommendations and insights');
    console.log('• Complete audit trails');
    console.log('\n✨ Your VendorMS RFQ system is ready for comprehensive testing!');

  } catch (error) {
    console.error('\n💥 COMPREHENSIVE TEST SUITE FAILED:', error.message);
    console.error('Please check the error details above and ensure:');
    console.error('• Database is running and accessible');
    console.error('• Backend server is running on port 3001');
    console.error('• All required tables exist');
    console.error('• Vendor records exist with IDs 1-5');
    process.exit(1);
  }
}

// Export functions for modular use
module.exports = {
  testComprehensiveRFQCRUD,
  createDetailedSubmissions,
  testSubmissionViews,
  testEnhancedFeatures,
  runCompleteTestSuite,
  completeRFQData1,
  completeRFQData2,
  detailedSubmissions
};

// Run the complete test suite if executed directly
if (require.main === module) {
  runCompleteTestSuite().catch(console.error);
}