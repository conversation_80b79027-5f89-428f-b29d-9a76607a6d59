const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const { query } = require('./config/database');

// Test configuration
const BASE_URL = 'http://localhost:3001';
const API_BASE = `${BASE_URL}/api`;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  header: (msg) => console.log(`${colors.cyan}\n=== ${msg} ===${colors.reset}`),
  step: (msg) => console.log(`${colors.magenta}🔄 ${msg}${colors.reset}`)
};

// Test data
let testTokens = [];
let testRFQData = null;

/**
 * Get available test tokens from database
 */
async function getTestTokens() {
  try {
    log.step('Fetching available test tokens from database...');
    
    const result = await query(`
      SELECT 
        ri.token,
        ri.rfq_id,
        ri.vendor_id,
        ri.status,
        ri.viewed_at,
        ri.expires_at,
        r.title as rfq_title,
        v.name as vendor_name,
        -- Check if submission exists
        CASE WHEN rs.id IS NOT NULL THEN true ELSE false END as has_submission
      FROM rfq_invitations ri
      LEFT JOIN rfqs r ON ri.rfq_id = r.id
      LEFT JOIN vendors v ON ri.vendor_id = v.id
      LEFT JOIN rfq_submissions rs ON ri.rfq_id = rs.rfq_id AND ri.vendor_id = rs.vendor_id
      WHERE ri.status IN ('sent', 'viewed') 
        AND ri.expires_at > CURRENT_TIMESTAMP
        AND r.status = 'sent'
      ORDER BY ri.created_at DESC
      LIMIT 5
    `);
    
    testTokens = result.rows;
    log.success(`Found ${testTokens.length} available test tokens`);
    
    testTokens.forEach((token, index) => {
      log.info(`  ${index + 1}. Token: ${token.token.substring(0, 8)}... | RFQ: "${token.rfq_title}" | Vendor: ${token.vendor_name} | Status: ${token.status} | Has Submission: ${token.has_submission}`);
    });
    
    return testTokens;
  } catch (error) {
    log.error(`Failed to fetch test tokens: ${error.message}`);
    throw error;
  }
}

/**
 * Test 1: Token validation and viewed status tracking
 */
async function testTokenValidation() {
  log.header('Test 1: Token Validation and Viewed Status Tracking');
  
  if (testTokens.length === 0) {
    log.warning('No test tokens available, skipping token validation tests');
    return;
  }
  
  const token = testTokens[0];
  
  try {
    // Test 1a: Valid token validation
    log.step('Testing valid token validation...');
    const response = await axios.get(`${API_BASE}/public/rfq/${token.token}/validate`);
    
    if (response.data.success) {
      log.success('Token validation successful');
      log.info(`  Message: ${response.data.message}`);
    } else {
      log.error('Token validation failed unexpectedly');
    }
    
    // Test 1b: Get RFQ details and check viewed status update
    log.step('Testing RFQ details retrieval and viewed status tracking...');
    
    // Get current viewed_at status
    const beforeResult = await query(
      'SELECT viewed_at, status FROM rfq_invitations WHERE token = $1',
      [token.token]
    );
    const beforeStatus = beforeResult.rows[0];
    
    // Make request to get RFQ details
    const rfqResponse = await axios.get(`${API_BASE}/public/rfq/${token.token}`);
    
    if (rfqResponse.data.success) {
      testRFQData = rfqResponse.data.data;
      log.success('RFQ details retrieved successfully');
      log.info(`  RFQ Title: ${testRFQData.title}`);
      log.info(`  Items Count: ${testRFQData.items.length}`);
      log.info(`  Vendor: ${testRFQData.vendor_name}`);
      
      // Check if viewed_at was updated
      const afterResult = await query(
        'SELECT viewed_at, status FROM rfq_invitations WHERE token = $1',
        [token.token]
      );
      const afterStatus = afterResult.rows[0];
      
      if (!beforeStatus.viewed_at && afterStatus.viewed_at) {
        log.success('✨ Viewed status tracking working! viewed_at timestamp was set');
        log.info(`  Status changed from '${beforeStatus.status}' to '${afterStatus.status}'`);
      } else if (beforeStatus.viewed_at) {
        log.info('Viewed status already set (expected for subsequent requests)');
      } else {
        log.warning('Viewed status was not updated');
      }
    } else {
      log.error('Failed to retrieve RFQ details');
    }
    
    // Test 1c: Invalid token
    log.step('Testing invalid token...');
    try {
      await axios.get(`${API_BASE}/public/rfq/invalid-token-12345/validate`);
      log.error('Invalid token should have failed');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        log.success('Invalid token correctly rejected');
      } else {
        log.error(`Unexpected error: ${error.message}`);
      }
    }
    
  } catch (error) {
    log.error(`Token validation test failed: ${error.message}`);
    if (error.response) {
      log.error(`  Status: ${error.response.status}`);
      log.error(`  Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

/**
 * Test 2: Submission creation scenarios
 */
async function testSubmissionCreation() {
  log.header('Test 2: Submission Creation Scenarios');
  
  if (!testRFQData) {
    log.warning('No RFQ data available, skipping submission tests');
    return;
  }
  
  // Find a token without existing submission
  const availableToken = testTokens.find(t => !t.has_submission);
  if (!availableToken) {
    log.warning('No tokens without existing submissions, skipping creation test');
    return;
  }
  
  try {
    // Test 2a: Valid submission creation
    log.step('Testing valid submission creation...');
    
    const submissionData = {
      rfq_id: availableToken.rfq_id,
      invitation_token: availableToken.token,
      bidItems: testRFQData.items.map((item, index) => ({
        rfq_item_id: item.id,
        item_name: item.name,
        unit_price: 10.50 + (index * 2.25),
        quantity: item.quantity || 1,
        delivery_days: 7 + index,
        specifications: item.specifications || {},
        alternatives: [],
        notes: `Test bid for ${item.name}`
      })),
      bidData: {
        generalTerms: 'Standard commercial terms',
        deliverySchedule: 'As per individual item delivery days',
        paymentTerms: 'Net 30 days',
        validityPeriod: 30,
        additionalNotes: 'Test submission created by automated test'
      },
      currency: 'USD',
      delivery_days: 14,
      payment_terms: 'Net 30 days',
      validity_period: 30,
      additional_notes: 'This is a test submission created by the automated test suite'
    };
    
    const response = await axios.post(
      `${API_BASE}/public/rfq/submission`,
      submissionData,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (response.data.success) {
      log.success('Submission created successfully');
      log.info(`  Submission ID: ${response.data.data.id}`);
      log.info(`  Total Amount: $${response.data.data.total_amount}`);
      log.info(`  Items Count: ${response.data.data.bid_items.length}`);
      
      // Verify invitation status was updated
      const statusCheck = await query(
        'SELECT status, submitted_at FROM rfq_invitations WHERE token = $1',
        [availableToken.token]
      );
      
      if (statusCheck.rows[0].status === 'submitted') {
        log.success('Invitation status correctly updated to "submitted"');
      } else {
        log.warning(`Invitation status is "${statusCheck.rows[0].status}", expected "submitted"`);
      }
    } else {
      log.error('Submission creation failed');
    }
    
  } catch (error) {
    log.error(`Submission creation test failed: ${error.message}`);
    if (error.response) {
      log.error(`  Status: ${error.response.status}`);
      log.error(`  Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

/**
 * Test 3: Duplicate submission prevention
 */
async function testDuplicateSubmissionPrevention() {
  log.header('Test 3: Duplicate Submission Prevention');
  
  // Find a token with existing submission
  const tokenWithSubmission = testTokens.find(t => t.has_submission);
  if (!tokenWithSubmission) {
    log.warning('No tokens with existing submissions, skipping duplicate test');
    return;
  }
  
  try {
    log.step('Testing duplicate submission prevention...');
    
    const submissionData = {
      rfq_id: tokenWithSubmission.rfq_id,
      invitation_token: tokenWithSubmission.token,
      bidItems: [{
        rfq_item_id: 'test-item',
        item_name: 'Test Item',
        unit_price: 10.00,
        quantity: 1,
        delivery_days: 7,
        specifications: {},
        alternatives: [],
        notes: 'Test duplicate submission'
      }],
      bidData: {},
      currency: 'USD',
      delivery_days: 14,
      payment_terms: 'Net 30 days',
      validity_period: 30,
      additional_notes: 'This should be rejected as duplicate'
    };
    
    try {
        await axios.post(
          `${API_BASE}/public/rfq/submission`,
          submissionData,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
      log.error('Duplicate submission should have been rejected');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        log.success('Duplicate submission correctly rejected');
        log.info(`  Error message: ${error.response.data.message}`);
      } else {
        log.error(`Unexpected error: ${error.message}`);
      }
    }
    
  } catch (error) {
    log.error(`Duplicate submission test failed: ${error.message}`);
  }
}

/**
 * Test 4: Submission status tracking
 */
async function testSubmissionStatusTracking() {
  log.header('Test 4: Submission Status Tracking');
  
  if (testTokens.length === 0) {
    log.warning('No test tokens available, skipping status tracking tests');
    return;
  }
  
  const token = testTokens[0];
  
  try {
    log.step('Testing submission status retrieval...');
    
    const response = await axios.get(`${API_BASE}/public/rfq/${token.token}/status`);
    
    if (response.data.success) {
      const status = response.data.data;
      log.success('Submission status retrieved successfully');
      log.info(`  Token Valid: ${status.token_valid}`);
      log.info(`  RFQ Title: ${status.rfq_title}`);
      log.info(`  Vendor: ${status.vendor_name}`);
      log.info(`  Invitation Status: ${status.invitation_status}`);
      log.info(`  Has Submission: ${status.has_submission}`);
      log.info(`  Can Submit: ${status.can_submit}`);
      log.info(`  Can Modify: ${status.can_modify}`);
    } else {
      log.error('Failed to retrieve submission status');
    }
    
  } catch (error) {
    log.error(`Submission status test failed: ${error.message}`);
    if (error.response) {
      log.error(`  Status: ${error.response.status}`);
      log.error(`  Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

/**
 * Test 5: File upload functionality
 */
async function testFileUpload() {
  log.header('Test 5: File Upload Functionality');
  
  // Find a token without existing submission for file upload test
  const availableToken = testTokens.find(t => !t.has_submission);
  if (!availableToken) {
    log.warning('No tokens without existing submissions, skipping file upload test');
    return;
  }
  
  try {
    log.step('Creating test file for upload...');
    
    // Create a test file
    const testFilePath = path.join(__dirname, 'test-attachment.txt');
    const testFileContent = 'This is a test attachment file for RFQ submission testing.\nCreated at: ' + new Date().toISOString();
    fs.writeFileSync(testFilePath, testFileContent);
    
    log.step('Testing submission with file attachment...');
    
    const formData = new FormData();
    
    // Add form fields
    formData.append('rfq_id', availableToken.rfq_id.toString());
    formData.append('invitation_token', availableToken.token);
    formData.append('bidItems', JSON.stringify([{
      rfq_item_id: 'test-item-upload',
      item_name: 'Test Item with Upload',
      unit_price: 25.00,
      quantity: 1,
      delivery_days: 10,
      specifications: {},
      alternatives: [],
      notes: 'Test item with file attachment'
    }]));
    formData.append('bidData', JSON.stringify({
      generalTerms: 'Standard terms with attachment',
      additionalNotes: 'Submission includes test attachment'
    }));
    formData.append('currency', 'USD');
    formData.append('delivery_days', '14');
    formData.append('payment_terms', 'Net 30 days');
    formData.append('validity_period', '30');
    formData.append('additional_notes', 'Test submission with file attachment');
    
    // Add file
    formData.append('attachments', fs.createReadStream(testFilePath));
    
    const response = await axios.post(
      `${API_BASE}/public/rfq/submission`,
      formData,
      {
        headers: {
          ...formData.getHeaders()
        },
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      }
    );
    
    if (response.data.success) {
      log.success('Submission with file attachment created successfully');
      log.info(`  Submission ID: ${response.data.data.id}`);
      log.info(`  Attachments: ${response.data.data.attachments.length}`);
      
      if (response.data.data.attachments.length > 0) {
        const attachment = response.data.data.attachments[0];
        log.info(`  File: ${attachment.originalName} (${attachment.fileSize} bytes)`);
      }
    } else {
      log.error('Submission with file attachment failed');
    }
    
    // Clean up test file
    fs.unlinkSync(testFilePath);
    log.info('Test file cleaned up');
    
  } catch (error) {
    log.error(`File upload test failed: ${error.message}`);
    if (error.response) {
      log.error(`  Status: ${error.response.status}`);
      log.error(`  Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    
    // Clean up test file if it exists
    const testFilePath = path.join(__dirname, 'test-attachment.txt');
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }
  }
}

/**
 * Test 6: Edge cases and error handling
 */
async function testEdgeCases() {
  log.header('Test 6: Edge Cases and Error Handling');
  
  try {
    // Test 6a: Missing required fields
    log.step('Testing submission with missing required fields...');
    
    if (testTokens.length > 0) {
      const token = testTokens[0];
      
      try {
        await axios.post(
          `${API_BASE}/public/rfq/submission`,
          {
            // Missing required fields
            rfq_id: token.rfq_id,
            invitation_token: token.token
            // Missing bidItems, bidData, etc.
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
        log.error('Submission with missing fields should have been rejected');
      } catch (error) {
        if (error.response && error.response.status === 400) {
          log.success('Submission with missing fields correctly rejected');
          log.info(`  Error: ${error.response.data.message}`);
        } else {
          log.error(`Unexpected error: ${error.message}`);
        }
      }
    }
    
    // Test 6b: Invalid data types
    log.step('Testing submission with invalid data types...');
    
    if (testTokens.length > 0) {
      const token = testTokens[0];
      
      try {
        await axios.post(
          `${API_BASE}/public/rfq/submission`,
          {
            rfq_id: 'invalid-id', // Should be number
            invitation_token: token.token,
            bidItems: 'invalid-array', // Should be array
            bidData: 'invalid-object', // Should be object
            currency: 'USD',
            delivery_days: 'invalid-number', // Should be number
            payment_terms: 'Net 30 days',
            validity_period: 30,
            additional_notes: 'Test with invalid data types'
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
        log.error('Submission with invalid data types should have been rejected');
      } catch (error) {
        if (error.response && error.response.status === 400) {
          log.success('Submission with invalid data types correctly rejected');
        } else {
          log.error(`Unexpected error: ${error.message}`);
        }
      }
    }
    
    // Test 6c: Expired token (if any)
    log.step('Testing expired token handling...');
    
    const expiredTokenResult = await query(`
      SELECT token FROM rfq_invitations 
      WHERE expires_at < CURRENT_TIMESTAMP 
      LIMIT 1
    `);
    
    if (expiredTokenResult.rows.length > 0) {
      const expiredToken = expiredTokenResult.rows[0].token;
      
      try {
        await axios.get(`${API_BASE}/public/rfq/${expiredToken}/validate`);
        log.error('Expired token should have been rejected');
      } catch (error) {
        if (error.response && error.response.status === 400) {
          log.success('Expired token correctly rejected');
        } else {
          log.error(`Unexpected error: ${error.message}`);
        }
      }
    } else {
      log.info('No expired tokens found to test');
    }
    
  } catch (error) {
    log.error(`Edge cases test failed: ${error.message}`);
  }
}

/**
 * Test 7: Database consistency checks
 */
async function testDatabaseConsistency() {
  log.header('Test 7: Database Consistency Checks');
  
  try {
    log.step('Checking invitation status consistency...');
    
    const statusConsistency = await query(`
      SELECT 
        ri.status,
        COUNT(*) as count,
        COUNT(CASE WHEN ri.viewed_at IS NOT NULL THEN 1 END) as viewed_count,
        COUNT(CASE WHEN rs.id IS NOT NULL THEN 1 END) as submission_count
      FROM rfq_invitations ri
      LEFT JOIN rfq_submissions rs ON ri.rfq_id = rs.rfq_id AND ri.vendor_id = rs.vendor_id
      GROUP BY ri.status
      ORDER BY ri.status
    `);
    
    log.success('Invitation status consistency check:');
    statusConsistency.rows.forEach(row => {
      log.info(`  Status "${row.status}": ${row.count} invitations, ${row.viewed_count} viewed, ${row.submission_count} with submissions`);
    });
    
    log.step('Checking for orphaned records...');
    
    const orphanedSubmissions = await query(`
      SELECT COUNT(*) as count
      FROM rfq_submissions rs
      LEFT JOIN rfq_invitations ri ON rs.invitation_token = ri.token
      WHERE ri.token IS NULL
    `);
    
    if (orphanedSubmissions.rows[0].count > 0) {
      log.warning(`Found ${orphanedSubmissions.rows[0].count} orphaned submissions`);
    } else {
      log.success('No orphaned submissions found');
    }
    
    log.step('Checking token uniqueness...');
    
    const duplicateTokens = await query(`
      SELECT token, COUNT(*) as count
      FROM rfq_invitations
      GROUP BY token
      HAVING COUNT(*) > 1
    `);
    
    if (duplicateTokens.rows.length > 0) {
      log.error(`Found ${duplicateTokens.rows.length} duplicate tokens`);
    } else {
      log.success('All tokens are unique');
    }
    
  } catch (error) {
    log.error(`Database consistency test failed: ${error.message}`);
  }
}

/**
 * Main test runner
 */
async function runAllTests() {
  log.header('Public RFQ Submission Complete Test Suite');
  log.info('Testing all scenarios for public RFQ submission functionality');
  log.info(`Base URL: ${BASE_URL}`);
  
  try {
    // Setup
    await getTestTokens();
    
    // Run all tests
    await testTokenValidation();
    await testSubmissionCreation();
    await testDuplicateSubmissionPrevention();
    await testSubmissionStatusTracking();
    await testFileUpload();
    await testEdgeCases();
    await testDatabaseConsistency();
    
    log.header('Test Suite Completed');
    log.success('All tests have been executed. Check the output above for results.');
    
  } catch (error) {
    log.error(`Test suite failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log.warning('Test suite interrupted by user');
  process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  testTokenValidation,
  testSubmissionCreation,
  testDuplicateSubmissionPrevention,
  testSubmissionStatusTracking,
  testFileUpload,
  testEdgeCases,
  testDatabaseConsistency
};