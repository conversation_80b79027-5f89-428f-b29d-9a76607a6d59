# Technology Stack

## Frontend Stack
- **React 18** - Modern UI library with concurrent features
- **TypeScript** - Type-safe development with relaxed configuration
- **Vite** - Lightning-fast build tool and dev server
- **Tailwind CSS** - Utility-first styling with custom neumorphism design system
- **Shadcn/ui + Radix UI** - Accessible component library
- **Framer Motion** - Smooth animations and micro-interactions

## State Management
- **Redux Toolkit** - Global state management
- **React Hook Form + Yup** - Form handling and validation
- **TanStack Query** - Server state management and caching

## Backend Stack
- **Node.js + Express** - REST API server
- **PostgreSQL** - Primary database
- **Socket.io** - Real-time communication
- **JWT** - Authentication tokens
- **Multer** - File upload handling

## Key Libraries
- **Lucide React** - Icon library
- **Recharts** - Data visualization
- **i18next** - Internationalization
- **Axios** - HTTP client
- **date-fns** - Date manipulation
- **currency.js** - Currency handling

## Development Tools
- **ESLint** - Code linting with relaxed TypeScript rules
- **Vite PWA** - Progressive Web App features
- **Lovable Tagger** - Development component tagging

## Build & Development Commands

### Frontend
```bash
# Development server (runs on port 8080)
npm run dev

# Production build
npm run build

# Development build
npm run build:dev

# Preview production build
npm run preview

# Lint code
npm run lint
```

### Backend
```bash
# Development server with nodemon (runs on port 3001)
npm run dev

# Production server
npm start

# Run tests
npm test

# Test RFQ functionality
npm run test:rfq
npm run test:rfq-enhanced

# Setup test data
npm run setup:test-data
```

## Configuration Notes
- Frontend proxies `/api` requests to backend on port 3001
- TypeScript configured with relaxed rules (no implicit any, unused parameters allowed)
- Tailwind uses custom CSS variables for theming
- Path alias `@/*` maps to `./src/*`
- Socket.io enables real-time features across the application