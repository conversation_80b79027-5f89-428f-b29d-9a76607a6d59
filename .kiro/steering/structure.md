# Project Structure

## Root Directory Layout
```
├── src/                    # Frontend React application
├── backend/               # Node.js/Express API server
├── database/              # Database schemas and migrations
├── docs/                  # Project documentation
├── Plan/                  # Project planning documents
├── public/                # Static frontend assets
└── jarvis/                # AI assistant specifications
```

## Frontend Structure (`src/`)
```
src/
├── components/            # Reusable UI components
│   ├── ui/               # Shadcn/ui base components
│   ├── Layout/           # Layout components (Sidebar, Header)
│   ├── accounts/         # Account management components
│   ├── contacts/         # Contact management components
│   ├── rfq/              # RFQ system components
│   ├── admin/            # Admin-specific components
│   ├── auth/             # Authentication components
│   └── workflows/        # Workflow builder components
├── pages/                # Route-level page components
│   ├── admin/            # Admin pages
│   └── opportunities/    # Opportunity management pages
├── hooks/                # Custom React hooks
├── services/             # API service layer and external integrations
│   ├── api/              # API client functions
│   ├── ai/               # AI service adapters
│   ├── encryption/       # Security services
│   └── storage/          # Storage utilities
├── store/                # Redux store configuration
│   └── slices/           # Feature-specific Redux slices
├── types/                # TypeScript type definitions
├── utils/                # Utility functions
└── lib/                  # Shared libraries and configurations
```

## Backend Structure (`backend/`)
```
backend/
├── controllers/          # Route handlers and business logic
├── models/               # Database models and schemas
├── routes/               # Express route definitions
├── services/             # Business logic services
├── middleware/           # Express middleware
├── validation/           # Input validation schemas
├── config/               # Configuration files
├── scripts/              # Database and utility scripts
├── uploads/              # File upload storage
└── data/                 # Default data and templates
```

## Database Structure (`database/`)
```
database/
├── schema.sql            # Complete database schema
├── migrations/           # Database migration files
└── README.md             # Database setup instructions
```

## Component Organization Patterns

### Feature-Based Components
Components are organized by feature domain (accounts, contacts, rfq, etc.) with each containing:
- Main component files
- Form components
- List/display components
- Modal/dialog components
- Test files in `__tests__/` subdirectories

### UI Components
Base UI components in `src/components/ui/` follow Shadcn/ui patterns:
- Single-purpose, reusable components
- Consistent API patterns
- Accessibility built-in
- Theme-aware styling

## File Naming Conventions
- **Components**: PascalCase (e.g., `AccountForm.tsx`)
- **Pages**: PascalCase (e.g., `AccountsList.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useAuth.ts`)
- **Services**: camelCase (e.g., `accountService.ts`)
- **Types**: camelCase (e.g., `account.ts`)
- **Utilities**: camelCase (e.g., `validation.ts`)

## Import Path Conventions
- Use `@/` alias for src imports: `import { Button } from '@/components/ui/button'`
- Relative imports for same-directory files
- Barrel exports via `index.ts` files for cleaner imports

## Backend Conventions
- **Controllers**: Handle HTTP requests/responses, delegate to services
- **Services**: Contain business logic, interact with models
- **Models**: Database interaction and data validation
- **Routes**: Define API endpoints and middleware
- **Validation**: Input validation using Joi schemas