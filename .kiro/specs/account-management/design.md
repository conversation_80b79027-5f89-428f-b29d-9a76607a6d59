# Design Document

## Overview

The Account Management module introduces client-side account management capabilities to the VMS, enabling comprehensive management of client organizations. The design follows a Salesforce-aligned approach to facilitate seamless integration while maintaining VMS-specific requirements for procurement workflows.

The module implements a hierarchical account structure with comprehensive business information, advanced search capabilities, and optional Salesforce synchronization. It serves as the foundation for future CRM-like features including Contacts, Opportunities, and client-facing Quotes.

## Architecture

### System Architecture

The Account Management module follows the existing VMS architecture pattern:

```
Frontend (React/TypeScript)
├── Account Components
├── Forms & Validation
├── Search & Filtering
└── Integration UI

Backend (Node.js/Express)
├── Account Controllers
├── Salesforce Service
├── Validation Middleware
└── Audit Logging

Database (PostgreSQL)
├── Account Table
├── Audit Tables
├── Integration Logs
└── JSONB Indexes
```

### Integration Points

- **Existing VMS Modules**: Links to User management for ownership, Audit system for logging
- **Future Modules**: Foundation for Contacts, Opportunities, RFQs, and Quotes
- **External Systems**: Salesforce REST API for bidirectional synchronization
- **Authentication**: Leverages existing JWT-based auth with RBAC

## Components and Interfaces

### Database Schema

```sql
-- Account table with Salesforce-aligned structure
CREATE TABLE accounts (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    account_number VARCHAR(100),
    type VARCHAR(50), -- Prospect, Customer - Direct, etc.
    industry VARCHAR(100), -- Banking, IT, Manufacturing, etc.
    annual_revenue DECIMAL(15,2),
    number_of_employees INTEGER,
    ownership VARCHAR(50), -- Public, Private, Subsidiary, Other
    phone VARCHAR(50),
    fax VARCHAR(50),
    website VARCHAR(255),
    ticker_symbol VARCHAR(10),
    site VARCHAR(255),
    rating VARCHAR(20), -- Hot, Warm, Cold
    description TEXT,
    billing_address JSONB, -- {street, city, state, postalCode, country, latitude, longitude}
    shipping_address JSONB,
    parent_account_id INTEGER REFERENCES accounts(id),
    owner_id INTEGER REFERENCES users(id),
    integration_id VARCHAR(100), -- Salesforce Record ID
    custom_fields JSONB,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by_id INTEGER REFERENCES users(id),
    last_modified_by_id INTEGER REFERENCES users(id),
    is_deleted BOOLEAN DEFAULT FALSE,
    
    CONSTRAINT unique_name_per_tenant UNIQUE (name, tenant_id),
    CONSTRAINT no_self_parent CHECK (id != parent_account_id)
);

-- Indexes for performance
CREATE INDEX idx_accounts_name ON accounts USING btree(name);
CREATE INDEX idx_accounts_industry ON accounts USING btree(industry);
CREATE INDEX idx_accounts_annual_revenue ON accounts USING btree(annual_revenue);
CREATE INDEX idx_accounts_parent ON accounts USING btree(parent_account_id);
CREATE INDEX idx_accounts_billing_address ON accounts USING gin(billing_address);
CREATE INDEX idx_accounts_shipping_address ON accounts USING gin(shipping_address);
CREATE INDEX idx_accounts_integration_id ON accounts USING btree(integration_id);
```

### API Endpoints

```typescript
// Account CRUD Operations
POST   /api/accounts                    // Create account
GET    /api/accounts/:id               // Get account by ID
PUT    /api/accounts/:id               // Update account
DELETE /api/accounts/:id               // Soft delete account
GET    /api/accounts                   // List accounts with pagination/filtering

// Search and Filtering
GET    /api/accounts/search            // Advanced search with filters
GET    /api/accounts/export            // Export accounts to CSV/PDF

// Hierarchy Operations
GET    /api/accounts/:id/hierarchy     // Get account hierarchy tree
GET    /api/accounts/:id/children      // Get direct children
GET    /api/accounts/:id/ancestors     // Get parent chain

// Integration Operations
POST   /api/integrations/salesforce/sync-account/:id  // Manual sync to Salesforce
GET    /api/integrations/salesforce/status            // Integration status
POST   /api/integrations/salesforce/pull              // Pull updates from Salesforce
```

### Frontend Components

```typescript
// Core Components
AccountForm              // Create/Edit account form with validation
AccountList              // Paginated list with search/filter
AccountCard              // Individual account display card
AccountHierarchy         // Tree view for parent-child relationships
AddressForm              // Composite address input component
SearchFilters            // Advanced filtering interface

// Integration Components
SalesforceSync           // Sync status and manual sync controls
IntegrationStatus        // Display sync health and logs
ConflictResolution       // Handle sync conflicts

// Utility Components
AccountSelector          // Dropdown for selecting accounts
AccountBreadcrumb        // Navigation for hierarchy
ExportDialog             // Export options and progress
```

### Service Layer

```typescript
// Account Service
class AccountService {
    async createAccount(accountData: AccountCreateRequest): Promise<Account>
    async updateAccount(id: number, updates: AccountUpdateRequest): Promise<Account>
    async getAccount(id: number): Promise<Account>
    async searchAccounts(filters: AccountSearchFilters): Promise<PaginatedAccounts>
    async getAccountHierarchy(id: number): Promise<AccountHierarchy>
    async exportAccounts(format: 'csv' | 'pdf', filters?: AccountSearchFilters): Promise<string>
}

// Salesforce Integration Service
class SalesforceService {
    async syncAccountToSalesforce(account: Account): Promise<SyncResult>
    async pullAccountFromSalesforce(salesforceId: string): Promise<Account>
    async batchSyncAccounts(accountIds: number[]): Promise<BatchSyncResult>
    async validateConnection(): Promise<boolean>
    async handleConflict(conflict: SyncConflict): Promise<ConflictResolution>
}
```

## Data Models

### Account Model

```typescript
interface Account {
    id: number;
    name: string;
    accountNumber?: string;
    type?: AccountType;
    industry?: Industry;
    annualRevenue?: number;
    numberOfEmployees?: number;
    ownership?: Ownership;
    phone?: string;
    fax?: string;
    website?: string;
    tickerSymbol?: string;
    site?: string;
    rating?: Rating;
    description?: string;
    billingAddress?: Address;
    shippingAddress?: Address;
    parentAccountId?: number;
    ownerId?: number;
    integrationId?: string;
    customFields?: Record<string, any>;
    status: AccountStatus;
    createdAt: Date;
    updatedAt: Date;
    createdById?: number;
    lastModifiedById?: number;
    isDeleted: boolean;
    
    // Relationships
    parent?: Account;
    children?: Account[];
    owner?: User;
    createdBy?: User;
    lastModifiedBy?: User;
}

interface Address {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
    latitude?: number;
    longitude?: number;
}

// Enums matching Salesforce picklists
enum AccountType {
    PROSPECT = 'Prospect',
    CUSTOMER_DIRECT = 'Customer - Direct',
    CUSTOMER_CHANNEL = 'Customer - Channel',
    CHANNEL_PARTNER = 'Channel Partner / Reseller',
    INSTALLATION_PARTNER = 'Installation Partner',
    TECHNOLOGY_PARTNER = 'Technology Partner',
    OTHER = 'Other'
}

enum Industry {
    AGRICULTURE = 'Agriculture',
    APPAREL = 'Apparel',
    BANKING = 'Banking',
    BIOTECHNOLOGY = 'Biotechnology',
    CHEMICALS = 'Chemicals',
    COMMUNICATIONS = 'Communications',
    CONSTRUCTION = 'Construction',
    CONSULTING = 'Consulting',
    EDUCATION = 'Education',
    ELECTRONICS = 'Electronics',
    ENERGY = 'Energy',
    ENGINEERING = 'Engineering',
    ENTERTAINMENT = 'Entertainment',
    ENVIRONMENTAL = 'Environmental',
    FINANCE = 'Finance',
    FOOD_BEVERAGE = 'Food & Beverage',
    GOVERNMENT = 'Government',
    HEALTHCARE = 'Healthcare',
    HOSPITALITY = 'Hospitality',
    INSURANCE = 'Insurance',
    MACHINERY = 'Machinery',
    MANUFACTURING = 'Manufacturing',
    MEDIA = 'Media',
    NOT_FOR_PROFIT = 'Not For Profit',
    RECREATION = 'Recreation',
    RETAIL = 'Retail',
    SHIPPING = 'Shipping',
    TECHNOLOGY = 'Technology',
    TELECOMMUNICATIONS = 'Telecommunications',
    TRANSPORTATION = 'Transportation',
    UTILITIES = 'Utilities',
    OTHER = 'Other'
}

enum Rating {
    HOT = 'Hot',
    WARM = 'Warm',
    COLD = 'Cold'
}

enum Ownership {
    PUBLIC = 'Public',
    PRIVATE = 'Private',
    SUBSIDIARY = 'Subsidiary',
    OTHER = 'Other'
}

enum AccountStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE'
}
```

### Search and Filter Models

```typescript
interface AccountSearchFilters {
    name?: string;
    industry?: Industry[];
    type?: AccountType[];
    rating?: Rating[];
    annualRevenueMin?: number;
    annualRevenueMax?: number;
    numberOfEmployeesMin?: number;
    numberOfEmployeesMax?: number;
    ownership?: Ownership[];
    country?: string[];
    state?: string[];
    city?: string[];
    parentAccountId?: number;
    ownerId?: number;
    status?: AccountStatus[];
    createdAfter?: Date;
    createdBefore?: Date;
    hasChildren?: boolean;
}

interface PaginatedAccounts {
    accounts: Account[];
    totalCount: number;
    page: number;
    pageSize: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
}
```

### Integration Models

```typescript
interface SyncResult {
    success: boolean;
    salesforceId?: string;
    error?: string;
    conflictResolution?: ConflictResolution;
}

interface SyncConflict {
    field: string;
    vmsValue: any;
    salesforceValue: any;
    lastModifiedVms: Date;
    lastModifiedSalesforce: Date;
}

interface ConflictResolution {
    strategy: 'VMS_WINS' | 'SALESFORCE_WINS' | 'MANUAL';
    resolvedValue: any;
    resolvedBy?: number;
    resolvedAt: Date;
}
```

## Error Handling

### Validation Errors

```typescript
// Input validation using Zod schemas
const AccountCreateSchema = z.object({
    name: z.string().min(1).max(255),
    accountNumber: z.string().max(100).optional(),
    type: z.nativeEnum(AccountType).optional(),
    industry: z.nativeEnum(Industry).optional(),
    annualRevenue: z.number().min(0).optional(),
    numberOfEmployees: z.number().int().min(0).optional(),
    // ... other fields
});

// Error response format
interface ValidationError {
    field: string;
    message: string;
    code: string;
}

interface ErrorResponse {
    success: false;
    error: {
        type: 'VALIDATION_ERROR' | 'BUSINESS_RULE_ERROR' | 'INTEGRATION_ERROR' | 'SYSTEM_ERROR';
        message: string;
        details?: ValidationError[];
    };
}
```

### Business Rule Errors

- Duplicate account names within tenant
- Circular hierarchy references
- Invalid parent-child relationships
- Required field violations
- Data type mismatches

### Integration Errors

- Salesforce API rate limits
- Authentication failures
- Network connectivity issues
- Data mapping conflicts
- Sync timeout errors

### Error Recovery Strategies

- Automatic retry with exponential backoff for transient errors
- Conflict resolution workflows for data synchronization
- Graceful degradation when external services are unavailable
- Comprehensive error logging for debugging and monitoring

## Testing Strategy

### Unit Testing

```typescript
// Account Service Tests
describe('AccountService', () => {
    test('should create account with valid data', async () => {
        const accountData = createValidAccountData();
        const result = await accountService.createAccount(accountData);
        expect(result.id).toBeDefined();
        expect(result.name).toBe(accountData.name);
    });

    test('should reject duplicate account names', async () => {
        const accountData = createValidAccountData();
        await accountService.createAccount(accountData);
        await expect(accountService.createAccount(accountData))
            .rejects.toThrow('Account name already exists');
    });

    test('should prevent circular hierarchy', async () => {
        const parent = await accountService.createAccount(createValidAccountData());
        const child = await accountService.createAccount({
            ...createValidAccountData(),
            parentAccountId: parent.id
        });
        
        await expect(accountService.updateAccount(parent.id, {
            parentAccountId: child.id
        })).rejects.toThrow('Circular hierarchy detected');
    });
});

// Salesforce Integration Tests
describe('SalesforceService', () => {
    test('should sync account to Salesforce', async () => {
        const account = await createTestAccount();
        const result = await salesforceService.syncAccountToSalesforce(account);
        expect(result.success).toBe(true);
        expect(result.salesforceId).toBeDefined();
    });

    test('should handle API rate limits', async () => {
        mockSalesforceRateLimit();
        const account = await createTestAccount();
        const result = await salesforceService.syncAccountToSalesforce(account);
        expect(result.success).toBe(true); // Should succeed after retry
    });
});
```

### Integration Testing

- End-to-end account creation and editing workflows
- Salesforce synchronization scenarios
- Search and filtering functionality
- Hierarchy management operations
- Export functionality testing

### Performance Testing

- Large dataset search performance (>10,000 accounts)
- Hierarchy query optimization testing
- Concurrent user access testing
- Database index effectiveness validation
- API response time benchmarking

### Security Testing

- RBAC enforcement testing
- Data encryption validation
- SQL injection prevention
- XSS protection verification
- Authentication and authorization testing

## Performance Considerations

### Database Optimization

- Proper indexing on frequently queried fields
- JSONB GIN indexes for address searches
- Recursive CTE optimization for hierarchy queries
- Connection pooling for high concurrency
- Query result caching for read-heavy operations

### Frontend Optimization

- Lazy loading for large account lists
- Virtual scrolling for hierarchy trees
- Debounced search inputs
- Optimistic updates for better UX
- Component memoization for performance

### Integration Optimization

- Batch operations for Salesforce sync
- Asynchronous processing for large datasets
- Intelligent retry mechanisms
- Connection pooling for external APIs
- Caching of integration metadata

## Security Considerations

### Data Protection

- Encryption of sensitive fields (addresses, financial data)
- Secure storage of integration credentials
- GDPR compliance for data deletion
- Audit trails for all data modifications
- Role-based access control enforcement

### Integration Security

- OAuth 2.0 for Salesforce authentication
- API key rotation and management
- Secure webhook endpoints
- Rate limiting and throttling
- Input sanitization and validation

### Access Control

- Hierarchical permission inheritance
- Field-level security controls
- IP-based access restrictions
- Session management and timeout
- Multi-factor authentication support