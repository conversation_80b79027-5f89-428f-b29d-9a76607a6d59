# Design Document

## Overview

This design document outlines the implementation of admin management pages and user profile functionality for the VendorMS system. The design maintains consistency with the existing neumorphism design system, responsive layouts, and role-based access patterns established in the current application.

The solution includes three main components:
1. **Admin User Management Page** (`/admin/users`) - Comprehensive user administration interface
2. **Admin Settings Page** (`/admin/settings`) - System configuration and customization
3. **User Profile Settings Page** (`/profile/settings`) - Personal profile management accessible via header dropdown

All pages will integrate seamlessly with the existing Redux store, authentication system, and UI component library.

## Architecture

### Component Structure

```
src/pages/
├── admin/
│   ├── AdminUsers.tsx          # User management interface
│   └── AdminSettings.tsx       # System settings interface
└── ProfileSettings.tsx         # User profile settings

src/components/
├── admin/
│   ├── UserManagementTable.tsx # Data table for user management
│   ├── InviteUserModal.tsx     # User invitation modal
│   ├── RoleAssignmentModal.tsx # Role management modal
│   ├── SettingsForm.tsx        # System settings form
│   ├── CustomFieldBuilder.tsx  # Dynamic field creation
│   └── AuditLogViewer.tsx      # Audit trail component
└── profile/
    ├── PersonalInfoForm.tsx    # Personal information form
    ├── SecuritySettings.tsx    # Password and 2FA settings
    ├── PreferencesForm.tsx     # User preferences
    └── ProfilePictureUpload.tsx # Avatar upload component
```

### State Management

The design leverages existing Redux slices and introduces new ones:

```typescript
// Existing slices (extended)
interface AuthState {
  user: User;
  users: User[];           // New: for admin user management
  invitations: Invitation[]; // New: pending invitations
}

interface UIState {
  theme: 'light' | 'dark';
  language: 'en' | 'es' | 'fr';
  currency: 'USD' | 'EUR' | 'GBP';
  sidebarCollapsed: boolean;
  // New settings
  systemSettings: SystemSettings;
  customFields: CustomField[];
}

// New types
interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'manager' | 'viewer';
  status: 'active' | 'inactive' | 'pending';
  lastLogin: string;
  createdAt: string;
  profilePicture?: string;
  phone?: string;
  preferences: UserPreferences;
}

interface SystemSettings {
  organizationName: string;
  organizationLogo: string;
  contactEmail: string;
  passwordPolicy: PasswordPolicy;
  sessionTimeout: number;
  notificationSettings: NotificationSettings;
}

interface CustomField {
  id: string;
  entityType: 'vendor' | 'contract' | 'invoice';
  fieldName: string;
  fieldType: 'text' | 'number' | 'date' | 'select' | 'boolean';
  required: boolean;
  validation: ValidationRules;
  options?: string[]; // for select fields
}
```

### API Integration

New API endpoints following existing patterns:

```typescript
// Admin User Management
GET    /api/admin/users              # List all users
POST   /api/admin/users/invite       # Send user invitation
PUT    /api/admin/users/:id/role     # Update user role
PUT    /api/admin/users/:id/status   # Update user status
DELETE /api/admin/users/:id          # Deactivate user

// Admin Settings
GET    /api/admin/settings           # Get system settings
PUT    /api/admin/settings           # Update system settings
GET    /api/admin/custom-fields      # Get custom fields
POST   /api/admin/custom-fields      # Create custom field
PUT    /api/admin/custom-fields/:id  # Update custom field
DELETE /api/admin/custom-fields/:id  # Delete custom field
GET    /api/admin/audit-logs         # Get audit trail

// User Profile
GET    /api/profile                  # Get user profile
PUT    /api/profile                  # Update profile
PUT    /api/profile/password         # Change password
POST   /api/profile/avatar           # Upload profile picture
POST   /api/profile/2fa/setup        # Setup 2FA
PUT    /api/profile/preferences      # Update preferences
```

## Components and Interfaces

### 1. Admin User Management Page

**Layout Structure:**
- Header with page title and "Invite User" button
- Search and filter controls in neumorphic card
- User data table with actions
- Pagination controls

**Key Features:**
- **User Table**: Displays name, email, role, status, last login
- **Search & Filter**: Real-time search, role filter, status filter
- **Bulk Actions**: Select multiple users for bulk operations
- **Role Management**: Inline role editing with confirmation
- **User Invitation**: Modal form with email validation
- **Status Management**: Activate/deactivate users with audit trail

**Component Interface:**
```typescript
interface AdminUsersProps {}

interface UserTableProps {
  users: User[];
  onRoleChange: (userId: string, newRole: string) => void;
  onStatusChange: (userId: string, newStatus: string) => void;
  onDelete: (userId: string) => void;
}

interface InviteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInvite: (email: string, role: string) => void;
}
```

### 2. Admin Settings Page

**Layout Structure:**
- Tabbed interface for different setting categories
- Form sections with neumorphic styling
- Save/Cancel actions with confirmation
- Preview functionality for visual changes

**Setting Categories:**
1. **General Settings**: Organization info, logo, contact details
2. **Custom Fields**: Dynamic field builder for entities
3. **Security Settings**: Password policies, session timeouts
4. **Notification Settings**: Email templates, notification preferences
5. **Audit Logs**: System activity tracking and export

**Component Interface:**
```typescript
interface AdminSettingsProps {}

interface SettingsTabProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

interface CustomFieldBuilderProps {
  fields: CustomField[];
  onFieldAdd: (field: Omit<CustomField, 'id'>) => void;
  onFieldUpdate: (id: string, field: Partial<CustomField>) => void;
  onFieldDelete: (id: string) => void;
}
```

### 3. User Profile Settings Page

**Layout Structure:**
- Profile header with avatar and basic info
- Tabbed sections for different settings
- Form validation and real-time updates
- Security section with 2FA setup

**Profile Sections:**
1. **Personal Information**: Name, email, phone, avatar
2. **Security**: Password change, 2FA setup
3. **Preferences**: Theme, language, currency, notifications
4. **Activity**: Login history and session management

**Component Interface:**
```typescript
interface ProfileSettingsProps {}

interface PersonalInfoFormProps {
  user: User;
  onUpdate: (data: Partial<User>) => void;
}

interface SecuritySettingsProps {
  onPasswordChange: (oldPassword: string, newPassword: string) => void;
  onSetup2FA: () => void;
}
```

### 4. Header Dropdown Enhancement

**Updated User Menu:**
- Profile picture/avatar display
- User name and role
- "Profile Settings" navigation
- Existing logout functionality

**Implementation:**
```typescript
// Update existing Header.tsx
const userMenuItems = [
  {
    label: 'Profile Settings',
    icon: Settings,
    action: () => navigate('/profile/settings'),
  },
  {
    label: 'Logout',
    icon: LogOut,
    action: handleLogout,
    variant: 'destructive',
  },
];
```

## Data Models

### User Management Models

```typescript
interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'manager' | 'viewer';
  status: 'active' | 'inactive' | 'pending';
  lastLogin: string;
  createdAt: string;
  updatedAt: string;
  profilePicture?: string;
  phone?: string;
  preferences: UserPreferences;
  invitedBy?: string;
  invitationToken?: string;
  invitationExpiry?: string;
}

interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'en' | 'es' | 'fr';
  currency: 'USD' | 'EUR' | 'GBP';
  notifications: {
    email: boolean;
    browser: boolean;
    mobile: boolean;
  };
  timezone: string;
}

interface Invitation {
  id: string;
  email: string;
  role: string;
  token: string;
  expiresAt: string;
  invitedBy: string;
  status: 'pending' | 'accepted' | 'expired';
  createdAt: string;
}
```

### System Settings Models

```typescript
interface SystemSettings {
  id: string;
  organizationName: string;
  organizationLogo: string;
  contactEmail: string;
  contactPhone: string;
  address: Address;
  passwordPolicy: PasswordPolicy;
  sessionTimeout: number;
  maxLoginAttempts: number;
  notificationSettings: NotificationSettings;
  maintenanceMode: boolean;
  updatedAt: string;
  updatedBy: string;
}

interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  expiryDays: number;
  preventReuse: number;
}

interface CustomField {
  id: string;
  entityType: 'vendor' | 'contract' | 'invoice';
  fieldName: string;
  fieldLabel: string;
  fieldType: 'text' | 'number' | 'date' | 'select' | 'boolean' | 'textarea';
  required: boolean;
  validation: ValidationRules;
  options?: SelectOption[];
  defaultValue?: any;
  helpText?: string;
  order: number;
  active: boolean;
  createdAt: string;
  createdBy: string;
}

interface ValidationRules {
  min?: number;
  max?: number;
  pattern?: string;
  customValidator?: string;
}
```

## Error Handling

### Validation Patterns

```typescript
// Form validation using existing patterns
const userInvitationSchema = {
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'Please enter a valid email address',
  },
  role: {
    required: true,
    enum: ['admin', 'manager', 'viewer'],
    message: 'Please select a valid role',
  },
};

const passwordChangeSchema = {
  currentPassword: {
    required: true,
    message: 'Current password is required',
  },
  newPassword: {
    required: true,
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: 'Password must meet security requirements',
  },
  confirmPassword: {
    required: true,
    match: 'newPassword',
    message: 'Passwords must match',
  },
};
```

### Error States

- **Network Errors**: Toast notifications with retry options
- **Validation Errors**: Inline form validation with clear messages
- **Permission Errors**: Redirect to unauthorized page with explanation
- **Server Errors**: Graceful degradation with fallback UI

## Testing Strategy

### Unit Testing

```typescript
// Component testing with React Testing Library
describe('AdminUsers', () => {
  it('should display user list correctly', () => {
    render(<AdminUsers />);
    expect(screen.getByText('Users')).toBeInTheDocument();
  });

  it('should handle user invitation', async () => {
    const mockInvite = jest.fn();
    render(<InviteUserModal onInvite={mockInvite} />);
    
    fireEvent.change(screen.getByLabelText('Email'), {
      target: { value: '<EMAIL>' }
    });
    fireEvent.click(screen.getByText('Send Invitation'));
    
    expect(mockInvite).toHaveBeenCalledWith('<EMAIL>', 'viewer');
  });
});
```

### Integration Testing

```typescript
// API integration testing
describe('Admin API', () => {
  it('should fetch users successfully', async () => {
    const users = await adminAPI.getUsers();
    expect(users).toHaveLength(3);
    expect(users[0]).toHaveProperty('email');
  });

  it('should handle user invitation', async () => {
    const result = await adminAPI.inviteUser('<EMAIL>', 'manager');
    expect(result.success).toBe(true);
  });
});
```

### E2E Testing

```typescript
// Cypress tests for user flows
describe('Admin User Management', () => {
  it('should allow admin to invite and manage users', () => {
    cy.login('<EMAIL>', 'password');
    cy.visit('/admin/users');
    
    cy.get('[data-testid="invite-user-btn"]').click();
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="role-select"]').select('manager');
    cy.get('[data-testid="send-invitation-btn"]').click();
    
    cy.contains('Invitation sent successfully').should('be.visible');
  });
});
```

## Security Considerations

### Access Control

- **Route Protection**: Admin routes protected by role-based guards
- **API Authorization**: Backend validation of admin permissions
- **Session Management**: Secure token handling and expiration
- **Audit Logging**: All admin actions logged with user identification

### Data Protection

- **Input Sanitization**: All form inputs validated and sanitized
- **File Upload Security**: Avatar uploads with type and size validation
- **Password Security**: Hashing with bcrypt, secure password policies
- **CSRF Protection**: Token-based protection for state-changing operations

### Privacy Compliance

- **Data Minimization**: Only collect necessary user information
- **Consent Management**: Clear privacy policy and consent flows
- **Data Export**: User data export functionality for GDPR compliance
- **Data Deletion**: Secure user data deletion with audit trail

## Performance Optimizations

### Frontend Optimizations

- **Code Splitting**: Lazy load admin pages to reduce initial bundle size
- **Memoization**: React.memo for expensive components
- **Virtual Scrolling**: For large user lists and audit logs
- **Image Optimization**: Compressed avatars with lazy loading

### Backend Optimizations

- **Pagination**: Server-side pagination for user lists
- **Caching**: Redis caching for frequently accessed settings
- **Database Indexing**: Optimized queries for user searches
- **Rate Limiting**: API rate limiting to prevent abuse

### Monitoring

- **Performance Metrics**: Track page load times and API response times
- **Error Tracking**: Comprehensive error logging and alerting
- **User Analytics**: Track feature usage and user engagement
- **System Health**: Monitor system resources and database performance