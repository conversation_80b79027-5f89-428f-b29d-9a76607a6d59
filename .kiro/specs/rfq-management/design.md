# Design Document

## Overview

The RFQ (Request for Quote) Management module is a comprehensive feature that enables procurement managers to streamline competitive bidding processes within the VendorMS system. This module integrates seamlessly with existing vendor management, contract management, and invoicing modules to provide an end-to-end procurement workflow.

The system supports creating RFQs with multiple line items, distributing them to selected vendors via public submission forms, collecting and analyzing vendor bids with AI-powered recommendations, generating professional quotes for clients, and automatically transitioning approved quotes to invoices.

Key architectural principles:
- **Integration-First Design**: Leverages existing VendorMS infrastructure and data models
- **Public Access Security**: Secure token-based access for vendor submissions without authentication
- **AI-Enhanced Decision Making**: Machine learning recommendations for optimal vendor selection
- **Audit Trail Compliance**: Complete tracking of all RFQ activities for regulatory compliance
- **Scalable Architecture**: Designed to handle high-volume RFQ processing with performance optimization

## Architecture

### System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[RFQ Creation UI] --> B[Bid Review Dashboard]
        B --> C[Quote Generation UI]
        D[Public Submission Form] --> E[Client Approval Interface]
    end
    
    subgraph "API Layer"
        F[RFQ Controller] --> G[Bid Controller]
        G --> H[Quote Controller]
        I[Public API Controller]
    end
    
    subgraph "Business Logic Layer"
        J[RFQ Service] --> K[AI Recommendation Service]
        K --> L[Quote Generation Service]
        M[Email Notification Service] --> N[File Upload Service]
    end
    
    subgraph "Data Layer"
        O[RFQ Tables] --> P[Vendor Integration]
        P --> Q[Invoice Integration]
        R[Audit Logging] --> S[File Storage]
    end
    
    A --> F
    D --> I
    F --> J
    I --> J
    J --> O
    J --> P
    L --> Q
```

### Database Architecture

The RFQ module extends the existing VendorMS database schema with new tables while maintaining referential integrity with existing modules:

```mermaid
erDiagram
    VENDORS ||--o{ RFQ_INVITATIONS : receives
    USERS ||--o{ RFQS : creates
    RFQS ||--o{ RFQ_ITEMS : contains
    RFQS ||--o{ RFQ_INVITATIONS : has
    RFQS ||--o{ RFQ_SUBMISSIONS : receives
    RFQ_SUBMISSIONS ||--o{ BID_ITEMS : contains
    RFQS ||--o{ CLIENT_QUOTES : generates
    CLIENT_QUOTES ||--o{ INVOICES : creates
    
    RFQS {
        int id PK
        int creator_id FK
        string title
        text description
        jsonb items
        timestamp due_date
        enum status
        jsonb form_config
        jsonb ai_settings
        timestamp created_at
        timestamp updated_at
    }
    
    RFQ_INVITATIONS {
        int id PK
        int rfq_id FK
        int vendor_id FK
        string token
        enum status
        timestamp sent_at
        timestamp viewed_at
        timestamp submitted_at
    }
    
    RFQ_SUBMISSIONS {
        int id PK
        int rfq_id FK
        int vendor_id FK
        string invitation_token
        jsonb bid_data
        jsonb attachments
        decimal total_amount
        string currency
        timestamp submitted_at
        timestamp updated_at
    }
    
    BID_ITEMS {
        int id PK
        int submission_id FK
        int rfq_item_id
        string item_name
        decimal unit_price
        int quantity
        decimal total_price
        text notes
        int delivery_days
    }
    
    CLIENT_QUOTES {
        int id PK
        int rfq_id FK
        jsonb selected_bids
        decimal total_amount
        string currency
        jsonb terms
        enum status
        string public_token
        timestamp approved_at
        int invoice_id FK
        timestamp created_at
        timestamp updated_at
    }
```

### Integration Points

The RFQ module integrates with existing VendorMS modules through well-defined interfaces:

1. **Vendor Management Integration**
   - Pulls vendor contact information and performance scores
   - Updates vendor engagement metrics
   - Leverages vendor categorization for targeted invitations

2. **Contract Management Integration**
   - Uses contract templates for RFQ terms and conditions
   - Generates contracts from successful RFQ outcomes
   - Inherits approval workflow patterns

3. **Invoice Management Integration**
   - Automatically creates invoices from approved quotes
   - Maps RFQ line items to invoice line items
   - Maintains audit trail linkage

4. **Authentication & Authorization Integration**
   - Uses existing JWT token system for internal users
   - Implements secure token-based access for public forms
   - Respects role-based permissions (Admin, Manager, Viewer)

## Components and Interfaces

### Frontend Components

#### RFQ Creation Components

**RFQCreationForm**
```typescript
interface RFQCreationFormProps {
  onSubmit: (rfqData: RFQCreateData) => void;
  vendors: Vendor[];
  templates: RFQTemplate[];
  isLoading: boolean;
}

interface RFQCreateData {
  title: string;
  description: string;
  items: RFQItem[];
  dueDate: Date;
  selectedVendors: number[];
  formConfig: FormFieldConfig[];
  terms: string;
  aiSettings: AIRecommendationSettings;
}
```

**ItemListBuilder**
```typescript
interface ItemListBuilderProps {
  items: RFQItem[];
  onChange: (items: RFQItem[]) => void;
  allowCustomFields: boolean;
}

interface RFQItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  specifications: Record<string, any>;
  estimatedPrice?: number;
  category: string;
}
```

**VendorSelector**
```typescript
interface VendorSelectorProps {
  vendors: Vendor[];
  selectedVendors: number[];
  onSelectionChange: (vendorIds: number[]) => void;
  filters: VendorFilterOptions;
  showPerformanceScores: boolean;
}
```

#### Bid Review Components

**BidComparisonDashboard**
```typescript
interface BidComparisonDashboardProps {
  rfq: RFQ;
  submissions: RFQSubmission[];
  aiRecommendations: AIRecommendation[];
  onSelectionChange: (selections: BidSelection[]) => void;
}

interface BidSelection {
  itemId: string;
  selectedSubmissionId: number;
  selectedVendorId: number;
  rationale: string;
  overrideAI: boolean;
}
```

**AIRecommendationPanel**
```typescript
interface AIRecommendationPanelProps {
  recommendations: AIRecommendation[];
  onAccept: (recommendationId: string) => void;
  onReject: (recommendationId: string, reason: string) => void;
  showConfidenceScores: boolean;
}

interface AIRecommendation {
  id: string;
  itemId: string;
  recommendedVendorId: number;
  recommendedSubmissionId: number;
  confidence: number;
  factors: RecommendationFactor[];
  rationale: string;
  costSavings: number;
  riskScore: number;
}
```

#### Public Submission Components

**PublicSubmissionForm**
```typescript
interface PublicSubmissionFormProps {
  rfq: PublicRFQData;
  token: string;
  onSubmit: (bidData: BidSubmissionData) => void;
  isLoading: boolean;
}

interface PublicRFQData {
  id: number;
  title: string;
  description: string;
  items: RFQItem[];
  dueDate: Date;
  formConfig: FormFieldConfig[];
  terms: string;
  companyInfo: CompanyInfo;
}
```

### Backend Services

#### RFQ Service

```typescript
class RFQService {
  async createRFQ(rfqData: RFQCreateData, userId: number): Promise<RFQ>;
  async sendInvitations(rfqId: number, vendorIds: number[]): Promise<RFQInvitation[]>;
  async getRFQSubmissions(rfqId: number, userId: number): Promise<RFQSubmission[]>;
  async generateAIRecommendations(rfqId: number): Promise<AIRecommendation[]>;
  async updateRFQStatus(rfqId: number, status: RFQStatus, userId: number): Promise<RFQ>;
  async getRFQAnalytics(rfqId: number): Promise<RFQAnalytics>;
}
```

#### AI Recommendation Service

```typescript
class AIRecommendationService {
  async analyzeSubmissions(submissions: RFQSubmission[], rfq: RFQ): Promise<AIRecommendation[]>;
  async calculateOptimalSelections(
    submissions: RFQSubmission[],
    criteria: SelectionCriteria
  ): Promise<OptimalSelection[]>;
  async getVendorRiskAssessment(vendorId: number): Promise<RiskAssessment>;
  async updateModelWeights(feedback: SelectionFeedback[]): Promise<void>;
}

interface SelectionCriteria {
  priceWeight: number; // 0-1
  performanceWeight: number; // 0-1
  deliveryWeight: number; // 0-1
  riskWeight: number; // 0-1
  diversificationPreference: number; // 0-1
}
```

#### Quote Generation Service

```typescript
class QuoteGenerationService {
  async generateQuote(
    rfqId: number,
    selections: BidSelection[],
    quoteConfig: QuoteConfig
  ): Promise<ClientQuote>;
  async formatQuotePDF(quote: ClientQuote): Promise<Buffer>;
  async sendQuoteToClient(quoteId: number, clientEmail: string): Promise<void>;
  async processQuoteApproval(quoteId: number, approvalData: QuoteApproval): Promise<Invoice>;
}
```

### API Endpoints

#### Internal API (Authenticated)

```typescript
// RFQ Management
POST   /api/rfqs                    // Create new RFQ
GET    /api/rfqs                    // List RFQs with filters
GET    /api/rfqs/:id                // Get RFQ details
PUT    /api/rfqs/:id                // Update RFQ
DELETE /api/rfqs/:id                // Delete RFQ
POST   /api/rfqs/:id/send           // Send invitations
GET    /api/rfqs/:id/submissions    // Get submissions
POST   /api/rfqs/:id/ai-recommend   // Get AI recommendations
POST   /api/rfqs/:id/generate-quote // Generate client quote

// Quote Management
GET    /api/quotes                  // List quotes
GET    /api/quotes/:id              // Get quote details
PUT    /api/quotes/:id              // Update quote
POST   /api/quotes/:id/send         // Send quote to client
GET    /api/quotes/:id/pdf          // Download quote PDF

// Analytics
GET    /api/rfqs/analytics          // RFQ performance analytics
GET    /api/rfqs/:id/analytics      // Specific RFQ analytics
```

#### Public API (Token-based)

```typescript
// Public Submission
GET    /public/rfq/:token           // Get RFQ details for submission
POST   /public/rfq/:token/submit    // Submit bid
GET    /public/rfq/:token/status    // Check submission status

// Quote Approval
GET    /public/quote/:token         // View quote for approval
POST   /public/quote/:token/approve // Approve quote
POST   /public/quote/:token/feedback // Provide feedback
```

## Data Models

### Core RFQ Models

```typescript
interface RFQ {
  id: number;
  creatorId: number;
  title: string;
  description: string;
  items: RFQItem[];
  dueDate: Date;
  status: RFQStatus;
  formConfig: FormFieldConfig[];
  aiSettings: AIRecommendationSettings;
  invitations: RFQInvitation[];
  submissions: RFQSubmission[];
  createdAt: Date;
  updatedAt: Date;
}

enum RFQStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  IN_PROGRESS = 'in_progress',
  CLOSED = 'closed',
  CANCELLED = 'cancelled'
}

interface RFQItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  specifications: Record<string, any>;
  category: string;
  estimatedPrice?: number;
  customFields: Record<string, any>;
}

interface FormFieldConfig {
  id: string;
  type: 'text' | 'number' | 'date' | 'file' | 'select' | 'textarea';
  label: string;
  required: boolean;
  options?: string[];
  validation?: ValidationRule[];
  itemSpecific: boolean;
}
```

### Submission Models

```typescript
interface RFQSubmission {
  id: number;
  rfqId: number;
  vendorId: number;
  invitationToken: string;
  bidData: BidData;
  attachments: FileAttachment[];
  totalAmount: number;
  currency: string;
  submittedAt: Date;
  updatedAt: Date;
}

interface BidData {
  items: BidItem[];
  generalTerms: Record<string, any>;
  deliverySchedule: DeliverySchedule;
  paymentTerms: string;
  validityPeriod: number;
  additionalNotes: string;
}

interface BidItem {
  rfqItemId: string;
  unitPrice: number;
  totalPrice: number;
  deliveryDays: number;
  specifications: Record<string, any>;
  alternatives?: AlternativeOption[];
  notes: string;
}
```

### AI and Analytics Models

```typescript
interface AIRecommendation {
  id: string;
  rfqId: number;
  itemId: string;
  recommendedVendorId: number;
  recommendedSubmissionId: number;
  confidence: number;
  factors: RecommendationFactor[];
  rationale: string;
  costSavings: number;
  riskScore: number;
  createdAt: Date;
}

interface RecommendationFactor {
  name: string;
  weight: number;
  score: number;
  description: string;
}

interface RFQAnalytics {
  rfqId: number;
  totalInvitations: number;
  responseRate: number;
  averageResponseTime: number;
  costSavings: number;
  vendorParticipation: VendorParticipation[];
  itemAnalysis: ItemAnalysis[];
  timelineMetrics: TimelineMetric[];
}
```

## Error Handling

### Error Categories

1. **Validation Errors**
   - Invalid RFQ data structure
   - Missing required fields
   - Invalid date ranges
   - Malformed vendor selections

2. **Business Logic Errors**
   - RFQ deadline passed
   - Insufficient vendor selections
   - Duplicate submissions
   - Invalid status transitions

3. **Integration Errors**
   - Vendor data not found
   - Email delivery failures
   - File upload errors
   - AI service unavailable

4. **Security Errors**
   - Invalid submission tokens
   - Expired access tokens
   - Unauthorized access attempts
   - Rate limit exceeded

### Error Response Format

```typescript
interface APIError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: Date;
  requestId: string;
}

// Example error responses
{
  "code": "RFQ_VALIDATION_ERROR",
  "message": "Invalid RFQ data provided",
  "details": {
    "title": "Title is required",
    "dueDate": "Due date must be in the future",
    "items": "At least one item is required"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456"
}
```

### Error Handling Strategy

1. **Client-Side Error Handling**
   - Form validation with real-time feedback
   - Graceful degradation for network issues
   - User-friendly error messages
   - Retry mechanisms for transient failures

2. **Server-Side Error Handling**
   - Comprehensive input validation
   - Database transaction rollbacks
   - Structured error logging
   - Circuit breaker patterns for external services

3. **Public Form Error Handling**
   - Token validation and expiration handling
   - File upload error recovery
   - Submission conflict resolution
   - Offline submission queuing

## Testing Strategy

### Unit Testing

1. **Service Layer Testing**
   - RFQ creation and validation logic
   - AI recommendation algorithms
   - Quote generation calculations
   - Email notification services

2. **Model Testing**
   - Data validation rules
   - Business logic constraints
   - Relationship integrity
   - Serialization/deserialization

3. **Utility Testing**
   - Token generation and validation
   - File upload handling
   - Date/time calculations
   - Currency conversions

### Integration Testing

1. **API Integration Testing**
   - End-to-end RFQ workflow
   - Public submission flow
   - Quote approval process
   - Invoice generation integration

2. **Database Integration Testing**
   - Transaction integrity
   - Constraint validation
   - Performance under load
   - Data migration scenarios

3. **External Service Integration**
   - Email delivery testing
   - File storage operations
   - AI service integration
   - Payment gateway integration

### End-to-End Testing

1. **User Journey Testing**
   - Complete RFQ creation to invoice flow
   - Vendor submission experience
   - Client quote approval process
   - Multi-user collaboration scenarios

2. **Performance Testing**
   - High-volume RFQ processing
   - Concurrent submission handling
   - Large file upload scenarios
   - Database query optimization

3. **Security Testing**
   - Token security validation
   - Access control verification
   - Input sanitization testing
   - Rate limiting effectiveness

### Test Data Management

```typescript
// Test data factories
class RFQTestFactory {
  static createRFQ(overrides?: Partial<RFQ>): RFQ;
  static createSubmission(rfqId: number, overrides?: Partial<RFQSubmission>): RFQSubmission;
  static createVendorList(count: number): Vendor[];
}

// Test scenarios
const testScenarios = {
  simpleRFQ: {
    items: 3,
    vendors: 5,
    submissions: 4
  },
  complexRFQ: {
    items: 15,
    vendors: 20,
    submissions: 18,
    customFields: true,
    aiRecommendations: true
  },
  highVolumeRFQ: {
    items: 100,
    vendors: 50,
    submissions: 45,
    fileAttachments: true
  }
};
```

## Security Considerations

### Authentication and Authorization

1. **Internal User Authentication**
   - JWT token-based authentication
   - Role-based access control (Admin, Manager, Viewer)
   - Session management and timeout
   - Multi-factor authentication support

2. **Public Access Security**
   - Secure token generation for vendor submissions
   - Token expiration and rotation
   - Rate limiting for public endpoints
   - CAPTCHA integration for spam prevention

3. **Data Access Control**
   - RFQ creator permissions
   - Vendor data isolation
   - Audit trail protection
   - Sensitive data encryption

### Data Protection

1. **Data Encryption**
   - Encryption at rest for sensitive data
   - TLS encryption for data in transit
   - Secure file storage with access controls
   - Database field-level encryption

2. **Privacy Compliance**
   - GDPR compliance for vendor data
   - Data retention policies
   - Right to deletion implementation
   - Consent management

3. **Audit and Compliance**
   - Comprehensive audit logging
   - Immutable audit trails
   - Regulatory reporting capabilities
   - Data integrity verification

### Input Validation and Sanitization

```typescript
// Input validation schemas
const rfqValidationSchema = {
  title: {
    type: 'string',
    minLength: 3,
    maxLength: 255,
    sanitize: true
  },
  description: {
    type: 'string',
    maxLength: 5000,
    sanitize: true,
    allowHTML: false
  },
  items: {
    type: 'array',
    minItems: 1,
    maxItems: 100,
    items: {
      type: 'object',
      properties: {
        name: { type: 'string', minLength: 1, maxLength: 255 },
        quantity: { type: 'number', minimum: 1, maximum: 1000000 }
      }
    }
  }
};
```

## Performance Optimization

### Database Optimization

1. **Indexing Strategy**
   - Composite indexes for common query patterns
   - Full-text search indexes for RFQ content
   - Partial indexes for status-based queries
   - GIN indexes for JSONB fields

2. **Query Optimization**
   - Efficient pagination with cursor-based navigation
   - Optimized joins for submission aggregation
   - Materialized views for analytics
   - Query result caching

3. **Data Archiving**
   - Automated archiving of completed RFQs
   - Compressed storage for historical data
   - Efficient data retrieval for reporting
   - Compliance-aware data retention

### Application Performance

1. **Caching Strategy**
   - Redis caching for frequently accessed data
   - CDN caching for static assets
   - Application-level caching for AI recommendations
   - Database query result caching

2. **Asynchronous Processing**
   - Background job processing for email notifications
   - Async AI recommendation generation
   - Batch processing for bulk operations
   - Queue management for high-volume scenarios

3. **Resource Optimization**
   - Connection pooling for database access
   - Memory-efficient file processing
   - Optimized image and document handling
   - Lazy loading for large datasets

### Scalability Considerations

1. **Horizontal Scaling**
   - Stateless application design
   - Load balancer configuration
   - Database read replicas
   - Microservice architecture readiness

2. **Monitoring and Alerting**
   - Performance metrics collection
   - Real-time alerting for system issues
   - Capacity planning and forecasting
   - User experience monitoring