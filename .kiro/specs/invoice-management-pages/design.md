# Design Document

## Overview

The Invoice Management Pages will provide a comprehensive interface for managing the complete invoice lifecycle within the VendorMS system. The design maintains strict consistency with the existing neumorphism UI theme, responsive layouts, and interaction patterns established in the vendor and contract management modules. The system will support four primary views: Invoice List, Invoice Creation, Invoice Detail View, and Invoice Edit, each optimized for their specific use cases while maintaining a cohesive user experience.

The design leverages the existing Redux Toolkit state management architecture, Framer Motion animations, and Shadcn/ui component library to ensure seamless integration with the current system. All pages will be fully responsive, accessible, and support the established role-based access control patterns.

## Architecture

### Component Hierarchy

```
InvoiceManagement/
├── InvoicesList.tsx (Main list view)
├── InvoiceCreate.tsx (Creation form)
├── InvoiceView.tsx (Detail view)
├── InvoiceEdit.tsx (Edit form)
└── components/
    ├── InvoiceCard.tsx (List item component)
    ├── InvoiceForm.tsx (Shared form component)
    ├── InvoiceStatusBadge.tsx (Status indicator)
    ├── PaymentHistory.tsx (Payment tracking)
    ├── LineItemsTable.tsx (Invoice line items)
    └── ApprovalWorkflow.tsx (Approval interface)
```

### State Management Integration

The invoice pages will integrate with the existing Redux store structure:

- **invoicesSlice.ts**: Extended with additional actions for CRUD operations, filtering, and pagination
- **Integration with vendorsSlice.ts**: For vendor selection and information display
- **Integration with contractsSlice.ts**: For contract-based invoice generation
- **New actions**: `createInvoiceAsync`, `updateInvoiceAsync`, `deleteInvoiceAsync`, `approveInvoiceAsync`

### Routing Structure

```
/invoices
├── / (InvoicesList - main list view)
├── /create (InvoiceCreate - new invoice form)
├── /create?contractId=:id (Contract-based creation)
├── /:id/view (InvoiceView - read-only detail)
├── /:id/edit (InvoiceEdit - editable form)
└── /:id/approve (Approval workflow)
```

## Components and Interfaces

### 1. InvoicesList Component

**Purpose**: Main dashboard for viewing and managing all invoices with filtering, searching, and bulk operations.

**Design Pattern**: Follows the established pattern from VendorsList.tsx and ContractsList.tsx with neumorphic cards and responsive grid layout.

**Key Features**:
- Neumorphic card-based layout with hover animations
- Advanced filtering (status, vendor, date range, amount range)
- Search functionality across invoice numbers, vendor names, and descriptions
- Sortable columns (date, amount, status, vendor)
- Bulk selection and operations
- Status-based color coding and icons
- Responsive grid that adapts to screen size

**UI Elements**:
- Header with title, description, and "Create Invoice" button
- Search bar with filter toggle (matching vendor list pattern)
- Statistics cards showing invoice counts by status
- Invoice cards with key information and action buttons
- Pagination controls
- Empty state with call-to-action

**Data Display**:
```typescript
interface InvoiceCardData {
  id: number;
  invoiceNumber: string;
  vendorName: string;
  amount: number;
  currency: string;
  status: 'draft' | 'sent' | 'approved' | 'paid' | 'overdue';
  dueDate: string;
  createdDate: string;
  contractReference?: string;
}
```

### 2. InvoiceCreate Component

**Purpose**: Form interface for creating new invoices either manually or from existing contracts.

**Design Pattern**: Multi-step form with neumorphic styling, similar to vendor onboarding flow.

**Key Features**:
- Two creation modes: Manual and Contract-based
- Vendor selection with search and autocomplete
- Dynamic line item management (add/remove/edit)
- Automatic tax and total calculations
- Draft saving functionality
- Form validation with real-time feedback
- Contract data pre-population when applicable

**Form Structure**:
1. **Invoice Header**: Vendor selection, invoice number, dates
2. **Line Items**: Dynamic table with description, quantity, unit price
3. **Calculations**: Subtotal, taxes, penalties, total
4. **Additional Details**: Notes, attachments, payment terms
5. **Actions**: Save as draft, submit for approval

**Validation Rules**:
- Required fields: Vendor, at least one line item, due date
- Business rules: Due date must be future, amounts must be positive
- Format validation: Email, phone, currency amounts

### 3. InvoiceView Component

**Purpose**: Read-only detailed view of invoice information with approval and payment tracking.

**Design Pattern**: Card-based layout with sections, similar to vendor profile view.

**Key Features**:
- Complete invoice information display
- Vendor information panel
- Contract reference (if applicable)
- Payment history timeline
- Approval workflow status
- Document attachments
- Action buttons based on user permissions and invoice status

**Information Sections**:
1. **Invoice Header**: Number, dates, amounts, status
2. **Vendor Details**: Name, contact, address
3. **Line Items**: Detailed breakdown with calculations
4. **Payment Information**: History, methods, transaction IDs
5. **Approval Trail**: Timestamps, approvers, comments
6. **Attachments**: Supporting documents and receipts

### 4. InvoiceEdit Component

**Purpose**: Editable form for modifying draft and pending invoices with audit trail.

**Design Pattern**: Form layout matching InvoiceCreate with additional audit information.

**Key Features**:
- Conditional editing based on invoice status and user permissions
- Change tracking and audit trail
- Amendment creation for approved invoices
- Real-time validation and calculation updates
- Confirmation dialogs for significant changes

**Edit Permissions**:
- **Draft invoices**: Full editing for creators and managers
- **Pending invoices**: Limited editing for managers
- **Approved invoices**: Amendment creation only (special permissions)
- **Paid invoices**: Read-only with dispute option

### 5. Shared Components

#### InvoiceStatusBadge
- Consistent status visualization across all views
- Color-coded badges with icons
- Hover tooltips with status descriptions

#### LineItemsTable
- Reusable table component for line item display/editing
- Automatic calculations
- Add/remove functionality in edit mode
- Responsive design for mobile devices

#### PaymentHistory
- Timeline component showing payment events
- Integration with payment gateway status
- Visual indicators for successful/failed payments

## Data Models

### Extended Invoice Interface

```typescript
interface Invoice {
  id: number;
  invoiceNumber: string;
  contractId?: number;
  vendorId: number;
  vendor?: Vendor; // Populated from vendor data
  contract?: Contract; // Populated from contract data
  
  // Financial Information
  lineItems: LineItem[];
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  penalties: number;
  discounts: number;
  totalAmount: number;
  currency: 'USD' | 'EUR' | 'GBP';
  
  // Status and Workflow
  status: 'draft' | 'pending' | 'approved' | 'sent' | 'paid' | 'overdue' | 'disputed';
  approvalStatus?: 'pending' | 'approved' | 'rejected';
  approvedBy?: number;
  approvedAt?: string;
  approvalComments?: string;
  
  // Dates
  createdAt: string;
  updatedAt: string;
  dueDate: string;
  sentDate?: string;
  paidDate?: string;
  
  // Additional Information
  notes?: string;
  attachments: Attachment[];
  paymentHistory: PaymentRecord[];
  auditTrail: AuditRecord[];
  
  // Payment Integration
  paymentMethod?: 'stripe' | 'paypal' | 'bank_transfer';
  transactionId?: string;
  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';
}

interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxable: boolean;
}

interface PaymentRecord {
  id: string;
  amount: number;
  method: string;
  transactionId: string;
  status: string;
  processedAt: string;
  gateway: 'stripe' | 'paypal';
}

interface AuditRecord {
  id: string;
  action: string;
  userId: number;
  timestamp: string;
  changes: Record<string, any>;
  comments?: string;
}
```

## Error Handling

### Validation Errors
- Real-time field validation with inline error messages
- Form-level validation summary
- Business rule validation (e.g., duplicate invoice numbers)

### API Errors
- Network error handling with retry options
- Server error display with user-friendly messages
- Optimistic updates with rollback on failure

### Payment Errors
- Gateway-specific error handling
- Payment failure recovery flows
- Transaction status synchronization

## Testing Strategy

### Unit Testing
- Component rendering and prop handling
- Form validation logic
- Calculation functions (taxes, totals)
- State management actions and reducers

### Integration Testing
- API integration with mock services
- Payment gateway integration (sandbox mode)
- Cross-component data flow
- Route navigation and parameter handling

### End-to-End Testing
- Complete invoice creation workflow
- Approval process testing
- Payment processing flows
- Multi-user collaboration scenarios

### Accessibility Testing
- Screen reader compatibility
- Keyboard navigation
- Color contrast validation
- ARIA label verification

## Performance Considerations

### Data Loading
- Lazy loading for large invoice lists
- Pagination with configurable page sizes
- Caching of vendor and contract data
- Optimistic updates for better UX

### Bundle Optimization
- Code splitting by route
- Lazy loading of heavy components (charts, PDF viewers)
- Tree shaking of unused dependencies

### Memory Management
- Cleanup of event listeners and subscriptions
- Efficient re-rendering with React.memo
- Proper dependency arrays in useEffect hooks

## Security Considerations

### Data Protection
- Sensitive financial data encryption
- Secure transmission of payment information
- PCI DSS compliance for payment processing

### Access Control
- Role-based permissions for all operations
- Invoice-level access restrictions
- Audit logging for all financial operations

### Input Validation
- Server-side validation for all financial data
- SQL injection prevention
- XSS protection for user inputs

## Mobile Responsiveness

### Breakpoint Strategy
- Mobile-first design approach
- Responsive grid layouts
- Touch-friendly interaction targets
- Optimized form layouts for mobile

### Mobile-Specific Features
- Swipe gestures for card actions
- Collapsible sections for better space usage
- Mobile-optimized date/number pickers
- Simplified navigation patterns

## Integration Points

### Existing System Integration
- Vendor data synchronization
- Contract reference linking
- User authentication and permissions
- Notification system integration

### External Service Integration
- Payment gateway APIs (Stripe, PayPal)
- Email service for invoice delivery
- PDF generation for invoice documents
- Currency conversion services

### Future Integration Considerations
- ERP system connectivity
- Accounting software integration
- Bank reconciliation services
- Tax calculation services