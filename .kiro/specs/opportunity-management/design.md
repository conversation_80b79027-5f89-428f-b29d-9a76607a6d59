# Design Document

## Overview

The Opportunity Management module extends VendorMS with comprehensive CRM functionality for tracking procurement deals and sales pipelines. The design closely mirrors Salesforce's Opportunity object structure to enable seamless integration while providing VMS-specific enhancements for procurement workflows. The module serves as the central hub connecting Accounts and Contacts to RFQs, Quotes, and Invoices, creating an end-to-end deal management system.

Key design principles:
- **Salesforce Compatibility**: Field names, types, and behaviors align with Salesforce standards
- **Integration-First**: Built for bi-directional sync with external CRM systems
- **Procurement-Focused**: Enhanced with VMS-specific features for vendor sourcing
- **Multi-Tenant Isolation**: Maintains per-tenant data separation and security
- **Performance Optimized**: Designed for fast queries and real-time updates

## Architecture

### System Integration

```mermaid
graph TB
    subgraph "VendorMS Core"
        A[Account Management]
        C[Contact Management]
        V[Vendor Management]
        R[RFQ Management]
        Q[Quote Management]
        I[Invoice Management]
    end
    
    subgraph "Opportunity Module"
        O[Opportunity Management]
        P[Pipeline Views]
        W[Workflow Engine]
        S[Sync Service]
    end
    
    subgraph "External Systems"
        SF[Salesforce]
        ERP[ERP Systems]
    end
    
    A --> O
    C --> O
    O --> R
    O --> Q
    Q --> I
    O --> P
    O --> W
    S --> SF
    S --> ERP
    O --> S
```

### Data Flow Architecture

The opportunity module follows a layered architecture:

1. **Presentation Layer**: React components with Redux state management
2. **API Layer**: RESTful endpoints with Express.js controllers
3. **Business Logic Layer**: Service classes handling opportunity workflows
4. **Data Access Layer**: Prisma ORM with PostgreSQL database
5. **Integration Layer**: Sync services for external CRM systems

### Technology Stack

- **Frontend**: React 18, Redux Toolkit, React Router, Tailwind CSS
- **Backend**: Node.js, Express.js, Prisma ORM
- **Database**: PostgreSQL with JSONB for flexible data
- **Integration**: jsforce for Salesforce, REST APIs for other systems
- **UI Components**: Shadcn/ui, React Beautiful DND for kanban
- **Charts**: Recharts for pipeline analytics

## Components and Interfaces

### Database Schema

```sql
-- Opportunity table with Salesforce-compatible fields
CREATE TABLE opportunities (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    account_id INTEGER NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    amount DECIMAL(15,2),
    close_date DATE,
    stage_name VARCHAR(50) DEFAULT 'Prospecting',
    probability FLOAT DEFAULT 0,
    type VARCHAR(50),
    lead_source VARCHAR(50),
    is_private BOOLEAN DEFAULT FALSE,
    description TEXT,
    forecast_category VARCHAR(50) DEFAULT 'Pipeline',
    next_step TEXT,
    is_closed BOOLEAN DEFAULT FALSE,
    is_won BOOLEAN DEFAULT FALSE,
    owner_id INTEGER REFERENCES users(id),
    campaign_id VARCHAR(50),
    has_opportunity_line_item BOOLEAN DEFAULT FALSE,
    items JSONB,
    custom_fields JSONB,
    integration_id VARCHAR(50) UNIQUE,
    status VARCHAR(20) DEFAULT 'OPEN',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by_id INTEGER REFERENCES users(id),
    last_modified_by_id INTEGER REFERENCES users(id),
    is_deleted BOOLEAN DEFAULT FALSE
);

-- Indexes for performance
CREATE INDEX idx_opportunities_account_id ON opportunities(account_id);
CREATE INDEX idx_opportunities_stage_name ON opportunities(stage_name);
CREATE INDEX idx_opportunities_close_date ON opportunities(close_date);
CREATE INDEX idx_opportunities_owner_id ON opportunities(owner_id);
CREATE INDEX idx_opportunities_amount ON opportunities(amount);
CREATE INDEX idx_opportunities_integration_id ON opportunities(integration_id);

-- Junction table for opportunity-contact relationships
CREATE TABLE opportunity_contacts (
    id SERIAL PRIMARY KEY,
    opportunity_id INTEGER NOT NULL REFERENCES opportunities(id) ON DELETE CASCADE,
    contact_id INTEGER NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
    role VARCHAR(50),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(opportunity_id, contact_id)
);

-- Enum types for consistent values
CREATE TYPE opportunity_stage AS ENUM (
    'Prospecting',
    'Qualification',
    'Needs Analysis',
    'Value Proposition',
    'Id. Decision Makers',
    'Perception Analysis',
    'Proposal/Price Quote',
    'Negotiation/Review',
    'Closed Won',
    'Closed Lost'
);

CREATE TYPE opportunity_type AS ENUM (
    'New Customer',
    'Existing Customer - Upgrade',
    'Existing Customer - Replacement',
    'Existing Customer - Downgrade',
    'Renewal - Upsell',
    'Renewal - Renewal'
);

CREATE TYPE lead_source AS ENUM (
    'Web',
    'Phone Inquiry',
    'Partner Referral',
    'Purchased List',
    'Other'
);

CREATE TYPE forecast_category AS ENUM (
    'Omitted',
    'Pipeline',
    'Best Case',
    'Commit',
    'Closed'
);
```

### API Endpoints

```typescript
// Opportunity CRUD operations
POST   /api/opportunities              // Create new opportunity
GET    /api/opportunities              // List opportunities with filters
GET    /api/opportunities/:id          // Get specific opportunity
PUT    /api/opportunities/:id          // Update opportunity
DELETE /api/opportunities/:id          // Soft delete opportunity

// Pipeline and analytics
GET    /api/opportunities/pipeline     // Pipeline view data
GET    /api/opportunities/analytics    // Analytics and reports
GET    /api/opportunities/forecast     // Forecast calculations

// Integration endpoints
POST   /api/opportunities/:id/sync     // Manual sync to Salesforce
GET    /api/opportunities/sync-status  // Check sync status
POST   /api/integrations/salesforce/webhook // Salesforce webhook

// Workflow endpoints
POST   /api/opportunities/:id/advance-stage  // Advance opportunity stage
POST   /api/opportunities/:id/create-rfq     // Generate RFQ from opportunity
GET    /api/opportunities/:id/activities     // Get opportunity activities
```

### Core Components

#### OpportunityService

```typescript
class OpportunityService {
    async createOpportunity(data: CreateOpportunityDto): Promise<Opportunity>
    async updateOpportunity(id: number, data: UpdateOpportunityDto): Promise<Opportunity>
    async getOpportunity(id: number): Promise<Opportunity>
    async listOpportunities(filters: OpportunityFilters): Promise<PaginatedOpportunities>
    async advanceStage(id: number, newStage: string): Promise<Opportunity>
    async calculateProbability(stage: string): number
    async generateRFQ(opportunityId: number): Promise<RFQ>
    async getPipelineData(filters?: PipelineFilters): Promise<PipelineData>
    async getAnalytics(dateRange: DateRange): Promise<OpportunityAnalytics>
}
```

#### SalesforceIntegrationService

```typescript
class SalesforceIntegrationService {
    async syncOpportunityToSalesforce(opportunity: Opportunity): Promise<string>
    async pullOpportunityFromSalesforce(salesforceId: string): Promise<Opportunity>
    async syncLineItems(opportunityId: number, items: LineItem[]): Promise<void>
    async handleWebhook(payload: SalesforceWebhookPayload): Promise<void>
    async validateConnection(): Promise<boolean>
    async getFieldMappings(): Promise<FieldMapping[]>
}
```

#### WorkflowService

```typescript
class WorkflowService {
    async executeStageChangeWorkflow(opportunity: Opportunity, oldStage: string): Promise<void>
    async sendNotifications(opportunity: Opportunity, event: string): Promise<void>
    async checkEscalationRules(opportunity: Opportunity): Promise<void>
    async applyAssignmentRules(opportunity: Opportunity): Promise<User>
}
```

### Frontend Components

#### OpportunityForm Component

```typescript
interface OpportunityFormProps {
    opportunity?: Opportunity;
    accountId?: number;
    onSave: (data: OpportunityFormData) => void;
    onCancel: () => void;
}

const OpportunityForm: React.FC<OpportunityFormProps> = ({
    opportunity,
    accountId,
    onSave,
    onCancel
}) => {
    // Form implementation with validation
    // Includes all Salesforce-compatible fields
    // Line item management interface
    // Contact assignment functionality
};
```

#### PipelineView Component

```typescript
interface PipelineViewProps {
    opportunities: Opportunity[];
    onStageChange: (opportunityId: number, newStage: string) => void;
    filters: PipelineFilters;
    onFilterChange: (filters: PipelineFilters) => void;
}

const PipelineView: React.FC<PipelineViewProps> = ({
    opportunities,
    onStageChange,
    filters,
    onFilterChange
}) => {
    // Kanban board implementation
    // Drag and drop functionality
    // Stage totals and metrics
    // Responsive design for mobile
};
```

#### OpportunityList Component

```typescript
interface OpportunityListProps {
    opportunities: Opportunity[];
    pagination: PaginationInfo;
    filters: OpportunityFilters;
    onFilterChange: (filters: OpportunityFilters) => void;
    onPageChange: (page: number) => void;
}

const OpportunityList: React.FC<OpportunityListProps> = ({
    opportunities,
    pagination,
    filters,
    onFilterChange,
    onPageChange
}) => {
    // Searchable and filterable table
    // Export functionality
    // Bulk actions
    // Column sorting and customization
};
```

## Data Models

### Opportunity Model

```typescript
interface Opportunity {
    id: number;
    name: string;
    accountId: number;
    amount?: number;
    closeDate?: Date;
    stageName: OpportunityStage;
    probability: number;
    type?: OpportunityType;
    leadSource?: LeadSource;
    isPrivate: boolean;
    description?: string;
    forecastCategory: ForecastCategory;
    nextStep?: string;
    isClosed: boolean;
    isWon: boolean;
    ownerId?: number;
    campaignId?: string;
    hasOpportunityLineItem: boolean;
    items?: LineItem[];
    customFields?: Record<string, any>;
    integrationId?: string;
    status: OpportunityStatus;
    createdAt: Date;
    updatedAt: Date;
    createdById?: number;
    lastModifiedById?: number;
    isDeleted: boolean;
    
    // Relationships
    account: Account;
    owner?: User;
    contacts: Contact[];
    rfqs: RFQ[];
    quotes: Quote[];
    activities: Activity[];
}
```

### Line Item Model

```typescript
interface LineItem {
    id?: string;
    name: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    description?: string;
    productCode?: string;
    category?: string;
    specifications?: Record<string, any>;
}
```

### Pipeline Data Model

```typescript
interface PipelineData {
    stages: PipelineStage[];
    totalValue: number;
    totalCount: number;
    conversionRates: Record<string, number>;
    averageDealSize: number;
    averageSalesCycle: number;
}

interface PipelineStage {
    name: string;
    opportunities: Opportunity[];
    totalValue: number;
    count: number;
    probability: number;
}
```

## Error Handling

### Error Types

```typescript
enum OpportunityErrorType {
    VALIDATION_ERROR = 'VALIDATION_ERROR',
    PERMISSION_DENIED = 'PERMISSION_DENIED',
    INTEGRATION_ERROR = 'INTEGRATION_ERROR',
    WORKFLOW_ERROR = 'WORKFLOW_ERROR',
    NOT_FOUND = 'NOT_FOUND',
    CONFLICT = 'CONFLICT'
}

class OpportunityError extends Error {
    constructor(
        public type: OpportunityErrorType,
        public message: string,
        public details?: any
    ) {
        super(message);
    }
}
```

### Error Handling Strategy

1. **Validation Errors**: Client-side validation with server-side verification
2. **Permission Errors**: Role-based access control with clear error messages
3. **Integration Errors**: Retry mechanisms with exponential backoff
4. **Workflow Errors**: Graceful degradation with manual override options
5. **Sync Errors**: Queue-based retry system with error logging

### Error Recovery

- **Automatic Retry**: For transient integration failures
- **Manual Retry**: For sync operations that require user intervention
- **Rollback**: For failed multi-step operations
- **Notification**: Alert administrators of critical errors
- **Logging**: Comprehensive error logging for debugging

## Testing Strategy

### Unit Testing

- **Service Layer**: Test all business logic and calculations
- **Integration Layer**: Mock external API calls
- **Validation**: Test all input validation rules
- **Workflows**: Test stage transitions and triggers

### Integration Testing

- **API Endpoints**: Test all CRUD operations
- **Database Operations**: Test complex queries and transactions
- **Salesforce Integration**: Test sync operations with sandbox
- **Workflow Execution**: Test end-to-end workflow scenarios

### End-to-End Testing

- **User Workflows**: Test complete user journeys
- **Pipeline Management**: Test kanban functionality
- **RFQ Generation**: Test opportunity to RFQ flow
- **Reporting**: Test analytics and export functionality

### Performance Testing

- **Load Testing**: Test with large datasets (10,000+ opportunities)
- **Concurrent Users**: Test multi-user scenarios
- **Sync Performance**: Test bulk sync operations
- **Query Optimization**: Test complex filtering and sorting

### Test Data Management

```typescript
// Test data factories for consistent testing
class OpportunityTestFactory {
    static createOpportunity(overrides?: Partial<Opportunity>): Opportunity
    static createPipelineData(stageCount: number): PipelineData
    static createLineItems(count: number): LineItem[]
    static createSalesforcePayload(): SalesforceOpportunity
}
```

### Testing Tools

- **Unit Tests**: Jest with TypeScript support
- **Integration Tests**: Supertest for API testing
- **E2E Tests**: Cypress for browser automation
- **Performance Tests**: Artillery for load testing
- **Database Tests**: In-memory PostgreSQL for fast tests

## Security Considerations

### Access Control

- **Role-Based Permissions**: Admin, Manager, Viewer roles
- **Opportunity Ownership**: Owners have full access to their opportunities
- **Private Opportunities**: Restricted visibility based on isPrivate flag
- **Account-Based Security**: Users can only access opportunities for accounts they have permission to view

### Data Protection

- **Encryption**: Sensitive fields encrypted at rest
- **Audit Logging**: All changes tracked with user attribution
- **Data Masking**: PII masked in logs and exports
- **Retention Policies**: Automated cleanup of deleted records

### Integration Security

- **API Authentication**: OAuth 2.0 for Salesforce integration
- **Webhook Validation**: Verify webhook signatures
- **Rate Limiting**: Prevent API abuse
- **IP Whitelisting**: Restrict integration access by IP

### Compliance

- **GDPR**: Right to deletion and data portability
- **SOX**: Audit trails for financial data
- **Industry Standards**: Follow CRM security best practices