# Requirements Document

## Introduction

The Opportunity Management module extends the VendorMS system to include CRM-like functionality for managing potential procurement deals and sales pipelines. This module enables users to track opportunities from initial prospecting through deal closure, integrating seamlessly with existing Account and Contact management while serving as the bridge to RFQ generation, Quote creation, and Invoice processing. The module closely mirrors Salesforce's Opportunity object structure to enable seamless integration and familiar user experience for CRM users.

## Requirements

### Requirement 1

**User Story:** As a Manager, I want to create new opportunities linked to accounts and contacts, so that I can track potential procurement deals and sales pipelines.

#### Acceptance Criteria

1. WHEN a Manager navigates to `/opportunities/create` THEN the system SHALL display an opportunity creation form with all required and optional fields
2. WHEN creating an opportunity THEN the system SHALL require Name, Account, and Close Date fields
3. WH<PERSON> selecting an Account THEN the system SHALL populate available Contacts for assignment
4. <PERSON><PERSON><PERSON> submitting a valid opportunity form THEN the system SHALL create the opportunity record and link it to the specified Account and Contacts
5. WHEN an opportunity is created THEN the system SHALL automatically set the status to 'Open' and assign the current user as owner
6. WHEN an opportunity is created THEN the system SHALL log the creation in the audit trail
7. IF Salesforce integration is enabled THEN the system SHALL sync the new opportunity to Salesforce and store the integration ID

### Requirement 2

**User Story:** As a Manager, I want to edit existing opportunities and advance their stages, so that I can keep deal pipelines current and trigger appropriate workflows.

#### Acceptance Criteria

1. WHEN a Manager accesses `/opportunities/:id/edit` THEN the system SHALL display the opportunity edit form with current values
2. WHEN updating the Stage field THEN the system SHALL automatically recalculate the Probability percentage based on stage mapping
3. WHEN advancing to 'Closed Won' stage THEN the system SHALL set is_won to true and is_closed to true
4. WHEN advancing to 'Closed Lost' stage THEN the system SHALL set is_won to false and is_closed to true
5. WHEN saving opportunity changes THEN the system SHALL validate all required fields and business rules
6. WHEN an opportunity is updated THEN the system SHALL log all changes in the audit trail
7. IF Salesforce integration is enabled THEN the system SHALL sync updates to Salesforce
8. WHEN stage changes affect related RFQs THEN the system SHALL trigger appropriate notifications or cancellations

### Requirement 3

**User Story:** As a user, I want to search and filter opportunities with multiple criteria, so that I can quickly find relevant deals and analyze pipeline performance.

#### Acceptance Criteria

1. WHEN accessing `/opportunities/list` THEN the system SHALL display a searchable and filterable list of opportunities
2. WHEN using search functionality THEN the system SHALL support fuzzy search on Name and Description fields
3. WHEN applying filters THEN the system SHALL support filtering by Stage, Type, Amount Range, Close Date, and Owner
4. WHEN viewing filtered results THEN the system SHALL display aggregate totals including pipeline value
5. WHEN applying filters THEN the system SHALL update the URL to enable shareable filtered views
6. WHEN exporting results THEN the system SHALL generate reports in PDF and CSV formats
7. WHEN loading opportunity lists THEN the system SHALL respond within 1 second for up to 1000 records
8. WHEN viewing opportunities THEN the system SHALL implement pagination for large result sets

### Requirement 4

**User Story:** As a Manager, I want to view opportunities in a visual pipeline format, so that I can better understand deal flow and prioritize activities.

#### Acceptance Criteria

1. WHEN accessing `/opportunities/pipeline` THEN the system SHALL display opportunities in a kanban board grouped by Stage
2. WHEN viewing the pipeline THEN the system SHALL show opportunity cards with key information (Name, Amount, Close Date, Account)
3. WHEN dragging opportunity cards between stages THEN the system SHALL update the opportunity stage and recalculate probability
4. WHEN viewing pipeline totals THEN the system SHALL display aggregate amounts for each stage
5. WHEN filtering the pipeline THEN the system SHALL maintain filter state across stage columns
6. WHEN viewing overdue opportunities THEN the system SHALL highlight opportunities past their close date
7. WHEN accessing pipeline on mobile devices THEN the system SHALL provide a responsive layout suitable for touch interaction

### Requirement 5

**User Story:** As a Manager, I want to generate RFQs directly from opportunities, so that I can seamlessly transition from deal identification to vendor sourcing.

#### Acceptance Criteria

1. WHEN viewing an opportunity with items defined THEN the system SHALL display a "Create RFQ" action button
2. WHEN creating an RFQ from an opportunity THEN the system SHALL auto-populate RFQ items from the opportunity's items field
3. WHEN generating an RFQ THEN the system SHALL link the RFQ back to the source opportunity
4. WHEN an RFQ is created from an opportunity THEN the system SHALL update the opportunity stage to 'Sourcing'
5. WHEN RFQ submissions are received THEN the system SHALL enable quote generation from the opportunity context
6. WHEN viewing an opportunity THEN the system SHALL display all linked RFQs and their current status
7. WHEN an RFQ is completed THEN the system SHALL provide options to advance the opportunity stage appropriately

### Requirement 6

**User Story:** As an Admin, I want to configure Salesforce integration for opportunities, so that deal data stays synchronized between VendorMS and Salesforce.

#### Acceptance Criteria

1. WHEN configuring Salesforce integration THEN the system SHALL validate connection credentials and permissions
2. WHEN syncing opportunities to Salesforce THEN the system SHALL map VMS fields to corresponding Salesforce Opportunity fields
3. WHEN pulling updates from Salesforce THEN the system SHALL handle field conflicts with configurable priority rules
4. WHEN syncing line items THEN the system SHALL convert JSONB items to OpportunityLineItem records in Salesforce
5. WHEN sync operations fail THEN the system SHALL log errors and provide retry mechanisms
6. WHEN opportunities are created or updated THEN the system SHALL sync changes within 5 minutes
7. WHEN viewing opportunities THEN the system SHALL indicate sync status and last sync timestamp

### Requirement 7

**User Story:** As a user, I want to manage opportunity line items and products, so that I can specify detailed requirements for procurement deals.

#### Acceptance Criteria

1. WHEN creating or editing opportunities THEN the system SHALL provide an interface to add, edit, and remove line items
2. WHEN adding line items THEN the system SHALL capture Name, Quantity, Unit Price, and Description
3. WHEN line items are present THEN the system SHALL set has_opportunity_line_item to true
4. WHEN calculating opportunity amounts THEN the system SHALL optionally auto-calculate from line item totals
5. WHEN viewing opportunities THEN the system SHALL display line items in a structured table format
6. WHEN exporting opportunities THEN the system SHALL include line item details in reports
7. WHEN generating RFQs THEN the system SHALL use line items as the basis for RFQ item specifications

### Requirement 8

**User Story:** As a user, I want to track opportunity ownership and collaboration, so that team members can coordinate on deals effectively.

#### Acceptance Criteria

1. WHEN creating opportunities THEN the system SHALL assign the current user as the default owner
2. WHEN reassigning opportunity ownership THEN the system SHALL validate the new owner has appropriate permissions
3. WHEN opportunity ownership changes THEN the system SHALL notify both old and new owners
4. WHEN viewing opportunities THEN the system SHALL display owner information and contact details
5. WHEN opportunities are private THEN the system SHALL restrict visibility to owner and authorized users only
6. WHEN collaborating on opportunities THEN the system SHALL support comments and activity tracking
7. WHEN opportunities are shared THEN the system SHALL maintain audit trails of access and modifications

### Requirement 9

**User Story:** As a Manager, I want to set up automated workflows based on opportunity stage changes, so that appropriate actions are triggered without manual intervention.

#### Acceptance Criteria

1. WHEN opportunity stages advance THEN the system SHALL trigger configured workflow rules
2. WHEN opportunities reach 'Proposal' stage THEN the system SHALL notify assigned contacts automatically
3. WHEN opportunities are marked 'Closed Won' THEN the system SHALL initiate quote and invoice generation workflows
4. WHEN opportunities are overdue THEN the system SHALL send escalation notifications to owners and managers
5. WHEN forecast categories change THEN the system SHALL update pipeline reports and dashboards
6. WHEN opportunities are created THEN the system SHALL apply default assignment rules based on account territory or industry
7. WHEN workflow actions fail THEN the system SHALL log errors and provide manual override options

### Requirement 10

**User Story:** As a user, I want to generate reports and analytics on opportunity performance, so that I can analyze sales effectiveness and forecast accuracy.

#### Acceptance Criteria

1. WHEN accessing opportunity reports THEN the system SHALL provide pre-built reports for pipeline analysis, win rates, and forecasting
2. WHEN generating pipeline reports THEN the system SHALL show opportunity distribution across stages with trend analysis
3. WHEN calculating win rates THEN the system SHALL provide metrics by owner, account, industry, and time period
4. WHEN forecasting THEN the system SHALL use probability-weighted amounts for pipeline projections
5. WHEN viewing analytics THEN the system SHALL provide interactive charts and drill-down capabilities
6. WHEN exporting analytics THEN the system SHALL support multiple formats including PDF, Excel, and CSV
7. WHEN scheduling reports THEN the system SHALL enable automated delivery via email on configurable intervals
8. WHEN comparing periods THEN the system SHALL provide year-over-year and quarter-over-quarter analysis