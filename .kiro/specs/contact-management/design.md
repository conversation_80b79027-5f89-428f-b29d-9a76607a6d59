# Design Document

## Overview

The Contact Management module provides comprehensive individual contact management within the VendorMS ecosystem. The design closely mirrors Salesforce's Contact object structure to enable seamless integration while adding VMS-specific enhancements for procurement workflows. The module supports hierarchical relationships, multi-channel communication, compliance features, and real-time synchronization with external CRM systems.

The architecture follows VMS's established patterns using React.js frontend, Node.js/Express backend, and PostgreSQL database with Prisma ORM. The module integrates with existing Account management and provides foundation services for Opportunities, RFQs, and Quote workflows.

## Architecture

### System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        CF[Contact Forms]
        CL[Contact List]
        OC[Org Chart]
        CS[Contact Search]
    end
    
    subgraph "Backend Layer"
        CC[Contact Controller]
        CS_SVC[Contact Service]
        SF_SVC[Salesforce Service]
        VAL[Validation Service]
    end
    
    subgraph "Data Layer"
        DB[(PostgreSQL)]
        SF[Salesforce API]
        CACHE[Redis Cache]
    end
    
    CF --> CC
    CL --> CC
    OC --> CC
    CS --> CC
    
    CC --> CS_SVC
    CC --> VAL
    CS_SVC --> SF_SVC
    CS_SVC --> DB
    SF_SVC --> SF
    CS_SVC --> CACHE
```

### Integration Architecture

```mermaid
graph LR
    subgraph "VMS Core"
        CM[Contact Module]
        AM[Account Module]
        OM[Opportunity Module]
        QM[Quote Module]
    end
    
    subgraph "External Systems"
        SF[Salesforce]
        EMAIL[Email Service]
        SMS[SMS Service]
    end
    
    AM --> CM
    CM --> OM
    CM --> QM
    CM <--> SF
    CM --> EMAIL
    CM --> SMS
```

## Components and Interfaces

### Database Schema

```typescript
// Prisma Schema for Contact
model Contact {
  id                     Int       @id @default(autoincrement())
  account_id             Int       // Required FK to Account
  first_name             String?
  last_name              String    // Required
  salutation             String?   // Enum: Mr, Ms, Dr, Prof, etc.
  title                  String?
  department             String?
  phone                  String?
  mobile_phone           String?
  home_phone             String?
  other_phone            String?
  fax                    String?
  email                  String    @unique // Required, unique per tenant
  mailing_address        Json?     // Structured address object
  other_address          Json?     // Secondary address
  reports_to_id          Int?      // Self-referencing FK
  assistant_name         String?
  assistant_phone        String?
  birthdate              DateTime? @db.Date
  lead_source            String?   // Enum: Web, Phone, Partner, etc.
  description            Text?
  languages              String?   // JSON array or comma-separated
  level                  String?   // Enum: Primary, Secondary, Tertiary
  do_not_call            Boolean   @default(false)
  has_opted_out_of_email Boolean   @default(false)
  owner_id               Int?      // FK to User
  integration_id         String?   // Salesforce ID
  custom_fields          Json?     // Flexible custom data
  status                 StatusEnum @default(ACTIVE)
  created_at             DateTime  @default(now())
  updated_at             DateTime  @updatedAt
  created_by_id          Int?
  last_modified_by_id    Int?
  is_deleted             Boolean   @default(false)

  // Relations
  account                Account   @relation(fields: [account_id], references: [id], onDelete: Cascade)
  reports_to             Contact?  @relation("ContactHierarchy", fields: [reports_to_id], references: [id])
  reports                Contact[] @relation("ContactHierarchy")
  owner                  User?     @relation("ContactOwner", fields: [owner_id], references: [id])
  created_by             User?     @relation("CreatedBy", fields: [created_by_id], references: [id])
  last_modified_by       User?     @relation("LastModifiedBy", fields: [last_modified_by_id], references: [id])
  
  @@index([account_id])
  @@index([last_name])
  @@index([email])
  @@index([department])
  @@index([reports_to_id])
}
```

### API Interfaces

```typescript
// Contact API Interfaces
interface ContactCreateRequest {
  account_id: number;
  first_name?: string;
  last_name: string;
  salutation?: string;
  title?: string;
  department?: string;
  phone?: string;
  mobile_phone?: string;
  home_phone?: string;
  other_phone?: string;
  fax?: string;
  email: string;
  mailing_address?: Address;
  other_address?: Address;
  reports_to_id?: number;
  assistant_name?: string;
  assistant_phone?: string;
  birthdate?: string;
  lead_source?: string;
  description?: string;
  languages?: string[];
  level?: string;
  do_not_call?: boolean;
  has_opted_out_of_email?: boolean;
  custom_fields?: Record<string, any>;
}

interface Address {
  street?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
}

interface ContactResponse {
  id: number;
  account_id: number;
  full_name: string; // Computed field
  first_name?: string;
  last_name: string;
  salutation?: string;
  title?: string;
  department?: string;
  phone?: string;
  mobile_phone?: string;
  home_phone?: string;
  other_phone?: string;
  fax?: string;
  email: string;
  mailing_address?: Address;
  other_address?: Address;
  reports_to?: ContactSummary;
  direct_reports?: ContactSummary[];
  assistant_name?: string;
  assistant_phone?: string;
  birthdate?: string;
  lead_source?: string;
  description?: string;
  languages?: string[];
  level?: string;
  do_not_call: boolean;
  has_opted_out_of_email: boolean;
  owner?: UserSummary;
  integration_id?: string;
  custom_fields?: Record<string, any>;
  status: string;
  created_at: string;
  updated_at: string;
  created_by?: UserSummary;
  last_modified_by?: UserSummary;
}

interface ContactSearchRequest {
  account_id?: number;
  department?: string;
  level?: string;
  lead_source?: string;
  search?: string; // Fuzzy search on name, email, title
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}
```

### Frontend Components

```typescript
// React Component Interfaces
interface ContactFormProps {
  contact?: Contact;
  accountId?: number;
  onSave: (contact: ContactCreateRequest) => Promise<void>;
  onCancel: () => void;
  mode: 'create' | 'edit';
}

interface ContactListProps {
  accountId?: number;
  filters?: ContactSearchRequest;
  onContactSelect: (contact: Contact) => void;
  onContactEdit: (contact: Contact) => void;
  onContactDelete: (contactId: number) => void;
}

interface OrgChartProps {
  rootContactId?: number;
  accountId?: number;
  onContactClick: (contact: Contact) => void;
  maxDepth?: number;
}

interface ContactSearchProps {
  onSearch: (filters: ContactSearchRequest) => void;
  initialFilters?: ContactSearchRequest;
  availableAccounts?: Account[];
}
```

## Data Models

### Core Contact Model

The Contact model serves as the central entity with the following key characteristics:

- **Identity**: Unique ID with tenant isolation
- **Account Relationship**: Required many-to-one relationship with Account
- **Personal Information**: Name components, salutation, contact details
- **Professional Information**: Title, department, reporting relationships
- **Communication Channels**: Multiple phone numbers, email, addresses
- **Preferences**: Language, communication opt-outs, custom fields
- **Integration**: Salesforce sync capabilities with integration_id
- **Audit Trail**: Creation, modification tracking with user attribution

### Address Model

Addresses are stored as JSONB for flexibility:

```typescript
interface Address {
  street?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
}
```

### Hierarchy Model

Contact hierarchies use self-referencing relationships:

```typescript
interface ContactHierarchy {
  contact_id: number;
  reports_to_id?: number;
  depth: number;
  path: number[]; // Array of contact IDs from root to current
}
```

## Error Handling

### Validation Errors

```typescript
interface ValidationError {
  field: string;
  message: string;
  code: string;
}

// Common validation scenarios
const ValidationRules = {
  email: {
    required: true,
    format: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    unique: true
  },
  last_name: {
    required: true,
    maxLength: 255
  },
  account_id: {
    required: true,
    exists: true
  },
  reports_to_id: {
    noCycles: true,
    sameAccount: true
  }
};
```

### Sync Errors

```typescript
interface SyncError {
  contact_id: number;
  operation: 'create' | 'update' | 'delete';
  error_type: 'validation' | 'network' | 'mapping' | 'conflict';
  error_message: string;
  retry_count: number;
  last_attempt: string;
}
```

### Error Recovery

- **Validation Errors**: Return detailed field-level errors with suggestions
- **Sync Failures**: Implement exponential backoff retry with manual resolution options
- **Hierarchy Cycles**: Prevent creation and provide alternative suggestions
- **Duplicate Emails**: Offer merge options or tenant-scoped uniqueness
- **Network Issues**: Queue operations for retry with user notification

## Testing Strategy

### Unit Testing

```typescript
// Contact Service Tests
describe('ContactService', () => {
  describe('createContact', () => {
    it('should create contact with required fields', async () => {
      const contactData = {
        account_id: 1,
        last_name: 'Doe',
        email: '<EMAIL>'
      };
      const result = await contactService.create(contactData);
      expect(result.id).toBeDefined();
      expect(result.full_name).toBe('Doe');
    });

    it('should validate email uniqueness', async () => {
      const contactData = {
        account_id: 1,
        last_name: 'Doe',
        email: '<EMAIL>'
      };
      await expect(contactService.create(contactData))
        .rejects.toThrow('Email already exists');
    });

    it('should prevent hierarchy cycles', async () => {
      const contactData = {
        account_id: 1,
        last_name: 'Manager',
        email: '<EMAIL>',
        reports_to_id: 2 // Contact that reports to this contact
      };
      await expect(contactService.create(contactData))
        .rejects.toThrow('Circular hierarchy detected');
    });
  });

  describe('syncToSalesforce', () => {
    it('should map VMS fields to Salesforce fields', async () => {
      const contact = createMockContact();
      const sfContact = await salesforceService.mapToSalesforce(contact);
      expect(sfContact.FirstName).toBe(contact.first_name);
      expect(sfContact.LastName).toBe(contact.last_name);
      expect(sfContact.AccountId).toBe(contact.account.integration_id);
    });
  });
});
```

### Integration Testing

```typescript
// API Integration Tests
describe('Contact API', () => {
  describe('POST /api/contacts', () => {
    it('should create contact and sync to Salesforce', async () => {
      const response = await request(app)
        .post('/api/contacts')
        .send(validContactData)
        .expect(201);
      
      expect(response.body.integration_id).toBeDefined();
      
      // Verify Salesforce sync
      const sfContact = await salesforce.getContact(response.body.integration_id);
      expect(sfContact.Email).toBe(validContactData.email);
    });
  });

  describe('GET /api/contacts/search', () => {
    it('should return filtered results', async () => {
      const response = await request(app)
        .get('/api/contacts/search')
        .query({ department: 'Sales', level: 'Primary' })
        .expect(200);
      
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.pagination).toBeDefined();
    });
  });
});
```

### End-to-End Testing

```typescript
// E2E Test Scenarios
describe('Contact Management E2E', () => {
  it('should complete full contact lifecycle', async () => {
    // 1. Create account
    const account = await createTestAccount();
    
    // 2. Create contact
    await page.goto(`/contacts/create?accountId=${account.id}`);
    await page.fill('[data-testid="first-name"]', 'John');
    await page.fill('[data-testid="last-name"]', 'Doe');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.click('[data-testid="save-contact"]');
    
    // 3. Verify creation
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    // 4. Edit contact
    await page.click('[data-testid="edit-contact"]');
    await page.fill('[data-testid="title"]', 'CEO');
    await page.click('[data-testid="save-contact"]');
    
    // 5. Verify hierarchy
    await page.goto('/contacts/org-chart');
    await expect(page.locator('[data-testid="contact-node"]')).toContainText('John Doe');
    
    // 6. Search and filter
    await page.goto('/contacts');
    await page.fill('[data-testid="search-input"]', 'john');
    await expect(page.locator('[data-testid="contact-row"]')).toHaveCount(1);
  });
});
```

### Performance Testing

- **Load Testing**: 1000 concurrent contact searches should complete in <2 seconds
- **Sync Performance**: Salesforce sync should complete in <5 seconds for single contact
- **Hierarchy Queries**: Org chart with 500 contacts should render in <3 seconds
- **Search Performance**: Fuzzy search across 10,000 contacts should return results in <1 second

### Security Testing

- **Access Control**: Verify tenant isolation and role-based permissions
- **Data Encryption**: Ensure sensitive fields are encrypted at rest
- **Input Validation**: Test SQL injection, XSS, and malformed data handling
- **Audit Trail**: Verify all operations are logged with proper attribution