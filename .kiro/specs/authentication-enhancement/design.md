# Design Document

## Overview

This design document outlines the implementation of enhanced authentication pages for VendorMS, including user registration, password recovery, email verification, and related flows. The design maintains complete consistency with the existing neumorphic design system while extending the current Redux-based authentication architecture.

The implementation leverages the existing design patterns from the Login component, including the neumorphic styling, form validation with React Hook Form and Yup, Framer Motion animations, and the established color scheme and component structure. All new pages will integrate seamlessly with the current authentication state management and routing system.

## Architecture

### Component Architecture

The authentication enhancement follows the existing component patterns:

```
src/pages/
├── Login.tsx (existing)
├── Register.tsx (new)
├── ForgotPassword.tsx (new)
├── ResetPassword.tsx (new)
├── VerifyEmail.tsx (new)
└── VerificationPending.tsx (new)

src/store/slices/
└── authSlice.ts (enhanced with new async thunks)

src/hooks/
└── useAuth.ts (enhanced with new methods)
```

### State Management Architecture

The existing Redux Toolkit architecture will be extended with new async thunks:

- `registerAsync`: Handle user registration
- `forgotPasswordAsync`: Send password reset email
- `resetPasswordAsync`: Reset password with token
- `verifyEmailAsync`: Verify email with token
- `resendVerificationAsync`: Resend verification email

### Routing Architecture

The authentication routes will be updated in App.tsx:

```typescript
// Public Routes (unauthenticated users)
<Route path="/login" element={<Login />} />
<Route path="/register" element={<Register />} />
<Route path="/forgot-password" element={<ForgotPassword />} />
<Route path="/reset-password" element={<ResetPassword />} />
<Route path="/verify-email" element={<VerifyEmail />} />
<Route path="/verification-pending" element={<VerificationPending />} />
```

## Components and Interfaces

### 1. Register Component

**Purpose**: User registration with email verification flow

**Design Pattern**: Follows Login.tsx structure with additional fields

**Key Features**:
- Form fields: email, password, confirmPassword, fullName, organization
- Real-time password strength validation
- Terms of service acceptance checkbox
- Consistent neumorphic styling and animations
- Integration with existing toast notification system

**Form Validation Schema**:
```typescript
const registerSchema = yup.object({
  email: yup.string().email('Invalid email').required('Email is required'),
  password: yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number')
    .required('Password is required'),
  confirmPassword: yup.string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
  fullName: yup.string().required('Full name is required'),
  organization: yup.string().required('Organization is required'),
  acceptTerms: yup.boolean().oneOf([true], 'You must accept the terms of service')
});
```

**UI Layout**:
- Same card-neumorphic container as Login
- VendorMS logo and branding
- Form with neumorphic input styling
- Password strength indicator
- Navigation links to login page
- Loading states and error handling

### 2. ForgotPassword Component

**Purpose**: Password reset request flow

**Design Pattern**: Simplified single-field form following Login patterns

**Key Features**:
- Single email input field
- Clear instructions and feedback
- Success state showing next steps
- Link back to login page
- Same visual styling as Login

**Form Validation Schema**:
```typescript
const forgotPasswordSchema = yup.object({
  email: yup.string().email('Invalid email').required('Email is required')
});
```

**UI States**:
- Initial form state
- Loading state during email sending
- Success state with instructions
- Error state with retry option

### 3. ResetPassword Component

**Purpose**: Password reset with token validation

**Design Pattern**: Password-focused form with token handling

**Key Features**:
- Token validation on component mount
- New password and confirm password fields
- Password strength validation
- Automatic redirect to login on success
- Token expiry handling

**Form Validation Schema**:
```typescript
const resetPasswordSchema = yup.object({
  password: yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number')
    .required('Password is required'),
  confirmPassword: yup.string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password')
});
```

### 4. VerifyEmail Component

**Purpose**: Email verification with token processing

**Design Pattern**: Status display component with minimal interaction

**Key Features**:
- Automatic token verification on mount
- Success/error status display
- Resend verification option
- Navigation to login on success
- Clear error messaging

**UI States**:
- Loading verification state
- Success state with login link
- Error state with resend option
- Token expired state

### 5. VerificationPending Component

**Purpose**: Post-registration guidance page

**Design Pattern**: Informational page with action options

**Key Features**:
- Clear instructions about email verification
- Resend verification email option
- Link to change email address
- Consistent branding and styling

## Data Models

### Enhanced User Interface

```typescript
export interface User {
  id: number;
  email: string;
  role: 'admin' | 'manager' | 'viewer';
  is_verified: boolean;
  full_name?: string;
  organization?: string;
  preferences: {
    lang: string;
    currency: string;
    theme: string;
  };
  consent_given: boolean;
  created_at: string;
  updated_at: string;
}
```

### Registration Data Interface

```typescript
export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  organization: string;
  acceptTerms: boolean;
}
```

### Password Reset Interfaces

```typescript
export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  token: string;
  password: string;
  confirmPassword: string;
}
```

### Enhanced Auth State

```typescript
interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  verificationStatus: 'pending' | 'verified' | 'expired' | null;
  resetTokenValid: boolean;
}
```

## Error Handling

### Error Categories

1. **Validation Errors**: Client-side form validation using Yup schemas
2. **Network Errors**: API communication failures with retry mechanisms
3. **Authentication Errors**: Invalid credentials, expired tokens, etc.
4. **Verification Errors**: Invalid or expired verification tokens

### Error Display Strategy

- Use existing toast notification system for success/error messages
- Inline form validation errors below input fields
- Consistent error styling with destructive color scheme
- Clear, actionable error messages with next steps

### Error Recovery Patterns

- Automatic retry for network failures
- Resend options for email-based flows
- Clear navigation back to working states
- Graceful degradation for edge cases

## Testing Strategy

### Unit Testing

- Component rendering and interaction tests using React Testing Library
- Form validation testing with various input scenarios
- Redux slice testing for all new async thunks
- Hook testing for enhanced useAuth functionality

### Integration Testing

- End-to-end authentication flows using Cypress
- Form submission and validation workflows
- Navigation between authentication pages
- Error handling and recovery scenarios

### Visual Testing

- Consistent styling across all authentication pages
- Responsive design testing on various screen sizes
- Animation and transition testing
- Accessibility compliance testing

### Mock Data Strategy

The implementation will extend the existing mock authentication system:

```typescript
// Enhanced mock users with verification status
const mockUsers: Record<string, User> = {
  '<EMAIL>': {
    // existing fields...
    full_name: 'System Administrator',
    organization: 'VendorMS Corp',
    is_verified: true
  },
  // Additional mock users for testing
};

// Mock email service for development
const mockEmailService = {
  sendVerificationEmail: (email: string, token: string) => Promise.resolve(),
  sendPasswordResetEmail: (email: string, token: string) => Promise.resolve()
};
```

## Security Considerations

### Password Security

- Minimum 8 characters with complexity requirements
- Client-side strength validation with visual feedback
- Secure password hashing (bcrypt) on backend
- Password confirmation validation

### Token Security

- JWT tokens with appropriate expiration times
- Secure token storage in localStorage
- Token validation on protected routes
- Automatic token refresh mechanisms

### Email Security

- Verification tokens with limited lifespan (24 hours)
- Reset tokens with short expiration (1 hour)
- Rate limiting on email sending endpoints
- Secure token generation using crypto libraries

### Input Validation

- Comprehensive client-side validation using Yup
- Server-side validation for all inputs
- XSS prevention through proper input sanitization
- SQL injection prevention through parameterized queries

## Performance Considerations

### Code Splitting

- Lazy loading of authentication pages
- Separate bundles for authentication flows
- Optimized bundle sizes for faster loading

### Caching Strategy

- Form state persistence during navigation
- Optimistic UI updates for better UX
- Efficient Redux state management
- Minimal re-renders through proper memoization

### Network Optimization

- Debounced validation for real-time feedback
- Efficient API calls with proper error handling
- Retry mechanisms for failed requests
- Progressive enhancement for offline scenarios

## Accessibility

### WCAG Compliance

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support

### Form Accessibility

- Clear form labels and instructions
- Error message association with form fields
- Focus management during form submission
- Accessible loading states and feedback

## Mobile Responsiveness

### Design Adaptations

- Touch-friendly input fields and buttons
- Optimized form layouts for mobile screens
- Consistent spacing and typography scaling
- Gesture-friendly navigation patterns

### Performance on Mobile

- Optimized bundle sizes for mobile networks
- Efficient rendering for lower-powered devices
- Progressive loading strategies
- Offline capability considerations