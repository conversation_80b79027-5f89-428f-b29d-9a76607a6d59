# Design Document

## Overview

The Workflows & Automation module provides a comprehensive interface for creating, managing, and monitoring automated business processes within VendorMS. The design follows the established neumorphism theme and maintains consistency with existing pages while introducing specialized components for workflow visualization and management.

The module consists of two primary pages:
1. **All Workflows** (`/workflows`) - A list view for managing existing workflows
2. **Create Workflow** (`/workflows/create`) - A visual workflow builder interface

## Architecture

### Component Structure
```
src/pages/
├── WorkflowsList.tsx          # Main workflows listing page
├── WorkflowCreate.tsx         # Workflow creation/editing page
└── WorkflowView.tsx           # Workflow details/execution view

src/components/workflows/
├── WorkflowCard.tsx           # Individual workflow display card
├── WorkflowBuilder.tsx        # Visual workflow builder component
├── WorkflowStep.tsx           # Individual workflow step component
├── StepConfigPanel.tsx        # Step configuration sidebar
├── WorkflowCanvas.tsx         # Drag-and-drop canvas area
├── TriggerSelector.tsx        # Trigger type selection component
├── ApprovalStepConfig.tsx     # Approval step configuration
├── NotificationStepConfig.tsx # Notification step configuration
├── IntegrationStepConfig.tsx  # Integration step configuration
├── ConditionStepConfig.tsx    # Condition/branching step configuration
└── WorkflowExecutionLog.tsx   # Execution history and logs
```

### State Management
```typescript
// Redux slice: workflowsSlice.ts
interface WorkflowsState {
  workflows: Workflow[];
  currentWorkflow: Workflow | null;
  isLoading: boolean;
  error: string | null;
  filters: {
    search: string;
    status: 'all' | 'active' | 'inactive' | 'draft';
    trigger: string;
  };
  executionLogs: WorkflowExecution[];
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'draft';
  trigger: WorkflowTrigger;
  steps: WorkflowStep[];
  created_at: string;
  updated_at: string;
  last_run: string | null;
  success_rate: number;
  execution_count: number;
}

interface WorkflowStep {
  id: string;
  type: 'approval' | 'notification' | 'integration' | 'condition' | 'delay';
  name: string;
  config: StepConfig;
  position: { x: number; y: number };
  connections: string[]; // Connected step IDs
}
```

## Components and Interfaces

### 1. WorkflowsList Page (`/workflows`)

**Layout Structure:**
- Header with title, description, and "Create Workflow" button (Admin only)
- Search and filter bar with neumorphic styling
- Statistics cards showing workflow metrics
- Grid/list view of workflow cards
- Empty state for no workflows

**Key Features:**
- Real-time search filtering
- Status-based filtering (Active, Inactive, Draft)
- Trigger type filtering
- Workflow activation/deactivation toggles
- Quick actions menu (View, Edit, Delete, Duplicate)
- Performance metrics display (success rate, last run, execution count)

**Component Implementation:**
```typescript
export const WorkflowsList: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { canEdit } = useAuth();
  const { workflows, isLoading, filters } = useSelector((state: RootState) => state.workflows);

  // Search and filtering logic
  // Workflow cards rendering
  // Statistics calculation
  // Role-based action buttons
}
```

### 2. WorkflowCreate Page (`/workflows/create`)

**Layout Structure:**
- Header with workflow name input and save/cancel actions
- Left sidebar with step palette and configuration panel
- Main canvas area for visual workflow building
- Right sidebar with workflow settings and testing tools
- Bottom panel for execution logs during testing

**Key Features:**
- Drag-and-drop workflow builder
- Visual step connections with flow lines
- Real-time step configuration
- Workflow validation and error highlighting
- Test mode for workflow simulation
- Template selection for common workflows
- Auto-save functionality

**Visual Workflow Builder:**
```typescript
export const WorkflowBuilder: React.FC = () => {
  const [steps, setSteps] = useState<WorkflowStep[]>([]);
  const [selectedStep, setSelectedStep] = useState<string | null>(null);
  const [connections, setConnections] = useState<Connection[]>([]);

  // Drag and drop handlers
  // Step connection logic
  // Canvas interaction
  // Step configuration management
}
```

### 3. Step Configuration Components

**Approval Step Configuration:**
- Approver role selection (Admin, Manager, specific users)
- Timeout settings with escalation rules
- Parallel vs sequential approval options
- Custom approval criteria

**Notification Step Configuration:**
- Recipient selection (roles, specific users, external emails)
- Message template editor with variables
- Delivery method selection (Email, SMS, In-app)
- Scheduling options

**Integration Step Configuration:**
- Integration type selection (QuickBooks, Salesforce, Custom API)
- Credential management with secure storage
- Data mapping configuration
- Error handling settings

**Condition Step Configuration:**
- Field selection from available data
- Comparison operators (equals, greater than, contains, etc.)
- Multiple condition logic (AND, OR)
- Branch path configuration

## Data Models

### Workflow Triggers
```typescript
interface WorkflowTrigger {
  type: 'vendor_onboarded' | 'contract_expiring' | 'invoice_approved' | 'performance_score_changed' | 'manual';
  config: {
    conditions?: TriggerCondition[];
    schedule?: CronExpression; // For scheduled triggers
  };
}

interface TriggerCondition {
  field: string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
  value: any;
}
```

### Step Configurations
```typescript
interface ApprovalStepConfig {
  approvers: {
    type: 'role' | 'user';
    value: string;
  }[];
  timeout_hours: number;
  escalation: {
    enabled: boolean;
    escalate_to: string;
    after_hours: number;
  };
  approval_type: 'any' | 'all' | 'majority';
}

interface NotificationStepConfig {
  recipients: {
    type: 'role' | 'user' | 'email';
    value: string;
  }[];
  template: {
    subject: string;
    body: string;
    variables: string[];
  };
  delivery_method: 'email' | 'sms' | 'in_app';
  delay_minutes?: number;
}

interface IntegrationStepConfig {
  integration_type: 'quickbooks' | 'salesforce' | 'custom_api';
  action: string;
  data_mapping: {
    source_field: string;
    target_field: string;
  }[];
  error_handling: 'continue' | 'stop' | 'retry';
  retry_count?: number;
}
```

## Error Handling

### Workflow Validation
- Step connectivity validation (no orphaned steps)
- Required field validation for each step type
- Circular dependency detection
- Permission validation for approvers and integrations

### Runtime Error Handling
- Step execution failure recovery
- Integration timeout handling
- Approval timeout and escalation
- Notification delivery failure handling

### User Feedback
- Real-time validation messages in workflow builder
- Execution error logs with detailed information
- Success/failure notifications for workflow actions
- Performance warnings for complex workflows

## Testing Strategy

### Unit Testing
- Individual step configuration components
- Workflow validation logic
- State management actions and reducers
- Utility functions for workflow execution

### Integration Testing
- Workflow builder drag-and-drop functionality
- Step connection and validation
- API integration for workflow CRUD operations
- Real workflow execution with mock data

### End-to-End Testing
- Complete workflow creation process
- Workflow execution from trigger to completion
- Role-based access control validation
- Cross-browser compatibility testing

## Performance Considerations

### Optimization Strategies
- Lazy loading of workflow execution logs
- Virtualized rendering for large workflow lists
- Debounced search and filtering
- Memoized workflow validation calculations

### Scalability
- Pagination for workflow lists
- Background processing for workflow execution
- Caching of frequently accessed workflow data
- Efficient database queries with proper indexing

## Security Considerations

### Access Control
- Role-based workflow creation and editing permissions
- Secure credential storage for integrations
- Audit logging for all workflow modifications
- User authentication for workflow execution actions

### Data Protection
- Encryption of sensitive workflow configuration data
- Secure API communication for integrations
- Input validation and sanitization
- Protection against workflow injection attacks

## UI/UX Design Patterns

### Consistency with VendorMS Theme
- Neumorphic card designs for workflow items
- Consistent color scheme and typography
- Smooth animations and micro-interactions
- Responsive design for mobile and desktop

### Visual Workflow Builder
- Intuitive drag-and-drop interface
- Clear visual connections between steps
- Color-coded step types for easy identification
- Contextual tooltips and help text

### User Experience Enhancements
- Auto-save functionality to prevent data loss
- Undo/redo capabilities in workflow builder
- Keyboard shortcuts for power users
- Progressive disclosure of advanced features

## Accessibility

### WCAG Compliance
- Keyboard navigation support for workflow builder
- Screen reader compatibility for all components
- High contrast mode support
- Focus management for modal dialogs

### Usability Features
- Clear error messages and validation feedback
- Consistent navigation patterns
- Logical tab order for form elements
- Alternative text for visual workflow elements