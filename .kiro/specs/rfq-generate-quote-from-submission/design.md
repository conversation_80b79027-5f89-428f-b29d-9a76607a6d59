# RFQ Quote Generation from Submissions - Design

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │Quote Builder│ │◄──►│ │Quote Service│ │◄──►│ │PostgreSQL   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │Item Selector│ │◄──►│ │PDF Generator│ │    │ │Audit Tables │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │                 │
│ │Client Portal│ │◄──►│ │Email Service│ │    │                 │
│ └─────────────┘ │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Database Schema Design

### Enhanced Client Quotes Table
```sql
-- Extend existing client_quotes table
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS item_selections JSONB DEFAULT '{}'::JSONB;
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS commission_structure JSONB DEFAULT '{}'::JSONB;
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS quote_template_id INTEGER;
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS revision_number INTEGER DEFAULT 1;
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS parent_quote_id INTEGER REFERENCES client_quotes(id);

COMMENT ON COLUMN client_quotes.item_selections IS 'Detailed item-level selections: {itemId: {submissionId, vendorId, unitPrice, quantity, commission}}';
COMMENT ON COLUMN client_quotes.commission_structure IS 'Commission settings: {global: {type, value}, items: {itemId: {type, value}}}';
```

### Quote Templates Table
```sql
CREATE TABLE quote_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_data JSONB NOT NULL, -- {layout, styling, sections, branding}
    is_default BOOLEAN DEFAULT FALSE,
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE quote_templates IS 'Customizable quote templates for PDF generation';
```

### Quote Interactions Table
```sql
CREATE TABLE quote_interactions (
    id SERIAL PRIMARY KEY,
    quote_id INTEGER NOT NULL REFERENCES client_quotes(id) ON DELETE CASCADE,
    interaction_type VARCHAR(50) NOT NULL, -- 'viewed', 'downloaded', 'approved', 'rejected', 'commented'
    client_ip VARCHAR(45),
    user_agent TEXT,
    interaction_data JSONB, -- Additional context data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE quote_interactions IS 'Track client interactions with quotes';
```

### Indexes for Performance
```sql
-- Performance indexes
CREATE INDEX idx_client_quotes_rfq_id ON client_quotes(rfq_id);
CREATE INDEX idx_client_quotes_status ON client_quotes(status);
CREATE INDEX idx_client_quotes_item_selections ON client_quotes USING GIN(item_selections);
CREATE INDEX idx_quote_interactions_quote_id ON quote_interactions(quote_id);
CREATE INDEX idx_quote_interactions_type ON quote_interactions(interaction_type);
CREATE INDEX idx_quote_templates_default ON quote_templates(is_default) WHERE is_default = true;
```

## API Design

### Quote Generation Endpoints

#### Get Submission Data for Quote Generation
```typescript
GET /api/rfqs/:rfqId/submissions/for-quote

Response: {
  rfq: {
    id: number,
    title: string,
    description: string,
    items: RFQItem[],
    status: string,
    deadline: string
  },
  submissions: {
    id: number,
    vendor: VendorInfo,
    bid_items: BidItem[],
    total_amount: number,
    submitted_at: string,
    status: string
  }[],
  statistics: {
    total_submissions: number,
    average_price: number,
    price_range: { min: number, max: number },
    vendor_count: number
  }
}
```

#### Create Quote from Selections
```typescript
POST /api/quotes/generate

Body: {
  rfqId: number,
  selections: {
    [itemId: string]: {
      submissionId: number,
      vendorId: number,
      quantity: number,
      unitPrice: number,
      commission: { type: 'percentage' | 'fixed', value: number }
    }
  },
  globalCommission: { type: 'percentage' | 'fixed', value: number },
  clientInfo: {
    name: string,
    email: string,
    company?: string,
    address?: string
  },
  quoteSettings: {
    templateId?: number,
    expirationDays: number,
    terms?: string,
    notes?: string,
    showVendorAttribution: boolean
  }
}

Response: {
  quote: ClientQuote,
  token: string,
  previewUrl: string
}
```

#### Update Quote Selections
```typescript
PUT /api/quotes/:quoteId/selections

Body: {
  selections: SelectionData,
  commissionStructure: CommissionData
}

Response: {
  quote: ClientQuote,
  updatedPricing: PricingBreakdown
}
```

#### Generate PDF Quote
```typescript
POST /api/quotes/:quoteId/generate-pdf

Response: {
  pdfUrl: string,
  downloadToken: string,
  expiresAt: string
}
```

#### Send Quote to Client
```typescript
POST /api/quotes/:quoteId/send

Body: {
  clientEmail: string,
  message?: string,
  includeAttachment: boolean
}

Response: {
  sent: boolean,
  messageId: string,
  clientAccessUrl: string
}
```

#### Client Quote Approval
```typescript
POST /api/quotes/:token/approve

Body: {
  signature?: string,
  comments?: string,
  approvalData: {
    approvedItems?: string[],
    requestedChanges?: string
  }
}

Response: {
  approved: boolean,
  invoice?: Invoice,
  nextSteps: string[]
}
```

## Frontend Component Design

### QuoteBuilder Component
```typescript
interface QuoteBuilderProps {
  rfqId: number;
  submissions: RFQSubmission[];
  onQuoteGenerated: (quote: ClientQuote) => void;
}

interface QuoteBuilderState {
  selections: SelectionState;
  globalCommission: CommissionSettings;
  itemCommissions: Record<string, CommissionSettings>;
  clientInfo: ClientInfo;
  quoteSettings: QuoteSettings;
  pricing: PricingBreakdown;
  isGenerating: boolean;
}

// Features:
// - Submission comparison table with sortable columns
// - Item-level selection checkboxes with bulk actions
// - Real-time pricing calculator with tax calculations
// - Commission input fields with validation
// - Quote preview panel with PDF generation
// - Client information form with validation
// - Template selection dropdown
```

### ItemSelector Component
```typescript
interface ItemSelectorProps {
  rfqItems: RFQItem[];
  submissions: RFQSubmission[];
  selections: SelectionState;
  onSelectionChange: (selections: SelectionState) => void;
  onCommissionChange: (itemId: string, commission: CommissionSettings) => void;
}

// Features:
// - Drag-and-drop item selection interface
// - Vendor comparison table for each item
// - Price and delivery time comparison charts
// - Bulk selection actions (select all from vendor, clear all)
// - Commission override controls per item
// - Visual indicators for selected items
// - Search and filter capabilities
```

### ClientQuotePortal Component
```typescript
interface ClientQuotePortalProps {
  token: string;
  quote: ClientQuote;
  onApproval: (approvalData: ApprovalData) => void;
  onInteraction: (interaction: InteractionData) => void;
}

// Features:
// - Responsive quote details display
// - PDF download with tracking
// - Digital signature capture canvas
// - Comments and feedback form
// - Approval/rejection buttons
// - Progress tracking for multi-step approval
// - Mobile-optimized interface
```

### CommissionCalculator Component
```typescript
interface CommissionCalculatorProps {
  baseAmount: number;
  globalCommission: CommissionSettings;
  itemCommissions: Record<string, CommissionSettings>;
  taxRate?: number;
  onCalculationUpdate: (breakdown: PricingBreakdown) => void;
}

// Features:
// - Real-time calculation updates
// - Breakdown display (base, commission, taxes, total)
// - Profit margin visualization
// - Commission type switching (percentage/fixed)
// - Validation for reasonable commission rates
```

## Service Layer Design

### QuoteGenerationService
```typescript
class QuoteGenerationService {
  async generateQuote(data: QuoteGenerationData): Promise<ClientQuote> {
    // Validate selections and pricing
    // Calculate final amounts with commissions
    // Create quote record in database
    // Generate unique access token
    // Return quote with preview URL
  }

  async updateSelections(quoteId: number, selections: SelectionData): Promise<ClientQuote> {
    // Validate updated selections
    // Recalculate pricing
    // Update quote record
    // Increment revision number if significant changes
  }

  async calculatePricing(selections: SelectionData, commissions: CommissionData): Promise<PricingBreakdown> {
    // Calculate base amounts from selections
    // Apply global and item-level commissions
    // Calculate taxes and fees
    // Return detailed breakdown
  }
}
```

### PDFGenerationService
```typescript
class PDFGenerationService {
  async generateQuotePDF(quote: ClientQuote, template: QuoteTemplate): Promise<Buffer> {
    // Load quote template
    // Populate template with quote data
    // Generate PDF with proper formatting
    // Include company branding
    // Return PDF buffer
  }

  async getTemplate(templateId: number): Promise<QuoteTemplate> {
    // Retrieve template from database
    // Apply default template if not found
    // Return template configuration
  }
}
```

### EmailNotificationService
```typescript
class EmailNotificationService {
  async sendQuoteToClient(quote: ClientQuote, clientEmail: string, message?: string): Promise<boolean> {
    // Generate secure access URL
    // Create email template with quote details
    // Attach PDF if requested
    // Send email via configured service
    // Log email interaction
  }

  async sendApprovalNotification(quote: ClientQuote, approvalData: ApprovalData): Promise<boolean> {
    // Notify internal team of approval
    // Include approval details and next steps
    // Trigger invoice generation if configured
  }
}
```

## Security Design

### Token-Based Access
- Unique, cryptographically secure tokens for each quote
- Time-limited access with configurable expiration
- IP-based access logging for security monitoring
- Rate limiting to prevent abuse

### Role-Based Permissions
```typescript
enum QuotePermissions {
  CREATE_QUOTE = 'quote:create',
  VIEW_QUOTE = 'quote:view',
  EDIT_QUOTE = 'quote:edit',
  DELETE_QUOTE = 'quote:delete',
  SEND_QUOTE = 'quote:send',
  APPROVE_QUOTE = 'quote:approve',
  MANAGE_TEMPLATES = 'quote:manage_templates'
}
```

### Data Encryption
- Client information encrypted at rest
- Secure transmission of all quote data
- Digital signatures stored with cryptographic verification
- Audit trail with tamper-proof logging

## Performance Optimization

### Database Optimization
- Efficient indexes on frequently queried columns
- JSONB indexes for flexible item selection queries
- Connection pooling for concurrent access
- Query optimization for large submission datasets

### Caching Strategy
- Redis caching for frequently accessed quotes
- Template caching to reduce database load
- PDF generation caching for identical quotes
- API response caching for submission data

### Frontend Optimization
- Lazy loading of submission data
- Debounced real-time calculations
- Optimistic UI updates for better responsiveness
- Progressive loading for large quote lists

## Error Handling

### Backend Error Handling
- Comprehensive validation for all inputs
- Graceful degradation for external service failures
- Detailed error logging with correlation IDs
- Automatic retry mechanisms for transient failures

### Frontend Error Handling
- User-friendly error messages
- Automatic retry for network failures
- Offline capability for quote viewing
- Progress indicators for long-running operations

## Monitoring and Analytics

### Key Metrics
- Quote generation success rate
- Average quote processing time
- Client approval rates
- PDF generation performance
- Email delivery success rates

### Logging Strategy
- Structured logging with correlation IDs
- Performance metrics collection
- User interaction tracking
- Error rate monitoring
- Business metrics dashboard