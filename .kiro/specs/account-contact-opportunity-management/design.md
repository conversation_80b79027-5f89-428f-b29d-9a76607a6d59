# Design Document

## Overview

The Account, Contact, and Opportunity Management system extends the VMS with comprehensive CRM capabilities, creating a bidirectional procurement ecosystem. The design follows a modular architecture that integrates seamlessly with existing VMS modules while maintaining Salesforce compatibility for enterprise integration needs.

The system transforms the traditional vendor-focused procurement workflow into a complete deal lifecycle: Account/Contact management → Opportunity creation → RFQ generation → Vendor submissions → Quote creation → Client approval → Invoice generation. This design enables organizations to manage both supply-side (vendors) and demand-side (clients) relationships within a unified platform.

## Architecture

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Account Management UI]
        B[Contact Management UI]
        C[Opportunity Pipeline UI]
        D[Quote Builder UI]
        E[Public Quote Approval UI]
    end
    
    subgraph "API Layer"
        F[Account Controller]
        G[Contact Controller]
        H[Opportunity Controller]
        I[Quote Controller]
        J[Integration Controller]
    end
    
    subgraph "Service Layer"
        K[Salesforce Sync Service]
        L[Quote Generation Service]
        M[AI Recommendation Service]
        N[Notification Service]
    end
    
    subgraph "Data Layer"
        O[(PostgreSQL)]
        P[Account Table]
        Q[Contact Table]
        R[Opportunity Table]
        S[Quote Table]
    end
    
    subgraph "External Systems"
        T[Salesforce API]
        U[Existing RFQ Module]
        V[Existing Invoice Module]
    end
    
    A --> F
    B --> G
    C --> H
    D --> I
    E --> I
    
    F --> K
    G --> K
    H --> L
    I --> L
    I --> M
    
    K --> T
    L --> U
    I --> V
    
    F --> O
    G --> O
    H --> O
    I --> O
```

### Database Architecture

The database design extends the existing VMS schema with new CRM entities while maintaining referential integrity and supporting hierarchical relationships.

```mermaid
erDiagram
    Account {
        int id PK
        string name
        string account_number
        string type
        string industry
        decimal annual_revenue
        int number_of_employees
        string ownership
        string phone
        string website
        string ticker_symbol
        string rating
        text description
        jsonb billing_address
        jsonb shipping_address
        int parent_account_id FK
        int owner_id FK
        string integration_id
        jsonb custom_fields
        enum status
        datetime created_at
        datetime updated_at
        int created_by_id FK
        int last_modified_by_id FK
        boolean is_deleted
    }
    
    Contact {
        int id PK
        int account_id FK
        string name
        string email
        string role
        string phone
        datetime created_at
        datetime updated_at
    }
    
    Opportunity {
        int id PK
        int account_id FK
        string name
        string stage
        decimal expected_value
        jsonb items
        enum status
        datetime created_at
        datetime updated_at
    }
    
    Quote {
        int id PK
        int opportunity_id FK
        int account_id FK
        int contact_id FK
        jsonb selections
        decimal total_amount
        string currency
        enum status
        string pdf_url
        datetime created_at
        datetime approved_at
    }
    
    RFQ {
        int id PK
        int opportunity_id FK
        string title
        text description
        jsonb items
        enum status
        datetime created_at
    }
    
    User {
        int id PK
        string email
        string name
        enum role
    }
    
    Account ||--o{ Contact : "has many"
    Account ||--o{ Opportunity : "has many"
    Account ||--o{ Account : "parent-child"
    Opportunity ||--o{ RFQ : "generates"
    Opportunity ||--o{ Quote : "produces"
    Contact ||--o{ Quote : "approves"
    User ||--o{ Account : "owns"
    User ||--o{ Account : "created_by"
    User ||--o{ Account : "modified_by"
```

## Components and Interfaces

### Frontend Components

#### Account Management Components

**AccountForm Component**
- Purpose: Create and edit account records with Salesforce-compatible fields
- Props: `account?: Account, onSave: (account: Account) => void, onCancel: () => void`
- Features: Address autocomplete, hierarchy selection, custom field management
- Validation: Real-time validation with Salesforce field constraints

**AccountHierarchyTree Component**
- Purpose: Display and manage account hierarchical relationships
- Props: `rootAccountId: number, onAccountSelect: (account: Account) => void`
- Features: Recursive tree rendering, drag-and-drop reorganization, depth limiting
- Performance: Lazy loading for large hierarchies

**AccountSearchFilter Component**
- Purpose: Advanced search and filtering interface
- Props: `onFilterChange: (filters: AccountFilters) => void`
- Features: Industry dropdowns, revenue sliders, rating filters, fuzzy search
- State: URL-synchronized filter state for sharing

#### Opportunity Management Components

**OpportunityPipeline Component**
- Purpose: Kanban-style opportunity pipeline visualization
- Props: `opportunities: Opportunity[], onStageChange: (id: number, stage: string) => void`
- Features: Drag-and-drop stage transitions, real-time updates, filtering
- Charts: Integration with Recharts for pipeline analytics

**OpportunityForm Component**
- Purpose: Create and edit opportunities with item specifications
- Props: `opportunity?: Opportunity, accountId?: number, onSave: (opportunity: Opportunity) => void`
- Features: Account selection, contact assignment, item builder, stage tracking
- Validation: Business rule validation for stage transitions

#### Quote Management Components

**QuoteBuilder Component**
- Purpose: Interactive quote creation from RFQ submissions
- Props: `rfqId: number, submissions: RFQSubmission[], onQuoteCreate: (quote: Quote) => void`
- Features: Multi-vendor item selection, real-time total calculation, AI suggestions
- UI: Grid layout with checkboxes, comparison tables, margin controls

**QuoteApprovalForm Component**
- Purpose: Public quote approval interface for clients
- Props: `quoteToken: string, onApproval: (approved: boolean, signature?: string) => void`
- Features: PDF preview, e-signature capture, approval workflow
- Security: Token-based authentication, time-limited access

### Backend Services

#### Salesforce Integration Service

```typescript
interface SalesforceService {
  // Account synchronization
  syncAccountToSalesforce(account: Account): Promise<string>
  pullAccountFromSalesforce(integrationId: string): Promise<Account>
  
  // Batch operations
  batchSyncAccounts(accounts: Account[]): Promise<SyncResult[]>
  
  // Webhook handling
  handleSalesforceWebhook(payload: SalesforceWebhookPayload): Promise<void>
  
  // Connection management
  authenticate(): Promise<void>
  refreshToken(): Promise<void>
}
```

#### Quote Generation Service

```typescript
interface QuoteGenerationService {
  // Quote creation from submissions
  createQuoteFromSelections(
    opportunityId: number,
    selections: ItemSelection[],
    margins: MarginConfig
  ): Promise<Quote>
  
  // AI-powered optimization
  suggestOptimalSelections(
    submissions: RFQSubmission[],
    criteria: OptimizationCriteria
  ): Promise<ItemSelection[]>
  
  // PDF generation
  generateQuotePDF(quote: Quote): Promise<string>
  
  // Approval workflow
  generateApprovalToken(quoteId: number): Promise<string>
  processApproval(token: string, approved: boolean): Promise<void>
}
```

#### Notification Service

```typescript
interface NotificationService {
  // Quote notifications
  notifyQuoteReady(quote: Quote, contacts: Contact[]): Promise<void>
  notifyQuoteApproved(quote: Quote): Promise<void>
  
  // Opportunity notifications
  notifyOpportunityStageChange(opportunity: Opportunity): Promise<void>
  
  // Integration notifications
  notifySyncFailure(error: SyncError): Promise<void>
}
```

## Data Models

### Account Model

```typescript
interface Account {
  id: number
  name: string // Required, matches Salesforce Name
  accountNumber?: string
  type?: AccountType // Prospect, Customer-Direct, etc.
  industry?: Industry // Banking, IT, Manufacturing, etc.
  annualRevenue?: number
  numberOfEmployees?: number
  ownership?: Ownership // Public, Private
  phone?: string
  website?: string
  tickerSymbol?: string
  rating?: Rating // Hot, Warm, Cold
  description?: string
  billingAddress?: Address
  shippingAddress?: Address
  parentAccountId?: number
  ownerId?: number
  integrationId?: string // Salesforce Record ID
  customFields?: Record<string, any>
  status: Status
  createdAt: Date
  updatedAt: Date
  createdById?: number
  lastModifiedById?: number
  isDeleted: boolean
}

interface Address {
  street: string
  city: string
  state: string
  postalCode: string
  country: string
  latitude?: number
  longitude?: number
}
```

### Opportunity Model

```typescript
interface Opportunity {
  id: number
  accountId: number
  name: string
  stage: OpportunityStage // Prospecting, Sourcing, Quoted, Won, Lost
  expectedValue?: number
  items?: OpportunityItem[]
  status: Status
  createdAt: Date
  updatedAt: Date
  
  // Relationships
  account: Account
  contacts: Contact[]
  rfqs: RFQ[]
  quotes: Quote[]
}

interface OpportunityItem {
  name: string
  description: string
  quantity: number
  specifications?: Record<string, any>
  category?: string
  estimatedValue?: number
}
```

### Quote Model

```typescript
interface Quote {
  id: number
  opportunityId: number
  accountId: number
  contactId?: number
  selections: ItemSelection[]
  totalAmount: number
  currency: string
  status: QuoteStatus // Draft, Sent, Approved, Rejected
  pdfUrl?: string
  createdAt: Date
  approvedAt?: Date
  
  // Relationships
  opportunity: Opportunity
  account: Account
  contact?: Contact
  invoice?: Invoice
}

interface ItemSelection {
  itemId: string
  itemName: string
  submissionId: number
  vendorId: number
  vendorName: string
  quantity: number
  unitPrice: number
  totalPrice: number
  specifications?: Record<string, any>
}
```

## Error Handling

### API Error Responses

```typescript
interface APIError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: Date
  requestId: string
}

// Standard error codes
enum ErrorCodes {
  ACCOUNT_NOT_FOUND = 'ACCOUNT_NOT_FOUND',
  SALESFORCE_SYNC_FAILED = 'SALESFORCE_SYNC_FAILED',
  QUOTE_GENERATION_FAILED = 'QUOTE_GENERATION_FAILED',
  INVALID_HIERARCHY = 'INVALID_HIERARCHY',
  APPROVAL_TOKEN_EXPIRED = 'APPROVAL_TOKEN_EXPIRED'
}
```

### Error Handling Strategies

**Salesforce Integration Errors**
- Retry mechanism with exponential backoff for API rate limits
- Fallback to local operations when Salesforce is unavailable
- Detailed error logging for troubleshooting integration issues
- Queue-based sync for handling temporary failures

**Quote Generation Errors**
- Validation of item selections before processing
- Fallback to manual quote creation when AI suggestions fail
- Comprehensive logging of calculation errors
- Rollback mechanisms for failed quote operations

**Data Validation Errors**
- Client-side validation with immediate feedback
- Server-side validation with detailed error messages
- Constraint violation handling for database operations
- Hierarchy validation to prevent circular references

## Testing Strategy

### Unit Testing

**Backend Services**
- Account CRUD operations with 90% code coverage
- Salesforce integration service with mocked API responses
- Quote generation algorithms with various selection scenarios
- Hierarchy management with edge cases (circular references, depth limits)

**Frontend Components**
- Component rendering with various props combinations
- Form validation with invalid input scenarios
- User interaction testing with React Testing Library
- State management testing with Redux actions/reducers

### Integration Testing

**API Integration**
- End-to-end API workflows from account creation to quote approval
- Salesforce synchronization with sandbox environment
- Database transaction integrity across related entities
- Error handling and recovery scenarios

**User Interface Integration**
- Complete user workflows using Cypress
- Cross-browser compatibility testing
- Responsive design validation across device sizes
- Accessibility compliance testing (WCAG 2.1 AA)

### Performance Testing

**Database Performance**
- Query optimization for large account hierarchies
- Index effectiveness for search and filtering operations
- Concurrent user load testing with realistic data volumes
- Memory usage monitoring for large quote generations

**API Performance**
- Response time benchmarking for all endpoints
- Salesforce API rate limit handling
- Concurrent request handling and resource management
- Caching effectiveness for frequently accessed data

### Security Testing

**Authentication and Authorization**
- Role-based access control validation
- Token-based quote approval security
- SQL injection prevention testing
- Cross-site scripting (XSS) protection validation

**Data Protection**
- Encryption of sensitive data (addresses, contact information)
- GDPR compliance for data handling and deletion
- Audit trail integrity and tamper protection
- Secure API communication with external systems

## Deployment Considerations

### Database Migration Strategy

```sql
-- Migration script for CRM extension
CREATE TABLE accounts (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  account_number VARCHAR(100),
  type VARCHAR(50),
  industry VARCHAR(100),
  annual_revenue DECIMAL(15,2),
  number_of_employees INTEGER,
  ownership VARCHAR(50),
  phone VARCHAR(50),
  website VARCHAR(255),
  ticker_symbol VARCHAR(10),
  rating VARCHAR(20),
  description TEXT,
  billing_address JSONB,
  shipping_address JSONB,
  parent_account_id INTEGER REFERENCES accounts(id),
  owner_id INTEGER REFERENCES users(id),
  integration_id VARCHAR(100),
  custom_fields JSONB,
  status VARCHAR(20) DEFAULT 'ACTIVE',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by_id INTEGER REFERENCES users(id),
  last_modified_by_id INTEGER REFERENCES users(id),
  is_deleted BOOLEAN DEFAULT FALSE
);

-- Indexes for performance
CREATE INDEX idx_accounts_name ON accounts(name);
CREATE INDEX idx_accounts_industry ON accounts(industry);
CREATE INDEX idx_accounts_parent ON accounts(parent_account_id);
CREATE INDEX idx_accounts_billing_address ON accounts USING GIN(billing_address);
```

### Environment Configuration

```typescript
// Environment variables for CRM extension
interface CRMConfig {
  // Salesforce integration
  SALESFORCE_LOGIN_URL: string
  SALESFORCE_USERNAME: string
  SALESFORCE_PASSWORD: string
  SALESFORCE_SECURITY_TOKEN: string
  SALESFORCE_API_VERSION: string
  
  // Quote generation
  QUOTE_PDF_STORAGE_PATH: string
  QUOTE_APPROVAL_TOKEN_EXPIRY: number
  
  // Performance settings
  MAX_HIERARCHY_DEPTH: number
  MAX_QUOTE_ITEMS: number
  SYNC_BATCH_SIZE: number
}
```

### Monitoring and Alerting

**Key Metrics to Monitor**
- Account creation and modification rates
- Salesforce sync success/failure rates
- Quote generation performance and success rates
- Opportunity pipeline conversion metrics
- API response times and error rates

**Alert Conditions**
- Salesforce sync failures exceeding 5% of operations
- Quote generation taking longer than 30 seconds
- Database query response times exceeding 2 seconds
- Memory usage exceeding 80% during peak operations
- Failed authentication attempts exceeding threshold