# Design Document

## Overview

The RFQ Detail Page enhancements will build upon the existing RFQ management system to provide improved user experience and advanced functionality. The design focuses on maintaining consistency with the current UI patterns while adding powerful new features for item management, invitation handling, quote generation with partial selection support, and comprehensive audit tracking.

Key design principles:
- **Seamless Integration**: All enhancements integrate with existing components and maintain current UI patterns
- **Progressive Enhancement**: New features enhance existing workflows without breaking current functionality
- **Flexible Quote Generation**: Support for both whole-submission and partial-item selection in quote generation
- **Comprehensive Tracking**: Enhanced audit trails and engagement metrics for better decision making
- **Account Integration**: Deep integration with account and opportunity management for sales pipeline tracking

## Architecture

### System Architecture

The enhancements will extend the existing RFQ architecture with new components and enhanced existing ones:

```mermaid
graph TB
    subgraph "Enhanced Frontend Components"
        A[Enhanced RFQ Detail Page] --> B[Improved Items Display]
        A --> C[Enhanced Invitations Tab]
        A --> D[Advanced Quote Builder]
        A --> E[Enhanced History Tab]
        F[RFQ Edit Form] --> G[Partial Selection Config]
    end
    
    subgraph "New API Endpoints"
        H[Items Management API] --> I[Show All Items]
        J[Enhanced Invitations API] --> K[Resend/Reminder]
        L[Advanced Quote API] --> M[Partial Selection]
        N[Account Integration API] --> O[Account/Opp Lookup]
    end
    
    subgraph "Enhanced Data Layer"
        P[RFQ Schema Updates] --> Q[Partial Selection Fields]
        R[Quote Schema Updates] --> S[Account/Opp Links]
        T[Enhanced Audit Logging] --> U[Detailed Change Tracking]
    end
    
    A --> H
    C --> J
    D --> L
    D --> N
    E --> T
```

### Database Schema Enhancements

The existing database schema will be extended with new fields and tables:

```mermaid
erDiagram
    RFQS ||--o{ RFQ_ITEMS : contains
    RFQS ||--o{ CLIENT_QUOTES : generates
    CLIENT_QUOTES ||--o{ ACCOUNTS : links_to
    CLIENT_QUOTES ||--o{ OPPORTUNITIES : links_to
    RFQ_SUBMISSIONS ||--o{ BID_ITEMS : contains
    
    RFQS {
        int id PK
        boolean allow_partial_selection
        jsonb partial_selection_config
        timestamp updated_at
    }
    
    RFQ_SUBMISSIONS {
        int id PK
        boolean allows_partial_selection
        text partial_selection_notes
        timestamp viewed_at
    }
    
    CLIENT_QUOTES {
        int id PK
        int account_id FK
        int opportunity_id FK
        jsonb selected_items
        decimal global_commission_rate
        jsonb item_commission_overrides
        decimal total_cost
        decimal total_margin
        decimal final_amount
    }
    
    QUOTE_SELECTIONS {
        int id PK
        int quote_id FK
        int submission_id FK
        int bid_item_id FK
        boolean is_whole_submission
        decimal commission_rate
        decimal item_cost
        decimal item_margin
    }
    
    INVITATION_TRACKING {
        int id PK
        int invitation_id FK
        timestamp viewed_at
        timestamp reminded_at
        int reminder_count
        jsonb engagement_data
    }
```

## Components and Interfaces

### Enhanced RFQ Detail Page Components

#### Enhanced Items Display Component

```typescript
interface EnhancedItemsDisplayProps {
  items: RFQItem[];
  maxDisplayItems?: number;
  showExpandButton?: boolean;
  onShowAllItems: () => void;
}

interface ItemsModalProps {
  items: RFQItem[];
  isOpen: boolean;
  onClose: () => void;
  currency: string;
}

// Enhanced RFQ Item interface
interface RFQItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  unit: string;
  category: string;
  specifications: Record<string, any>;
  estimatedPrice?: number;
  customFields?: Record<string, any>;
  attachments?: FileAttachment[];
}
```

#### Enhanced Invitations Tab Component

```typescript
interface EnhancedInvitationsTabProps {
  rfqId: number;
  canEdit: boolean;
  onInvitationUpdate: (invitations: RFQInvitation[]) => void;
}

interface InvitationActions {
  resendInvitation: (invitationId: number) => Promise<void>;
  sendReminder: (invitationId: number, message?: string) => Promise<void>;
  addVendors: (vendorIds: number[]) => Promise<void>;
  trackViewing: (token: string) => Promise<void>;
}

interface EnhancedRFQInvitation extends RFQInvitation {
  viewed_at?: string;
  reminder_count: number;
  last_reminded_at?: string;
  engagement_score: number;
}
```

#### Advanced Quote Builder Component

```typescript
interface AdvancedQuoteBuilderProps {
  rfqId: number;
  submissions: RFQSubmission[];
  allowPartialSelection: boolean;
  onQuoteGenerated: (quote: ClientQuote) => void;
}

interface QuoteSelectionItem {
  submissionId: number;
  bidItemId?: number; // null for whole submission
  vendorName: string;
  itemName?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  allowsPartialSelection: boolean;
  commissionRate?: number; // override global rate
}

interface QuoteConfiguration {
  accountId?: number;
  opportunityId?: number;
  clientName: string;
  clientEmail: string;
  globalCommissionRate: number;
  itemCommissionOverrides: Record<string, number>;
  terms: string;
  notes: string;
  expiryDate?: Date;
}

interface PartialSelectionConfig {
  enabled: boolean;
  requireVendorConfirmation: boolean;
  defaultMessage: string;
  instructions: string;
}
```

#### Account/Opportunity Integration Component

```typescript
interface AccountOpportunitySelector {
  selectedAccountId?: number;
  selectedOpportunityId?: number;
  onAccountChange: (accountId: number) => void;
  onOpportunityChange: (opportunityId: number) => void;
}

interface AccountSearchResult {
  id: number;
  name: string;
  type: string;
  industry: string;
  location: string;
  activeOpportunities: number;
}

interface OpportunitySearchResult {
  id: number;
  name: string;
  stage: string;
  value: number;
  currency: string;
  closeDate: Date;
  probability: number;
}
```

### Backend Service Enhancements

#### Enhanced RFQ Service

```typescript
class EnhancedRFQService extends RFQService {
  async updateRFQ(rfqId: number, updates: RFQUpdateData, userId: number): Promise<RFQ>;
  async getAllItems(rfqId: number): Promise<RFQItem[]>;
  async updatePartialSelectionConfig(rfqId: number, config: PartialSelectionConfig): Promise<void>;
  async getDetailedAuditHistory(rfqId: number, filters?: AuditFilters): Promise<AuditEntry[]>;
}

interface RFQUpdateData {
  title?: string;
  description?: string;
  items?: RFQItem[];
  dueDate?: Date;
  allowPartialSelection?: boolean;
  partialSelectionConfig?: PartialSelectionConfig;
  terms?: string;
}
```

#### Enhanced Invitation Service

```typescript
class EnhancedInvitationService {
  async resendInvitation(invitationId: number, userId: number): Promise<RFQInvitation>;
  async sendReminder(invitationId: number, message: string, userId: number): Promise<void>;
  async addVendorsToRFQ(rfqId: number, vendorIds: number[], userId: number): Promise<RFQInvitation[]>;
  async trackInvitationViewing(token: string): Promise<void>;
  async getEngagementMetrics(rfqId: number): Promise<EngagementMetrics>;
}

interface EngagementMetrics {
  totalInvitations: number;
  viewedInvitations: number;
  submittedBids: number;
  averageViewTime: number;
  responseRate: number;
  engagementScore: number;
}
```

#### Advanced Quote Generation Service

```typescript
class AdvancedQuoteGenerationService extends QuoteGenerationService {
  async generateAdvancedQuote(
    rfqId: number,
    selections: QuoteSelectionItem[],
    config: QuoteConfiguration,
    userId: number
  ): Promise<ClientQuote>;
  
  async validatePartialSelections(
    selections: QuoteSelectionItem[]
  ): Promise<ValidationResult>;
  
  async calculateQuotePricing(
    selections: QuoteSelectionItem[],
    globalCommissionRate: number,
    itemOverrides: Record<string, number>
  ): Promise<QuotePricing>;
  
  async linkQuoteToAccountOpportunity(
    quoteId: number,
    accountId: number,
    opportunityId: number
  ): Promise<void>;
}

interface QuotePricing {
  subtotal: number;
  totalCommission: number;
  totalMargin: number;
  finalAmount: number;
  itemBreakdown: QuoteItemPricing[];
}

interface QuoteItemPricing {
  itemId: string;
  cost: number;
  commissionRate: number;
  commission: number;
  finalPrice: number;
}
```

#### Account Integration Service

```typescript
class AccountIntegrationService {
  async searchAccounts(query: string, limit?: number): Promise<AccountSearchResult[]>;
  async getAccountOpportunities(accountId: number): Promise<OpportunitySearchResult[]>;
  async validateAccountOpportunityLink(accountId: number, opportunityId: number): Promise<boolean>;
  async getAccountDetails(accountId: number): Promise<Account>;
  async getOpportunityDetails(opportunityId: number): Promise<Opportunity>;
}
```

### API Endpoints

#### Enhanced RFQ Management Endpoints

```typescript
// Enhanced RFQ endpoints
PUT    /api/rfqs/:id                    // Update RFQ with enhanced fields
GET    /api/rfqs/:id/items/all          // Get all items for RFQ
POST   /api/rfqs/:id/items              // Add items to RFQ
PUT    /api/rfqs/:id/items/:itemId      // Update specific item
DELETE /api/rfqs/:id/items/:itemId      // Remove item from RFQ

// Enhanced invitation endpoints
POST   /api/rfqs/:id/invitations/resend/:invitationId  // Resend invitation
POST   /api/rfqs/:id/invitations/remind/:invitationId  // Send reminder
POST   /api/rfqs/:id/invitations/add-vendors           // Add new vendors
POST   /api/rfqs/invitations/track-view                // Track invitation viewing
GET    /api/rfqs/:id/invitations/metrics               // Get engagement metrics

// Advanced quote endpoints
POST   /api/rfqs/:id/quotes/advanced                   // Generate advanced quote
POST   /api/quotes/:id/validate-selections             // Validate partial selections
GET    /api/quotes/:id/pricing-breakdown               // Get detailed pricing
PUT    /api/quotes/:id/link-account-opportunity        // Link to account/opportunity

// Account integration endpoints
GET    /api/accounts/search                            // Search accounts
GET    /api/accounts/:id/opportunities                 // Get account opportunities
GET    /api/opportunities/:id/details                  // Get opportunity details
POST   /api/quotes/validate-account-opportunity        // Validate account/opp link
```

## Data Models

### Enhanced RFQ Models

```typescript
interface EnhancedRFQ extends RFQ {
  allowPartialSelection: boolean;
  partialSelectionConfig: PartialSelectionConfig;
  itemCount: number;
  lastModifiedBy: string;
  lastModifiedAt: Date;
  engagementMetrics: EngagementMetrics;
}

interface PartialSelectionConfig {
  enabled: boolean;
  requireVendorConfirmation: boolean;
  confirmationMessage: string;
  instructions: string;
  defaultAllowed: boolean;
}

interface EnhancedRFQSubmission extends RFQSubmission {
  allowsPartialSelection: boolean;
  partialSelectionNotes?: string;
  viewedAt?: Date;
  engagementScore: number;
  responseTime: number; // hours from invitation to submission
}
```

### Advanced Quote Models

```typescript
interface AdvancedClientQuote extends ClientQuote {
  accountId?: number;
  opportunityId?: number;
  accountName?: string;
  opportunityName?: string;
  selections: QuoteSelection[];
  globalCommissionRate: number;
  itemCommissionOverrides: Record<string, number>;
  pricingBreakdown: QuotePricing;
  selectionSummary: SelectionSummary;
}

interface QuoteSelection {
  id: string;
  submissionId: number;
  bidItemId?: number; // null for whole submission
  vendorId: number;
  vendorName: string;
  itemId?: string;
  itemName?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  commissionRate: number;
  commission: number;
  finalPrice: number;
  isWholeSubmission: boolean;
  allowsPartialSelection: boolean;
}

interface SelectionSummary {
  totalVendors: number;
  totalItems: number;
  wholeSubmissions: number;
  partialSelections: number;
  averageCommissionRate: number;
  totalSavings: number;
}
```

### Enhanced Audit Models

```typescript
interface EnhancedAuditEntry extends AuditEntry {
  entityType: 'rfq' | 'invitation' | 'submission' | 'quote' | 'item';
  actionType: 'create' | 'update' | 'delete' | 'view' | 'send' | 'approve' | 'reject';
  changeDetails: ChangeDetails;
  relatedEntities: RelatedEntity[];
  impactLevel: 'low' | 'medium' | 'high';
  userRole: string;
  sessionId: string;
}

interface ChangeDetails {
  fieldsChanged: string[];
  oldValues: Record<string, any>;
  newValues: Record<string, any>;
  changeReason?: string;
  automatedChange: boolean;
}

interface RelatedEntity {
  entityType: string;
  entityId: number;
  entityName: string;
  relationship: string;
}
```

## User Interface Design

### Enhanced Items Display

The items section in the RFQ overview will show a maximum of 3 items with a "Show All Items" button when there are more:

```typescript
// Items display component structure
<div className="items-section">
  <h4 className="font-medium mb-2">Items ({itemCount})</h4>
  <div className="space-y-2">
    {items.slice(0, 3).map(item => (
      <ItemCard key={item.id} item={item} compact />
    ))}
  </div>
  {itemCount > 3 && (
    <Button 
      variant="outline" 
      size="sm" 
      onClick={openItemsModal}
      className="mt-2"
    >
      Show All {itemCount} Items
    </Button>
  )}
</div>
```

### Advanced Quote Builder Interface

The quote builder will feature a sophisticated selection interface:

```typescript
// Quote builder layout
<div className="quote-builder">
  <div className="selection-panel">
    <SubmissionGrid 
      submissions={submissions}
      onSelectionChange={handleSelectionChange}
      allowPartialSelection={allowPartialSelection}
    />
  </div>
  
  <div className="configuration-panel">
    <AccountOpportunitySelector />
    <CommissionConfiguration />
    <QuotePricingPreview />
    <QuoteTermsEditor />
  </div>
  
  <div className="preview-panel">
    <QuotePreview selections={selections} config={config} />
  </div>
</div>
```

### Enhanced Invitations Management

The invitations tab will include action buttons for each invitation:

```typescript
// Enhanced invitation row actions
<TableCell>
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="ghost" size="sm">
        <MoreHorizontal className="w-4 h-4" />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent>
      <DropdownMenuItem onClick={() => resendInvitation(invitation.id)}>
        <Send className="w-4 h-4 mr-2" />
        Resend Invitation
      </DropdownMenuItem>
      <DropdownMenuItem onClick={() => sendReminder(invitation.id)}>
        <Bell className="w-4 h-4 mr-2" />
        Send Reminder
      </DropdownMenuItem>
      <DropdownMenuItem onClick={() => viewEngagement(invitation.id)}>
        <TrendingUp className="w-4 h-4 mr-2" />
        View Engagement
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</TableCell>
```

## Error Handling

### Enhanced Error Categories

1. **Partial Selection Validation Errors**
   - Attempting to select individual items from non-partial vendors
   - Invalid item combinations
   - Commission rate validation errors

2. **Account Integration Errors**
   - Invalid account/opportunity combinations
   - Missing account permissions
   - Opportunity not found or inactive

3. **Quote Generation Errors**
   - Insufficient selections for quote
   - Pricing calculation errors
   - PDF generation failures

4. **Invitation Management Errors**
   - Email delivery failures
   - Invalid vendor selections
   - Duplicate invitation attempts

### Error Handling Strategy

```typescript
// Enhanced error handling with specific error types
interface RFQEnhancementError {
  code: string;
  message: string;
  category: 'validation' | 'business' | 'integration' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  context: Record<string, any>;
  suggestions: string[];
  retryable: boolean;
}

// Example error responses
{
  "code": "PARTIAL_SELECTION_VIOLATION",
  "message": "Cannot select individual items from this vendor",
  "category": "business",
  "severity": "medium",
  "context": {
    "vendorId": 123,
    "vendorName": "ABC Corp",
    "submissionId": 456
  },
  "suggestions": [
    "Select the entire submission instead",
    "Choose items from vendors that allow partial selection"
  ],
  "retryable": false
}
```

## Testing Strategy

### Component Testing

1. **Enhanced Items Display Testing**
   - Test item display limits and expansion
   - Test modal functionality and data loading
   - Test responsive behavior with many items

2. **Advanced Quote Builder Testing**
   - Test partial selection validation
   - Test commission calculations
   - Test account/opportunity integration
   - Test real-time pricing updates

3. **Enhanced Invitations Testing**
   - Test resend and reminder functionality
   - Test vendor addition workflow
   - Test engagement tracking
   - Test status updates

### Integration Testing

1. **End-to-End Quote Generation**
   - Test complete quote generation workflow
   - Test partial selection scenarios
   - Test account/opportunity linking
   - Test PDF generation and delivery

2. **Audit Trail Testing**
   - Test comprehensive change tracking
   - Test history filtering and search
   - Test performance with large audit logs

3. **Account Integration Testing**
   - Test account search functionality
   - Test opportunity filtering
   - Test data synchronization

### Performance Testing

1. **Large Dataset Handling**
   - Test with RFQs containing 100+ items
   - Test with 50+ vendor submissions
   - Test quote generation with complex selections

2. **Real-time Updates**
   - Test concurrent user interactions
   - Test real-time pricing calculations
   - Test engagement tracking performance

## Security Considerations

### Enhanced Security Measures

1. **Partial Selection Security**
   - Validate vendor permissions for partial selections
   - Prevent unauthorized item combinations
   - Audit all selection changes

2. **Account Integration Security**
   - Validate user permissions for account access
   - Ensure opportunity data privacy
   - Log all account/opportunity linkages

3. **Enhanced Audit Security**
   - Immutable audit trail storage
   - Encrypted sensitive change data
   - Role-based audit access control

### Data Protection

```typescript
// Enhanced audit logging with security
interface SecureAuditEntry {
  id: string;
  timestamp: Date;
  userId: number;
  userRole: string;
  action: string;
  entityType: string;
  entityId: number;
  changeHash: string; // Hash of change data for integrity
  encryptedDetails: string; // Encrypted sensitive data
  ipAddress: string;
  userAgent: string;
  sessionId: string;
}
```

## Performance Optimization

### Caching Strategy

1. **Account/Opportunity Caching**
   - Cache frequently accessed account data
   - Implement search result caching
   - Use Redis for session-based caching

2. **Quote Calculation Caching**
   - Cache commission calculations
   - Cache pricing breakdowns
   - Implement real-time calculation optimization

3. **Audit Data Optimization**
   - Implement pagination for large audit logs
   - Use database indexing for audit queries
   - Archive old audit data

### Database Optimization

```sql
-- Enhanced indexing for new features
CREATE INDEX idx_rfqs_partial_selection ON rfqs(allow_partial_selection);
CREATE INDEX idx_submissions_partial_selection ON rfq_submissions(allows_partial_selection);
CREATE INDEX idx_quotes_account_opportunity ON client_quotes(account_id, opportunity_id);
CREATE INDEX idx_audit_entity_timestamp ON audit_entries(entity_type, entity_id, timestamp);
CREATE INDEX idx_invitations_engagement ON rfq_invitations(viewed_at, submitted_at);
```

This design provides a comprehensive foundation for implementing all the requested RFQ Detail Page enhancements while maintaining consistency with the existing system architecture and ensuring scalability and security.