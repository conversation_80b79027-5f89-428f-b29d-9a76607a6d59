-- Migration: Add Partial Selection Support to RFQs
-- Enhancement: RFQ Detail Page Enhancements - Partial Selection Support
-- Date: 2024-02-08

-- ============================================================================
-- 1. ADD PARTIAL SELECTION FIELDS TO RFQS TABLE
-- ============================================================================

-- Add partial selection support fields to RFQs table
ALTER TABLE rfqs ADD COLUMN IF NOT EXISTS allow_partial_selection BOOLEAN DEFAULT false;
ALTER TABLE rfqs ADD COLUMN IF NOT EXISTS partial_selection_config JSONB DEFAULT '{
    "enabled": false,
    "requireVendorConfirmation": true,
    "confirmationMessage": "Do you allow individual item purchases at the quoted rates?",
    "instructions": "Please confirm if you allow partial selection of items from your submission.",
    "defaultAllowed": false
}'::JSONB;

-- Add comments for new fields
COMMENT ON COLUMN rfqs.allow_partial_selection IS 'Whether this RFQ allows vendors to opt-in for partial item selection';
COMMENT ON COLUMN rfqs.partial_selection_config IS 'Configuration for partial selection including vendor confirmation requirements';

-- ============================================================================
-- 2. ADD PARTIAL SELECTION FIELDS TO RFQ SUBMISSIONS TABLE
-- ============================================================================

-- Add partial selection tracking to submissions
ALTER TABLE rfq_submissions ADD COLUMN IF NOT EXISTS allows_partial_selection BOOLEAN DEFAULT false;
ALTER TABLE rfq_submissions ADD COLUMN IF NOT EXISTS partial_selection_notes TEXT;
ALTER TABLE rfq_submissions ADD COLUMN IF NOT EXISTS viewed_at TIMESTAMP WITH TIME ZONE;

-- Add comments for new fields
COMMENT ON COLUMN rfq_submissions.allows_partial_selection IS 'Whether vendor allows individual item purchases from this submission';
COMMENT ON COLUMN rfq_submissions.partial_selection_notes IS 'Vendor notes regarding partial selection terms';
COMMENT ON COLUMN rfq_submissions.viewed_at IS 'When the vendor first viewed the RFQ invitation';

-- ============================================================================
-- 3. CREATE ENHANCED QUOTE SELECTION TRACKING TABLE
-- ============================================================================

-- Create table for detailed quote selection tracking
CREATE TABLE IF NOT EXISTS quote_selections (
    id SERIAL PRIMARY KEY,
    quote_id INTEGER NOT NULL REFERENCES client_quotes(id) ON DELETE CASCADE,
    submission_id INTEGER NOT NULL REFERENCES rfq_submissions(id) ON DELETE CASCADE,
    bid_item_id INTEGER REFERENCES bid_items(id) ON DELETE CASCADE,
    vendor_id INTEGER NOT NULL REFERENCES vendors(id),
    rfq_item_id VARCHAR(50), -- References RFQ item ID
    item_name VARCHAR(255),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(15, 2) NOT NULL CHECK (unit_price >= 0),
    total_price DECIMAL(15, 2) NOT NULL CHECK (total_price >= 0),
    commission_rate DECIMAL(5, 2) DEFAULT 0.00 CHECK (commission_rate >= 0),
    commission_amount DECIMAL(15, 2) DEFAULT 0.00 CHECK (commission_amount >= 0),
    final_price DECIMAL(15, 2) NOT NULL CHECK (final_price >= 0),
    is_whole_submission BOOLEAN DEFAULT false,
    selection_rationale TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE quote_selections IS 'Detailed tracking of selected items/submissions for quotes';
COMMENT ON COLUMN quote_selections.bid_item_id IS 'NULL for whole submission selections, specific item ID for partial selections';
COMMENT ON COLUMN quote_selections.is_whole_submission IS 'TRUE if entire submission was selected, FALSE for individual items';
COMMENT ON COLUMN quote_selections.commission_rate IS 'Commission rate applied to this selection (can override global rate)';
COMMENT ON COLUMN quote_selections.selection_rationale IS 'Reason for selecting this vendor/item combination';

-- ============================================================================
-- 4. ADD ACCOUNT/OPPORTUNITY INTEGRATION TO QUOTES
-- ============================================================================

-- Add account and opportunity linking to client quotes
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS account_id INTEGER;
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS opportunity_id INTEGER;
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS account_name VARCHAR(255);
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS opportunity_name VARCHAR(255);
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS global_commission_rate DECIMAL(5, 2) DEFAULT 0.00 CHECK (global_commission_rate >= 0);
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS item_commission_overrides JSONB DEFAULT '{}'::JSONB;
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS total_cost DECIMAL(15, 2) DEFAULT 0.00 CHECK (total_cost >= 0);
ALTER TABLE client_quotes ADD COLUMN IF NOT EXISTS total_margin DECIMAL(15, 2) DEFAULT 0.00 CHECK (total_margin >= 0);

-- Add comments for new fields
COMMENT ON COLUMN client_quotes.account_id IS 'Reference to account this quote is for';
COMMENT ON COLUMN client_quotes.opportunity_id IS 'Reference to opportunity this quote is for';
COMMENT ON COLUMN client_quotes.account_name IS 'Cached account name for display';
COMMENT ON COLUMN client_quotes.opportunity_name IS 'Cached opportunity name for display';
COMMENT ON COLUMN client_quotes.global_commission_rate IS 'Default commission rate for all items in quote';
COMMENT ON COLUMN client_quotes.item_commission_overrides IS 'Per-item commission rate overrides {itemId: rate}';
COMMENT ON COLUMN client_quotes.total_cost IS 'Total cost before commission and markup';
COMMENT ON COLUMN client_quotes.total_margin IS 'Total margin/commission amount';

-- ============================================================================
-- 5. CREATE INVITATION TRACKING ENHANCEMENT TABLE
-- ============================================================================

-- Create table for detailed invitation engagement tracking
CREATE TABLE IF NOT EXISTS invitation_tracking (
    id SERIAL PRIMARY KEY,
    invitation_id INTEGER NOT NULL REFERENCES rfq_invitations(id) ON DELETE CASCADE,
    viewed_at TIMESTAMP WITH TIME ZONE,
    reminded_at TIMESTAMP WITH TIME ZONE,
    reminder_count INTEGER DEFAULT 0,
    engagement_data JSONB DEFAULT '{}'::JSONB, -- {viewCount, timeSpent, pagesViewed, etc.}
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(invitation_id) -- One tracking record per invitation
);

COMMENT ON TABLE invitation_tracking IS 'Detailed engagement tracking for RFQ invitations';
COMMENT ON COLUMN invitation_tracking.engagement_data IS 'Detailed engagement metrics and analytics';
COMMENT ON COLUMN invitation_tracking.reminder_count IS 'Number of reminder emails sent';

-- ============================================================================
-- 6. CREATE INDEXES FOR PERFORMANCE OPTIMIZATION
-- ============================================================================

-- Indexes for partial selection queries
CREATE INDEX IF NOT EXISTS idx_rfqs_partial_selection ON rfqs(allow_partial_selection);
CREATE INDEX IF NOT EXISTS idx_submissions_partial_selection ON rfq_submissions(allows_partial_selection);
CREATE INDEX IF NOT EXISTS idx_submissions_viewed_at ON rfq_submissions(viewed_at);

-- Indexes for quote selections
CREATE INDEX IF NOT EXISTS idx_quote_selections_quote ON quote_selections(quote_id);
CREATE INDEX IF NOT EXISTS idx_quote_selections_submission ON quote_selections(submission_id);
CREATE INDEX IF NOT EXISTS idx_quote_selections_vendor ON quote_selections(vendor_id);
CREATE INDEX IF NOT EXISTS idx_quote_selections_whole_submission ON quote_selections(is_whole_submission);

-- Indexes for account/opportunity integration
CREATE INDEX IF NOT EXISTS idx_quotes_account_opportunity ON client_quotes(account_id, opportunity_id);
CREATE INDEX IF NOT EXISTS idx_quotes_account ON client_quotes(account_id);
CREATE INDEX IF NOT EXISTS idx_quotes_opportunity ON client_quotes(opportunity_id);

-- Indexes for invitation tracking
CREATE INDEX IF NOT EXISTS idx_invitation_tracking_invitation ON invitation_tracking(invitation_id);
CREATE INDEX IF NOT EXISTS idx_invitation_tracking_viewed_at ON invitation_tracking(viewed_at);
CREATE INDEX IF NOT EXISTS idx_invitation_tracking_reminded_at ON invitation_tracking(reminded_at);

-- Enhanced indexes for RFQ invitations
CREATE INDEX IF NOT EXISTS idx_rfq_invitations_reminder_count ON rfq_invitations(email_sent_count);
CREATE INDEX IF NOT EXISTS idx_rfq_invitations_last_reminder ON rfq_invitations(last_reminder_sent);

-- ============================================================================
-- 7. ENHANCED FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to update quote totals with commission calculations
CREATE OR REPLACE FUNCTION update_enhanced_quote_total()
RETURNS TRIGGER AS $
DECLARE
    total_cost_val DECIMAL(15, 2) := 0;
    total_commission_val DECIMAL(15, 2) := 0;
    final_total_val DECIMAL(15, 2) := 0;
    selection_record RECORD;
BEGIN
    -- Calculate totals from quote selections
    FOR selection_record IN 
        SELECT 
            total_price,
            commission_rate,
            commission_amount
        FROM quote_selections 
        WHERE quote_id = NEW.id
    LOOP
        total_cost_val := total_cost_val + selection_record.total_price;
        total_commission_val := total_commission_val + selection_record.commission_amount;
    END LOOP;
    
    -- Calculate final total
    final_total_val := total_cost_val + total_commission_val + COALESCE(NEW.taxes, 0);
    
    -- Update the quote record
    NEW.total_cost := total_cost_val;
    NEW.total_margin := total_commission_val;
    NEW.total_amount := final_total_val;
    NEW.updated_at := CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

-- Function to calculate commission for quote selections
CREATE OR REPLACE FUNCTION calculate_selection_commission()
RETURNS TRIGGER AS $
DECLARE
    global_rate DECIMAL(5, 2);
    override_rate DECIMAL(5, 2);
    final_rate DECIMAL(5, 2);
BEGIN
    -- Get global commission rate from quote
    SELECT global_commission_rate INTO global_rate 
    FROM client_quotes 
    WHERE id = NEW.quote_id;
    
    -- Check for item-specific override
    SELECT (item_commission_overrides->>NEW.rfq_item_id)::DECIMAL INTO override_rate
    FROM client_quotes 
    WHERE id = NEW.quote_id 
    AND item_commission_overrides ? NEW.rfq_item_id;
    
    -- Use override rate if available, otherwise use global rate
    final_rate := COALESCE(override_rate, global_rate, 0);
    
    -- Calculate commission
    NEW.commission_rate := final_rate;
    NEW.commission_amount := NEW.total_price * (final_rate / 100);
    NEW.final_price := NEW.total_price + NEW.commission_amount;
    
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

-- Function to track invitation viewing
CREATE OR REPLACE FUNCTION track_invitation_view()
RETURNS TRIGGER AS $
BEGIN
    -- Update viewed_at timestamp if not already set
    IF OLD.viewed_at IS NULL AND NEW.viewed_at IS NOT NULL THEN
        -- Insert or update invitation tracking
        INSERT INTO invitation_tracking (invitation_id, viewed_at, engagement_data)
        VALUES (NEW.id, NEW.viewed_at, '{"viewCount": 1}'::JSONB)
        ON CONFLICT (invitation_id) 
        DO UPDATE SET 
            viewed_at = COALESCE(invitation_tracking.viewed_at, NEW.viewed_at),
            engagement_data = jsonb_set(
                invitation_tracking.engagement_data, 
                '{viewCount}', 
                ((invitation_tracking.engagement_data->>'viewCount')::INTEGER + 1)::TEXT::JSONB
            ),
            updated_at = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

-- Apply enhanced triggers
DROP TRIGGER IF EXISTS client_quotes_total_trigger ON client_quotes;
CREATE TRIGGER enhanced_client_quotes_total_trigger 
    BEFORE INSERT OR UPDATE ON client_quotes 
    FOR EACH ROW EXECUTE FUNCTION update_enhanced_quote_total();

CREATE TRIGGER quote_selections_commission_trigger 
    BEFORE INSERT OR UPDATE ON quote_selections 
    FOR EACH ROW EXECUTE FUNCTION calculate_selection_commission();

CREATE TRIGGER invitation_view_tracking_trigger 
    AFTER UPDATE ON rfq_invitations 
    FOR EACH ROW EXECUTE FUNCTION track_invitation_view();

-- Apply updated_at triggers to new tables
CREATE TRIGGER update_quote_selections_updated_at 
    BEFORE UPDATE ON quote_selections 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_invitation_tracking_updated_at 
    BEFORE UPDATE ON invitation_tracking 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 8. ENHANCED VIEWS FOR COMMON QUERIES
-- ============================================================================

-- Enhanced RFQ summary view with partial selection info
DROP VIEW IF EXISTS rfq_summary;
CREATE VIEW rfq_summary AS
SELECT 
    r.id,
    r.title,
    r.description,
    r.status,
    r.due_date,
    r.creator_id,
    u.email as creator_email,
    r.currency,
    jsonb_array_length(r.items) as item_count,
    r.allow_partial_selection,
    r.partial_selection_config,
    COUNT(DISTINCT ri.id) as invitation_count,
    COUNT(DISTINCT rs.id) as submission_count,
    COUNT(DISTINCT CASE WHEN rs.allows_partial_selection = true THEN rs.id END) as partial_selection_submissions,
    COUNT(DISTINCT CASE WHEN ri.viewed_at IS NOT NULL THEN ri.id END) as viewed_invitations,
    CASE 
        WHEN COUNT(DISTINCT ri.id) > 0 
        THEN ROUND((COUNT(DISTINCT rs.id)::DECIMAL / COUNT(DISTINCT ri.id)) * 100, 2)
        ELSE 0 
    END as response_rate,
    CASE 
        WHEN COUNT(DISTINCT ri.id) > 0 
        THEN ROUND((COUNT(DISTINCT CASE WHEN ri.viewed_at IS NOT NULL THEN ri.id END)::DECIMAL / COUNT(DISTINCT ri.id)) * 100, 2)
        ELSE 0 
    END as view_rate,
    MIN(rs.total_amount) as lowest_bid,
    MAX(rs.total_amount) as highest_bid,
    AVG(rs.total_amount) as average_bid,
    COUNT(DISTINCT cq.id) as quote_count,
    r.created_at,
    r.updated_at
FROM rfqs r
JOIN users u ON r.creator_id = u.id
LEFT JOIN rfq_invitations ri ON r.id = ri.rfq_id
LEFT JOIN rfq_submissions rs ON r.id = rs.rfq_id
LEFT JOIN client_quotes cq ON r.id = cq.rfq_id
GROUP BY r.id, r.title, r.description, r.status, r.due_date, r.creator_id, u.email, r.currency, r.items, r.allow_partial_selection, r.partial_selection_config, r.created_at, r.updated_at;

COMMENT ON VIEW rfq_summary IS 'Enhanced summary view of RFQs with partial selection and engagement statistics';

-- View for quote selection analysis
CREATE VIEW quote_selection_analysis AS
SELECT 
    qs.quote_id,
    cq.title as quote_title,
    cq.rfq_id,
    r.title as rfq_title,
    qs.vendor_id,
    v.name as vendor_name,
    COUNT(*) as selected_items,
    SUM(qs.total_price) as total_cost,
    SUM(qs.commission_amount) as total_commission,
    SUM(qs.final_price) as total_final_price,
    AVG(qs.commission_rate) as avg_commission_rate,
    COUNT(CASE WHEN qs.is_whole_submission = true THEN 1 END) as whole_submissions,
    COUNT(CASE WHEN qs.is_whole_submission = false THEN 1 END) as partial_selections,
    qs.created_at
FROM quote_selections qs
JOIN client_quotes cq ON qs.quote_id = cq.id
JOIN rfqs r ON cq.rfq_id = r.id
JOIN vendors v ON qs.vendor_id = v.id
GROUP BY qs.quote_id, cq.title, cq.rfq_id, r.title, qs.vendor_id, v.name, qs.created_at;

COMMENT ON VIEW quote_selection_analysis IS 'Analysis view for quote selections showing vendor distribution and pricing';

-- ============================================================================
-- 9. GRANT PERMISSIONS FOR NEW TABLES
-- ============================================================================

-- Grant permissions for new tables
GRANT SELECT, INSERT, UPDATE, DELETE ON quote_selections TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON invitation_tracking TO PUBLIC;

GRANT USAGE ON SEQUENCE quote_selections_id_seq TO PUBLIC;
GRANT USAGE ON SEQUENCE invitation_tracking_id_seq TO PUBLIC;

GRANT SELECT ON quote_selection_analysis TO PUBLIC;

-- ============================================================================
-- 10. UPDATE EXISTING DATA (SAFE DEFAULTS)
-- ============================================================================

-- Update existing RFQs with default partial selection settings
UPDATE rfqs 
SET 
    allow_partial_selection = false,
    partial_selection_config = '{
        "enabled": false,
        "requireVendorConfirmation": true,
        "confirmationMessage": "Do you allow individual item purchases at the quoted rates?",
        "instructions": "Please confirm if you allow partial selection of items from your submission.",
        "defaultAllowed": false
    }'::JSONB
WHERE allow_partial_selection IS NULL;

-- Update existing submissions with default partial selection values
UPDATE rfq_submissions 
SET allows_partial_selection = false
WHERE allows_partial_selection IS NULL;

-- Update existing quotes with default commission settings
UPDATE client_quotes 
SET 
    global_commission_rate = 0.00,
    item_commission_overrides = '{}'::JSONB,
    total_cost = total_amount,
    total_margin = 0.00
WHERE global_commission_rate IS NULL;

COMMIT;