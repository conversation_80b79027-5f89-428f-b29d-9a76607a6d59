-- ============================================================================
-- Migration: 003_extend_client_quotes_for_generation.sql
-- Description: Extend client_quotes table for enhanced quote generation system
-- Author: System
-- Date: 2024
-- ============================================================================

BEGIN;

-- ============================================================================
-- 1. EXTEND CLIENT_QUOTES TABLE
-- ============================================================================

-- ============================================================================
-- 2. CREATE QUOTE_TEMPLATES TABLE
-- ============================================================================

CREATE TABLE quote_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_data JSONB NOT NULL DEFAULT '{}'::JSONB,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE quote_templates IS 'Templates for quote generation with customizable layouts and branding';
COMMENT ON COLUMN quote_templates.template_data IS 'JSONB structure containing template configuration, styling, and layout';
COMMENT ON COLUMN quote_templates.is_default IS 'Indicates if this is the default template for new quotes';

-- ============================================================================
-- 3. EXTEND CLIENT_QUOTES TABLE
-- ============================================================================

-- Add new columns to client_quotes table
ALTER TABLE client_quotes 
ADD COLUMN IF NOT EXISTS item_selections JSONB DEFAULT '{}'::JSONB,
ADD COLUMN IF NOT EXISTS commission_structure JSONB DEFAULT '{}'::JSONB,
ADD COLUMN IF NOT EXISTS quote_template_id INTEGER,
ADD COLUMN IF NOT EXISTS revision_number INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS parent_quote_id INTEGER;

-- Add foreign key constraints
ALTER TABLE client_quotes 
ADD CONSTRAINT fk_client_quotes_template 
    FOREIGN KEY (quote_template_id) REFERENCES quote_templates(id) ON DELETE SET NULL,
ADD CONSTRAINT fk_client_quotes_parent 
    FOREIGN KEY (parent_quote_id) REFERENCES client_quotes(id) ON DELETE SET NULL;

-- Add check constraints
ALTER TABLE client_quotes 
ADD CONSTRAINT chk_revision_number_positive 
    CHECK (revision_number > 0);

-- Add comments for new columns
COMMENT ON COLUMN client_quotes.item_selections IS 'JSONB structure storing selected items from submissions with vendor details';
COMMENT ON COLUMN client_quotes.commission_structure IS 'JSONB structure storing commission rates (global and per-item overrides)';
COMMENT ON COLUMN client_quotes.quote_template_id IS 'Reference to quote template used for generation';
COMMENT ON COLUMN client_quotes.revision_number IS 'Version number for quote revisions';
COMMENT ON COLUMN client_quotes.parent_quote_id IS 'Reference to parent quote for revisions';

-- ============================================================================
-- 4. CREATE QUOTE_INTERACTIONS TABLE
-- ============================================================================

CREATE TABLE quote_interactions (
    id SERIAL PRIMARY KEY,
    quote_id INTEGER NOT NULL REFERENCES client_quotes(id) ON DELETE CASCADE,
    interaction_type VARCHAR(50) NOT NULL,
    interaction_data JSONB DEFAULT '{}'::JSONB,
    ip_address INET,
    user_agent TEXT,
    client_email VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE quote_interactions IS 'Tracking table for client interactions with quotes';
COMMENT ON COLUMN quote_interactions.interaction_type IS 'Type of interaction: viewed, downloaded, approved, rejected, etc.';
COMMENT ON COLUMN quote_interactions.interaction_data IS 'Additional data related to the interaction';

-- ============================================================================
-- 5. CREATE PERFORMANCE INDEXES
-- ============================================================================

-- GIN index for JSONB columns
CREATE INDEX IF NOT EXISTS idx_client_quotes_item_selections_gin 
    ON client_quotes USING GIN (item_selections);

CREATE INDEX IF NOT EXISTS idx_client_quotes_commission_structure_gin 
    ON client_quotes USING GIN (commission_structure);

CREATE INDEX IF NOT EXISTS idx_quote_templates_template_data_gin 
    ON quote_templates USING GIN (template_data);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_client_quotes_rfq_status 
    ON client_quotes (rfq_id, status);

CREATE INDEX IF NOT EXISTS idx_client_quotes_creator_status 
    ON client_quotes (creator_id, status);

CREATE INDEX IF NOT EXISTS idx_client_quotes_revision 
    ON client_quotes (parent_quote_id, revision_number);

-- Quote templates indexes
CREATE INDEX IF NOT EXISTS idx_quote_templates_default 
    ON quote_templates (is_default, is_active);

CREATE INDEX IF NOT EXISTS idx_quote_templates_creator 
    ON quote_templates (created_by, is_active);

-- Quote interactions indexes
CREATE INDEX IF NOT EXISTS idx_quote_interactions_quote_type 
    ON quote_interactions (quote_id, interaction_type);

CREATE INDEX IF NOT EXISTS idx_quote_interactions_created_at 
    ON quote_interactions (created_at);

-- ============================================================================
-- 6. CREATE DEFAULT QUOTE TEMPLATE
-- ============================================================================

-- Insert default quote template
INSERT INTO quote_templates (name, description, template_data, is_default, created_by) VALUES (
    'Default Professional Template',
    'Standard professional quote template with company branding',
    '{
        "layout": {
            "header": {
                "showLogo": true,
                "showCompanyInfo": true,
                "backgroundColor": "#ffffff",
                "textColor": "#333333"
            },
            "body": {
                "showItemDetails": true,
                "showVendorAttribution": false,
                "groupByCategory": true,
                "showUnitPrices": true,
                "showQuantities": true
            },
            "footer": {
                "showTerms": true,
                "showSignature": true,
                "showContactInfo": true
            }
        },
        "styling": {
            "primaryColor": "#2563eb",
            "secondaryColor": "#64748b",
            "fontFamily": "Inter, sans-serif",
            "fontSize": "14px"
        },
        "content": {
            "defaultTerms": "Payment terms: Net 30 days. Prices valid for 30 days from quote date.",
            "defaultNotes": "Thank you for your business. Please contact us with any questions."
        }
    }'::JSONB,
    true,
    1
);

-- ============================================================================
-- 7. UPDATE TRIGGERS
-- ============================================================================

-- Add updated_at trigger for quote_templates
CREATE TRIGGER update_quote_templates_updated_at 
    BEFORE UPDATE ON quote_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 8. GRANT PERMISSIONS
-- ============================================================================

-- Grant permissions for new tables
GRANT SELECT, INSERT, UPDATE, DELETE ON quote_templates TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON quote_interactions TO PUBLIC;
GRANT USAGE ON SEQUENCE quote_templates_id_seq TO PUBLIC;
GRANT USAGE ON SEQUENCE quote_interactions_id_seq TO PUBLIC;

COMMIT;