-- Migration: Create Opportunities Table
-- Description: Add Opportunity management functionality with Salesforce-aligned structure
-- Date: 2025-01-29

-- ============================================================================
-- 1. CREATE OPPORTUNITY ENUMS
-- ============================================================================

-- Opportunity stage enum (Salesforce-compatible)
CREATE TYPE opportunity_stage_enum AS ENUM (
    'Prospecting',
    'Qualification',
    'Needs Analysis',
    'Value Proposition',
    'Id. Decision Makers',
    'Perception Analysis',
    'Proposal/Price Quote',
    'Negotiation/Review',
    'Closed Won',
    'Closed Lost'
);

-- Opportunity type enum
CREATE TYPE opportunity_type_enum AS ENUM (
    'New Customer',
    'Existing Customer - Upgrade',
    'Existing Customer - Replacement',
    'Existing Customer - Downgrade',
    'Renewal - Upsell',
    'Renewal - Renewal'
);

-- Lead source enum (reusing from contacts but extending)
CREATE TYPE opportunity_lead_source_enum AS ENUM (
    'Web',
    'Phone Inquiry',
    'Partner Referral',
    'Purchased List',
    'Trade Show',
    'Word of mouth',
    'Email',
    'Other'
);

-- Forecast category enum
CREATE TYPE forecast_category_enum AS ENUM (
    'Omitted',
    'Pipeline',
    'Best Case',
    'Commit',
    'Closed'
);

-- ============================================================================
-- 2. CREATE OPPORTUNITIES TABLE
-- ============================================================================

CREATE TABLE opportunities (
    id SERIAL PRIMARY KEY,
    
    -- Required fields
    name VARCHAR(255) NOT NULL,
    account_id INTEGER NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    
    -- Financial information
    amount DECIMAL(15,2),
    currency currency_enum DEFAULT 'USD',
    
    -- Timeline and stage
    close_date DATE,
    stage_name opportunity_stage_enum DEFAULT 'Prospecting',
    probability FLOAT DEFAULT 0 CHECK (probability >= 0 AND probability <= 100),
    
    -- Classification
    type opportunity_type_enum,
    lead_source opportunity_lead_source_enum,
    forecast_category forecast_category_enum DEFAULT 'Pipeline',
    
    -- Privacy and access
    is_private BOOLEAN DEFAULT FALSE,
    
    -- Descriptive fields
    description TEXT,
    next_step TEXT,
    
    -- Status flags
    is_closed BOOLEAN DEFAULT FALSE,
    is_won BOOLEAN DEFAULT FALSE,
    
    -- Ownership and assignment
    owner_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    
    -- Campaign and marketing
    campaign_id VARCHAR(50),
    
    -- Line items
    has_opportunity_line_item BOOLEAN DEFAULT FALSE,
    items JSONB DEFAULT '[]'::JSONB, -- [{product_name, quantity, unit_price, total_price, description}]
    
    -- Integration fields
    integration_id VARCHAR(100) UNIQUE, -- Salesforce Opportunity ID
    
    -- Custom fields for tenant-specific requirements
    custom_fields JSONB DEFAULT '{}'::JSONB,
    
    -- Status and lifecycle
    status VARCHAR(20) DEFAULT 'OPEN',
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    last_modified_by_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    
    -- Soft delete support
    is_deleted BOOLEAN DEFAULT FALSE,
    
    -- Constraints
    CONSTRAINT valid_amount CHECK (amount IS NULL OR amount >= 0),
    CONSTRAINT valid_close_date CHECK (close_date IS NULL OR close_date >= CURRENT_DATE - INTERVAL '10 years'),
    CONSTRAINT closed_won_logic CHECK (
        (is_closed = FALSE) OR 
        (is_closed = TRUE AND stage_name IN ('Closed Won', 'Closed Lost'))
    ),
    CONSTRAINT won_lost_logic CHECK (
        (is_won = FALSE) OR 
        (is_won = TRUE AND stage_name = 'Closed Won')
    )
);

-- ============================================================================
-- 3. CREATE OPPORTUNITY_CONTACTS JUNCTION TABLE
-- ============================================================================

CREATE TABLE opportunity_contacts (
    id SERIAL PRIMARY KEY,
    opportunity_id INTEGER NOT NULL REFERENCES opportunities(id) ON DELETE CASCADE,
    contact_id INTEGER NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
    role VARCHAR(50), -- Decision Maker, Influencer, Economic Buyer, etc.
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT unique_opportunity_contact UNIQUE(opportunity_id, contact_id)
);

-- ============================================================================
-- 4. CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Primary search indexes
CREATE INDEX idx_opportunities_name ON opportunities USING btree(name);
CREATE INDEX idx_opportunities_name_trgm ON opportunities USING GIN (name gin_trgm_ops);
CREATE INDEX idx_opportunities_account_id ON opportunities USING btree(account_id);
CREATE INDEX idx_opportunities_stage_name ON opportunities USING btree(stage_name);
CREATE INDEX idx_opportunities_type ON opportunities USING btree(type);
CREATE INDEX idx_opportunities_lead_source ON opportunities USING btree(lead_source);
CREATE INDEX idx_opportunities_forecast_category ON opportunities USING btree(forecast_category);
CREATE INDEX idx_opportunities_status ON opportunities USING btree(status);
CREATE INDEX idx_opportunities_is_deleted ON opportunities USING btree(is_deleted);

-- Financial and timeline indexes
CREATE INDEX idx_opportunities_amount ON opportunities USING btree(amount);
CREATE INDEX idx_opportunities_close_date ON opportunities USING btree(close_date);
CREATE INDEX idx_opportunities_probability ON opportunities USING btree(probability);

-- Status and flags indexes
CREATE INDEX idx_opportunities_is_closed ON opportunities USING btree(is_closed);
CREATE INDEX idx_opportunities_is_won ON opportunities USING btree(is_won);
CREATE INDEX idx_opportunities_is_private ON opportunities USING btree(is_private);

-- Integration and ownership indexes
CREATE INDEX idx_opportunities_integration_id ON opportunities USING btree(integration_id);
CREATE INDEX idx_opportunities_owner_id ON opportunities USING btree(owner_id);
CREATE INDEX idx_opportunities_created_by ON opportunities USING btree(created_by_id);

-- Timestamp indexes
CREATE INDEX idx_opportunities_created_at ON opportunities USING btree(created_at);
CREATE INDEX idx_opportunities_updated_at ON opportunities USING btree(updated_at);

-- JSONB indexes for custom fields and items
CREATE INDEX idx_opportunities_custom_fields ON opportunities USING gin(custom_fields);
CREATE INDEX idx_opportunities_items ON opportunities USING gin(items);

-- Composite indexes for common queries
CREATE INDEX idx_opportunities_stage_deleted ON opportunities USING btree(stage_name, is_deleted);
CREATE INDEX idx_opportunities_owner_stage ON opportunities USING btree(owner_id, stage_name) WHERE is_deleted = FALSE;
CREATE INDEX idx_opportunities_account_stage ON opportunities USING btree(account_id, stage_name) WHERE is_deleted = FALSE;
CREATE INDEX idx_opportunities_close_date_stage ON opportunities USING btree(close_date, stage_name) WHERE is_deleted = FALSE;

-- Junction table indexes
CREATE INDEX idx_opportunity_contacts_opportunity_id ON opportunity_contacts USING btree(opportunity_id);
CREATE INDEX idx_opportunity_contacts_contact_id ON opportunity_contacts USING btree(contact_id);
CREATE INDEX idx_opportunity_contacts_role ON opportunity_contacts USING btree(role);
CREATE INDEX idx_opportunity_contacts_is_primary ON opportunity_contacts USING btree(is_primary);

-- ============================================================================
-- 5. CREATE TRIGGERS
-- ============================================================================

-- Apply updated_at trigger to opportunities table
CREATE TRIGGER update_opportunities_updated_at 
    BEFORE UPDATE ON opportunities 
    FOR EACH ROW 
    EXECUTE PROCEDURE update_updated_at_column();

-- Function to automatically calculate probability based on stage
CREATE OR REPLACE FUNCTION update_opportunity_probability()
RETURNS TRIGGER AS $$
BEGIN
    -- Auto-calculate probability based on stage if not explicitly set
    IF NEW.stage_name IS DISTINCT FROM OLD.stage_name OR NEW.probability IS NULL THEN
        NEW.probability := CASE NEW.stage_name
            WHEN 'Prospecting' THEN 10
            WHEN 'Qualification' THEN 20
            WHEN 'Needs Analysis' THEN 30
            WHEN 'Value Proposition' THEN 40
            WHEN 'Id. Decision Makers' THEN 50
            WHEN 'Perception Analysis' THEN 60
            WHEN 'Proposal/Price Quote' THEN 70
            WHEN 'Negotiation/Review' THEN 80
            WHEN 'Closed Won' THEN 100
            WHEN 'Closed Lost' THEN 0
            ELSE COALESCE(NEW.probability, 0)
        END;
    END IF;
    
    -- Auto-set closed flags based on stage
    IF NEW.stage_name IN ('Closed Won', 'Closed Lost') THEN
        NEW.is_closed := TRUE;
        NEW.is_won := (NEW.stage_name = 'Closed Won');
        NEW.forecast_category := 'Closed';
    ELSE
        NEW.is_closed := FALSE;
        NEW.is_won := FALSE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply probability calculation trigger
CREATE TRIGGER update_opportunity_probability_trigger
    BEFORE INSERT OR UPDATE ON opportunities
    FOR EACH ROW
    EXECUTE PROCEDURE update_opportunity_probability();

-- ============================================================================
-- 6. COMMENTS
-- ============================================================================

COMMENT ON TABLE opportunities IS 'Opportunity management with Salesforce-compatible structure';
COMMENT ON COLUMN opportunities.name IS 'Opportunity name/title';
COMMENT ON COLUMN opportunities.account_id IS 'Associated account (required)';
COMMENT ON COLUMN opportunities.amount IS 'Expected revenue amount';
COMMENT ON COLUMN opportunities.close_date IS 'Expected close date';
COMMENT ON COLUMN opportunities.stage_name IS 'Current sales stage';
COMMENT ON COLUMN opportunities.probability IS 'Win probability percentage (0-100)';
COMMENT ON COLUMN opportunities.items IS 'Line items array with product details';
COMMENT ON COLUMN opportunities.integration_id IS 'External system ID (e.g., Salesforce)';
COMMENT ON COLUMN opportunities.custom_fields IS 'Tenant-specific custom fields';

COMMENT ON TABLE opportunity_contacts IS 'Many-to-many relationship between opportunities and contacts';
COMMENT ON COLUMN opportunity_contacts.role IS 'Contact role in the opportunity (Decision Maker, Influencer, etc.)';
COMMENT ON COLUMN opportunity_contacts.is_primary IS 'Primary contact flag';