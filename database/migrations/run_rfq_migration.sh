#!/bin/bash

# RFQ Management Migration Runner
# This script applies the RFQ management database migration

set -e  # Exit on any error

# Configuration
DB_NAME="${DB_NAME:-vendorms}"
DB_USER="${DB_USER:-postgres}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting RFQ Management Migration...${NC}"

# Check if PostgreSQL is running
if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" > /dev/null 2>&1; then
    echo -e "${RED}Error: PostgreSQL is not running or not accessible${NC}"
    echo "Please ensure PostgreSQL is running and accessible at $DB_HOST:$DB_PORT"
    exit 1
fi

# Check if database exists
if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    echo -e "${RED}Error: Database '$DB_NAME' does not exist${NC}"
    echo "Please create the database first or run the main schema setup"
    exit 1
fi

# Run the migration
echo -e "${YELLOW}Applying RFQ management migration...${NC}"

if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$(dirname "$0")/002_create_rfq_management_tables.sql"; then
    echo -e "${GREEN}✓ RFQ management migration completed successfully${NC}"
    
    # Verify tables were created
    echo -e "${YELLOW}Verifying table creation...${NC}"
    
    TABLES=("rfqs" "rfq_invitations" "rfq_submissions" "bid_items" "client_quotes" "ai_recommendations" "rfq_analytics")
    
    for table in "${TABLES[@]}"; do
        if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dt $table" | grep -q "$table"; then
            echo -e "${GREEN}✓ Table '$table' created successfully${NC}"
        else
            echo -e "${RED}✗ Table '$table' was not created${NC}"
        fi
    done
    
    # Verify views were created
    echo -e "${YELLOW}Verifying view creation...${NC}"
    
    VIEWS=("rfq_summary" "vendor_submission_performance")
    
    for view in "${VIEWS[@]}"; do
        if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dv $view" | grep -q "$view"; then
            echo -e "${GREEN}✓ View '$view' created successfully${NC}"
        else
            echo -e "${RED}✗ View '$view' was not created${NC}"
        fi
    done
    
    # Show table counts
    echo -e "${YELLOW}Table information:${NC}"
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            tableowner
        FROM pg_tables 
        WHERE tablename IN ('rfqs', 'rfq_invitations', 'rfq_submissions', 'bid_items', 'client_quotes', 'ai_recommendations', 'rfq_analytics')
        ORDER BY tablename;
    "
    
    echo -e "${GREEN}RFQ Management migration completed successfully!${NC}"
    echo -e "${YELLOW}Next steps:${NC}"
    echo "1. Update your application models to use the new tables"
    echo "2. Implement the RFQ controllers and services"
    echo "3. Create the frontend components for RFQ management"
    
else
    echo -e "${RED}✗ Migration failed${NC}"
    exit 1
fi