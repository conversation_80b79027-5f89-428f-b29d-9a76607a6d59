-- Migration: Create Accounts Table
-- Description: Add Account management functionality with Salesforce-aligned structure
-- Date: 2025-01-29

-- ============================================================================
-- 1. CREATE ACCOUNT STATUS ENUM
-- ============================================================================

-- Account status enum
CREATE TYPE account_status_enum AS ENUM ('ACTIVE', 'INACTIVE');

-- ============================================================================
-- 2. CREATE ACCOUNTS TABLE
-- ============================================================================

CREATE TABLE accounts (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    account_number VARCHAR(100),
    type VARCHAR(50), -- Prospect, Customer - Direct, Customer - Channel, etc.
    industry VARCHAR(100), -- Banking, IT, Manufacturing, Healthcare, etc.
    annual_revenue DECIMAL(15,2),
    number_of_employees INTEGER,
    ownership VARCHAR(50), -- Public, Private, Subsidiary, Other
    phone VARCHAR(50),
    fax VARCHAR(50),
    website VARCHAR(255),
    ticker_symbol VARCHAR(10),
    site VARCHAR(255),
    rating VARCHAR(20), -- Hot, Warm, Cold
    description TEXT,
    billing_address JSONB, -- {street, city, state, postalCode, country, latitude, longitude}
    shipping_address JSONB, -- Similar composite structure
    parent_account_id INTEGER REFERENCES accounts(id),
    owner_id INTEGER REFERENCES users(id),
    integration_id VARCHAR(100), -- Salesforce Record ID for sync
    custom_fields JSONB DEFAULT '{}'::JSONB,
    status account_status_enum DEFAULT 'ACTIVE',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id INTEGER REFERENCES users(id),
    last_modified_by_id INTEGER REFERENCES users(id),
    is_deleted BOOLEAN DEFAULT FALSE,
    
    -- Constraints
    CONSTRAINT no_self_parent CHECK (id != parent_account_id),
    CONSTRAINT valid_annual_revenue CHECK (annual_revenue IS NULL OR annual_revenue >= 0),
    CONSTRAINT valid_employee_count CHECK (number_of_employees IS NULL OR number_of_employees >= 0)
);

-- ============================================================================
-- 3. CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Primary search indexes
CREATE INDEX idx_accounts_name ON accounts USING btree(name);
CREATE INDEX idx_accounts_name_trgm ON accounts USING GIN (name gin_trgm_ops);
CREATE INDEX idx_accounts_industry ON accounts USING btree(industry);
CREATE INDEX idx_accounts_type ON accounts USING btree(type);
CREATE INDEX idx_accounts_rating ON accounts USING btree(rating);
CREATE INDEX idx_accounts_ownership ON accounts USING btree(ownership);
CREATE INDEX idx_accounts_status ON accounts USING btree(status);
CREATE INDEX idx_accounts_is_deleted ON accounts USING btree(is_deleted);

-- Financial and size indexes
CREATE INDEX idx_accounts_annual_revenue ON accounts USING btree(annual_revenue);
CREATE INDEX idx_accounts_number_of_employees ON accounts USING btree(number_of_employees);

-- Hierarchy indexes
CREATE INDEX idx_accounts_parent ON accounts USING btree(parent_account_id);

-- Address search indexes (JSONB)
CREATE INDEX idx_accounts_billing_address ON accounts USING gin(billing_address);
CREATE INDEX idx_accounts_shipping_address ON accounts USING gin(shipping_address);

-- Integration and ownership indexes
CREATE INDEX idx_accounts_integration_id ON accounts USING btree(integration_id);
CREATE INDEX idx_accounts_owner_id ON accounts USING btree(owner_id);
CREATE INDEX idx_accounts_created_by ON accounts USING btree(created_by_id);

-- Timestamp indexes
CREATE INDEX idx_accounts_created_at ON accounts USING btree(created_at);
CREATE INDEX idx_accounts_updated_at ON accounts USING btree(updated_at);

-- Composite indexes for common queries
CREATE INDEX idx_accounts_status_deleted ON accounts USING btree(status, is_deleted);
CREATE INDEX idx_accounts_industry_status ON accounts USING btree(industry, status) WHERE is_deleted = FALSE;
CREATE INDEX idx_accounts_type_status ON accounts USING btree(type, status) WHERE is_deleted = FALSE;

-- ============================================================================
-- 4. CREATE TRIGGERS
-- ============================================================================

-- Apply updated_at trigger to accounts table
CREATE TRIGGER update_accounts_updated_at 
    BEFORE UPDATE ON accounts 
    FOR EACH ROW 
    EXECUTE PROCEDURE update_updated_at_column();

-- Function to automatically log account changes
CREATE OR REPLACE FUNCTION log_account_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' THEN
        INSERT INTO audits (entity_type, entity_id, action, user_id, old_value, new_value, timestamp)
        VALUES ('accounts', NEW.id, 'update', 
                COALESCE(CURRENT_SETTING('app.current_user_id', true)::INTEGER, 1), 
                row_to_json(OLD)::JSONB, row_to_json(NEW)::JSONB, CURRENT_TIMESTAMP);
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audits (entity_type, entity_id, action, user_id, new_value, timestamp)
        VALUES ('accounts', NEW.id, 'create', 
                COALESCE(CURRENT_SETTING('app.current_user_id', true)::INTEGER, 1), 
                row_to_json(NEW)::JSONB, CURRENT_TIMESTAMP);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audits (entity_type, entity_id, action, user_id, old_value, timestamp)
        VALUES ('accounts', OLD.id, 'delete', 
                COALESCE(CURRENT_SETTING('app.current_user_id', true)::INTEGER, 1), 
                row_to_json(OLD)::JSONB, CURRENT_TIMESTAMP);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply audit trigger to accounts table
CREATE TRIGGER account_audit_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON accounts 
    FOR EACH ROW 
    EXECUTE PROCEDURE log_account_changes();

-- ============================================================================
-- 5. CREATE VIEWS
-- ============================================================================

-- View: Account Summary with hierarchy information
CREATE VIEW account_summary AS
SELECT 
    a.id,
    a.name,
    a.account_number,
    a.type,
    a.industry,
    a.annual_revenue,
    a.number_of_employees,
    a.ownership,
    a.rating,
    a.status,
    a.parent_account_id,
    parent.name as parent_name,
    a.owner_id,
    owner.email as owner_email,
    a.created_at,
    a.updated_at,
    (SELECT COUNT(*) FROM accounts child 
     WHERE child.parent_account_id = a.id AND child.is_deleted = FALSE) as children_count,
    a.billing_address->>'country' as billing_country,
    a.billing_address->>'state' as billing_state,
    a.billing_address->>'city' as billing_city,
    a.shipping_address->>'country' as shipping_country,
    a.shipping_address->>'state' as shipping_state,
    a.shipping_address->>'city' as shipping_city
FROM accounts a
LEFT JOIN accounts parent ON a.parent_account_id = parent.id
LEFT JOIN users owner ON a.owner_id = owner.id
WHERE a.is_deleted = FALSE;

COMMENT ON VIEW account_summary IS 'Account summary with hierarchy and address information';

-- ============================================================================
-- 6. INSERT DEFAULT DATA
-- ============================================================================

-- Insert default account types (matching Salesforce picklist values)
INSERT INTO system_settings (key, value, updated_by) VALUES 
('account_types', '[
    "Prospect",
    "Customer - Direct", 
    "Customer - Channel",
    "Channel Partner / Reseller",
    "Installation Partner",
    "Technology Partner",
    "Other"
]'::JSONB, 1)
ON CONFLICT (key) DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = CURRENT_TIMESTAMP;

-- Insert default industries (matching Salesforce picklist values)
INSERT INTO system_settings (key, value, updated_by) VALUES 
('account_industries', '[
    "Agriculture",
    "Apparel", 
    "Banking",
    "Biotechnology",
    "Chemicals",
    "Communications",
    "Construction",
    "Consulting",
    "Education",
    "Electronics",
    "Energy",
    "Engineering",
    "Entertainment",
    "Environmental",
    "Finance",
    "Food & Beverage",
    "Government",
    "Healthcare",
    "Hospitality",
    "Insurance",
    "Machinery",
    "Manufacturing",
    "Media",
    "Not For Profit",
    "Recreation",
    "Retail",
    "Shipping",
    "Technology",
    "Telecommunications",
    "Transportation",
    "Utilities",
    "Other"
]'::JSONB, 1)
ON CONFLICT (key) DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = CURRENT_TIMESTAMP;

-- Insert default ownership types
INSERT INTO system_settings (key, value, updated_by) VALUES 
('account_ownership_types', '[
    "Public",
    "Private", 
    "Subsidiary",
    "Other"
]'::JSONB, 1)
ON CONFLICT (key) DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = CURRENT_TIMESTAMP;

-- Insert default rating values
INSERT INTO system_settings (key, value, updated_by) VALUES 
('account_ratings', '[
    "Hot",
    "Warm",
    "Cold"
]'::JSONB, 1)
ON CONFLICT (key) DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = CURRENT_TIMESTAMP;

-- ============================================================================
-- 7. COMMENTS AND DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE accounts IS 'Client account management with Salesforce-aligned structure for CRM integration';
COMMENT ON COLUMN accounts.name IS 'Account name (required, matches Salesforce Name field)';
COMMENT ON COLUMN accounts.account_number IS 'Optional account number for reference (matches Salesforce AccountNumber)';
COMMENT ON COLUMN accounts.type IS 'Account type from picklist (Prospect, Customer, etc.)';
COMMENT ON COLUMN accounts.industry IS 'Industry classification from picklist';
COMMENT ON COLUMN accounts.annual_revenue IS 'Annual revenue in decimal format';
COMMENT ON COLUMN accounts.number_of_employees IS 'Number of employees (integer)';
COMMENT ON COLUMN accounts.ownership IS 'Ownership type (Public, Private, etc.)';
COMMENT ON COLUMN accounts.phone IS 'Primary phone number';
COMMENT ON COLUMN accounts.fax IS 'Fax number';
COMMENT ON COLUMN accounts.website IS 'Company website URL';
COMMENT ON COLUMN accounts.ticker_symbol IS 'Stock ticker symbol for public companies';
COMMENT ON COLUMN accounts.site IS 'Site or location identifier';
COMMENT ON COLUMN accounts.rating IS 'Account rating (Hot, Warm, Cold)';
COMMENT ON COLUMN accounts.description IS 'Account description and notes';
COMMENT ON COLUMN accounts.billing_address IS 'Billing address in JSONB format with street, city, state, postalCode, country, latitude, longitude';
COMMENT ON COLUMN accounts.shipping_address IS 'Shipping address in JSONB format with same structure as billing_address';
COMMENT ON COLUMN accounts.parent_account_id IS 'Parent account for hierarchy (self-referential foreign key)';
COMMENT ON COLUMN accounts.owner_id IS 'Account owner (user responsible for this account)';
COMMENT ON COLUMN accounts.integration_id IS 'External system integration ID (e.g., Salesforce record ID)';
COMMENT ON COLUMN accounts.custom_fields IS 'Custom fields in JSONB format for extensibility';
COMMENT ON COLUMN accounts.status IS 'Account status (ACTIVE, INACTIVE)';
COMMENT ON COLUMN accounts.created_by_id IS 'User who created this account';
COMMENT ON COLUMN accounts.last_modified_by_id IS 'User who last modified this account';
COMMENT ON COLUMN accounts.is_deleted IS 'Soft delete flag';

-- ============================================================================
-- 8. GRANT PERMISSIONS
-- ============================================================================

-- Grant appropriate permissions (adjust based on your user roles)
-- GRANT SELECT, INSERT, UPDATE ON accounts TO vendorms_app_role;
-- GRANT USAGE ON SEQUENCE accounts_id_seq TO vendorms_app_role;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Log migration completion
INSERT INTO audits (entity_type, entity_id, action, user_id, new_value, details, timestamp)
VALUES ('system', 0, 'create', 1, 
        '{"migration": "004_create_accounts_table"}'::JSONB,
        '{"description": "Created accounts table with Salesforce-aligned structure"}'::JSONB,
        CURRENT_TIMESTAMP);

COMMIT;