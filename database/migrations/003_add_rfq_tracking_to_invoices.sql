-- Migration: Add RFQ Tracking to Invoices
-- Phase 2: Invoice Integration with RFQ Management Module
-- Date: 2024-01-28

-- ============================================================================
-- 1. ADD RFQ TRACKING COLUMNS TO INVOICES TABLE
-- ============================================================================

-- Add RFQ tracking fields to existing invoices table
ALTER TABLE invoices 
ADD COLUMN IF NOT EXISTS rfq_id INTEGER REFERENCES rfqs(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS rfq_quote_id INTEGER REFERENCES client_quotes(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS invoice_created BOOLEAN DEFAULT FALSE;

-- Add comments for new columns
COMMENT ON COLUMN invoices.rfq_id IS 'Link to the original RFQ that generated this invoice';
COMMENT ON COLUMN invoices.rfq_quote_id IS 'Link to the client quote that generated this invoice';
COMMENT ON COLUMN invoices.notes IS 'Additional notes about the invoice, especially for RFQ-generated invoices';
COMMENT ON COLUMN invoices.invoice_created IS 'Flag to track if invoice was created from quote';

-- ============================================================================
-- 2. ADD INVOICE TRACKING TO CLIENT QUOTES TABLE
-- ============================================================================

-- Add invoice tracking field to client quotes
ALTER TABLE client_quotes 
ADD COLUMN IF NOT EXISTS invoice_created BOOLEAN DEFAULT FALSE;

COMMENT ON COLUMN client_quotes.invoice_created IS 'Flag to track if invoice has been created from this quote';

-- ============================================================================
-- 3. CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Indexes for RFQ-Invoice relationships
CREATE INDEX IF NOT EXISTS idx_invoices_rfq_id ON invoices (rfq_id);
CREATE INDEX IF NOT EXISTS idx_invoices_rfq_quote_id ON invoices (rfq_quote_id);
CREATE INDEX IF NOT EXISTS idx_invoices_rfq_tracking ON invoices (rfq_id, rfq_quote_id) WHERE rfq_id IS NOT NULL;

-- Index for quote invoice tracking
CREATE INDEX IF NOT EXISTS idx_client_quotes_invoice_created ON client_quotes (invoice_created);

-- ============================================================================
-- 4. GRANT PERMISSIONS
-- ============================================================================

-- Grant permissions on updated tables
GRANT SELECT, INSERT, UPDATE, DELETE ON invoices TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON client_quotes TO PUBLIC;

COMMIT;
</content>
</file></script>

Now I'll run this migration to add the RFQ tracking fields to the invoices table:

<function_calls>
<invoke name="executeBash">
<parameter name="command">cd database/migrations && psql -h localhost -U postgres -d vendordb -f 003_add_rfq_tracking_to_invoices.sql