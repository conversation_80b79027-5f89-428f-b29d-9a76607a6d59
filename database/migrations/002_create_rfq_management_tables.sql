-- Migration: Create RFQ Management Tables
-- Phase 1: Database Schema Setup for RFQ Management Module
-- Date: 2024-01-28

-- ============================================================================
-- 1. CUSTOM TYPES AND ENUMS FOR RFQ MODULE
-- ============================================================================

-- RFQ lifecycle statuses
CREATE TYPE rfq_status_enum AS ENUM ('draft', 'sent', 'in_progress', 'closed', 'cancelled');

-- RFQ invitation statuses
CREATE TYPE invitation_status_enum AS ENUM ('pending', 'sent', 'viewed', 'submitted', 'expired');

-- Quote statuses
CREATE TYPE quote_status_enum AS ENUM ('draft', 'sent', 'viewed', 'approved', 'rejected', 'expired');

-- ============================================================================
-- 2. CORE RFQ TABLES
-- ============================================================================

-- RFQs Table (Main RFQ Management)
CREATE TABLE rfqs (
    id SERIAL PRIMARY KEY,
    creator_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    items JSONB NOT NULL DEFAULT '[]'::JSONB,  -- [{id, name, description, quantity, specifications, category, estimatedPrice}]
    due_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status rfq_status_enum DEFAULT 'draft',
    form_config JSONB DEFAULT '[]'::JSONB,  -- [{id, type, label, required, options, validation, itemSpecific}]
    ai_settings JSONB DEFAULT '{}'::JSONB,  -- {priceWeight, performanceWeight, deliveryWeight, riskWeight, diversificationPreference}
    terms TEXT,
    currency currency_enum DEFAULT 'USD',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE rfqs IS 'Request for Quote management with configurable forms and AI settings';
COMMENT ON COLUMN rfqs.items IS 'Array of RFQ items with specifications and requirements';
COMMENT ON COLUMN rfqs.form_config IS 'Dynamic form field configuration for vendor submissions';
COMMENT ON COLUMN rfqs.ai_settings IS 'AI recommendation engine configuration and weights';
COMMENT ON COLUMN rfqs.terms IS 'Terms and conditions for the RFQ';

-- RFQ Invitations Table (Vendor Invitations)
CREATE TABLE rfq_invitations (
    id SERIAL PRIMARY KEY,
    rfq_id INTEGER NOT NULL REFERENCES rfqs(id) ON DELETE CASCADE,
    vendor_id INTEGER NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    token VARCHAR(128) UNIQUE NOT NULL,  -- Secure token for public access
    status invitation_status_enum DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    viewed_at TIMESTAMP WITH TIME ZONE,
    submitted_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    email_sent_count INTEGER DEFAULT 0,
    last_reminder_sent TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(rfq_id, vendor_id)  -- One invitation per vendor per RFQ
);

COMMENT ON TABLE rfq_invitations IS 'RFQ invitations sent to vendors with secure token access';
COMMENT ON COLUMN rfq_invitations.token IS 'Secure token for public submission access without authentication';
COMMENT ON COLUMN rfq_invitations.expires_at IS 'Token expiration timestamp (typically matches RFQ due_date)';
COMMENT ON COLUMN rfq_invitations.email_sent_count IS 'Number of invitation emails sent (including reminders)';

-- RFQ Submissions Table (Vendor Bids)
CREATE TABLE rfq_submissions (
    id SERIAL PRIMARY KEY,
    rfq_id INTEGER NOT NULL REFERENCES rfqs(id) ON DELETE CASCADE,
    vendor_id INTEGER NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    invitation_token VARCHAR(128) NOT NULL REFERENCES rfq_invitations(token),
    bid_data JSONB NOT NULL DEFAULT '{}'::JSONB,  -- {generalTerms, deliverySchedule, paymentTerms, validityPeriod, additionalNotes}
    attachments JSONB DEFAULT '[]'::JSONB,  -- [{filename, originalName, fileSize, mimeType, filePath, uploadedAt}]
    total_amount DECIMAL(15, 2) NOT NULL DEFAULT 0.00 CHECK (total_amount >= 0),
    currency currency_enum DEFAULT 'USD',
    delivery_days INTEGER,  -- Overall delivery timeframe
    payment_terms TEXT,
    validity_period INTEGER DEFAULT 30,  -- Days the bid is valid
    additional_notes TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(rfq_id, vendor_id)  -- One submission per vendor per RFQ
);

COMMENT ON TABLE rfq_submissions IS 'Vendor bid submissions with pricing and terms';
COMMENT ON COLUMN rfq_submissions.bid_data IS 'Structured bid data including terms and conditions';
COMMENT ON COLUMN rfq_submissions.attachments IS 'Array of file attachments with metadata';
COMMENT ON COLUMN rfq_submissions.validity_period IS 'Number of days the bid remains valid';

-- Bid Items Table (Individual Item Bids)
CREATE TABLE bid_items (
    id SERIAL PRIMARY KEY,
    submission_id INTEGER NOT NULL REFERENCES rfq_submissions(id) ON DELETE CASCADE,
    rfq_item_id VARCHAR(50) NOT NULL,  -- References the item ID from RFQ items JSONB
    item_name VARCHAR(255) NOT NULL,
    unit_price DECIMAL(15, 2) NOT NULL CHECK (unit_price >= 0),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    total_price DECIMAL(15, 2) NOT NULL CHECK (total_price >= 0),
    delivery_days INTEGER,  -- Item-specific delivery time
    specifications JSONB DEFAULT '{}'::JSONB,  -- Item-specific specifications and responses
    alternatives JSONB DEFAULT '[]'::JSONB,  -- [{description, unitPrice, totalPrice, notes}]
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(submission_id, rfq_item_id)  -- One bid per item per submission
);

COMMENT ON TABLE bid_items IS 'Individual item bids within vendor submissions';
COMMENT ON COLUMN bid_items.rfq_item_id IS 'References the item ID from the RFQ items array';
COMMENT ON COLUMN bid_items.specifications IS 'Item-specific responses to RFQ specifications';
COMMENT ON COLUMN bid_items.alternatives IS 'Alternative options proposed by vendor';

-- Client Quotes Table (Generated Quotes)
CREATE TABLE client_quotes (
    id SERIAL PRIMARY KEY,
    rfq_id INTEGER NOT NULL REFERENCES rfqs(id) ON DELETE CASCADE,
    creator_id INTEGER NOT NULL REFERENCES users(id),
    title VARCHAR(255) NOT NULL,
    selected_bids JSONB NOT NULL DEFAULT '{}'::JSONB,  -- {itemId: {submissionId, vendorId, unitPrice, totalPrice, rationale}}
    total_amount DECIMAL(15, 2) NOT NULL DEFAULT 0.00 CHECK (total_amount >= 0),
    currency currency_enum DEFAULT 'USD',
    margin_percentage DECIMAL(5, 2) DEFAULT 0.00,  -- Markup percentage
    taxes DECIMAL(15, 2) DEFAULT 0.00,
    terms TEXT,
    notes TEXT,
    status quote_status_enum DEFAULT 'draft',
    public_token VARCHAR(128) UNIQUE,  -- Token for client approval access
    client_email VARCHAR(255),
    client_name VARCHAR(255),
    sent_at TIMESTAMP WITH TIME ZONE,
    viewed_at TIMESTAMP WITH TIME ZONE,
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by_name VARCHAR(255),
    approved_by_email VARCHAR(255),
    approval_signature TEXT,  -- Digital signature data
    invoice_id INTEGER REFERENCES invoices(id),  -- Link to generated invoice
    expires_at TIMESTAMP WITH TIME ZONE,
    pdf_path TEXT,  -- Path to generated PDF
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE client_quotes IS 'Generated quotes for clients based on selected vendor bids';
COMMENT ON COLUMN client_quotes.selected_bids IS 'Selected vendor bids for each item with rationale';
COMMENT ON COLUMN client_quotes.public_token IS 'Secure token for client approval access';
COMMENT ON COLUMN client_quotes.margin_percentage IS 'Markup percentage applied to vendor costs';
COMMENT ON COLUMN client_quotes.approval_signature IS 'Digital signature data for quote approval';
COMMENT ON COLUMN client_quotes.version IS 'Quote version for change tracking';

-- AI Recommendations Table (AI-Generated Vendor Recommendations)
CREATE TABLE ai_recommendations (
    id SERIAL PRIMARY KEY,
    rfq_id INTEGER NOT NULL REFERENCES rfqs(id) ON DELETE CASCADE,
    item_id VARCHAR(50) NOT NULL,  -- References RFQ item ID
    recommended_vendor_id INTEGER NOT NULL REFERENCES vendors(id),
    recommended_submission_id INTEGER NOT NULL REFERENCES rfq_submissions(id),
    confidence DECIMAL(3, 2) NOT NULL CHECK (confidence BETWEEN 0 AND 1),
    factors JSONB NOT NULL DEFAULT '{}'::JSONB,  -- {price: {weight, score, description}, performance: {...}, delivery: {...}, risk: {...}}
    rationale TEXT NOT NULL,
    cost_savings DECIMAL(15, 2) DEFAULT 0.00,
    risk_score DECIMAL(3, 2) CHECK (risk_score BETWEEN 0 AND 1),
    model_version VARCHAR(50),
    accepted BOOLEAN,  -- Whether recommendation was accepted by user
    override_reason TEXT,  -- Reason if recommendation was overridden
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE ai_recommendations IS 'AI-generated vendor selection recommendations';
COMMENT ON COLUMN ai_recommendations.factors IS 'Detailed scoring factors used in recommendation';
COMMENT ON COLUMN ai_recommendations.confidence IS 'AI confidence score (0-1)';
COMMENT ON COLUMN ai_recommendations.accepted IS 'Whether the user accepted the AI recommendation';

-- RFQ Analytics Table (Performance Metrics)
CREATE TABLE rfq_analytics (
    id SERIAL PRIMARY KEY,
    rfq_id INTEGER NOT NULL REFERENCES rfqs(id) ON DELETE CASCADE,
    total_invitations INTEGER NOT NULL DEFAULT 0,
    total_submissions INTEGER NOT NULL DEFAULT 0,
    response_rate DECIMAL(5, 2) DEFAULT 0.00,  -- Percentage
    average_response_time_hours INTEGER,
    cost_savings_amount DECIMAL(15, 2) DEFAULT 0.00,
    cost_savings_percentage DECIMAL(5, 2) DEFAULT 0.00,
    lowest_bid_amount DECIMAL(15, 2),
    highest_bid_amount DECIMAL(15, 2),
    average_bid_amount DECIMAL(15, 2),
    selected_total_amount DECIMAL(15, 2),
    vendor_participation JSONB DEFAULT '{}'::JSONB,  -- {vendorId: {invited, submitted, selected, responseTime}}
    item_analysis JSONB DEFAULT '{}'::JSONB,  -- {itemId: {bidCount, avgPrice, selectedPrice, savings}}
    timeline_metrics JSONB DEFAULT '{}'::JSONB,  -- {created, sent, firstSubmission, lastSubmission, closed}
    ai_accuracy DECIMAL(3, 2),  -- How often AI recommendations were accepted
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(rfq_id)  -- One analytics record per RFQ
);

COMMENT ON TABLE rfq_analytics IS 'Comprehensive analytics and metrics for RFQ performance';
COMMENT ON COLUMN rfq_analytics.response_rate IS 'Percentage of invited vendors who submitted bids';
COMMENT ON COLUMN rfq_analytics.vendor_participation IS 'Detailed vendor participation metrics';
COMMENT ON COLUMN rfq_analytics.item_analysis IS 'Per-item bidding analysis and savings';
COMMENT ON COLUMN rfq_analytics.ai_accuracy IS 'Percentage of AI recommendations that were accepted';

-- ============================================================================
-- 3. INDEXES FOR PERFORMANCE OPTIMIZATION
-- ============================================================================

-- RFQs indexes
CREATE INDEX idx_rfqs_creator_status ON rfqs (creator_id, status);
CREATE INDEX idx_rfqs_due_date ON rfqs (due_date);
CREATE INDEX idx_rfqs_status ON rfqs (status);
CREATE INDEX idx_rfqs_created_at ON rfqs (created_at DESC);
CREATE INDEX idx_rfqs_items_gin ON rfqs USING GIN (items);

-- RFQ Invitations indexes
CREATE INDEX idx_rfq_invitations_rfq_status ON rfq_invitations (rfq_id, status);
CREATE INDEX idx_rfq_invitations_vendor ON rfq_invitations (vendor_id);
CREATE INDEX idx_rfq_invitations_token ON rfq_invitations (token);
CREATE INDEX idx_rfq_invitations_expires ON rfq_invitations (expires_at);
CREATE INDEX idx_rfq_invitations_sent_at ON rfq_invitations (sent_at);

-- RFQ Submissions indexes
CREATE INDEX idx_rfq_submissions_rfq ON rfq_submissions (rfq_id);
CREATE INDEX idx_rfq_submissions_vendor ON rfq_submissions (vendor_id);
CREATE INDEX idx_rfq_submissions_token ON rfq_submissions (invitation_token);
CREATE INDEX idx_rfq_submissions_submitted_at ON rfq_submissions (submitted_at);
CREATE INDEX idx_rfq_submissions_total_amount ON rfq_submissions (total_amount);

-- Bid Items indexes
CREATE INDEX idx_bid_items_submission ON bid_items (submission_id);
CREATE INDEX idx_bid_items_rfq_item ON bid_items (rfq_item_id);
CREATE INDEX idx_bid_items_unit_price ON bid_items (unit_price);
CREATE INDEX idx_bid_items_total_price ON bid_items (total_price);

-- Client Quotes indexes
CREATE INDEX idx_client_quotes_rfq ON client_quotes (rfq_id);
CREATE INDEX idx_client_quotes_creator ON client_quotes (creator_id);
CREATE INDEX idx_client_quotes_status ON client_quotes (status);
CREATE INDEX idx_client_quotes_token ON client_quotes (public_token);
CREATE INDEX idx_client_quotes_client_email ON client_quotes (client_email);
CREATE INDEX idx_client_quotes_created_at ON client_quotes (created_at DESC);
CREATE INDEX idx_client_quotes_expires_at ON client_quotes (expires_at);

-- AI Recommendations indexes
CREATE INDEX idx_ai_recommendations_rfq ON ai_recommendations (rfq_id);
CREATE INDEX idx_ai_recommendations_item ON ai_recommendations (item_id);
CREATE INDEX idx_ai_recommendations_vendor ON ai_recommendations (recommended_vendor_id);
CREATE INDEX idx_ai_recommendations_confidence ON ai_recommendations (confidence DESC);
CREATE INDEX idx_ai_recommendations_accepted ON ai_recommendations (accepted);

-- RFQ Analytics indexes
CREATE INDEX idx_rfq_analytics_rfq ON rfq_analytics (rfq_id);
CREATE INDEX idx_rfq_analytics_calculated_at ON rfq_analytics (calculated_at DESC);

-- ============================================================================
-- 4. FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to generate secure tokens
CREATE OR REPLACE FUNCTION generate_secure_token(length INTEGER DEFAULT 64)
RETURNS VARCHAR AS $
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    result TEXT := '';
    i INTEGER := 0;
BEGIN
    FOR i IN 1..length LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::INTEGER, 1);
    END LOOP;
    RETURN result;
END;
$ LANGUAGE plpgsql;

-- Function to automatically generate invitation tokens
CREATE OR REPLACE FUNCTION generate_invitation_token()
RETURNS TRIGGER AS $
BEGIN
    IF NEW.token IS NULL OR NEW.token = '' THEN
        NEW.token := generate_secure_token(64);
    END IF;
    
    -- Set expiration to RFQ due date if not set
    IF NEW.expires_at IS NULL THEN
        SELECT due_date INTO NEW.expires_at FROM rfqs WHERE id = NEW.rfq_id;
    END IF;
    
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

-- Function to automatically generate quote tokens
CREATE OR REPLACE FUNCTION generate_quote_token()
RETURNS TRIGGER AS $
BEGIN
    IF NEW.public_token IS NULL OR NEW.public_token = '' THEN
        NEW.public_token := generate_secure_token(64);
    END IF;
    
    -- Set expiration to 30 days from creation if not set
    IF NEW.expires_at IS NULL THEN
        NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '30 days';
    END IF;
    
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

-- Function to update submission totals
CREATE OR REPLACE FUNCTION update_submission_total()
RETURNS TRIGGER AS $
DECLARE
    new_total DECIMAL(15, 2);
BEGIN
    -- Calculate total from bid items
    SELECT COALESCE(SUM(total_price), 0) 
    INTO new_total 
    FROM bid_items 
    WHERE submission_id = COALESCE(NEW.submission_id, OLD.submission_id);
    
    -- Update the submission total
    UPDATE rfq_submissions 
    SET total_amount = new_total, updated_at = CURRENT_TIMESTAMP
    WHERE id = COALESCE(NEW.submission_id, OLD.submission_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$ LANGUAGE plpgsql;

-- Function to update quote totals
CREATE OR REPLACE FUNCTION update_quote_total()
RETURNS TRIGGER AS $
DECLARE
    selected_total DECIMAL(15, 2);
    final_total DECIMAL(15, 2);
BEGIN
    -- Calculate total from selected bids
    SELECT COALESCE(
        (SELECT SUM((value->>'totalPrice')::DECIMAL) 
         FROM jsonb_each(NEW.selected_bids)), 
        0
    ) INTO selected_total;
    
    -- Apply margin and taxes
    final_total := selected_total * (1 + COALESCE(NEW.margin_percentage, 0) / 100) + COALESCE(NEW.taxes, 0);
    
    NEW.total_amount := final_total;
    NEW.updated_at := CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

-- Function to update RFQ status based on submissions
CREATE OR REPLACE FUNCTION update_rfq_status()
RETURNS TRIGGER AS $
DECLARE
    rfq_id_val INTEGER;
    submission_count INTEGER;
    invitation_count INTEGER;
BEGIN
    rfq_id_val := COALESCE(NEW.rfq_id, OLD.rfq_id);
    
    -- Count submissions and invitations
    SELECT COUNT(*) INTO submission_count FROM rfq_submissions WHERE rfq_id = rfq_id_val;
    SELECT COUNT(*) INTO invitation_count FROM rfq_invitations WHERE rfq_id = rfq_id_val;
    
    -- Update RFQ status
    IF submission_count > 0 THEN
        UPDATE rfqs SET status = 'in_progress', updated_at = CURRENT_TIMESTAMP 
        WHERE id = rfq_id_val AND status = 'sent';
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$ LANGUAGE plpgsql;

-- Function to log RFQ audit trail
CREATE OR REPLACE FUNCTION log_rfq_audit()
RETURNS TRIGGER AS $
DECLARE
    action_type audit_action_enum;
    entity_type_val VARCHAR(50);
BEGIN
    -- Determine action type
    IF TG_OP = 'INSERT' THEN
        action_type := 'create';
    ELSIF TG_OP = 'UPDATE' THEN
        action_type := 'update';
    ELSIF TG_OP = 'DELETE' THEN
        action_type := 'delete';
    END IF;
    
    -- Determine entity type
    entity_type_val := TG_TABLE_NAME;
    
    -- Insert audit record
    INSERT INTO audits (
        entity_type, 
        entity_id, 
        action, 
        user_id, 
        old_value, 
        new_value, 
        timestamp
    ) VALUES (
        entity_type_val,
        COALESCE(NEW.id, OLD.id),
        action_type,
        COALESCE(CURRENT_SETTING('app.current_user_id', true)::INTEGER, 1),
        CASE WHEN TG_OP != 'INSERT' THEN row_to_json(OLD)::JSONB ELSE NULL END,
        CASE WHEN TG_OP != 'DELETE' THEN row_to_json(NEW)::JSONB ELSE NULL END,
        CURRENT_TIMESTAMP
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$ LANGUAGE plpgsql;

-- Apply triggers
CREATE TRIGGER rfq_invitation_token_trigger 
    BEFORE INSERT ON rfq_invitations 
    FOR EACH ROW EXECUTE FUNCTION generate_invitation_token();

CREATE TRIGGER client_quote_token_trigger 
    BEFORE INSERT ON client_quotes 
    FOR EACH ROW EXECUTE FUNCTION generate_quote_token();

CREATE TRIGGER bid_items_total_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON bid_items 
    FOR EACH ROW EXECUTE FUNCTION update_submission_total();

CREATE TRIGGER client_quotes_total_trigger 
    BEFORE INSERT OR UPDATE ON client_quotes 
    FOR EACH ROW EXECUTE FUNCTION update_quote_total();

CREATE TRIGGER rfq_submissions_status_trigger 
    AFTER INSERT OR UPDATE ON rfq_submissions 
    FOR EACH ROW EXECUTE FUNCTION update_rfq_status();

-- Apply audit triggers to all RFQ tables
CREATE TRIGGER rfqs_audit_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON rfqs 
    FOR EACH ROW EXECUTE FUNCTION log_rfq_audit();

CREATE TRIGGER rfq_invitations_audit_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON rfq_invitations 
    FOR EACH ROW EXECUTE FUNCTION log_rfq_audit();

CREATE TRIGGER rfq_submissions_audit_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON rfq_submissions 
    FOR EACH ROW EXECUTE FUNCTION log_rfq_audit();

CREATE TRIGGER client_quotes_audit_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON client_quotes 
    FOR EACH ROW EXECUTE FUNCTION log_rfq_audit();

-- Apply updated_at triggers
CREATE TRIGGER update_rfqs_updated_at 
    BEFORE UPDATE ON rfqs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rfq_submissions_updated_at 
    BEFORE UPDATE ON rfq_submissions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_client_quotes_updated_at 
    BEFORE UPDATE ON client_quotes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 5. VIEWS FOR COMMON QUERIES
-- ============================================================================

-- View: RFQ Summary with Statistics
CREATE VIEW rfq_summary AS
SELECT 
    r.id,
    r.title,
    r.description,
    r.status,
    r.due_date,
    r.creator_id,
    u.email as creator_email,
    jsonb_array_length(r.items) as item_count,
    COUNT(DISTINCT ri.id) as invitation_count,
    COUNT(DISTINCT rs.id) as submission_count,
    CASE 
        WHEN COUNT(DISTINCT ri.id) > 0 
        THEN ROUND((COUNT(DISTINCT rs.id)::DECIMAL / COUNT(DISTINCT ri.id)) * 100, 2)
        ELSE 0 
    END as response_rate,
    MIN(rs.total_amount) as lowest_bid,
    MAX(rs.total_amount) as highest_bid,
    AVG(rs.total_amount) as average_bid,
    r.created_at,
    r.updated_at
FROM rfqs r
JOIN users u ON r.creator_id = u.id
LEFT JOIN rfq_invitations ri ON r.id = ri.rfq_id
LEFT JOIN rfq_submissions rs ON r.id = rs.rfq_id
GROUP BY r.id, r.title, r.description, r.status, r.due_date, r.creator_id, u.email, r.items, r.created_at, r.updated_at;

COMMENT ON VIEW rfq_summary IS 'Summary view of RFQs with key statistics';

-- View: Vendor Submission Performance
CREATE VIEW vendor_submission_performance AS
SELECT 
    v.id as vendor_id,
    v.name as vendor_name,
    v.category,
    v.performance_score,
    COUNT(DISTINCT ri.id) as total_invitations,
    COUNT(DISTINCT rs.id) as total_submissions,
    CASE 
        WHEN COUNT(DISTINCT ri.id) > 0 
        THEN ROUND((COUNT(DISTINCT rs.id)::DECIMAL / COUNT(DISTINCT ri.id)) * 100, 2)
        ELSE 0 
    END as response_rate,
    AVG(rs.total_amount) as average_bid_amount,
    AVG(EXTRACT(EPOCH FROM (rs.submitted_at - ri.sent_at))/3600) as avg_response_time_hours,
    COUNT(DISTINCT cq.id) as quotes_won,
    SUM(CASE WHEN cq.id IS NOT NULL THEN rs.total_amount ELSE 0 END) as total_value_won
FROM vendors v
LEFT JOIN rfq_invitations ri ON v.id = ri.vendor_id
LEFT JOIN rfq_submissions rs ON v.id = rs.vendor_id AND ri.rfq_id = rs.rfq_id
LEFT JOIN client_quotes cq ON rs.rfq_id = cq.rfq_id 
    AND cq.selected_bids::TEXT LIKE '%"vendorId":' || v.id || '%'
WHERE v.status = 'active'
GROUP BY v.id, v.name, v.category, v.performance_score;

COMMENT ON VIEW vendor_submission_performance IS 'Vendor performance metrics for RFQ participation';

-- ============================================================================
-- 6. SAMPLE DATA FOR TESTING (Optional - Remove in Production)
-- ============================================================================

-- Insert sample RFQ templates and settings
INSERT INTO system_settings (key, value, updated_by) VALUES
    ('rfq_default_validity_days', '30', 1),
    ('rfq_reminder_days_before_due', '3', 1),
    ('rfq_max_file_size_mb', '10', 1),
    ('rfq_allowed_file_types', '["pdf", "doc", "docx", "xls", "xlsx", "jpg", "png"]', 1)
ON CONFLICT (key) DO NOTHING;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON rfqs TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON rfq_invitations TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON rfq_submissions TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON bid_items TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON client_quotes TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON ai_recommendations TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON rfq_analytics TO PUBLIC;

GRANT USAGE ON SEQUENCE rfqs_id_seq TO PUBLIC;
GRANT USAGE ON SEQUENCE rfq_invitations_id_seq TO PUBLIC;
GRANT USAGE ON SEQUENCE rfq_submissions_id_seq TO PUBLIC;
GRANT USAGE ON SEQUENCE bid_items_id_seq TO PUBLIC;
GRANT USAGE ON SEQUENCE client_quotes_id_seq TO PUBLIC;
GRANT USAGE ON SEQUENCE ai_recommendations_id_seq TO PUBLIC;
GRANT USAGE ON SEQUENCE rfq_analytics_id_seq TO PUBLIC;

GRANT SELECT ON rfq_summary TO PUBLIC;
GRANT SELECT ON vendor_submission_performance TO PUBLIC;

COMMIT;