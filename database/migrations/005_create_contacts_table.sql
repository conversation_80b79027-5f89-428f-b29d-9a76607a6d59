-- Migration: Create Contacts Table
-- This migration adds the Contact management functionality to VendorMS
-- Aligns with Salesforce Contact object structure for seamless integration

-- ============================================================================
-- 1. CONTACT ENUMS AND TYPES
-- ============================================================================

-- Contact salutation options
CREATE TYPE contact_salutation_enum AS ENUM ('Mr.', 'Ms.', 'Mrs.', 'Dr.', 'Prof.', 'Rev.');

-- Contact lead source options
CREATE TYPE contact_lead_source_enum AS ENUM ('Web', 'Phone Inquiry', 'Partner Referral', 'Purchased List', 'Other');

-- Contact level/priority options
CREATE TYPE contact_level_enum AS ENUM ('Primary', 'Secondary', 'Tertiary');

-- Contact status options (reusing general_status_enum but adding contact-specific if needed)
-- We'll use the existing general_status_enum: 'open', 'resolved', 'dismissed', 'escalated'
-- But for contacts, we'll interpret these as: 'Active', 'Inactive', 'Archived', 'Pending'

-- ============================================================================
-- 2. CONTACTS TABLE
-- ============================================================================

CREATE TABLE contacts (
    id SERIAL PRIMARY KEY,
    
    -- Required fields
    account_id INTEGER NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    last_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    
    -- Personal information
    first_name VARCHAR(255),
    salutation contact_salutation_enum,
    title VARCHAR(255),
    department VARCHAR(255),
    
    -- Communication channels
    phone VARCHAR(50),
    mobile_phone VARCHAR(50),
    home_phone VARCHAR(50),
    other_phone VARCHAR(50),
    fax VARCHAR(50),
    
    -- Address information (JSONB for flexibility)
    mailing_address JSONB, -- {street, city, state, postal_code, country, latitude, longitude}
    other_address JSONB,   -- Secondary address with same structure
    
    -- Hierarchy and relationships
    reports_to_id INTEGER REFERENCES contacts(id) ON DELETE SET NULL,
    
    -- Assistant information
    assistant_name VARCHAR(255),
    assistant_phone VARCHAR(50),
    
    -- Additional personal details
    birthdate DATE,
    
    -- Lead and qualification information
    lead_source contact_lead_source_enum,
    level contact_level_enum,
    description TEXT,
    
    -- Language and communication preferences
    languages TEXT, -- JSON array or comma-separated string
    do_not_call BOOLEAN DEFAULT FALSE,
    has_opted_out_of_email BOOLEAN DEFAULT FALSE,
    
    -- Ownership and assignment
    owner_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    
    -- Integration fields
    integration_id VARCHAR(100), -- Salesforce Contact ID
    
    -- Custom fields for tenant-specific requirements
    custom_fields JSONB DEFAULT '{}'::JSONB,
    
    -- Status and lifecycle
    status general_status_enum DEFAULT 'open', -- 'open' = Active, 'dismissed' = Inactive
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    last_modified_by_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    
    -- Soft delete support
    is_deleted BOOLEAN DEFAULT FALSE,
    
    -- Constraints
    CONSTRAINT contacts_email_unique_per_tenant UNIQUE (email, is_deleted) DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT contacts_hierarchy_no_self_reference CHECK (id != reports_to_id)
);

-- ============================================================================
-- 3. COMMENTS AND INDEXES
-- ============================================================================

COMMENT ON TABLE contacts IS 'Individual contacts associated with accounts, mirroring Salesforce Contact structure';
COMMENT ON COLUMN contacts.account_id IS 'Required reference to parent account';
COMMENT ON COLUMN contacts.email IS 'Unique email address within tenant scope';
COMMENT ON COLUMN contacts.mailing_address IS 'Primary address in JSON format with geocoding support';
COMMENT ON COLUMN contacts.other_address IS 'Secondary address in JSON format';
COMMENT ON COLUMN contacts.reports_to_id IS 'Self-referencing hierarchy for organizational structure';
COMMENT ON COLUMN contacts.languages IS 'Supported languages for communication (JSON array or CSV)';
COMMENT ON COLUMN contacts.integration_id IS 'External system ID (e.g., Salesforce Contact ID)';
COMMENT ON COLUMN contacts.custom_fields IS 'Flexible JSONB storage for tenant-specific fields';

-- Performance indexes
CREATE INDEX idx_contacts_account_id ON contacts (account_id);
CREATE INDEX idx_contacts_last_name ON contacts (last_name);
CREATE INDEX idx_contacts_email ON contacts (email) WHERE is_deleted = FALSE;
CREATE INDEX idx_contacts_department ON contacts (department);
CREATE INDEX idx_contacts_reports_to_id ON contacts (reports_to_id);
CREATE INDEX idx_contacts_owner_id ON contacts (owner_id);
CREATE INDEX idx_contacts_integration_id ON contacts (integration_id) WHERE integration_id IS NOT NULL;
CREATE INDEX idx_contacts_status ON contacts (status);
CREATE INDEX idx_contacts_created_at ON contacts (created_at);
CREATE INDEX idx_contacts_is_deleted ON contacts (is_deleted);

-- Full-text search indexes
CREATE INDEX idx_contacts_name_search ON contacts USING GIN (
    to_tsvector('english', COALESCE(first_name, '') || ' ' || COALESCE(last_name, '') || ' ' || COALESCE(title, '') || ' ' || COALESCE(department, ''))
);

-- JSONB indexes for address searches
CREATE INDEX idx_contacts_mailing_address_gin ON contacts USING GIN (mailing_address);
CREATE INDEX idx_contacts_other_address_gin ON contacts USING GIN (other_address);
CREATE INDEX idx_contacts_custom_fields_gin ON contacts USING GIN (custom_fields);

-- ============================================================================
-- 4. TRIGGERS AND FUNCTIONS
-- ============================================================================

-- Apply updated_at trigger to contacts table
CREATE TRIGGER update_contacts_updated_at 
    BEFORE UPDATE ON contacts 
    FOR EACH ROW 
    EXECUTE PROCEDURE update_updated_at_column();

-- Function to compute full name
CREATE OR REPLACE FUNCTION get_contact_full_name(first_name TEXT, last_name TEXT)
RETURNS TEXT AS $$
BEGIN
    IF first_name IS NOT NULL AND first_name != '' THEN
        RETURN first_name || ' ' || last_name;
    ELSE
        RETURN last_name;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to validate contact hierarchy (prevent cycles)
CREATE OR REPLACE FUNCTION validate_contact_hierarchy()
RETURNS TRIGGER AS $$
DECLARE
    current_contact_id INTEGER;
    max_depth INTEGER := 10;
    depth INTEGER := 0;
BEGIN
    -- Only check if reports_to_id is being set
    IF NEW.reports_to_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Start from the new reports_to_id and traverse up the hierarchy
    current_contact_id := NEW.reports_to_id;
    
    WHILE current_contact_id IS NOT NULL AND depth < max_depth LOOP
        -- If we encounter the contact being updated, we have a cycle
        IF current_contact_id = NEW.id THEN
            RAISE EXCEPTION 'Circular hierarchy detected: Contact cannot report to itself or its subordinates';
        END IF;
        
        -- Move up one level
        SELECT reports_to_id INTO current_contact_id 
        FROM contacts 
        WHERE id = current_contact_id AND is_deleted = FALSE;
        
        depth := depth + 1;
    END LOOP;
    
    -- Check if we hit max depth (potential infinite loop)
    IF depth >= max_depth THEN
        RAISE EXCEPTION 'Hierarchy depth exceeds maximum allowed levels';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply hierarchy validation trigger
CREATE TRIGGER validate_contact_hierarchy_trigger
    BEFORE INSERT OR UPDATE ON contacts
    FOR EACH ROW
    EXECUTE PROCEDURE validate_contact_hierarchy();

-- Function to automatically log contact changes
CREATE OR REPLACE FUNCTION log_contact_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' THEN
        INSERT INTO audits (entity_type, entity_id, action, user_id, old_value, new_value, timestamp)
        VALUES ('contacts', NEW.id, 'update', 
                COALESCE(CURRENT_SETTING('app.current_user_id', true)::INTEGER, 1), 
                row_to_json(OLD)::JSONB, row_to_json(NEW)::JSONB, CURRENT_TIMESTAMP);
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audits (entity_type, entity_id, action, user_id, new_value, timestamp)
        VALUES ('contacts', NEW.id, 'create', 
                COALESCE(CURRENT_SETTING('app.current_user_id', true)::INTEGER, 1), 
                row_to_json(NEW)::JSONB, CURRENT_TIMESTAMP);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audits (entity_type, entity_id, action, user_id, old_value, timestamp)
        VALUES ('contacts', OLD.id, 'delete', 
                COALESCE(CURRENT_SETTING('app.current_user_id', true)::INTEGER, 1), 
                row_to_json(OLD)::JSONB, CURRENT_TIMESTAMP);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply audit trigger to contacts table
CREATE TRIGGER contact_audit_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON contacts 
    FOR EACH ROW 
    EXECUTE PROCEDURE log_contact_changes();

-- ============================================================================
-- 5. VIEWS FOR COMMON QUERIES
-- ============================================================================

-- View: Contact Summary with computed fields
CREATE VIEW contact_summary AS
SELECT 
    c.id,
    c.account_id,
    a.name as account_name,
    get_contact_full_name(c.first_name, c.last_name) as full_name,
    c.first_name,
    c.last_name,
    c.salutation,
    c.title,
    c.department,
    c.email,
    c.phone,
    c.mobile_phone,
    c.reports_to_id,
    rt.first_name || ' ' || rt.last_name as reports_to_name,
    c.owner_id,
    u.email as owner_email,
    c.status,
    c.created_at,
    c.updated_at,
    -- Count of direct reports
    (SELECT COUNT(*) FROM contacts sub WHERE sub.reports_to_id = c.id AND sub.is_deleted = FALSE) as direct_reports_count
FROM contacts c
LEFT JOIN accounts a ON c.account_id = a.id
LEFT JOIN contacts rt ON c.reports_to_id = rt.id AND rt.is_deleted = FALSE
LEFT JOIN users u ON c.owner_id = u.id
WHERE c.is_deleted = FALSE;

COMMENT ON VIEW contact_summary IS 'Contact summary with computed fields and related data';

-- ============================================================================
-- 6. SAMPLE DATA (Optional - for development)
-- ============================================================================

-- Note: Sample data would be inserted here for development/testing
-- This would be removed or made conditional for production deployments

-- ============================================================================
-- 7. PERMISSIONS AND SECURITY
-- ============================================================================

-- Grant appropriate permissions to application role
-- GRANT SELECT, INSERT, UPDATE, DELETE ON contacts TO vendorms_app_role;
-- GRANT USAGE, SELECT ON SEQUENCE contacts_id_seq TO vendorms_app_role;

-- Note: Actual permission grants would depend on your specific role setup