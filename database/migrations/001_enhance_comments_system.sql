-- Migration: Enhance Comments System for Real-time Collaboration
-- Phase 1: Foundation & Database Setup
-- Date: 2024-01-15

-- First, let's enhance the existing comments table to match the specification
ALTER TABLE comments 
  ADD COLUMN IF NOT EXISTS content TEXT,
  ADD COLUMN IF NOT EXISTS content_type VARCHAR(20) DEFAULT 'text',
  ADD COLUMN IF NOT EXISTS category VARCHAR(50),
  ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active',
  ADD COLUMN IF NOT EXISTS is_edited BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE,
  ADD COLUMN IF NOT EXISTS metadata JSONB;

-- Rename existing columns to match specification
ALTER TABLE comments 
  RENAME COLUMN entity_type TO object_type;
ALTER TABLE comments 
  RENAME COLUMN entity_id TO object_id;
ALTER TABLE comments 
  RENAME COLUMN text TO content;
ALTER TABLE comments 
  RENAME COLUMN parent_comment_id TO parent_id;

-- Add constraints and comments
COMMENT ON COLUMN comments.object_type IS 'Type of object being commented on: vendor, contract, etc.';
COMMENT ON COLUMN comments.object_id IS 'ID of the object being commented on';
COMMENT ON COLUMN comments.parent_id IS 'Parent comment ID for threaded replies';
COMMENT ON COLUMN comments.content IS 'Comment content text';
COMMENT ON COLUMN comments.content_type IS 'Content format: text or html';
COMMENT ON COLUMN comments.category IS 'Comment category: general, legal, financial, technical';
COMMENT ON COLUMN comments.status IS 'Comment status: active, resolved, deleted';
COMMENT ON COLUMN comments.is_edited IS 'Whether the comment has been edited';
COMMENT ON COLUMN comments.deleted_at IS 'Soft delete timestamp';
COMMENT ON COLUMN comments.metadata IS 'Additional metadata for extensibility';

-- Create comment_attachments table
CREATE TABLE IF NOT EXISTS comment_attachments (
    id SERIAL PRIMARY KEY,
    comment_id INTEGER NOT NULL REFERENCES comments(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path TEXT NOT NULL,
    uploaded_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE comment_attachments IS 'File attachments for comments';
COMMENT ON COLUMN comment_attachments.filename IS 'Stored filename (UUID-based)';
COMMENT ON COLUMN comment_attachments.original_name IS 'Original filename from user';
COMMENT ON COLUMN comment_attachments.file_size IS 'File size in bytes';
COMMENT ON COLUMN comment_attachments.mime_type IS 'MIME type of the file';
COMMENT ON COLUMN comment_attachments.file_path IS 'Full path to stored file';

-- Create comment_mentions table
CREATE TABLE IF NOT EXISTS comment_mentions (
    id SERIAL PRIMARY KEY,
    comment_id INTEGER NOT NULL REFERENCES comments(id) ON DELETE CASCADE,
    mentioned_user_id INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE comment_mentions IS 'User mentions in comments for notifications';
COMMENT ON COLUMN comment_mentions.mentioned_user_id IS 'ID of the user being mentioned';

-- Create optimized indexes for the enhanced comments system
CREATE INDEX IF NOT EXISTS idx_comments_object_enhanced ON comments(object_type, object_id, status, created_at);
CREATE INDEX IF NOT EXISTS idx_comments_parent_enhanced ON comments(parent_id) WHERE parent_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_comments_user_enhanced ON comments(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_comments_category ON comments(category) WHERE category IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_comments_status ON comments(status);
CREATE INDEX IF NOT EXISTS idx_comments_deleted ON comments(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for comment_attachments
CREATE INDEX IF NOT EXISTS idx_attachments_comment ON comment_attachments(comment_id);
CREATE INDEX IF NOT EXISTS idx_attachments_uploaded_by ON comment_attachments(uploaded_by);

-- Indexes for comment_mentions
CREATE INDEX IF NOT EXISTS idx_mentions_comment ON comment_mentions(comment_id);
CREATE INDEX IF NOT EXISTS idx_mentions_user ON comment_mentions(mentioned_user_id);
CREATE INDEX IF NOT EXISTS idx_mentions_user_created ON comment_mentions(mentioned_user_id, created_at);

-- Add check constraints
ALTER TABLE comments ADD CONSTRAINT chk_comments_content_type 
  CHECK (content_type IN ('text', 'html'));

ALTER TABLE comments ADD CONSTRAINT chk_comments_status 
  CHECK (status IN ('active', 'resolved', 'deleted'));

ALTER TABLE comments ADD CONSTRAINT chk_comments_category 
  CHECK (category IS NULL OR category IN ('general', 'legal', 'financial', 'technical', 'performance', 'compliance'));

ALTER TABLE comment_attachments ADD CONSTRAINT chk_attachments_file_size 
  CHECK (file_size > 0 AND file_size <= 10485760); -- 10MB limit

-- Create function to update comment edit status
CREATE OR REPLACE FUNCTION update_comment_edit_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Mark comment as edited if content changes
    IF OLD.content IS DISTINCT FROM NEW.content THEN
        NEW.is_edited = TRUE;
    END IF;
    
    -- Update timestamp
    NEW.updated_at = CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for comment edit tracking
DROP TRIGGER IF EXISTS update_comment_edit_status_trigger ON comments;
CREATE TRIGGER update_comment_edit_status_trigger
    BEFORE UPDATE ON comments
    FOR EACH ROW
    EXECUTE FUNCTION update_comment_edit_status();

-- Create function for soft delete
CREATE OR REPLACE FUNCTION soft_delete_comment(comment_id INTEGER, user_id INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    comment_exists BOOLEAN;
BEGIN
    -- Check if comment exists and user owns it
    SELECT EXISTS(
        SELECT 1 FROM comments 
        WHERE id = comment_id 
        AND user_id = soft_delete_comment.user_id 
        AND deleted_at IS NULL
    ) INTO comment_exists;
    
    IF NOT comment_exists THEN
        RETURN FALSE;
    END IF;
    
    -- Soft delete the comment
    UPDATE comments 
    SET deleted_at = CURRENT_TIMESTAMP, 
        status = 'deleted'
    WHERE id = comment_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create function to get comment thread
CREATE OR REPLACE FUNCTION get_comment_thread(object_type_param VARCHAR, object_id_param INTEGER)
RETURNS TABLE (
    id INTEGER,
    object_type VARCHAR,
    object_id INTEGER,
    parent_id INTEGER,
    user_id INTEGER,
    user_name VARCHAR,
    user_email VARCHAR,
    content TEXT,
    content_type VARCHAR,
    category VARCHAR,
    status VARCHAR,
    is_edited BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    attachment_count BIGINT,
    mention_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.object_type,
        c.object_id,
        c.parent_id,
        c.user_id,
        u.name as user_name,
        u.email as user_email,
        c.content,
        c.content_type,
        c.category,
        c.status,
        c.is_edited,
        c.created_at,
        c.updated_at,
        COALESCE(att.attachment_count, 0) as attachment_count,
        COALESCE(men.mention_count, 0) as mention_count
    FROM comments c
    JOIN users u ON c.user_id = u.id
    LEFT JOIN (
        SELECT comment_id, COUNT(*) as attachment_count
        FROM comment_attachments
        GROUP BY comment_id
    ) att ON c.id = att.comment_id
    LEFT JOIN (
        SELECT comment_id, COUNT(*) as mention_count
        FROM comment_mentions
        GROUP BY comment_id
    ) men ON c.id = men.comment_id
    WHERE c.object_type = object_type_param 
    AND c.object_id = object_id_param
    AND c.deleted_at IS NULL
    ORDER BY c.created_at ASC;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON comments TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON comment_attachments TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON comment_mentions TO PUBLIC;
GRANT USAGE ON SEQUENCE comments_id_seq TO PUBLIC;
GRANT USAGE ON SEQUENCE comment_attachments_id_seq TO PUBLIC;
GRANT USAGE ON SEQUENCE comment_mentions_id_seq TO PUBLIC;

-- Insert sample data for testing (optional)
-- This can be removed in production
INSERT INTO comments (object_type, object_id, user_id, content, category, status) 
VALUES 
    ('vendor', 1, 1, 'This vendor has excellent response times and quality.', 'general', 'active'),
    ('contract', 1, 1, 'Legal review completed. Terms look good.', 'legal', 'resolved')
ON CONFLICT DO NOTHING;

COMMIT;