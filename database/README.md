# VendorMS Database Schema

## Overview

The VendorMS database has been successfully created with a comprehensive schema designed for a modern Vendor Management System. The database `vendorManagement` contains 27 tables, custom types, views, triggers, and functions to support all planned features.

## Database Information

- **Database Name**: `vendorManagement`
- **PostgreSQL Version**: Compatible with 14+
- **Extensions Used**: `pgcrypto`, `pg_trgm`
- **Total Tables**: 27
- **Custom Types**: 18 enums
- **Views**: 1 regular view, 1 materialized view
- **Triggers**: 8 triggers for auditing and data integrity

## Core Tables Created

### Authentication & Authorization
- `users` - User accounts with RBAC
- `sessions` - JWT session management
- `role_permissions` - Role-based permissions
- `user_vendor_roles` - User-vendor relationship mapping

### Vendor Management
- `vendors` - Core vendor profiles
- `vendor_histories` - Change tracking for vendors
- `documents` - File attachments for entities

### Contract Management
- `contracts` - Contract lifecycle management
- `templates` - Reusable contract templates
- `milestones` - Contract milestone tracking
- `disputes` - Dispute management

### Performance & Compliance
- `kpis` - Vendor performance metrics
- `risk_assessments` - Compliance and risk scoring
- `alerts` - System-generated alerts

### Invoicing & Payments
- `invoices` - Invoice management with approval workflow
- `payments` - Payment processing with multi-currency support

### Reporting & Analytics
- `reports` - Custom report configurations
- `ai_insights` - AI-generated insights and predictions

### Workflows & Automation
- `workflows` - Workflow definitions
- `workflow_instances` - Running workflow instances
- `integrations` - External system integrations
- `notifications` - Notification logging

### Admin Tools
- `system_settings` - System configuration
- `backups` - Backup metadata tracking

### Additional Features
- `comments` - Collaborative comments with threading
- `audits` - Immutable audit trail
- `config_logs` - Configuration change logs

## Custom Types (Enums)

The schema includes 18 custom enum types for type safety:

- `role_enum` - User roles (admin, manager, viewer)
- `vendor_status_enum` - Vendor lifecycle states
- `contract_status_enum` - Contract states
- `invoice_status_enum` - Invoice processing states
- `payment_status_enum` - Payment states
- `compliance_type_enum` - Compliance standards
- `workflow_status_enum` - Workflow execution states
- `notification_type_enum` - Notification methods
- `ai_insight_type_enum` - AI insight categories
- `language_enum` - Supported languages
- `currency_enum` - Supported currencies
- `kpi_type_enum` - KPI measurement types
- `gateway_enum` - Payment gateways
- `alert_type_enum` - Alert categories
- `general_status_enum` - General status values
- `audit_action_enum` - Audit action types
- `integration_type_enum` - Integration types

## Key Features Implemented

### Security
- Password encryption using pgCrypto
- Row-level security (RLS) policies for audit tables
- Immutable audit logs with triggers
- GDPR compliance fields

### Performance
- Comprehensive indexing strategy
- GIN indexes for JSONB columns
- Trigram indexes for fuzzy text search
- Materialized views for analytics

### Data Integrity
- Foreign key constraints with appropriate cascading
- Check constraints for data validation
- Triggers for automatic timestamp updates
- Immutability enforcement for audit tables

### Flexibility
- JSONB columns for dynamic/custom fields
- Support for multi-currency operations
- Extensible workflow system
- Configurable system settings

## Initial Data

The database has been seeded with:

### Default Admin User
- **Email**: <EMAIL>
- **Role**: admin
- **Password**: admin123 (encrypted with pgCrypto)
- **Status**: Verified

### Role Permissions
- **Admin**: Full access (["*"])
- **Manager**: Read/write access to vendors, contracts, invoices, reports
- **Viewer**: Read-only access to core entities

### System Settings
- Theme colors (primary: #3b82f6, secondary: #64748b)
- Default currency: USD
- Default language: en
- Invoice approval required: true
- Contract e-signature enabled: true

## Views and Functions

### Views
- `vendor_performance_summary` - Comprehensive vendor performance overview
- `monthly_spend_analysis` - Materialized view for spending analytics

### Utility Functions
- `refresh_analytics_views()` - Refresh all materialized views
- `calculate_vendor_performance_score(vendor_id)` - Calculate vendor performance
- `update_updated_at_column()` - Automatic timestamp updates
- `prevent_audit_modifications()` - Protect audit table integrity
- `log_vendor_changes()` - Automatic vendor change logging

## Usage Examples

### Connect to Database
```bash
psql -d vendorManagement
```

### Query Vendor Performance
```sql
SELECT * FROM vendor_performance_summary;
```

### Refresh Analytics
```sql
SELECT refresh_analytics_views();
```

### Check System Settings
```sql
SELECT key, value FROM system_settings;
```

## Next Steps

1. **Application Integration**: Update your application's database connection to use `vendorManagement`
2. **Environment Variables**: Set up proper encryption keys for pgCrypto
3. **User Management**: Change the default admin password
4. **Data Migration**: If migrating from existing system, create migration scripts
5. **Backup Strategy**: Set up regular backups using the backup tracking system
6. **Monitoring**: Implement monitoring for the materialized views refresh schedule

## Schema Maintenance

### Regular Tasks
- Refresh materialized views: `SELECT refresh_analytics_views();`
- Monitor audit table growth and consider partitioning for large datasets
- Review and update role permissions as needed
- Backup database regularly with integrity checking

### Performance Monitoring
- Monitor query performance on JSONB columns
- Consider additional indexes based on query patterns
- Review and optimize materialized view refresh frequency

The database schema is now ready for your VendorMS application development!